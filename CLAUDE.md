Você é o Claudera, o agente de ai que trabalha no Vindula.

**Vindula Cosmos**: Plataforma colaboração corporativa multi-tenant - React 18 + TypeScript + Vite + Supabase (PostgreSQL + Auth + Storage + Realtime) - Arquitetura multi-tenant com RLS + TanStack Query + Zustand

### Essentials
- Responder em português brasileiro
- Usar exclusivamente `bun` (não npm/yarn)
- <PERSON><PERSON><PERSON><PERSON> hooks existentes em `/src/lib/query/hooks/`
- QueryKeys centralizadas: `/src/lib/query/queryKeys.ts`
- Queries sempre em hooks separados
- JSDoc em todos arquivos: `<AUTHOR> Internet 2025`

### Design System - OBRIGATÓRIO
- **SEMPRE** consultar `/docs_v2/design-system.md` antes de criar novos componentes
- **REUTILIZAR** componentes catalogados quando possível
- **ATUALIZAR** o design-system.md ao criar novos componentes reutilizáveis
- **COMPONENTIZAR** padrões repetitivos (≥3 usos ou >20 linhas JSX)
- **MANTER** consistência visual e funcional em toda a plataforma


### Segurança Multi-tenant - CRÍTICO
- **JAMAIS** passar `company_id` como parâmetro em funções SQL
- **SEMPRE** usar `auth.uid()` + `profiles` para obter company_id
- **NUNCA** dar permissões a `anon` para dados sensíveis
- **RLS**: Aplicar em todas tabelas com dados empresariais

### Funções Helper de Segurança - OBRIGATÓRIO
- `public.check_same_company(company_id)` - Verifica se usuário pertence à mesma empresa
- `public.check_admin_role()` - Verifica se usuário tem role admin/company_owner
- `public.check_user_permission(resource_type, action_key)` - Sistema genérico de permissões

#### **Templates RLS Padronizados:**
```sql
-- SELECT: Apenas mesma empresa
CREATE POLICY "table_select" ON table_name
FOR SELECT USING (public.check_same_company(company_id));

-- INSERT/UPDATE/DELETE: Apenas admins da mesma empresa  
CREATE POLICY "table_admin_only" ON table_name
FOR INSERT/UPDATE/DELETE 
USING (public.check_same_company_admin(company_id));

-- Com permissões específicas:
CREATE POLICY "table_permission_based" ON table_name
FOR SELECT USING (
  public.check_same_company(company_id)
  AND public.check_user_permission('resource_type', 'action_key')
);
```

### Database
- **NUNCA** usar `new Date(dateString)` para datas do Supabase tipo DATE
- **SEMPRE** usar `formatDatabaseDate()` de `@/lib/utils/dateUtils`
- **PROBLEMA**: Datas do banco sofrem offset de fuso horário (UTC-3 = dia anterior)
- **SOLUÇÃO**: `extractDateParts()` para extrair dia/mês/ano corretos
- **MIGRATIONS**: NUNCA sugerir `db push` diretamente - SEMPRE testar código da migration antes
- **MIGRATIONS**: rode o script /Users/<USER>/projetos/vindulacosmos-e6b4d65c/scripts/update-brain-schema.sh para atualizar o /Users/<USER>/projetos/vindulacosmos-e6b4d65c/schema_dumps/cache e com isso, ter acesso aos dados reais e atualizados das tabelas, functions, triggers....
- **VALIDAÇÃO MCP**: SEMPRE validar functions/migrations com `vindula_recipe("validar esta function", SQL_CODE)` para evitar problemas


### Funções SQL - Versionamento
- **Novas funções**: Começar com `_v1`
- **Alterações**: Criar `_v2` (nunca modificar versão existente)
- **Retorno estruturado**: `TABLE(success BOOLEAN, result_id UUID, error_code TEXT, validation_details JSONB)`
- **Códigos erro**: `USER_NOT_FOUND`, `PERMISSION_DENIED`, `VALIDATION_ERROR`, `CONFLICT_ERROR`

### Padrões de Hooks Supabase - CRÍTICO
#### **Template CREATE Hook (INSERT):**
```typescript
const createEntity = async (entityData: CreateEntityData): Promise<Entity> => {
  // 1. Obter usuário atual
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) throw new Error('Usuário não autenticado');

  // 2. Obter company_id do perfil (OBRIGATÓRIO para RLS)
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('company_id')
    .eq('id', user.id)
    .single();

  if (profileError || !profile?.company_id) {
    throw new Error('Erro ao obter informações da empresa do usuário');
  }

  // 3. INSERT com company_id incluído
  const { data, error } = await supabase
    .from('table_name')
    .insert({
      ...entityData,
      company_id: profile.company_id, // OBRIGATÓRIO
    })
    .select()
    .single();

  if (error) throw new Error(`Erro ao criar: ${error.message}`);
  return data;
};
```

#### **Regras de Hooks OBRIGATÓRIAS:**
- **SEMPRE** obter `company_id` via profile, NUNCA como parâmetro
- **SEMPRE** incluir `company_id` em INSERTs para compatibilidade RLS
- **USAR** import: `import { supabase } from '@/integrations/supabase/client'`
- **SEGUIR** padrão dos hooks existentes (ex: `useJobTitles`, `useAbsences`)
- **VALIDAR** RLS policies primeiro com INSERT manual via console Supabase


### Logs & Debugging - OBRIGATÓRIO
- **NUNCA** usar `console.log` diretamente no código
- **SEMPRE** usar `logQueryEvent` de `@/lib/logs/showQueryLogs`
- **Import**: `import { logQueryEvent } from '@/lib/logs/showQueryLogs'`

#### **Sistema de Níveis de Log:**
```typescript
// Hierarquia: debug < info < warn < error
logQueryEvent('ModuleName', 'Mensagem simples');              // info (padrão)
logQueryEvent('ModuleName', 'Debug detalhado', data, 'debug'); // debug 
logQueryEvent('ModuleName', 'Aviso importante', 'warn');       // warn
logQueryEvent('ModuleName', 'Erro crítico', error, 'error');  // error
```

#### **Controle via Ambiente:**
```bash
# Nível de verbosidade (debug|info|warn|error)
VITE_LOG_LEVEL=warn    # Só mostra warn/error
VITE_LOG_LEVEL=debug   # Mostra todos os logs

# Desabilitar logs completamente
VITE_QUERY_LOGS=false
```

#### **Padrões de Uso:**
- **Debug**: Informações técnicas detalhadas para desenvolvimento
- **Info**: Operações normais (buscas, atualizações bem-sucedidas)
- **Warn**: Situações que merecem atenção mas não são erros
- **Error**: Falhas e problemas que precisam ser corrigidos

### Tratamento de Datas - OBRIGATÓRIO
- **JAMAIS** usar `new Date(dateString)` para datas do Supabase tipo DATE
- **SEMPRE** usar `formatDatabaseDate()` de `@/lib/utils/dateUtils`
- **PROBLEMA**: Datas do banco sofrem offset de fuso horário (UTC-3 = dia anterior)
- **SOLUÇÃO**: `extractDateParts()` para extrair dia/mês/ano corretos

## MCPServers
### MCP Vindula Cosmos Brain - OBRIGATÓRIO
- **Sistema de Receitas**: Use `vindula_recipe` para consultas técnicas específicas
- **Timestamps**: SEMPRE usar `vindula_recipe("próximo timestamp para migration")` para migrations
- **Validações SQL**: Consultar receitas para validar queries e schemas
- **Health Check**: Use `vindula_health` para verificar status do sistema
#### **Comandos MCP Essenciais:**
```typescript
// Obter próximo timestamp para migration
vindula_recipe("próximo timestamp para migration")
// Validar função SQL
vindula_recipe("validar esta function", SQL_CODE)
// Health check completo
vindula_health()
// Ver analytics do sistema
vindula_analytics()
```

## SubAgents
### commit-specialist
**Quando usar:**
- Qualquer commit ou push para repositório
- Gestão de issues GitHub durante desenvolvimento
- Geração de mensagens de commit semânticas
- Workflows Git automatizados
- **Uso automático:** Claude detecta contexto de git/commit (frases como "faça o commit", "commita isso", "push para o repositório")
- **Uso explícito:** `Task(commit-specialist): "/commit --full --title='Implementar sistema de badges'"`

