on:
  workflow_dispatch: {}
  pull_request: {}
  push:
    branches:
    - main
    - master
    paths:
    - .github/workflows/semgrep.yml
  schedule:
  # random HH:MM to avoid a load spike on GitHub Actions at 00:00
  - cron: 18 11 * * *
name: Semgrep
jobs:
  semgrep:
    name: semgrep/ci
    runs-on: ubuntu-latest
    permissions:
      contents: read
    env:
      SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}
    container:
      image: semgrep/semgrep
    steps:
    - uses: actions/checkout@v4
    - run: semgrep ci
