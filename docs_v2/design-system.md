# Design System - Vindula Cosmos

> **Catálogo de componentes reutilizáveis prontos para uso**  
> Mantido automaticamente pelo frontend-specialist

---

## 🎨 Layout e Estrutura

### HeroSection
**Arquivo**: `@/components/common/HeroSection`  
**Uso**: Headers principais com gradiente e ações customizáveis

```tsx
<HeroSection
  title="Portal de Administração LGPD"
  description="Gerencie solicitações de privacidade dos usuários"
  icon={Shield}
  gradientColors="from-red-600 via-orange-600 to-amber-600"
  iconAnimation={true}
  actions={<div>/* Seus botões aqui */</div>}
/>
```

---

## 📦 Componentes Base (Shadcn/ui)

### Button
**Arquivo**: `@/components/ui/button`
```tsx
<Button variant="default" size="lg" disabled={false}>
  Texto do Botão
</Button>
```

### Card
**Arquivo**: `@/components/ui/card`
```tsx
<Card>
  <CardHeader>
    <CardTitle>Título</CardTitle>
  </CardHeader>
  <CardContent>
    Conteúdo do card
  </CardContent>
</Card>
```

### Badge
**Arquivo**: `@/components/ui/badge`
```tsx
<Badge variant="outline">Status</Badge>
```

### Input
**Arquivo**: `@/components/ui/input`
```tsx
<Input placeholder="Digite aqui..." />
```

### Tabs
**Arquivo**: `@/components/ui/tabs`
```tsx
<Tabs value={activeTab} onValueChange={setActiveTab}>
  <TabsList>
    <TabsTrigger value="tab1">Tab 1</TabsTrigger>
  </TabsList>
  <TabsContent value="tab1">Conteúdo</TabsContent>
</Tabs>
```

---

## 🔄 Componentes Interativos

### AdvancedRefreshButton
**Arquivo**: `@/components/ui/advanced-refresh-button`  
**Uso**: Botão de atualização avançado com feedback completo

```tsx
<AdvancedRefreshButton
  onRefresh={handleRefresh}
  size="lg"
  minimumDisplayTime={2000}
  timeout={8000}
  operationName="Dados"
  successMessage="Dados atualizados!"
  errorMessage="Erro ao atualizar"
  enableSound={true}
>
  Atualizar
</AdvancedRefreshButton>

// Variantes disponíveis
<AdvancedRefreshButtonIcon onRefresh={handleRefresh} />
<AdvancedRefreshButtonLarge onRefresh={handleRefresh} />
<AdvancedRefreshButtonGhost onRefresh={handleRefresh} />
```

**Funcionalidades:**
- ✅ Feedback sonoro automático
- ✅ Timeout configurável (default: 8s)
- ✅ Tempo mínimo de display (default: 2s)
- ✅ Animação de rotação durante refresh
- ✅ Notificações de sucesso/erro
- ✅ Estados visuais avançados
- ✅ Hook useAdvancedRefresh incluído

### RefreshButton (Básico)
**Arquivo**: `@/components/ui/RefreshButton`  
**Uso**: Botão de atualização simples com animação

```tsx
<RefreshButton
  isLoading={isLoading}
  onRefresh={handleRefresh}
  variant="outline"
  size="sm"
>
  Atualizar
</RefreshButton>

// Em HeroSections com gradient (IMPORTANTE: sempre usar border)
<RefreshButton
  variant="ghost"
  className="text-white hover:bg-white/10 border border-white/20"
  isLoading={isLoading}
  onRefresh={handleRefresh}
  successMessage="Dados atualizados!"
/>
```

**⚠️ Padrões Importantes:**
- Em fundos gradient/coloridos: sempre usar `border border-white/20` com variant="ghost"
- Para melhor UX: prefira AdvancedRefreshButton em telas administrativas

---

## 📊 Estatísticas
- **Componentes catalogados**: 8
- **Prontos para uso**: 8
- **Última atualização**: 2025-01-29

---

*Este documento será atualizado automaticamente conforme novos componentes forem criados.*