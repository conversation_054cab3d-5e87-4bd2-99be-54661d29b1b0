# Implementação Padlet-Style Timeline Colaborativa - Roadmap Técnico

**<AUTHOR> Internet 2025**  
**Data:** 2025-07-22 (Atualizado: 2025-07-27)  
**Categoria:** Roadmap - Timeline Colaborativa  
**Status:** 🚧 EM DESENVOLVIMENTO  

## 🎯 Visão Geral

Roadmap técnico para implementação de funcionalidades colaborativas inspiradas no Padlet na timeline/feed do Vindula Cosmos, aproveitando a arquitetura existente (React 18 + TypeScript + Vite + Supabase + TanStack Query + Zustand + UnifiedRealtimeProvider).

### **Objetivo Principal**
Transformar o feed atual do Vindula em uma plataforma colaborativa visual com múltiplos layouts, drag & drop, e colaboração em tempo real, mantendo compatibilidade com o sistema multi-tenant existente.

## 📈 **Status de Implementação - Julho 2025**

### ✅ **CONCLUÍDO - Sistema de Galeria de Fotos (Issue #177)**
- **Data:** 27/07/2025
- **Funcionalidades Implementadas:**
  - ✅ PhotoGalleryUploader com drag & drop aprimorado
  - ✅ PostPhotoGallery com carrossel e modal
  - ✅ Sistema de limites por plano (15/75/ilimitado fotos)
  - ✅ Preview em tempo real no composer (EnhancedCreatePost.tsx)
  - ✅ Exibição em posts publicados (feed e timeline)
  - ✅ Integração com Supabase Storage e RLS
  - ✅ Sistema de associação tempPostId → postId
  - ✅ Suporte a múltiplos formatos de imagem
  - ✅ Acessibilidade (ARIA, DialogTitle/Description)
  - ✅ Funcionalidade acessível em ContextPanel/Timeline

**Arquivos Implementados:**
- `/src/components/feed/PhotoGalleryUploader.tsx` - Upload com drag & drop
- `/src/components/feed/PostPhotoGallery.tsx` - Exibição de galeria
- `/src/hooks/usePhotoGalleryLimits.ts` - Validação de limites
- `/src/lib/query/hooks/usePosts.ts` - Integração com queries
- **3 migrations SQL** para feature flags e contagem

### 🔄 **PRÓXIMAS ETAPAS - Priorizadas**
1. **Timeline Layout** (próxima implementação)
2. **Grid Layout** (layout multimídia estilo Padlet)
3. **Sistema de Seções** colaborativas
4. **Canvas Layout** (posicionamento livre)

### 📝 **Decisões Técnicas Tomadas**
- **Não implementar aba "Multimídia" separada** - optou-se por integrar diretamente nos layouts
- **Manter Grid Layout para posts com mídia** - seguindo especificação do roadmap
- **Priorizar funcionalidade sobre UI nova** - maximizar valor com arquitetura existente

---

## 1. 📊 Análise Técnica da Arquitetura Atual

### **Componentes Base Existentes** ✅
```typescript
// Feed atual já implementado
- EnhancedFeedPosts.tsx (Sistema principal de posts)
- PostCard.tsx (Cards individuais)
- FeedFilters.tsx (Sistema de filtros)
- UnifiedRealtimeProvider (WebSocket consolidado)
- TanStack Query hooks (Gerenciamento de cache/dados)
```

### **Sistema de Posts Existente** ✅
```sql
-- Tabelas principais já implementadas
posts (id, content, created_at, author_id, company_id, audience, metadata)
post_likes (id, post_id, user_id, company_id)
post_audience (id, post_id, target_type, target_id)
comments (id, post_id, content, author_id, company_id)

-- ✅ NOVO: Sistema de Galeria de Fotos (Implementado 27/07/2025)
post_images (id, post_id, image_url, storage_path, size, created_at, company_id)
-- Integrado com feature flags: photogallery_max (15/75/unlimited por plano)
-- RLS policies aplicadas para isolamento multi-tenant
```

### **Sistema Realtime Existente** ✅
```typescript
// UnifiedRealtimeProvider já processa:
- INSERT/UPDATE/DELETE em posts
- INSERT em post_likes  
- INSERT em comments
- Cache invalidation automática
- Event-driven architecture
```

### **Pontos Fortes da Arquitetura**
- ✅ Sistema multi-tenant robusto (RLS + company_id)
- ✅ Permissões granulares (GenericPermissionGate)
- ✅ WebSocket consolidado (UnifiedRealtimeProvider)
- ✅ Cache inteligente (TanStack Query)
- ✅ Animações centralizadas (Framer Motion)
- ✅ Sistema de uploads (Supabase Storage)
- ✅ **NOVO:** Sistema de galeria de fotos com drag & drop
- ✅ **NOVO:** Validação de limites baseada em planos
- ✅ **NOVO:** Preview em tempo real para múltiplas mídias

---

## 2. 🎨 Mapeamento de Funcionalidades Padlet → Vindula

### **2.1. Layouts Múltiplos - Análise Visual**

#### **Wall/Grid Layout** 🔥 **ALTA PRIORIDADE**
```
┌─────────────────────────────────────────────────┐
│  [+ New Post]  [Filter ▼]  [Grid Settings ⚙️]   │
├─────────────────────────────────────────────────┤
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│ │ Post 1  │ │ Post 2  │ │ Post 3  │ │ Post 4  │ │
│ │ 📷 IMG   │ │ 📝 TEXT │ │ 🎥 VID   │ │ 📄 DOC  │ │
│ │ [❤️ 15]  │ │ [❤️ 8]   │ │ [❤️ 23]  │ │ [❤️ 4]   │ │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘ │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│ │ Post 5  │ │ Post 6  │ │ Post 7  │ │ Post 8  │ │
│ │ 📊 CHART │ │ 🔗 LINK │ │ 📱 APP   │ │ 💭 IDEA │ │
│ │ [❤️ 12]  │ │ [❤️ 6]   │ │ [❤️ 19]  │ │ [❤️ 7]   │ │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘ │
└─────────────────────────────────────────────────┘
```

**Recursos Específicos do Padlet a Implementar:**
- ✅ **Masonry Layout**: Cards de alturas variáveis se ajustam automaticamente
- ✅ **Hover Interactions**: Preview expandido ao passar mouse
- ✅ **Quick Actions**: Like, comment, share em hover
- ✅ **Auto-arrange**: Reorganização automática ao adicionar/remover posts
- ✅ **Responsive Columns**: 1-6 colunas baseado no tamanho da tela

#### **Timeline Layout** ⏰ **ALTA PRIORIDADE** 
```
              📅 2024 Timeline View
          ◄── January ── February ── March ──►
┌─────────────────────────────────────────────────┐
│    │         │         │         │             │
│    ▼         ▼         ▼         ▼             │
│ ┌─────┐  ┌─────┐  ┌─────┐  ┌─────┐           │
│ │Post1│  │Post2│  │Post3│  │Post4│           │
│ │Jan 5│  │Feb12│  │Mar 8│  │Mar25│           │
│ └─────┘  └─────┘  └─────┘  └─────┘           │
│ ┌─────┐     │    ┌─────┐     │              │
│ │Post5│     │    │Post6│     │              │
│ │Jan15│     │    │Mar15│     │              │
│ └─────┘     │    └─────┘     │              │
└─────────────────────────────────────────────────┘
```

**Recursos Específicos do Timeline:**
- ✅ **Dual-track Layout**: Posts acima e abaixo da linha temporal
- ✅ **Date Scrubber**: Navegação rápida por períodos
- ✅ **Zoom Levels**: Dia, semana, mês, ano
- ✅ **Custom Dates**: Arrastar posts para datas específicas
- ✅ **Event Integration**: Integrar com eventos corporativos existentes

#### **Canvas Layout** 🎨 **MÉDIA PRIORIDADE**
```
┌─────────────────────────────────────────────────┐
│ 🎨 [Pen] [Eraser] [Shapes] [Text] 🔍[Zoom 100%] │
├─────────────────────────────────────────────────┤
│     ┌─────────┐                   ┌─────────┐   │
│     │ Post A  │◄──── line ────────│ Post B  │   │
│     │ Idea    │                   │ Result  │   │
│     └─────────┘                   └─────────┘   │
│                                                 │
│  ┌─────────┐         👤 User1                  │
│  │ Post C  │         👤 User2                  │
│  │ Process │         (cursors)                 │
│  └─────────┘                                   │
│       ▲                                        │
│       │                                        │
│       └─── annotation: "Important!"            │
└─────────────────────────────────────────────────┘
```

**Recursos Específicos do Canvas:**
- ✅ **Free Positioning**: Arrastar posts para qualquer posição (x,y)
- ✅ **Collaborative Cursors**: Ver outros usuários em tempo real
- ✅ **Drawing Tools**: Setas, linhas, shapes para conectar ideias
- ✅ **Infinite Canvas**: Scroll infinito com zoom
- ✅ **Annotations**: Notas fixas no canvas

### **2.2. Recursos de Colaboração - Interface Visual**

#### **Presença em Tempo Real**
```
┌─── Online Users (3) ────────────────────────────┐
│ 👤 João Silva (editing Post #123)               │
│ 👤 Maria Santos (viewing Timeline)              │ 
│ 👤 Pedro Costa (canvas - cursor at 245,156)     │
└─────────────────────────────────────────────────┘
```

#### **Comentários Inline**
```
┌─── Post Card ──────────────────────┐
│ "Ótima apresentação do projeto!"   │
│                                    │
│ 💬 3 Comments  👀 12 Views         │
│ ┌─ 👤 Ana: "Concordo totalmente!"  │
│ ├─ 👤 Carlos: "Faltou orçamento"   │
│ └─ 👤 Lisa: "Quando apresentamos?" │
│   └─ 💬 Reply...                   │
└────────────────────────────────────┘
```

### **2.3. Elementos Multimídia - Status de Implementação**

| Tipo de Mídia | Padlet Original | Vindula Implementation | Status |
|---------------|-----------------|------------------------|--------|
| **🖼️ Galerias** | Multiple images | ✅ **IMPLEMENTADO** (27/07/2025) | Drag & drop, carrossel, modal |
| **🖼️ Imagens** | JPG, PNG, GIF, WebP | ✅ Já suportado | Upload individual |
| **🎥 Vídeos** | MP4, WebM, YouTube | ✅ Sistema existente | Player integrado |
| **🎵 Áudio** | MP3, WAV, SoundCloud | ✅ Sistema existente | AudioPlayer component |
| **📄 Docs** | PDF, Office, Google Docs | ✅ Já suportado | Preview, download |
| **🔗 Links** | Auto-preview cards | 🔄 Planejado | Open Graph, embed |
| **📊 Embeds** | YouTube, Vimeo, Maps | 🔄 Planejado | iFrame seguro, sandbox |

### **✅ Sistema de Galeria Implementado**
**Funcionalidades incluídas:**
- PhotoGalleryUploader com drag & drop melhorado
- Suporte a múltiplas imagens por post
- Limites baseados em plano (15/75/ilimitado)
- Preview em tempo real durante composição
- Carrossel com modal para visualização
- Integração completa com sistema multi-tenant

### **2.4. Sistema de Seções - Visual Design**

#### **Grid com Seções**
```
┌─── 📂 MARKETING TEAM ────────────────────┐
│ ┌─────────┐ ┌─────────┐ ┌─────────┐     │
│ │Campaign │ │Analytics│ │Creative │     │
│ │Ideas    │ │Report   │ │Assets   │     │
│ └─────────┘ └─────────┘ └─────────┘     │
└─────────────────────────────────────────┘
┌─── 📂 DEVELOPMENT TEAM ──────────────────┐
│ ┌─────────┐ ┌─────────┐ ┌─────────┐     │
│ │Features │ │Bugs     │ │Deploy   │     │
│ │Backlog  │ │Reports  │ │Notes    │     │
│ └─────────┘ └─────────┘ └─────────┘     │
└─────────────────────────────────────────┘
┌─── + Add New Section ────────────────────┐
```

**Recursos de Seções:**
- ✅ **Cores Personalizáveis**: 12 cores pré-definidas + picker
- ✅ **Drag Between Sections**: Mover posts entre seções
- ✅ **Collapse/Expand**: Ocultar seções temporariamente
- ✅ **Section Permissions**: Quem pode adicionar/editar
- ✅ **Auto-categorization**: Sugestões de IA para categorizar posts

---

## 3. 🏗️ Arquitetura Proposta

### **Estrutura de Componentes**
```
src/components/collaborative-feed/
├── layouts/
│   ├── FeedLayoutProvider.tsx      # Context para layout ativo
│   ├── GridLayout/
│   │   ├── GridFeedContainer.tsx
│   │   ├── GridPostCard.tsx
│   │   └── GridDragDropArea.tsx
│   ├── TimelineLayout/
│   │   ├── TimelineFeedContainer.tsx
│   │   ├── TimelinePostCard.tsx
│   │   └── TimelineAxis.tsx
│   ├── CanvasLayout/
│   │   ├── CanvasFeedContainer.tsx
│   │   ├── CanvasPostCard.tsx
│   │   └── CanvasDrawingTools.tsx
│   └── StreamLayout/            # Existente - Refatorar
│       └── StreamFeedContainer.tsx
├── collaboration/
│   ├── CollaborativeCursors.tsx    # Cursores de outros usuários
│   ├── RealtimeIndicators.tsx      # Status de typing, etc
│   ├── CollaborativeComments.tsx   # Comentários em tempo real
│   └── PresenceIndicator.tsx       # Usuários online
├── drag-drop/
│   ├── DragDropProvider.tsx        # Context do DnD
│   ├── DraggablePost.tsx          # Post arrastável
│   ├── DropZone.tsx               # Zonas de drop
│   └── DragPreview.tsx            # Preview durante drag
├── sections/
│   ├── SectionManager.tsx         # Gerenciar seções
│   ├── SectionHeader.tsx          # Cabeçalhos de seção
│   └── SectionDropZone.tsx        # Drop em seções
└── multimedia/
    ├── MediaUploadDropzone.tsx    # Upload por drag
    ├── EmbedPreview.tsx           # Preview de links
    └── MediaGallery.tsx           # Galeria de mídia
```

### **Hooks Especializados**
```typescript
// src/hooks/collaborative-feed/
useLayoutManager()              // Gerenciar layout ativo
useDragDropPosts()             // Drag & drop de posts
useCollaborativePresence()     // Presença de usuários
usePostPositioning()           # Posições visuais dos posts
useSectionManager()            // Gerenciar seções
useMediaUpload()               // Upload de mídia
```

---

## 4. 🗃️ Database Schema Changes

### **4.1. Tabela de Layouts de Feed**
```sql
-- Armazenar configurações de layout por usuário/empresa
CREATE TABLE feed_layouts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE, -- NULL = configuração global da empresa
  layout_type TEXT NOT NULL CHECK (layout_type IN ('stream', 'grid', 'timeline', 'canvas', 'map')),
  layout_config JSONB NOT NULL DEFAULT '{}', -- Configurações específicas do layout
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(company_id, user_id, layout_type) -- Um layout por usuário por tipo
);

-- RLS Policies
ALTER TABLE feed_layouts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own feed layouts"
  ON feed_layouts FOR ALL
  USING (check_same_company(company_id) AND (user_id = auth.uid() OR user_id IS NULL));
```

### **4.2. Sistema de Posicionamento Visual**
```sql
-- Posições visuais dos posts para layouts Canvas/Grid
CREATE TABLE post_positions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
  layout_type TEXT NOT NULL CHECK (layout_type IN ('grid', 'canvas', 'timeline')),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE, -- Posição específica por usuário
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  
  -- Posições específicas por layout
  x_position INTEGER DEFAULT 0,    -- Canvas/Grid: posição X
  y_position INTEGER DEFAULT 0,    -- Canvas/Grid: posição Y
  timeline_date TIMESTAMPTZ,       -- Timeline: data personalizada
  section_id UUID REFERENCES feed_sections(id), -- Seção (se aplicável)
  
  -- Metadados de posicionamento
  width INTEGER DEFAULT 300,       -- Largura do card (Canvas)
  height INTEGER DEFAULT 200,      -- Altura do card (Canvas)
  z_index INTEGER DEFAULT 0,       -- Camada (Canvas)
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(post_id, layout_type, user_id) -- Uma posição por post por layout por usuário
);

-- RLS Policies
ALTER TABLE post_positions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage positions in their company"
  ON post_positions FOR ALL
  USING (check_same_company(company_id) AND user_id = auth.uid());

-- Índices para performance
CREATE INDEX idx_post_positions_layout_user ON post_positions(layout_type, user_id, company_id);
CREATE INDEX idx_post_positions_post_layout ON post_positions(post_id, layout_type);
```

### **4.3. Sistema de Seções**
```sql
-- Seções para organizar posts (inspirado no Padlet Sections)
CREATE TABLE feed_sections (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  created_by UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  
  title TEXT NOT NULL,
  description TEXT,
  color TEXT DEFAULT '#6366f1', -- Cor da seção
  icon TEXT, -- Ícone da seção (Lucide icon name)
  
  -- Posicionamento da seção
  order_index INTEGER DEFAULT 0,
  layout_type TEXT NOT NULL CHECK (layout_type IN ('grid', 'timeline', 'canvas')),
  
  -- Configurações visuais
  visual_config JSONB DEFAULT '{
    "backgroundColor": "#f8fafc",
    "borderColor": "#e2e8f0", 
    "titleColor": "#1e293b"
  }',
  
  -- Permissões
  visibility TEXT DEFAULT 'company' CHECK (visibility IN ('company', 'department', 'team', 'private')),
  permissions JSONB DEFAULT '{
    "can_add_posts": ["all"],
    "can_edit_section": ["creator", "admin"],
    "can_delete_section": ["creator", "admin"]
  }',
  
  -- Auditoria
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ
);

-- RLS Policies  
ALTER TABLE feed_sections ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view sections in their company"
  ON feed_sections FOR SELECT
  USING (check_same_company(company_id) AND deleted_at IS NULL);

CREATE POLICY "Users can create sections in their company"
  ON feed_sections FOR INSERT
  WITH CHECK (check_same_company(company_id) AND created_by = auth.uid());

CREATE POLICY "Section creators can update their sections"
  ON feed_sections FOR UPDATE
  USING (check_same_company(company_id) AND created_by = auth.uid());

-- Índices
CREATE INDEX idx_feed_sections_company_layout ON feed_sections(company_id, layout_type, deleted_at);
CREATE INDEX idx_feed_sections_order ON feed_sections(layout_type, order_index);
```

### **4.4. Sistema de Presença Colaborativa**
```sql
-- Rastrear presença de usuários em layouts específicos
CREATE TABLE collaborative_presence (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  
  layout_type TEXT NOT NULL,
  current_section UUID REFERENCES feed_sections(id),
  cursor_x INTEGER,
  cursor_y INTEGER,
  
  -- Status de atividade
  is_active BOOLEAN DEFAULT true,
  activity_type TEXT CHECK (activity_type IN ('viewing', 'editing', 'commenting', 'dragging')),
  target_post_id UUID REFERENCES posts(id),
  
  last_seen TIMESTAMPTZ DEFAULT NOW(),
  session_id TEXT NOT NULL -- Para limpeza de sessões mortas
);

-- RLS Policies
ALTER TABLE collaborative_presence ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view presence in their company"
  ON collaborative_presence FOR SELECT
  USING (check_same_company(company_id));

CREATE POLICY "Users can update their own presence"
  ON collaborative_presence FOR ALL
  USING (check_same_company(company_id) AND user_id = auth.uid());

-- Índice para limpeza automática
CREATE INDEX idx_collaborative_presence_cleanup ON collaborative_presence(last_seen, is_active);

-- Trigger para limpeza automática (sessões > 5 minutos inativas)
CREATE OR REPLACE FUNCTION cleanup_inactive_presence()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM collaborative_presence 
  WHERE last_seen < NOW() - INTERVAL '5 minutes' 
    AND is_active = false;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER cleanup_presence_trigger
  AFTER INSERT OR UPDATE ON collaborative_presence
  EXECUTE FUNCTION cleanup_inactive_presence();
```

### **4.5. Extensões aos Posts Existentes**
```sql
-- Adicionar campos de colaboração aos posts existentes
ALTER TABLE posts ADD COLUMN IF NOT EXISTS 
  collaborative_metadata JSONB DEFAULT '{
    "allow_repositioning": true,
    "allow_collaborative_editing": false,
    "lock_editing": false,
    "editing_user_id": null,
    "last_edited_at": null
  }';

-- Adicionar campos de multimedia ao posts existentes
ALTER TABLE posts ADD COLUMN IF NOT EXISTS 
  multimedia_attachments JSONB DEFAULT '[]'; -- Array de objetos de mídia

-- Trigger para atualizar metadata colaborativa
CREATE OR REPLACE FUNCTION update_collaborative_metadata()
RETURNS TRIGGER AS $$
BEGIN
  -- Atualizar timestamp de última edição
  NEW.collaborative_metadata = COALESCE(NEW.collaborative_metadata, '{}'::jsonb) || 
    jsonb_build_object(
      'last_edited_at', NOW(),
      'last_edited_by', auth.uid()
    );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER posts_collaborative_metadata_trigger
  BEFORE UPDATE ON posts
  FOR EACH ROW
  EXECUTE FUNCTION update_collaborative_metadata();
```

---

## 5. 🖥️ Frontend Components Architecture

### **5.1. Sistema de Layout Provider**
```typescript
// src/contexts/FeedLayoutContext.tsx
interface FeedLayoutContextType {
  currentLayout: FeedLayoutType;
  setLayout: (layout: FeedLayoutType) => void;
  layoutConfig: LayoutConfig;
  updateLayoutConfig: (config: Partial<LayoutConfig>) => void;
  availableLayouts: FeedLayoutType[];
}

type FeedLayoutType = 'stream' | 'grid' | 'timeline' | 'canvas' | 'map';

interface LayoutConfig {
  gridColumns?: number;
  cardSize?: 'small' | 'medium' | 'large';
  timelineOrientation?: 'horizontal' | 'vertical';
  canvasZoom?: number;
  sections?: {
    enabled: boolean;
    defaultSections: string[];
  };
}
```

### **5.2. Grid Layout Implementation - Baseado na Interface Padlet**

#### **Análise Visual da Interface Padlet Grid**
Com base na análise das telas do Padlet, o Grid Layout possui características específicas:

1. **Header Toolbar Integrado**: Botões de ação, filtros e configurações na parte superior
2. **Masonry Grid**: Cards de diferentes alturas se auto-organizam
3. **Visual Feedback**: Hover states, drop zones, drag indicators
4. **Section Headers**: Cabeçalhos de seção com cores e ícones
5. **Responsive Behavior**: Auto-ajuste de colunas baseado na largura

```typescript
// src/components/collaborative-feed/layouts/GridLayout/GridFeedContainer.tsx
interface GridFeedContainerProps {
  posts: ProcessedPost[];
  sections: FeedSection[];
  onPostMove: (postId: string, newPosition: Position) => void;
  onSectionCreate: (section: CreateSectionData) => void;
  collaborativeUsers: CollaborativePresence[];
}

const GridFeedContainer: React.FC<GridFeedContainerProps> = ({
  posts,
  sections,
  onPostMove,
  onSectionCreate,
  collaborativeUsers
}) => {
  const { layoutConfig, updateLayoutConfig } = useFeedLayout();
  const { dragState, dropHandlers } = useDragDropPosts();
  const { columns, gap, cardMinHeight } = layoutConfig;
  
  // Auto-responsivo baseado na largura do container
  const containerRef = useRef<HTMLDivElement>(null);
  const { width } = useResizeObserver(containerRef);
  const responsiveColumns = useMemo(() => {
    if (width < 768) return 1;
    if (width < 1024) return 2;
    if (width < 1280) return 3;
    return Math.min(columns || 4, 6);
  }, [width, columns]);

  return (
    <div 
      ref={containerRef}
      className="grid-feed-container min-h-screen bg-gradient-to-br from-slate-50 to-slate-100"
    >
      {/* Padlet-style Header Toolbar */}
      <GridLayoutHeader
        totalPosts={posts.length}
        columns={responsiveColumns}
        onColumnsChange={(cols) => updateLayoutConfig({ gridColumns: cols })}
        onAddPost={() => setShowCreatePost(true)}
        onAddSection={onSectionCreate}
        collaborativeUsers={collaborativeUsers}
        currentLayout="grid"
      />
      
      {/* Seções com Visual Design do Padlet */}
      <div className="sections-container space-y-8 p-6">
        {sections.map(section => (
          <GridSection
            key={section.id}
            section={section}
            posts={posts.filter(p => p.sectionId === section.id)}
            columns={responsiveColumns}
            onPostDrop={(postId) => onPostMove(postId, { sectionId: section.id })}
            onSectionUpdate={(updates) => updateSection(section.id, updates)}
            dragState={dragState}
          />
        ))}
        
        {/* Posts sem seção - área principal */}
        <MasonryGrid
          posts={posts.filter(p => !p.sectionId)}
          columns={responsiveColumns}
          gap={gap || 16}
          onPostMove={onPostMove}
          renderItem={(post, index) => (
            <DraggablePostCard
              key={post.id}
              post={post}
              index={index}
              variant="grid"
              showQuickActions
              onMove={(newPosition) => onPostMove(post.id, newPosition)}
            />
          )}
        />
        
        {/* Add Section Button - Padlet Style */}
        <AddSectionButton
          onAdd={onSectionCreate}
          layoutType="grid"
          className="border-2 border-dashed border-slate-300 hover:border-primary transition-colors"
        />
      </div>
    </div>
  );
};

// Componente de Header inspirado no Padlet
const GridLayoutHeader: React.FC<GridLayoutHeaderProps> = ({
  totalPosts,
  columns,
  onColumnsChange,
  onAddPost,
  onAddSection,
  collaborativeUsers,
  currentLayout
}) => {
  return (
    <div className="sticky top-0 z-20 bg-white/95 backdrop-blur-sm border-b border-slate-200 shadow-sm">
      <div className="flex items-center justify-between p-4">
        {/* Left: Title + Stats - Padlet Style */}
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold text-slate-800">
            Timeline Colaborativa
          </h1>
          <Badge variant="secondary" className="text-xs">
            {totalPosts} posts
          </Badge>
          {collaborativeUsers.length > 0 && (
            <CollaborativePresenceBadge users={collaborativeUsers} />
          )}
        </div>

        {/* Center: Layout Controls - Padlet Style */}
        <div className="flex items-center space-x-3">
          <LayoutSwitcher currentLayout={currentLayout} />
          
          <Separator orientation="vertical" className="h-6" />
          
          {/* Grid-specific controls */}
          <div className="flex items-center space-x-2">
            <Label className="text-sm text-slate-600">Colunas:</Label>
            <Select value={columns.toString()} onValueChange={(v) => onColumnsChange(parseInt(v))}>
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {[1,2,3,4,5,6].map(n => (
                  <SelectItem key={n} value={n.toString()}>{n}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Right: Actions - Padlet Style */}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onAddSection}
            className="text-slate-700 hover:text-primary"
          >
            <FolderPlus className="h-4 w-4 mr-2" />
            Nova Seção
          </Button>
          
          <Button
            onClick={onAddPost}
            className="bg-primary hover:bg-primary/90"
          >
            <Plus className="h-4 w-4 mr-2" />
            Novo Post
          </Button>
        </div>
      </div>
    </div>
  );
};

// Masonry Grid implementado com CSS Grid e JavaScript
const MasonryGrid: React.FC<MasonryGridProps> = ({
  posts,
  columns,
  gap,
  onPostMove,
  renderItem
}) => {
  const gridRef = useRef<HTMLDivElement>(null);
  const [columnHeights, setColumnHeights] = useState<number[]>([]);

  // Layout masonry dinâmico
  useLayoutEffect(() => {
    if (!gridRef.current) return;

    const grid = gridRef.current;
    const items = Array.from(grid.children) as HTMLElement[];
    const heights = new Array(columns).fill(0);

    items.forEach((item, index) => {
      const shortestColumn = heights.indexOf(Math.min(...heights));
      
      item.style.position = 'absolute';
      item.style.left = `${(shortestColumn * (100 / columns))}%`;
      item.style.top = `${heights[shortestColumn]}px`;
      item.style.width = `calc(${100 / columns}% - ${gap / 2}px)`;
      
      heights[shortestColumn] += item.offsetHeight + gap;
    });

    setColumnHeights(heights);
    grid.style.height = `${Math.max(...heights)}px`;
  }, [posts, columns, gap]);

  return (
    <div
      ref={gridRef}
      className="masonry-grid relative"
      style={{ minHeight: Math.max(...columnHeights) }}
    >
      {posts.map((post, index) => renderItem(post, index))}
    </div>
  );
};
```

### **5.3. Timeline Layout Implementation**
```typescript
// src/components/collaborative-feed/layouts/TimelineLayout/TimelineFeedContainer.tsx
const TimelineFeedContainer: React.FC<TimelineFeedProps> = ({
  posts,
  dateRange,
  onDateRangeChange,
  onPostMove
}) => {
  const timelinePosts = useMemo(() => 
    posts.map(post => ({
      ...post,
      timelineDate: post.position?.timeline_date || post.created_at
    })).sort((a, b) => new Date(a.timelineDate).getTime() - new Date(b.timelineDate).getTime())
  , [posts]);

  return (
    <div className="timeline-feed-container">
      {/* Controles de navegação temporal */}
      <TimelineControls
        dateRange={dateRange}
        onDateRangeChange={onDateRangeChange}
        onZoomChange={(zoom) => updateLayoutConfig({ timelineZoom: zoom })}
      />
      
      {/* Eixo principal da timeline */}
      <TimelineAxis
        posts={timelinePosts}
        orientation={layoutConfig.timelineOrientation}
        onPostDrop={(postId, newDate) => 
          onPostMove(postId, { timeline_date: newDate })
        }
      />
      
      {/* Posts posicionados na timeline */}
      {timelinePosts.map((post, index) => (
        <TimelinePostCard
          key={post.id}
          post={post}
          position={calculateTimelinePosition(post, index)}
          onMove={(newDate) => onPostMove(post.id, { timeline_date: newDate })}
        />
      ))}
    </div>
  );
};
```

### **5.4. Canvas Layout Implementation** 
```typescript
// src/components/collaborative-feed/layouts/CanvasLayout/CanvasFeedContainer.tsx
const CanvasFeedContainer: React.FC<CanvasFeedProps> = ({
  posts,
  onPostMove,
  onPostResize
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const { zoom, pan } = useCanvasControls();
  const { cursors } = useCollaborativeCursors();

  return (
    <div 
      ref={canvasRef}
      className="canvas-feed-container relative overflow-hidden"
      style={{ transform: `scale(${zoom}) translate(${pan.x}px, ${pan.y}px)` }}
    >
      {/* Cursores colaborativos */}
      {cursors.map(cursor => (
        <CollaborativeCursor
          key={cursor.userId}
          userId={cursor.userId}
          position={cursor.position}
          activity={cursor.activity}
        />
      ))}
      
      {/* Posts posicionados livremente */}
      {posts.map(post => (
        <CanvasPostCard
          key={post.id}
          post={post}
          position={post.position || { x: 0, y: 0 }}
          size={post.size || { width: 300, height: 200 }}
          onMove={(newPosition) => onPostMove(post.id, newPosition)}
          onResize={(newSize) => onPostResize(post.id, newSize)}
          zIndex={post.position?.z_index || 0}
        />
      ))}
      
      {/* Ferramentas de canvas */}
      <CanvasToolbar
        zoom={zoom}
        onZoomChange={setZoom}
        onResetView={() => setPan({ x: 0, y: 0 })}
      />
    </div>
  );
};
```

---

## 6. ⚡ Real-time Implementation (WebSocket/Supabase)

### **6.1. Extensão do UnifiedRealtimeProvider**
```typescript
// src/lib/realtime/handlers/CollaborativeHandler.ts
export class CollaborativeHandler {
  constructor(private queryClient: QueryClient) {}

  handlePostPositionChange = (payload: RealtimePostgresChangesPayload<PostPosition>) => {
    const { eventType, new: newPosition, old: oldPosition } = payload;
    
    switch (eventType) {
      case 'INSERT':
      case 'UPDATE':
        // Invalidar cache de posições
        this.queryClient.setQueryData(
          ['post-positions', newPosition.layout_type, newPosition.user_id],
          (oldData: PostPosition[]) => {
            return oldData?.map(pos => 
              pos.post_id === newPosition.post_id ? newPosition : pos
            ) || [newPosition];
          }
        );
        
        // Emitir evento para listeners
        window.dispatchEvent(new CustomEvent('vindula-post-moved', {
          detail: { 
            postId: newPosition.post_id,
            layoutType: newPosition.layout_type,
            position: newPosition,
            userId: newPosition.user_id
          }
        }));
        break;
    }
  };

  handlePresenceChange = (payload: RealtimePostgresChangesPayload<CollaborativePresence>) => {
    const { eventType, new: newPresence } = payload;
    
    // Atualizar presença colaborativa
    window.dispatchEvent(new CustomEvent('vindula-presence-updated', {
      detail: {
        userId: newPresence.user_id,
        presence: newPresence,
        eventType
      }
    }));
  };

  handleSectionChange = (payload: RealtimePostgresChangesPayload<FeedSection>) => {
    const { eventType, new: newSection, old: oldSection } = payload;
    
    // Invalidar cache de seções
    this.queryClient.invalidateQueries({
      queryKey: ['feed-sections', newSection?.layout_type]
    });
    
    // Emitir evento
    window.dispatchEvent(new CustomEvent('vindula-section-changed', {
      detail: { section: newSection || oldSection, eventType }
    }));
  };
}
```

### **6.2. WebSocket Subscriptions**
```typescript
// Adicionar ao UnifiedRealtimeProvider as novas tabelas
const subscriptions = [
  // Existentes...
  
  // Novas subscriptions colaborativas
  {
    event: '*',
    schema: 'public',
    table: 'post_positions',
    handler: collaborativeHandler.handlePostPositionChange
  },
  {
    event: '*', 
    schema: 'public',
    table: 'collaborative_presence',
    handler: collaborativeHandler.handlePresenceChange
  },
  {
    event: '*',
    schema: 'public', 
    table: 'feed_sections',
    handler: collaborativeHandler.handleSectionChange
  }
];
```

### **6.3. Hooks de Tempo Real**
```typescript
// src/hooks/collaborative-feed/useCollaborativePresence.ts
export const useCollaborativePresence = (layoutType: string) => {
  const [presence, setPresence] = useState<CollaborativePresence[]>([]);
  const [myPresence, setMyPresence] = useState<Partial<CollaborativePresence>>();

  // Escutar mudanças de presença
  useEffect(() => {
    const handlePresenceUpdate = (event: CustomEvent<PresenceUpdateDetail>) => {
      const { userId, presence, eventType } = event.detail;
      
      setPresence(current => {
        switch (eventType) {
          case 'INSERT':
          case 'UPDATE':
            return current.some(p => p.user_id === userId)
              ? current.map(p => p.user_id === userId ? presence : p)
              : [...current, presence];
          case 'DELETE':
            return current.filter(p => p.user_id !== userId);
          default:
            return current;
        }
      });
    };

    window.addEventListener('vindula-presence-updated', handlePresenceUpdate);
    return () => window.removeEventListener('vindula-presence-updated', handlePresenceUpdate);
  }, []);

  // Atualizar minha presença
  const updateMyPresence = useCallback(async (update: Partial<CollaborativePresence>) => {
    try {
      const { data } = await supabase
        .from('collaborative_presence')
        .upsert({
          ...myPresence,
          ...update,
          layout_type: layoutType,
          last_seen: new Date().toISOString()
        }, { 
          onConflict: 'user_id,layout_type' 
        })
        .select()
        .single();

      setMyPresence(data);
    } catch (error) {
      console.error('Erro ao atualizar presença:', error);
    }
  }, [myPresence, layoutType]);

  return {
    presence: presence.filter(p => p.layout_type === layoutType),
    myPresence,
    updateMyPresence
  };
};
```

---

## 7. 📡 API Endpoints Design

### **7.1. Endpoints de Layout**
```typescript
// GET /api/layouts/:companyId/:userId?
interface GetLayoutsResponse {
  layouts: FeedLayout[];
  defaultLayout: FeedLayoutType;
}

// PUT /api/layouts/:layoutId
interface UpdateLayoutRequest {
  layout_config: LayoutConfig;
  is_default?: boolean;
}

// POST /api/layouts
interface CreateLayoutRequest {
  layout_type: FeedLayoutType;
  layout_config: LayoutConfig;
  is_default?: boolean;
}
```

### **7.2. Endpoints de Posicionamento**
```typescript
// PUT /api/posts/:postId/position
interface UpdatePostPositionRequest {
  layout_type: FeedLayoutType;
  x_position?: number;
  y_position?: number;
  timeline_date?: string;
  section_id?: string;
  width?: number;
  height?: number;
  z_index?: number;
}

// GET /api/posts/positions/:layoutType/:userId
interface GetPostPositionsResponse {
  positions: PostPosition[];
}

// POST /api/posts/positions/bulk
interface BulkUpdatePositionsRequest {
  updates: Array<{
    post_id: string;
    position: Partial<PostPosition>;
  }>;
}
```

### **7.3. Endpoints de Seções**
```typescript
// GET /api/sections/:companyId/:layoutType
interface GetSectionsResponse {
  sections: FeedSection[];
}

// POST /api/sections
interface CreateSectionRequest {
  title: string;
  description?: string;
  layout_type: FeedLayoutType;
  color?: string;
  icon?: string;
  visibility: 'company' | 'department' | 'team' | 'private';
}

// PUT /api/sections/:sectionId
interface UpdateSectionRequest {
  title?: string;
  description?: string;
  color?: string;
  icon?: string;
  order_index?: number;
  visual_config?: Record<string, any>;
}

// DELETE /api/sections/:sectionId
interface DeleteSectionResponse {
  success: boolean;
  affected_posts: number; // Posts que foram movidos para "sem seção"
}
```

### **7.4. Endpoints de Presença**
```typescript
// POST /api/presence/update
interface UpdatePresenceRequest {
  layout_type: string;
  current_section?: string;
  cursor_x?: number;
  cursor_y?: number;
  activity_type?: 'viewing' | 'editing' | 'commenting' | 'dragging';
  target_post_id?: string;
}

// GET /api/presence/:layoutType/:companyId
interface GetPresenceResponse {
  users: Array<{
    user_id: string;
    profile: {
      full_name: string;
      avatar_url?: string;
    };
    presence: CollaborativePresence;
  }>;
}
```

---

## 7.5. 🎨 Componentes Visuais Inspirados no Padlet

### **7.5.1. DraggablePostCard - Card Estilo Padlet**
```typescript
// src/components/collaborative-feed/cards/DraggablePostCard.tsx
interface DraggablePostCardProps {
  post: ProcessedPost;
  variant: 'grid' | 'timeline' | 'canvas' | 'stream';
  showQuickActions?: boolean;
  onMove: (position: Position) => void;
  isDragging?: boolean;
}

const DraggablePostCard: React.FC<DraggablePostCardProps> = ({
  post,
  variant,
  showQuickActions = true,
  onMove,
  isDragging = false
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const { attributes, listeners, setNodeRef, transform, isDragging: dndIsDragging } = useDraggable({
    id: post.id,
    data: { post, type: 'post' }
  });

  // Estilos baseados no design do Padlet
  const cardStyles = useMemo(() => {
    const baseStyles = "relative bg-white rounded-lg border border-slate-200 shadow-sm transition-all duration-200 cursor-pointer";
    
    switch (variant) {
      case 'grid':
        return cn(baseStyles, 
          "hover:shadow-md hover:border-primary/20 hover:-translate-y-1",
          isDragging && "rotate-3 scale-105 shadow-xl z-50",
          isHovered && showQuickActions && "ring-2 ring-primary/10"
        );
      case 'timeline':
        return cn(baseStyles, 
          "max-w-sm hover:shadow-lg",
          isDragging && "rotate-1 scale-110 shadow-xl z-50"
        );
      case 'canvas':
        return cn(baseStyles,
          "absolute hover:shadow-lg border-2",
          isDragging && "shadow-2xl scale-110 z-50",
          "resize overflow-hidden"
        );
      default:
        return baseStyles;
    }
  }, [variant, isDragging, isHovered, showQuickActions]);

  const transformStyle = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0) ${isDragging ? 'rotate(3deg) scale(1.05)' : ''}`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      className={cardStyles}
      style={transformStyle}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      {...attributes}
      {...listeners}
    >
      {/* Drag Handle - Padlet Style */}
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="w-8 h-8 bg-slate-100 hover:bg-slate-200 rounded-full flex items-center justify-center cursor-move">
          <GripVertical className="w-4 h-4 text-slate-500" />
        </div>
      </div>

      {/* Post Content */}
      <div className="p-4">
        {/* Author Info */}
        <div className="flex items-center space-x-2 mb-3">
          <OptimizedAvatar 
            src={post.author.avatar_url} 
            name={post.author.full_name}
            size="sm"
          />
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-slate-900 truncate">
              {post.author.full_name}
            </p>
            <p className="text-xs text-slate-500">
              {formatDistanceToNow(new Date(post.created_at), { locale: ptBR })}
            </p>
          </div>
        </div>

        {/* Post Content */}
        <div className="space-y-3">
          {/* Multimedia Content */}
          {post.multimedia_attachments && post.multimedia_attachments.length > 0 && (
            <MultimediaAttachments attachments={post.multimedia_attachments} />
          )}

          {/* Text Content */}
          <div className="prose prose-sm max-w-none text-slate-700">
            {post.content}
          </div>

          {/* Post Metadata */}
          <div className="flex items-center justify-between pt-2 border-t border-slate-100">
            <div className="flex items-center space-x-4 text-xs text-slate-500">
              <span className="flex items-center">
                <Eye className="w-3 h-3 mr-1" />
                {post.views || 0}
              </span>
              <span className="flex items-center">
                <MessageCircle className="w-3 h-3 mr-1" />
                {post.comments_count || 0}
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <Button variant="ghost" size="sm" className="h-6 px-2">
                <Heart className="w-3 h-3 mr-1" />
                {post.likes || 0}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions Overlay - Appears on Hover */}
      {isHovered && showQuickActions && (
        <QuickActionsOverlay
          post={post}
          onEdit={() => console.log('edit')}
          onDelete={() => console.log('delete')}
          onShare={() => console.log('share')}
        />
      )}

      {/* Collaborative Indicators */}
      <CollaborativeIndicators post={post} />
    </div>
  );
};
```

### **7.5.2. Section Container - Estilo Padlet**
```typescript
// src/components/collaborative-feed/sections/GridSection.tsx
interface GridSectionProps {
  section: FeedSection;
  posts: ProcessedPost[];
  columns: number;
  onPostDrop: (postId: string) => void;
  onSectionUpdate: (updates: Partial<FeedSection>) => void;
  dragState: DragState;
}

const GridSection: React.FC<GridSectionProps> = ({
  section,
  posts,
  columns,
  onPostDrop,
  onSectionUpdate,
  dragState
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { isOver, setNodeRef } = useDroppable({
    id: `section-${section.id}`,
    data: { section, type: 'section' }
  });

  // Visual feedback durante drag over
  const sectionStyles = cn(
    "section-container rounded-xl p-6 transition-all duration-200",
    "border-2",
    isOver && dragState.activeId 
      ? "border-primary bg-primary/5 shadow-lg scale-[1.02]" 
      : "border-slate-200 bg-white shadow-sm",
    isCollapsed && "pb-4"
  );

  return (
    <div ref={setNodeRef} className={sectionStyles}>
      {/* Section Header - Padlet Style */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          {/* Color Indicator */}
          <div 
            className="w-4 h-4 rounded-full border-2 border-white shadow-sm"
            style={{ backgroundColor: section.color }}
          />
          
          {/* Section Title */}
          <div className="flex-1">
            {editingSection === section.id ? (
              <Input
                value={editingTitle}
                onChange={(e) => setEditingTitle(e.target.value)}
                onBlur={() => handleSaveTitle()}
                onKeyPress={(e) => e.key === 'Enter' && handleSaveTitle()}
                className="text-lg font-semibold"
                autoFocus
              />
            ) : (
              <button
                onClick={() => startEditing(section)}
                className="text-lg font-semibold text-slate-800 hover:text-primary transition-colors text-left"
              >
                {section.title}
              </button>
            )}
            
            {section.description && (
              <p className="text-sm text-slate-600 mt-1">{section.description}</p>
            )}
          </div>

          {/* Post Count Badge */}
          <Badge variant="secondary" className="text-xs">
            {posts.length} {posts.length === 1 ? 'post' : 'posts'}
          </Badge>
        </div>

        {/* Section Actions */}
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            {isCollapsed ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronUp className="w-4 h-4" />
            )}
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => startEditing(section)}>
                <Edit className="w-4 h-4 mr-2" />
                Editar Seção
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleChangeColor(section)}>
                <Palette className="w-4 h-4 mr-2" />
                Mudar Cor
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => handleDeleteSection(section.id)}
                className="text-red-600"
              >
                <Trash className="w-4 h-4 mr-2" />
                Excluir Seção
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Section Content */}
      {!isCollapsed && (
        <>
          {/* Drop Zone Visual */}
          {posts.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 border-2 border-dashed border-slate-300 rounded-lg bg-slate-50/50">
              <Folder className="w-8 h-8 text-slate-400 mb-2" />
              <p className="text-sm text-slate-500 text-center">
                Arraste posts para esta seção<br />
                ou clique para adicionar conteúdo
              </p>
            </div>
          ) : (
            /* Posts Grid */
            <MasonryGrid
              posts={posts}
              columns={columns}
              gap={16}
              onPostMove={(postId, position) => onPostDrop(postId)}
              renderItem={(post, index) => (
                <DraggablePostCard
                  key={post.id}
                  post={post}
                  variant="grid"
                  showQuickActions
                  onMove={(position) => onPostDrop(post.id)}
                />
              )}
            />
          )}
        </>
      )}
    </div>
  );
};
```

### **7.5.3. Layout Switcher - Interface Padlet**
```typescript
// src/components/collaborative-feed/controls/LayoutSwitcher.tsx
const LayoutSwitcher: React.FC<{ currentLayout: FeedLayoutType }> = ({ currentLayout }) => {
  const { setLayout } = useFeedLayout();
  
  const layouts = [
    { key: 'stream', label: 'Lista', icon: List, description: 'Posts em lista cronológica' },
    { key: 'grid', label: 'Grid', icon: Grid3X3, description: 'Organização em grade flexível' },
    { key: 'timeline', label: 'Timeline', icon: Timeline, description: 'Linha do tempo visual' },
    { key: 'canvas', label: 'Canvas', icon: Paintbrush2, description: 'Posicionamento livre' },
  ] as const;

  return (
    <div className="flex items-center space-x-1 bg-slate-100 rounded-lg p-1">
      {layouts.map(layout => {
        const Icon = layout.icon;
        const isActive = currentLayout === layout.key;
        
        return (
          <TooltipProvider key={layout.key}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={isActive ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setLayout(layout.key)}
                  className={cn(
                    "h-8 px-3 transition-all",
                    isActive 
                      ? "bg-white shadow-sm text-slate-900" 
                      : "text-slate-600 hover:text-slate-900"
                  )}
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {layout.label}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>{layout.description}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      })}
    </div>
  );
};
```

### **7.5.4. Multimedia Attachments - Rich Media Padlet Style**
```typescript
// src/components/collaborative-feed/multimedia/MultimediaAttachments.tsx
const MultimediaAttachments: React.FC<{ attachments: MultimediaAttachment[] }> = ({ attachments }) => {
  if (!attachments.length) return null;

  return (
    <div className="multimedia-attachments space-y-3">
      {attachments.map(attachment => (
        <div key={attachment.id} className="attachment-item">
          {attachment.type === 'image' && (
            <div className="relative rounded-lg overflow-hidden bg-slate-100">
              <img
                src={attachment.url}
                alt="Post attachment"
                className="w-full h-auto max-h-64 object-cover"
                loading="lazy"
              />
              {attachment.metadata.width && attachment.metadata.height && (
                <Badge 
                  variant="secondary" 
                  className="absolute top-2 right-2 text-xs bg-black/50 text-white"
                >
                  {attachment.metadata.width}x{attachment.metadata.height}
                </Badge>
              )}
            </div>
          )}

          {attachment.type === 'video' && (
            <div className="relative rounded-lg overflow-hidden bg-black">
              <video
                src={attachment.url}
                poster={attachment.thumbnail_url}
                controls
                className="w-full h-auto max-h-64"
                preload="metadata"
              />
              {attachment.metadata.duration && (
                <Badge className="absolute top-2 right-2 bg-black/70 text-white text-xs">
                  {formatDuration(attachment.metadata.duration)}
                </Badge>
              )}
            </div>
          )}

          {attachment.type === 'embed' && attachment.metadata.embed_data && (
            <EmbedPreview embed={attachment.metadata.embed_data} />
          )}

          {attachment.type === 'document' && (
            <div className="flex items-center p-3 bg-slate-50 rounded-lg border">
              <FileText className="w-8 h-8 text-slate-500 mr-3" />
              <div className="flex-1 min-w-0">
                <p className="font-medium text-slate-900 truncate">
                  {attachment.filename}
                </p>
                <p className="text-sm text-slate-500">
                  {formatFileSize(attachment.size)}
                </p>
              </div>
              <Button variant="outline" size="sm" asChild>
                <a href={attachment.url} download target="_blank">
                  <Download className="w-4 h-4" />
                </a>
              </Button>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};
```

---

## 8. 📦 File Upload/Storage Strategy

### **8.1. Multimedia Upload Strategy**
```typescript
// Extensão do sistema de storage existente
interface MultimediaAttachment {
  id: string;
  type: 'image' | 'video' | 'audio' | 'document' | 'embed';
  url: string;
  thumbnail_url?: string;
  filename?: string;
  size?: number;
  metadata: {
    width?: number;
    height?: number;
    duration?: number; // para vídeo/áudio
    embed_data?: EmbedData; // para links externos
  };
}

// Upload com drag & drop
export const useMultimediaUpload = () => {
  const uploadFiles = useCallback(async (files: FileList, postId?: string) => {
    const attachments: MultimediaAttachment[] = [];
    
    for (const file of Array.from(files)) {
      // Upload para Supabase Storage
      const filePath = `posts/${companyId}/${postId || 'temp'}/${file.name}`;
      const { data, error } = await supabase.storage
        .from('attachments')
        .upload(filePath, file);
      
      if (!error) {
        // Gerar thumbnail se for imagem/vídeo
        const thumbnailUrl = await generateThumbnail(file, data.path);
        
        attachments.push({
          id: generateId(),
          type: getFileType(file),
          url: getPublicUrl(data.path),
          thumbnail_url: thumbnailUrl,
          filename: file.name,
          size: file.size,
          metadata: await extractMetadata(file)
        });
      }
    }
    
    return attachments;
  }, [companyId]);

  return { uploadFiles };
};
```

### **8.2. Embed System**
```typescript
// Sistema de embeds para links externos (YouTube, etc)
interface EmbedData {
  type: 'youtube' | 'vimeo' | 'link' | 'image';
  title?: string;
  description?: string;
  thumbnail?: string;
  embed_url?: string;
  original_url: string;
}

export const useEmbedProcessor = () => {
  const processEmbed = useCallback(async (url: string): Promise<EmbedData | null> => {
    // Detectar tipo de link
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      return await processYouTubeEmbed(url);
    }
    
    if (url.includes('vimeo.com')) {
      return await processVimeoEmbed(url);
    }
    
    // Fallback para links genéricos
    return await processGenericLink(url);
  }, []);

  return { processEmbed };
};
```

### **8.3. Storage Buckets Organization**
```sql
-- Organização de buckets no Supabase Storage
attachments/
├── posts/
│   ├── {company_id}/
│   │   ├── {post_id}/
│   │   │   ├── images/
│   │   │   ├── videos/
│   │   │   ├── documents/
│   │   │   └── thumbnails/
│   │   └── temp/ -- uploads antes da criação do post
└── collaborative/
    ├── canvas-exports/
    ├── section-backgrounds/
    └── user-cursors/ -- avatars customizados para cursores
```

---

## 9. 🚀 Performance e Scalability

### **9.1. Otimizações de Performance**
```typescript
// Virtual scrolling para grandes volumes de posts
import { useVirtualizer } from '@tanstack/react-virtual';

const VirtualizedGridLayout: React.FC = ({ posts }) => {
  const parentRef = useRef<HTMLDivElement>(null);
  
  const virtualizer = useVirtualizer({
    count: posts.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 350, // altura estimada do card
    overscan: 5 // renderizar 5 itens extras fora da tela
  });

  return (
    <div ref={parentRef} className="h-96 overflow-auto">
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative'
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => (
          <div
            key={virtualItem.key}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualItem.size}px`,
              transform: `translateY(${virtualItem.start}px)`
            }}
          >
            <GridPostCard post={posts[virtualItem.index]} />
          </div>
        ))}
      </div>
    </div>
  );
};
```

### **9.2. Lazy Loading e Code Splitting**
```typescript
// Lazy loading dos layouts
const GridLayout = lazy(() => import('./layouts/GridLayout/GridFeedContainer'));
const TimelineLayout = lazy(() => import('./layouts/TimelineLayout/TimelineFeedContainer'));
const CanvasLayout = lazy(() => import('./layouts/CanvasLayout/CanvasFeedContainer'));

const FeedLayoutRenderer: React.FC<{ layoutType: FeedLayoutType }> = ({ layoutType }) => {
  const LayoutComponent = useMemo(() => {
    switch (layoutType) {
      case 'grid':
        return GridLayout;
      case 'timeline':
        return TimelineLayout;
      case 'canvas':
        return CanvasLayout;
      default:
        return StreamLayout; // layout padrão já carregado
    }
  }, [layoutType]);

  return (
    <Suspense fallback={<FeedLayoutSkeleton layoutType={layoutType} />}>
      <LayoutComponent />
    </Suspense>
  );
};
```

### **9.3. Cache Strategy**
```typescript
// Cache inteligente por layout
export const useFeedData = (layoutType: FeedLayoutType) => {
  // Cache separado por layout
  const postsQuery = useInfiniteQuery({
    queryKey: ['posts', 'feed', layoutType, currentUser.company_id],
    queryFn: ({ pageParam = 0 }) => fetchPosts(layoutType, pageParam),
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    staleTime: 1000 * 60 * 5, // 5 minutos
    cacheTime: 1000 * 60 * 30 // 30 minutos
  });

  const positionsQuery = useQuery({
    queryKey: ['post-positions', layoutType, currentUser.id],
    queryFn: () => fetchPostPositions(layoutType),
    staleTime: 1000 * 60 * 2, // 2 minutos
    enabled: layoutType !== 'stream' // não precisa para stream
  });

  return {
    posts: postsQuery.data?.pages.flatMap(page => page.posts) || [],
    positions: positionsQuery.data || [],
    ...postsQuery
  };
};
```

### **9.4. Database Indexing Strategy**
```sql
-- Índices otimizados para performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_timeline_query 
ON posts (company_id, created_at DESC, status) 
WHERE status = 'published';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_post_positions_layout_query
ON post_positions (layout_type, user_id, company_id) 
INCLUDE (post_id, x_position, y_position);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_feed_sections_active
ON feed_sections (company_id, layout_type, order_index)
WHERE deleted_at IS NULL;

-- Particionamento da tabela de presença por data (cleanup automático)
CREATE TABLE collaborative_presence_old AS TABLE collaborative_presence WITH NO DATA;
ALTER TABLE collaborative_presence ADD CONSTRAINT presence_date_check 
CHECK (last_seen >= CURRENT_DATE - INTERVAL '1 day');
```

---

## 10. 🔒 Security Considerations

### **10.1. Segurança Multi-tenant**
```sql
-- RLS Policies rigorosas para todas as novas tabelas
CREATE POLICY "Strict multi-tenant isolation" ON post_positions
  FOR ALL USING (
    check_same_company(company_id) AND
    (user_id = auth.uid() OR check_admin_role())
  );

-- Função de validação de permissões colaborativas
CREATE OR REPLACE FUNCTION check_collaborative_permission(
  action_key TEXT,
  target_resource_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
  -- Verificar permissão genérica
  IF NOT check_permission_v2(auth.uid(), 'collaborative_feed', action_key, target_resource_id) THEN
    RETURN FALSE;
  END IF;
  
  -- Verificações específicas por ação
  CASE action_key
    WHEN 'edit_post_position' THEN
      -- Verificar se o post pertence à mesma empresa
      RETURN EXISTS (
        SELECT 1 FROM posts 
        WHERE id = target_resource_id 
          AND company_id = (SELECT company_id FROM profiles WHERE id = auth.uid())
      );
    WHEN 'create_section' THEN
      -- Verificar se o usuário pode criar seções
      RETURN check_user_permission(auth.uid(), 'feed_management', 'create_section');
    ELSE
      RETURN TRUE;
  END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### **10.2. Validação de Conteúdo**
```typescript
// Validação de uploads e conteúdo colaborativo
export const validateMultimediaUpload = (file: File): ValidationResult => {
  const maxSize = 50 * 1024 * 1024; // 50MB
  const allowedTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'video/mp4', 'video/webm', 'video/quicktime',
    'audio/mp3', 'audio/wav', 'audio/ogg',
    'application/pdf', 'text/plain'
  ];

  if (file.size > maxSize) {
    return { valid: false, error: 'Arquivo muito grande (máx. 50MB)' };
  }

  if (!allowedTypes.includes(file.type)) {
    return { valid: false, error: 'Tipo de arquivo não permitido' };
  }

  return { valid: true };
};

// Rate limiting para ações colaborativas
export const useCollaborativeRateLimit = () => {
  const rateLimiter = useRef(new Map<string, number[]>());
  
  const checkRateLimit = useCallback((action: string, maxActions: number = 10, windowMs: number = 60000) => {
    const now = Date.now();
    const actions = rateLimiter.current.get(action) || [];
    
    // Remove ações fora da janela de tempo
    const validActions = actions.filter(time => now - time < windowMs);
    
    if (validActions.length >= maxActions) {
      return false; // Rate limit excedido
    }
    
    validActions.push(now);
    rateLimiter.current.set(action, validActions);
    return true;
  }, []);

  return { checkRateLimit };
};
```

### **10.3. Audit Trail Colaborativo**
```sql
-- Log de ações colaborativas para auditoria
CREATE TABLE collaborative_audit_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  company_id UUID NOT NULL REFERENCES companies(id),
  user_id UUID NOT NULL REFERENCES profiles(id),
  action_type TEXT NOT NULL CHECK (action_type IN (
    'post_moved', 'section_created', 'section_deleted', 
    'layout_changed', 'collaborative_edit', 'presence_update'
  )),
  resource_type TEXT NOT NULL,
  resource_id UUID,
  old_data JSONB,
  new_data JSONB,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Trigger automático para log de mudanças
CREATE OR REPLACE FUNCTION log_collaborative_action()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO collaborative_audit_log (
    company_id, user_id, action_type, resource_type, resource_id,
    old_data, new_data, metadata
  ) VALUES (
    COALESCE(NEW.company_id, OLD.company_id),
    auth.uid(),
    TG_ARGV[0], -- action_type passado como parâmetro
    TG_TABLE_NAME,
    COALESCE(NEW.id, OLD.id),
    CASE WHEN OLD IS NOT NULL THEN to_jsonb(OLD) ELSE NULL END,
    CASE WHEN NEW IS NOT NULL THEN to_jsonb(NEW) ELSE NULL END,
    jsonb_build_object('trigger_operation', TG_OP, 'timestamp', NOW())
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Aplicar triggers nas tabelas colaborativas
CREATE TRIGGER post_positions_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON post_positions
  FOR EACH ROW EXECUTE FUNCTION log_collaborative_action('post_moved');

CREATE TRIGGER feed_sections_audit_trigger  
  AFTER INSERT OR UPDATE OR DELETE ON feed_sections
  FOR EACH ROW EXECUTE FUNCTION log_collaborative_action('section_changed');
```

---

## 11. 🧪 Testing Strategy

### **11.1. Testes de Componente (Jest + Testing Library)**
```typescript
// src/components/collaborative-feed/__tests__/GridLayout.test.tsx
describe('GridLayout', () => {
  const mockPosts = generateMockPosts(10);
  const mockSections = generateMockSections(3);

  test('deve renderizar posts em layout grid', async () => {
    render(
      <FeedLayoutProvider layoutType="grid">
        <GridFeedContainer 
          posts={mockPosts}
          sections={mockSections}
          onPostMove={jest.fn()}
          onSectionCreate={jest.fn()}
        />
      </FeedLayoutProvider>
    );

    expect(screen.getByRole('region', { name: /grid feed/i })).toBeInTheDocument();
    expect(screen.getAllByTestId('grid-post-card')).toHaveLength(mockPosts.length);
  });

  test('deve permitir drag and drop de posts', async () => {
    const onPostMove = jest.fn();
    
    render(
      <FeedLayoutProvider layoutType="grid">
        <GridFeedContainer 
          posts={mockPosts}
          sections={mockSections}
          onPostMove={onPostMove}
          onSectionCreate={jest.fn()}
        />
      </FeedLayoutProvider>
    );

    const firstPost = screen.getAllByTestId('grid-post-card')[0];
    const targetSection = screen.getByTestId('section-drop-zone-0');

    // Simular drag and drop
    fireEvent.dragStart(firstPost);
    fireEvent.dragOver(targetSection);
    fireEvent.drop(targetSection);

    expect(onPostMove).toHaveBeenCalledWith(
      mockPosts[0].id,
      expect.objectContaining({ sectionId: mockSections[0].id })
    );
  });
});
```

### **11.2. Testes de Integração (Playwright)**
```typescript
// tests/collaborative-feed/layouts.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Collaborative Feed Layouts', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/feed');
    await page.waitForSelector('[data-testid="feed-container"]');
  });

  test('deve alternar entre layouts', async ({ page }) => {
    // Verificar layout inicial (stream)
    await expect(page.locator('[data-testid="stream-layout"]')).toBeVisible();

    // Mudar para grid
    await page.click('[data-testid="layout-selector"]');
    await page.click('[data-testid="grid-layout-option"]');
    await expect(page.locator('[data-testid="grid-layout"]')).toBeVisible();

    // Mudar para timeline
    await page.click('[data-testid="layout-selector"]');
    await page.click('[data-testid="timeline-layout-option"]');
    await expect(page.locator('[data-testid="timeline-layout"]')).toBeVisible();
  });

  test('deve permitir reposicionamento colaborativo', async ({ page, context }) => {
    // Abrir segunda aba para teste colaborativo
    const page2 = await context.newPage();
    await page2.goto('/feed');
    
    // Mudar ambas para grid layout
    await page.click('[data-testid="layout-selector"]');
    await page.click('[data-testid="grid-layout-option"]');
    
    await page2.click('[data-testid="layout-selector"]');
    await page2.click('[data-testid="grid-layout-option"]');

    // Mover post na primeira página
    const post = page.locator('[data-testid="post-card"]').first();
    const targetSection = page.locator('[data-testid="section-drop-zone"]').first();
    
    await post.dragTo(targetSection);

    // Verificar se a mudança aparece na segunda página
    await page2.waitForTimeout(1000); // Aguardar propagação WebSocket
    const movedPost = page2.locator('[data-testid="section-0"] [data-testid="post-card"]').first();
    await expect(movedPost).toBeVisible();
  });

  test('deve mostrar presença colaborativa', async ({ page, context }) => {
    const page2 = await context.newPage();
    await page2.goto('/feed');

    // Verificar se cursores colaborativos aparecem
    await page2.waitForSelector('[data-testid="collaborative-cursor"]', { timeout: 5000 });
    const cursors = await page2.locator('[data-testid="collaborative-cursor"]').count();
    expect(cursors).toBeGreaterThan(0);
  });
});
```

### **11.3. Testes de Performance**
```typescript
// tests/performance/collaborative-feed.perf.ts
import { test, expect } from '@playwright/test';

test.describe('Collaborative Feed Performance', () => {
  test('deve carregar layout grid em menos de 2 segundos', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/feed');
    await page.click('[data-testid="grid-layout-option"]');
    await page.waitForSelector('[data-testid="grid-layout"]');
    
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(2000);
  });

  test('deve manter performance com 100+ posts', async ({ page }) => {
    // Mock 100 posts
    await page.route('**/api/posts/feed**', (route) => {
      route.fulfill({
        json: { posts: generateMockPosts(100), hasMore: false }
      });
    });

    const startTime = Date.now();
    await page.goto('/feed');
    await page.waitForSelector('[data-testid="post-card"]');
    
    const renderTime = Date.now() - startTime;
    expect(renderTime).toBeLessThan(3000);

    // Verificar scroll performance
    await page.locator('[data-testid="feed-container"]').evaluate((el) => {
      el.scrollTop = el.scrollHeight;
    });
    
    // Aguardar renderização de itens virtualizados
    await page.waitForTimeout(100);
    const visiblePosts = await page.locator('[data-testid="post-card"]:visible').count();
    expect(visiblePosts).toBeGreaterThan(5).toBeLessThan(20); // Virtual scrolling
  });
});
```

### **11.4. Testes de WebSocket**
```typescript
// src/__tests__/realtime/collaborative-realtime.test.ts
describe('Collaborative Realtime', () => {
  let mockSupabase: jest.Mocked<SupabaseClient>;
  let mockChannel: jest.Mocked<RealtimeChannel>;

  beforeEach(() => {
    mockChannel = {
      on: jest.fn().mockReturnThis(),
      subscribe: jest.fn().mockReturnThis(),
      unsubscribe: jest.fn()
    } as any;

    mockSupabase = {
      channel: jest.fn().mockReturnValue(mockChannel)
    } as any;
  });

  test('deve processar mudanças de posição em tempo real', async () => {
    const handler = new CollaborativeHandler(mockQueryClient);
    
    const mockPayload: RealtimePostgresChangesPayload<PostPosition> = {
      eventType: 'UPDATE',
      new: {
        id: 'pos-1',
        post_id: 'post-1',
        layout_type: 'grid',
        user_id: 'user-1',
        company_id: 'company-1',
        x_position: 100,
        y_position: 200
      },
      old: null,
      errors: null
    };

    // Simular evento WebSocket
    handler.handlePostPositionChange(mockPayload);

    // Verificar se evento foi emitido
    const eventSpy = jest.spyOn(window, 'dispatchEvent');
    expect(eventSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'vindula-post-moved',
        detail: expect.objectContaining({
          postId: 'post-1',
          layoutType: 'grid'
        })
      })
    );
  });

  test('deve limpar presença inativa automaticamente', async () => {
    const presenceData = {
      user_id: 'user-1',
      layout_type: 'grid',
      last_seen: new Date(Date.now() - 10 * 60 * 1000).toISOString(), // 10 min atrás
      is_active: false
    };

    // Mock da função de limpeza
    const cleanupSpy = jest.spyOn(mockSupabase, 'rpc').mockResolvedValue({ data: null });
    
    // Trigger cleanup
    await cleanupInactivePresence();
    
    expect(cleanupSpy).toHaveBeenCalledWith('cleanup_inactive_presence');
  });
});
```

---

## 12. 🔄 Migration Plan

### **12.1. Fase 1: Fundação (Semanas 1-3)**
```typescript
// Migração sem breaking changes no feed atual
interface MigrationPhase1 {
  tasks: [
    // Backend
    "Criar tabelas: feed_layouts, post_positions, feed_sections",
    "Implementar RLS policies e funções SQL",
    "Estender UnifiedRealtimeProvider com CollaborativeHandler",
    
    // Frontend  
    "Criar FeedLayoutContext e provider",
    "Refatorar EnhancedFeedPosts para suportar layouts",
    "Implementar layout Stream (manter comportamento atual)",
    "Adicionar seletor de layout na UI"
  ];
  
  criteria: {
    // Feed atual deve continuar funcionando normalmente
    backward_compatibility: true;
    performance_impact: "< 5%";
    breaking_changes: false;
  };
}
```

### **12.2. Fase 2: Grid Layout (Semanas 4-6)**
```typescript
interface MigrationPhase2 {
  tasks: [
    "Implementar GridFeedContainer e componentes relacionados",
    "Sistema de drag & drop básico",
    "Sistema de seções (FeedSection)",
    "Testes de integração Grid Layout",
    "Feature flag 'collaborative_grid_layout'"
  ];

  rollout_strategy: {
    // Rollout gradual por empresa
    canary_companies: ["internal-vindula", "beta-customers"];
    rollout_percentage: 10; // Começar com 10% dos usuários
    success_metrics: ["layout_switch_success_rate > 95%", "drag_drop_success_rate > 90%"];
  };
}
```

### **12.3. Fase 3: Timeline Layout (Semanas 7-9)**
```typescript
interface MigrationPhase3 {
  tasks: [
    "Implementar TimelineFeedContainer",
    "Sistema de posicionamento temporal",
    "Controles de navegação temporal",
    "Integração com sistema de posts agendados existente",
    "Feature flag 'collaborative_timeline_layout'"
  ];

  integration_points: {
    existing_scheduled_posts: "Aproveitar posts agendados como pontos na timeline";
    date_filters: "Integrar com filtros existentes de data";
    events_system: "Mostrar eventos corporativos na timeline";
  };
}
```

### **12.4. Fase 4: Canvas Layout (Semanas 10-13)**
```typescript
interface MigrationPhase4 {
  tasks: [
    "Implementar CanvasFeedContainer",
    "Sistema de posicionamento livre (x, y, z-index)",
    "Collaborative cursors e presença em tempo real",
    "Ferramentas de zoom e pan",
    "Sistema de resize de posts",
    "Export/import de canvas"
  ];

  advanced_features: {
    collaborative_cursors: "Mostrar posição do mouse de outros usuários";
    real_time_editing: "Edição simultânea de posts";
    canvas_tools: "Ferramentas de desenho e anotação";
  };
}
```

### **12.5. Fase 5: Otimizações e Recursos Avançados (Semanas 14-16)**
```typescript
interface MigrationPhase5 {
  tasks: [
    "Implementar virtual scrolling para performance",
    "Sistema de templates de layout",
    "Integração com AI para sugestões de organização",
    "Analytics de colaboração",
    "Export para formatos externos (PDF, PNG)",
    "Modo presentation para canvas"
  ];

  performance_optimizations: {
    virtual_scrolling: "Para feeds com 1000+ posts";
    lazy_loading: "Carregar layouts sob demanda";
    websocket_optimization: "Throttling de eventos de presença";
  };
}
```

### **12.6. Scripts de Migração**
```sql
-- Script de migração de dados existentes
-- Migration: 20250722000001_create_collaborative_feed_system.sql

BEGIN;

-- 1. Criar tabelas (já definidas anteriormente)
-- ... (tabelas definidas na seção Database Schema Changes)

-- 2. Migrar dados existentes
-- Criar layout padrão 'stream' para todos os usuários/empresas existentes
INSERT INTO feed_layouts (company_id, user_id, layout_type, layout_config, is_default)
SELECT 
  c.id as company_id,
  NULL as user_id, -- configuração global da empresa
  'stream' as layout_type,
  '{"cardSize": "medium", "showFilters": true}' as layout_config,
  true as is_default
FROM companies c
WHERE NOT EXISTS (
  SELECT 1 FROM feed_layouts fl 
  WHERE fl.company_id = c.id AND fl.layout_type = 'stream'
);

-- 3. Criar seção padrão "Geral" para empresas que usarão grid/timeline
INSERT INTO feed_sections (company_id, created_by, title, description, layout_type, order_index)
SELECT 
  c.id,
  (SELECT p.id FROM profiles p WHERE p.company_id = c.id AND p.role = 'company_owner' LIMIT 1),
  'Geral',
  'Seção padrão para posts gerais',
  'grid',
  0
FROM companies c
WHERE EXISTS (
  SELECT 1 FROM profiles p 
  WHERE p.company_id = c.id AND p.role = 'company_owner'
);

-- 4. Criar permissões para o sistema colaborativo
INSERT INTO access_control_entries (
  company_id, principal_type, principal_id, resource_type, action_key, granted
)
SELECT 
  c.id,
  'role',
  'user',
  'collaborative_feed',
  action,
  true
FROM companies c
CROSS JOIN (VALUES 
  ('view_layouts'),
  ('switch_layout'),
  ('move_posts'),
  ('create_sections')
) AS actions(action)
WHERE NOT EXISTS (
  SELECT 1 FROM access_control_entries ace
  WHERE ace.company_id = c.id 
    AND ace.resource_type = 'collaborative_feed'
    AND ace.action_key = actions.action
);

-- 5. Habilitar feature flags para layout grid (gradual rollout)
INSERT INTO feature_flags (
  company_id, feature_key, is_enabled, config, created_by
)
SELECT 
  c.id,
  'collaborative_grid_layout',
  false, -- Desabilitado por padrão, habilitar gradualmente
  '{"beta": true, "rollout_percentage": 0}',
  (SELECT p.id FROM profiles p WHERE p.company_id = c.id AND p.role = 'company_owner' LIMIT 1)
FROM companies c
WHERE NOT EXISTS (
  SELECT 1 FROM feature_flags ff
  WHERE ff.company_id = c.id AND ff.feature_key = 'collaborative_grid_layout'
);

COMMIT;
```

---

## 13. ⏱️ Timeline e Milestones

### **📅 Cronograma Detalhado**

#### **Mês 1: Fundação e Grid Layout**
| Semana | Milestone | Entregáveis | Responsável |
|--------|-----------|-------------|-------------|
| **S1** | 🏗️ Setup Base | DB schema, migrations, RLS policies | Backend Dev |
| **S2** | 🎨 Layout Provider | FeedLayoutContext, seletor UI | Frontend Dev |
| **S3** | 📱 Stream Refactor | Refatorar feed atual para novo sistema | Frontend Dev |
| **S4** | 🔲 Grid Layout | GridFeedContainer, drag & drop básico | Frontend Dev |

#### **Mês 2: Timeline e Seções**
| Semana | Milestone | Entregáveis | Responsável |
|--------|-----------|-------------|-------------|
| **S5** | 📝 Sistema Seções | FeedSections, gerenciamento UI | Frontend Dev |
| **S6** | 🧪 Testes Grid | Testes unitários e integração | QA Engineer |
| **S7** | ⏰ Timeline Layout | TimelineFeedContainer, controles temporais | Frontend Dev |
| **S8** | 🔗 Integração Posts | Timeline + posts agendados + eventos | Backend Dev |

#### **Mês 3: Canvas e Colaboração**
| Semana | Milestone | Entregáveis | Responsável |
|--------|-----------|-------------|-------------|
| **S9** | 🧪 Testes Timeline | Testes unitários e performance | QA Engineer |
| **S10** | 🎨 Canvas Layout | CanvasFeedContainer, posicionamento livre | Frontend Dev |
| **S11** | 👥 Colaboração RT | Cursores colaborativos, presença | Frontend Dev |
| **S12** | 🔄 WebSocket Ext | CollaborativeHandler, eventos RT | Backend Dev |

#### **Mês 4: Otimizações e Finalização**
| Semana | Milestone | Entregáveis | Responsável |
|--------|-----------|-------------|-------------|
| **S13** | ⚡ Performance | Virtual scrolling, lazy loading | Frontend Dev |
| **S14** | 🧪 Testes Canvas | Testes completos, stress testing | QA Engineer |
| **S15** | 📊 Analytics | Métricas de uso, dashboard admin | Backend Dev |
| **S16** | 🚀 Deploy Prod | Rollout gradual, monitoramento | DevOps |

### **🎯 Key Performance Indicators (KPIs)**

#### **Métricas de Sucesso**
```typescript
interface CollaborativeFeedKPIs {
  adoption: {
    layout_usage_percentage: {
      stream: number; // baseline atual
      grid: number;   // meta: 40%
      timeline: number; // meta: 25%
      canvas: number;  // meta: 15%
    };
    daily_layout_switches: number; // meta: > 500/dia
    user_retention_with_new_layouts: number; // meta: 85%
  };

  engagement: {
    posts_repositioned_per_day: number; // meta: > 200/dia
    sections_created_per_company: number; // meta: 3-5
    collaborative_sessions_per_week: number; // meta: > 50
    average_session_duration_increase: number; // meta: +20%
  };

  performance: {
    layout_switch_time_ms: number; // meta: < 500ms
    drag_drop_response_time_ms: number; // meta: < 100ms
    websocket_event_latency_ms: number; // meta: < 50ms
    memory_usage_increase_percentage: number; // meta: < 15%
  };

  business: {
    premium_feature_conversion_rate: number; // meta: 12%
    support_ticket_reduction_percentage: number; // meta: -30%
    user_satisfaction_score: number; // meta: > 4.5/5
    time_spent_in_feed_increase: number; // meta: +25%
  };
}
```

---

## 14. 📦 Dependencies e Trade-offs

### **14.1. Dependências Técnicas**

#### **Novas Dependências NPM**
```json
{
  "dependencies": {
    "@dnd-kit/core": "^6.1.0",
    "@dnd-kit/sortable": "^8.0.0", 
    "@dnd-kit/utilities": "^3.2.2",
    "@tanstack/react-virtual": "^3.5.0",
    "react-hotkeys-hook": "^4.5.0",
    "fabric": "^6.0.0",
    "konva": "^9.2.0",
    "react-konva": "^18.2.10"
  },
  "devDependencies": {
    "@testing-library/user-event": "^14.5.2",
    "playwright": "^1.45.0"
  }
}

// Bundle size impact: +~250KB gzipped
// Performance impact: +5-10% render time inicial
```

#### **Dependências de Infraestrutura**
```yaml
# Supabase Extensions necessárias
extensions:
  - name: "pg_cron"  # Para limpeza automática de presença
    version: "1.4"
  - name: "postgis"  # Para Map Layout (futuro)
    version: "3.3"

# Aumento estimado no uso de recursos:
database:
  storage_increase: "+15-20%" # Novas tabelas de posicionamento
  query_complexity: "+10%" # Joins adicionais
  realtime_subscriptions: "+40%" # Mais canais WebSocket

# Redis (se implementarmos cache de presença)
redis:
  memory_usage: "+50MB por 1000 usuários ativos"
  cache_keys: "presence:*, layout:*, sections:*"
```

### **14.2. Trade-offs Arquiteturais**

#### **✅ Benefícios vs ❌ Custos**

| Aspecto | ✅ Benefícios | ❌ Custos | Mitigação |
|---------|---------------|-----------|-----------|
| **Complexidade** | UX moderna e colaborativa | +40% linhas de código | Modularização rigorosa |
| **Performance** | Layouts visuais melhores | +10% tempo render inicial | Virtual scrolling, lazy loading |
| **Storage** | Flexibilidade positioning | +20% uso banco dados | Cleanup automático, arquivamento |
| **Realtime** | Colaboração verdadeira | +40% tráfego WebSocket | Throttling, batching de eventos |
| **Testing** | Funcionalidade robusta | +60% tempo de testes | Automação, testes paralelos |

#### **🚨 Riscos e Mitigações**

```typescript
interface ProjectRisks {
  technical: {
    websocket_scalability: {
      risk: "Sobrecarga com muitos usuários simultâneos";
      probability: "medium";
      impact: "high";
      mitigation: [
        "Implementar throttling de eventos de presença",
        "Redis para cache de presença distribuído",
        "Load balancer para WebSocket connections"
      ];
    };
    
    browser_performance: {
      risk: "Canvas layout pode ser lento em dispositivos antigos";
      probability: "medium";
      impact: "medium";
      mitigation: [
        "Fallback automático para layout mais simples",
        "Detecção de performance do dispositivo",
        "Limites de posts por canvas"
      ];
    };
    
    data_migration: {
      risk: "Migração de dados existentes sem downtime";
      probability: "low";
      impact: "high";
      mitigation: [
        "Migrations incrementais",
        "Feature flags para rollback",
        "Dual-write durante transição"
      ];
    };
  };

  business: {
    user_adoption: {
      risk: "Usuários podem resistir à mudança de interface";
      probability: "medium";
      impact: "medium";
      mitigation: [
        "Manter layout stream como padrão",
        "Onboarding interativo",
        "Rollout gradual com feedback"
      ];
    };
    
    performance_perception: {
      risk: "Interface mais pesada pode ser percebida negativamente";
      probability: "low";
      impact: "medium";
      mitigation: [
        "Otimizações agressivas de performance",
        "Loading states bem desenhados",
        "Progressive enhancement"
      ];
    };
  };
}
```

### **14.3. Decisões de Design**

#### **React vs Canvas Engine**
```typescript
// DECISÃO: Usar React + DOM para Grid/Timeline, Canvas nativo apenas para Canvas Layout

// ✅ Escolhido: Híbrido
const LayoutRenderer = ({ layoutType }: { layoutType: FeedLayoutType }) => {
  switch (layoutType) {
    case 'grid':
    case 'timeline':
      return <ReactBasedLayout />; // Melhor acessibilidade, SEO
    case 'canvas':
      return <CanvasBasedLayout />; // Performance para posicionamento livre
    default:
      return <StreamLayout />;
  }
};

// ❌ Rejeitado: Canvas para tudo (problemas de acessibilidade)
// ❌ Rejeitado: React para Canvas (performance ruim com muitos elements)
```

#### **State Management**
```typescript
// DECISÃO: Context API + TanStack Query, sem Redux/Zustand adicional

interface FeedLayoutState {
  // Context API para estado de UI local
  currentLayout: FeedLayoutType;
  layoutConfig: LayoutConfig;
  selectedPosts: string[];
  dragState: DragState;
  
  // TanStack Query para dados do servidor
  posts: UseInfiniteQueryResult<PostsPage>;
  positions: UseQueryResult<PostPosition[]>;
  sections: UseQueryResult<FeedSection[]>;
  presence: UseQueryResult<CollaborativePresence[]>;
}

// Justificativa: Evitar over-engineering, usar ferramentas já estabelecidas
```

#### **Styling Strategy**
```typescript
// DECISÃO: Tailwind CSS + CSS Variables para themes

// ✅ Escolhido: Hybrid approach
const GridPostCard = () => (
  <div className="
    relative p-4 rounded-lg border
    bg-card text-card-foreground
    transition-all duration-200
    hover:shadow-md hover:border-primary/20
    draggable:cursor-move draggable:scale-105
  ">
    <style jsx>{`
      .draggable {
        --post-shadow: 0 10px 40px rgba(0,0,0,0.15);
        box-shadow: var(--post-shadow);
      }
    `}</style>
  </div>
);

// Benefícios: Consistência com sistema existente + flexibilidade para animações
```

---

## 15. 🎯 Conclusão e Próximos Passos

### **🚀 Roadmap de Implementação - Resumo Executivo**

A implementação do sistema Padlet-style no Vindula Cosmos representa uma evolução natural do feed existente, aproveitando a arquitetura sólida já estabelecida (UnifiedRealtimeProvider, sistema de permissões, multi-tenancy) para criar uma experiência colaborativa moderna.

#### **✨ Principais Vantagens da Abordagem**
- ✅ **Compatibilidade Retroativa**: Feed atual continua funcionando normalmente
- ✅ **Migração Incremental**: Rollout gradual por layout sem breaking changes
- ✅ **Arquitetura Testada**: Aproveita UnifiedRealtimeProvider e TanStack Query
- ✅ **Multi-tenant Seguro**: RLS policies rigorosas para isolamento de dados
- ✅ **Performance Otimizada**: Virtual scrolling e lazy loading desde o início

#### **🎯 ROI Esperado**
```typescript
interface ExpectedROI {
  user_engagement: "+25% tempo no feed, +40% interações";
  collaboration: "+60% posts colaborativos, +35% comentários";
  retention: "+15% retenção mensal, +20% sessões por usuário";
  premium_conversion: "+12% conversão para planos superiores";
  support_reduction: "-30% tickets relacionados ao feed";
}
```

### **🔧 Ações Imediatas**

#### **Semana 1-2: Preparação**
1. **Setup de Desenvolvimento**
   ```bash
   # Criar branch de desenvolvimento
   git checkout -b feature/collaborative-feed-padlet-style
   
   # Instalar dependências
   bun add @dnd-kit/core @dnd-kit/sortable @tanstack/react-virtual
   
   # Aplicar migrations de schema
   bun run supabase db push
   ```

2. **Arquitetura Base**
   ```typescript
   // Criar estrutura de pastas
   mkdir -p src/components/collaborative-feed/{layouts,drag-drop,sections}
   mkdir -p src/hooks/collaborative-feed
   mkdir -p src/lib/realtime/handlers
   
   // Setup de contextos
   touch src/contexts/FeedLayoutContext.tsx
   ```

#### **Semana 3-4: Desenvolvimento Core**
1. **Implementar FeedLayoutProvider**
2. **Refatorar EnhancedFeedPosts para suportar layouts**
3. **Criar primeiro layout (Grid) como POC**
4. **Setup de testes básicos**

#### **Controle de Qualidade**
- **Code Review**: Toda PR deve ter revisão de arquitetura
- **Performance Budget**: Máximo +10% no bundle size
- **Testing Coverage**: Mínimo 80% para novos componentes
- **Accessibility**: WCAG 2.1 AA compliance

### **📊 Métricas de Acompanhamento**

#### **Dashboard de Desenvolvimento** 
```typescript
interface DevelopmentMetrics {
  progress: {
    features_completed: number;
    tests_passing: number;
    performance_budget_status: 'green' | 'yellow' | 'red';
    code_coverage_percentage: number;
  };
  
  technical_debt: {
    eslint_warnings: number;
    typescript_errors: number;
    bundle_size_increase_kb: number;
    lighthouse_performance_score: number;
  };
  
  user_feedback: {
    beta_user_satisfaction: number;
    feature_request_count: number;
    bug_report_count: number;
    layout_preference_distribution: Record<FeedLayoutType, number>;
  };
}
```

### **🎓 Aprendizados e Evolução**

#### **Melhorias Futuras Identificadas**
1. **AI Integration**: Sugestões automáticas de organização de posts
2. **Advanced Analytics**: Heatmaps de interação, padrões de colaboração
3. **Mobile Enhancements**: Gestos nativos, interface otimizada
4. **Integration APIs**: Webhooks para sistemas externos

#### **Documentação Living**
Este roadmap será atualizado quinzenalmente com:
- ✅ Progresso real vs planejado
- 🔄 Mudanças de escopo ou prioridade  
- 📈 Métricas de performance e adoção
- 🐛 Bugs críticos e resoluções
- 💡 Insights de usuários e melhorias

### **🎨 Elementos Visuais Específicos do Padlet Incorporados**

Com base na análise das interfaces do Padlet, foram identificados e incorporados os seguintes elementos visuais específicos no roadmap:

#### **🔧 Melhorias de Interface Implementadas**

1. **Header Toolbar Integrado**
   - ✅ Barra superior fixa com ações rápidas (+ Nova Seção, + Novo Post)
   - ✅ Contador de posts em badge 
   - ✅ Indicadores de presença colaborativa
   - ✅ Seletor de layout tipo pill/toggle

2. **Cards Estilo Padlet**
   - ✅ Hover effects com elevação (-translate-y-1)
   - ✅ Drag handle circular no canto superior direito
   - ✅ Quick actions overlay em hover
   - ✅ Rotação e escala durante drag (rotate-3 scale-105)
   - ✅ Visual feedback rico durante interações

3. **Seções com Design Visual**
   - ✅ Indicador de cor circular na lateral
   - ✅ Cabeçalhos editáveis inline
   - ✅ Badges de contagem de posts
   - ✅ Drop zones visuais com feedback imediato
   - ✅ Estados collapsed/expanded

4. **Masonry Grid Responsivo**
   - ✅ Auto-ajuste de colunas baseado na largura
   - ✅ Cards de alturas variáveis auto-organizados
   - ✅ Gaps configuráveis entre elementos
   - ✅ Performance otimizada com virtual scrolling

5. **Rich Media Support**
   - ✅ Preview inteligente de imagens com badges de dimensões
   - ✅ Player de vídeo com poster e duração
   - ✅ Embeds seguros para YouTube/Vimeo
   - ✅ Preview de documentos com ações de download

#### **🚀 Diferencial Competitivo vs Padlet Original**

| Aspecto | Padlet Original | Vindula Collaborative Feed |
|---------|-----------------|---------------------------|
| **Multi-tenancy** | ❌ Não tem | ✅ RLS + company isolation |
| **Permissions** | ❌ Básico | ✅ Sistema granular ACL |
| **Real-time** | ✅ WebSocket | ✅ WebSocket + optimized |
| **Mobile** | ✅ Responsivo | ✅ Native + PWA |
| **Integration** | ❌ Limitado | ✅ Sistema corporativo |
| **Analytics** | ❌ Básico | ✅ Dashboard avançado |
| **Security** | ❌ Básico | ✅ Enterprise-grade |

#### **🎯 UX Enhancements Específicos**

```typescript
// Micro-interações que fazem a diferença
const padletStyleEnhancements = {
  dragFeedback: "rotate-3 scale-105 shadow-xl", // Padlet signature
  hoverStates: "hover:shadow-md hover:-translate-y-1", 
  dropZones: "border-2 border-dashed border-primary/50",
  colorIndicators: "w-4 h-4 rounded-full border-2 border-white",
  quickActions: "opacity-0 group-hover:opacity-100 transition-opacity",
  collaborativeCursors: "absolute z-50 pointer-events-none",
  sectionHeaders: "text-lg font-semibold text-slate-800 hover:text-primary",
  responsiveColumns: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
};
```

---

**🎯 Próxima Ação**: Aprovação stakeholders → Setup ambiente desenvolvimento → Início Fase 1

**📞 Ponto de Contato**: Time de Desenvolvimento Vindula Internet  
**📅 Próxima Revisão**: 2025-07-29 (Revisão semanal de progresso)

---

*Este documento é um roadmap técnico living - será atualizado conforme o desenvolvimento progride e novos requisitos emergem. As análises visuais do Padlet foram incorporadas para garantir uma experiência de usuário competitiva e familiar.*