# 🎯 Roadmap: Unificação dos Editores com TipTap

**<AUTHOR> Internet 2025**  
**Data:** 2025-01-25  
**Status:** 📋 Planejamento  
**Prioridade:** 🔥 Alta  

## 🎯 Objetivo

Unificar todos os editores da plataforma usando o **PostEditor** (TipTap) como base, garantindo consistência de UX e funcionalidades de IA em todos os contextos.

## 🐛 Issue Relacionada

- **Issue #252**: "Opções inoperantes ao criar um template"
- **Causa**: Formulário HTML simples não responde aos cliques
- **Solução**: Substituir por PostEditor unificado

## 📋 Fases do Projeto

### **FASE 1: Correção Imediata - GenerateTemplateModal** ⚡
**Prazo**: 1-2 dias  
**Prioridade**: 🔥 Crítica (Fix Issue #252)

#### Tarefas:
- [ ] Substituir `<Textarea>` por `PostEditor` no `GenerateTemplateModal`
- [ ] Adaptar lógica de validação para conteúdo HTML
- [ ] Testar upload de imagens no contexto de templates
- [ ] Validar funcionalidades de IA (geração, melhoria)
- [ ] Atualizar testes E2E se necessário

#### Arquivos Impactados:
- `src/components/knowledge/GenerateTemplateModal.tsx`
- `supabase/functions/generate-knowledge-template/index.ts` (pode precisar processar HTML)

### **FASE 2: Unificação KnowledgePageEditor** 📝
**Prazo**: 3-5 dias  
**Prioridade**: 🟡 Média

#### Problemas Atuais:
- `KnowledgePageEditor` → `KnowledgePostEditor` → `PostEditor` (muita indireção)
- Duplicação desnecessária de código
- Funcionalidades específicas (contadores) misturadas com editor

#### Solução:
```typescript
// Ao invés de KnowledgePageEditor → KnowledgePostEditor
// Usar diretamente PostEditor + hooks específicos

const KnowledgePageEditor = ({ formData, updateFormData, ... }) => {
  return (
    <div>
      <PostEditor
        content={formData.content}
        onChange={(content) => updateFormData({ content })}
        tempPostId={editPageId}
      />
      
      {/* Componentes específicos de conhecimento */}
      <WordCountFloat {...wordCountProps} />
      <ReadingTimeIndicator {...readingProps} />
    </div>
  );
};
```

#### Tarefas:
- [ ] Refatorar `KnowledgePageEditor` para usar `PostEditor` diretamente
- [ ] Extrair lógica de contadores em hooks separados (`useWordCount`, `useReadingTime`)
- [ ] Remover `KnowledgePostEditor` (duplicação)
- [ ] Migrar funcionalidades específicas para componentes auxiliares
- [ ] Testar edição de páginas existentes

### **FASE 3: Hook Unificado de Editor** 🔧
**Prazo**: 2-3 dias  
**Prioridade**: 🟢 Baixa (Otimização)

#### Objetivo:
Criar hook base para funcionalidades comuns dos editores.

```typescript
// useEditorCore.ts
export function useEditorCore(options: EditorCoreOptions) {
  const [content, setContent] = useState(options.initialContent);
  const [isLoading, setIsLoading] = useState(false);
  
  // Lógica comum: validação, autosave, upload, IA
  return {
    content,
    setContent,
    handleImageUpload,
    handleAIGeneration,
    validate,
    // ...
  };
}
```

#### Tarefas:
- [ ] Criar `useEditorCore` hook
- [ ] Extrair lógica comum dos editores
- [ ] Refatorar editores para usar o hook
- [ ] Documentar padrões de uso

## 🏗️ Arquitetura Final

```
┌─────────────────────────────────────────┐
│                PostEditor               │
│        (Base TipTap Unificado)          │
├─────────────────────────────────────────┤
│ • TipTap + Extensions                   │
│ • Upload de Imagens                     │
│ • 3 Modais de IA                        │
│ • Formatação Rica                       │
│ • Drag & Drop, Paste                    │
└─────────────────────────────────────────┘
                     ▲
          ┌──────────┼──────────┐
          │          │          │
    ┌─────────┐ ┌─────────┐ ┌─────────┐
    │  Posts  │ │Templates│ │ Pages   │
    │ Context │ │ Context │ │ Context │
    └─────────┘ └─────────┘ └─────────┘
```

## 🎯 Benefícios Esperados

### **Funcionalidades**
- ✅ **Recursos de IA unificados** em todos os editores
- ✅ **Upload de imagens consistente** (drag, drop, paste)
- ✅ **Formatação rica** (headers, listas, código, links)
- ✅ **Slash commands** para inserção rápida
- ✅ **Bubble menu** para formatação contextual

### **Desenvolvimento**
- 🔧 **Menos código duplicado** (-60% linhas de editor)
- 🛠️ **Manutenção simplificada** (uma base, múltiplos contextos)  
- 🧪 **Testes unificados** (cobrir uma vez, funciona em todos)
- 📚 **Documentação centralizada**

### **UX/UI**
- 🎨 **Consistência visual** entre editores
- ⚡ **Performance otimizada** (componente otimizado)
- 🔄 **Funcionalidades sync** (nova feature → todos os editores)

## 🚧 Riscos e Mitigações

### **Risco 1**: Quebrar funcionalidades específicas
**Mitigação**: Migração incremental, testes extensivos

### **Risco 2**: Templates existentes não funcionarem
**Mitigação**: Manter compatibilidade com HTML existente

### **Risco 3**: Performance com editores múltiplos
**Mitigação**: Lazy loading, otimização de re-renders

## 📊 Métricas de Sucesso

- [ ] **Issue #252 resolvida** - Templates funcionando 100%
- [ ] **Zero regressões** em editores existentes  
- [ ] **Redução de 60%** no código relacionado a editores
- [ ] **100% dos editores** com recursos de IA
- [ ] **Testes E2E passando** para todos os contextos

## 🎯 Issues GitHub Sugeridas

### **Issue 1**: Unificar GenerateTemplateModal com PostEditor
```
Título: "Substituir formulário HTML por PostEditor no GenerateTemplateModal"
Labels: bug, enhancement, editor
Milestone: v2.1
Fixes: #252
```

### **Issue 2**: Refatorar KnowledgePageEditor  
```
Título: "Unificar KnowledgePageEditor com PostEditor base"
Labels: refactor, editor, technical-debt
Milestone: v2.2
```

### **Issue 3**: Hook unificado para editores
```
Título: "Criar useEditorCore hook para lógica comum"
Labels: enhancement, architecture, DX
Milestone: v2.3
```

---

## 🚀 Conclusão

A unificação dos editores resolverá imediatamente a **Issue #252** e criará uma base sólida para evolução futura. Qualquer nova funcionalidade de editor (IA, formatação, etc.) será automaticamente disponibilizada em todos os contextos.

**Recomendação**: Começar imediatamente pela **Fase 1** para resolver o problema crítico, e planejar as outras fases conforme capacidade da equipe.