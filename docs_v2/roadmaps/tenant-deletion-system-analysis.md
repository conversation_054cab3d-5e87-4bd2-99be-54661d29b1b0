# Sistema de Exclusão de Tenants - Análise Completa

**Data:** 28/01/2025  
**Autor:** Vindula Internet 2025  
**Status:** Análise Concluída - Aguardando Implementação

## 📋 Resumo Executivo

Análise completa da implementação de funcionalidade de exclusão de tenants no sistema administrativo `/admin/tenants` do Vindula Cosmos. **RISCO IDENTIFICADO: ALTO** - Sistema atual possui 67% das tabelas principais sem CASCADE, dependências circulares e 232 foreign keys que podem causar exclusões descontroladas.

### Metodologia: Regra do 10º Homem Aplicada

**Perspectiva do Consenso:** Implementar exclusão direta com validações básicas.  
**Perspectiva do 10º Homem:** Exclusão de tenants é operação de **risco extremo** que requer soft delete como padrão + múltiplas camadas de segurança.

---

## 🏗️ Estrutura Atual do /admin/tenants

### Componentes Principais
- **Rota:** `/admin/tenants`
- **Componente:** `TenantDashboard` (`/src/components/vindula-admin/TenantDashboard.tsx`)
- **Proteção:** `ProtectedContent` + `useAdminLayout={true}`

### Funcionalidades Existentes ✅
- Hero Section com gestão de tenants
- Botão "Novo Tenant" com dialog de criação
- 3 widgets de estatísticas: `TotalTenantsWidget`, `PlanDistributionWidget`, `TotalUsersWidget`
- `TenantListWidget` com filtros completos e tabela detalhada

### Ações Disponíveis no Dropdown
- ✅ Ver detalhes (`TenantDetailSheet`)
- ✅ Editar informações (`TenantEditSheet`)
- ✅ Gerenciar plano (`TenantPlanSheet`)
- ✅ Acessar como admin
- ✅ Enviar notificação
- ✅ Suspender/Reativar acesso
- ❌ **AUSENTE: Excluir tenant**

### Hooks de Dados
- **useTenantList:** Listagem com validação security (`vindula-intranet` only)
- **useUpdateCompany:** UPDATE operations com validation
- **❌ AUSENTE:** `useDeleteTenant` ou similar

---

## 🔐 Sistema de Autenticação e Relacionamentos

### Estrutura de Dados Core
```typescript
interface TenantData {
  id: string;
  name: string;
  cnpj: string;
  status: string; // active, trial, suspended, cancelled
  plan: string; // Grátis, Pro, Max
  users_count: number;
  users_limit: number;
  storage_used: number;
  storage_limit: number;
  // ... outros campos
}
```

### Segurança Multi-tenant
- **✅ RLS Policies:** Implementadas com padrões padronizados
- **✅ Helper Functions:** `check_same_company()`, `check_admin_role()`, `check_user_permission()`
- **✅ AuthManager:** Robusto com cache otimizado
- **✅ Isolamento:** Efetivo na maioria das tabelas

### Dependências Críticas Identificadas
```sql
-- PROBLEMA: Dependência circular
companies.primary_manager_id -> profiles(id)
companies.substitute_manager_id -> profiles(id)
profiles.company_id -> companies(id)
```

---

## 🗄️ Mapeamento Completo de Tabelas Multi-tenant

### 🔴 TABELAS CRÍTICAS (Dados Empresariais Essenciais)

#### CASCADE (Exclusão Automática) ✅ - Apenas 2 tabelas
```sql
channels (company_id) ON DELETE CASCADE
post_its (company_id) ON DELETE CASCADE
```

#### SEM CASCADE (Bloqueio de Exclusão) ❌ - 67% das tabelas
```sql
-- CRÍTICAS que impedem exclusão
audit_logs (company_id)          -- logs de auditoria
profiles (company_id)            -- usuários da empresa  
posts (company_id)               -- conteúdo
documents (company_id)           -- biblioteca
notifications (company_id)       -- notificações
obligations (company_id)         -- documentos obrigatórios
departments (company_id)         -- estrutura organizacional
teams (company_id)               -- times
roles (company_id)               -- roles e permissões
```

### 🟡 TABELAS SECUNDÁRIAS (50+ tabelas)
- Sistema de gamificação: `medals`, `experience_history`, `user_levels`, etc.
- Sistema stardust: `stardust_balance`, `stardust_transactions`, etc.
- Configurações: `company_features`, `portlet_type_settings`, etc.
- Storage: `storage_usage`, `storage_breakdown`, etc.

### 🟢 TABELAS COM PROTEÇÃO ESPECIAL
```sql
page_views (company_id) ON DELETE SET NULL      -- Analytics preservadas
user_sessions (company_id) ON DELETE SET NULL   -- Sessões preservadas
```

### 🔵 TABELAS RECENTES (Pós-2025) - CASCADE Correto ✅
- **65 tabelas** implementadas corretamente com CASCADE
- Inclui: `tasks`, `missions`, `ai_credits_balance`, `email_campaigns`, etc.

---

## ⚠️ Riscos Críticos Identificados

### RISCO 1: Impossibilidade de Exclusão Direta
- **67% das tabelas principais** não têm CASCADE
- **Dependência circular** companies ↔ profiles impede exclusão
- **Policy RLS:** `"Impedir exclusão de empresas"` com `USING (false)` ✅ (corretamente restritiva)

### RISCO 2: Cascading Deletes Descontrolados  
- **232 foreign keys** com potencial CASCADE
- **Perda irreversível** de audit_logs (violação compliance)
- **Exclusões em massa** descontroladas

### RISCO 3: Ausência de Sistema de Exclusão Segura
- **Não existe função** `delete_company()` no schema
- **Sem auditoria** para operações críticas
- **Sem backup automático** antes de exclusões

---

## 🛡️ Solução Recomendada: Defense in Depth

### Estratégia Principal: Soft Delete + Hard Delete Controlado

#### **Camada 1: Soft Delete (Padrão)**
```sql
-- Adicionar à tabela companies
ALTER TABLE companies ADD COLUMN deleted_at TIMESTAMPTZ NULL;
ALTER TABLE companies ADD COLUMN deleted_by UUID REFERENCES auth.users(id);
ALTER TABLE companies ADD COLUMN deletion_reason TEXT;
```

#### **Camada 2: Função de Soft Delete Segura**
```sql
CREATE FUNCTION soft_delete_company(reason TEXT, confirmation TEXT)
RETURNS jsonb SECURITY DEFINER;
```

#### **Camada 3: Hard Delete para Casos Extremos**
```sql  
CREATE FUNCTION hard_delete_company(
  target_company_id UUID, 
  confirmation_text TEXT,
  admin_override BOOLEAN
) RETURNS jsonb SECURITY DEFINER;
```

#### **Camada 4: Auditoria Global**
```sql
CREATE TABLE company_deletion_logs (
  -- Tabela sem company_id para sobreviver às exclusões
  deleted_company_id UUID,
  deletion_summary JSONB,
  deleted_by UUID,
  -- ...
);
```

### Validações de Segurança Obrigatórias

1. **Isolamento Multi-tenant:** `auth.uid() + profiles` lookup (nunca aceitar company_id como parâmetro)
2. **Permissões:** `check_same_company_admin()` obrigatório
3. **Confirmação Textual:** `"EXCLUIR EMPRESA DEFINITIVAMENTE"`
4. **Rate Limiting:** Máximo 1 tentativa por hora
5. **Auditoria Dupla:** Internal + External logs
6. **Backup Automático:** Antes de qualquer exclusão definitiva

---

## 🎯 Implementação Recomendada

### Fase 1: Preparação (24h)
- [ ] Migração SQL: Adicionar campos soft delete
- [ ] Criar tabela `company_deletion_logs`
- [ ] Implementar função `soft_delete_company()`
- [ ] Atualizar RLS policies para excluir soft-deleted

### Fase 2: Interface (48h)  
- [ ] Hook `useDeleteCompany()` com validações extremas
- [ ] Componente `CompanyDeletionDialog` com múltiplas confirmações
- [ ] Integração no dropdown do `TenantListWidget`
- [ ] Testes de segurança multi-tenant

### Fase 3: Hard Delete (7 dias)
- [ ] Função `hard_delete_company()` com quebra de dependências circulares
- [ ] Sistema de backup automático
- [ ] Função `restore_company()` para soft delete
- [ ] Auditoria e compliance LGPD

### Componente React Proposto
```typescript
export function CompanyDeletionDialog() {
  const [confirmationText, setConfirmationText] = useState('');
  const deleteCompanyMutation = useDeleteCompany();
  
  const REQUIRED_CONFIRMATION = 'EXCLUIR EMPRESA DEFINITIVAMENTE';
  
  // Validações extremas + múltiplas confirmações
  // GenericPermissionGate para dupla validação
  // Auditoria completa da operação
}
```

### Hook de Exclusão Proposto
```typescript
export function useDeleteCompany() {
  return useMutation<DeletionSummary, Error, CompanyDeletionParams>({
    mutationFn: async ({ confirmationText }) => {
      // CRÍTICO: Não passar company_id (isolamento garantido via auth.uid())
      const { data, error } = await supabase.rpc('soft_delete_company', {
        confirmation_text: confirmationText
      });
      
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: () => {
      // Forçar logout (empresa não existe mais)  
      window.location.href = '/login?deleted=true';
    }
  });
}
```

---

## 📊 Score de Risco Final

| Aspecto | Score | Status |
|---------|-------|--------|
| **Multi-tenancy** | 7/10 | RLS funcional, mas cascade perigoso |
| **Data Loss Prevention** | 3/10 | Sem soft delete, sem backup automático |
| **Compliance** | 4/10 | Logs de auditoria podem ser perdidos |
| **Security** | 6/10 | Validações adequadas, mas função inexistente |

**RISCO GERAL: ALTO** ⚠️

---

## 📋 Checklist de Implementação

### Implementação Imediata (24h)
- [ ] **Migração SQL:** `20250128_tenant_deletion_system.sql`
- [ ] **Função Soft Delete:** Com validações de segurança multi-tenant
- [ ] **Tabela Auditoria:** `company_deletion_logs` (sem company_id)
- [ ] **RLS Update:** Excluir companies soft-deleted das consultas

### Implementação Prioritária (1-7 dias)
- [ ] **Hook Frontend:** `useDeleteCompany()` com validações extremas
- [ ] **Componente UI:** `CompanyDeletionDialog` com múltiplas confirmações
- [ ] **Integração:** Adicionar botão no dropdown do `TenantListWidget`
- [ ] **Função Hard Delete:** Para casos extremos (admin override)

### Validação de Segurança
- [ ] **Teste Penetração:** Tentar exclusão cross-tenant
- [ ] **Validar RLS:** Confirmar bloqueio pós soft delete
- [ ] **Auditoria:** Logs preservados em tabela externa
- [ ] **Backup:** Testar recuperação pós-exclusão

### Compliance e Documentação
- [ ] **LGPD:** Documentar processo para auditoria
- [ ] **Notificação:** Procedimento para titulares de dados
- [ ] **Retenção:** Política de backups (90 dias)
- [ ] **Treinamento:** Equipe em procedimentos seguros

---

## 🚀 Próximos Passos

1. **Revisar análise** com equipe de arquitetura
2. **Aprovar estratégia** soft delete vs hard delete  
3. **Implementar Fase 1** (migração SQL + funções core)
4. **Testes extensivos** em ambiente de desenvolvimento
5. **Deploy gradual** com feature flag
6. **Monitoramento** de operações críticas

---

**⚠️ ATENÇÃO:** Esta funcionalidade envolve **risco extremo** de perda de dados. Implementação deve seguir rigorosamente as validações de segurança propostas e ser testada extensivamente antes de produção.

**✅ RECOMENDAÇÃO FINAL:** Implementar **soft delete como padrão** com período de quarentena de 30 dias antes de permitir hard delete. Hard delete apenas para casos extremos com supervisão administrativa.