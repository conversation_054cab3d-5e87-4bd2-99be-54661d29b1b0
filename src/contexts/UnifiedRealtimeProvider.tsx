/**
 * UnifiedRealtimeProvider - Canal Catch-All Discovery Implementation
 * 
 * BREAKTHROUGH: Canal catch-all único que recebe TODOS os eventos do schema public
 * e roteia internamente para handlers especializados. Solução revolucionária
 * que garante 100% de confiabilidade para birthday cards e outros eventos.
 * 
 * <AUTHOR> Internet 2025
 */
import React, { createContext, useContext, useEffect, useMemo, useState, useCallback, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useProfile } from '@/hooks/useProfile';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { NotificationHandler } from '@/lib/realtime/handlers/NotificationHandler';
import { ChatHandler } from '@/lib/realtime/handlers/ChatHandler';
import { PostsHandler } from '@/lib/realtime/handlers/PostsHandler';
import { GamificationHandler } from '@/lib/realtime/handlers/GamificationHandler';
import { AuthHandler } from '@/lib/realtime/handlers/AuthHandler';
import { ProfileHandler } from '@/lib/realtime/handlers/ProfileHandler';
import { ObligationsHandler } from '@/lib/realtime/handlers/ObligationsHandler';
import { PostItHandler } from '@/lib/realtime/handlers/PostItHandler';

type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'error';

interface ConnectionMetrics {
  activeConnections: number;
  totalMessages: number;
  averageLatency: number;
  memoryUsage: number;
  lastActivity: Date;
}

interface RealtimeHandlers {
  notifications?: NotificationHandler;
  chat?: ChatHandler;
  posts?: PostsHandler;
  gamification?: GamificationHandler;
  auth?: AuthHandler;
  profile?: ProfileHandler;
  obligations?: ObligationsHandler;
  postits?: PostItHandler;
}

interface UnifiedRealtimeState {
  // Handlers especializados
  handlers: RealtimeHandlers;
  
  // Estado da conexão
  isConnected: boolean;
  connectionStatus: ConnectionStatus;
  connectionCount: number;
  
  // Métricas
  metrics: ConnectionMetrics;
  
  // Métodos de controle
  reconnect: () => Promise<void>;
  disconnect: () => void;
  getDebugInfo: () => Record<string, unknown>;
  
  // Compatibility methods (manter interface existente)
  markAsRead: (notificationId: string) => Promise<void>;
  achievedMedal: { medal: any; autoOpen: boolean } | null;
  clearAchievedMedal: () => void;
  birthdayCardData: any;
  clearBirthdayCard: () => void;
  promotionCelebrationData: any;
  clearPromotionCelebration: () => void;
  shouldShowNotification: boolean;
  toggleNotifications: (enabled: boolean) => void;
}

const UnifiedRealtimeContext = createContext<UnifiedRealtimeState | null>(null);

interface UnifiedRealtimeProviderProps {
  children: React.ReactNode;
  enableDebug?: boolean;
}

export const UnifiedRealtimeProvider: React.FC<UnifiedRealtimeProviderProps> = ({ 
  children, 
  enableDebug = false 
}) => {
  const { profile } = useProfile();
  const queryClient = useQueryClient();
  
  // Estados
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [handlers, setHandlers] = useState<RealtimeHandlers>({});
  const [shouldShowNotification, setShouldShowNotification] = useState(true);
  const [metrics, setMetrics] = useState<ConnectionMetrics>({
    activeConnections: 0,
    totalMessages: 0,
    averageLatency: 0,
    memoryUsage: 0,
    lastActivity: new Date()
  });
  
  // Estados reativos para gamificação
  const [achievedMedal, setAchievedMedal] = useState<{ medal: any; autoOpen: boolean } | null>(null);
  const [birthdayCardData, setBirthdayCardData] = useState<any>(null);
  const [promotionCelebrationData, setPromotionCelebrationData] = useState<any>(null);
  
  // Refs para canais
  const catchAllChannelRef = useRef<any>(null);
  const metricsRef = useRef<ConnectionMetrics>(metrics);
  const isMountedRef = useRef(true);
  
  // Atualizar métricas ref
  useEffect(() => {
    metricsRef.current = metrics;
  }, [metrics]);
  
  
  // Cleanup no unmount
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);
  
  // Listeners para eventos de gamificação
  useEffect(() => {
    const handleAchievedMedalChanged = (event: CustomEvent) => {
      logQueryEvent('UnifiedRealtime', '🏅 achievedMedal mudou via evento', { 
        newAchievedMedal: event.detail.achievedMedal,
        autoOpen: event.detail.autoOpen
      });
      setAchievedMedal({
        medal: event.detail.achievedMedal,
        autoOpen: event.detail.autoOpen ?? true // Default true para compatibilidade
      });
    };
    
    const handleBirthdayCardChanged = (event: CustomEvent) => {
      logQueryEvent('UnifiedRealtime', '🎂 birthdayCardData mudou via evento', { 
        newBirthdayCardData: event.detail.birthdayCardData 
      });
      setBirthdayCardData(event.detail.birthdayCardData);
    };
    
    const handlePromotionCelebrationChanged = (event: CustomEvent) => {
      logQueryEvent('UnifiedRealtime', '🎉 promotionCelebrationData mudou via evento', { 
        newPromotionData: event.detail.promotionData 
      });
      setPromotionCelebrationData(event.detail.promotionData);
    };
    
    window.addEventListener('vindula-achieved-medal-changed', handleAchievedMedalChanged as EventListener);
    window.addEventListener('vindula-birthday-card-changed', handleBirthdayCardChanged as EventListener);
    window.addEventListener('vindula-promotion-celebration-changed', handlePromotionCelebrationChanged as EventListener);
    
    return () => {
      window.removeEventListener('vindula-achieved-medal-changed', handleAchievedMedalChanged as EventListener);
      window.removeEventListener('vindula-birthday-card-changed', handleBirthdayCardChanged as EventListener);
      window.removeEventListener('vindula-promotion-celebration-changed', handlePromotionCelebrationChanged as EventListener);
    };
  }, []);
  
  // Inicializar handlers
  useEffect(() => {
    if (!profile?.id) {
      // Profile aguardando inicialização
      return;
    }

    // Inicializando handlers especializados

    const newHandlers: RealtimeHandlers = {
      notifications: new NotificationHandler(queryClient, profile, setAchievedMedal, setBirthdayCardData),
      chat: new ChatHandler(queryClient, profile),
      posts: new PostsHandler(queryClient, profile),
      gamification: new GamificationHandler(queryClient, profile, setAchievedMedal),
      auth: new AuthHandler(queryClient, profile),
      profile: new ProfileHandler(queryClient, profile),
      obligations: new ObligationsHandler(queryClient, profile),
      postits: new PostItHandler(queryClient, profile),
    };

    setHandlers(newHandlers);
    // Handlers inicializados
  }, [profile?.id, profile?.company_id, queryClient]);

  // Helper para atualizar métricas
  const updateMetrics = useCallback((updates: Partial<ConnectionMetrics>) => {
    if (!isMountedRef.current) return;
    
    setMetrics(prev => ({
      ...prev,
      ...updates,
      lastActivity: new Date()
    }));
  }, []);

  // Master Router - Distribui eventos para handlers especializados
  const routeToSpecializedHandler = useCallback((payload: any) => {
    const { table, eventType } = payload;
    
    console.log('🎯 [CATCH-ALL] 🔀 ROUTING EVENT:', {
      table,
      eventType: payload.eventType,
      id: payload.new?.id || payload.old?.id,
      timestamp: new Date().toISOString()
    });

    updateMetrics({ totalMessages: metricsRef.current.totalMessages + 1 });

    switch(table) {
      case 'notifications':
        console.log('🎯 [CATCH-ALL] 📢 NOTIFICAÇÃO DETECTADA:', {
          type: payload.new?.type,
          isBirthdayCard: payload.new?.type === 'mention' && payload.new?.metadata?.card_type === 'birthday_card'
        });
        
        if (handlers.notifications) {
          console.log('✅ [CATCH-ALL] Handler notifications disponível, processando...');
          handlers.notifications.process(payload);
        } else {
          console.log('❌ [CATCH-ALL] Handler notifications não disponível, usando fallback...');
          
          // FALLBACK: processar birthday cards manualmente
          if (payload.new?.type === 'mention' && payload.new?.metadata?.card_type === 'birthday_card') {
            console.log('🎂 [CATCH-ALL] FALLBACK: Processando birthday card manualmente');
            const cardData = {
              id: payload.new.metadata.card_id,
              message: payload.new.metadata.message,
              sender_name: payload.new.metadata.sender_name,
              background_config: payload.new.metadata.background_config || {},
              media_url: payload.new.metadata.media_url,
              media_type: payload.new.metadata.media_type,
              media_duration: payload.new.metadata.media_duration,
            };
            
            const event = new CustomEvent('vindula-birthday-card-changed', { 
              detail: { birthdayCardData: cardData }
            });
            window.dispatchEvent(event);
          }
        }
        break;
        
      case 'chat_messages':
        if (handlers.chat) {
          // Diferenciar entre INSERT (nova mensagem) e UPDATE (edição)
          if (payload.eventType === 'INSERT') {
            handlers.chat.processMessage(payload);
          } else if (payload.eventType === 'UPDATE') {
            console.log('🎯 [CATCH-ALL] ✏️ CHAT MESSAGE UPDATE DETECTADO (edição)');
            handlers.chat.processMessageUpdate(payload);
          } else if (payload.eventType === 'DELETE') {
            // TODO: implementar processMessageDelete se necessário
            console.log('🎯 [CATCH-ALL] 🗑️ CHAT MESSAGE DELETE DETECTADO');
          }
        }
        break;
        
      case 'channels':
        console.log('🎯 [CATCH-ALL] 📢 CHANNEL EVENT DETECTADO:', {
          eventType: payload.eventType,
          channelId: payload.new?.id || payload.old?.id,
          channelName: payload.new?.name || payload.old?.name,
          isPublic: payload.new?.is_public,
          isArchived: payload.new?.is_archived
        });
        
        if (handlers.chat) {
          handlers.chat.processChannel(payload);
        }
        break;
        
      case 'channel_members':
        console.log('🎯 [CATCH-ALL] 👥 CHANNEL_MEMBERS EVENT DETECTADO:', {
          eventType: payload.eventType,
          channelId: payload.new?.channel_id || payload.old?.channel_id,
          userId: payload.new?.user_id || payload.old?.user_id,
          role: payload.new?.role || payload.old?.role,
          memberId: payload.new?.id || payload.old?.id
        });
        
        if (handlers.chat) {
          handlers.chat.processChannelMember(payload);
        }
        break;
        
      case 'message_reactions':
        console.log('🎯 [CATCH-ALL] 😊 MESSAGE_REACTIONS EVENT DETECTADO:', {
          eventType: payload.eventType,
          messageId: payload.new?.message_id || payload.old?.message_id,
          userId: payload.new?.user_id || payload.old?.user_id,
          emoji: payload.new?.emoji || payload.old?.emoji,
          reactionId: payload.new?.id || payload.old?.id
        });
        
        if (handlers.chat) {
          handlers.chat.processMessageReaction(payload);
        }
        break;
        
      case 'message_read_receipts':
        console.log('🎯 [CATCH-ALL] ✓ MESSAGE_READ_RECEIPTS EVENT DETECTADO:', {
          eventType: payload.eventType,
          messageId: payload.new?.message_id || payload.old?.message_id,
          userId: payload.new?.user_id || payload.old?.user_id,
          readAt: payload.new?.read_at || payload.old?.read_at,
          receiptId: payload.new?.id || payload.old?.id
        });
        
        if (handlers.chat) {
          handlers.chat.processReadReceipt(payload);
        }
        break;
        
      case 'chat_participants':
        console.log('🎯 [CATCH-ALL] 💬 CHAT_PARTICIPANTS EVENT DETECTADO:', {
          eventType: payload.eventType,
          chatId: payload.new?.chat_id || payload.old?.chat_id,
          userId: payload.new?.user_id || payload.old?.user_id,
          participantId: payload.new?.id || payload.old?.id
        });
        
        // Invalidar queries relacionadas a participantes de chat
        queryClient.invalidateQueries({ queryKey: ['chat-participants'] });
        queryClient.invalidateQueries({ queryKey: ['user-chats'] });
        
        // Disparar evento customizado
        const chatParticipantEvent = new CustomEvent('vindula-chat-participant-changed', {
          detail: {
            participant: payload.new || payload.old,
            payload,
            eventType: payload.eventType,
            timestamp: new Date().toISOString()
          }
        });
        window.dispatchEvent(chatParticipantEvent);
        break;
        
      case 'posts':
        if (handlers.posts) {
          handlers.posts.processPost(payload);
        }
        break;
        
      case 'post_likes':
        if (handlers.posts) {
          handlers.posts.processLike(payload);
        }
        break;
        
      case 'comments':
        if (handlers.posts) {
          handlers.posts.processComment(payload);
        }
        break;
        
      case 'stardust_transactions':
        if (handlers.gamification) {
          handlers.gamification.processStardust(payload);
        }
        break;
        
      case 'user_missions':
        if (handlers.gamification) {
          handlers.gamification.processMission(payload);
        }
        break;
        
      case 'user_levels':
        console.log('🎯 [CATCH-ALL] 📊 USER_LEVELS DETECTADO - Disparando evento');
        // Disparar evento para useUserLevel capturar
        const userLevelEvent = new CustomEvent('vindula-user-level-update', {
          detail: { payload }
        });
        window.dispatchEvent(userLevelEvent);
        break;
        
      case 'experience_history':
        console.log('🎯 [CATCH-ALL] 📈 EXPERIENCE_HISTORY DETECTADO - Disparando evento');
        // Disparar evento para useUserLevel capturar
        const expHistoryEvent = new CustomEvent('vindula-experience-history-insert', {
          detail: { payload }
        });
        window.dispatchEvent(expHistoryEvent);
        break;
        
      case 'user_medals':
        console.log('🎯 [CATCH-ALL] 🏆 USER_MEDALS DETECTADO');
        if (handlers.gamification) {
          // Usar método existente no GamificationHandler para medalhas
          if (typeof handlers.gamification.processMedal === 'function') {
            handlers.gamification.processMedal(payload);
          } else {
            console.log('⚠️ [CATCH-ALL] Método processMedal não existe, usando fallback de medalhas');
            // Fallback: usar o sistema de medalhas que já funciona
            // CORREÇÃO: Verificar se é uma medalha nova (INSERT) e ainda não processada
            if (payload.eventType === 'INSERT' && payload.new?.achieved_at) {
              console.log('🏆 [CATCH-ALL] Processando medalha via fallback:', {
                medalId: payload.new.medal_id || payload.new.id,
                medalName: payload.new.name,
                achieved_at: payload.new.achieved_at
              });
              
              // Preferências são verificadas via NotificationHandler agora
              const medalData = {
                id: payload.new.medal_id || payload.new.id,
                name: payload.new.name || payload.new.medal?.name || 'Medalha Conquistada',
                description: payload.new.description || payload.new.medal?.description || 'Parabéns pela conquista!',
                type: payload.new.type || 'achievement',
                icon_url: payload.new.icon_url || payload.new.medal?.icon_url || '',
                required_count: payload.new.required_count || 1,
                progress: payload.new.progress || 100,
                achieved_at: payload.new.achieved_at
              };
              
              if (handlers.gamification.showMedalModal) {
                handlers.gamification.showMedalModal(medalData);
              }
            } else {
              console.log('🏆 [CATCH-ALL] Medalha ignorada - não é INSERT ou já processada:', {
                eventType: payload.eventType,
                hasAchievedAt: !!payload.new?.achieved_at,
                medalId: payload.new?.medal_id || payload.new?.id
              });
            }
          }
        }
        break;
        
      case 'user_roles':
        if (handlers.auth) {
          handlers.auth.processRoleUpdate(payload);
        }
        break;
        
      case 'profiles':
        if (handlers.profile) {
          handlers.profile.process(payload);
        }
        break;
        
      case 'user_obligations':
        if (handlers.obligations) {
          handlers.obligations.processUserObligation(payload);
        }
        break;
        
      case 'obligations':
        if (handlers.obligations) {
          handlers.obligations.processObligation(payload);
        }
        break;
        
      case 'task_assignment_requests':
        console.log('🎯 [CATCH-ALL] 🤝 TASK_ASSIGNMENT_REQUEST DETECTADO:', {
          eventType: payload.eventType,
          from_user: payload.new?.from_user_id,
          to_user: payload.new?.to_user_id,
          task_id: payload.new?.task_id,
          status: payload.new?.status
        });
        
        // Invalidar queries relacionadas a pedidos de ajuda
        queryClient.invalidateQueries({ queryKey: ['help-requests'] });
        queryClient.invalidateQueries({ queryKey: ['tasks'] });
        
        // Disparar evento para componentes que escutam
        const taskRequestEvent = new CustomEvent('vindula-task-assignment-request-changed', {
          detail: { payload }
        });
        window.dispatchEvent(taskRequestEvent);
        break;
        
      case 'post_it_shares':
        // Dispatch evento para compatibilidade
        const event = new CustomEvent('vindula-post-it-share-changed', {
          detail: { 
            postItShare: payload.new || payload.old,
            payload
          }
        });
        window.dispatchEvent(event);
        
        if (handlers.postits) {
          handlers.postits.processShare(payload);
        }
        break;
        
      case 'post_its':
        if (handlers.postits) {
          handlers.postits.processPostIt(payload);
        }
        break;
        
      case 'channels':
        console.log('🎯 [CATCH-ALL] 📢 CHANNEL EVENT DETECTADO:', {
          eventType: payload.eventType,
          channelId: payload.new?.id || payload.old?.id,
          channelName: payload.new?.name || payload.old?.name,
          isPublic: payload.new?.is_public,
          isArchived: payload.new?.is_archived
        });
        
        if (handlers.chat) {
          handlers.chat.processChannel(payload);
        }
        break;
        
      case 'channel_members':
        console.log('🎯 [CATCH-ALL] 👥 CHANNEL_MEMBERS EVENT DETECTADO:', {
          eventType: payload.eventType,
          channelId: payload.new?.channel_id || payload.old?.channel_id,
          userId: payload.new?.user_id || payload.old?.user_id,
          role: payload.new?.role || payload.old?.role,
          memberId: payload.new?.id || payload.old?.id
        });
        
        if (handlers.chat) {
          handlers.chat.processChannelMember(payload);
        }
        break;
        
      case 'chat_participants':
        console.log('🎯 [CATCH-ALL] 🤝 CHAT_PARTICIPANTS EVENT DETECTADO:', {
          eventType: payload.eventType,
          chatId: payload.new?.chat_id || payload.old?.chat_id,
          userId: payload.new?.user_id || payload.old?.user_id,
          participantId: payload.new?.id || payload.old?.id
        });
        
        // Invalidar queries relacionadas a participantes de chat
        queryClient.invalidateQueries({ queryKey: ['user-chats'] });
        queryClient.invalidateQueries({ queryKey: ['chat-list'] });
        
        // Disparar evento para componentes que escutam
        const participantEvent = new CustomEvent('vindula-chat-participant-changed', {
          detail: { payload }
        });
        window.dispatchEvent(participantEvent);
        break;
        
      case 'typing_status':
        console.log('🎯 [CATCH-ALL] ⌨️ TYPING_STATUS EVENT DETECTADO:', {
          eventType: payload.eventType,
          userId: payload.new?.user_id || payload.old?.user_id,
          chatId: payload.new?.chat_id || payload.old?.chat_id,
          channelId: payload.new?.channel_id || payload.old?.channel_id,
          lastTypedAt: payload.new?.last_typed_at,
          typingId: payload.new?.id || payload.old?.id
        });
        
        if (handlers.chat) {
          handlers.chat.processTypingStatus(payload);
        }
        break;
        
      case 'message_read_receipts':
        console.log('🎯 [CATCH-ALL] ✓ MESSAGE_READ_RECEIPTS EVENT DETECTADO:', {
          eventType: payload.eventType,
          messageId: payload.new?.message_id || payload.old?.message_id,
          userId: payload.new?.user_id || payload.old?.user_id,
          readAt: payload.new?.read_at || payload.old?.read_at,
          receiptId: payload.new?.id || payload.old?.id
        });
        
        // ✅ CORREÇÃO: Delegar para ChatHandler para invalidação específica
        if (handlers.chat) {
          handlers.chat.processReadReceipt(payload);
        } else {
          // Fallback se ChatHandler não estiver disponível
          console.log('⚠️ [CATCH-ALL] ChatHandler não disponível, usando fallback para read receipts');
          
          // Invalidar contador global apenas (mínimo necessário)
          queryClient.invalidateQueries({ queryKey: ['unread-chat-count'] });
          
          // Invalidar read receipts da mensagem específica
          if (payload.new?.message_id || payload.old?.message_id) {
            const messageId = payload.new?.message_id || payload.old?.message_id;
            queryClient.invalidateQueries({ 
              queryKey: ['chat', 'read-receipts', messageId] 
            });
          }
        }
        
        // Disparar evento customizado para compatibilidade
        const readReceiptEvent = new CustomEvent('vindula-message-read-receipt-changed', {
          detail: {
            receipt: payload.new || payload.old,
            payload,
            eventType: payload.eventType,
            timestamp: new Date().toISOString()
          }
        });
        window.dispatchEvent(readReceiptEvent);
        break;
        
      case 'commercial_lead_history':
        // Evento interno - não precisa de handler específico no momento
        break;
        
      default:
        console.log('🎯 [CATCH-ALL] ⚠️ Tabela não mapeada:', table);
    }
  }, [handlers, updateMetrics]);

  // 🚀 CANAL CATCH-ALL MASTER - Recebe TODOS os eventos do schema public
  useEffect(() => {
    if (!profile?.id || !profile?.company_id) {
      // Profile aguardando inicialização
      return;
    }

    if (Object.keys(handlers).length === 0) {
      // Handlers aguardando inicialização
      return;
    }

    // Criando canal catch-all master
    // Handlers especializados disponíveis

    // CANAL CATCH-ALL: recebe TUDO do schema public
    const catchAllChannel = supabase
      .channel(`vindula-catch-all-${profile.id}`)
      .on('postgres_changes', {
        event: '*',      // TODOS os eventos (INSERT, UPDATE, DELETE)
        schema: 'public' // TODO o schema público
        // SEM FILTRO DE TABELA - recebe de TODAS as tabelas
      }, (payload) => {
        // Evento recebido via catch-all
        
        // Router master distribui para handlers
        routeToSpecializedHandler(payload);
      })
      .subscribe((status) => {
        // Status do canal catch-all
        logQueryEvent('UnifiedRealtime', 'Status catch-all channel', { status });
        
        if (status === 'SUBSCRIBED') {
          setConnectionStatus('connected');
          updateMetrics({ activeConnections: 1 });
          // Canal ativo recebendo eventos
        } else if (status === 'CHANNEL_ERROR') {
          setConnectionStatus('error');
          console.error('❌ [CATCH-ALL] Erro no canal!');
        } else if (status === 'TIMED_OUT') {
          setConnectionStatus('error');
          console.error('❌ [CATCH-ALL] Timeout no canal!');
        } else if (status === 'CLOSED') {
          setConnectionStatus('disconnected');
          updateMetrics({ activeConnections: 0 });
        }
      });

    catchAllChannelRef.current = catchAllChannel;

    // Cleanup function
    return () => {
      if (catchAllChannelRef.current) {
        console.log('🧹 [CATCH-ALL] Limpando canal catch-all');
        logQueryEvent('UnifiedRealtime', '🧹 Limpando canal catch-all', { 
          channelTopic: catchAllChannelRef.current.topic 
        });
        supabase.removeChannel(catchAllChannelRef.current);
        catchAllChannelRef.current = null;
      }
    };
  }, [profile?.id, profile?.company_id, handlers, updateMetrics, routeToSpecializedHandler]);

  // Métodos de controle
  const reconnect = useCallback(async () => {
    logQueryEvent('UnifiedRealtime', 'Reconectando...', {});
    setConnectionStatus('connecting');
    
    if (catchAllChannelRef.current) {
      supabase.removeChannel(catchAllChannelRef.current);
      catchAllChannelRef.current = null;
    }
    
    // Força re-render para recriar canal
    setConnectionStatus('disconnected');
  }, []);

  const disconnect = useCallback(() => {
    logQueryEvent('UnifiedRealtime', 'Desconectando...', {});
    
    if (catchAllChannelRef.current) {
      supabase.removeChannel(catchAllChannelRef.current);
      catchAllChannelRef.current = null;
    }
    
    setConnectionStatus('disconnected');
    updateMetrics({ activeConnections: 0 });
  }, [updateMetrics]);

  const getDebugInfo = useCallback(() => {
    return {
      connectionStatus,
      metrics,
      handlers: Object.keys(handlers),
      channelActive: !!catchAllChannelRef.current,
      profileId: profile?.id,
      companyId: profile?.company_id,
    };
  }, [connectionStatus, metrics, handlers, profile]);

  // Compatibility methods
  const markAsRead = useCallback(async (notificationId: string) => {
    if (handlers.notifications) {
      await handlers.notifications.markAsRead(notificationId);
    }
  }, [handlers.notifications]);

  const clearAchievedMedal = useCallback(() => {
    setAchievedMedal(null);
  }, []);

  const clearBirthdayCard = useCallback(() => {
    setBirthdayCardData(null);
  }, []);

  const clearPromotionCelebration = useCallback(() => {
    setPromotionCelebrationData(null);
  }, []);

  const toggleNotifications = useCallback((enabled: boolean) => {
    setShouldShowNotification(enabled);
  }, []);

  const contextValue = useMemo<UnifiedRealtimeState>(() => ({
    handlers,
    isConnected: connectionStatus === 'connected',
    connectionStatus,
    connectionCount: metrics.activeConnections,
    metrics,
    reconnect,
    disconnect,
    getDebugInfo,
    markAsRead,
    achievedMedal,
    clearAchievedMedal,
    birthdayCardData,
    clearBirthdayCard,
    promotionCelebrationData,
    clearPromotionCelebration,
    shouldShowNotification,
    toggleNotifications,
  }), [
    handlers,
    connectionStatus,
    metrics,
    reconnect,
    disconnect,
    getDebugInfo,
    markAsRead,
    achievedMedal,
    clearAchievedMedal,
    birthdayCardData,
    clearBirthdayCard,
    promotionCelebrationData,
    clearPromotionCelebration,
    shouldShowNotification,
    toggleNotifications,
  ]);

  return (
    <UnifiedRealtimeContext.Provider value={contextValue}>
      {children}
    </UnifiedRealtimeContext.Provider>
  );
};

export const useUnifiedRealtime = () => {
  const context = useContext(UnifiedRealtimeContext);
  if (!context) {
    throw new Error('useUnifiedRealtime deve ser usado dentro de UnifiedRealtimeProvider');
  }
  return context;
};

// Helper hook para retrocompatibilidade
export const useRealtimeNotifications = () => {
  const { 
    isConnected, 
    markAsRead, 
    achievedMedal, 
    clearAchievedMedal,
    birthdayCardData,
    clearBirthdayCard,
    shouldShowNotification,
    toggleNotifications 
  } = useUnifiedRealtime();

  return {
    isConnected,
    markAsRead,
    achievedMedal,
    clearAchievedMedal,
    birthdayCardData,
    clearBirthdayCard,
    shouldShowNotification,
    toggleNotifications,
  };
};