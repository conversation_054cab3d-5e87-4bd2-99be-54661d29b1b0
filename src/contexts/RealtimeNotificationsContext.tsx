/**
 * Contexto para gerenciar notificações em tempo real no Vindula Cosmos.
 * Gerencia a exibição de medalhas conquistadas, notificações de nível e outras atualizações.
 * <AUTHOR> Internet 2025
 */
import { createContext, useContext, useEffect, useState, useCallback, useRef } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Medal } from "@/hooks/use-medals";
import { useProfile } from "@/hooks/useProfile";
import { useRealtimeSubscription } from "@/hooks/useRealtimeSubscription";
import { logQueryEvent } from "@/lib/logs/showQueryLogs"; // Importar logQueryEvent
import { useAuthStore } from "@/stores/authStore"; // Importar o authStore para verificar o estado de autenticação
import { XpActionType } from '@/types/gamification.types'; // Importação ES6
import { getActionName } from '@/lib/gamification/actionUtils'; // Importação ES6
import { showAchievementToast, showXpToast, showMedalToast, showLevelUpToast, showEventToast, showPromotionToast, showTaskToast } from "@/components/ui/sonner";
import { QueryKeys } from "@/lib/query/queryKeys";
import { toast } from "sonner";
import { playSound, SoundEffects } from '@/lib/sound-effects';
import { PromotionCelebrationModal } from '@/components/admin/PromotionCelebrationModal';
import { BirthdayCardDialog } from '@/components/birthday/BirthdayCardDialog';
import { LevelUpAnimation } from '@/components/gamification/LevelUpAnimation';
import { MissionProgressToast } from '@/components/missions/MissionProgressToast';
import { MissionCompletedAnimation } from '@/components/missions/MissionCompletedAnimation';

// Chaves para o cache do React Query
export const NOTIFICATIONS_QUERY_KEY = ["notifications"];
export const MEDAL_QUERY_KEY = ["medal"];

interface PromotionCelebrationData {
  id: string;
  userFullName: string;
  userAvatarUrl?: string;
  userEmail: string;
  oldJobTitle: string;
  newJobTitle: string;
  promotionDate: string;
  hrManagerName: string;
  customMessage?: string;
  effectiveDate?: string;
  companyName?: string;
}

interface RealtimeNotificationsContextType {
  markAsRead: (notificationId: string) => Promise<void>;
  achievedMedal: Medal | null;
  clearAchievedMedal: () => void;
  shouldShowNotification: boolean;
  toggleNotifications: (enabled: boolean) => void;
  showXpGained: (amount: number, actionType: 'post_created' | 'post_liked' | 'comment_created' | 'comment_received' | 'document_uploaded' | 'profile_updated' | 'daily_login' | 'achievement_unlocked' | 'xp_gain' | string) => void;
  currentNotification: QueuedNotification | null;
  showNextNotification: () => void;
  // Adicionado para celebração de promoção
  showPromotionCelebration: (data: PromotionCelebrationData) => void;
  hidePromotionCelebration: () => void;
  isPromotionCelebrationVisible: boolean;
  // Adicionado para cartões de aniversário
  showBirthdayCard: (data: BirthdayCardData) => void;
  hideBirthdayCard: () => void;
  isBirthdayCardVisible: boolean;
  birthdayCardData: BirthdayCardData | null;
  // Adicionado para LevelUpAnimation
  showLevelUpAnimation: (data: LevelUpAnimationData) => void;
  hideLevelUpAnimation: () => void;
  isLevelUpAnimationVisible: boolean;
  levelUpAnimationData: LevelUpAnimationData | null;
  // Funções para exibir modais manualmente
  showMedalModal: (medalId: string) => Promise<void>;
  showLevelUpModal: (level: number, title?: string) => void;
  // Adicionado para notificações de missão
  showMissionProgress: (data: MissionProgressData) => void;
  hideMissionProgress: () => void;
  isMissionProgressVisible: boolean;
  missionProgressData: MissionProgressData | null;
  // Adicionado para animação de missão completada
  showMissionCompleted: (data: MissionCompletedData) => void;
  hideMissionCompleted: () => void;
  isMissionCompletedVisible: boolean;
  missionCompletedData: MissionCompletedData | null;
  // Função de teste para desenvolvimento
  triggerTestMissionNotification: (type: 'progress' | 'completed') => void;
}

const RealtimeNotificationsContext =
  createContext<RealtimeNotificationsContextType | null>(null);

// Tipos para metadados de notificação
interface NotificationMetadata {
  medal_id?: string;
  achieved_at?: string;
  level?: number;
  xp_amount?: number;
  action_name?: string;
  event_title?: string;
  participant_name?: string;
  status_emoji?: string;
  new_status?: string;
  promotion_id?: string;
  old_job_title?: string;
  new_job_title?: string;
  hr_manager_name?: string;
  effective_date?: string;
  promotion_emoji?: string;
  action_type?: string;
  // Metadados para cartões de aniversário
  card_id?: string;
  sender_id?: string;
  sender_name?: string;
  sender_avatar?: string;
  message?: string;
  background_type?: string;
  background_config?: Record<string, any>;
  media_type?: string;
  media_url?: string;
  media_duration?: number;
  card_type?: string;
  // Metadados para notificações de tarefas
  task_id?: string;
  task_title?: string;
  from_user_name?: string;
  request_id?: string;
  priority?: string;
  [key: string]: unknown;
}

// Tipo para o payload de notificação
interface NotificationPayload {
  new: {
    id: string;
    user_id: string;
    type: string;
    title: string | null;
    content: string;
    read: boolean | null;
    reference_id: string | null;
    company_id: string | null;
    created_at: string;
    metadata: NotificationMetadata | null;
  };
  old: Record<string, unknown> | null;
  schema: string;
  table: string;
  type: "INSERT" | "UPDATE" | "DELETE";
  commit_timestamp: string;
}

type NotificationType = 'level_up' | 'medal_earned' | 'birthday_card' | 'task' | 'mission_progress' | 'mission_completed';

// Interface para dados de cartão de aniversário
interface BirthdayCardData {
  id: string;
  sender_id: string;
  sender_name: string;
  sender_avatar?: string;
  message: string;
  background_type: 'gradient' | 'color' | 'image';
  background_config: Record<string, any>;
  media_type?: 'video' | 'audio';
  media_url?: string;
  media_duration?: number;
  created_at: string;
}

// Interface para dados de progresso de missão
interface MissionProgressData {
  missionId: string;
  title: string;
  description: string;
  currentCount: number;
  targetCount: number;
  progressPercentage: number;
  xpReward?: number;
  stardustReward?: number;
  activityType: string;
  isCompleted: boolean;
  isNewMission?: boolean;
}

// Interface para dados de missão completada
interface MissionCompletedData {
  missionId: string;
  title: string;
  description: string;
  xpReward: number;
  stardustReward: number;
  activityType: string;
}

// Interface para dados de Level Up Animation
interface LevelUpAnimationData {
  level: number;
  title: string;
  unlockedAssets?: VisualAsset[];
}

// Interface para VisualAsset (deve existir no gamification types)
interface VisualAsset {
  id: string;
  name: string;
  type: string;
  url?: string;
}

interface QueuedNotification {
  id: string;
  type: NotificationType;
  data: Medal | { level: number; title?: string } | BirthdayCardData | MissionProgressData | MissionCompletedData;
}

/**
 * Hook personalizado para gerenciar a subscrição de notificações em tempo real
 * Implementado com React Query para evitar re-renderizações desnecessárias
 */
function useNotificationsSubscription() {
  const { profile } = useProfile();
  const queryClient = useQueryClient();
  
  // Log quando o profile está disponível
  useEffect(() => {
    if (profile?.id) {
      logQueryEvent('RealtimeNotifications', 'Profile carregado para subscrições', { 
        profileId: profile.id,
        companyId: profile.company_id
      });
    }
  }, [profile?.id, profile?.company_id]);
  const [achievedMedal, setAchievedMedal] = useState<Medal | null>(null);
  const [shouldShowNotification, setShouldShowNotification] = useState(true);
  const [notificationQueue, setNotificationQueue] = useState<QueuedNotification[]>([]);
  const [currentNotification, setCurrentNotification] = useState<QueuedNotification | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  
  // Estado para celebração de promoção
  const [promotionCelebrationData, setPromotionCelebrationData] = useState<PromotionCelebrationData | null>(null);
  const [isPromotionCelebrationVisible, setIsPromotionCelebrationVisible] = useState(false);

  // Estado para cartão de aniversário
  const [birthdayCardData, setBirthdayCardData] = useState<BirthdayCardData | null>(null);
  const [isBirthdayCardVisible, setIsBirthdayCardVisible] = useState(false);

  // Estado para LevelUpAnimation
  const [levelUpAnimationData, setLevelUpAnimationData] = useState<LevelUpAnimationData | null>(null);
  const [isLevelUpAnimationVisible, setIsLevelUpAnimationVisible] = useState(false);

  // Estado para notificações de missão
  const [missionProgressData, setMissionProgressData] = useState<MissionProgressData | null>(null);
  const [isMissionProgressVisible, setIsMissionProgressVisible] = useState(false);

  // Estado para animação de missão completada
  const [missionCompletedData, setMissionCompletedData] = useState<MissionCompletedData | null>(null);
  const [isMissionCompletedVisible, setIsMissionCompletedVisible] = useState(false);

  // Funções para gerenciar notificações de missão (declaradas antes dos useEffects)
  const showMissionProgress = useCallback((data: MissionProgressData) => {
    logQueryEvent('RealtimeNotifications', 'Exibindo notificação de progresso de missão', { missionId: data.missionId, title: data.title });
    setMissionProgressData(data);
    setIsMissionProgressVisible(true);
  }, []);

  const showMissionCompleted = useCallback((data: MissionCompletedData) => {
    logQueryEvent('RealtimeNotifications', 'Exibindo animação de missão completada', { missionId: data.missionId, title: data.title });
    setMissionCompletedData(data);
    setIsMissionCompletedVisible(true);
  }, []);

  // Função para forçar a exibição da próxima notificação
  const showNextNotification = useCallback(() => {
    if (currentNotification) {
      // Lógica para limpar a notificação atual ou avançar para a próxima
      logQueryEvent('RealtimeNotifications', 'Limpando notificação atual ou avançando para a próxima.', { currentNotificationId: currentNotification.id });
      setCurrentNotification(null); // Limpa a notificação atual
      // Não é necessário chamar setIsProcessing(false) aqui, pois processNextNotification fará isso
    }
    // Após limpar, tentar processar a próxima da fila, se houver
    // Mas apenas se não estivermos já em um ciclo de processamento para evitar loops
    // A lógica de processar a próxima já está no useEffect que observa notificationQueue e isProcessing
  }, [currentNotification]);

  const hideMissionProgress = useCallback(() => {
    logQueryEvent('RealtimeNotifications', 'Ocultando notificação de progresso de missão');
    setIsMissionProgressVisible(false);
    setTimeout(() => {
      setMissionProgressData(null);
    }, 300); // Aguarda animação de saída
    showNextNotification(); // Processar próxima notificação na fila
  }, [showNextNotification]);

  const hideMissionCompleted = useCallback(() => {
    logQueryEvent('RealtimeNotifications', 'Ocultando animação de missão completada');
    setIsMissionCompletedVisible(false);
    setTimeout(() => {
      setMissionCompletedData(null);
    }, 500); // Aguarda animação de saída (mais tempo para animação épica)
    showNextNotification(); // Processar próxima notificação na fila
  }, [showNextNotification]);

  // Processa a próxima notificação na fila
  const processNextNotification = useCallback(() => {
    if (isProcessing || notificationQueue.length === 0) return;
    
    setIsProcessing(true);
    const [nextNotification, ...remainingQueue] = notificationQueue;
    setCurrentNotification(nextNotification);
    setNotificationQueue(remainingQueue);
    
    // Se for uma notificação de medalha, atualiza o estado achievedMedal
    // Esta linha será removida/alterada depois, pois achievedMedal é derivado de currentNotification
    // if (nextNotification.type === 'medal_earned') {
    //   setAchievedMedal(nextNotification.data);
    // }
    
    // Configura um timeout para processar a próxima notificação após um atraso
    setTimeout(() => {
      setIsProcessing(false);
      // Apenas chame showNextNotification automaticamente se NÃO for uma medalha, cartão de aniversário ou missão.
      // As notificações de medalha, cartão e missão requerem interação do usuário para serem dispensadas
      if (nextNotification.type !== 'medal_earned' && 
          nextNotification.type !== 'birthday_card' && 
          nextNotification.type !== 'mission_progress' && 
          nextNotification.type !== 'mission_completed') {
        showNextNotification();
      }
    }, 5000); // Tempo que a notificação fica visível
  }, [notificationQueue, isProcessing, showNextNotification]);

  // Adiciona uma notificação à fila
  const addToNotificationQueue = useCallback((type: NotificationType, data: Medal | { level: number; title?: string } | BirthdayCardData | MissionProgressData | MissionCompletedData) => {
    const newNotification: QueuedNotification = {
      id: Date.now().toString(), // Gerar um ID simples
      type,
      data,
    };
    
    setNotificationQueue((prevQueue) => {
      const newQueue = [...prevQueue, newNotification];
      logQueryEvent('RealtimeNotifications', 'Notificação adicionada à fila', { 
        type,
        queueLength: newQueue.length, 
        prevLength: prevQueue.length
      });
      return newQueue;
    });
  }, []);

  // Referência para rastrear se o componente está montado
  const isMountedRef = useRef<boolean>(true);

  // Efeito para limpar a referência quando o componente for desmontado
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Função para processar notificações recebidas
  const handleNotification = useCallback(
    async (payload: Record<string, unknown>) => {
      logQueryEvent('RealtimeNotifications', 'Notificação recebida (raw payload):', payload); // Log inicial
      // Converter o payload para o tipo NotificationPayload
      const notificationPayload = payload as unknown as NotificationPayload;

      logQueryEvent('RealtimeNotifications', 'Payload convertido:', notificationPayload);

      // Verificar se profile?.company_id existe
      if (!profile?.company_id) {
        logQueryEvent('RealtimeNotifications', 'Processamento ignorado: profile.company_id ausente.', null, 'warn');
        return;
      }

      logQueryEvent('RealtimeNotifications', 'Verificando tipo da notificação...', { type: notificationPayload.new.type });

      if (
        (notificationPayload.new.type === "medal_earned" ||
          notificationPayload.new.type === "level_up") &&
        profile?.company_id
      ) {
        try {
          // Verificar se é uma notificação de level_up sem medalha associada
      if (notificationPayload.new.type === "level_up") {
        logQueryEvent('RealtimeNotifications', 'Level up detectado - delegando para LevelUpAnimation');
        
        // A notificação de level up é tratada inteiramente pelo componente LevelUpAnimation
        // Não precisamos fazer nada aqui, apenas registrar o evento

            // Se houver uma medalha associada ao level up, adicionar à fila de notificações
            const medalId = notificationPayload.new.metadata?.medal_id;
            logQueryEvent('RealtimeNotifications', 'Verificando medalha associada ao level up...', { medalId });
            if (medalId && profile.company_id) {
              logQueryEvent('RealtimeNotifications', 'Buscando detalhes da medalha (level up)...', { medalId, companyId: profile.company_id });
              const { data: medal, error } = await supabase
                .from("medals")
                .select("*")
                .eq("id", medalId)
                .eq("company_id", profile.company_id)
                .maybeSingle();

              if (error) {
                logQueryEvent('RealtimeNotifications', 'Erro ao buscar medalha (level up):', { medalId, error }, 'error');
                throw error; // Re-throw para ser pego pelo catch externo
              }
              if (medal) {
                logQueryEvent('RealtimeNotifications', 'Medalha encontrada (level up), adicionando à fila de notificações', medal);
                const achievedMedalData: Medal = {
                  id: medal.id,
                  name: medal.name,
                  description: medal.description || "",
                  // Garantir que o tipo seja um dos valores válidos do enum
                  type: medal.type as 'communication' | 'engagement' | 'documentation' | 'attendance' | 'collaboration',
                  icon_url: medal.icon_url,
                  required_count: medal.required_count,
                  progress: 1,
                  achieved_at:
                    notificationPayload.new.metadata?.achieved_at ||
                    new Date().toISOString(),
                };

                // Adiciona a notificação de medalha à fila
                addToNotificationQueue('medal_earned', achievedMedalData);
                logQueryEvent('RealtimeNotifications', 'Notificação de medalha adicionada à fila (level up)');

                // Invalidar queries relacionadas a medalhas para forçar atualização
                queryClient.invalidateQueries({ queryKey: QueryKeys.gamification.medals() });
              }
            }
          } else if (notificationPayload.new.type === "medal_earned") {
            const medalId = notificationPayload.new.metadata?.medal_id;
            logQueryEvent('RealtimeNotifications', 'Medalha ganha detectada.', { medalId });

            if (!medalId) {
              logQueryEvent('RealtimeNotifications', 'Erro: medal_earned sem medal_id nos metadados.', notificationPayload.new.metadata, 'error');
              return; // Não processar se não houver medal_id
            }

            logQueryEvent('RealtimeNotifications', 'Buscando detalhes da medalha (medal_earned)...', { medalId, companyId: profile.company_id });
            const { data: medal, error } = await supabase
              .from("medals")
              .select("*")
              .eq("id", medalId)
              .eq("company_id", profile.company_id)
              .maybeSingle();

            if (error) {
              logQueryEvent('RealtimeNotifications', 'Erro ao buscar medalha (medal_earned):', { medalId, error }, 'error');
              throw error;
            }
            if (!medal) {
              logQueryEvent('RealtimeNotifications', 'Erro: Medalha não encontrada no DB (medal_earned)', { medalId }, 'error');
              throw new Error("Medalha não encontrada");
            }

            logQueryEvent('RealtimeNotifications', 'Medalha encontrada (medal_earned):', medal);

            const achievedMedalData: Medal = {
              id: medal.id,
              name: medal.name,
              description: medal.description || "",
              // Garantir que o tipo seja um dos valores válidos do enum
              type: medal.type as 'communication' | 'engagement' | 'documentation' | 'attendance' | 'collaboration',
              icon_url: medal.icon_url,
              required_count: medal.required_count,
              progress: 1,
              achieved_at:
                notificationPayload.new.metadata?.achieved_at ||
                new Date().toISOString(),
            };

            // Adiciona a notificação de medalha à fila
            addToNotificationQueue('medal_earned', achievedMedalData);
            logQueryEvent('RealtimeNotifications', 'Notificação de medalha adicionada à fila (medal_earned)');

            // Invalidar queries relacionadas a medalhas para forçar atualização
            queryClient.invalidateQueries({ queryKey: QueryKeys.gamification.medals() });
            
            // Verificar se houve ganho de nível junto com a medalha
            // Isso forçará o useUserLevel a verificar o nível atual do usuário
            queryClient.invalidateQueries({ queryKey: QueryKeys.gamification.userLevel() });
          }
        } catch (error) {
          // Usar logQueryEvent para erros
          logQueryEvent('RealtimeNotifications', 'Erro ao processar notificação no bloco try/catch:', { error: error instanceof Error ? error.message : error, payload: notificationPayload }, 'error');
          // Usar o novo sistema de notificações gamificadas para erros
          showAchievementToast(
            "Erro", 
            "Não foi possível processar a notificação de medalha/nível",
            "achievement"
          );
        }
      } else if (notificationPayload.new.type === "xp_gain") {
        logQueryEvent('RealtimeNotifications', 'Notificação de ganho de XP detectada', { notificationPayload }); // LOG ADICIONADO
        const xp_amount = notificationPayload.new.metadata?.xp_amount;
        const action_name = notificationPayload.new.metadata?.action_name || 'xp_gain';

        logQueryEvent('RealtimeNotifications', 'Detalhes da notificação de ganho de XP:', { xp_amount, action_name }); // LOG ADICIONADO

        if (typeof xp_amount !== 'number' || isNaN(xp_amount)) {
          logQueryEvent('RealtimeNotifications', 'xp_amount inválido', { xp_amount }, 'warn'); // LOG ADICIONADO
          return;
        }

        logQueryEvent('RealtimeNotifications', 'Buscando preferência xp_gain...', { userId: profile?.id }); // LOG ADICIONADO
        try {
          const { data: preference, error: prefError } = await supabase
            .from('notification_preferences')
            .select('enabled')
            .eq('user_id', profile?.id)
            .eq('activity_type', 'xp_gain' as XpActionType)
            .maybeSingle();

          if (prefError) {
            logQueryEvent('RealtimeNotifications', 'Erro ao buscar preferência xp_gain', { error: prefError }, 'error');
            showXpGained(xp_amount, action_name as string); // Exibir mesmo em caso de erro de busca, comportamento padrão
            return;
          }

          const xpGainEnabled = preference ? preference.enabled : true; // Padrão para true se não houver preferência explícita
          logQueryEvent('RealtimeNotifications', 'Preferência xp_gain encontrada', { enabled: xpGainEnabled, preference }); // LOG ADICIONADO

          if (xpGainEnabled) {
            logQueryEvent('RealtimeNotifications', 'Preferência xp_gain ATIVADA, mostrando notificação.', { xp_amount, action_name }); // LOG ADICIONADO
            showXpGained(xp_amount, action_name as string);
          } else {
            logQueryEvent('RealtimeNotifications', 'Preferência xp_gain DESATIVADA, não mostrando notificação.'); // LOG ADICIONADO
          }
        } catch (error) {
          logQueryEvent('RealtimeNotifications', 'Erro inesperado ao processar preferência xp_gain', { error }, 'error');
          showXpGained(xp_amount, action_name); // Exibir em caso de erro inesperado, comportamento padrão
        }
      } else if (notificationPayload.new.type === "event_participation_update") {
        // Nova funcionalidade: Toast em tempo real para criadores de eventos
        logQueryEvent('RealtimeNotifications', 'Notificação de atualização de participação em evento detectada', { notificationPayload });
        
        const metadata = notificationPayload.new.metadata;
        const eventTitle = metadata?.event_title;
        const participantName = metadata?.participant_name;
        const statusEmoji = metadata?.status_emoji;
        const newStatus = metadata?.new_status;
        
        if (eventTitle && participantName && statusEmoji) {
          let statusMessage = '';
          
          switch (newStatus) {
            case 'confirmed':
              statusMessage = 'confirmou presença';
              break;
            case 'declined':
              statusMessage = 'recusou o convite';
              break;
            case 'maybe':
              statusMessage = 'marcou como "talvez"';
              break;
            default:
              statusMessage = 'atualizou sua participação';
          }
          
          const toastTitle = `${statusEmoji} Evento: ${eventTitle}`;
          const toastDescription = `${participantName} ${statusMessage} no seu evento.`;
          
          // Exibir toast especializado para eventos
          showEventToast(
            toastTitle,
            toastDescription,
            newStatus === 'confirmed' ? 'confirmed' : 
            newStatus === 'declined' ? 'declined' : 
            newStatus === 'maybe' ? 'maybe' : 'updated'
          );
          
          // Som será tocado automaticamente pelo sistema de sound-effects se habilitado
          playSound(SoundEffects.NEW_MESSAGE);
          
          logQueryEvent('RealtimeNotifications', 'Toast de evento exibido', { 
            title: toastTitle, 
            description: toastDescription, 
            status: newStatus 
          });
        }
      } else if (
        notificationPayload.new.type === "event_invitation" ||
        notificationPayload.new.type === "event_reminder" ||
        notificationPayload.new.type === "event_cancelled" ||
        notificationPayload.new.type === "event_updated"
      ) {
        // Suporte para todas as categorias de notificações de eventos
        logQueryEvent('RealtimeNotifications', `Notificação de evento detectada: ${notificationPayload.new.type}`, { notificationPayload });
        
        const metadata = notificationPayload.new.metadata;
        const eventTitle = metadata?.event_title;
        const title = notificationPayload.new.title;
        const content = notificationPayload.new.content;
        
        if (title && content) {
          let status: 'confirmed' | 'declined' | 'maybe' | 'updated' = 'updated';
          
          // Definir status baseado no tipo de notificação para cores apropriadas
          switch (notificationPayload.new.type) {
            case 'event_invitation':
              status = 'confirmed'; // Azul/verde para convites
              break;
            case 'event_reminder':
              status = 'maybe'; // Âmbar para lembretes
              break;
            case 'event_cancelled':
              status = 'declined'; // Vermelho para cancelamentos
              break;
            case 'event_updated':
              status = 'updated'; // Azul para atualizações
              break;
          }
          
          // Exibir toast especializado para eventos
          showEventToast(title, content, status);
          
          // Som será tocado automaticamente pelo sistema de sound-effects se habilitado
          playSound(SoundEffects.NEW_MESSAGE);
          
          logQueryEvent('RealtimeNotifications', 'Toast de evento exibido', { 
            type: notificationPayload.new.type,
            title, 
            content, 
            status 
          });
        }
      } else if (notificationPayload.new.type === "mention" && notificationPayload.new.metadata?.card_type === "birthday_card") {
        // Tratamento especial para cartões de aniversário (tipo mention com card_type)
        logQueryEvent('RealtimeNotifications', 'Notificação de cartão de aniversário detectada', { notificationPayload });
        
        const metadata = notificationPayload.new.metadata;
        const title = notificationPayload.new.title;
        const content = notificationPayload.new.content;
        
        // Criar objeto de dados do cartão
        const cardData: BirthdayCardData = {
          id: metadata.card_id as string,
          sender_id: metadata.sender_id as string,
          sender_name: metadata.sender_name as string,
          sender_avatar: metadata.sender_avatar as string,
          message: metadata.message as string,
          background_type: (metadata.background_type as 'gradient' | 'color' | 'image') || 'gradient',
          background_config: metadata.background_config as Record<string, any> || {},
          media_type: metadata.media_type as 'video' | 'audio' | undefined,
          media_url: metadata.media_url as string | undefined,
          media_duration: metadata.media_duration as number | undefined,
          created_at: notificationPayload.new.created_at
        };

        // Adicionar à fila de notificações para processamento
        addToNotificationQueue('birthday_card', cardData);
        
        logQueryEvent('RealtimeNotifications', 'Cartão de aniversário adicionado à fila de notificações', { cardData });
        
        // Som será tocado automaticamente pelo sistema de sound-effects se habilitado
        playSound(SoundEffects.ACHIEVEMENT, 0.4);
        
      } else if (
        notificationPayload.new.type === "promotion_created" ||
        notificationPayload.new.type === "promotion_effectuated" ||
        notificationPayload.new.type === "promotion_notification_sent" ||
        notificationPayload.new.type === "promotion_public_congratulations"
      ) {
        // Sistema de notificações em tempo real para promoções
        logQueryEvent('RealtimeNotifications', `Notificação de promoção detectada: ${notificationPayload.new.type}`, { notificationPayload });
        
        const metadata = notificationPayload.new.metadata;
        const title = notificationPayload.new.title;
        const content = notificationPayload.new.content;
        const actionType = metadata?.action_type;
        
        if (title && content) {
          let promotionActionType: 'created' | 'effectuated' | 'notification_sent' | 'public_congratulations' = 'created';
          
          // Mapear tipo de notificação para o tipo de ação do toast
          switch (notificationPayload.new.type) {
            case 'promotion_created':
              promotionActionType = 'created';
              break;
            case 'promotion_effectuated':
              promotionActionType = 'effectuated';
              break;
            case 'promotion_notification_sent':
              promotionActionType = 'notification_sent';
              break;
            case 'promotion_public_congratulations':
              promotionActionType = 'public_congratulations';
              break;
          }
          
          // Para NOTIFICAÇÃO OFICIAL de promoção, mostrar celebração especial
          if (notificationPayload.new.type === 'promotion_notification_sent' && metadata) {
            logQueryEvent('RealtimeNotifications', 'Notificação oficial de promoção detectada - preparando celebração especial', { metadata });
            
            // Buscar dados do usuário para completar as informações da celebração
            if (profile?.id) {
              supabase
                .from('profiles')
                .select('full_name, avatar_url, email')
                .eq('id', profile.id)
                .single()
                .then(({ data: userProfile }) => {
                  if (userProfile) {
                    const celebrationData: PromotionCelebrationData = {
                      id: metadata.promotion_id as string || 'unknown',
                      userFullName: userProfile.full_name || 'Usuário',
                      userAvatarUrl: userProfile.avatar_url || undefined,
                      userEmail: userProfile.email || '',
                      oldJobTitle: metadata.old_job_title as string || 'Cargo Anterior',
                      newJobTitle: metadata.new_job_title as string || 'Novo Cargo',
                      promotionDate: new Date().toISOString().split('T')[0],
                      hrManagerName: metadata.hr_manager_name as string || 'RH',
                      customMessage: content || undefined,
                      effectiveDate: metadata.effective_date as string || undefined,
                      companyName: 'Vindula Cosmos' // Pode ser obtido do perfil posteriormente
                    };
                    
                    // Mostrar celebração especial
                    showPromotionCelebration(celebrationData);
                  }
                })
                .catch((error) => {
                  logQueryEvent('RealtimeNotifications', 'Erro ao buscar dados do usuário para celebração', { error }, 'error');
                  // Fallback para toast normal se houver erro
                  showPromotionToast(title, content, promotionActionType);
                });
            } else {
              // Fallback se não tiver profile
              showPromotionToast(title, content, promotionActionType);
            }
          } else if (notificationPayload.new.type === 'promotion_created') {
            // Para criação de promoção, NÃO notificar o usuário (ainda é surpresa)
            logQueryEvent('RealtimeNotifications', 'Promoção criada - não notificando usuário (surpresa)', { metadata });
            return; // Sair sem mostrar nada
          } else if (notificationPayload.new.type === 'promotion_effectuated') {
            // Para efetivação, NÃO mostrar celebração (ainda não foi comunicado oficialmente)
            logQueryEvent('RealtimeNotifications', 'Promoção efetivada - não mostrando celebração (aguardando notificação oficial)', { metadata });
            return; // Sair sem mostrar nada
          } else {
            // Para outros tipos de promoção (parabéns públicos), usar toast normal
            showPromotionToast(title, content, promotionActionType);
          }
          
          // Tocar som de notificação especial para promoções (conquista)
          playSound(SoundEffects.ACHIEVEMENT);
          
          logQueryEvent('RealtimeNotifications', 'Notificação de promoção processada', { 
            type: notificationPayload.new.type,
            title, 
            content, 
            actionType: promotionActionType,
            metadata,
            usesCelebration: notificationPayload.new.type === 'promotion_effectuated'
          });
        }
      } else if (notificationPayload.new.type === "privacy_request") {
        // Sistema de notificações para solicitações de privacidade LGPD
        logQueryEvent('RealtimeNotifications', 'Notificação de privacy request detectada', { notificationPayload });
        
        const metadata = notificationPayload.new.metadata;
        const title = notificationPayload.new.title;
        const content = notificationPayload.new.content;
        const requestType = metadata?.request_type;
        const newStatus = metadata?.new_status;
        const justification = metadata?.justification;
        
        if (title && content) {
          // Determinar cores e ícones baseado no status
          let statusColor = 'default';
          let statusIcon = '📋';
          
          switch (newStatus) {
            case 'in_progress':
              statusColor = 'blue';
              statusIcon = '📋';
              break;
            case 'completed':
              statusColor = 'green';
              statusIcon = '✅';
              break;
            case 'rejected':
              statusColor = 'red';
              statusIcon = '❌';
              break;
            default:
              statusColor = 'gray';
              statusIcon = '📋';
          }
          
          // Limitar o tamanho do conteúdo para evitar notificações muito longas
          const maxContentLength = 120; // Limite de caracteres para o conteúdo
          let truncatedContent = content;
          let hasMoreContent = false;
          
          if (content.length > maxContentLength) {
            truncatedContent = content.substring(0, maxContentLength) + '...';
            hasMoreContent = true;
          }
          
          // Exibir toast com informações da solicitação
          toast(title, {
            description: truncatedContent,
            icon: statusIcon,
            duration: hasMoreContent ? 10000 : 8000, // 10 segundos se houver mais conteúdo
            action: {
              label: hasMoreContent ? "Ver Completo" : "Ver Detalhes",
              onClick: () => {
                // Navegar para página de privacidade
                window.location.href = '/privacy';
              },
            },
            style: {
              borderColor: statusColor === 'green' ? '#10b981' : 
                          statusColor === 'red' ? '#ef4444' : 
                          statusColor === 'blue' ? '#3b82f6' : '#6b7280'
            }
          });
          
          // Tocar som baseado no tipo de atualização
          if (newStatus === 'completed') {
            playSound(SoundEffects.ACHIEVEMENT); // Som de conquista para solicitação concluída
          } else if (newStatus === 'rejected') {
            playSound(SoundEffects.ERROR); // Som de erro para rejeição
          } else {
            playSound(SoundEffects.NEW_MESSAGE); // Som padrão para processamento
          }
          
          logQueryEvent('RealtimeNotifications', 'Toast de privacy request exibido', { 
            type: notificationPayload.new.type,
            title, 
            content, 
            requestType,
            newStatus,
            statusColor,
            statusIcon
          });
        }
      } else if (notificationPayload.new.type === "task") {
        // Sistema de notificações para tarefas
        logQueryEvent('RealtimeNotifications', 'Notificação de tarefa detectada', { notificationPayload });
        
        const metadata = notificationPayload.new.metadata;
        const title = notificationPayload.new.title;
        const content = notificationPayload.new.content;
        const actionType = metadata?.action_type;
        
        if (title && content) {
          let taskType: 'request' | 'accepted' | 'declined' | 'negotiation' = 'request';
          
          // Mapear tipo de ação para o tipo do toast
          switch (actionType) {
            case 'task_request':
              taskType = 'request';
              break;
            case 'task_accepted':
              taskType = 'accepted';
              break;
            case 'task_declined':
              taskType = 'declined';
              break;
            case 'task_negotiation':
              taskType = 'negotiation';
              break;
            default:
              taskType = 'request';
          }
          
          // Criar função de navegação baseada no tipo de ação
          const handleNavigate = () => {
            // Navegar para a página de Tasks com a aba apropriada
            const tasksUrl = '/tasks';
            
            logQueryEvent('RealtimeNotifications', 'Navegando para tarefa', { 
              actionType, 
              metadata, 
              currentUrl: window.location.href 
            });
            
            // Determinar qual aba abrir baseado no tipo e usuário
            if (actionType === 'task_request' || actionType === 'task_message') {
              // Para pedidos ou mensagens, abrir aba de solicitações recebidas
              const targetUrl = `${tasksUrl}?tab=received`;
              logQueryEvent('RealtimeNotifications', 'Navegando para solicitações recebidas', { targetUrl });
              window.location.href = targetUrl;
            } else {
              // Para respostas (accepted, declined, negotiation), abrir aba de respostas
              const targetUrl = `${tasksUrl}?tab=responses`;
              logQueryEvent('RealtimeNotifications', 'Navegando para respostas', { targetUrl });
              window.location.href = targetUrl;
            }
          };
          
          // Exibir toast especializado para tarefas com navegação
          showTaskToast(title, content, taskType, handleNavigate);
          
          // Tocar som de notificação para tarefas
          playSound(SoundEffects.NEW_MESSAGE);
          
          // Invalidar queries relacionadas às tarefas para forçar atualização
          queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.assignmentRequests() });
          queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.receivedRequests() });
          queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.myRequests() });
          queryClient.invalidateQueries({ queryKey: QueryKeys.notifications.all() });
          
          // Invalidar histórico de conversação de tarefas se for uma mensagem
          if (actionType === 'task_message' && metadata?.request_id) {
            queryClient.invalidateQueries({ queryKey: ['task-conversation-history', metadata.request_id] });
            logQueryEvent('RealtimeNotifications', 'Cache de conversação invalidado', { requestId: metadata.request_id });
          }
          
          logQueryEvent('RealtimeNotifications', 'Toast de tarefa exibido e queries invalidadas', { 
            type: notificationPayload.new.type,
            title, 
            content, 
            taskType,
            actionType
          });
        }
      } else if (
        notificationPayload.new.type === "mission_progress" ||
        notificationPayload.new.type === "mission_completed" ||
        notificationPayload.new.type === "new_mission"
      ) {
        // Sistema de notificações para progresso de missões
        logQueryEvent('RealtimeNotifications', `🚀 Notificação de missão detectada: ${notificationPayload.new.type}`, { 
          notificationPayload,
          title: notificationPayload.new.title,
          content: notificationPayload.new.content,
          metadata: notificationPayload.new.metadata,
          hasTitle: !!notificationPayload.new.title,
          hasMetadata: !!notificationPayload.new.metadata
        });
        
        const metadata = notificationPayload.new.metadata;
        const title = notificationPayload.new.title;
        const content = notificationPayload.new.content;
        
        logQueryEvent('RealtimeNotifications', '🔍 Debug estrutura da notificação de missão:', {
          title,
          content,
          metadata,
          metadataKeys: metadata ? Object.keys(metadata) : [],
          titleExists: !!title,
          metadataExists: !!metadata
        });
        
        if (title && metadata) {
          logQueryEvent('RealtimeNotifications', '✅ Condições atendidas - criando MissionData', {
            title,
            hasMetadata: !!metadata,
            metadataKeys: Object.keys(metadata)
          });
          
          const missionData: MissionProgressData = {
            missionId: metadata.mission_id as string || '',
            title: title,
            description: content || '',
            currentCount: metadata.current_count as number || 0,
            targetCount: metadata.target_count as number || 1,
            progressPercentage: metadata.progress_percentage as number || 0, // 🔧 Corrigido: progress_percentage ao invés de percentage
            xpReward: metadata.xp_reward as number,
            stardustReward: metadata.stardust_reward as number,
            activityType: metadata.activity_type as string || 'task_completed',
            isCompleted: notificationPayload.new.type === 'mission_completed',
            isNewMission: notificationPayload.new.type === 'new_mission',
          };

          logQueryEvent('RealtimeNotifications', '✅ MissionData criado:', {
            missionData,
            originalMetadata: metadata
          });

          // Adicionar à fila de notificações para processamento
          if (notificationPayload.new.type === 'mission_completed') {
            addToNotificationQueue('mission_completed', missionData);
          } else {
            addToNotificationQueue('mission_progress', missionData);
          }
          
          logQueryEvent('RealtimeNotifications', 'Notificação de missão adicionada à fila', { 
            type: notificationPayload.new.type,
            missionId: missionData.missionId,
            progress: `${missionData.currentCount}/${missionData.targetCount}` 
          });
          
          // Tocar som espacial para missões
          if (notificationPayload.new.type === 'mission_completed') {
            playSound(SoundEffects.ACHIEVEMENT, 0.8); // Som de conquista para missão completa
          } else {
            playSound(SoundEffects.NEW_MESSAGE, 0.4); // Som mais suave para progresso
          }
          
          // Invalidar queries relacionadas a missões para forçar atualização
          queryClient.invalidateQueries({ queryKey: QueryKeys.gamification.userProgress(metadata.user_id) });
          queryClient.invalidateQueries({ queryKey: QueryKeys.notifications.all() });
        } else {
          logQueryEvent('RealtimeNotifications', '❌ Condições NÃO atendidas para notificação de missão:', {
            hasTitle: !!title,
            hasMetadata: !!metadata,
            title,
            metadata,
            notificationType: notificationPayload.new.type
          }, 'warn');
        }
      }

      // Invalidar o cache de notificações para forçar atualização
      logQueryEvent('RealtimeNotifications', 'Invalidando query de notificações.');
      queryClient.invalidateQueries({ queryKey: NOTIFICATIONS_QUERY_KEY });
    },
    [profile?.company_id, profile?.id, queryClient] // Removida a dependência do toast que não existe mais
  );

  // Subscrição para notificações de gamificação
  useRealtimeSubscription({
    enabled: !!profile?.id, // Habilitar apenas se profile.id existir
    channelName: `notifications:${profile?.id || "unknown"}`,
    event: "INSERT",
    schema: "public",
    table: "notifications",
    filter: profile?.id ? `user_id=eq.${profile.id}` : undefined,
    onPayload: (payload) => {
      logQueryEvent('RealtimeNotifications', '📡 REALTIME: Nova notificação recebida', {
        payload,
        profileId: profile?.id,
        payloadUserId: (payload as any)?.new?.user_id,
        payloadType: (payload as any)?.new?.type,
        isMatch: (payload as any)?.new?.user_id === profile?.id
      });
      handleNotification(payload);
    },
    dependencies: [profile?.id, profile?.company_id], // Garantir que re-subscreva se profile mudar
  });

  // Subscrição para mensagens de conversação de tarefas
  useRealtimeSubscription({
    enabled: !!profile?.id,
    channelName: `task_conversation_messages:${profile?.id || "unknown"}`,
    event: "INSERT",
    schema: "public",
    table: "task_conversation_messages",
    filter: profile?.id ? `from_user_id=neq.${profile.id}` : undefined, // Apenas mensagens que NÃO são minhas
    onPayload: async (payload: Record<string, unknown>) => {
      logQueryEvent('RealtimeNotifications', 'Nova mensagem de conversação de tarefa detectada:', payload);
      
      try {
        // Estrutura do realtime payload para mensagens de conversação de tarefas
        const newMessage = payload.new as {
          id: string;
          request_id: string;
          from_user_id: string;
          message: string;
          message_type: string;
          created_at: string;
        };
        
        // Verificar se sou participante desta conversação (via task_assignment_requests)
        const { data: request } = await supabase
          .from('task_assignment_requests')
          .select('from_user_id, to_user_id, task:tasks!inner(title)')
          .eq('id', newMessage.request_id)
          .single();
        
        if (request && (request.from_user_id === profile.id || request.to_user_id === profile.id)) {
          // Buscar dados do remetente para a notificação
          const { data: sender } = await supabase
            .from('profiles')
            .select('full_name, avatar_url')
            .eq('id', newMessage.from_user_id)
            .single();
          
          const senderName = sender?.full_name || 'Usuário';
          const taskTitle = request.task?.title || 'Tarefa';
          const isFromRequestee = newMessage.from_user_id === request.to_user_id; // Mensagem do destinatário
          const isFromRequester = newMessage.from_user_id === request.from_user_id; // Mensagem do solicitante
          
          // Invalidar cache do histórico desta conversação específica
          queryClient.invalidateQueries({ queryKey: ['task-conversation-history', newMessage.request_id] });
          
          // Invalidar todas as queries relacionadas a tarefas para forçar atualização completa
          queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.assignmentRequests() });
          queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.receivedRequests() });
          queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.myRequests() });
          queryClient.invalidateQueries({ queryKey: QueryKeys.notifications.all() });
          
          // Forçar atualização das queries específicas também
          queryClient.refetchQueries({ queryKey: QueryKeys.tasks.myRequests() });
          queryClient.refetchQueries({ queryKey: QueryKeys.tasks.receivedRequests() });
          
          // Exibir notificação de nova mensagem no chat de tarefa
          if (newMessage.message_type === 'message') {
            // Determinar o tipo de navegação baseado na perspectiva do usuário
            let navigationTab = 'received';
            let title = '💬 Nova mensagem na tarefa';
            let description = `${senderName}: ${newMessage.message.substring(0, 80)}${newMessage.message.length > 80 ? '...' : ''}`;
            
            // Se sou o solicitante original (from_user_id) e recebo mensagem do destinatário
            if (profile.id === request.from_user_id && isFromRequestee) {
              navigationTab = 'responses';
              title = '💬 Resposta na sua solicitação';
              description = `${senderName} respondeu em "${taskTitle}": ${newMessage.message.substring(0, 60)}${newMessage.message.length > 60 ? '...' : ''}`;
            }
            // Se sou o destinatário (to_user_id) e recebo mensagem do solicitante
            else if (profile.id === request.to_user_id && isFromRequester) {
              navigationTab = 'received';
              title = '💬 Nova mensagem na tarefa';
              description = `${senderName} enviou uma mensagem em "${taskTitle}": ${newMessage.message.substring(0, 60)}${newMessage.message.length > 60 ? '...' : ''}`;
            }
            
            // Criar função de navegação para ir à aba correta
            const handleNavigateToConversation = () => {
              const tasksUrl = '/tasks';
              const targetUrl = `${tasksUrl}?tab=${navigationTab}`;
              logQueryEvent('RealtimeNotifications', 'Navegando para conversação de tarefa', { 
                targetUrl, 
                navigationTab, 
                requestId: newMessage.request_id 
              });
              window.location.href = targetUrl;
            };
            
            // Exibir toast de mensagem com navegação
            showTaskToast(title, description, 'negotiation', handleNavigateToConversation);
            
            // Tocar som de nova mensagem
            playSound(SoundEffects.NEW_MESSAGE, 0.6);
          }
          
          logQueryEvent('RealtimeNotifications', 'Cache de conversação de tarefa invalidado automaticamente', { 
            requestId: newMessage.request_id,
            messageId: newMessage.id,
            isFromRequestee,
            isFromRequester,
            currentUserId: profile.id,
            requesterUserId: request.from_user_id,
            requesteeUserId: request.to_user_id,
            invalidatedQueries: ['task-conversation-history', 'assignment-requests', 'received-requests', 'my-requests', 'notifications']
          });
        }
      } catch (error) {
        logQueryEvent('RealtimeNotifications', 'Erro ao processar mensagem de conversação de tarefa:', error, 'error');
      }
    },
    dependencies: [profile?.id],
  });

  // Subscrição para mensagens de chat (escuta diretamente chat_messages como o sistema sempre fez)
  // DESABILITADO: UnifiedRealtimeProvider/ChatHandler já processa chat_messages para evitar notificações duplicadas
  useRealtimeSubscription({
    enabled: false, // !!profile?.id, - DESABILITADO para evitar duplicação com ChatHandler
    channelName: `chat_messages_notifications:${profile?.id || "unknown"}`,
    event: "INSERT",
    schema: "public", 
    table: "chat_messages",
    filter: profile?.id ? `sender_id=neq.${profile.id}` : undefined, // Apenas mensagens que NÃO são minhas
    onPayload: async (payload: Record<string, unknown>) => {
      logQueryEvent('RealtimeNotifications', 'Nova mensagem de chat detectada:', payload);
      
             try {
         // Estrutura do realtime payload para mensagens de chat
         const newMessage = payload.new as {
           id: string;
           chat_id?: string;
           channel_id?: string;
           sender_id: string;
           content: string;
           created_at: string;
         };
         
         // Verificar se a mensagem é para mim (seja em chat 1-1 ou canal)
         let shouldNotify = false;
         
         if (newMessage.chat_id) {
           // Chat 1-1: verificar se sou participante
           const { data: isParticipant } = await supabase
             .from('chat_participants')
             .select('chat_id') // chat_participants só tem chat_id e user_id
             .eq('chat_id', newMessage.chat_id)
             .eq('user_id', profile.id)
             .maybeSingle();
           
           shouldNotify = !!isParticipant;
         } else if (newMessage.channel_id) {
           // Canal: verificar se sou membro
           const { data: isMember } = await supabase
             .from('channel_members')
             .select('channel_id')
             .eq('channel_id', newMessage.channel_id)
             .eq('user_id', profile.id)
             .maybeSingle();
           
           shouldNotify = !!isMember;
         }
        
                 if (shouldNotify) {
           // Invalidar contagem de mensagens não lidas
           queryClient.invalidateQueries({ queryKey: ['unread-chat-count'] });
           
           // Evitar notificações duplicadas usando um identificador único
           const notificationId = `${newMessage.chat_id || newMessage.channel_id}_${newMessage.sender_id}_${Date.now()}`;
           
           // Buscar dados do remetente incluindo foto
           const { data: sender } = await supabase
             .from('profiles')
             .select('full_name, avatar_url')
             .eq('id', newMessage.sender_id)
             .single();
           
           const senderName = sender?.full_name || 'Alguém';
           const senderAvatar = sender?.avatar_url;
           const messagePreview = newMessage.content?.substring(0, 50) || 'Nova mensagem';
           
           // Mostrar toast de nova mensagem com foto usando toast customizado
           // Verificação defensiva para garantir que toast existe e é uma função
           if (typeof toast === 'function' && typeof toast.custom === 'function') {
             toast.custom(
               (t) => (
                 <div className="flex items-center gap-3 p-4 bg-background border rounded-lg shadow-lg">
                   {senderAvatar ? (
                     <img 
                       src={senderAvatar} 
                       alt={senderName}
                       className="w-10 h-10 rounded-full object-cover flex-shrink-0"
                     />
                   ) : (
                     <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                       <span className="text-sm font-medium text-primary">
                         {senderName.charAt(0).toUpperCase()}
                       </span>
                     </div>
                   )}
                   <div className="flex-1 min-w-0">
                     <p className="font-medium text-foreground">Nova mensagem de {senderName}</p>
                     <p className="text-sm text-muted-foreground truncate">{messagePreview}</p>
                   </div>
                   <button 
                     onClick={() => {
                       const chatUrl = newMessage.chat_id 
                         ? `/chat/${newMessage.chat_id}`
                         : `/chat/channel/${newMessage.channel_id}`;
                       window.location.href = chatUrl;
                     }}
                     className="px-3 py-1 text-xs bg-primary text-primary-foreground rounded hover:bg-primary/90 flex-shrink-0"
                   >
                     Ver
                   </button>
                 </div>
               ),
               {
                 duration: 5000,
                 id: notificationId, // Evita duplicatas
               }
             );
           } else {
             logQueryEvent('RealtimeNotifications', 'Erro: toast.custom não está disponível', { 
               toastType: typeof toast,
               hasCustom: typeof toast?.custom,
               notificationId 
             }, 'error');
           }
         }
      } catch (error) {
        logQueryEvent('RealtimeNotifications', 'Erro ao processar notificação de mensagem:', error, 'error');
      }
    },
    dependencies: [profile?.id],
  });

  // Função para limpar a medalha conquistada
  const clearAchievedMedal = useCallback(() => {
    logQueryEvent('RealtimeNotifications', 'Limpando medalha conquistada');
    if (isMountedRef.current) {
      setAchievedMedal(null);
    }
    showNextNotification();
  }, [showNextNotification]);

  // Função para desativar notificações temporariamente
  const toggleNotifications = useCallback((enabled: boolean) => {
    logQueryEvent('RealtimeNotifications', `${enabled ? 'Ativando' : 'Desativando'} notificações`);
    if (isMountedRef.current) {
      setShouldShowNotification(enabled);
    }
  }, []);

  // Funções para gerenciar celebração de promoção
  const showPromotionCelebration = useCallback((data: PromotionCelebrationData) => {
    logQueryEvent('RealtimeNotifications', 'Exibindo celebração de promoção', { promotionId: data.id });
    setPromotionCelebrationData(data);
    setIsPromotionCelebrationVisible(true);
  }, []);

  const hidePromotionCelebration = useCallback(() => {
    logQueryEvent('RealtimeNotifications', 'Ocultando celebração de promoção');
    setIsPromotionCelebrationVisible(false);
    setTimeout(() => {
      setPromotionCelebrationData(null);
    }, 300); // Aguarda animação de saída
  }, []);

  // Funções para gerenciar cartão de aniversário
  const showBirthdayCard = useCallback((data: BirthdayCardData) => {
    logQueryEvent('RealtimeNotifications', 'Exibindo cartão de aniversário', { cardId: data.id, sender: data.sender_name });
    setBirthdayCardData(data);
    setIsBirthdayCardVisible(true);
  }, []);

  const hideBirthdayCard = useCallback(() => {
    logQueryEvent('RealtimeNotifications', 'Ocultando cartão de aniversário');
    setIsBirthdayCardVisible(false);
    setTimeout(() => {
      setBirthdayCardData(null);
    }, 300); // Aguarda animação de saída
    showNextNotification(); // Processar próxima notificação na fila
  }, [showNextNotification]);

  // Função para exibir notificação de XP ganho de forma gamificada
  const showXpGained = useCallback((amount: number, actionType: 'post_created' | 'post_liked' | 'post_commented' | 'document_uploaded' | 'document_commented' | 'document_tagged' | 'xp_gain' | string) => {
    if (!shouldShowNotification) return;
    
    logQueryEvent('RealtimeNotifications', 'Exibindo notificação de XP ganho', { amount, actionType });
    
    // Obter o nome amigável da ação, se disponível
    let actionName = actionType;
    try {
      if (typeof getActionName === 'function') {
        actionName = getActionName(actionType);
      }
    } catch (error) {
      logQueryEvent('RealtimeNotifications', 'Erro ao obter nome da ação (catch inesperado, getActionName deve estar disponível)', { error }, 'warn');
    }
    
    // Exibir toast gamificado para XP
    try {
      if (typeof showXpToast === 'function') {
        showXpToast(amount, actionName);
      } else {
        logQueryEvent('RealtimeNotifications', 'Erro: showXpToast não é uma função', { 
          showXpToastType: typeof showXpToast,
          amount,
          actionName
        }, 'error');
      }
    } catch (error) {
      logQueryEvent('RealtimeNotifications', 'Erro ao chamar showXpToast', { error, amount, actionName }, 'error');
    }
  }, [shouldShowNotification]);

  // Função para marcar notificação como lida
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from("notifications")
        .update({ read: true })
        .eq("id", notificationId);

      if (error) {
        logQueryEvent('RealtimeNotifications', 'Erro ao marcar notificação como lida:', { notificationId, error }, 'error');
        throw error;
      }
      
      // Atualizar o cache local
      queryClient.setQueryData(NOTIFICATIONS_QUERY_KEY, (old: unknown) => {
        if (Array.isArray(old)) {
          return old.map((n: { id: string; read: boolean }) =>
            n.id === notificationId ? { ...n, read: true } : n
          );
        }
        return old;
      });
      
      logQueryEvent('RealtimeNotifications', 'Notificação marcada como lida:', { notificationId });
    } catch (error) {
      logQueryEvent('RealtimeNotifications', 'Erro no catch ao marcar como lida:', { notificationId, error: error instanceof Error ? error.message : error }, 'error');
      try {
        if (typeof showAchievementToast === 'function') {
          showAchievementToast(
            "Erro", 
            "Não foi possível marcar a notificação como lida",
            "achievement"
          );
        } else {
          logQueryEvent('RealtimeNotifications', 'Erro: showAchievementToast não é uma função', { 
            showAchievementToastType: typeof showAchievementToast
          }, 'error');
        }
      } catch (toastError) {
        logQueryEvent('RealtimeNotifications', 'Erro ao exibir toast de erro', { toastError, originalError: error }, 'error');
      }
    }
  }, [queryClient]);

  // Efeito para lidar com a exibição da notificação atual
  useEffect(() => {
    if (currentNotification && isMountedRef.current) {
      logQueryEvent('RealtimeNotifications', 'Processando notificação atual:', currentNotification);
      if (currentNotification.type === 'level_up') {
        const levelData = currentNotification.data as { level: number; title?: string };
        showLevelUpToast(levelData.level);
      } else if (currentNotification.type === 'medal_earned') {
        // O som e o confete da medalha serão tratados pelo MedalUnlockedAnimation
        logQueryEvent('RealtimeNotifications', 'Notificação de medalha detectada no currentNotification', { medal: currentNotification.data });
        // Não precisamos mais de showMedalToast aqui, pois a animação cuidará disso.
      } else if (currentNotification.type === 'birthday_card') {
        // Exibir dialog de cartão de aniversário
        const cardData = currentNotification.data as BirthdayCardData;
        logQueryEvent('RealtimeNotifications', 'Notificação de cartão de aniversário detectada no currentNotification', { cardData });
        showBirthdayCard(cardData);
      } else if (currentNotification.type === 'mission_progress') {
        // Exibir notificação de progresso de missão
        const missionData = currentNotification.data as MissionProgressData;
        logQueryEvent('RealtimeNotifications', 'Notificação de progresso de missão detectada no currentNotification', { missionData });
        showMissionProgress(missionData);
      } else if (currentNotification.type === 'mission_completed') {
        // Exibir animação de missão completada
        const missionData = currentNotification.data as MissionCompletedData;
        logQueryEvent('RealtimeNotifications', 'Notificação de missão completada detectada no currentNotification', { missionData });
        showMissionCompleted(missionData);
      }
      // Adicionar outros tipos de notificação aqui conforme necessário
    }
  }, [currentNotification, showBirthdayCard, showMissionProgress, showMissionCompleted]);
  
  // Efeito para monitorar a fila de notificações e processar a próxima quando necessário
  useEffect(() => {
    if (notificationQueue.length > 0 && !isProcessing && !currentNotification) {
      logQueryEvent('RealtimeNotifications', 'Fila de notificações tem itens e não está processando. Processando próxima notificação...', {
        queueLength: notificationQueue.length,
        isProcessing,
        hasCurrentNotification: !!currentNotification
      });
      processNextNotification();
    } else if (notificationQueue.length > 0) {
      logQueryEvent('RealtimeNotifications', 'Fila de notificações tem itens, mas já está processando ou tem notificação atual', {
        queueLength: notificationQueue.length,
        isProcessing,
        hasCurrentNotification: !!currentNotification
      });
    }
  }, [notificationQueue, isProcessing, currentNotification, processNextNotification]);

  // Função para exibir modal de medalha manualmente
  const showMedalModal = useCallback(async (medalId: string) => {
    try {
      logQueryEvent('RealtimeNotifications', 'Exibindo modal de medalha manualmente', { medalId });
      
      // Buscar dados da medalha
      const { data: medalData, error } = await supabase
        .from('medals')
        .select('*')
        .eq('id', medalId)
        .single();

      if (error) {
        logQueryEvent('RealtimeNotifications', 'Erro ao buscar dados da medalha', { medalId, error }, 'error');
        throw error;
      }

      if (!medalData) {
        logQueryEvent('RealtimeNotifications', 'Medalha não encontrada', { medalId }, 'error');
        return;
      }

      // Criar objeto de medalha no formato esperado
      const medal: Medal = {
        id: medalData.id,
        name: medalData.name,
        description: medalData.description,
        type: medalData.type,
        icon_url: medalData.icon_url,
        required_count: medalData.required_count,
        progress: medalData.required_count, // Medalha já foi conquistada
        achieved_at: new Date().toISOString() // Data atual como placeholder
      };

      // Adicionar à fila de notificações para exibir o modal
      addToNotificationQueue('medal_earned', medal);
    } catch (error) {
      logQueryEvent('RealtimeNotifications', 'Erro ao exibir modal de medalha', { medalId, error }, 'error');
    }
  }, [addToNotificationQueue]);

  // Funções para gerenciar LevelUpAnimation (declaradas antes de showLevelUpModal)
  const showLevelUpAnimation = useCallback((data: LevelUpAnimationData) => {
    logQueryEvent('RealtimeNotifications', 'Exibindo LevelUpAnimation', { level: data.level, title: data.title });
    setLevelUpAnimationData(data);
    setIsLevelUpAnimationVisible(true);
  }, []);

  const hideLevelUpAnimation = useCallback(() => {
    logQueryEvent('RealtimeNotifications', 'Ocultando LevelUpAnimation');
    setIsLevelUpAnimationVisible(false);
    setTimeout(() => {
      setLevelUpAnimationData(null);
    }, 300); // Aguarda animação de saída
  }, []);



  // Função para exibir modal de level up manualmente (agora pode usar showLevelUpAnimation)
  const showLevelUpModal = useCallback((level: number, title?: string) => {
    logQueryEvent('RealtimeNotifications', 'Exibindo modal de level up manualmente', { level, title });
    
    const levelUpData: LevelUpAnimationData = {
      level,
      title: title || `Explorador do Cosmos Nv. ${level}`,
      unlockedAssets: [] // Por enquanto vazio, pode ser expandido futuramente
    };

    // Exibir diretamente o LevelUpAnimation ao invés de adicionar à fila
    showLevelUpAnimation(levelUpData);
  }, [showLevelUpAnimation]);

  // Função de teste para notificações de missão (apenas desenvolvimento)
  const triggerTestMissionNotification = useCallback((type: 'progress' | 'completed') => {
    logQueryEvent('RealtimeNotifications', 'Acionando notificação de teste de missão', { type });
    
    if (type === 'completed') {
      const testMissionCompletedData: MissionCompletedData = {
        missionId: 'test-mission-' + Date.now(),
        title: 'fazer 10 comentários',
        description: 'Interaja com a comunidade fazendo comentários construtivos',
        xpReward: 50,
        stardustReward: 25,
        activityType: 'comment_made',
      };
      addToNotificationQueue('mission_completed', testMissionCompletedData);
    } else {
      const testMissionProgressData: MissionProgressData = {
        missionId: 'test-mission-' + Date.now(),
        title: 'fazer 10 comentários',
        description: 'Interaja com a comunidade fazendo comentários construtivos',
        currentCount: 3,
        targetCount: 10,
        progressPercentage: 30,
        xpReward: 50,
        stardustReward: 25,
        activityType: 'comment_made',
        isCompleted: false,
        isNewMission: false,
      };
      addToNotificationQueue('mission_progress', testMissionProgressData);
    }
  }, [addToNotificationQueue]);

  // Listener para eventos de cartão de aniversário vindos do hook de compatibilidade
  useEffect(() => {
    const handleBirthdayCardShow = (event: CustomEvent) => {
      const data = event.detail;
      logQueryEvent('RealtimeNotifications', 'Evento de cartão de aniversário recebido', { data });
      
      // Converter dados do timeline para formato esperado pelo contexto
      const birthdayCardData: BirthdayCardData = {
        id: data.recipient?.id || 'timeline-' + Date.now(),
        sender_id: data.recipient?.id || 'system',
        sender_name: data.recipient?.name || 'Sistema',
        sender_avatar: data.recipient?.avatar,
        message: data.message || 'Parabéns pelo seu aniversário! 🎉',
        background_type: data.backgroundType || 'gradient',
        background_config: data.backgroundConfig || { colors: ['#ff6b9d', '#c44569'] },
        media_type: data.mediaType,
        media_url: data.mediaUrl,
        created_at: data.createdAt || new Date().toISOString()
      };
      
      showBirthdayCard(birthdayCardData);
    };

    window.addEventListener('vindula-birthday-card-show', handleBirthdayCardShow as EventListener);
    
    return () => {
      window.removeEventListener('vindula-birthday-card-show', handleBirthdayCardShow as EventListener);
    };
  }, [showBirthdayCard]);

  return {
    markAsRead,
    achievedMedal: currentNotification?.type === 'medal_earned' ? currentNotification.data as Medal : null,
    clearAchievedMedal,
    shouldShowNotification,
    toggleNotifications,
    showXpGained,
    currentNotification: currentNotification || null,
    showNextNotification,
    showPromotionCelebration,
    hidePromotionCelebration,
    isPromotionCelebrationVisible,
    promotionCelebrationData,
    showBirthdayCard,
    hideBirthdayCard,
    isBirthdayCardVisible,
    birthdayCardData,
    // States e funções para LevelUpAnimation
    showLevelUpAnimation,
    hideLevelUpAnimation,
    isLevelUpAnimationVisible,
    levelUpAnimationData,
    // Funções para exibir modais manualmente
    showMedalModal,
    showLevelUpModal,
    // Funções para notificações de missão
    showMissionProgress,
    hideMissionProgress,
    isMissionProgressVisible,
    missionProgressData,
    // Funções para animação de missão completada
    showMissionCompleted,
    hideMissionCompleted,
    isMissionCompletedVisible,
    missionCompletedData,
    // Função de teste para desenvolvimento
    triggerTestMissionNotification,
  };
}

export function RealtimeNotificationsProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  // Extrair todos os estados e funções do hook useNotificationsSubscription
  const {
    markAsRead,
    achievedMedal,
    clearAchievedMedal,
    shouldShowNotification,
    toggleNotifications,
    showXpGained,
    currentNotification,
    showNextNotification,
    showPromotionCelebration,
    hidePromotionCelebration,
    isPromotionCelebrationVisible,
    promotionCelebrationData,
    showBirthdayCard,
    hideBirthdayCard,
    isBirthdayCardVisible,
    birthdayCardData,
    // States e funções para LevelUpAnimation
    showLevelUpAnimation,
    hideLevelUpAnimation,
    isLevelUpAnimationVisible,
    levelUpAnimationData,
    // Funções para exibir modais manualmente
    showMedalModal,
    showLevelUpModal,
    // Funções para notificações de missão
    showMissionProgress,
    hideMissionProgress,
    isMissionProgressVisible,
    missionProgressData,
    // Funções para animação de missão completada
    showMissionCompleted,
    hideMissionCompleted,
    isMissionCompletedVisible,
    missionCompletedData,
    // Função de teste para desenvolvimento
    triggerTestMissionNotification,
  } = useNotificationsSubscription();
  
  // Obter dados do usuário diretamente do AuthStore
  const user = useAuthStore((state) => state.user);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  
  // Log quando o contexto é montado
  useEffect(() => {
    logQueryEvent('RealtimeNotificationsContext', 'Contexto de notificações montado', { 
      userId: user?.id, 
      isAuthenticated, 
      hasUser: !!user
    });
  }, [user?.id, isAuthenticated]);
  
  const value = {
    markAsRead,
    achievedMedal,
    clearAchievedMedal,
    shouldShowNotification,
    toggleNotifications,
    showXpGained,
    currentNotification,
    showNextNotification,
    showPromotionCelebration,
    hidePromotionCelebration,
    isPromotionCelebrationVisible,
    showBirthdayCard,
    hideBirthdayCard,
    isBirthdayCardVisible,
    birthdayCardData,
    // States e funções para LevelUpAnimation
    showLevelUpAnimation,
    hideLevelUpAnimation,
    isLevelUpAnimationVisible,
    levelUpAnimationData,
    // Funções para exibir modais manualmente
    showMedalModal,
    showLevelUpModal,
    // Funções para notificações de missão
    showMissionProgress,
    hideMissionProgress,
    isMissionProgressVisible,
    missionProgressData,
    // Funções para animação de missão completada
    showMissionCompleted,
    hideMissionCompleted,
    isMissionCompletedVisible,
    missionCompletedData,
    // Função de teste para desenvolvimento
    triggerTestMissionNotification,
  };

  return (
    <RealtimeNotificationsContext.Provider value={value}>
      {children}
      
      {/* Modal de celebração de promoção */}
      {isPromotionCelebrationVisible && promotionCelebrationData && (
        <PromotionCelebrationModal
          isOpen={isPromotionCelebrationVisible}
          onClose={hidePromotionCelebration}
          promotionData={promotionCelebrationData}
        />
      )}
      
      {/* Dialog de cartão de aniversário */}
      {birthdayCardData && (
        <BirthdayCardDialog
          show={isBirthdayCardVisible}
          onClose={hideBirthdayCard}
          cardData={birthdayCardData}
        />
      )}
      
      {/* LevelUpAnimation */}
      {levelUpAnimationData && (
        <LevelUpAnimation
          show={isLevelUpAnimationVisible}
          onClose={hideLevelUpAnimation}
          level={levelUpAnimationData.level}
          title={levelUpAnimationData.title}
          unlockedAssets={levelUpAnimationData.unlockedAssets || []}
        />
      )}
      
      {/* MissionProgressToast */}
      {missionProgressData && (
        <MissionProgressToast
          mission={missionProgressData}
          isVisible={isMissionProgressVisible}
          onClose={hideMissionProgress}
        />
      )}
      
      {/* MissionCompletedAnimation */}
      {missionCompletedData && (
        <MissionCompletedAnimation
          show={isMissionCompletedVisible}
          onClose={hideMissionCompleted}
          missionTitle={missionCompletedData.title}
          xpReward={missionCompletedData.xpReward}
          stardustReward={missionCompletedData.stardustReward}
          activityType={missionCompletedData.activityType}
        />
      )}
    </RealtimeNotificationsContext.Provider>
  );
}

export function useRealtimeNotifications() {
  const context = useContext(RealtimeNotificationsContext);
  if (!context) {
    logQueryEvent('RealtimeNotifications', 'Erro: useRealtimeNotifications usado fora do Provider', null, 'error');
    throw new Error(
      "useRealtimeNotifications deve ser usado dentro de RealtimeNotificationsProvider"
    );
  }
  return context;
}
