/**
 * Provider para Query Client com IndexedDB (Sistema Híbrido)
 * <AUTHOR> Internet 2025
 * 
 * Sistema híbrido para migração gradual:
 * - Permite que componentes específicos optem pelo novo sistema IndexedDB
 * - Mantém o sistema antigo localStorage funcionando em paralelo
 * - Migração gradual componente por componente
 */
import React, { createContext, useContext, useEffect, useState } from 'react';
import { QueryClient } from '@tanstack/react-query';
import { initializeCentralizedQueryClient } from '@/lib/query/queryClientCentralized';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

interface IndexedDBQueryClientContextType {
  queryClient: QueryClient | null;
  isReady: boolean;
  error: string | null;
}

const IndexedDBQueryClientContext = createContext<IndexedDBQueryClientContextType>({
  queryClient: null,
  isReady: false,
  error: null,
});

export const useIndexedDBQueryClient = () => {
  const context = useContext(IndexedDBQueryClientContext);
  if (!context) {
    throw new Error('useIndexedDBQueryClient deve ser usado dentro de IndexedDBQueryClientProvider');
  }
  return context;
};

interface IndexedDBQueryClientProviderProps {
  children: React.ReactNode;
}

export function IndexedDBQueryClientProvider({ children }: IndexedDBQueryClientProviderProps) {
  const [queryClient, setQueryClient] = useState<QueryClient | null>(null);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeClient = async () => {
      try {
        logQueryEvent('IndexedDBQueryClient', 'Inicializando query client com IndexedDB...');
        
        const { queryClient: client } = await initializeCentralizedQueryClient();
        setQueryClient(client);
        setIsReady(true);
        
        logQueryEvent('IndexedDBQueryClient', 'Query client IndexedDB inicializado com sucesso ✅', {
          hasQueryClient: !!client,
          storage: 'IndexedDB',
          parallel: 'Sistema antigo localStorage ainda ativo'
        });
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
        logQueryEvent('IndexedDBQueryClient', 'Erro ao inicializar query client IndexedDB:', err, 'error');
        setError(errorMessage);
        setIsReady(true); // Permite que a app continue mesmo com erro
      }
    };

    initializeClient();
  }, []);

  return (
    <IndexedDBQueryClientContext.Provider value={{ queryClient, isReady, error }}>
      {children}
    </IndexedDBQueryClientContext.Provider>
  );
}