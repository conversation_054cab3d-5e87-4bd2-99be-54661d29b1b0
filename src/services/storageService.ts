import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";

// Interfaces para os tipos de dados
export interface StorageData {
    currentPlan: string;
    totalStorage: number; // bytes
    usedStorage: number; // bytes
    percentUsed: number; // %
    lastUpdated?: Date;
    userCount?: number;
    nextBillingDate?: Date;
}

export interface StorageBreakdown {
    type: string;
    size: number; // bytes
    color: string;
    percentOfTotal: number; // % do total
    percentOfUsed: number; // % do usado
}

export interface StorageHistory {
    labels: string[]; // Meses
    data: number[]; // bytes usados
    quotas: number[]; // bytes totais
    growthRate?: number; // % de crescimento no último período
    projectedMonthsToQuota?: number; // Meses estimados até atingir a cota
}

export interface StorageAddon {
    id: string;
    name: string;
    description: string;
    price: number;
    sizeGB: number;
    active?: boolean;
}

export interface PurchaseAddonParams {
    companyId: string;
    addonId: string;
}

export interface SubscriptionPlan {
    id: string;
    name: string;
    description: string;
    price: number;
    storageLimit: number;
    userLimit: number;
    features: { [key: string]: boolean };
    isActive: boolean;
}

export interface StorageAddonPurchase {
    id: string;
    company_id: string;
    addon_id: string;
    purchase_date: string;
    status: string;
    price: number;
    created_at: string;
    updated_at: string;
    storage_addons: StorageAddon;
}

// Novas interfaces para as novas funcionalidades
export interface StorageDetailedStats {
    storageData: StorageData;
    breakdown: StorageBreakdown[];
    history: StorageHistory;
    addons: StorageAddon[];
    status: {
        usage_status: "critical" | "warning" | "normal";
        status_message: string;
        recommendation: string;
        priority: "high" | "medium" | "low";
    };
}

export interface StorageRecommendation {
    recommendationType: "urgent" | "important" | "info";
    message: string;
    recommendedAddonId: string | null;
    currentUsage: number;
    actions: StorageAction[];
}

export interface StorageAction {
    type: string;
    priority: "high" | "medium" | "low";
    addon_id?: string;
    area?: string;
    description?: string;
}

export interface StorageGrowthProjection {
    currentUsage: number;
    currentLimit: number;
    growthRatePercent: number;
    projectionMonths: number;
    willExceedLimit: boolean;
    exceedDate: string | null;
    monthsUntilFull: number | null;
    projectedData: {
        month: string;
        projectedUsage: number;
        storageLimit: number;
        percentUsed: number;
        exceedsLimit: boolean;
    }[];
    monthLabels: string[];
    recommendedAddons?: StorageAddon[];
}

export interface AddonImpactSimulation {
    currentUsage: number;
    currentLimit: number;
    newLimit: number;
    addonDetails: StorageAddon;
    additionalMonths: number;
    baseProjection: StorageGrowthProjection;
    withAddonProjection: StorageGrowthProjection;
    costBenefit: {
        pricePerGB: number;
        pricePerMonth: number;
    };
}

export interface StorageViewResponse {
    company_id: string;
    company_name: string;
    subscription_name: string;
    subscription_price: number;
    billing_cycle: string;
    current_period_start: string;
    current_period_end: string;
    used_storage: number;
    storage_limit: number;
    percent_used: number;
    last_updated: string;
    active_addons: Array<{
        id: string;
        name: string;
        size_gb: number;
        price: number;
        purchase_date: string;
        status: string;
    }>;
    storage_breakdown: Array<{
        type: string;
        size: number;
        color: string;
        percentOfTotal: number;
        percentOfUsed: number;
    }>;
    storage_history: Array<{
        month: string;
        used_storage: number;
        storage_limit: number;
    }>;
    growth_rate: number;
    usage_status: "critical" | "warning" | "normal";
    status_message: string;
    recommendation: string;
    priority: "high" | "medium" | "low";
}

// Mapeamento de cores para tipos de arquivo
const typeColors: Record<string, string> = {
    documents: "bg-blue-500",
    posts: "bg-green-500",
    chat: "bg-purple-600",
    media: "bg-purple-500",
    images: "bg-emerald-500",
    videos: "bg-violet-500",
    audio: "bg-amber-500",
    other: "bg-orange-500",
    others: "bg-gray-500",
};

// Constantes para conversão de unidades
const BYTES_PER_KB = 1024;
const BYTES_PER_MB = 1024 * 1024;
const BYTES_PER_GB = 1024 * 1024 * 1024;

// Constante para armazenamento mínimo (1GB em bytes)
const MIN_STORAGE_BYTES = 1024 * 1024 * 1024; // 1GB em bytes

/**
 * Converte bytes para gigabytes
 * @param bytes Número de bytes
 * @returns Valor em gigabytes
 * <AUTHOR> Internet 2025
 */
const bytesToGB = (bytes: number): number => {
    // Garantir que a entrada é um número válido
    if (isNaN(bytes) || bytes === 0) return 0;

    // Divisão direta por BYTES_PER_GB para obter GB
    return bytes / BYTES_PER_GB;
};

/**
 * Converte gigabytes para bytes
 * @param gb Número de gigabytes
 * @returns Valor em bytes
 * <AUTHOR> Internet 2025
 */
const gbToBytes = (gb: number): number => {
    // Garantir que a entrada é um número válido
    if (isNaN(gb) || gb === 0) return 0;

    return gb * BYTES_PER_GB;
};

// Formatação de tamanho para exibição em bytes
/**
 * Formata o tamanho de armazenamento para exibição
 * @param sizeInBytes Tamanho em bytes
 * @returns String formatada com a unidade apropriada
 * <AUTHOR> Internet 2025
 */
export function formatStorageSize(sizeInBytes: number): string {
    // Se for menor que 1MB (1024 * 1024 bytes), mostrar em KB
    if (sizeInBytes < 1024 * 1024) {
        return `${(sizeInBytes / 1024).toFixed(2)} KB`;
    }

    // Se for menor que 1GB (1024 * 1024 * 1024 bytes), mostrar em MB
    if (sizeInBytes < 1024 * 1024 * 1024) {
        return `${(sizeInBytes / (1024 * 1024)).toFixed(2)} MB`;
    }

    // Se for menor que 1TB (1024 * 1024 * 1024 * 1024 bytes), mostrar em GB
    if (sizeInBytes < 1024 * 1024 * 1024 * 1024) {
        return `${(sizeInBytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
    }

    // Para valores maiores que 1TB
    return `${(sizeInBytes / (1024 * 1024 * 1024 * 1024)).toFixed(2)} TB`;
}

export class StorageService {
    /**
     * Obtém dados básicos de armazenamento para uma empresa
     * <AUTHOR> Internet 2025
     */
    static async getStorageData(
        companyId: string,
    ): Promise<StorageData | null> {
        try {
            // Buscar dados atuais de uso de armazenamento
            const { data: usageData, error: usageError } = await supabase
                .from("storage_usage")
                .select("*")
                .eq("company_id", companyId)
                .maybeSingle();

            // Se não há dados, isso é OK - empresa nova pode não ter dados de storage ainda
            if (usageError && usageError.code !== "PGRST116") {
                console.error(
                    "Erro ao buscar dados de armazenamento:",
                    usageError,
                );
                return null;
            }

            // Buscar dados da empresa
            const { data: companyData, error: companyError } = await supabase
                .from("companies")
                .select("*")
                .eq("id", companyId)
                .single();

            if (companyError) {
                console.error("Erro ao buscar dados da empresa:", companyError);
                return null;
            }

            // Buscar dados de assinatura e plano
            // Especificar o constraint correto para disambiguar a relação com subscription_plans
            const { data: subscriptionData, error: subError } = await supabase
                .from("subscriptions")
                .select(`
                    *,
                    subscription_plans!subscriptions_plan_id_fkey(
                        id,
                        name,
                        storage_limit
                    )
                `)
                .eq("company_id", companyId)
                .eq("status", "active")
                .maybeSingle();

            if (subError && subError.code !== "PGRST116") {
                console.error("Erro ao buscar dados de assinatura:", subError);
            }

            // Usar dados do plano se disponíveis, ou valores padrão
            const planName = subscriptionData?.subscription_plans?.name || "Plano Básico";

            // ✅ CORRIGIDO: Usar função SQL que calcula storage total incluindo addons
            const { data: totalStorageData, error: storageError } = await supabase
                .rpc("get_total_storage_limit", { p_company_id: companyId });

            if (storageError) {
                console.error("Erro ao calcular storage total:", storageError);
            }

            // Converter de GB para bytes (a função retorna em GB)
            const totalStorageGB = totalStorageData || 1; // Default 1GB se não encontrar
            const totalStorage = Math.max(totalStorageGB * BYTES_PER_GB, MIN_STORAGE_BYTES);

            // O used_storage no banco está em bytes
            const usedStorage = Math.max(
                parseFloat(usageData?.used_storage || "0"),
                0,
            );

            // Calcular percentual de uso com limites
            const percentUsed = Math.min(
                Math.max(
                    totalStorage > 0 ? (usedStorage / totalStorage) * 100 : 0,
                    0,
                ),
                100,
            );

            return {
                currentPlan: planName,
                totalStorage,
                usedStorage,
                percentUsed,
                lastUpdated: usageData?.updated_at
                    ? new Date(usageData.updated_at)
                    : new Date(),
                userCount: companyData?.user_count || 0,
                nextBillingDate: subscriptionData?.current_period_end
                    ? new Date(subscriptionData.current_period_end)
                    : null,
            };
        } catch (error) {
            console.error("Erro ao obter dados de armazenamento:", error);
            return null;
        }
    }

    /**
     * Obtém a distribuição de armazenamento por tipo de arquivo
     * <AUTHOR> Internet 2025
     */
    static async getStorageBreakdown(
        companyId: string,
    ): Promise<StorageBreakdown[] | null> {
        try {
            // Buscar dados detalhados de uso por categoria
            const { data: breakdownData, error: breakdownError } =
                await supabase
                    .from("storage_breakdown")
                    .select("*")
                    .eq("company_id", companyId);

            if (breakdownError) {
                console.error("Erro ao buscar breakdown:", breakdownError);
                return null;
            }

            if (!breakdownData || breakdownData.length === 0) {
                console.warn("Nenhum dado de breakdown encontrado");
                return null;
            }

            // Buscar total de armazenamento usado
            const { data: usageData, error: usageError } = await supabase
                .from("storage_usage")
                .select("used_storage")
                .eq("company_id", companyId)
                .maybeSingle();

            if (usageError && usageError.code !== "PGRST116") {
                console.error("Erro ao buscar uso total:", usageError);
                return null;
            }

            // Manter em bytes
            const totalUsedBytes = parseFloat(
                (usageData as any)?.used_storage || "0",
            );

            // Transformar em StorageBreakdown
            return (breakdownData as any[]).map((item) => {
                // Manter tamanho em bytes
                const sizeInBytes = parseFloat(item.size || "0");

                const percentOfTotal = totalUsedBytes > 0
                    ? (sizeInBytes / totalUsedBytes) * 100
                    : 0;

                return {
                    type: item.type,
                    size: sizeInBytes,
                    color: typeColors[item.type] || "bg-gray-500",
                    percentOfTotal,
                    percentOfUsed: percentOfTotal,
                };
            });
        } catch (error) {
            console.error("Erro ao obter breakdown de armazenamento:", error);
            return null;
        }
    }

    /**
     * Obtém o histórico de uso de armazenamento
     * <AUTHOR> Internet 2025
     */
    static async getStorageHistory(
        companyId: string,
        months: number = 6,
    ): Promise<StorageHistory | null> {
        try {
            // Buscar histórico de uso
            const { data: historyData, error: historyError } = await supabase
                .from("storage_history")
                .select("*")
                .eq("company_id", companyId)
                .order("month", { ascending: true })
                .limit(months);

            if (historyError) {
                console.error("Erro ao buscar histórico:", historyError);
                return null;
            }

            if (!historyData || historyData.length === 0) {
                console.warn("Nenhum dado histórico encontrado");
                return null;
            }

            // Formatar dados para o gráfico
            const labels = historyData.map((item) => {
                const date = new Date(item.month);
                return date.toLocaleDateString("pt-BR", {
                    month: "short",
                    year: "numeric",
                });
            });

            // Manter valores em bytes
            const data = historyData.map((item) =>
                parseFloat(item.used_storage)
            );

            // Manter limites em bytes
            const quotas = historyData.map((item) =>
                parseFloat(item.storage_limit)
            );

            // Calcular taxa de crescimento
            let growthRate = 0;
            if (historyData.length >= 2) {
                const latest = data[data.length - 1];
                const previous = data[data.length - 2];

                if (previous > 0) {
                    growthRate = ((latest - previous) / previous) * 100;
                }
            }

            // Calcular meses até atingir a cota (projeção)
            let projectedMonthsToQuota = null;
            if (growthRate > 0) {
                const latestUsage = data[data.length - 1];
                const latestQuota = quotas[quotas.length - 1];

                if (latestUsage < latestQuota) {
                    const monthlyGrowth = latestUsage * (growthRate / 100);
                    const remainingStorage = latestQuota * 0.8 - latestUsage; // 80% da cota

                    if (monthlyGrowth > 0) {
                        projectedMonthsToQuota = Math.ceil(
                            remainingStorage / monthlyGrowth,
                        );
                    }
                }
            }

            return {
                labels,
                data,
                quotas,
                growthRate,
                projectedMonthsToQuota,
            };
        } catch (error) {
            console.error("Erro ao obter histórico de armazenamento:", error);
            return null;
        }
    }

    /**
     * Obtém os addons de armazenamento disponíveis
     * @returns Lista de addons ou null em caso de erro
     */
    static async getStorageAddons(): Promise<StorageAddon[] | null> {
        try {
            const { data, error } = await supabase
                .from("storage_addons")
                .select("*")
                .eq("active", true)
                .order("price", { ascending: true });

            if (error) {
                console.warn("Erro ao buscar addons, usando mock:", error);
                return null;
            }

            if (!data || data.length === 0) {
                console.warn("Nenhum addon encontrado, usando mock");
                return null;
            }

            return data.map((addon) => ({
                id: addon.id,
                name: addon.name,
                description: addon.description || "",
                price: parseFloat(addon.price),
                sizeGB: parseFloat(addon.size_gb),
                active: addon.active,
            }));
        } catch (error) {
            console.error("Erro ao buscar addons de armazenamento:", error);
            return null;
        }
    }

    /**
     * Obtém o histórico de compras de addons de armazenamento
     * @param companyId ID da empresa
     * @returns Histórico de compras ou null em caso de erro
     */
    static async getStorageAddonPurchases(
        companyId: string,
    ): Promise<StorageAddonPurchase[] | null> {
        try {
            const { data, error } = await supabase
                .from("storage_addon_purchases")
                .select("*, storage_addons(*)")
                .eq("company_id", companyId)
                .order("purchase_date", { ascending: false });

            if (error) throw error;

            return data as StorageAddonPurchase[];
        } catch (error) {
            console.error("Erro ao buscar histórico de compras:", error);
            return null;
        }
    }

    /**
     * Obtém estatísticas detalhadas de armazenamento incluindo previsões
     * @param companyId ID da empresa
     * @returns Estatísticas detalhadas ou null em caso de erro
     */
    static async getDetailedStats(
        companyId: string,
    ): Promise<StorageDetailedStats | null> {
        try {
            // Chamar a função RPC que retorna estatísticas detalhadas
            const { data, error } = await supabase.rpc(
                "get_storage_detailed_stats",
                { p_company_id: companyId },
            );

            if (error) {
                console.warn(
                    "Erro ao buscar estatísticas detalhadas, usando mock:",
                    error,
                );
                return null;
            }

            return data as StorageDetailedStats;
        } catch (error) {
            console.error("Erro ao buscar estatísticas detalhadas:", error);
            return null;
        }
    }

    /**
     * Obtém recomendações personalizadas de armazenamento
     * @param companyId ID da empresa
     * @returns Recomendações ou null em caso de erro
     */
    static async getStorageRecommendations(
        companyId: string,
    ): Promise<StorageRecommendation | null> {
        try {
            // Chamar a função RPC que retorna recomendações
            const { data, error } = await supabase.rpc(
                "get_storage_recommendations",
                { p_company_id: companyId },
            );

            if (error) throw error;

            return data as StorageRecommendation;
        } catch (error) {
            console.error(
                "Erro ao buscar recomendações de armazenamento:",
                error,
            );
            return null;
        }
    }

    /**
     * Simula o crescimento do armazenamento para os próximos meses
     * @param companyId ID da empresa
     * @param months Número de meses para projeção
     * @param growthRate Taxa de crescimento mensal (opcional)
     * @returns Projeção de crescimento ou null em caso de erro
     */
    static async simulateStorageGrowth(
        companyId: string,
        months: number = 12,
        growthRate?: number,
    ): Promise<StorageGrowthProjection | null> {
        try {
            // Preparar parâmetros para a função RPC
            const params: {
                p_company_id: string;
                p_months: number;
                p_monthly_growth_percent?: number;
            } = {
                p_company_id: companyId,
                p_months: months,
            };

            // Adicionar taxa de crescimento se especificada
            if (growthRate !== undefined) {
                params.p_monthly_growth_percent = growthRate;
            }

            // Chamar a função RPC que simula crescimento
            const { data, error } = await supabase.rpc(
                "simulate_storage_growth",
                params,
            );

            if (error) throw error;

            return data as StorageGrowthProjection;
        } catch (error) {
            console.error(
                "Erro ao simular crescimento de armazenamento:",
                error,
            );
            return null;
        }
    }

    /**
     * Simula o impacto da compra de um addon na previsão de armazenamento
     * @param companyId ID da empresa
     * @param addonId ID do addon a ser simulado
     * @param months Número de meses para projeção
     * @returns Simulação de impacto ou null em caso de erro
     */
    static async simulateAddonImpact(
        companyId: string,
        addonId: string,
        months: number = 12,
    ): Promise<AddonImpactSimulation | null> {
        try {
            // Chamar a função RPC que simula impacto do addon
            const { data, error } = await supabase.rpc(
                "simulate_addon_impact",
                {
                    p_company_id: companyId,
                    p_addon_id: addonId,
                    p_months: months,
                },
            );

            if (error) throw error;

            // Tratamento para resposta da API que pode conter um campo de erro
            if (data && typeof data === "object" && "error" in data) {
                throw new Error(data.error as string);
            }

            // Se os dados existem, converter valores de bytes para GB
            if (data) {
                // Converter os valores do impacto do addon (no corpo principal)
                const result = data as any;

                if ("currentUsage" in result) {
                    result.currentUsage = bytesToGB(
                        parseFloat(result.currentUsage),
                    );
                }

                if ("currentLimit" in result) {
                    // Limites já estão em GB, não precisam ser convertidos
                    result.currentLimit = parseFloat(result.currentLimit);
                }

                if ("newLimit" in result) {
                    // Limites já estão em GB, não precisam ser convertidos
                    result.newLimit = parseFloat(result.newLimit);
                }

                // Converter os valores nas projeções
                if (
                    "baseProjection" in result && result.baseProjection &&
                    "projectedData" in result.baseProjection
                ) {
                    result.baseProjection.projectedData = result.baseProjection
                        .projectedData.map((item: any) => ({
                            ...item,
                            projectedUsage: bytesToGB(
                                parseFloat(item.projectedUsage),
                            ),
                            // Limites já estão em GB
                            storageLimit: parseFloat(item.storageLimit),
                        }));
                }

                if (
                    "withAddonProjection" in result &&
                    result.withAddonProjection &&
                    "projectedData" in result.withAddonProjection
                ) {
                    result.withAddonProjection.projectedData = result
                        .withAddonProjection.projectedData.map((item: any) => ({
                            ...item,
                            projectedUsage: bytesToGB(
                                parseFloat(item.projectedUsage),
                            ),
                            // Limites já estão em GB
                            storageLimit: parseFloat(item.storageLimit),
                        }));
                }

                return result as AddonImpactSimulation;
            }

            return data as AddonImpactSimulation;
        } catch (error) {
            console.error("Erro ao simular impacto do addon:", error);

            toast({
                title: "Erro na simulação",
                description:
                    "Não foi possível simular o impacto do addon. Tente novamente mais tarde.",
                variant: "destructive",
            });

            return null;
        }
    }

    /**
     * Processa a compra de um addon de armazenamento
     * @param companyId ID da empresa
     * @param addonId ID do addon
     * @returns true se a compra foi bem-sucedida, false caso contrário
     */
    static async purchaseAddon(
        params: PurchaseAddonParams,
    ): Promise<boolean> {
        try {
            // Chamar a função RPC para processar a compra de addon
            const { data, error } = await supabase.rpc(
                "process_storage_addon_purchase",
                {
                    p_company_id: params.companyId,
                    p_addon_id: params.addonId,
                },
            );

            if (error) throw error;

            if (!data) {
                throw new Error("Falha ao processar a compra do addon");
            }

            // Se chegou aqui, a compra foi bem-sucedida
            toast({
                title: "Addon de armazenamento adicionado",
                description: "O addon foi processado e adicionado à sua conta.",
            });

            return true;
        } catch (error) {
            console.error("Erro ao processar compra de addon:", error);

            toast({
                title: "Erro ao adicionar armazenamento",
                description:
                    "Ocorreu um erro ao processar sua compra. Tente novamente mais tarde.",
                variant: "destructive",
            });

            return false;
        }
    }

    /**
     * Obtém todos os dados de armazenamento de uma empresa usando V3 (storage_files)
     * @param companyId ID da empresa
     * @returns Dados detalhados de armazenamento
     * <AUTHOR> Internet 2025
     */
    static async getAllStorageData(
        companyId: string,
    ): Promise<StorageDetailedStats> {
        try {
            // 🔄 ATUALIZADO: Usar a nova função V3 que utiliza storage_files
            const { data: dashboardData, error: dashboardError } = await supabase
                .rpc("get_storage_dashboard_data_v3", { p_company_id: companyId })
                .maybeSingle();

            if (dashboardError) {
                console.error("Erro ao buscar dados do dashboard V3:", dashboardError);
                throw dashboardError;
            }

            // Se não houver dados, retornar um objeto com valores padrão
            if (!dashboardData) {
                // Criar um objeto de resposta padrão
                return {
                    storageData: {
                        currentPlan: "",
                        totalStorage: 1024 * 1024 * 1024, // 1GB em bytes
                        usedStorage: 0,
                        percentUsed: 0,
                        lastUpdated: new Date(),
                    },
                    breakdown: [],
                    history: {
                        labels: [],
                        data: [],
                        quotas: [],
                        growthRate: 0,
                        projectedMonthsToQuota: null,
                    },
                    addons: [],
                    status: {
                        usage_status: "normal",
                        status_message: "Sem dados de armazenamento",
                        recommendation: "Configure seu armazenamento",
                        priority: "low",
                    },
                };
            }

            // 🔄 NOVO: Processar dados da função V3
            const { used_storage, total_storage, storage_limit, breakdown, stats } = dashboardData;

            // Obter dados de assinatura para complementar informações
            const { data: subscriptionData } = await supabase
                .from("subscriptions")
                .select(`
                    *,
                    subscription_plans(name, price)
                `)
                .eq("company_id", companyId)
                .eq("status", "active")
                .maybeSingle();

            // Obter histórico de storage
            const { data: historyData } = await supabase
                .from("storage_history")
                .select("*")
                .eq("company_id", companyId)
                .order("month", { ascending: true })
                .limit(12);

            // Converter breakdown JSONB para array
            const breakdownArray: StorageBreakdown[] = breakdown ? 
                Object.entries(breakdown).map(([type, size]) => ({
                    type,
                    size: Number(size),
                    color: typeColors[type] || typeColors.other,
                    percentOfTotal: used_storage > 0 ? (Number(size) / used_storage) * 100 : 0,
                    percentOfUsed: used_storage > 0 ? (Number(size) / used_storage) * 100 : 0
                })) : [];

            // Obter add-ons ativos
            const { data: addonsData } = await supabase
                .from("storage_addon_purchases")
                .select(`
                    *,
                    storage_addons(id, name, description, price, size_gb)
                `)
                .eq("company_id", companyId)
                .eq("status", "active");

            // Calcular percentual de uso
            const percentUsed = storage_limit > 0 ? (used_storage / storage_limit) * 100 : 0;

            // Determinar status
            let usage_status: "critical" | "warning" | "normal" = "normal";
            let status_message = "Uso normal de armazenamento";
            let recommendation = "Continue monitorando o uso";
            let priority: "high" | "medium" | "low" = "low";

            if (percentUsed >= 95) {
                usage_status = "critical";
                status_message = "Armazenamento quase esgotado";
                recommendation = "Urgente: Adicione mais espaço ou limpe arquivos";
                priority = "high";
            } else if (percentUsed >= 80) {
                usage_status = "warning";
                status_message = "Uso alto de armazenamento";
                recommendation = "Considere adicionar mais espaço";
                priority = "medium";
            }

            // Converter os dados para o formato esperado pelo frontend
            const result: StorageDetailedStats = {
                storageData: {
                    currentPlan: subscriptionData?.subscription_plans?.name || "Free",
                    totalStorage: storage_limit,
                    usedStorage: used_storage,
                    percentUsed,
                    lastUpdated: new Date(),
                    nextBillingDate: subscriptionData?.current_period_end
                        ? new Date(subscriptionData.current_period_end)
                        : undefined,
                },
                breakdown: breakdownArray,
                history: {
                    labels: (historyData || []).map((h) =>
                        new Date(h.month).toLocaleDateString('pt-BR', { month: 'short', year: 'numeric' })
                    ),
                    data: (historyData || []).map((h) => Number(h.used_storage)),
                    quotas: (historyData || []).map((h) => Number(h.storage_limit)),
                    growthRate: 0, // Será calculado se necessário
                    projectedMonthsToQuota: null,
                },
                addons: (addonsData || []).map((addon) => ({
                    id: addon.storage_addons.id,
                    name: addon.storage_addons.name,
                    description: addon.storage_addons.description || 
                        `${addon.storage_addons.size_gb}GB de armazenamento adicional`,
                    price: addon.storage_addons.price,
                    sizeGB: addon.storage_addons.size_gb,
                    active: addon.status === "active",
                })),
                status: {
                    usage_status,
                    status_message,
                    recommendation,
                    priority,
                },
            };

            return result;
        } catch (error) {
            console.error("Erro ao obter dados de armazenamento:", error);
            // Retornar um objeto com valores padrão em vez de lançar o erro
            return {
                storageData: {
                    currentPlan: "",
                    totalStorage: 1024 * 1024 * 1024, // 1GB em bytes
                    usedStorage: 0,
                    percentUsed: 0,
                    lastUpdated: new Date(),
                },
                breakdown: [],
                history: {
                    labels: [],
                    data: [],
                    quotas: [],
                    growthRate: 0,
                    projectedMonthsToQuota: null,
                },
                addons: [],
                status: {
                    usage_status: "normal",
                    status_message: "Erro ao carregar dados",
                    recommendation: "Tente novamente mais tarde",
                    priority: "low",
                },
            };
        }
    }

    /**
     * Obtém todos os planos de assinatura disponíveis
     * @returns Lista de planos ou null em caso de erro
     */
    static async getSubscriptionPlans(): Promise<SubscriptionPlan[] | null> {
        try {
            const { data, error } = await supabase
                .from("subscription_plans")
                .select("*")
                .eq("is_active", true)
                .order("price", { ascending: true });

            if (error) throw error;

            return data.map((plan) => ({
                id: plan.id,
                name: plan.name,
                description: plan.description || "",
                price: parseFloat(plan.price),
                storageLimit: parseFloat(plan.storage_limit),
                userLimit: plan.user_limit,
                features: plan.features,
                isActive: plan.is_active,
            }));
        } catch (error) {
            console.error("Erro ao buscar planos de assinatura:", error);
            return null;
        }
    }

    /**
     * Força a atualização dos dados de uso de armazenamento de uma empresa
     * @param companyId ID da empresa
     * @returns true se a atualização foi bem-sucedida, false caso contrário
     */
    static async forceUpdateStorageUsage(companyId: string): Promise<boolean> {
        try {
            const { data, error } = await supabase.rpc(
                "update_company_storage_usage_v3", // Usando a versão V3 da função (corrigida para overflow)
                { p_company_id: companyId },
            );

            if (error) throw error;


            return true;
        } catch (error) {
            console.error("Erro ao atualizar uso de armazenamento:", error);

            toast({
                title: "Erro ao atualizar armazenamento",
                description:
                    "Não foi possível atualizar os dados de uso. Tente novamente mais tarde.",
                variant: "destructive",
            });

            return false;
        }
    }
}
