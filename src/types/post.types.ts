export interface PostImage {
  id: string;
  image_url: string;
  storage_path: string;
  size: number;
  created_at: string;
}

export interface Post {
  id: string;
  content: string;
  created_at: string;
  updated_at?: string;
  likes: number;
  company_id: string;
  status: 'draft' | 'scheduled' | 'published';
  scheduled_at?: string;
  has_poll?: boolean;
  // Campos de edição
  is_edited?: boolean;
  edit_count?: number;
  last_edited_at?: string;
  last_edited_by?: string;
  audience: {
    type: 'all' | 'department' | 'team' | 'user';
    targets?: string[];
    targetCount?: number;
  };
  author: {
    id: string;
    full_name: string | null;
    avatar_url: string | null;
  };
  liked_by: Array<{
    profiles: {
      id: string;
      full_name: string | null;
      avatar_url: string | null;
    };
  }>;
  mentions?: Array<{
    id: string;
    full_name: string;
    avatar_url: string | null;
  }>;
  images?: PostImage[];
}