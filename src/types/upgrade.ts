/**
 * Tipos TypeScript para o Sistema de Upgrade Unificado
 * Baseado no PRD do Componente Unificado de Upgrade
 * <AUTHOR> Internet 2025
 */

// Enum para as origens de upgrade
export enum UpgradeSource {
  AI_CREDITS = 'ai-credits',
  STORAGE_FULL = 'storage-full', 
  USERS_LIMIT = 'users-limit',
  PLAN_MANAGEMENT = 'plan-management',
  FEED = 'feed',
  MOBILE_NAVIGATION = 'navegacaoaplicativomobile',
  KNOWLEDGE_HUB = 'knowledge-hub',
  PEOPLE_DIRECTORY = 'people-directory',
  CHAT_HISTORY = 'chat-history',
  TRANSCRIPTION = 'transcription',
  MEDALS = 'medals',
  LEVELS = 'levels',
  ACTIONS = 'actions',
  MARKETPLACE = 'marketplace'
}

// Enum para os status do lead comercial
export enum CommercialLeadStatus {
  PENDING = 'pending',
  CONTACTED = 'contacted',
  NEGOTIATING = 'negotiating',
  CONVERTED = 'converted',
  LOST = 'lost',
  COURTESY_EXPIRED = 'courtesy_expired',
  CANCELLED = 'cancelled'
}

// Enum para os tipos de evento de analytics
export enum UpgradeEventType {
  PAGE_VIEW = 'page_view',
  PLAN_SELECTED = 'plan_selected',
  ADDON_SELECTED = 'addon_selected',
  ADDON_DESELECTED = 'addon_deselected',
  FORM_SUBMITTED = 'form_submitted',
  CONFIRMATION_VIEWED = 'confirmation_viewed',
  TERMS_ACCEPTED = 'terms_accepted',
  LEAD_SUBMITTED = 'lead_submitted',
  PLAN_ACTIVATED = 'plan_activated',
  COURTESY_STARTED = 'courtesy_started'
}

// Interface para informações de plano
export interface PlanInfo {
  id: string;
  name: string;
  description: string;
  monthlyPrice: number;
  annualPrice: number;
  features: string[];
  userLimit: number;
  storageLimit: number; // em GB
  aiCredits: number | 'unlimited';
  badge?: 'popular' | 'best-value' | 'recommended';
  isCurrentPlan?: boolean;
}

// Interface para add-ons
export interface AddOn {
  id: string;
  name: string;
  description: string;
  type: 'user_pack' | 'storage_pack' | 'ai_credits';
  monthlyPrice: number;
  annualPrice: number;
  value: number; // quantidade de usuários, GB ou créditos
  relevanceScore?: number; // para ordenação baseada em contexto
  isSelected?: boolean;
}

// Interface para informações de contato
export interface ContactInfo {
  name: string;
  email: string;
  phone?: string;
  company: string;
  position?: string;
  preferredContactTime?: 'morning' | 'afternoon' | 'evening';
  preferredContactMethod?: 'email' | 'phone' | 'whatsapp';
  timezone?: string;
}

// Interface para preferências de pagamento
export interface PaymentPreferences {
  billingCycle: 'monthly' | 'annual';
  paymentMethod?: 'credit_card' | 'bank_transfer' | 'pix';
  invoiceEmail?: string;
  purchaseOrderRequired?: boolean;
  budgetApprovalNeeded?: boolean;
  decisionTimeframe?: 'immediate' | 'week' | 'month' | 'quarter';
}

// Interface para contexto de upgrade
export interface UpgradeContext {
  source: UpgradeSource;
  sessionId: string;
  currentPlan: PlanInfo;
  availablePlans: PlanInfo[];
  availableAddons: AddOn[];
  urgencyLevel: 'low' | 'medium' | 'high';
  limitReached?: {
    type: 'users' | 'storage' | 'ai_credits';
    current: number;
    limit: number;
    percentage: number;
  };
  recommendations?: {
    suggestedPlan?: PlanInfo;
    suggestedAddons?: AddOn[];
    reasoning: string;
  };
}

// Interface para estado do carrinho
export interface CartState {
  selectedPlan?: PlanInfo;
  selectedAddons: AddOn[];
  billingCycle: 'monthly' | 'annual';
  subtotal: number;
  discount: number;
  total: number;
  savings?: {
    amount: number;
    percentage: number;
    comparedTo: 'current' | 'monthly';
  };
}

// Interface para dados do lead comercial
export interface CommercialLeadData {
  id?: string;
  companyId: string;
  userId: string;
  selectedPlan: PlanInfo;
  selectedAddons: AddOn[];
  estimatedMonthlyValue: number;
  contactInfo: ContactInfo;
  paymentPreferences: PaymentPreferences;
  sourceContext: UpgradeSource;
  sourceFeature?: string;
  urgencyLevel: 'low' | 'medium' | 'high';
  status: CommercialLeadStatus;
  courtesyPeriodEnd?: Date;
  notes?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// Interface para evento de analytics
export interface UpgradeAnalyticsEvent {
  id?: string;
  sessionId: string;
  companyId: string;
  userId: string;
  eventType: UpgradeEventType;
  eventData?: Record<string, any>;
  sourceContext: UpgradeSource;
  stepName?: string;
  stepOrder?: number;
  timeSpentSeconds?: number;
  converted?: boolean;
  conversionValue?: number;
  createdAt?: Date;
}

// Interface para estado global do upgrade
export interface UpgradeState {
  // Contexto e configuração
  context: UpgradeContext | null;
  isLoading: boolean;
  error: string | null;
  
  // Estado do carrinho
  cart: CartState;
  
  // Estado do formulário
  contactInfo: Partial<ContactInfo>;
  paymentPreferences: Partial<PaymentPreferences>;
  
  // Estado do fluxo
  currentStep: 'plan-selection' | 'addons' | 'contact-info' | 'confirmation' | 'success';
  completedSteps: string[];
  
  // Período de cortesia
  courtesyPeriod?: {
    startDate: Date;
    endDate: Date;
    isActive: boolean;
  };
  
  // Analytics
  sessionId: string;
  events: UpgradeAnalyticsEvent[];
}

// Interface para ações do store
export interface UpgradeActions {
  // Inicialização
  initializeUpgrade: (source: UpgradeSource, context: Partial<UpgradeContext>) => void;
  resetUpgrade: () => void;
  
  // Gerenciamento de planos
  selectPlan: (plan: PlanInfo) => void;
  clearPlanSelection: () => void;
  
  // Gerenciamento de add-ons
  toggleAddon: (addon: AddOn) => void;
  clearAddons: () => void;
  
  // Ciclo de cobrança
  setBillingCycle: (cycle: 'monthly' | 'annual') => void;
  
  // Informações de contato
  updateContactInfo: (info: Partial<ContactInfo>) => void;
  updatePaymentPreferences: (prefs: Partial<PaymentPreferences>) => void;
  
  // Navegação do fluxo
  setCurrentStep: (step: UpgradeState['currentStep']) => void;
  markStepCompleted: (step: string) => void;
  
  // Analytics
  trackEvent: (event: Omit<UpgradeAnalyticsEvent, 'id' | 'sessionId' | 'companyId' | 'userId' | 'createdAt'>) => void;
  
  // Submissão
  submitLead: () => Promise<{ success: boolean; leadId?: string; error?: string }>;
  
  // Estados de loading e erro
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

// Interface para o store completo
export interface UpgradeStore extends UpgradeState, UpgradeActions {}

// Interface para configuração de gradientes por contexto
export interface ContextGradients {
  [UpgradeSource.AI_CREDITS]: {
    from: string;
    to: string;
    accent: string;
  };
  [UpgradeSource.STORAGE_FULL]: {
    from: string;
    to: string;
    accent: string;
  };
  [UpgradeSource.USERS_LIMIT]: {
    from: string;
    to: string;
    accent: string;
  };
  [UpgradeSource.PLAN_MANAGEMENT]: {
    from: string;
    to: string;
    accent: string;
  };
  [UpgradeSource.FEED]: {
    from: string;
    to: string;
    accent: string;
  };
  [UpgradeSource.MOBILE_NAVIGATION]: {
    from: string;
    to: string;
    accent: string;
  };
  [UpgradeSource.KNOWLEDGE_HUB]: {
    from: string;
    to: string;
    accent: string;
  };
  [UpgradeSource.PEOPLE_DIRECTORY]: {
    from: string;
    to: string;
    accent: string;
  };
  [UpgradeSource.CHAT_HISTORY]: {
    from: string;
    to: string;
    accent: string;
  };
  [UpgradeSource.TRANSCRIPTION]: {
    from: string;
    to: string;
    accent: string;
  };
  [UpgradeSource.MEDALS]: {
    from: string;
    to: string;
    accent: string;
  };
}

// Interface para configuração de contexto
export interface ContextConfig {
  title: string;
  subtitle: string;
  description: string;
  icon: string;
  urgencyMessage?: string;
  ctaText: string;
  gradients: {
    from: string;
    to: string;
    accent: string;
  };
}

// Interface para resposta da API de submissão de lead
export interface SubmitLeadResponse {
  success: boolean;
  leadId?: string;
  planActivated?: boolean;
  courtesyPeriodEnd?: string;
  error?: string;
  message?: string;
}

// Interface para resposta da API de ativação de plano
export interface ActivatePlanResponse {
  success: boolean;
  activationId?: string;
  courtesyEndDate?: string;
  error?: string;
  message?: string;
}

// Interface para tracking de engajamento
export interface EngagementTrackingData {
  leadId: string;
  engagementType: 'login' | 'feature_usage' | 'resource_access' | 'question_submitted' | 'support_contact';
  engagementData?: Record<string, any>;
  timestamp?: Date;
}

// Interface para resposta de tracking de engajamento
export interface TrackEngagementResponse {
  success: boolean;
  riskScore?: number;
  interventionTriggered?: boolean;
  error?: string;
  message?: string;
}

// Tipos utilitários
export type UpgradeSourceKey = keyof typeof UpgradeSource;
export type CommercialLeadStatusKey = keyof typeof CommercialLeadStatus;
export type UpgradeEventTypeKey = keyof typeof UpgradeEventType;

// Interface para validação de formulário
export interface FormValidation {
  isValid: boolean;
  errors: Record<string, string>;
  warnings?: Record<string, string>;
}

// Interface para configuração de período de cortesia
export interface CourtesyPeriodConfig {
  durationDays: number;
  description: string;
  benefits: string[];
  timeline: {
    day: number;
    title: string;
    description: string;
    icon: string;
  }[];
}

export default {
  UpgradeSource,
  CommercialLeadStatus,
  UpgradeEventType
}; 