@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* Ocultar barra de rolagem mantendo a funcionalidade */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE e Edge */
    scrollbar-width: none; /* Firefox */
  }
  
  /* Ocultar barra de rolagem para Chrome, Safari e Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Scrollbar customizada fina */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thumb-orange-200::-webkit-scrollbar-thumb {
    background-color: rgb(254 215 170);
    border-radius: 3px;
  }

  .scrollbar-thumb-orange-200::-webkit-scrollbar-thumb:hover {
    background-color: rgb(253 186 116);
  }

  .scrollbar-track-transparent::-webkit-scrollbar-track {
    background: transparent;
  }

  /* Scrollbar customizada elegante */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #d4af7a #fcf8ed !important;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 10px !important;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #fcf8ed !important;
    border-radius: 8px !important;
    border: 1px solid #e5d4b1 !important;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #d4af7a !important;
    border-radius: 8px !important;
    border: 1px solid #c29f6c !important;
    transition: all 0.3s ease !important;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #c29f6c !important;
    border-color: #b8925e !important;
  }

  .custom-scrollbar::-webkit-scrollbar-corner {
    background: transparent !important;
  }

  /* Dark mode scrollbar */
  .dark .custom-scrollbar {
    scrollbar-color: rgba(215, 180, 140, 0.6) rgba(44, 37, 29, 0.8);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(44, 37, 29, 0.8);
    border-color: rgba(139, 117, 83, 0.3);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(215, 180, 140, 0.7), rgba(194, 159, 108, 0.8));
    border-color: rgba(215, 180, 140, 0.4);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(215, 180, 140, 0.9), rgba(194, 159, 108, 1));
    border-color: rgba(215, 180, 140, 0.6);
  }

  /* Corrigir comportamento de inputs e textareas */
  .text-input-fix {
    text-align: left !important;
    direction: ltr !important;
  }

  .text-input-fix::placeholder {
    text-align: left !important;
    direction: ltr !important;
  }

  .text-input-fix:focus {
    text-align: left !important;
    direction: ltr !important;
  }

  /* Garantir bordas uniformes e consistentes */
  .uniform-border {
    border: 1px solid hsl(var(--border)) !important;
    border-radius: 0.375rem !important;
    box-sizing: border-box !important;
  }

  .uniform-border:focus-visible {
    border: 1px solid hsl(var(--border)) !important;
    outline: 2px solid transparent !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 2px hsl(var(--ring)) !important;
  }

  /* Garantir que todos os Sheets ficam acima da TopBar e outros elementos */
  [data-radix-dialog-content] {
    z-index: 200000 !important;
  }

  /* Garantir que overlay dos Sheets também fica acima da TopBar */
  [data-radix-dialog-overlay] {
    z-index: 199999 !important;
  }

  /* Garantir que Select dropdowns ficam acima dos Sheets */
  [data-radix-select-content] {
    z-index: 300000 !important;
  }

  /* Garantir que outros dropdowns também ficam acima */
  [data-radix-dropdown-menu-content] {
    z-index: 300000 !important;
  }

  /* Garantir que Popover content também fica acima */
  [data-radix-popover-content] {
    z-index: 300000 !important;
  }

  /* Corrigir cursor e interação do calendário */
  .clickable-calendar .rdp-button {
    cursor: pointer !important;
    pointer-events: auto !important;
  }
  
  .clickable-calendar .rdp-day {
    cursor: pointer !important;
    pointer-events: auto !important;
  }
  
  .clickable-calendar .rdp-cell {
    cursor: pointer !important;
    pointer-events: auto !important;
  }
  
  .clickable-calendar button {
    cursor: pointer !important;
    pointer-events: auto !important;
  }

  /* Estilos para listas no editor TipTap */
  .prose ul {
    list-style-type: disc !important;
    list-style-position: outside !important;
    margin-left: 1.5rem !important;
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
    padding-left: 0 !important;
  }

  .prose ol {
    list-style-type: decimal !important;
    list-style-position: outside !important;
    margin-left: 1.5rem !important;
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
    padding-left: 0 !important;
  }

  .prose li {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
    line-height: 1.5 !important;
    list-style: inherit !important;
    list-style-position: inherit !important;
    display: list-item !important;
    margin-left: 0 !important;
    padding-left: 0 !important;
  }

  .prose ul ul {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
    list-style-type: circle !important;
  }

  .prose ol ol {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
    list-style-type: lower-alpha !important;
  }

  /* Garantir que os marcadores apareçam - fallback */
  .prose ul > li {
    position: relative;
  }

  .prose ul > li::before {
    content: "•";
    position: absolute;
    left: -1rem;
    font-weight: bold;
    color: currentColor;
  }

  .prose ol > li {
    position: relative;
    counter-increment: list-item;
  }

  /* Resetar counter no início da lista */
  .prose ol {
    counter-reset: list-item;
  }

  /* Estilos para listas aninhadas - melhor hierarquia visual */
  .prose ul li ul {
    list-style-type: circle !important;
  }

  .prose ul li ul li ul {
    list-style-type: square !important;
  }

  .prose ol li ol {
    list-style-type: lower-alpha !important;
  }

  .prose ol li ol li ol {
    list-style-type: lower-roman !important;
  }

  /* Forçar visibilidade em todos os contextos */
  .prose ul, .prose ol {
    overflow: visible !important;
  }

  .prose li {
    overflow: visible !important;
  }
}

@layer base {
  :root {
    /* Variáveis de safe area para dispositivos móveis */
    --safe-area-top: 0px;
    --safe-area-bottom: 0px;
    --safe-area-left: 0px;
    --safe-area-right: 0px;
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 24 95% 53%;
    --primary-foreground: 60 100% 99%;

    --secondary: 60 100% 90%;
    --secondary-foreground: 24 95% 53%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 222 18% 7%; /* Padronizado com TopBar (#0b0e14) */
    --sidebar-foreground: 60 100% 99%;
    --sidebar-primary: 24 95% 53%;
    --sidebar-primary-foreground: 60 100% 99%;
    --sidebar-accent: 60 100% 90%;
    --sidebar-accent-foreground: 24 95% 53%;
    --sidebar-border: 222 18% 10%; /* Escurecido para combinar */
    --sidebar-ring: 24 95% 53%;
  }

  .dark {
    --background: 222 18% 14%;
    --foreground: 60 100% 99%;

    --card: 222 18% 14%;
    --card-foreground: 60 100% 99%;

    --popover: 222 18% 14%;
    --popover-foreground: 60 100% 99%;

    --primary: 24 95% 53%;
    --primary-foreground: 60 100% 99%;

    --secondary: 60 100% 90%;
    --secondary-foreground: 24 95% 53%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 60 100% 99%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 60 100% 99%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 24 95% 53%;

    --sidebar-background: 222 18% 7%; /* Padronizado com TopBar (#0b0e14) */
    --sidebar-foreground: 60 100% 99%;
    --sidebar-primary: 24 95% 53%;
    --sidebar-primary-foreground: 60 100% 99%;
    --sidebar-accent: 60 100% 90%;
    --sidebar-accent-foreground: 24 95% 53%;
    --sidebar-border: 222 18% 10%; /* Escurecido para combinar */
    --sidebar-ring: 24 95% 53%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}