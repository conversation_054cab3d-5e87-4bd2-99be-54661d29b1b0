/**
 * Notification<PERSON><PERSON><PERSON> - Handler especializado para processamento de notificações
 * 
 * Reutiliza toda a lógica existente do RealtimeNotificationsContext mas de forma modular
 * 
 * <AUTHOR> Internet 2025
 */
import { QueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Medal } from '@/hooks/use-medals';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { showAchievementToast, showXpToast, showMedalToast, showLevelUpToast, showEventToast, showPromotionToast, showTaskToast } from '@/components/ui/sonner';
import { QueryKeys } from '@/lib/query/queryKeys';
import { playSound, SoundEffects } from '@/lib/sound-effects';

// Tipos reutilizados do sistema existente
interface NotificationMetadata {
  medal_id?: string;
  achieved_at?: string;
  level?: number;
  xp_amount?: number;
  action_name?: string;
  event_title?: string;
  participant_name?: string;
  status_emoji?: string;
  new_status?: string;
  promotion_id?: string;
  old_job_title?: string;
  new_job_title?: string;
  hr_manager_name?: string;
  effective_date?: string;
  promotion_emoji?: string;
  action_type?: string;
  card_id?: string;
  sender_id?: string;
  sender_name?: string;
  sender_avatar?: string;
  message?: string;
  background_type?: string;
  background_config?: Record<string, any>;
  media_type?: string;
  media_url?: string;
  media_duration?: number;
  card_type?: string;
  task_id?: string;
  task_title?: string;
  from_user_name?: string;
  request_id?: string;
  priority?: string;
  [key: string]: unknown;
}

interface NotificationPayload {
  new: {
    id: string;
    user_id: string;
    type: string;
    title: string | null;
    content: string;
    read: boolean | null;
    reference_id: string | null;
    company_id: string | null;
    created_at: string;
    metadata: NotificationMetadata | null;
  };
  old: Record<string, unknown> | null;
  schema: string;
  table: string;
  type: "INSERT" | "UPDATE" | "DELETE";
  commit_timestamp: string;
}

export class NotificationHandler {
  private queryClient: QueryClient;
  private achievedMedal: Medal | null = null;
  private shouldShowNotificationFlag: boolean = true;
  private profile: any;
  private setAchievedMedal: any;
  private setBirthdayCardData: any;
  
  constructor(queryClient: QueryClient, profile?: any, setAchievedMedal?: any, setBirthdayCardData?: any) {
    this.queryClient = queryClient;
    this.profile = profile;
    this.setAchievedMedal = setAchievedMedal;
    this.setBirthdayCardData = setBirthdayCardData;
  }
  
  // Helper para verificar preferências de notificação (baseado no padrão de NotificationPreferences.tsx)
  private async checkNotificationPreference(activityType: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return true; // Default para true se não conseguir autenticar
      
      const { data, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', user.id);
      
      if (error) {
        logQueryEvent('NotificationHandler', 'Erro ao carregar preferências', { error, activityType }, 'error');
        return true; // Default para true em caso de erro
      }
      
      // Buscar preferência específica (igual ao padrão do NotificationPreferences.tsx)
      const preference = (data || []).find(p => p.activity_type === activityType);
      const isEnabled = preference?.enabled ?? true; // padrão true se não configurado
      
      logQueryEvent('NotificationHandler', `🔔 Preferência ${activityType}:`, { isEnabled, preference });
      return isEnabled;
    } catch (error) {
      logQueryEvent('NotificationHandler', 'Erro ao verificar preferência de notificação', { error, activityType }, 'error');
      return true; // Default para true em caso de erro
    }
  }
  
  async process(payload: Record<string, unknown>) {
    logQueryEvent('NotificationHandler', '📢 Processando notificação', { 
      type: payload.new?.type,
      userId: payload.new?.user_id,
      metadata: payload.new?.metadata,
      isMention: payload.new?.type === 'mention',
      hasBirthdayCard: payload.new?.metadata?.card_type === 'birthday_card'
    });
    
    try {
      const notificationPayload = payload as unknown as NotificationPayload;
      const notificationType = notificationPayload.new.type;
      
      // ✅ CORREÇÃO: Invalidar cache da timeline para todas as notificações
      // Substitui a funcionalidade do useTimelineNotificationsRealtime desabilitado
      this.queryClient.invalidateQueries({ queryKey: ['timeline', 'notifications'] });
      
      // Processar cada tipo de notificação usando lógica existente
      switch (notificationType) {
        case 'medal_earned':
          logQueryEvent('NotificationHandler', '🏆 DEBUG: Processando medalha');
          await this.handleMedalEarned(notificationPayload);
          break;
          
        case 'level_up':
          await this.handleLevelUp(notificationPayload);
          break;
          
        case 'xp_gain':
          await this.handleXpGain(notificationPayload);
          break;
          
        case 'event_participation_update':
          await this.handleEventParticipationUpdate(notificationPayload);
          break;
          
        case 'event_invitation':
        case 'event_reminder':
        case 'event_cancelled':
        case 'event_updated':
          await this.handleEventNotifications(notificationPayload);
          break;
          
        case 'mention':
          
          if (notificationPayload.new.metadata?.card_type === 'birthday_card') {
            await this.handleBirthdayCard(notificationPayload);
          } else {
          }
          break;
          
        case 'promotion_created':
        case 'promotion_effectuated':
        case 'promotion_notification_sent':
        case 'promotion_public_congratulations':
          await this.handlePromotionNotifications(notificationPayload);
          break;
          
        case 'privacy_request':
          await this.handlePrivacyRequest(notificationPayload);
          break;
          
        case 'task':
          await this.handleTaskNotifications(notificationPayload);
          break;
          
        case 'mission_progress':
        case 'mission_completed':
        case 'new_mission':
          await this.handleMissionNotifications(notificationPayload);
          break;
          
        default:
          logQueryEvent('NotificationHandler', 'Tipo de notificação não reconhecido', { 
            type: notificationType,
            payload: notificationPayload 
          }, 'warn');
      }
      
      // Invalidar cache de notificações
      this.queryClient.invalidateQueries({ queryKey: ['notifications'] });
      
    } catch (error) {
      logQueryEvent('NotificationHandler', 'Erro ao processar notificação', { 
        error: error instanceof Error ? error.message : error,
        payload 
      }, 'error');
    }
  }
  
  private async handleMedalEarned(payload: NotificationPayload) {
    logQueryEvent('NotificationHandler', '🏅 Processando medalha conquistada');
    
    // ✨ VERIFICAÇÃO DE PREFERÊNCIAS - Verificar se notificações de medalha estão habilitadas
    const isEnabled = await this.checkNotificationPreference('medal_achieved');
    if (!isEnabled) {
      logQueryEvent('NotificationHandler', '🚫 Notificação de medalha desabilitada pelo usuário - ignorando');
      return;
    }
    
    const medalId = payload.new.metadata?.medal_id;
    if (!medalId) {
      logQueryEvent('NotificationHandler', 'medal_earned sem medal_id nos metadados', payload.new.metadata, 'error');
      return;
    }
    
    // Buscar dados da medalha (reutilizar lógica existente)
    const { data: medal, error } = await supabase
      .from('medals')
      .select('*')
      .eq('id', medalId)
      .maybeSingle();
    
    if (error) {
      logQueryEvent('NotificationHandler', 'Erro ao buscar medalha', { medalId, error }, 'error');
      return;
    }
    
    if (!medal) {
      logQueryEvent('NotificationHandler', 'Medalha não encontrada', { medalId }, 'error');
      return;
    }
    
    logQueryEvent('NotificationHandler', '✅ Notificação de medalha habilitada - exibindo medalha');
    
    // Criar objeto Medal
    const achievedMedalData: Medal = {
      id: medal.id,
      name: medal.name,
      description: medal.description || '',
      type: medal.type as 'communication' | 'engagement' | 'documentation' | 'attendance' | 'collaboration',
      icon_url: medal.icon_url,
      required_count: medal.required_count,
      progress: 1,
      achieved_at: payload.new.metadata?.achieved_at || new Date().toISOString(),
    };
    
    // Armazenar medalha para exibição
    this.achievedMedal = achievedMedalData;
    
    // 🏅 FIXAR ANIMAÇÃO DE MEDALHA - Disparar evento reativo como o GamificationHandler
    
    // Disparar evento para notificar mudança de estado reativo (igual ao GamificationHandler)
    const stateChangeEvent = new CustomEvent('vindula-achieved-medal-changed', { 
      detail: { 
        achievedMedal: achievedMedalData,
        autoOpen: true // Medalhas de notificação sempre abrem automaticamente
      }
    });
    window.dispatchEvent(stateChangeEvent);
    
    // Disparar evento para componentes que escutam (manter compatibilidade)
    const modalEvent = new CustomEvent('vindula-medal-modal', { detail: achievedMedalData });
    window.dispatchEvent(modalEvent);
    
    
    // Exibir toast de medalha
    showMedalToast(achievedMedalData.name, achievedMedalData.description);
    
    // Tocar som
    playSound(SoundEffects.ACHIEVEMENT);
    
    // Invalidar queries relacionadas
    this.queryClient.invalidateQueries({ queryKey: QueryKeys.gamification.medals() });
    this.queryClient.invalidateQueries({ queryKey: QueryKeys.gamification.userLevel() });
    
  }
  
  private async handleLevelUp(payload: NotificationPayload) {
    logQueryEvent('NotificationHandler', '🚀 Processando level up');
    
    // ✨ VERIFICAÇÃO DE PREFERÊNCIAS - Verificar se notificações de level-up estão habilitadas
    const isEnabled = await this.checkNotificationPreference('level_up');
    if (!isEnabled) {
      logQueryEvent('NotificationHandler', '🚫 Notificação de level-up desabilitada pelo usuário - ignorando');
      return;
    }
    
    const level = payload.new.metadata?.level;
    if (typeof level !== 'number') {
      logQueryEvent('NotificationHandler', 'level_up sem level válido', payload.new.metadata, 'warn');
      return;
    }
    
    logQueryEvent('NotificationHandler', '✅ Notificação de level-up habilitada - exibindo level-up');
    
    // Exibir toast de level up
    showLevelUpToast(level);
    
    // Tocar som
    playSound(SoundEffects.ACHIEVEMENT);
    
    // Verificar se há medalha associada ao level up
    const medalId = payload.new.metadata?.medal_id;
    if (medalId) {
      await this.handleMedalEarned(payload);
    }
    
    // Invalidar queries relacionadas
    this.queryClient.invalidateQueries({ queryKey: QueryKeys.gamification.userLevel() });
    
    logQueryEvent('NotificationHandler', '🚀 Level up processado', { level, medalId });
  }
  
  private async handleXpGain(payload: NotificationPayload) {
    logQueryEvent('NotificationHandler', '⭐ Processando ganho de XP');
    
    const xpAmount = payload.new.metadata?.xp_amount;
    const actionName = payload.new.metadata?.action_name || 'xp_gain';
    
    if (typeof xpAmount !== 'number' || isNaN(xpAmount)) {
      logQueryEvent('NotificationHandler', 'xp_amount inválido', { xpAmount }, 'warn');
      return;
    }
    
    if (!this.shouldShowNotificationFlag) return;
    
    // Exibir toast de XP
    showXpToast(xpAmount, actionName as string);
    
    // Tocar som
    playSound(SoundEffects.XP_GAIN);
    
    logQueryEvent('NotificationHandler', '⭐ XP processado', { xpAmount, actionName });
  }
  
  private async handleEventParticipationUpdate(payload: NotificationPayload) {
    logQueryEvent('NotificationHandler', '📅 Processando atualização de participação em evento');
    
    const metadata = payload.new.metadata;
    const eventTitle = metadata?.event_title;
    const participantName = metadata?.participant_name;
    const statusEmoji = metadata?.status_emoji;
    const newStatus = metadata?.new_status;
    
    if (eventTitle && participantName && statusEmoji) {
      let statusMessage = '';
      
      switch (newStatus) {
        case 'confirmed':
          statusMessage = 'confirmou presença';
          break;
        case 'declined':
          statusMessage = 'recusou o convite';
          break;
        case 'maybe':
          statusMessage = 'marcou como "talvez"';
          break;
        default:
          statusMessage = 'atualizou sua participação';
      }
      
      const toastTitle = `${statusEmoji} Evento: ${eventTitle}`;
      const toastDescription = `${participantName} ${statusMessage} no seu evento.`;
      
      showEventToast(
        toastTitle,
        toastDescription,
        newStatus === 'confirmed' ? 'confirmed' : 
        newStatus === 'declined' ? 'declined' : 
        newStatus === 'maybe' ? 'maybe' : 'updated'
      );
      
      playSound(SoundEffects.NEW_MESSAGE);
    }
  }
  
  private async handleEventNotifications(payload: NotificationPayload) {
    logQueryEvent('NotificationHandler', '📅 Processando notificação de evento');
    
    const metadata = payload.new.metadata;
    const title = payload.new.title;
    const content = payload.new.content;
    
    if (title && content) {
      let status: 'confirmed' | 'declined' | 'maybe' | 'updated' = 'updated';
      
      switch (payload.new.type) {
        case 'event_invitation':
          status = 'confirmed';
          break;
        case 'event_reminder':
          status = 'maybe';
          break;
        case 'event_cancelled':
          status = 'declined';
          break;
        case 'event_updated':
          status = 'updated';
          break;
      }
      
      showEventToast(title, content, status);
      playSound(SoundEffects.NEW_MESSAGE);
    }
  }
  
  private async handleBirthdayCard(payload: NotificationPayload) {
    
    const metadata = payload.new.metadata;
    
    if (!metadata || !metadata.card_id) {
      logQueryEvent('NotificationHandler', 'Cartão de aniversário sem dados necessários', metadata, 'error');
      return;
    }
    
    // Criar objeto de dados do cartão (igual ao sistema antigo)
    const cardData = {
      id: metadata.card_id as string,
      sender_id: metadata.sender_id as string,
      sender_name: metadata.sender_name as string,
      sender_avatar: metadata.sender_avatar as string,
      message: metadata.message as string,
      background_type: (metadata.background_type as 'gradient' | 'color' | 'image') || 'gradient',
      background_config: metadata.background_config as Record<string, any> || {},
      media_type: metadata.media_type as 'video' | 'audio' | undefined,
      media_url: metadata.media_url as string | undefined,
      media_duration: metadata.media_duration as number | undefined,
      created_at: payload.new.created_at,
      auto_open: true // ✅ NOVO: Flag para abertura automática
    };
    
    // Disparar evento para notificar mudança de estado reativo (seguindo padrão das medalhas)
    const stateChangeEvent = new CustomEvent('vindula-birthday-card-changed', { 
      detail: { birthdayCardData: cardData }
    });
    window.dispatchEvent(stateChangeEvent);
    
    // ✅ NOVO: Disparar evento específico para abertura automática
    const autoOpenEvent = new CustomEvent('vindula-birthday-card-auto-open', { 
      detail: { cardData }
    });
    window.dispatchEvent(autoOpenEvent);
    
    
    // Tocar som
    playSound(SoundEffects.ACHIEVEMENT, 0.4);
  }
  
  private async handlePromotionNotifications(payload: NotificationPayload) {
    logQueryEvent('NotificationHandler', '🎉 Processando notificação de promoção');
    
    const metadata = payload.new.metadata;
    const title = payload.new.title;
    const content = payload.new.content;
    
    if (title && content) {
      let promotionActionType: 'created' | 'effectuated' | 'notification_sent' | 'public_congratulations' = 'created';
      
      switch (payload.new.type) {
        case 'promotion_created':
          promotionActionType = 'created';
          break;
        case 'promotion_effectuated':
          promotionActionType = 'effectuated';
          break;
        case 'promotion_notification_sent':
          promotionActionType = 'notification_sent';
          break;
        case 'promotion_public_congratulations':
          promotionActionType = 'public_congratulations';
          break;
      }
      
      // Para criação e efetivação, não notificar (ainda é surpresa)
      if (payload.new.type === 'promotion_created' || payload.new.type === 'promotion_effectuated') {
        return;
      }
      
      // Para NOTIFICAÇÃO OFICIAL de promoção, mostrar celebração especial
      if (payload.new.type === 'promotion_notification_sent' && metadata) {
        logQueryEvent('NotificationHandler', 'Notificação oficial de promoção detectada - preparando celebração especial', { metadata });
        
        // Buscar dados do usuário para completar as informações da celebração
        const { data: userProfile } = await supabase
          .from('profiles')
          .select('full_name, avatar_url, email')
          .eq('id', payload.new.user_id)
          .single();
        
        if (userProfile) {
          const celebrationData = {
            id: metadata.promotion_id as string || 'unknown',
            userFullName: userProfile.full_name || 'Usuário',
            userAvatarUrl: userProfile.avatar_url || undefined,
            userEmail: userProfile.email || '',
            oldJobTitle: metadata.old_job_title as string || 'Cargo Anterior',
            newJobTitle: metadata.new_job_title as string || 'Novo Cargo',
            promotionDate: new Date().toISOString().split('T')[0],
            hrManagerName: metadata.hr_manager_name as string || 'RH',
            customMessage: content || undefined,
            effectiveDate: metadata.effective_date as string || undefined,
            companyName: 'Vindula Cosmos'
          };
          
          // Disparar evento para mostrar celebração especial
          const stateChangeEvent = new CustomEvent('vindula-promotion-celebration-changed', { 
            detail: { promotionData: celebrationData }
          });
          window.dispatchEvent(stateChangeEvent);
          
          logQueryEvent('NotificationHandler', 'Evento de celebração de promoção disparado', { promotionId: celebrationData.id });
        } else {
          // Fallback para toast normal se não conseguir buscar dados do usuário
          showPromotionToast(title, content, promotionActionType);
        }
      } else {
        // Para outros tipos de promoção, apenas toast
        showPromotionToast(title, content, promotionActionType);
      }
      
      playSound(SoundEffects.ACHIEVEMENT);
    }
  }
  
  private async handlePrivacyRequest(payload: NotificationPayload) {
    logQueryEvent('NotificationHandler', '🔒 Processando solicitação de privacidade');
    
    const metadata = payload.new.metadata;
    const title = payload.new.title;
    const content = payload.new.content;
    const newStatus = metadata?.new_status;
    
    if (title && content) {
      // Implementar toast específico para privacy request
      showAchievementToast(title, content, 'achievement');
      
      if (newStatus === 'completed') {
        playSound(SoundEffects.ACHIEVEMENT);
      } else if (newStatus === 'rejected') {
        playSound(SoundEffects.ERROR);
      } else {
        playSound(SoundEffects.NEW_MESSAGE);
      }
    }
  }
  
  private async handleTaskNotifications(payload: NotificationPayload) {
    logQueryEvent('NotificationHandler', '📋 Processando notificação de tarefa');
    
    const metadata = payload.new.metadata;
    const title = payload.new.title;
    const content = payload.new.content;
    const actionType = metadata?.action_type;
    
    if (title && content) {
      let taskType: 'request' | 'accepted' | 'declined' | 'negotiation' = 'request';
      
      switch (actionType) {
        case 'task_request':
          taskType = 'request';
          break;
        case 'task_accepted':
          taskType = 'accepted';
          break;
        case 'task_declined':
          taskType = 'declined';
          break;
        case 'task_negotiation':
          taskType = 'negotiation';
          break;
        default:
          taskType = 'request';
      }
      
      const handleNavigate = () => {
        const tasksUrl = '/tasks';
        const targetUrl = actionType === 'task_request' || actionType === 'task_message'
          ? `${tasksUrl}?tab=received`
          : `${tasksUrl}?tab=responses`;
        window.location.href = targetUrl;
      };
      
      showTaskToast(title, content, taskType, handleNavigate);
      playSound(SoundEffects.NEW_MESSAGE);
      
      // Invalidar queries relacionadas às tarefas
      this.queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.assignmentRequests() });
      this.queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.receivedRequests() });
      this.queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.myRequests() });
    }
  }
  
  private async handleMissionNotifications(payload: NotificationPayload) {
    logQueryEvent('NotificationHandler', '🎯 Processando notificação de missão');
    
    const metadata = payload.new.metadata;
    const title = payload.new.title;
    const content = payload.new.content;
    
    if (title && metadata) {
      // Processar missões (implementar lógica específica se necessário)
      if (payload.new.type === 'mission_completed') {
        playSound(SoundEffects.ACHIEVEMENT, 0.8);
      } else {
        playSound(SoundEffects.NEW_MESSAGE, 0.4);
      }
      
      // Invalidar queries relacionadas a missões
      this.queryClient.invalidateQueries({ queryKey: QueryKeys.gamification.userProgress(metadata.user_id) });
    }
  }
  
  // Métodos públicos para compatibilidade
  async markAsRead(notificationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);
      
      if (error) throw error;
      
      logQueryEvent('NotificationHandler', 'Notificação marcada como lida', { notificationId });
    } catch (error) {
      logQueryEvent('NotificationHandler', 'Erro ao marcar como lida', { 
        notificationId, 
        error: error instanceof Error ? error.message : error 
      }, 'error');
    }
  }
  
  clearAchievedMedal(): void {
    this.achievedMedal = null;
    logQueryEvent('NotificationHandler', 'Medalha conquistada limpa');
  }
  
  showXpGained(amount: number, actionType: string): void {
    if (!this.shouldShowNotificationFlag) return;
    
    showXpToast(amount, actionType);
    playSound(SoundEffects.XP_GAIN);
  }
  
  showNextNotification(): void {
    // Implementar lógica de fila se necessário
    logQueryEvent('NotificationHandler', 'Próxima notificação solicitada');
  }
  
  get getAchievedMedal(): Medal | null {
    return this.achievedMedal;
  }
  
  updateAchievedMedal(medal: Medal | null) {
    this.achievedMedal = medal;
  }
}