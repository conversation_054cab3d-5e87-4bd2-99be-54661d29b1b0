/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> - Handler especializado para processamento de mensagens de chat
 * 
 * Consolida lógica de chat do useChatRealtime e FloatingChat
 * 
 * <AUTHOR> Internet 2025
 */
import { QueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { toast } from 'sonner';
import { playSound, SoundEffects } from '@/lib/sound-effects';

interface ChatMessage {
  id: string;
  chat_id?: string;
  channel_id?: string;
  sender_id: string;
  content: string;
  created_at: string;
}

interface MessageReaction {
  id: string;
  message_id: string;
  user_id: string;
  emoji: string;
  created_at: string;
}

interface MessageReadReceipt {
  id: string;
  message_id: string;
  user_id: string;
  read_at: string;
}

interface Channel {
  id: string;
  name: string;
  description?: string;
  company_id: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  is_public: boolean;
  is_archived: boolean;
}

interface ChannelMember {
  id: string;
  channel_id: string;
  user_id: string;
  joined_at: string;
  role: 'member' | 'admin' | 'owner';
}

interface TypingStatus {
  id: string;
  user_id: string;
  chat_id?: string;
  channel_id?: string;
  last_typed_at: string;
}

export class ChatHandler {
  private queryClient: QueryClient;
  
  constructor(queryClient: QueryClient, profile?: any) {
    this.queryClient = queryClient;
  }
  
  async processMessage(payload: Record<string, unknown>) {
    logQueryEvent('ChatHandler', '💬 Processando mensagem de chat', { 
      chatId: payload.new?.chat_id,
      channelId: payload.new?.channel_id,
      senderId: payload.new?.sender_id 
    });
    
    try {
      const message = payload.new as ChatMessage;
      
      // Verificar se a mensagem é para o usuário atual
      const shouldNotify = await this.shouldShowNotification(message);
      
      if (shouldNotify) {
        await this.showChatNotification(message);
      }
      
      // Atualizar cache de mensagens
      await this.updateMessageCache(message);
      
      // Invalidar queries relacionadas
      this.invalidateQueries(message);
      
      // Disparar eventos customizados para compatibilidade
      this.dispatchMessageEvent(payload, message);
      
    } catch (error) {
      logQueryEvent('ChatHandler', 'Erro ao processar mensagem', { 
        error: error instanceof Error ? error.message : error,
        payload 
      }, 'error');
    }
  }

  async processMessageUpdate(payload: Record<string, unknown>) {
    logQueryEvent('ChatHandler', '✏️ Processando atualização de mensagem (edição)', { 
      messageId: payload.new?.id,
      chatId: payload.new?.chat_id,
      channelId: payload.new?.channel_id,
      edited: payload.new?.edited
    });
    
    try {
      // Buscar mensagem completa com dados relacionados (igual ao useChatRealtime.ts)
      const { data, error } = await supabase
        .from("chat_messages")
        .select(`
          id,
          chat_id,
          channel_id,
          content,
          parent_id,
          metadata,
          thread_participants,
          created_at,
          updated_at,
          edited,
          edited_at,
          original_content,
          edit_count,
          message_type,
          attachment_url,
          image_base64,
          audio_duration_seconds,
          video_duration_seconds,
          sender:profiles!chat_messages_sender_id_fkey(
            id,
            full_name,
            avatar_url
          ),
          reactions:message_reactions(
            id,
            emoji,
            user_id,
            user:profiles!message_reactions_user_id_fkey(
              id,
              full_name,
              avatar_url
            )
          )
        `)
        .eq("id", payload.new?.id)
        .single();

      if (error) {
        logQueryEvent('ChatHandler', 'Erro ao buscar mensagem atualizada', { 
          error, 
          messageId: payload.new?.id 
        }, 'error');
        return;
      }

      // Atualizar cache usando setQueryData (igual ao useChatRealtime.ts)
      const message = data as ChatMessage;
      await this.updateEditedMessageCache(message);
      
      logQueryEvent('ChatHandler', '✅ Mensagem editada processada com sucesso', { 
        messageId: message.id,
        chatId: message.chat_id,
        channelId: message.channel_id,
        edited: message.edited
      });
      
    } catch (error) {
      logQueryEvent('ChatHandler', 'Erro ao processar atualização de mensagem', { 
        error: error instanceof Error ? error.message : error,
        payload 
      }, 'error');
    }
  }
  
  async processMessageReaction(payload: Record<string, unknown>) {
    logQueryEvent('ChatHandler', '😊 Processando reação em mensagem');
    
    try {
      const reaction = payload.new as MessageReaction;
      
      // Invalidar cache de reações
      this.queryClient.invalidateQueries({ queryKey: ['message-reactions', reaction.message_id] });
      this.queryClient.invalidateQueries({ queryKey: ['chat-messages'] });
      
      logQueryEvent('ChatHandler', 'Reação processada', { reaction });
      
    } catch (error) {
      logQueryEvent('ChatHandler', 'Erro ao processar reação', { error }, 'error');
    }
  }
  
  async processReadReceipt(payload: Record<string, unknown>) {
    logQueryEvent('ChatHandler', '✓ Processando confirmação de leitura via realtime');
    
    try {
      const receipt = payload.new as MessageReadReceipt;
      const messageId = receipt.message_id;
      
      if (!messageId) {
        logQueryEvent('ChatHandler', 'Read receipt sem message_id', { receipt }, 'warning');
        return;
      }
      
      // ✅ CORREÇÃO: Invalidação específica por canal
      try {
        // Buscar channelId da mensagem para invalidação específica
        const { data: channelId, error } = await supabase
          .rpc('get_channel_id_from_message', { p_message_id: messageId });
        
        if (error) {
          logQueryEvent('ChatHandler', 'Erro ao buscar channelId', { error, messageId }, 'error');
        } else if (channelId) {
          // Invalidar contador específico do canal
          this.queryClient.invalidateQueries({ 
            queryKey: ['chat', 'unread-count', channelId] 
          });
          
          logQueryEvent('ChatHandler', 'Cache de canal específico invalidado', { 
            messageId, 
            channelId,
            queryKey: ['chat', 'unread-count', channelId]
          });
        }
      } catch (error) {
        logQueryEvent('ChatHandler', 'Erro na invalidação específica, usando fallback', { error }, 'error');
        
        // Fallback: invalidação ampla
        this.queryClient.invalidateQueries({ queryKey: ['chat', 'unread-count'] });
      }
      
      // Invalidar read receipts da mensagem específica
      this.queryClient.invalidateQueries({ 
        queryKey: ['chat', 'read-receipts', messageId] 
      });
      
      // Invalidar contador global (para badge geral)
      this.queryClient.invalidateQueries({ queryKey: ['unread-chat-count'] });
      
      logQueryEvent('ChatHandler', '✅ Read receipt processado com sucesso', { 
        messageId,
        userId: receipt.user_id,
        readAt: receipt.read_at
      });
      
    } catch (error) {
      logQueryEvent('ChatHandler', 'Erro ao processar confirmação de leitura', { 
        error: error instanceof Error ? error.message : error,
        payload 
      }, 'error');
    }
  }
  
  private async shouldShowNotification(message: ChatMessage): Promise<boolean> {
    // Obter ID do usuário atual
    const currentUser = await supabase.auth.getUser();
    const currentUserId = currentUser.data.user?.id;
    
    if (!currentUserId) {
      return false;
    }
    
    // CORREÇÃO DO BUG: Nunca notificar sobre suas próprias mensagens
    if (message.sender_id === currentUserId) {
      return false;
    }
    
    // Verificar se a mensagem é para o usuário atual
    let shouldNotify = false;
    
    if (message.chat_id) {
      // Chat 1-1: verificar se sou participante
      const { data: isParticipant } = await supabase
        .from('chat_participants')
        .select('chat_id')
        .eq('chat_id', message.chat_id)
        .eq('user_id', currentUserId)
        .maybeSingle();
      
      shouldNotify = !!isParticipant;
    } else if (message.channel_id) {
      // Canal: verificar se sou membro
      const { data: isMember } = await supabase
        .from('channel_members')
        .select('channel_id')
        .eq('channel_id', message.channel_id)
        .eq('user_id', currentUserId)
        .maybeSingle();
      
      shouldNotify = !!isMember;
    }
    
    return shouldNotify;
  }
  
  private async showChatNotification(message: ChatMessage) {
    try {
      // Buscar dados do remetente
      const { data: sender } = await supabase
        .from('profiles')
        .select('full_name, avatar_url')
        .eq('id', message.sender_id)
        .single();
      
      const senderName = sender?.full_name || 'Alguém';
      const senderAvatar = sender?.avatar_url;
      const messagePreview = message.content?.substring(0, 50) || 'Nova mensagem';
      
      // Mostrar toast simples (sem JSX customizado para evitar problemas de tipo)
      toast(`💬 Nova mensagem de ${senderName}`, {
        description: messagePreview,
        duration: 5000,
        action: {
          label: "Ver",
          onClick: () => {
            const chatUrl = message.chat_id 
              ? `/chat/${message.chat_id}`
              : `/chat/channel/${message.channel_id}`;
            window.location.href = chatUrl;
          }
        }
      });
      
      // Tocar som
      playSound(SoundEffects.NEW_MESSAGE, 0.6);
      
      logQueryEvent('ChatHandler', 'Notificação de chat exibida', { 
        senderName,
        messagePreview: messagePreview.substring(0, 20) 
      });
      
    } catch (error) {
      logQueryEvent('ChatHandler', 'Erro ao exibir notificação de chat', { error }, 'error');
    }
  }
  
  private async updateMessageCache(message: ChatMessage) {
    // Atualizar cache de mensagens
    const cacheKey = message.chat_id 
      ? ['chat-messages', message.chat_id]
      : ['channel-messages', message.channel_id];
    
    this.queryClient.setQueryData(cacheKey, (old: any) => {
      if (!old) return [message];
      return [...old, message];
    });
    
    logQueryEvent('ChatHandler', 'Cache de mensagens atualizado', { 
      cacheKey,
      messageId: message.id 
    });
  }

  private async updateEditedMessageCache(message: ChatMessage) {
    // Atualizar cache para mensagem editada - usar as mesmas chaves do useChatMessages.ts
    const queries = [
      // Query keys do useChatMessages.ts
      message.chat_id ? ['chatMessages', 'chat', message.chat_id] : null,
      message.channel_id ? ['chatMessages', 'channel', message.channel_id] : null,
      // Query keys do useChatMessagesPaginated.ts
      message.chat_id ? ['chatMessagesPaginated', 'chat', message.chat_id] : null,
      message.channel_id ? ['chatMessagesPaginated', 'channel', message.channel_id] : null,
    ].filter(Boolean);

    let cacheUpdated = false;

    queries.forEach(queryKey => {
      const currentData = this.queryClient.getQueryData(queryKey as any);
      
      // Se não tem dados no cache, usar invalidateQueries como fallback
      if (!currentData || !(currentData as any)?.messages || !Array.isArray((currentData as any).messages)) {
        logQueryEvent('ChatHandler', '🔄 Cache vazio, usando invalidateQueries como fallback', { 
          queryKey,
          messageId: message.id
        });
        this.queryClient.invalidateQueries({ queryKey: queryKey as any });
        return;
      }

      // Cache existe, atualizar usando setQueryData
      this.queryClient.setQueryData(
        queryKey,
        (oldData: { messages: ChatMessage[]; hasMoreMessages: boolean } | undefined) => {
          if (!oldData || !oldData.messages || !Array.isArray(oldData.messages)) {
            return oldData;
          }
          
          const updatedMessages = oldData.messages.map(msg => {
            if (msg.id === message.id) {
              return message; // Substituir com dados atualizados
            }
            return msg;
          });
          
          cacheUpdated = true;
          return {
            ...oldData,
            messages: updatedMessages,
          };
        }
      );
    });
    
    logQueryEvent('ChatHandler', '✏️ Cache de mensagem editada processado', { 
      messageId: message.id,
      chatId: message.chat_id,
      channelId: message.channel_id,
      queriesTotal: queries.length,
      cacheUpdated,
      method: cacheUpdated ? 'setQueryData' : 'invalidateQueries fallback'
    });
  }
  
  private invalidateQueries(message: ChatMessage) {
    // Invalidar contagem de mensagens não lidas GLOBAL
    this.queryClient.invalidateQueries({ queryKey: ['unread-chat-count'] });
    
    // ✅ CORREÇÃO CRÍTICA: Invalidar contador específico do canal quando nova mensagem é enviada
    if (message.channel_id) {
      // Invalidar contador de não lidas específico do canal
      this.queryClient.invalidateQueries({ 
        queryKey: ['chat', 'unread-count', message.channel_id] 
      });
      
      // ✅ NOVA CORREÇÃO: Invalidar última mensagem do canal para atualizar ChannelList em tempo real
      this.queryClient.invalidateQueries({ 
        queryKey: ['chat', 'last-message', message.channel_id] 
      });
      
      // Invalidar mensagens do canal
      this.queryClient.invalidateQueries({ 
        queryKey: ['channel-messages', message.channel_id] 
      });
      
      logQueryEvent('ChatHandler', '✅ Cache do canal invalidado (contador + última mensagem)', { 
        channelId: message.channel_id,
        unreadCountKey: ['chat', 'unread-count', message.channel_id],
        lastMessageKey: ['chat', 'last-message', message.channel_id]
      });
    } else if (message.chat_id) {
      // Para chats 1-1, invalidar mensagens
      this.queryClient.invalidateQueries({ 
        queryKey: ['chat-messages', message.chat_id] 
      });
    }
    
    // Invalidar lista de chats (para atualizar última mensagem)
    this.queryClient.invalidateQueries({ queryKey: ['user-chats'] });
    this.queryClient.invalidateQueries({ queryKey: ['user-channels'] });
    
    logQueryEvent('ChatHandler', 'Queries de chat invalidadas após nova mensagem', { 
      chatId: message.chat_id,
      channelId: message.channel_id,
      invalidatedCounterFor: message.channel_id ? `canal ${message.channel_id}` : `chat ${message.chat_id}`
    });
  }
  
  // Métodos públicos para integração com FloatingChats
  async shouldShowInFloatingChat(message: ChatMessage): Promise<boolean> {
    // Verificar se o chat está aberto no FloatingChat
    // (esta lógica pode ser expandida conforme necessário)
    return message.chat_id !== undefined;
  }
  
  async addMessageToFloatingChat(message: ChatMessage) {
    // Adicionar mensagem ao FloatingChat se estiver aberto
    // (implementar integração com FloatingChatsManager se necessário)
    logQueryEvent('ChatHandler', 'Mensagem adicionada ao FloatingChat', { messageId: message.id });
  }
  
  // Métodos para compatibilidade com interface existente
  get messages() {
    // Retornar mensagens do cache se necessário
    return [];
  }
  
  async sendMessage(chatId: string, content: string) {
    try {
      const { error } = await supabase
        .from('chat_messages')
        .insert({
          chat_id: chatId,
          content,
          sender_id: (await supabase.auth.getUser()).data.user?.id
        });
      
      if (error) throw error;
      
      logQueryEvent('ChatHandler', 'Mensagem enviada', { chatId, content: content.substring(0, 20) });
      
    } catch (error) {
      logQueryEvent('ChatHandler', 'Erro ao enviar mensagem', { error }, 'error');
      throw error;
    }
  }

  /**
   * Processa eventos de canais (channels)
   * - INSERT: Novo canal criado
   * - UPDATE: Canal atualizado (nome, descrição, configurações)
   * - DELETE: Canal arquivado/deletado
   */
  async processChannel(payload: Record<string, unknown>) {
    logQueryEvent('ChatHandler', '📢 Processando evento de canal', { 
      eventType: payload.eventType,
      channelId: payload.new?.id || payload.old?.id,
      channelName: payload.new?.name || payload.old?.name
    });
    
    try {
      const channel = (payload.new || payload.old) as Channel;
      
      // Invalidar queries relacionadas a canais
      this.invalidateChannelQueries(channel);
      
      // Disparar eventos customizados para compatibilidade
      this.dispatchChannelEvent(payload, channel);
      
      logQueryEvent('ChatHandler', '✅ Evento de canal processado com sucesso', { 
        eventType: payload.eventType,
        channelId: channel.id,
        channelName: channel.name
      });
      
    } catch (error) {
      logQueryEvent('ChatHandler', 'Erro ao processar evento de canal', { 
        error: error instanceof Error ? error.message : error,
        payload 
      }, 'error');
    }
  }

  /**
   * Processa eventos de membros de canal (channel_members)
   * - INSERT: Usuário adicionado ao canal
   * - UPDATE: Role do membro alterado
   * - DELETE: Usuário removido do canal
   */
  async processChannelMember(payload: Record<string, unknown>) {
    logQueryEvent('ChatHandler', '👥 Processando evento de membro de canal', { 
      eventType: payload.eventType,
      channelId: payload.new?.channel_id || payload.old?.channel_id,
      userId: payload.new?.user_id || payload.old?.user_id,
      role: payload.new?.role || payload.old?.role
    });
    
    try {
      const member = (payload.new || payload.old) as ChannelMember;
      
      // Invalidar queries relacionadas a membros
      this.invalidateChannelMemberQueries(member);
      
      // Disparar eventos customizados para compatibilidade
      this.dispatchChannelMemberEvent(payload, member);
      
      logQueryEvent('ChatHandler', '✅ Evento de membro de canal processado com sucesso', { 
        eventType: payload.eventType,
        channelId: member.channel_id,
        userId: member.user_id,
        role: member.role
      });
      
    } catch (error) {
      logQueryEvent('ChatHandler', 'Erro ao processar evento de membro de canal', { 
        error: error instanceof Error ? error.message : error,
        payload 
      }, 'error');
    }
  }

  /**
   * Invalida queries relacionadas a canais
   */
  private invalidateChannelQueries(channel: Channel) {
    // Lista de canais do usuário
    this.queryClient.invalidateQueries({ queryKey: ['user-channels'] });
    
    // Dados específicos do canal
    this.queryClient.invalidateQueries({ queryKey: ['channel', channel.id] });
    this.queryClient.invalidateQueries({ queryKey: ['channel-details', channel.id] });
    
    // Mensagens do canal (podem ter mudado se canal foi arquivado)
    this.queryClient.invalidateQueries({ queryKey: ['channel-messages', channel.id] });
    
    // Lista geral de chats/canais (para sidebar)
    this.queryClient.invalidateQueries({ queryKey: ['chat-list'] });
    
    logQueryEvent('ChatHandler', 'Queries de canal invalidadas', { 
      channelId: channel.id,
      channelName: channel.name
    });
  }

  /**
   * Invalida queries relacionadas a membros de canal
   */
  private invalidateChannelMemberQueries(member: ChannelMember) {
    // Membros do canal específico
    this.queryClient.invalidateQueries({ queryKey: ['channel-members', member.channel_id] });
    
    // Canais do usuário (pode ter mudado se foi adicionado/removido)
    this.queryClient.invalidateQueries({ queryKey: ['user-channels'] });
    
    // Permissões do canal (se role mudou)
    this.queryClient.invalidateQueries({ queryKey: ['channel-permissions', member.channel_id] });
    
    // Lista geral de chats (para sidebar)
    this.queryClient.invalidateQueries({ queryKey: ['chat-list'] });
    
    logQueryEvent('ChatHandler', 'Queries de membro de canal invalidadas', { 
      channelId: member.channel_id,
      userId: member.user_id,
      role: member.role
    });
  }

  /**
   * Dispara eventos customizados para canais
   */
  private dispatchChannelEvent(payload: Record<string, unknown>, channel: Channel) {
    const channelEvent = new CustomEvent('vindula-channel-changed', {
      detail: {
        channel,
        payload,
        eventType: payload.eventType,
        timestamp: new Date().toISOString()
      }
    });
    
    window.dispatchEvent(channelEvent);
    
    // Evento específico por tipo
    const specificEvent = new CustomEvent(`vindula-channel-${payload.eventType?.toLowerCase()}`, {
      detail: {
        channel,
        payload,
        timestamp: new Date().toISOString()
      }
    });
    
    window.dispatchEvent(specificEvent);
  }

  /**
   * Dispara eventos customizados para membros de canal
   */
  private dispatchChannelMemberEvent(payload: Record<string, unknown>, member: ChannelMember) {
    const memberEvent = new CustomEvent('vindula-channel-member-changed', {
      detail: {
        member,
        payload,
        eventType: payload.eventType,
        timestamp: new Date().toISOString()
      }
    });
    
    window.dispatchEvent(memberEvent);
    
    // Evento específico por tipo
    const specificEvent = new CustomEvent(`vindula-channel-member-${payload.eventType?.toLowerCase()}`, {
      detail: {
        member,
        payload,
        timestamp: new Date().toISOString()
      }
    });
    
    window.dispatchEvent(specificEvent);
  }

  /**
   * Processa eventos de typing status (typing_status)
   * - INSERT: Usuário começou a digitar
   * - UPDATE: Usuário continuou digitando (atualização do timestamp)
   * - DELETE: Usuário parou de digitar
   */
  async processTypingStatus(payload: Record<string, unknown>) {
    logQueryEvent('ChatHandler', '⌨️ Processando evento de typing status', { 
      eventType: payload.eventType,
      userId: payload.new?.user_id || payload.old?.user_id,
      chatId: payload.new?.chat_id || payload.old?.chat_id,
      channelId: payload.new?.channel_id || payload.old?.channel_id,
      lastTypedAt: payload.new?.last_typed_at
    });
    
    try {
      const typingStatus = (payload.new || payload.old) as TypingStatus;
      
      // Disparar eventos customizados para TypingIndicator capturar
      this.dispatchTypingStatusEvent(payload, typingStatus);
      
      logQueryEvent('ChatHandler', '✅ Evento de typing status processado com sucesso', { 
        eventType: payload.eventType,
        userId: typingStatus.user_id,
        chatId: typingStatus.chat_id,
        channelId: typingStatus.channel_id
      });
      
    } catch (error) {
      logQueryEvent('ChatHandler', 'Erro ao processar evento de typing status', { 
        error: error instanceof Error ? error.message : error,
        payload 
      }, 'error');
    }
  }

  /**
   * Dispara eventos customizados para typing status
   */
  private dispatchTypingStatusEvent(payload: Record<string, unknown>, typingStatus: TypingStatus) {
    const typingEvent = new CustomEvent('vindula-typing-status-changed', {
      detail: {
        typingStatus,
        payload,
        eventType: payload.eventType,
        timestamp: new Date().toISOString()
      }
    });
    
    window.dispatchEvent(typingEvent);
    
    // Evento específico por tipo
    const specificEvent = new CustomEvent(`vindula-typing-status-${payload.eventType?.toLowerCase()}`, {
      detail: {
        typingStatus,
        payload,
        timestamp: new Date().toISOString()
      }
    });
    
    window.dispatchEvent(specificEvent);
  }

  /**
   * Dispara eventos customizados para mensagens de chat
   */
  private dispatchMessageEvent(payload: Record<string, unknown>, message: ChatMessage) {
    const messageEvent = new CustomEvent('vindula-chat-message-changed', {
      detail: {
        message,
        payload,
        eventType: payload.eventType,
        timestamp: new Date().toISOString()
      }
    });
    
    window.dispatchEvent(messageEvent);
    
    // Evento específico por tipo
    const specificEvent = new CustomEvent(`vindula-chat-message-${payload.eventType?.toLowerCase()}`, {
      detail: {
        message,
        payload,
        timestamp: new Date().toISOString()
      }
    });
    
    window.dispatchEvent(specificEvent);
  }
}