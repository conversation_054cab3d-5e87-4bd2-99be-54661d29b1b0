/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> - Handler especializado para cache de autenticação e permissões
 * 
 * Consolida lógica do RealtimeManager, Cache<PERSON>anager, AuthManager
 * 
 * <AUTHOR> Internet 2025
 */
import { QueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { QueryKeys } from '@/lib/query/queryKeys';

interface UserRole {
  id: string;
  user_id: string;
  role_id: string;
  company_id: string;
  assigned_at: string;
  assigned_by?: string;
}

interface AccessControlEntry {
  id: string;
  role_id: string;
  resource_type: string;
  action_key: string;
  permission_type: 'allow' | 'deny';
  company_id: string;
}

interface Profile {
  id: string;
  full_name: string;
  email: string;
  company_id: string;
  job_title?: string;
  department?: string;
  avatar_url?: string;
  created_at: string;
  updated_at?: string;
}

export class AuthHandler {
  private queryClient: QueryClient;
  private permissionCache: Map<string, boolean> = new Map();
  
  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
  }
  
  async processRoleUpdate(payload: Record<string, unknown>) {
    logQueryEvent('AuthHandler', '🔐 Processando atualização de role', { 
      userId: payload.new?.user_id,
      roleId: payload.new?.role_id,
      companyId: payload.new?.company_id 
    });
    
    try {
      const roleUpdate = payload.new as UserRole;
      
      // Limpar cache de permissões para o usuário
      await this.clearUserPermissionCache(roleUpdate.user_id);
      
      // Atualizar cache de roles do usuário
      await this.updateUserRolesCache(roleUpdate);
      
      // Invalidar queries relacionadas a autenticação
      this.invalidateAuthQueries(roleUpdate.user_id);
      
      // Se for o usuário atual, invalidar cache de permissões global
      const currentUser = await supabase.auth.getUser();
      if (currentUser.data.user?.id === roleUpdate.user_id) {
        await this.refreshCurrentUserPermissions();
      }
      
      logQueryEvent('AuthHandler', 'Role atualizada com sucesso', { 
        userId: roleUpdate.user_id,
        roleId: roleUpdate.role_id 
      });
      
    } catch (error) {
      logQueryEvent('AuthHandler', 'Erro ao processar atualização de role', { 
        error: error instanceof Error ? error.message : error,
        payload 
      }, 'error');
    }
  }
  
  async processProfileUpdate(payload: Record<string, unknown>) {
    logQueryEvent('AuthHandler', '👤 Processando atualização de perfil', { 
      userId: payload.new?.id,
      companyId: payload.new?.company_id 
    });
    
    try {
      const profileUpdate = payload.new as Profile;
      
      // Atualizar cache do perfil
      await this.updateProfileCache(profileUpdate);
      
      // Invalidar queries relacionadas ao perfil
      this.invalidateProfileQueries(profileUpdate.id);
      
      logQueryEvent('AuthHandler', 'Perfil atualizado com sucesso', { 
        userId: profileUpdate.id 
      });
      
    } catch (error) {
      logQueryEvent('AuthHandler', 'Erro ao processar atualização de perfil', { 
        error: error instanceof Error ? error.message : error,
        payload 
      }, 'error');
    }
  }
  
  async processPermissionUpdate(payload: Record<string, unknown>) {
    logQueryEvent('AuthHandler', '🛡️ Processando atualização de permissão', { 
      roleId: payload.new?.role_id,
      resourceType: payload.new?.resource_type,
      actionKey: payload.new?.action_key 
    });
    
    try {
      const permissionUpdate = payload.new as AccessControlEntry;
      
      // Limpar cache de permissões para todos os usuários com este role
      await this.clearRolePermissionCache(permissionUpdate.role_id);
      
      // Invalidar queries relacionadas a permissões
      this.invalidatePermissionQueries(permissionUpdate.role_id);
      
      logQueryEvent('AuthHandler', 'Permissão atualizada com sucesso', { 
        roleId: permissionUpdate.role_id,
        resourceType: permissionUpdate.resource_type,
        actionKey: permissionUpdate.action_key 
      });
      
    } catch (error) {
      logQueryEvent('AuthHandler', 'Erro ao processar atualização de permissão', { 
        error: error instanceof Error ? error.message : error,
        payload 
      }, 'error');
    }
  }
  
  private async clearUserPermissionCache(userId: string) {
    // Limpar cache local de permissões
    const keysToRemove = Array.from(this.permissionCache.keys())
      .filter(key => key.startsWith(`${userId}:`));
    
    keysToRemove.forEach(key => {
      this.permissionCache.delete(key);
    });
    
    logQueryEvent('AuthHandler', 'Cache de permissões do usuário limpo', { 
      userId,
      keysRemoved: keysToRemove.length 
    });
  }
  
  private async clearRolePermissionCache(roleId: string) {
    // Buscar todos os usuários com este role para limpar cache
    try {
      const { data: usersWithRole } = await supabase
        .from('user_roles')
        .select('user_id')
        .eq('role_id', roleId);
      
      if (usersWithRole) {
        for (const userRole of usersWithRole) {
          await this.clearUserPermissionCache(userRole.user_id);
        }
      }
      
      logQueryEvent('AuthHandler', 'Cache de permissões do role limpo', { 
        roleId,
        usersAffected: usersWithRole?.length || 0 
      });
      
    } catch (error) {
      logQueryEvent('AuthHandler', 'Erro ao limpar cache de role', { 
        roleId,
        error 
      }, 'error');
    }
  }
  
  private async updateUserRolesCache(roleUpdate: UserRole) {
    // Atualizar cache de roles do usuário
    this.queryClient.setQueryData(['user-roles', roleUpdate.user_id], (old: any) => {
      if (!old) return [roleUpdate];
      
      // Atualizar role existente ou adicionar novo
      const existingIndex = old.findIndex((r: any) => r.id === roleUpdate.id);
      if (existingIndex >= 0) {
        const updated = [...old];
        updated[existingIndex] = roleUpdate;
        return updated;
      } else {
        return [...old, roleUpdate];
      }
    });
    
    // Atualizar cache global de roles por empresa
    this.queryClient.setQueryData(['company-roles', roleUpdate.company_id], (old: any) => {
      if (!old) return [roleUpdate];
      
      const existingIndex = old.findIndex((r: any) => r.id === roleUpdate.id);
      if (existingIndex >= 0) {
        const updated = [...old];
        updated[existingIndex] = roleUpdate;
        return updated;
      } else {
        return [...old, roleUpdate];
      }
    });
    
    logQueryEvent('AuthHandler', 'Cache de roles atualizado', { 
      userId: roleUpdate.user_id,
      roleId: roleUpdate.role_id 
    });
  }
  
  private async updateProfileCache(profileUpdate: Profile) {
    // Atualizar cache do perfil específico
    this.queryClient.setQueryData(['profile', profileUpdate.id], profileUpdate);
    
    // Atualizar cache de perfis da empresa
    this.queryClient.setQueryData(['company-profiles', profileUpdate.company_id], (old: any) => {
      if (!old) return [profileUpdate];
      
      const existingIndex = old.findIndex((p: any) => p.id === profileUpdate.id);
      if (existingIndex >= 0) {
        const updated = [...old];
        updated[existingIndex] = profileUpdate;
        return updated;
      } else {
        return [...old, profileUpdate];
      }
    });
    
    // Se for o perfil do usuário atual, atualizar cache especial
    const currentUser = await supabase.auth.getUser();
    if (currentUser.data.user?.id === profileUpdate.id) {
      this.queryClient.setQueryData(['current-profile'], profileUpdate);
    }
    
    logQueryEvent('AuthHandler', 'Cache de perfil atualizado', { 
      userId: profileUpdate.id 
    });
  }
  
  private async refreshCurrentUserPermissions() {
    // Recarregar permissões do usuário atual
    try {
      const currentUser = await supabase.auth.getUser();
      if (!currentUser.data.user?.id) return;
      
      // Invalidar cache de permissões específicas
      this.queryClient.invalidateQueries({ 
        queryKey: ['user-permissions', currentUser.data.user.id] 
      });
      
      // Invalidar verificações de permissão genérica
      this.queryClient.invalidateQueries({ 
        queryKey: ['permission-check'] 
      });
      
      logQueryEvent('AuthHandler', 'Permissões do usuário atual recarregadas', { 
        userId: currentUser.data.user.id 
      });
      
    } catch (error) {
      logQueryEvent('AuthHandler', 'Erro ao recarregar permissões do usuário atual', { 
        error 
      }, 'error');
    }
  }
  
  private invalidateAuthQueries(userId: string) {
    // Invalidar queries específicas do usuário
    this.queryClient.invalidateQueries({ queryKey: ['user-roles', userId] });
    this.queryClient.invalidateQueries({ queryKey: ['user-permissions', userId] });
    
    // Invalidar queries globais de autenticação
    this.queryClient.invalidateQueries({ queryKey: ['permission-check'] });
    this.queryClient.invalidateQueries({ queryKey: ['access-control'] });
    
    logQueryEvent('AuthHandler', 'Queries de autenticação invalidadas', { userId });
  }
  
  private invalidateProfileQueries(userId: string) {
    // Invalidar queries do perfil
    this.queryClient.invalidateQueries({ queryKey: ['profile', userId] });
    this.queryClient.invalidateQueries({ queryKey: ['current-profile'] });
    this.queryClient.invalidateQueries({ queryKey: ['company-profiles'] });
    
    // Invalidar queries que dependem de dados do perfil
    this.queryClient.invalidateQueries({ queryKey: ['users'] });
    this.queryClient.invalidateQueries({ queryKey: ['team-members'] });
    
    logQueryEvent('AuthHandler', 'Queries de perfil invalidadas', { userId });
  }
  
  private invalidatePermissionQueries(roleId: string) {
    // Invalidar todas as queries relacionadas a permissões
    this.queryClient.invalidateQueries({ queryKey: ['role-permissions', roleId] });
    this.queryClient.invalidateQueries({ queryKey: ['permission-check'] });
    this.queryClient.invalidateQueries({ queryKey: ['access-control-entries'] });
    this.queryClient.invalidateQueries({ queryKey: ['user-permissions'] });
    
    logQueryEvent('AuthHandler', 'Queries de permissões invalidadas', { roleId });
  }
  
  // Métodos públicos para integração com sistema de autenticação
  async checkPermission(userId: string, resourceType: string, actionKey: string): Promise<boolean> {
    const cacheKey = `${userId}:${resourceType}:${actionKey}`;
    
    // Verificar cache local primeiro
    if (this.permissionCache.has(cacheKey)) {
      return this.permissionCache.get(cacheKey)!;
    }
    
    try {
      // Buscar no banco se não estiver em cache
      const { data, error } = await supabase.rpc('check_permission_v2', {
        p_user_id: userId,
        p_resource_type: resourceType,
        p_action_key: actionKey,
        p_resource_id: null
      });
      
      if (error) throw error;
      
      const hasPermission = data === true;
      
      // Armazenar em cache local
      this.permissionCache.set(cacheKey, hasPermission);
      
      logQueryEvent('AuthHandler', 'Permissão verificada', { 
        userId,
        resourceType,
        actionKey,
        hasPermission 
      });
      
      return hasPermission;
      
    } catch (error) {
      logQueryEvent('AuthHandler', 'Erro ao verificar permissão', { 
        userId,
        resourceType,
        actionKey,
        error 
      }, 'error');
      
      return false; // Falha segura
    }
  }
  
  async getCurrentUserProfile(): Promise<Profile | null> {
    try {
      const currentUser = await supabase.auth.getUser();
      if (!currentUser.data.user?.id) return null;
      
      return this.queryClient.getQueryData(['current-profile']) || null;
      
    } catch (error) {
      logQueryEvent('AuthHandler', 'Erro ao obter perfil atual', { error }, 'error');
      return null;
    }
  }
  
  async invalidateAllUserCache(userId: string) {
    // Método para invalidar todo o cache de um usuário
    await this.clearUserPermissionCache(userId);
    this.invalidateAuthQueries(userId);
    this.invalidateProfileQueries(userId);
    
    logQueryEvent('AuthHandler', 'Todo cache do usuário invalidado', { userId });
  }
  
  // Getter para compatibilidade
  getPermissionCache() {
    return this.permissionCache;
  }
  
  // Método para limpar todo o cache (útil em logout)
  clearAllCache() {
    this.permissionCache.clear();
    this.queryClient.clear();
    
    logQueryEvent('AuthHandler', 'Todo o cache de autenticação limpo');
  }
}