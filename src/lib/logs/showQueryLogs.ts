/**
 * Utilitário para logs padronizados do Cosmos.
 * <AUTHOR> Internet 2025
 */
// Configuração de logs baseada em variáveis de ambiente
export const showQueryLogs =
  import.meta.env.VITE_QUERY_LOGS === 'true' || import.meta.env.MODE === 'development';

export const logLevel = (import.meta.env.VITE_LOG_LEVEL as LogLevel) || 'info';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

/**
 * Loga mensagens padronizadas se showQueryLogs estiver ativo.
 * @param module Nome do módulo (ex: 'queryClient', 'useAuthRedirect')
 * @param message Mensagem a ser exibida
 * @param data Dados opcionais para log detalhado
 * @param level Nível do log: 'debug' (padrão, bolinha verde) ou 'error' (vermelho)
 */
// Hierarquia de níveis de log (menor = mais importante)
const logLevels = {
  'debug': 0,
  'info': 1,  
  'warn': 2,
  'error': 3
} as const;

export function logQueryEvent(
  module: string,
  message: string,
  ...args: any[]
) {
  if (!showQueryLogs) return;
  
  let level: LogLevel = 'info';
  let data: any[] = [];

  // Verificar se o último argumento é um nível de log válido
  if (args.length && typeof args[args.length - 1] === 'string' && 
      args[args.length - 1] in logLevels) {
    level = args.pop() as LogLevel;
  }
  data = args;

  // Filtrar logs baseado no nível configurado
  if (logLevels[level] < logLevels[logLevel]) {
    return;
  }

  const icons = {
    debug: '🔍',
    info: '🟢', 
    warn: '🟡',
    error: '❌',
  };
  const colors = {
    debug: 'color: #9e9e9e; font-weight: normal;', // cinza
    info: 'color: #43a047; font-weight: bold;',    // verde
    warn: 'color: #fb8c00; font-weight: bold;',    // laranja  
    error: 'color: #e53935; font-weight: bold;',   // vermelho
  };

  const prefix = `[${module}]`;
  const icon = icons[level];
  const style = colors[level];

  if (data.length > 0) {
    console.log(`%c${icon} ${prefix} ${message}`, style, ...data);
  } else {
    console.log(`%c${icon} ${prefix} ${message}`, style);
  }
}