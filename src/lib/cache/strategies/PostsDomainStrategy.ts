/**
 * PostsDomainStrategy - Estratégia híbrida para posts
 * 
 * 🎯 CONCEITO HÍBRIDO:
 * - Posts individuais: QUASI-STATIC (staleTime: Infinity)
 * - Feed/Listagens: DYNAMIC (staleTime: 2min)
 * 
 * 💡 INSIGHT: Posts raramente mudam após criação, mas feed recebe novos posts constantemente
 * 
 * <AUTHOR> Internet 2025
 */

import { BaseCacheStrategy } from '../core/BaseCacheStrategy';
import { CacheConfiguration, EventPayload } from '../types';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

interface PostEventData {
  postId?: string;
  action?: 'create' | 'update' | 'delete' | 'like' | 'unlike';
  userId?: string;
  metadata?: Record<string, any>;
}

interface FeedEventData {
  page?: number;
  count?: number;
  totalCount?: number;
  filters?: Record<string, any>;
  posts?: Array<{ id: string; type?: string }>;
}

export class PostsDomainStrategy extends BaseCacheStrategy {
  readonly domainName = 'posts';

  /**
   * 🎯 Configuração Híbrida por Tipo de Query
   */
  getConfig(): CacheConfiguration {
    return {
      // ⚡ DEFAULT: Para listagens/feeds (mudam frequentemente)
      staleTime: 2 * 60 * 1000,          // 2 minutos
      gcTime: 15 * 60 * 1000,            // 15 minutos na memória
      refetchOnMount: false,              // Cache-first
      refetchOnWindowFocus: true,         // Refresh ao voltar para aba
      refetchOnReconnect: true,           // Refresh ao reconectar
      retry: 2,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    };
  }

  /**
   * 🗿 Configuração para Posts Individuais (quase nunca mudam)
   */
  getPostConfig(): CacheConfiguration {
    return {
      staleTime: Infinity,                // 🚀 CACHE ETERNO!
      gcTime: 24 * 60 * 60 * 1000,       // 24 horas na memória
      refetchOnMount: false,              // Nunca refetch automático
      refetchOnWindowFocus: false,        // Nunca refetch no focus
      refetchOnReconnect: false,          // Nunca refetch ao reconectar
      retry: 1,
      retryDelay: 1000,
    };
  }

  /**
   * 🔄 Configuração para Feeds/Contagens (mudam com frequência)
   */
  getFeedConfig(): CacheConfiguration {
    return {
      staleTime: 1 * 60 * 1000,          // 1 minuto (mais agressivo)
      gcTime: 10 * 60 * 1000,            // 10 minutos na memória
      refetchOnMount: false,              // Cache-first mesmo para feeds
      refetchOnWindowFocus: true,         // Refresh feeds ao voltar
      refetchOnReconnect: true,           // Sync feeds ao reconectar
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(500 * 2 ** attemptIndex, 10000),
    };
  }

  /**
   * 🧪 Determinar tipo de configuração baseado na query key
   */
  getConfigForQueryKey(queryKey: readonly unknown[]): CacheConfiguration {
    const keyStr = JSON.stringify(queryKey);
    
    // 🗿 Posts individuais (cache eterno)
    if (keyStr.includes('"posts"') && 
        (keyStr.includes('"detail"') || keyStr.includes('"exists"') || 
         (queryKey.length === 2 && typeof queryKey[1] === 'string'))) {
      logQueryEvent('PostsDomainStrategy', '🗿 Config POST INDIVIDUAL (cache eterno)', { queryKey });
      return this.getPostConfig();
    }
    
    // 🔄 Feeds e listagens (cache dinâmico)
    if (keyStr.includes('"feed"') || keyStr.includes('"scheduled"') || 
        keyStr.includes('"trending"') || keyStr.includes('Count')) {
      logQueryEvent('PostsDomainStrategy', '🔄 Config FEED/LISTAGEM (cache dinâmico)', { queryKey });
      return this.getFeedConfig();
    }
    
    // ⚡ Default para outros casos
    logQueryEvent('PostsDomainStrategy', '⚡ Config DEFAULT', { queryKey });
    return this.getConfig();
  }

  /**
   * 🎯 Handler de Eventos Inteligente
   */
  async handleEvent(event: EventPayload): Promise<void> {
    const { type, action, data } = event;

    // 📝 Eventos de Posts
    if (type === 'posts') {
      await this.handlePostEvent(action, data as PostEventData);
    }
    
    // 🔄 Eventos de Feed  
    else if (type === 'posts.feed') {
      await this.handleFeedEvent(action, data as FeedEventData);
    }
    
    // 💗 Eventos de Likes (afetar post individual + feed)
    else if (type === 'posts.likes') {
      await this.handleLikeEvent(action, data as PostEventData);
    }
  }

  /**
   * 📝 Handler para Eventos de Posts Individuais
   */
  private async handlePostEvent(action: string, data: PostEventData): Promise<void> {
    const { postId, action: postAction } = data;

    switch (postAction) {
      case 'create':
        logQueryEvent('PostsDomainStrategy', '🆕 NOVO POST - Invalidando feeds', data);
        // Novo post criado → invalidar apenas feeds (post individual não existe em cache ainda)
        await this.invalidateFeeds();
        
        // 🚌 Emitir evento para componentes
        this.eventBus?.emit('posts.new', { postId, timestamp: Date.now() });
        break;

      case 'update':
        if (postId) {
          logQueryEvent('PostsDomainStrategy', '✏️ POST EDITADO - Invalidando post específico + feeds', data);
          // 🗿 Post foi editado → QUEBRAR cache eterno deste post específico
          await this.invalidatePostSpecific(postId);
          // 🔄 Invalidar feeds também (conteúdo pode ter mudado)
          await this.invalidateFeeds();
          
          // 🚌 Emitir evento
          this.eventBus?.emit('posts.updated', { postId, timestamp: Date.now() });
        }
        break;

      case 'delete':
        if (postId) {
          logQueryEvent('PostsDomainStrategy', '🗑️ POST DELETADO - Invalidando tudo relacionado', data);
          // 🗿 Post foi deletado → remover completamente do cache
          await this.removePostFromCache(postId);
          // 🔄 Invalidar feeds (post não deve mais aparecer)
          await this.invalidateFeeds();
          
          // 🚌 Emitir evento
          this.eventBus?.emit('posts.deleted', { postId, timestamp: Date.now() });
        }
        break;
    }
  }

  /**
   * 💗 Handler para Eventos de Likes
   */
  private async handleLikeEvent(action: string, data: PostEventData): Promise<void> {
    const { postId, userId } = data;

    if (postId) {
      logQueryEvent('PostsDomainStrategy', `💗 LIKE ${action?.toUpperCase()} - Invalidando post + feeds`, data);
      
      // 🗿 Invalidar post específico (like count mudou)
      await this.invalidatePostSpecific(postId);
      
      // 🔄 Invalidar feeds (ordenação "most_liked" pode ter mudado)
      await this.invalidateFeeds();
      
      // 💗 Invalidar queries de likes específicas
      await this.invalidatePattern(['posts', postId, 'likes']);
      
      // 🚌 Emitir evento
      this.eventBus?.emit('posts.liked', { 
        postId, 
        userId, 
        action: action as 'like' | 'unlike',
        timestamp: Date.now() 
      });
    }
  }

  /**
   * 🔄 Handler para Eventos de Feed
   */
  private async handleFeedEvent(action: string, data: FeedEventData): Promise<void> {
    logQueryEvent('PostsDomainStrategy', `🔄 FEED EVENT: ${action}`, data);
    
    switch (action) {
      case 'fetched':
        // Feed foi carregado com sucesso - não precisa invalidar nada
        // Dados já estão frescos
        break;
        
      case 'error':
        // Erro ao carregar feed - pode tentar invalidar para retry
        logQueryEvent('PostsDomainStrategy', '❌ Erro no feed - invalidando para retry', data);
        await this.invalidateFeeds();
        break;
    }
  }

  /**
   * 🎯 Métodos de Invalidação Específicos
   */

  /**
   * 🗿 Invalidar post específico (quebrar cache eterno)
   */
  private async invalidatePostSpecific(postId: string): Promise<void> {
    const patterns = [
      ['posts', postId],              // Post details
      ['posts', postId, 'detail'],    // Post detail variant
      ['posts', postId, 'exists'],    // Post exists check
      ['posts', postId, 'likes'],     // Post likes
      ['posts', postId, 'comments'],  // Post comments
    ];

    for (const pattern of patterns) {
      await this.invalidatePattern(pattern);
    }
  }

  /**
   * 🔄 Invalidar todos os feeds (mas preservar posts individuais)
   */
  private async invalidateFeeds(): Promise<void> {
    const feedPatterns = [
      ['posts', 'feed'],              // Feed principal (com/sem filtros)
      ['posts', 'scheduled'],         // Posts agendados
      ['posts', 'scheduled-count'],   // Contagem agendados
      ['posts', 'trending'],          // Posts trending
      ['timeline', 'posts'],          // Timeline posts
    ];

    for (const pattern of feedPatterns) {
      await this.invalidatePattern(pattern);
    }
  }

  /**
   * 🗑️ Remover post completamente do cache
   */
  private async removePostFromCache(postId: string): Promise<void> {
    // Remove queries específicas do post
    await this.invalidatePostSpecific(postId);
    
    // Remove post de qualquer query que possa contê-lo
    await this.invalidateFeeds();
    
    // 🧹 Limpeza manual de queries que possam referenciar este post
    this.queryClient.getQueryCache().findAll(['posts']).forEach(query => {
      const data = query.state.data as any;
      
      // Se é infinite query, limpar das páginas
      if (data?.pages) {
        let hasPost = false;
        data.pages.forEach((page: any) => {
          if (page?.posts?.some((post: any) => post.id === postId)) {
            hasPost = true;
          }
        });
        
        if (hasPost) {
          query.invalidate();
        }
      }
      
      // Se é array de posts, verificar se contém o post
      else if (Array.isArray(data) && data.some((post: any) => post?.id === postId)) {
        query.invalidate();
      }
    });
  }

  /**
   * 🚀 Cache Warming Específico para Posts
   */
  async warmCache(userId: string): Promise<void> {
    logQueryEvent('PostsDomainStrategy', '🔥 Iniciando cache warming para posts', { userId });
    
    try {
      // 🔥 Warm feeds principais (que o usuário provavelmente vai ver)
      const warmingTasks = [
        // Feed principal (primeira página)
        this.warmFeedFirstPage(),
        
        // Contagem de posts agendados (mostrado na UI)
        this.warmScheduledCount(),
        
        // Posts trending (sidebar)
        this.warmTrendingPosts(),
      ];

      await Promise.allSettled(warmingTasks);
      
      logQueryEvent('PostsDomainStrategy', '✅ Cache warming concluído para posts');
    } catch (error) {
      logQueryEvent('PostsDomainStrategy', '❌ Erro no cache warming para posts', error, 'error');
    }
  }

  /**
   * 🔥 Warm primeira página do feed
   */
  private async warmFeedFirstPage(): Promise<void> {
    try {
      await this.queryClient.prefetchInfiniteQuery({
        queryKey: ['posts', 'feed'],
        queryFn: ({ pageParam = 0 }) => {
          // Lógica simplificada para warming - só primeira página
          return this.fetchPostsFeedPage(pageParam as number);
        },
        initialPageParam: 0,
        staleTime: this.getFeedConfig().staleTime,
      });
    } catch (error) {
      logQueryEvent('PostsDomainStrategy', 'Erro ao warm feed primeira página', error, 'error');
    }
  }

  /**
   * 🔥 Warm contagem de posts agendados  
   */
  private async warmScheduledCount(): Promise<void> {
    try {
      await this.queryClient.prefetchQuery({
        queryKey: ['posts', 'scheduled-count'],
        queryFn: () => this.fetchScheduledCount(),
        staleTime: this.getFeedConfig().staleTime,
      });
    } catch (error) {
      logQueryEvent('PostsDomainStrategy', 'Erro ao warm scheduled count', error, 'error');
    }
  }

  /**
   * 🔥 Warm posts trending
   */
  private async warmTrendingPosts(): Promise<void> {
    try {
      await this.queryClient.prefetchQuery({
        queryKey: ['posts', 'trending', 5],
        queryFn: () => this.fetchTrendingPosts(5),
        staleTime: this.getFeedConfig().staleTime,
      });
    } catch (error) {
      logQueryEvent('PostsDomainStrategy', 'Erro ao warm trending posts', error, 'error');
    }
  }

  /**
   * 🎯 Métodos auxiliares para fetch direto (usado no warming)
   */
  private async fetchPostsFeedPage(page: number): Promise<any> {
    // Implementação simplificada - na prática usaria a mesma lógica do hook
    // Por enquanto retorna promise vazia para não quebrar
    return Promise.resolve({ posts: [], nextPage: undefined, totalCount: 0 });
  }

  private async fetchScheduledCount(): Promise<number> {
    // Implementação simplificada
    return Promise.resolve(0);
  }

  private async fetchTrendingPosts(limit: number): Promise<any[]> {
    // Implementação simplificada
    return Promise.resolve([]);
  }

  /**
   * 📊 Métricas específicas para Posts
   */
  getMetrics(): Record<string, any> {
    const queries = this.queryClient.getQueryCache().findAll(['posts']);
    
    const postQueries = queries.filter(q => 
      q.queryKey.includes('posts') && 
      q.queryKey.length === 2 && 
      typeof q.queryKey[1] === 'string'
    );
    
    const feedQueries = queries.filter(q => 
      q.queryKey.includes('posts') && 
      (q.queryKey.includes('feed') || q.queryKey.includes('scheduled'))
    );

    return {
      totalPostQueries: queries.length,
      individualPosts: postQueries.length,
      feedQueries: feedQueries.length,
      cacheHitRatio: this.calculateHitRatio(queries),
      averageStaleTime: this.calculateAverageStaleTime(queries),
      infiniteQueries: queries.filter(q => 
        q.state.data && 
        typeof q.state.data === 'object' && 
        'pages' in q.state.data
      ).length,
    };
  }

  /**
   * 🧹 Cleanup específico para Posts
   */
  async cleanup(): Promise<void> {
    logQueryEvent('PostsDomainStrategy', '🧹 Iniciando cleanup para posts');
    
    // Remover queries antigas de posts que não são mais necessárias
    const queries = this.queryClient.getQueryCache().findAll(['posts']);
    const oldQueries = queries.filter(query => {
      const updatedAt = query.state.dataUpdatedAt;
      const oneHourAgo = Date.now() - (60 * 60 * 1000);
      return updatedAt < oneHourAgo && query.getObserversCount() === 0;
    });

    oldQueries.forEach(query => {
      this.queryClient.getQueryCache().remove(query);
    });

    logQueryEvent('PostsDomainStrategy', `🧹 Cleanup concluído - ${oldQueries.length} queries removidas`);
  }
}