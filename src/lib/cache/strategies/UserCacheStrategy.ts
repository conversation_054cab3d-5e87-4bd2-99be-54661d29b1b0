/**
 * <AUTHOR> Internet 2025
 * @description Strategy para cache de usuários
 */

import { CacheEvent, CacheConfig } from '../types';
import { BaseCacheStrategy } from '../core/BaseCacheStrategy';
import { getCacheService } from '../core/CacheService';

export class UserCacheStrategy extends BaseCacheStrategy {
  constructor() {
    super('UserCacheStrategy');
  }

  getConfig(): CacheConfig {
    return this.getDomainConfig('dynamic');
  }

  canHandle(event: CacheEvent): boolean {
    return this.isEventType(event, 
      'user.profile.update',
      'user.status.change',
      'user.avatar.change',
      'user.preferences.update',
      'realtime.profiles.update',
      'auth.login',
      'auth.logout',
      // Novos eventos para usuários ativos
      'users.active.fetched',
      'users.error',
      'users.status.changed'
    );
  }

  protected async processEvent(event: CacheEvent): Promise<void> {
    switch (event.type) {
      case 'user.profile.update':
        await this.handleProfileUpdate(event);
        break;
      
      case 'user.status.change':
        await this.handleStatusChange(event);
        break;
      
      case 'user.avatar.change':
        await this.handleAvatarChange(event);
        break;
      
      case 'user.preferences.update':
        await this.handlePreferencesUpdate(event);
        break;
      
      case 'realtime.profiles.update':
        await this.handleRealtimeProfileUpdate(event);
        break;
      
      case 'auth.login':
        await this.handleAuthLogin(event);
        break;
      
      case 'auth.logout':
        await this.handleAuthLogout(event);
        break;
      
      case 'users.active.fetched':
        await this.handleActiveUsersFetched(event);
        break;
      
      case 'users.error':
        await this.handleUsersError(event);
        break;
      
      case 'users.status.changed':
        await this.handleUserStatusChanged(event);
        break;
      
      default:
        console.warn(`[${this.name}] Unhandled event type: ${event.type}`);
    }
  }

  protected async performInvalidation(keys: string[]): Promise<void> {
    const cacheService = getCacheService();
    
    for (const key of keys) {
      await cacheService.queryClient.invalidateQueries({
        queryKey: key.split('.'),
      });
    }
  }

  /**
   * Aquece cache com dados essenciais do usuário
   */
  async warmCache(data: any): Promise<void> {
    const { userId, action } = data;
    
    if (action === 'login' && userId) {
      // Pré-carregar dados essenciais do usuário
      await this.warmUserEssentials(userId);
    }
  }

  /**
   * Lida com atualização de perfil
   */
  private async handleProfileUpdate(event: CacheEvent): Promise<void> {
    const { userId, updatedFields } = event.payload;
    
    // Invalidar cache do usuário específico
    await this.invalidate([
      this.createQueryKey('users', 'profile', userId),
      this.createQueryKey('users', 'current'),
    ]);
    
    // Se mudou informações que afetam outros domínios
    if (updatedFields.includes('job_title') || updatedFields.includes('department')) {
      await this.invalidate([
        this.createQueryKey('company', 'employees'),
        this.createQueryKey('departments', 'members'),
      ]);
    }
    
    // Se mudou nome ou avatar, invalidar posts e comentários
    if (updatedFields.includes('name') || updatedFields.includes('avatar')) {
      await this.invalidate([
        this.createQueryKey('posts', 'user', userId),
        this.createQueryKey('comments', 'user', userId),
      ]);
    }
  }

  /**
   * Lida com mudança de status
   */
  private async handleStatusChange(event: CacheEvent): Promise<void> {
    const { userId, status } = event.payload;
    
    await this.invalidate([
      this.createQueryKey('users', 'status', userId),
      this.createQueryKey('users', 'online'),
    ]);
    
    // Se ficou offline, invalidar typing indicators
    if (status === 'offline') {
      await this.invalidate([
        this.createQueryKey('chat', 'typing', userId),
      ]);
    }
  }

  /**
   * Lida com mudança de avatar
   */
  private async handleAvatarChange(event: CacheEvent): Promise<void> {
    const { userId } = event.payload;
    
    await this.invalidate([
      this.createQueryKey('users', 'profile', userId),
      this.createQueryKey('users', 'avatar', userId),
      // Invalidar posts e comentários para mostrar novo avatar
      this.createQueryKey('posts', 'user', userId),
      this.createQueryKey('comments', 'user', userId),
    ]);
  }

  /**
   * Lida com atualização de preferências
   */
  private async handlePreferencesUpdate(event: CacheEvent): Promise<void> {
    const { userId } = event.payload;
    
    await this.invalidate([
      this.createQueryKey('users', 'preferences', userId),
      this.createQueryKey('users', 'settings', userId),
    ]);
  }

  /**
   * Lida com atualização via realtime
   */
  private async handleRealtimeProfileUpdate(event: CacheEvent): Promise<void> {
    const { record } = event.payload;
    
    // Determinar quais campos mudaram
    const updatedFields = Object.keys(record);
    
    await this.handleProfileUpdate({
      ...event,
      payload: {
        userId: record.id,
        updatedFields,
      },
    });
  }

  /**
   * Lida com login do usuário
   */
  private async handleAuthLogin(event: CacheEvent): Promise<void> {
    const { userId } = event.payload;
    
    // Warm cache com dados essenciais
    await this.warmUserEssentials(userId);
  }

  /**
   * Lida com logout do usuário
   */
  private async handleAuthLogout(event: CacheEvent): Promise<void> {
    // Limpar cache relacionado ao usuário
    await this.invalidate([
      this.createQueryKey('users', 'current'),
      this.createQueryKey('users', 'preferences'),
      this.createQueryKey('users', 'profile'),
    ]);
  }

  /**
   * Aquece cache com dados essenciais do usuário
   */
  private async warmUserEssentials(userId: string): Promise<void> {
    const cacheService = getCacheService();
    
    // Prefetch dados essenciais
    const essentialKeys = [
      ['users', 'profile', userId],
      ['users', 'current'],
      ['users', 'preferences', userId],
      ['users', 'permissions', userId],
    ];
    
    // Criar promises para prefetch
    const prefetchPromises = essentialKeys.map(async (queryKey) => {
      // Simular prefetch - na implementação real, seria feito com queryClient.prefetchQuery
      console.log(`[${this.name}] Warming cache for:`, queryKey);
    });
    
    await Promise.allSettled(prefetchPromises);
  }

  /**
   * Lida com dados de usuários ativos carregados com sucesso
   */
  private async handleActiveUsersFetched(event: CacheEvent): Promise<void> {
    const { count, threshold } = event.payload;
    
    console.log(`[${this.name}] Usuários ativos carregados: ${count} usuários (threshold: ${threshold}min)`);
    
    // Cache dos usuários ativos foi atualizado automaticamente pela query
    // Aqui podemos fazer invalidações relacionadas se necessário
    
    // Se houver mudanças significativas, invalidar queries dependentes
    if (count === 0) {
      // Não há usuários ativos, pode afetar outras consultas
      await this.invalidate([
        this.createQueryKey('chat', 'channels', 'active'),
        this.createQueryKey('notifications', 'broadcast', 'targets'),
      ]);
    }
  }

  /**
   * Lida com erros ao buscar usuários
   */
  private async handleUsersError(event: CacheEvent): Promise<void> {
    const { operation, error } = event.payload;
    
    console.error(`[${this.name}] Erro na operação ${operation}:`, error);
    
    // Dependendo do tipo de erro, pode ser necessário limpar cache inconsistente
    if (operation === 'fetch_active_users') {
      // Não invalidar cache em caso de erro de rede, manter dados antigos
      console.log(`[${this.name}] Mantendo cache de usuários ativos após erro de rede`);
    }
  }

  /**
   * Lida com mudança de status de usuário específico
   */
  private async handleUserStatusChanged(event: CacheEvent): Promise<void> {
    const { userId, oldStatus, newStatus } = event.payload;
    
    console.log(`[${this.name}] Status do usuário ${userId} mudou: ${oldStatus} → ${newStatus}`);
    
    // Invalidar cache de usuários ativos para refletir mudança
    await this.invalidate([
      this.createQueryKey('users', 'active'),
      this.createQueryKey('users', 'online'),
      this.createQueryKey('users', 'status', userId),
    ]);
  }
}