/**
 * Utilitários para exportação de relatórios em diferentes formatos
 * <AUTHOR> Internet 2025
 */
import { collectRealEngagementData, collectRealContentData, RealReportData } from './reportDataCollector';
import jsPDF from 'jspdf';

export interface ReportData {
  reportType: 'engagement' | 'content';
  dateRange?: { from?: Date; to?: Date };
  data: any;
}

// Função auxiliar para sanitizar texto e evitar caracteres problemáticos
function sanitizeText(text: string | null | undefined): string {
  if (!text || typeof text !== 'string') {
    return 'Sem título';
  }
  
  // Remove caracteres especiais que podem quebrar HTML/CSV
  return text
    .replace(/[<>]/g, '') // Remove < e >
    .replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '') // Remove emojis
    .replace(/[^\w\s\-.,!?()[\]]/g, ' ') // Remove outros caracteres especiais
    .replace(/\s+/g, ' ') // Normaliza espaços múltiplos
    .trim() || 'Sem título';
}

// Dados mock para demonstração - substituir por dados reais da API
const getMockEngagementData = () => ({
  metrics: {
    totalAccess: 12845,
    uniqueUsers: 427,
    averageSessionTime: 18.5,
    retentionRate: 87,
    reactions: 8743,
    comments: 2546,
    shares: 941,
    engagementRate: 75
  },
  dailyData: [
    { date: '2025-01-15', accesses: 1250, users: 427, sessionTime: 18.5, reactions: 743, comments: 156, shares: 89 },
    { date: '2025-01-16', accesses: 1180, users: 398, sessionTime: 17.2, reactions: 698, comments: 142, shares: 76 },
    { date: '2025-01-17', accesses: 1320, users: 445, sessionTime: 19.1, reactions: 812, comments: 167, shares: 95 },
    { date: '2025-01-18', accesses: 1280, users: 412, sessionTime: 18.8, reactions: 756, comments: 148, shares: 83 },
    { date: '2025-01-19', accesses: 1350, users: 456, sessionTime: 19.3, reactions: 834, comments: 172, shares: 98 }
  ],
  topContent: [
    { title: 'Guia Prático de Sustentabilidade', views: 543, reactions: 487, comments: 73, shares: 45 },
    { title: 'Atualização da Política de Home Office', views: 487, reactions: 432, comments: 65, shares: 39 },
    { title: 'Novos Benefícios para Colaboradores', views: 465, reactions: 398, comments: 68, shares: 41 }
  ],
  departmentData: [
    { department: 'Marketing', engagementRate: 92, users: 45 },
    { department: 'Vendas', engagementRate: 88, users: 38 },
    { department: 'RH', engagementRate: 85, users: 32 },
    { department: 'TI', engagementRate: 83, users: 41 }
  ]
});

const getMockContentData = () => ({
  metrics: {
    totalPosts: 124,
    totalViews: 45600,
    averageEngagement: 78.3,
    topCategory: 'Notícias Corporativas'
  },
  contentPerformance: [
    { title: 'Sustentabilidade Corporativa', category: 'ESG', views: 543, engagement: 89.8, publishDate: '2025-01-15' },
    { title: 'Home Office Guidelines', category: 'Políticas', views: 487, engagement: 88.7, publishDate: '2025-01-14' },
    { title: 'Novos Benefícios 2025', category: 'RH', views: 465, engagement: 86.6, publishDate: '2025-01-13' }
  ],
  categoryData: [
    { category: 'Notícias Corporativas', posts: 45, avgViews: 320, avgEngagement: 78.5 },
    { category: 'Políticas', posts: 28, avgViews: 275, avgEngagement: 82.1 },
    { category: 'RH', posts: 32, avgViews: 298, avgEngagement: 80.3 },
    { category: 'ESG', posts: 19, avgViews: 412, avgEngagement: 85.7 }
  ]
});

export async function generateCSVReport(reportData: ReportData): Promise<string> {
  const { reportType, dateRange } = reportData;
  
  if (reportType === 'engagement') {
    const realData = await collectRealEngagementData(dateRange);
    
    // Criar CSV com múltiplas seções
    let csv = 'RELATÓRIO DE ENGAJAMENTO\n';
    csv += `Gerado em: ${new Date().toLocaleString()}\n`;
    csv += `Período: ${reportData.dateRange?.from?.toLocaleDateString()} até ${reportData.dateRange?.to?.toLocaleDateString()}\n\n`;
    
    // Métricas principais
    csv += 'MÉTRICAS GERAIS\n';
    csv += 'Métrica,Valor,Tendência (%)\n';
    csv += `Total de Acessos,${realData.metrics.totalAccess},\n`;
    csv += `Usuários Únicos,${realData.metrics.uniqueUsers},\n`;
    csv += `Total de Sessões,${realData.metrics.totalSessions},\n`;
    csv += `Tempo Médio de Sessão,${realData.metrics.averageSessionTime},\n`;
    csv += `Taxa de Retenção (%),${realData.metrics.retentionRate},\n`;
    csv += `Total de Reações,${realData.metrics.reactions},${realData.metrics.reactionsTrend > 0 ? '+' : ''}${realData.metrics.reactionsTrend}\n`;
    csv += `Total de Comentários,${realData.metrics.comments},${realData.metrics.commentsTrend > 0 ? '+' : ''}${realData.metrics.commentsTrend}\n`;
    csv += `Total de Compartilhamentos,${realData.metrics.shares},${realData.metrics.sharesTrend > 0 ? '+' : ''}${realData.metrics.sharesTrend}\n\n`;
    
    // Dados diários
    if (realData.dailyData.length > 0) {
      csv += 'DADOS DIÁRIOS\n';
      csv += 'Data,Acessos,Usuários,Sessões,Média Páginas por Sessão\n';
      realData.dailyData.forEach(day => {
        csv += `${day.date},${day.accesses},${day.users},${day.sessions},${day.avgPagesPerSession}\n`;
      });
      csv += '\n';
    }
    
    // Top conteúdo
    if (realData.topContent.length > 0) {
      csv += 'CONTEÚDO MAIS ENGAJADO\n';
      csv += 'Posição,Título,Autor,Reações,Comentários,Compartilhamentos,Taxa Engajamento (%),Data Criação\n';
      realData.topContent.forEach(content => {
        csv += `${content.rank},"${sanitizeText(content.title)}","${sanitizeText(content.author)}",${content.reactions},${content.comments},${content.shares},${content.engagementRate},"${content.createdAt}"\n`;
      });
      csv += '\n';
    }
    
    // Dados por departamento
    if (realData.departmentData.length > 0) {
      csv += 'ENGAJAMENTO POR DEPARTAMENTO\n';
      csv += 'Departamento,Taxa de Engajamento (%),Número de Usuários,Total Interações\n';
      realData.departmentData.forEach(dept => {
        csv += `"${dept.department}",${dept.engagementRate},${dept.users},${dept.totalInteractions}\n`;
      });
    }
    
    return csv;
  } else {
    const realData = await collectRealContentData(dateRange);
    
    let csv = 'RELATÓRIO DE CONTEÚDO\n';
    csv += `Gerado em: ${new Date().toLocaleString()}\n`;
    csv += `Período: ${reportData.dateRange?.from?.toLocaleDateString()} até ${reportData.dateRange?.to?.toLocaleDateString()}\n\n`;
    
    // Métricas principais
    csv += 'MÉTRICAS GERAIS\n';
    csv += 'Métrica,Valor,Crescimento (%)\n';
    csv += `Total de Conteúdo,${realData.metrics.totalContent},${realData.metrics.contentGrowth > 0 ? '+' : ''}${realData.metrics.contentGrowth}\n`;
    csv += `Total de Posts,${realData.metrics.totalPosts},${realData.metrics.postsGrowth > 0 ? '+' : ''}${realData.metrics.postsGrowth}\n`;
    csv += `Total de Documentos,${realData.metrics.totalDocuments},${realData.metrics.documentsGrowth > 0 ? '+' : ''}${realData.metrics.documentsGrowth}\n\n`;
    
    // Performance de conteúdo
    if (realData.topContent.length > 0) {
      csv += 'CONTEÚDO MAIS POPULAR\n';
      csv += 'Posição,Título,Tipo,Autor,Visualizações,Interações,Categoria,Data Criação\n';
      realData.topContent.forEach(content => {
        csv += `${content.rank},"${sanitizeText(content.title)}","${content.type}","${sanitizeText(content.author)}",${content.views},${content.interactions},"${content.category || 'N/A'}","${content.createdAt}"\n`;
      });
      csv += '\n';
    }
    
    // Dados por departamento
    if (realData.departmentData.length > 0) {
      csv += 'CONTEÚDO POR DEPARTAMENTO\n';
      csv += 'Departamento,Total Conteúdo,Posts,Documentos,Taxa Engajamento (%),Total Visualizações\n';
      realData.departmentData.forEach(dept => {
        csv += `"${dept.department}",${dept.totalContent},${dept.totalPosts},${dept.totalDocuments},${dept.engagementRate},${dept.totalViews}\n`;
      });
    }
    
    return csv;
  }
}

export async function generateExcelData(reportData: ReportData): Promise<string> {
  const { reportType, dateRange } = reportData;
  
  if (reportType === 'engagement') {
    const realData = await collectRealEngagementData(dateRange);
    
    // Formato Excel-optimized CSV com múltiplas seções organizadas
    let excel = `sep=,\n`; // Indica ao Excel para usar vírgula como separador
    excel += `"RELATÓRIO DE ENGAJAMENTO - FORMATO EXCEL"\n`;
    excel += `"Gerado em:","${new Date().toLocaleString()}"\n`;
    excel += `"Período:","${reportData.dateRange?.from?.toLocaleDateString()} até ${reportData.dateRange?.to?.toLocaleDateString()}"\n`;
    excel += `\n`;
    
    // Seção de métricas principais com formatação Excel
    excel += `"=== MÉTRICAS PRINCIPAIS ==="\n`;
    excel += `"Métrica","Valor Atual","Período Anterior","Variação (%)","Status"\n`;
    excel += `"Total de Acessos",${realData.metrics.totalAccess},${realData.metrics.totalAccess - (realData.metrics.totalAccess * realData.metrics.reactionsTrend / 100)},${realData.metrics.reactionsTrend > 0 ? '+' : ''}${realData.metrics.reactionsTrend}%,${realData.metrics.reactionsTrend > 0 ? 'CRESCIMENTO' : realData.metrics.reactionsTrend < 0 ? 'DECLÍNIO' : 'ESTÁVEL'}\n`;
    excel += `"Usuários Únicos",${realData.metrics.uniqueUsers},"N/A","N/A","INFO"\n`;
    excel += `"Sessões Totais",${realData.metrics.totalSessions},"N/A","N/A","INFO"\n`;
    excel += `"Tempo Médio Sessão","${realData.metrics.averageSessionTime}","N/A","N/A","INFO"\n`;
    excel += `"Taxa de Retenção",${realData.metrics.retentionRate}%,"N/A","N/A","INFO"\n`;
    excel += `"Total Reações",${realData.metrics.reactions},${realData.metrics.reactions - (realData.metrics.reactions * realData.metrics.reactionsTrend / 100)},${realData.metrics.reactionsTrend > 0 ? '+' : ''}${realData.metrics.reactionsTrend}%,${realData.metrics.reactionsTrend > 0 ? 'CRESCIMENTO' : realData.metrics.reactionsTrend < 0 ? 'DECLÍNIO' : 'ESTÁVEL'}\n`;
    excel += `"Total Comentários",${realData.metrics.comments},${realData.metrics.comments - (realData.metrics.comments * realData.metrics.commentsTrend / 100)},${realData.metrics.commentsTrend > 0 ? '+' : ''}${realData.metrics.commentsTrend}%,${realData.metrics.commentsTrend > 0 ? 'CRESCIMENTO' : realData.metrics.commentsTrend < 0 ? 'DECLÍNIO' : 'ESTÁVEL'}\n`;
    excel += `"Total Compartilhamentos",${realData.metrics.shares},${realData.metrics.shares - (realData.metrics.shares * realData.metrics.sharesTrend / 100)},${realData.metrics.sharesTrend > 0 ? '+' : ''}${realData.metrics.sharesTrend}%,${realData.metrics.sharesTrend > 0 ? 'CRESCIMENTO' : realData.metrics.sharesTrend < 0 ? 'DECLÍNIO' : 'ESTÁVEL'}\n`;
    excel += `\n`;
    
    // Dados diários formatados para Excel
    if (realData.dailyData.length > 0) {
      excel += `"=== ENGAJAMENTO DIÁRIO ==="\n`;
      excel += `"Data","Acessos","Usuários","Sessões","Páginas/Sessão","Eficiência"\n`;
      realData.dailyData.forEach(day => {
        const efficiency = day.users > 0 ? (day.accesses / day.users).toFixed(1) : '0';
        excel += `"${day.date}",${day.accesses},${day.users},${day.sessions},${day.avgPagesPerSession},"${efficiency} acessos/usuário"\n`;
      });
      excel += `\n`;
    }
    
    // Top conteúdo com análise Excel
    if (realData.topContent.length > 0) {
      excel += `"=== TOP CONTEÚDO MAIS ENGAJADO ==="\n`;
      excel += `"Rank","Título","Autor","Reações","Comentários","Compartilhamentos","Taxa Engajamento (%)","Score Total","Data Criação","Categoria Performance"\n`;
      realData.topContent.forEach(content => {
        const totalScore = content.reactions + content.comments + (content.shares * 2); // Peso maior para shares
        const category = content.engagementRate >= 80 ? 'EXCELENTE' : content.engagementRate >= 60 ? 'BOA' : content.engagementRate >= 40 ? 'MÉDIA' : 'BAIXA';
        excel += `${content.rank},"${sanitizeText(content.title)}","${sanitizeText(content.author)}",${content.reactions},${content.comments},${content.shares},${content.engagementRate}%,${totalScore},"${content.createdAt}","${category}"\n`;
      });
      excel += `\n`;
    }
    
    // Análise por departamento com insights
    if (realData.departmentData.length > 0) {
      excel += `"=== ANÁLISE POR DEPARTAMENTO ==="\n`;
      excel += `"Departamento","Taxa Engajamento (%)","Usuários","Interações","Média Interações/Usuário","Classificação","Recomendação"\n`;
      realData.departmentData.forEach(dept => {
        const avgInteractions = dept.users > 0 ? (dept.totalInteractions / dept.users).toFixed(1) : '0';
        const classification = dept.engagementRate >= 75 ? 'ALTO' : dept.engagementRate >= 50 ? 'MÉDIO' : 'BAIXO';
        const recommendation = dept.engagementRate >= 75 ? 'Manter estratégia' : dept.engagementRate >= 50 ? 'Incrementar ações' : 'Revisar abordagem';
        excel += `"${dept.department}",${dept.engagementRate}%,${dept.users},${dept.totalInteractions},"${avgInteractions}","${classification}","${recommendation}"\n`;
      });
    }
    
    return excel;
  } else {
    const realData = await collectRealContentData(dateRange);
    
    let excel = `sep=,\n`;
    excel += `"RELATÓRIO DE CONTEÚDO - FORMATO EXCEL"\n`;
    excel += `"Gerado em:","${new Date().toLocaleString()}"\n`;
    excel += `"Período:","${reportData.dateRange?.from?.toLocaleDateString()} até ${reportData.dateRange?.to?.toLocaleDateString()}"\n`;
    excel += `\n`;
    
    // Métricas principais com análise
    excel += `"=== MÉTRICAS DE CONTEÚDO ==="\n`;
    excel += `"Métrica","Valor Atual","Crescimento (%)","Status","Meta Sugerida"\n`;
    excel += `"Total de Conteúdo",${realData.metrics.totalContent},${realData.metrics.contentGrowth > 0 ? '+' : ''}${realData.metrics.contentGrowth}%,${realData.metrics.contentGrowth > 0 ? 'CRESCIMENTO' : realData.metrics.contentGrowth < 0 ? 'DECLÍNIO' : 'ESTÁVEL'},"${Math.round(realData.metrics.totalContent * 1.1)} (+10%)"\n`;
    excel += `"Total de Posts",${realData.metrics.totalPosts},${realData.metrics.postsGrowth > 0 ? '+' : ''}${realData.metrics.postsGrowth}%,${realData.metrics.postsGrowth > 0 ? 'CRESCIMENTO' : realData.metrics.postsGrowth < 0 ? 'DECLÍNIO' : 'ESTÁVEL'},"${Math.round(realData.metrics.totalPosts * 1.15)} (+15%)"\n`;
    excel += `"Total de Documentos",${realData.metrics.totalDocuments},${realData.metrics.documentsGrowth > 0 ? '+' : ''}${realData.metrics.documentsGrowth}%,${realData.metrics.documentsGrowth > 0 ? 'CRESCIMENTO' : realData.metrics.documentsGrowth < 0 ? 'DECLÍNIO' : 'ESTÁVEL'},"${Math.round(realData.metrics.totalDocuments * 1.05)} (+5%)"\n`;
    excel += `\n`;
    
    // Top conteúdo com análise de performance
    if (realData.topContent.length > 0) {
      excel += `"=== CONTEÚDO MAIS POPULAR ==="\n`;
      excel += `"Rank","Título","Tipo","Autor","Visualizações","Interações","Categoria","Taxa Conversão","Performance","Data Criação"\n`;
      realData.topContent.forEach(content => {
        const conversionRate = content.views > 0 ? ((content.interactions / content.views) * 100).toFixed(1) : '0';
        const performance = parseFloat(conversionRate) >= 10 ? 'EXCELENTE' : parseFloat(conversionRate) >= 5 ? 'BOA' : parseFloat(conversionRate) >= 2 ? 'MÉDIA' : 'BAIXA';
        excel += `${content.rank},"${sanitizeText(content.title)}","${content.type}","${sanitizeText(content.author)}",${content.views},${content.interactions},"${content.category || 'N/A'}","${conversionRate}%","${performance}","${content.createdAt}"\n`;
      });
      excel += `\n`;
    }
    
    // Departamentos com insights estratégicos
    if (realData.departmentData.length > 0) {
      excel += `"=== PRODUÇÃO POR DEPARTAMENTO ==="\n`;
      excel += `"Departamento","Conteúdo Total","Posts","Documentos","Taxa Engajamento (%)","Visualizações","Produtividade","Foco Sugerido"\n`;
      realData.departmentData.forEach(dept => {
        const productivity = dept.totalContent > 0 ? (dept.totalViews / dept.totalContent).toFixed(0) : '0';
        const focus = dept.totalPosts > dept.totalDocuments ? 'Posts dinâmicos' : dept.totalDocuments > dept.totalPosts ? 'Documentação' : 'Balanceado';
        excel += `"${dept.department}",${dept.totalContent},${dept.totalPosts},${dept.totalDocuments},${dept.engagementRate}%,${dept.totalViews},"${productivity} views/conteúdo","${focus}"\n`;
      });
    }
    
    return excel;
  }
}

export async function generatePDFContent(reportData: ReportData): Promise<Uint8Array> {
  const { reportType, dateRange } = reportData;
  
  const realData = reportType === 'engagement' 
    ? await collectRealEngagementData(dateRange)
    : await collectRealContentData(dateRange);
  const title = reportType === 'engagement' ? 'Relatório de Engajamento' : 'Relatório de Conteúdo';
  
  // Criar novo documento PDF
  const pdf = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4'
  });

  // Configurações
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  const margin = 20;
  const lineHeight = 6;
  let currentY = margin;

  // Cores
  const primaryColor = '#f97316'; // Orange
  const textColor = '#333333';
  const lightGray = '#666666';

  // Função para adicionar nova página se necessário
  const checkNewPage = (neededHeight: number) => {
    if (currentY + neededHeight > pageHeight - margin) {
      pdf.addPage();
      currentY = margin;
      return true;
    }
    return false;
  };

  // Função para adicionar texto centralizado
  const addCenteredText = (text: string, fontSize: number, color: string = textColor, bold: boolean = false) => {
    pdf.setFontSize(fontSize);
    pdf.setTextColor(color);
    if (bold) pdf.setFont(undefined, 'bold');
    else pdf.setFont(undefined, 'normal');
    
    const textWidth = pdf.getStringUnitWidth(text) * fontSize / pdf.internal.scaleFactor;
    const x = (pageWidth - textWidth) / 2;
    pdf.text(text, x, currentY);
    currentY += lineHeight * (fontSize / 10);
  };

  // Função para adicionar texto normal
  const addText = (text: string, fontSize: number = 12, color: string = textColor, bold: boolean = false, x: number = margin) => {
    checkNewPage(lineHeight);
    pdf.setFontSize(fontSize);
    pdf.setTextColor(color);
    if (bold) pdf.setFont(undefined, 'bold');
    else pdf.setFont(undefined, 'normal');
    
    pdf.text(text, x, currentY);
    currentY += lineHeight * (fontSize / 10);
  };

  // Função para adicionar linha
  const addLine = () => {
    checkNewPage(5);
    pdf.setDrawColor(200, 200, 200);
    pdf.line(margin, currentY, pageWidth - margin, currentY);
    currentY += 5;
  };

  // CABEÇALHO
  addCenteredText(title, 24, primaryColor, true);
  addCenteredText('Relatório Completo com Métricas e Análises', 14, lightGray);
  currentY += 10;
  addLine();
  
  // INFORMAÇÕES DO RELATÓRIO
  addText('Informações do Relatório', 16, textColor, true);
  addText(`Gerado em: ${new Date().toLocaleString()}`, 12);
  addText(`Período: ${reportData.dateRange?.from?.toLocaleDateString()} até ${reportData.dateRange?.to?.toLocaleDateString()}`, 12);
  addText(`Tipo: ${title}`, 12);
  currentY += 10;
  addLine();

  if (reportType === 'engagement') {
    // MÉTRICAS PRINCIPAIS
    addText('Métricas Principais', 16, textColor, true);
    currentY += 5;

    // Criar grid de métricas (2 colunas)
    const metrics = [
      { label: 'Total de Acessos', value: realData.metrics.totalAccess.toLocaleString() },
      { label: 'Usuários Únicos', value: realData.metrics.uniqueUsers.toString() },
      { label: 'Total de Sessões', value: realData.metrics.totalSessions.toString() },
      { label: 'Tempo Médio de Sessão', value: realData.metrics.averageSessionTime },
      { label: 'Taxa de Retenção', value: `${realData.metrics.retentionRate}%` },
      { label: 'Total de Reações', value: realData.metrics.reactions.toLocaleString() },
      { label: 'Total de Comentários', value: realData.metrics.comments.toLocaleString() },
      { label: 'Total de Compartilhamentos', value: realData.metrics.shares.toLocaleString() }
    ];

    const colWidth = (pageWidth - margin * 2) / 2;
    for (let i = 0; i < metrics.length; i += 2) {
      checkNewPage(lineHeight * 3);
      
      // Primeira coluna
      pdf.setFontSize(12);
      pdf.setTextColor(textColor);
      pdf.setFont(undefined, 'bold');
      pdf.text(metrics[i].label, margin, currentY);
      pdf.setFont(undefined, 'normal');
      pdf.setFontSize(14);
      pdf.setTextColor(primaryColor);
      pdf.text(metrics[i].value, margin, currentY + lineHeight);

      // Segunda coluna (se existir)
      if (i + 1 < metrics.length) {
        pdf.setFontSize(12);
        pdf.setTextColor(textColor);
        pdf.setFont(undefined, 'bold');
        pdf.text(metrics[i + 1].label, margin + colWidth, currentY);
        pdf.setFont(undefined, 'normal');
        pdf.setFontSize(14);
        pdf.setTextColor(primaryColor);
        pdf.text(metrics[i + 1].value, margin + colWidth, currentY + lineHeight);
      }
      
      currentY += lineHeight * 3;
    }

    currentY += 10;
    addLine();

    // TOP CONTEÚDO
    if (realData.topContent.length > 0) {
      addText('Conteúdo Mais Engajado', 16, textColor, true);
      currentY += 5;

      // Cabeçalho da tabela
      pdf.setFontSize(10);
      pdf.setTextColor(textColor);
      pdf.setFont(undefined, 'bold');
      pdf.text('Pos', margin, currentY);
      pdf.text('Título', margin + 15, currentY);
      pdf.text('Autor', margin + 80, currentY);
      pdf.text('Reações', margin + 120, currentY);
      pdf.text('Taxa', margin + 150, currentY);
      currentY += lineHeight;

      // Linha separadora
      pdf.setDrawColor(200, 200, 200);
      pdf.line(margin, currentY - 2, pageWidth - margin, currentY - 2);

      // Dados da tabela
      pdf.setFont(undefined, 'normal');
      realData.topContent.slice(0, 10).forEach(content => {
        checkNewPage(lineHeight);
        
        pdf.text(content.rank.toString(), margin, currentY);
        
        // Truncar título se muito longo
        let title = sanitizeText(content.title);
        if (title.length > 35) title = title.substring(0, 32) + '...';
        pdf.text(title, margin + 15, currentY);
        
        let author = sanitizeText(content.author);
        if (author.length > 20) author = author.substring(0, 17) + '...';
        pdf.text(author, margin + 80, currentY);
        
        pdf.text(content.reactions.toString(), margin + 120, currentY);
        pdf.text(`${content.engagementRate}%`, margin + 150, currentY);
        
        currentY += lineHeight;
      });

      currentY += 10;
      addLine();
    }

    // DEPARTAMENTOS
    if (realData.departmentData.length > 0) {
      addText('Engajamento por Departamento', 16, textColor, true);
      currentY += 5;

      // Cabeçalho
      pdf.setFontSize(10);
      pdf.setTextColor(textColor);
      pdf.setFont(undefined, 'bold');
      pdf.text('Departamento', margin, currentY);
      pdf.text('Taxa Engajamento', margin + 60, currentY);
      pdf.text('Usuários', margin + 120, currentY);
      pdf.text('Interações', margin + 150, currentY);
      currentY += lineHeight;

      pdf.setDrawColor(200, 200, 200);
      pdf.line(margin, currentY - 2, pageWidth - margin, currentY - 2);

      pdf.setFont(undefined, 'normal');
      realData.departmentData.forEach(dept => {
        checkNewPage(lineHeight);
        
        pdf.text(dept.department, margin, currentY);
        pdf.text(`${dept.engagementRate}%`, margin + 60, currentY);
        pdf.text(dept.users.toString(), margin + 120, currentY);
        pdf.text(dept.totalInteractions.toString(), margin + 150, currentY);
        
        currentY += lineHeight;
      });
    }
  } else {
    // RELATÓRIO DE CONTEÚDO - Similar ao de engajamento mas com métricas diferentes
    addText('Métricas de Conteúdo', 16, textColor, true);
    currentY += 5;

    const contentMetrics = [
      { label: 'Total de Conteúdo', value: realData.metrics.totalContent.toString() },
      { label: 'Total de Posts', value: realData.metrics.totalPosts.toString() },
      { label: 'Total de Documentos', value: realData.metrics.totalDocuments.toString() },
      { label: 'Crescimento Total', value: `${realData.metrics.contentGrowth > 0 ? '+' : ''}${realData.metrics.contentGrowth}%` },
      { label: 'Crescimento Posts', value: `${realData.metrics.postsGrowth > 0 ? '+' : ''}${realData.metrics.postsGrowth}%` },
      { label: 'Crescimento Documentos', value: `${realData.metrics.documentsGrowth > 0 ? '+' : ''}${realData.metrics.documentsGrowth}%` }
    ];

    const colWidth = (pageWidth - margin * 2) / 2;
    for (let i = 0; i < contentMetrics.length; i += 2) {
      checkNewPage(lineHeight * 3);
      
      pdf.setFontSize(12);
      pdf.setTextColor(textColor);
      pdf.setFont(undefined, 'bold');
      pdf.text(contentMetrics[i].label, margin, currentY);
      pdf.setFont(undefined, 'normal');
      pdf.setFontSize(14);
      pdf.setTextColor(primaryColor);
      pdf.text(contentMetrics[i].value, margin, currentY + lineHeight);

      if (i + 1 < contentMetrics.length) {
        pdf.setFontSize(12);
        pdf.setTextColor(textColor);
        pdf.setFont(undefined, 'bold');
        pdf.text(contentMetrics[i + 1].label, margin + colWidth, currentY);
        pdf.setFont(undefined, 'normal');
        pdf.setFontSize(14);
        pdf.setTextColor(primaryColor);
        pdf.text(contentMetrics[i + 1].value, margin + colWidth, currentY + lineHeight);
      }
      
      currentY += lineHeight * 3;
    }
  }

  // RODAPÉ
  currentY = pageHeight - margin - 20;
  addLine();
  addCenteredText('Relatório gerado automaticamente pelo Vindula Cosmos', 10, lightGray);
  addCenteredText('© 2025 Vindula Internet - Todos os direitos reservados', 8, lightGray);

  // Retornar o PDF como array de bytes
  return pdf.output('arraybuffer') as Uint8Array;
}