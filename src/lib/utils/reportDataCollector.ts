/**
 * Coletor de dados reais para exportação de relatórios
 * <AUTHOR> Internet 2025
 */
import { supabase } from '@/integrations/supabase/client';
import { 
  TotalAccessMetrics,
  DailyEngagementMetrics,
  ReactionsMetrics,
  CommentsMetrics,
  SharesMetrics,
  TopEngagementContent,
  DeviceAnalytics,
  TopPageAnalytics,
  WeeklyDistributionAnalytics,
  DepartmentEngagementMetrics,
  ReactionTypeMetrics,
  HourlyDistributionMetrics,
  TotalContentMetrics,
  TopPopularContent,
  DepartmentContentMetrics,
  ContentTypeDistribution,
  AverageViewsMetrics,
  MonthlyContentEvolution,
  RetentionRateMetrics,
  formatSessionDuration
} from '@/lib/query/hooks/useAnalytics';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

export interface RealReportData {
  reportType: 'engagement' | 'content';
  dateRange?: { from?: Date; to?: Date };
  metrics: any;
  dailyData: any[];
  topContent: any[];
  departmentData: any[];
  additionalData?: any;
}

// Função auxiliar para normalizar datas
function normalizeDate(date: Date | string | undefined): Date | undefined {
  if (!date) return undefined;
  return date instanceof Date ? date : new Date(date);
}

export async function collectRealEngagementData(
  dateRange?: { from?: Date; to?: Date }
): Promise<RealReportData> {
  const safeFromDate = normalizeDate(dateRange?.from);
  const safeToDate = normalizeDate(dateRange?.to);
  
  logQueryEvent('reportDataCollector', 'Coletando dados reais de engajamento', { 
    dateFrom: safeFromDate, 
    dateTo: safeToDate 
  });

  try {
    // Coletar dados em paralelo
    const [
      totalAccessData,
      dailyEngagementData,
      reactionsData,
      commentsData,
      sharesData,
      topEngagementData,
      deviceData,
      topPagesData,
      weeklyDistData,
      departmentEngagementData,
      reactionTypesData,
      hourlyDistData,
      retentionData
    ] = await Promise.allSettled([
      // Métricas de acesso total
      supabase.rpc('get_total_access_metrics', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      }),
      // Engajamento diário
      supabase.rpc('get_daily_engagement_metrics', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      }),
      // Métricas de reações
      supabase.rpc('get_reactions_metrics', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      }),
      // Métricas de comentários
      supabase.rpc('get_comments_metrics', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      }),
      // Métricas de compartilhamentos
      supabase.rpc('get_shares_metrics', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      }),
      // Top conteúdo com engajamento
      supabase.rpc('get_top_engagement_content', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString(),
        p_limit: 10
      }),
      // Analytics por dispositivo
      supabase.rpc('get_device_analytics', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      }),
      // Páginas mais acessadas
      supabase.rpc('get_top_pages_analytics', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString(),
        p_limit: 10
      }),
      // Distribuição semanal
      supabase.rpc('get_weekly_distribution_analytics', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      }),
      // Engajamento por departamento
      supabase.rpc('get_department_engagement_metrics', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      }),
      // Tipos de reação
      supabase.rpc('get_reaction_types_metrics', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      }),
      // Distribuição horária
      supabase.rpc('get_hourly_distribution_metrics', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      }),
      // Retenção
      supabase.rpc('get_retention_rate', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      })
    ]);

    // Processar resultados
    const totalAccess: TotalAccessMetrics = totalAccessData.status === 'fulfilled' && totalAccessData.value.data?.[0] 
      ? totalAccessData.value.data[0] 
      : {
          total_page_views: 0,
          unique_users: 0,
          total_sessions: 0,
          avg_session_duration: '00:00:00',
          current_period_start: safeFromDate?.toISOString() || new Date().toISOString(),
          current_period_end: safeToDate?.toISOString() || new Date().toISOString(),
          previous_period_total_page_views: 0,
          previous_period_unique_users: 0,
          previous_period_total_sessions: 0
        };

    const reactions: ReactionsMetrics = reactionsData.status === 'fulfilled' && reactionsData.value.data?.[0]
      ? reactionsData.value.data[0]
      : {
          total_reactions: 0,
          post_likes: 0,
          message_reactions: 0,
          unique_users_reacting: 0,
          current_period_start: safeFromDate?.toISOString() || new Date().toISOString(),
          current_period_end: safeToDate?.toISOString() || new Date().toISOString(),
          previous_period_total_reactions: 0,
          previous_period_post_likes: 0,
          previous_period_message_reactions: 0,
          previous_period_unique_users: 0
        };

    const comments: CommentsMetrics = commentsData.status === 'fulfilled' && commentsData.value.data?.[0]
      ? commentsData.value.data[0]
      : {
          total_comments: 0,
          post_comments: 0,
          unique_users_commenting: 0,
          current_period_start: safeFromDate?.toISOString() || new Date().toISOString(),
          current_period_end: safeToDate?.toISOString() || new Date().toISOString(),
          previous_period_total_comments: 0,
          previous_period_post_comments: 0,
          previous_period_unique_users: 0
        };

    const shares: SharesMetrics = sharesData.status === 'fulfilled' && sharesData.value.data?.[0]
      ? sharesData.value.data[0]
      : {
          total_shares: 0,
          post_shares: 0,
          unique_users_sharing: 0,
          current_period_start: safeFromDate?.toISOString() || new Date().toISOString(),
          current_period_end: safeToDate?.toISOString() || new Date().toISOString(),
          previous_period_total_shares: 0,
          previous_period_post_shares: 0,
          previous_period_unique_users: 0
        };

    const retention: RetentionRateMetrics = retentionData.status === 'fulfilled' && retentionData.value.data?.[0]
      ? retentionData.value.data[0]
      : {
          retention_rate: 0,
          current_period_users: 0,
          returning_users: 0,
          previous_period_retention_rate: 0,
          data_available_since: null,
          data_available_until: null,
          period_requested_days: 30,
          period_available_days: 0,
          explanation_key: 'NO_DATA',
          details: {
            scenario: 'Nenhum dado disponível',
            cause: 'Sistema ainda não coletou dados suficientes',
            suggestion: 'Aguarde usuários navegarem no sistema'
          }
        };

    return {
      reportType: 'engagement',
      dateRange,
      metrics: {
        totalAccess: totalAccess.total_page_views,
        uniqueUsers: totalAccess.unique_users,
        totalSessions: totalAccess.total_sessions,
        averageSessionTime: formatSessionDuration(totalAccess.avg_session_duration),
        retentionRate: retention.retention_rate,
        reactions: reactions.total_reactions,
        comments: comments.total_comments,
        shares: shares.total_shares,
        reactionsTrend: reactions.previous_period_total_reactions > 0 
          ? Math.round(((reactions.total_reactions - reactions.previous_period_total_reactions) / reactions.previous_period_total_reactions) * 100)
          : 0,
        commentsTrend: comments.previous_period_total_comments > 0
          ? Math.round(((comments.total_comments - comments.previous_period_total_comments) / comments.previous_period_total_comments) * 100)
          : 0,
        sharesTrend: shares.previous_period_total_shares > 0
          ? Math.round(((shares.total_shares - shares.previous_period_total_shares) / shares.previous_period_total_shares) * 100)
          : 0
      },
      dailyData: dailyEngagementData.status === 'fulfilled' && dailyEngagementData.value.data 
        ? dailyEngagementData.value.data.map((day: DailyEngagementMetrics) => ({
            date: day.date_day,
            accesses: day.total_page_views,
            users: day.unique_users,
            sessions: day.unique_sessions,
            avgPagesPerSession: day.avg_pages_per_session
          }))
        : [],
      topContent: topEngagementData.status === 'fulfilled' && topEngagementData.value.data
        ? topEngagementData.value.data.map((content: TopEngagementContent, index: number) => ({
            rank: index + 1,
            title: content.post_title && content.post_title.trim() 
              ? content.post_title.replace(/[^\w\s\-.,!?]/g, '').trim()
              : `Post de ${content.author_name || 'Autor desconhecido'}`,
            author: content.author_name || 'Autor desconhecido',
            reactions: content.total_reactions || 0,
            comments: content.total_comments || 0,
            shares: content.total_shares || 0,
            engagementRate: Math.round(content.engagement_rate || 0),
            createdAt: content.created_at
          }))
        : [],
      departmentData: departmentEngagementData.status === 'fulfilled' && departmentEngagementData.value.data
        ? departmentEngagementData.value.data.map((dept: DepartmentEngagementMetrics) => ({
            department: dept.department_name,
            engagementRate: dept.engagement_rate,
            users: dept.user_count,
            totalInteractions: dept.total_interactions
          }))
        : [],
      additionalData: {
        deviceAnalytics: deviceData.status === 'fulfilled' ? deviceData.value.data || [] : [],
        topPages: topPagesData.status === 'fulfilled' ? topPagesData.value.data || [] : [],
        weeklyDistribution: weeklyDistData.status === 'fulfilled' ? weeklyDistData.value.data || [] : [],
        reactionTypes: reactionTypesData.status === 'fulfilled' ? reactionTypesData.value.data || [] : [],
        hourlyDistribution: hourlyDistData.status === 'fulfilled' ? hourlyDistData.value.data || [] : []
      }
    };

  } catch (error) {
    logQueryEvent('reportDataCollector', 'Erro ao coletar dados de engajamento', error, 'error');
    throw error;
  }
}

export async function collectRealContentData(
  dateRange?: { from?: Date; to?: Date }
): Promise<RealReportData> {
  const safeFromDate = normalizeDate(dateRange?.from);
  const safeToDate = normalizeDate(dateRange?.to);
  
  logQueryEvent('reportDataCollector', 'Coletando dados reais de conteúdo', { 
    dateFrom: safeFromDate, 
    dateTo: safeToDate 
  });

  try {
    // Coletar dados em paralelo
    const [
      totalContentData,
      topPopularData,
      departmentContentData,
      contentTypeDistData,
      averageViewsData,
      monthlyEvolutionData
    ] = await Promise.allSettled([
      // Métricas totais de conteúdo
      supabase.rpc('get_total_content_metrics', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      }),
      // Conteúdo mais popular
      supabase.rpc('get_top_popular_content', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString(),
        p_limit: 10
      }),
      // Conteúdo por departamento
      supabase.rpc('get_department_content_metrics', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      }),
      // Distribuição por tipo
      supabase.rpc('get_content_type_distribution', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      }),
      // Visualizações médias
      supabase.rpc('get_average_views_metrics', {
        p_date_from: safeFromDate?.toISOString(),
        p_date_to: safeToDate?.toISOString()
      }),
      // Evolução mensal
      supabase.rpc('get_monthly_content_evolution', {
        p_months_back: 12
      })
    ]);

    // Processar resultados
    const totalContent: TotalContentMetrics = totalContentData.status === 'fulfilled' && totalContentData.value.data?.[0]
      ? totalContentData.value.data[0]
      : {
          total_content: 0,
          posts_count: 0,
          documents_count: 0,
          current_period_start: safeFromDate?.toISOString() || new Date().toISOString(),
          current_period_end: safeToDate?.toISOString() || new Date().toISOString(),
          previous_period_total_content: 0,
          previous_period_posts_count: 0,
          previous_period_documents_count: 0,
          content_growth_percentage: 0,
          posts_growth_percentage: 0,
          documents_growth_percentage: 0
        };

    return {
      reportType: 'content',
      dateRange,
      metrics: {
        totalContent: totalContent.total_content,
        totalPosts: totalContent.posts_count,
        totalDocuments: totalContent.documents_count,
        contentGrowth: totalContent.content_growth_percentage,
        postsGrowth: totalContent.posts_growth_percentage,
        documentsGrowth: totalContent.documents_growth_percentage
      },
      dailyData: [], // Dados diários específicos de conteúdo (pode ser implementado posteriormente)
      topContent: topPopularData.status === 'fulfilled' && topPopularData.value.data
        ? topPopularData.value.data.map((content: TopPopularContent, index: number) => ({
            rank: index + 1,
            title: content.content_title && content.content_title.trim()
              ? content.content_title.replace(/[^\w\s\-.,!?]/g, '').trim()
              : `${content.content_type === 'post' ? 'Post' : 'Documento'} de ${content.author_name || 'Autor desconhecido'}`,
            type: content.content_type === 'post' ? 'Post' : 'Documento',
            author: content.author_name || 'Autor desconhecido',
            views: content.total_views || 0,
            interactions: content.total_interactions || 0,
            category: content.category_name || 'Sem categoria',
            createdAt: content.created_at
          }))
        : [],
      departmentData: departmentContentData.status === 'fulfilled' && departmentContentData.value.data
        ? departmentContentData.value.data.map((dept: DepartmentContentMetrics) => ({
            department: dept.department_name,
            totalContent: dept.total_content,
            totalPosts: dept.total_posts,
            totalDocuments: dept.total_documents,
            engagementRate: dept.engagement_rate,
            totalViews: dept.total_views
          }))
        : [],
      additionalData: {
        contentTypeDistribution: contentTypeDistData.status === 'fulfilled' ? contentTypeDistData.value.data || [] : [],
        averageViews: averageViewsData.status === 'fulfilled' ? averageViewsData.value.data || [] : [],
        monthlyEvolution: monthlyEvolutionData.status === 'fulfilled' ? monthlyEvolutionData.value.data || [] : []
      }
    };

  } catch (error) {
    logQueryEvent('reportDataCollector', 'Erro ao coletar dados de conteúdo', error, 'error');
    throw error;
  }
}