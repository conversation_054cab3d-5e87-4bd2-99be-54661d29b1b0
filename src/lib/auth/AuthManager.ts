/**
 * AuthManager - Sistema central de autenticação ultra-otimizado
 * Coordena cache, realtime e API do Supabase de forma transparente
 * <AUTHOR> Internet 2025
 */

import { supabase } from '@/integrations/supabase/client';
import { Session, User } from '@supabase/supabase-js';
import { authCache, permissionCache } from './CacheManager';
import { localStorageManager } from './LocalStorageManager';
import { realtimeManager } from './RealtimeManager';
import { queryClient } from '@/lib/query';

interface AuthState {
  session: Session | null;
  user: User | null;
  company_id: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  lastActivity: number;
}

type AuthStateListener = (state: AuthState) => void;

/**
 * TTL Configurations (em milissegundos)
 */
const TTL = {
  USER_BASIC: 24 * 60 * 60 * 1000,      // 24h - dados básicos do usuário
  COMPANY_ID: 60 * 60 * 1000,           // 1h - company_id
  USER_ROLES: 60 * 60 * 1000,           // 1h - roles do usuário
  PERMISSIONS: 5 * 60 * 1000,           // 5min - permissões específicas
  SESSION_HEALTH: 30 * 60 * 1000,       // 30min - health da sessão
} as const;

/**
 * Cache Keys Padronizados
 */
const CACHE_KEYS = {
  user: (userId: string) => `user:${userId}:basic`,
  companyId: (userId: string) => `user:${userId}:company_id`,
  userRoles: (userId: string) => `user:${userId}:roles`,
  permission: (userId: string, resourceType: string, action: string, resourceId?: string) => 
    `permission:${userId}:${resourceType}:${action}${resourceId ? `:${resourceId}` : ''}`,
  bulkPermissions: (userId: string, permissionKeys: string) => 
    `permission:${userId}:bulk:${btoa(permissionKeys)}`,
  sessionHealth: (userId: string) => `session:${userId}:health`,
} as const;

/**
 * Manager principal de autenticação com cache inteligente
 */
export class AuthManager {
  private currentState: AuthState;
  private listeners: Set<AuthStateListener> = new Set();
  private initializePromise: Promise<void> | null = null;
  private isInitialized = false;
  private supabaseAuthUnsubscribe: (() => void) | null = null;

  constructor() {
    this.currentState = {
      session: null,
      user: null,
      company_id: null,
      isAuthenticated: false,
      isLoading: true,
      lastActivity: Date.now(),
    };

    this.setupRealtimeInvalidation();
    this.setupSupabaseAuthListener();
  }

  /**
   * Inicializar de forma otimista e ultra-rápida
   */
  /**
   * Configurar listener para o onAuthStateChange do Supabase.
   */
  private setupSupabaseAuthListener(): void {
    // Configurando listener onAuthStateChange
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      // Auth state change event processado

      // Chamar handleSessionChange para processar a nova sessão ou ausência dela
      // Isso garante que o estado do AuthManager seja atualizado em tempo real
      await this.handleSessionChange(session);
    });

    this.supabaseAuthUnsubscribe = authListener?.unsubscribe || null;
    if (!this.supabaseAuthUnsubscribe) {
      // Unsubscribe não disponível para onAuthStateChange
    }
  }

  /**
   * Inicializar de forma otimista e ultra-rápida
   */
  public async initialize(): Promise<void> {
    if (this.initializePromise) {
      return this.initializePromise;
    }

    this.initializePromise = this._doInitialize();
    return this.initializePromise;
  }

  private async _doInitialize(): Promise<void> {
    // Iniciando carregamento otimista
    
    try {
      // PASSO 1: Carregar estado do cache instantaneamente
      const cachedState = this.loadCachedState();
      if (cachedState) {
        this.updateState({
          ...cachedState,
          isLoading: false, // Sempre false para cache
        });
        // Cache carregado instantaneamente
      } else {
        // Cache não encontrado, verificando Supabase
        
        // FALLBACK: Se cache não funcionou, verificar Supabase IMEDIATAMENTE
        try {
          const { data, error } = await supabase.auth.getSession();
          if (!error && data.session?.user) {
            // Sessão encontrada no Supabase
            
            // Inicializar estado básico IMEDIATAMENTE
            this.updateState({
              session: data.session,
              user: data.session.user,
              company_id: null, // Será carregado em background
              isAuthenticated: true,
              isLoading: false,
              lastActivity: Date.now(),
            });
            
            // Cachear sessão
            this.cacheSession(data.session);
            
            // Buscar company_id em background
            this.fetchCompanyIdInBackground(data.session.user.id);
          } else {
            // Nenhuma sessão encontrada no Supabase
            this.updateState({
              session: null,
              user: null,
              company_id: null,
              isAuthenticated: false,
              isLoading: false,
              lastActivity: Date.now(),
            });
          }
        } catch (error) {
          console.error('[AuthManager] ⚠️ Erro ao verificar sessão do Supabase:', error);
          this.updateState({
            session: null,
            user: null,
            company_id: null,
            isAuthenticated: false,
            isLoading: false,
            lastActivity: Date.now(),
          });
        }
      }

      // PASSO 2: Validar sessão e buscar company_id em background (sem bloquear UI)
      this.validateSessionInBackground();
      
      // PASSO 2.5: Se temos user mas não company_id, start pre-loading imediatamente
      if (cachedState?.user?.id && !cachedState.company_id) {
        // Pre-loading company_id
        this.fetchCompanyIdInBackground(cachedState.user.id);
      }

      // PASSO 3: Marcar como inicializado
      this.isInitialized = true;
      // Inicialização concluída

    } catch (error) {
      console.error('[AuthManager] 💥 Erro na inicialização:', error);
      this.updateState({
        session: null,
        user: null,
        company_id: null,
        isAuthenticated: false,
        isLoading: false, // Sempre false, nunca bloquear
        lastActivity: Date.now(),
      });
    }
  }

  /**
   * Carregar estado do cache (instantâneo)
   */
  private loadCachedState(): Partial<AuthState> | null {
    try {
      // Tentar carregar sessão do localStorage primeiro
      const sessionData = localStorageManager.get<{session: Session, user: User}>('auth:session');
      
      if (sessionData?.session && sessionData?.user) {
        const userId = sessionData.user.id;
        
        // Carregar company_id do cache
        const cachedCompanyId = localStorageManager.get<string>(CACHE_KEYS.companyId(userId)) ||
                               authCache.get<string>(CACHE_KEYS.companyId(userId));

        // Estado carregado do cache local

        return {
          session: sessionData.session,
          user: sessionData.user,
          company_id: cachedCompanyId,
          isAuthenticated: true,
          lastActivity: Date.now(),
        };
      }

      return null;
    } catch (error) {
      // Erro ao carregar cache
      return null;
    }
  }

  /**
   * Validar sessão em background (não bloqueia UI)
   * OTIMIZADO: Prioriza company_id loading para máxima performance
   */
  private async validateSessionInBackground(): Promise<void> {
    try {
      // Validando sessão em background
      
      const startTime = performance.now();
      const { data, error } = await supabase.auth.getSession();
      const sessionTime = performance.now() - startTime;
      
      if (error) {
        console.error('[AuthManager] Erro na validação:', error);
        this.handleInvalidSession();
        return;
      }

      const currentUser = this.currentState.user;
      const sessionUser = data.session?.user;

      // Se sessão mudou ou é diferente do cache
      if (!this.sessionsEqual(this.currentState.session, data.session)) {
        // Sessão alterada e validada
        await this.handleSessionChange(data.session);
      } else if (sessionUser && (!currentUser || currentUser.id !== sessionUser.id)) {
        // Usuário alterado e validado
        await this.handleSessionChange(data.session);
      } else {
        // Sessão validada com sucesso
        
        // PRIORIDADE MÁXIMA: company_id se usuário autenticado
        if (data.session?.user) {
          if (!this.currentState.company_id) {
            // Company_id ausente, buscando
            // Não await para não bloquear, mas start imediatamente
            this.fetchCompanyIdInBackground(data.session.user.id);
          } else {
            // Company_id disponível
            
            // Verificar cache periodicamente para manter fresh - apenas se não há promise em andamento
            if (!this.companyIdPromiseCache.has(data.session.user.id)) {
              const cacheKey = CACHE_KEYS.companyId(data.session.user.id);
              const cachedTime = authCache.getTimestamp(cacheKey);
              const now = Date.now();
              
              // Se cache está velho (> 30min), renovar em background
              if (cachedTime && (now - cachedTime) > (30 * 60 * 1000)) {
                // Cache antigo, renovando em background
                this.fetchCompanyIdInBackground(data.session.user.id);
              }
            }
          }
        }
      }

    } catch (error) {
      console.error('[AuthManager] Erro na validação background:', error);
    }
  }

  /**
   * Comparar sessões para detectar mudanças
   */
  private sessionsEqual(session1: Session | null, session2: Session | null): boolean {
    if (!session1 && !session2) return true;
    if (!session1 || !session2) return false;
    
    return (
      session1.access_token === session2.access_token &&
      session1.user.id === session2.user.id &&
      session1.expires_at === session2.expires_at
    );
  }

  /**
   * Handle mudança de sessão
   * OTIMIZADO: Carrega company_id de forma assíncrona se necessário
   */
  private async handleSessionChange(newSession: Session | null): Promise<void> {
    if (!newSession) {
      this.handleInvalidSession();
      return;
    }

    const userId = newSession.user.id;
    let companyId = this.currentState.company_id;
    const userChanged = !this.currentState.user || this.currentState.user.id !== userId;

    // Se usuário mudou, tentar carregar company_id do cache primeiro
    if (userChanged) {
      const cacheKey = CACHE_KEYS.companyId(userId);
      
      // Busca rápida do cache primeiro
      companyId = authCache.get<string>(cacheKey) || localStorageManager.get<string>(cacheKey);
      
      if (companyId) {
        console.log('[AuthManager] ⚡ Company ID do cache para novo usuário:', companyId);
      } else {
        console.log('[AuthManager] 🔍 Company ID não encontrado no cache, buscando...');
        // Buscar de forma assíncrona para não bloquear UI
        this.fetchCompanyIdInBackground(userId);
      }
    }

    // Atualizar estado IMEDIATAMENTE (mesmo sem company_id)
    this.updateState({
      session: newSession,
      user: newSession.user,
      company_id: companyId, // Pode ser null temporariamente
      isAuthenticated: true,
      isLoading: false,
      lastActivity: Date.now(),
    });

    // Cachear nova sessão
    this.cacheSession(newSession);
    
    console.log('[AuthManager] ✅ Sessão atualizada. Company_id:', companyId || 'será carregado em background');
  }

  /**
   * Handle sessão inválida
   */
  private handleInvalidSession(): void {
    console.log('[AuthManager] ❌ Sessão inválida, limpando estado...');
    
    this.updateState({
      session: null,
      user: null,
      company_id: null,
      isAuthenticated: false,
      isLoading: false,
      lastActivity: Date.now(),
    });

    this.clearAllCache();
  }

  /**
   * Cache de company_id em memória para acesso ultra-rápido
   * Evita múltiplas consultas ao mesmo company_id
   */
  private companyIdPromiseCache = new Map<string, Promise<string | null>>();

  /**
   * Buscar company_id (com cache ultra-otimizado)
   */
  public async fetchCompanyId(userId: string): Promise<string | null> {
    const cacheKey = CACHE_KEYS.companyId(userId);
    
    // NÍVEL 1: Cache em memória (instantâneo)
    let companyId = authCache.get<string>(cacheKey);
    if (companyId) {
      console.log('[AuthManager] ⚡ Company ID do cache em memória:', companyId);
      return companyId;
    }

    // NÍVEL 2: Cache localStorage (muito rápido)
    companyId = localStorageManager.get<string>(cacheKey);
    if (companyId) {
      console.log('[AuthManager] 📦 Company ID do localStorage:', companyId);
      // Promover para cache em memória
      authCache.set(cacheKey, companyId, TTL.COMPANY_ID);
      return companyId;
    }

    // NÍVEL 3: Promise cache para evitar requests duplicados
    if (this.companyIdPromiseCache.has(userId)) {
      console.log('[AuthManager] ⏳ Aguardando promise em cache para userId:', userId);
      return this.companyIdPromiseCache.get(userId)!;
    }

    // NÍVEL 4: Busca do banco (otimizada com promise cache)
    console.log('[AuthManager] 🔍 BUSCA ULTRA-OTIMIZADA de company_id do banco...');
    
    const fetchPromise = this._fetchCompanyIdFromDatabase(userId, cacheKey);
    
    // Cachear a promise para evitar requests duplicados
    this.companyIdPromiseCache.set(userId, fetchPromise);
    
    // Limpar promise cache após conclusão (sucesso ou erro)
    fetchPromise.finally(() => {
      this.companyIdPromiseCache.delete(userId);
    });
    
    return fetchPromise;
  }

  /**
   * Busca real do banco de dados (com retry resiliente e timeout)
   */
  /**
   * Busca real do banco de dados com validação de profile ativo
   * (com retry resiliente e timeout)
   */
  private async _fetchCompanyIdFromDatabase(userId: string, cacheKey: string): Promise<string | null> {
    const maxRetries = 3;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const startTime = performance.now();
      
      try {
        console.log(`[AuthManager] 🔍 Tentativa ${attempt}/${maxRetries} para buscar profile data...`);
        
        // Timeout escalonado: 8s, 12s, 15s
        const timeoutMs = 5000 + (attempt * 3000);
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error(`Timeout na tentativa ${attempt}/${maxRetries}`)), timeoutMs);
        });
        
        // 🚨 CORREÇÃO DE SEGURANÇA: Buscar company_id E status ativo
        const dbPromise = supabase
          .from('profiles')
          .select('company_id, active')  // ✅ Buscar ambos os campos
          .eq('id', userId)
          .maybeSingle();
        
        const { data, error } = await Promise.race([dbPromise, timeoutPromise]);

        const elapsed = performance.now() - startTime;
        
        if (error) {
          console.error(`[AuthManager] ❌ Erro na tentativa ${attempt} (${elapsed.toFixed(1)}ms):`, error);
          
          // Se é o último retry, log detalhado
          if (attempt === maxRetries) {
            console.error('[AuthManager] Detalhes finais do erro:', {
              code: error.code,
              message: error.message,
              details: error.details,
              hint: error.hint
            });
          }
          
          // Continue para próximo retry
          continue;
        }

        // 🚨 VALIDAÇÃO CRÍTICA DE SEGURANÇA: Verificar se profile está ativo
        if (data?.active === false) {
          console.warn(`[AuthManager] 🚫 PROFILE INATIVO detectado para userId: ${userId}`);
          console.warn('[AuthManager] Forçando logout por segurança...');
          
          // Não cachear - forçar logout imediato
          // Lançar erro especial para indicar profile inativo
          throw new Error('PROFILE_INACTIVE');
        }

        const companyId = data?.company_id || null;
        const isActive = data?.active !== false; // true por padrão se não especificado

        if (!isActive) {
          console.warn(`[AuthManager] 🚫 PROFILE INATIVO na tentativa ${attempt} para userId: ${userId}`);
          throw new Error('PROFILE_INACTIVE');
        }

        if (companyId) {
          // Cache triplo para máxima performance
          authCache.set(cacheKey, companyId, TTL.COMPANY_ID);
          localStorageManager.set(cacheKey, companyId, TTL.COMPANY_ID);
          
          console.log(`[AuthManager] ✅ Profile ativo com company_id obtido na tentativa ${attempt} em ${elapsed.toFixed(1)}ms:`, companyId);
          return companyId;
        } else {
          console.warn(`[AuthManager] ⚠️ Profile ativo mas company_id não encontrado na tentativa ${attempt} (${elapsed.toFixed(1)}ms)`);
          console.warn('[AuthManager] Data recebida:', data);
          
          // Profile ativo mas sem company_id - retornar null (não é erro de segurança)
          return null;
        }

      } catch (error) {
        const elapsed = performance.now() - startTime;
        
        // 🚨 TRATAMENTO ESPECIAL PARA PROFILE INATIVO
        if (error instanceof Error && error.message === 'PROFILE_INACTIVE') {
          console.error(`[AuthManager] 🚫 PROFILE INATIVO confirmado após ${elapsed.toFixed(1)}ms. Forçando logout...`);
          // Não fazer retry - profile inativo é definitivo
          throw error;
        }
        
        console.error(`[AuthManager] 💥 Exceção na tentativa ${attempt}/${maxRetries} (${elapsed.toFixed(1)}ms):`, error);
        
        // Se não é o último retry, aguardar antes de tentar novamente
        if (attempt < maxRetries) {
          const backoffMs = attempt * 1000; // 1s, 2s backoff
          console.log(`[AuthManager] ⏳ Aguardando ${backoffMs}ms antes do próximo retry...`);
          await new Promise(resolve => setTimeout(resolve, backoffMs));
        }
      }
    }
    
    // Todas as tentativas falharam
    console.error(`[AuthManager] 🚨 Falha total após ${maxRetries} tentativas para userId: ${userId}`);
    return null;
  }

  /**
   * Buscar company_id em background (não bloqueia)
   * ULTRA-RESILIENTE: Sistema continua funcionando mesmo com falhas temporárias
   */
  /**
   * Buscar company_id em background (não bloqueia)
   * ULTRA-RESILIENTE: Sistema continua funcionando mesmo com falhas temporárias
   * 🚨 SEGURANÇA: Força logout se profile estiver inativo
   */
  private async fetchCompanyIdInBackground(userId: string): Promise<void> {
    try {
      const startTime = performance.now();
      
      const companyId = await this.fetchCompanyId(userId);
      
      const elapsed = performance.now() - startTime;
      
      if (companyId && companyId !== this.currentState.company_id) {
        console.log(`[AuthManager] 🏢 Company ID atualizado em background em ${elapsed.toFixed(1)}ms:`, companyId);
        this.updateState({
          ...this.currentState,
          company_id: companyId,
        });
      } else if (companyId === null && !this.currentState.company_id) {
        console.warn('[AuthManager] 🚨 Company ID não pôde ser carregado. Sistema continua funcionando com funcionalidade limitada.');
        // Sistema continua funcionando - permissões que não dependem de company_id funcionarão
      } else if (elapsed > 0.1) {
        console.log(`[AuthManager] 📊 Company ID background fetch: ${elapsed.toFixed(1)}ms (${companyId ? 'encontrado' : 'não encontrado'})`);
      }
    } catch (error) {
      // 🚨 TRATAMENTO CRÍTICO PARA PROFILE INATIVO
      if (error instanceof Error && error.message === 'PROFILE_INACTIVE') {
        console.error('[AuthManager] 🚫 PROFILE INATIVO detectado durante background fetch. Executando logout forçado...');
        
        // Forçar logout imediato por segurança
        try {
          await this.signOut();
          console.log('[AuthManager] ✅ Logout forçado executado com sucesso devido a profile inativo');
        } catch (logoutError) {
          console.error('[AuthManager] ❌ Erro durante logout forçado:', logoutError);
          // Mesmo que o logout falhe, limpar estado local
          this.handleInvalidSession();
        }
        
        return; // Não fazer retry para profile inativo
      }
      
      console.error('[AuthManager] ⚠️ Erro ao buscar company_id em background (sistema continua funcionando):', error);
      
      // Implementar retry automático após um tempo se for crítico
      if (!this.currentState.company_id) {
        console.log('[AuthManager] 🔄 Agendando retry automático em 30 segundos...');
        setTimeout(() => {
          console.log('[AuthManager] 🔄 Executando retry automático para company_id...');
          this.fetchCompanyIdInBackground(userId);
        }, 30000);
      }
    }
  }

  /**
   * Verificar permissão única (com cache ultra-otimizado)
   */
  public async checkPermission(
    resourceTypeKey: string,
    actionKey: string,
    resourceId?: string | null,
    userId?: string
  ): Promise<boolean> {
    const currentUserId = userId || this.currentState.user?.id;
    
    if (!currentUserId || !this.currentState.isAuthenticated) {
      return false;
    }

    const cacheKey = CACHE_KEYS.permission(currentUserId, resourceTypeKey, actionKey, resourceId || undefined);
    
    // Verificar cache primeiro (ultra-rápido)
    const cached = permissionCache.get<boolean>(cacheKey);
    if (cached !== null) {
      console.log('[AuthManager] ⚡ Permissão do cache:', cached);
      return cached;
    }

    // Buscar do banco
    try {
      const { data, error } = await supabase.rpc('check_permission_v2_rpc', {
        p_user_id: currentUserId,
        p_resource_type_key: resourceTypeKey,
        p_action_key: actionKey,
        p_resource_id: resourceId,
      });

      if (error) {
        console.error('[AuthManager] Erro ao verificar permissão:', error);
        return false;
      }

      const hasPermission = !!data;
      
      // Cachear resultado
      permissionCache.set(cacheKey, hasPermission, TTL.PERMISSIONS);
      
      console.log('[AuthManager] ✅ Permissão verificada e cacheada:', {
        resourceTypeKey,
        actionKey,
        resourceId,
        hasPermission
      });

      return hasPermission;
    } catch (error) {
      console.error('[AuthManager] Exceção ao verificar permissão:', error);
      return false;
    }
  }

  /**
   * Verificar múltiplas permissões de uma vez (bulk)
   */
  public async checkPermissionsBulk(
    permissions: Array<{
      resourceTypeKey: string;
      actionKey: string;
      resourceId?: string | null;
    }>,
    userId?: string
  ): Promise<Record<string, boolean>> {
    const currentUserId = userId || this.currentState.user?.id;
    
    if (!currentUserId || !this.currentState.isAuthenticated) {
      return {};
    }

    const result: Record<string, boolean> = {};
    const uncachedPermissions: typeof permissions = [];

    // Verificar cache primeiro para todas as permissões
    for (const perm of permissions) {
      const cacheKey = CACHE_KEYS.permission(currentUserId, perm.resourceTypeKey, perm.actionKey, perm.resourceId || undefined);
      const cached = permissionCache.get<boolean>(cacheKey);
      
      if (cached !== null) {
        result[`${perm.resourceTypeKey}_${perm.actionKey}`] = cached;
      } else {
        uncachedPermissions.push(perm);
      }
    }

    // Buscar permissões não cacheadas
    if (uncachedPermissions.length > 0) {
      console.log('[AuthManager] 🔍 Buscando permissões bulk:', uncachedPermissions.length);
      
      // Fazer queries em paralelo para performance
      const promises = uncachedPermissions.map(perm => 
        this.checkPermission(perm.resourceTypeKey, perm.actionKey, perm.resourceId, currentUserId)
          .then(hasPermission => ({
            key: `${perm.resourceTypeKey}_${perm.actionKey}`,
            hasPermission
          }))
      );

      try {
        const results = await Promise.all(promises);
        results.forEach(({ key, hasPermission }) => {
          result[key] = hasPermission;
        });
      } catch (error) {
        console.error('[AuthManager] Erro em bulk permissions:', error);
      }
    }

    return result;
  }

  /**
   * Logout completo
   */
  /**
   * Limpar o listener do Supabase ao deslogar ou destruir a instância (se aplicável)
   * Embora o AuthManager seja um singleton, é uma boa prática ter um método de cleanup.
   */
  public disposeSupabaseAuthListener(): void {
    if (this.supabaseAuthUnsubscribe) {
      console.log('[AuthManager] 🔌 Removendo listener onAuthStateChange do Supabase.');
      this.supabaseAuthUnsubscribe();
      this.supabaseAuthUnsubscribe = null;
    }
  }

  // Logout completo
  public async signOut(): Promise<void> {
    console.log('[AuthManager] 🚪 Iniciando logout completo...');
    
    try {
      // --- Início: Registrar Fim da Sessão ---
      const sessionIdFromStorage = localStorage.getItem('userSessionId');
      console.log('[AuthManager] 📊 Valor lido de userSessionId:', sessionIdFromStorage);

      if (sessionIdFromStorage) {
        localStorage.removeItem('userSessionId');
        console.log('[AuthManager] 🗑️ userSessionId removido do localStorage');
        
        try {
          const rpcPromise = supabase.rpc('log_user_session_end', {
            p_session_id: sessionIdFromStorage,
          });
          
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Timeout ao registrar fim da sessão')), 3000);
          });
          
          const { error: rpcError } = await Promise.race([rpcPromise, timeoutPromise]) as any;
          
          if (rpcError) {
            console.error('[AuthManager] ❌ Erro ao chamar log_user_session_end:', rpcError.message);
          } else {
            console.log('[AuthManager] ✅ Fim da sessão registrado com sucesso via RPC');
          }
        } catch (error) {
          console.error('[AuthManager] ❌ Erro inesperado ao chamar log_user_session_end:', error);
        }
      } else {
        console.warn('[AuthManager] ⚠️ userSessionId não encontrado no localStorage');
      }
      
      // --- Salvar informações do último usuário ---
      try {
        if (this.currentState.user) {
          console.log('[AuthManager] 💾 Salvando informações do último usuário...');
          
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('full_name, avatar_url')
            .eq('id', this.currentState.user.id)
            .single();
          
          if (profileError) {
            console.error('[AuthManager] ❌ Erro ao buscar perfil:', profileError.message);
          }
          
          const userName = profileData?.full_name || 
                          this.currentState.user.user_metadata?.name || 
                          this.currentState.user.user_metadata?.full_name || 
                          this.currentState.user.email?.split('@')[0] || 
                          '';
                          
          const avatarUrl = profileData?.avatar_url || 
                           this.currentState.user.user_metadata?.avatar_url || 
                           '';
          
          const lastUserInfo = {
            email: this.currentState.user.email,
            name: userName,
            avatarUrl: avatarUrl,
            lastLogin: new Date().toISOString()
          };
          
          const lastUserInfoStr = JSON.stringify(lastUserInfo);
          
          // Salvar em sessionStorage temporariamente
          sessionStorage.setItem('temp_lastLoggedUser', lastUserInfoStr);
          
          // Também salvar em cookie como backup
          const expiryDate = new Date();
          expiryDate.setDate(expiryDate.getDate() + 30);
          document.cookie = `lastLoggedUser=${encodeURIComponent(lastUserInfoStr)};expires=${expiryDate.toUTCString()};path=/;SameSite=Strict`;
          
          console.log('[AuthManager] ✅ Informações do usuário salvas em cookie e sessionStorage');
        }
      } catch (error) {
        console.error('[AuthManager] ❌ Erro ao salvar informações do último usuário:', error);
      }
      
      // Salvar temporariamente as informações do último usuário
      const tempLastUserInfo = sessionStorage.getItem('temp_lastLoggedUser');
      
      // --- Limpeza completa ---
      console.log('[AuthManager] 🧹 Limpando caches e estados...');
      
      // Limpar cache do AuthManager
      this.clearAllCache();
      
      // Limpar QueryClient
      queryClient.clear();
      
      // Limpar localStorage
      localStorage.clear();
      
      // Restaurar as informações do último usuário
      if (tempLastUserInfo) {
        localStorage.setItem('lastLoggedUser', tempLastUserInfo);
        console.log('[AuthManager] ✅ Informações do último usuário restauradas');
      }
      
      // Limpar sessionStorage
      sessionStorage.clear();
      
      // Remover cookies específicos, exceto lastLoggedUser
      document.cookie.split(';').forEach(cookie => {
        const name = cookie.split('=')[0].trim();
        if (name !== 'lastLoggedUser') {
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
        }
      });
      
      // Logout do Supabase
      console.log('[AuthManager] 🔐 Executando signOut no Supabase...');
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('[AuthManager] ❌ Erro no supabase.auth.signOut:', error.message);
      }
      
      // Limpar cache específico do usuário
      const userCacheKeys = Object.keys(localStorage).filter(key => 
        key.startsWith('vindula-') || 
        key.includes('query-') || 
        key.includes('user-')
      );
      userCacheKeys.forEach(key => localStorage.removeItem(key));
      
      // Atualizar estado
      this.updateState({
        session: null,
        user: null,
        company_id: null,
        isAuthenticated: false,
        isLoading: false,
        lastActivity: Date.now(),
      });

      console.log('[AuthManager] ✅ Logout completo realizado com sucesso');
    } catch (error) {
      console.error('[AuthManager] ❌ Erro durante logout:', error);
      
      // Garantir limpeza mesmo em caso de erro
      try {
        localStorage.removeItem('userSessionId');
        localStorage.clear();
        sessionStorage.clear();
      } catch (cleanupError) {
        console.error('[AuthManager] ❌ Erro ao limpar storage:', cleanupError);
      }
      
      throw error;
    }
  }

  /**
   * Atualizar último activity
   */
  public updateLastActivity(): void {
    this.currentState.lastActivity = Date.now();
    // Não disparar listeners para evitar re-renders desnecessários
  }

  /**
   * Buscar roles do usuário
   */
  public async getUserRoles(): Promise<string[]> {
    if (!this.currentState.isAuthenticated || !this.currentState.user) {
      return [];
    }

    const userId = this.currentState.user.id;
    const cacheKey = CACHE_KEYS.userRoles(userId);

    // Verificar cache primeiro
    const cached = authCache.get<string[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      console.log('[AuthManager] 🔍 Buscando roles do usuário:', userId);
      
      const { data: userRoles, error } = await supabase
        .from('user_roles')
        .select('role')
        .eq('user_id', userId);
        
      if (error) {
        console.error('[AuthManager] Erro ao buscar roles:', error);
        return [];
      }
      
      const roles = userRoles?.map(r => r.role) || [];
      
      // Cachear resultado (5 minutos)
      authCache.set(cacheKey, roles, 300000);
      
      console.log('[AuthManager] ✅ Roles carregados:', roles);
      return roles;
    } catch (error) {
      console.error('[AuthManager] Erro ao carregar roles:', error);
      return [];
    }
  }

  /**
   * Verificar health da sessão
   */
  public async checkSessionHealth(): Promise<boolean> {
    if (!this.currentState.isAuthenticated || !this.currentState.user) {
      return false;
    }

    const userId = this.currentState.user.id;
    const cacheKey = CACHE_KEYS.sessionHealth(userId);
    
    // Verificar cache primeiro
    const cachedHealth = authCache.get<boolean>(cacheKey);
    if (cachedHealth !== null) {
      return cachedHealth;
    }

    try {
      const { data, error } = await supabase.auth.getSession();
      
      const isHealthy = !error && !!data.session && data.session.user.id === userId;
      
      // Cachear resultado
      authCache.set(cacheKey, isHealthy, TTL.SESSION_HEALTH);
      
      if (!isHealthy) {
        this.handleInvalidSession();
      }

      return isHealthy;
    } catch (error) {
      console.error('[AuthManager] Erro no health check:', error);
      return false;
    }
  }

  /**
   * Cachear sessão
   */
  private cacheSession(session: Session): void {
    const sessionData = {
      session,
      user: session.user,
    };
    
    localStorageManager.set('auth:session', sessionData, TTL.USER_BASIC);
    console.log('[AuthManager] 💾 Sessão cacheada');
  }

  /**
   * Limpar todo o cache
   */
  private clearAllCache(): void {
    console.log('[AuthManager] 🧹 Limpando todos os caches...');
    
    authCache.clear();
    permissionCache.clear();
    localStorageManager.clear('user:');
    localStorageManager.clear('permission:');
    localStorageManager.clear('auth:');
  }

  /**
   * Configurar invalidação realtime
   */
  private setupRealtimeInvalidation(): void {
    realtimeManager.onInvalidation((event) => {
      console.log('[AuthManager] 🔄 Invalidação realtime recebida:', event);
      
      if (event.userId === this.currentState.user?.id) {
        if (event.type === 'user_roles' || event.type === 'company_users') {
          // Invalidar cache de auth
          this.validateSessionInBackground();
        } else if (event.type === 'permissions' || event.type === 'access_control_entries') {
          // NOVO: Invalidar cache de permissões quando ACE muda
          console.log('[AuthManager] 🔓 Permissões alteradas, invalidando cache de permissões...');
          
          // Forçar invalidação de todas as páginas que usam GenericPermissionGate
          this.notifyPermissionsChanged();
        }
      }
    });
  }

  /**
   * Notificar mudança de permissões para forçar revalidação
   */
  private notifyPermissionsChanged(): void {
    // Invalidar cache de permissões no AuthManager
    const userId = this.currentState.user?.id;
    if (userId) {
      permissionCache.invalidateByUserId(userId);
      localStorageManager.invalidateByPattern(`permission:${userId}`);
      
      // Não precisamos forçar notificação via updateState
      // O cache foi limpo e na próxima verificação de permissão
      // o resultado será buscado do banco novamente
      console.log('[AuthManager] 🔓 Cache de permissões invalidado para userId:', userId);
    }
  }

  /**
   * Atualizar estado e notificar listeners
   */
  private updateState(newState: Partial<AuthState>): void {
    const prevState = { ...this.currentState };
    this.currentState = { ...this.currentState, ...newState };
    
    // Notificar listeners apenas se houve mudança significativa
    if (this.hasSignificantChange(prevState, this.currentState)) {
      this.listeners.forEach(listener => {
        try {
          listener(this.currentState);
        } catch (error) {
          console.error('[AuthManager] Erro em listener:', error);
        }
      });
    }
  }

  /**
   * Verificar se houve mudança significativa
   */
  private hasSignificantChange(prev: AuthState, current: AuthState): boolean {
    return (
      prev.isAuthenticated !== current.isAuthenticated ||
      prev.isLoading !== current.isLoading ||
      prev.user?.id !== current.user?.id ||
      prev.company_id !== current.company_id
    );
  }

  /**
   * API Pública - Getters
   */
  public getState(): AuthState {
    return { ...this.currentState };
  }

  public getUser(): User | null {
    return this.currentState.user;
  }

  public getCompanyId(): string | null {
    return this.currentState.company_id;
  }

  /**
   * Definir company_id externamente (usado durante signup para evitar race condition)
   * @param companyId - ID da empresa conhecido externamente
   */
  public setCompanyId(companyId: string): void {
    if (!this.currentState.user?.id) {
      console.warn('[AuthManager] ⚠️ Tentativa de definir company_id sem usuário autenticado');
      return;
    }

    console.log('[AuthManager] 🏢 Company ID definido externamente:', companyId);
    
    // Atualizar estado
    this.updateState({
      ...this.currentState,
      company_id: companyId,
    });

    // Cachear o company_id para evitar buscas futuras desnecessárias
    const cacheKey = CACHE_KEYS.companyId(this.currentState.user.id);
    authCache.set(cacheKey, companyId, TTL.COMPANY_ID);
    localStorageManager.set(cacheKey, companyId, TTL.COMPANY_ID);
  }

  public isAuthenticated(): boolean {
    return this.currentState.isAuthenticated;
  }

  public isLoading(): boolean {
    return this.currentState.isLoading;
  }

  public getSession(): Session | null {
    return this.currentState.session;
  }

  /**
   * API Pública - Listeners
   */
  public subscribe(listener: AuthStateListener): () => void {
    this.listeners.add(listener);
    
    // Enviar estado atual imediatamente
    listener(this.currentState);
    
    // Retornar função de cleanup
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Debug info
   */
  public getDebugInfo() {
    return {
      state: this.currentState,
      isInitialized: this.isInitialized,
      listenersCount: this.listeners.size,
      cacheStats: {
        auth: authCache.getStats(),
        permissions: permissionCache.getStats(),
      },
      realtimeStatus: realtimeManager.getStatus(),
    };
  }
}

// Singleton instance
export const authManager = new AuthManager();