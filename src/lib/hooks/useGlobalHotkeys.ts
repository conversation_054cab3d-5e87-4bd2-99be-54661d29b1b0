/**
 * Hook para gerenciamento global de atalhos de teclado
 * Centraliza todos os atalhos do sistema para melhor organização e reutilização
 * 
 * Eventos customizados disponíveis:
 * - 'openPostSelector': Abre o seletor de criação de publicação (Alt+P)
 * 
 * <AUTHOR> Internet 2025
 */
import { useHotkeys } from "react-hotkeys-hook";
import { useNavigate, useLocation } from "react-router-dom";
import { useUserRoles } from "@/lib/query/hooks/useUserRoles";
import { useIsFeatureEnabled } from "@/lib/query/hooks/useFeatureDetails";
import { useState } from "react";

// Tipos para eventos customizados
declare global {
  interface WindowEventMap {
    'openPostSelector': CustomEvent;
  }
}

interface HotkeyConfig {
  key: string;
  action: () => void;
  description: string;
  category: string;
  adminOnly?: boolean;
  featureRequired?: string;
  disabled?: boolean;
}

export function useGlobalHotkeys() {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAdmin } = useUserRoles();
  const { isEnabled: isReportsEnabled } = useIsFeatureEnabled('feature_reports');
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);

  // Função para navegar sem feedback visual
  const navigateWithoutFeedback = (url: string) => {
    if (location.pathname !== url) {
      navigate(url);
    }
  };

  // Função para mostrar/ocultar modal de ajuda
  const toggleHelpModal = () => {
    setIsHelpModalOpen(!isHelpModalOpen);
  };

  // Função explícita para fechar o modal
  const closeHelpModal = () => {
    setIsHelpModalOpen(false);
  };

  // Função explícita para abrir o modal  
  const openHelpModal = () => {
    setIsHelpModalOpen(true);
  };

  // Configuração de todos os atalhos do sistema
  const hotkeyConfigs: HotkeyConfig[] = [
    // Atalho de Ajuda
    {
      key: 'f1',
      action: toggleHelpModal,
      description: 'Mostrar todos os atalhos',
      category: 'Ajuda'
    },

    // Navegação Principal
    {
      key: 'alt+1',
      action: () => navigateWithoutFeedback('/feed'),
      description: 'Ir para Feed',
      category: 'Navegação Principal'
    },
    {
      key: 'alt+2',
      action: () => navigateWithoutFeedback('/knowledge'),
      description: 'Ir para Knowledge Hub',
      category: 'Navegação Principal'
    },
    {
      key: 'alt+3',
      action: () => navigateWithoutFeedback('/chat'),
      description: 'Ir para Chat',
      category: 'Navegação Principal'
    },

    // Produtividade
    {
      key: 'alt+4',
      action: () => navigateWithoutFeedback('/library'),
      description: 'Ir para Biblioteca',
      category: 'Produtividade'
    },

    {
      key: 'alt+6',
      action: () => navigateWithoutFeedback('/obligations'),
      description: 'Ir para Obrigações',
      category: 'Produtividade'
    },

    // Colaboração
    {
      key: 'alt+7',
      action: () => navigateWithoutFeedback('/people'),
      description: 'Ir para People Hub',
      category: 'Colaboração'
    },
    {
      key: 'alt+8',
      action: () => navigateWithoutFeedback('/events'),
      description: 'Ir para Eventos',
      category: 'Colaboração'
    },

    // Gamificação
    {
      key: 'alt+0',
      action: () => navigateWithoutFeedback('/ranking'),
      description: 'Ir para Ranking',
      category: 'Gamificação'
    },

    // Criação de Conteúdo
    {
      key: 'alt+p',
      action: () => {
        // Disparar evento customizado para abrir o PostCreationSelector
        window.dispatchEvent(new CustomEvent('openPostSelector'));
      },
      description: 'Abrir seletor de criação de publicação',
      category: 'Publicações'
    },

    // Atalhos Administrativos
    {
      key: 'ctrl+alt+1',
      action: () => navigateWithoutFeedback('/admin'),
      description: 'Ir para Painel de Controle',
      category: 'Administração',
      adminOnly: true
    },
    {
      key: 'ctrl+alt+2',
      action: () => navigateWithoutFeedback('/reports'),
      description: 'Ir para Relatórios',
      category: 'Administração',
      adminOnly: true,
      featureRequired: 'feature_reports'
    },

    // Atalhos de Navegação Especiais
    {
      key: 'alt+shift+h',
      action: () => navigateWithoutFeedback('/'),
      description: 'Ir para página inicial',
      category: 'Navegação Especial'
    },
    {
      key: 'alt+shift+s',
      action: () => navigateWithoutFeedback('/settings'),
      description: 'Ir para configurações',
      category: 'Navegação Especial'
    }
  ];

  // Filtrar atalhos com base nas permissões e features
  const activeHotkeys = hotkeyConfigs.filter(config => {
    if (config.adminOnly && !isAdmin) return false;
    if (config.featureRequired && !isReportsEnabled) return false;
    if (config.disabled) return false;
    return true;
  });

  // Registrar todos os atalhos ativos - Cada hook deve ser chamado no nível superior
  // Atalhos de Ajuda
  useHotkeys('f1', (e) => {
    e.preventDefault();
    toggleHelpModal();
  }, { enableOnFormTags: ['INPUT', 'TEXTAREA', 'SELECT'] });


  // Navegação Principal
  useHotkeys('alt+1', (e) => {
    e.preventDefault();
    navigateWithoutFeedback('/feed');
  }, { enableOnFormTags: ['INPUT', 'TEXTAREA', 'SELECT'] });

  useHotkeys('alt+2', (e) => {
    e.preventDefault();
    navigateWithoutFeedback('/knowledge');
  }, { enableOnFormTags: ['INPUT', 'TEXTAREA', 'SELECT'] });

  useHotkeys('alt+3', (e) => {
    e.preventDefault();
    navigateWithoutFeedback('/chat');
  }, { enableOnFormTags: ['INPUT', 'TEXTAREA', 'SELECT'] });

  // Produtividade
  useHotkeys('alt+4', (e) => {
    e.preventDefault();
    navigateWithoutFeedback('/library');
  }, { enableOnFormTags: ['INPUT', 'TEXTAREA', 'SELECT'] });



  useHotkeys('alt+6', (e) => {
    e.preventDefault();
    navigateWithoutFeedback('/obligations');
  }, { enableOnFormTags: ['INPUT', 'TEXTAREA', 'SELECT'] });

  // Colaboração
  useHotkeys('alt+7', (e) => {
    e.preventDefault();
    navigateWithoutFeedback('/people');
  }, { enableOnFormTags: ['INPUT', 'TEXTAREA', 'SELECT'] });

  useHotkeys('alt+8', (e) => {
    e.preventDefault();
    navigateWithoutFeedback('/events');
  }, { enableOnFormTags: ['INPUT', 'TEXTAREA', 'SELECT'] });


  // Gamificação
  useHotkeys('alt+0', (e) => {
    e.preventDefault();
    navigateWithoutFeedback('/ranking');
  }, { enableOnFormTags: ['INPUT', 'TEXTAREA', 'SELECT'] });

  // Criação de Conteúdo
  useHotkeys('alt+p', (e) => {
    e.preventDefault();
    // Disparar evento customizado para abrir o PostCreationSelector
    window.dispatchEvent(new CustomEvent('openPostSelector'));
  }, { enableOnFormTags: ['INPUT', 'TEXTAREA', 'SELECT'] });

  // Atalhos Administrativos (condicionais)
  useHotkeys('ctrl+alt+1', (e) => {
    if (!isAdmin) return;
    e.preventDefault();
    navigateWithoutFeedback('/admin');
  }, { 
    enableOnFormTags: ['INPUT', 'TEXTAREA', 'SELECT'],
    enabled: isAdmin
  });

  useHotkeys('ctrl+alt+2', (e) => {
    if (!isAdmin || !isReportsEnabled) return;
    e.preventDefault();
    navigateWithoutFeedback('/reports');
  }, { 
    enableOnFormTags: ['INPUT', 'TEXTAREA', 'SELECT'],
    enabled: isAdmin && isReportsEnabled
  });

  // Atalhos de Navegação Especiais
  useHotkeys('alt+shift+h', (e) => {
    e.preventDefault();
    navigateWithoutFeedback('/');
  }, { enableOnFormTags: ['INPUT', 'TEXTAREA', 'SELECT'] });

  useHotkeys('alt+shift+s', (e) => {
    e.preventDefault();
    navigateWithoutFeedback('/settings');
  }, { enableOnFormTags: ['INPUT', 'TEXTAREA', 'SELECT'] });

  return {
    activeHotkeys,
    allHotkeys: hotkeyConfigs,
    navigateWithoutFeedback,
    isHelpModalOpen,
    setIsHelpModalOpen,
    toggleHelpModal,
    closeHelpModal,
    openHelpModal
  };
} 