/**
 * ⚠️ DEPRECATED - Use queryClientCentralized.ts
 * 
 * Configuração do React Query Client com persistência no localStorage
 * 
 * @deprecated Este arquivo será removido em futuras versões.
 * Use @/lib/query/queryClientCentralized em vez deste arquivo.
 * 
 * <AUTHOR> Internet 2025
 */

import { QueryClient } from '@tanstack/react-query';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';
import { persistQueryClient } from '@tanstack/query-persist-client-core';
import { supabase } from '@/integrations/supabase/client';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

/**
 * Retorna a chave de cache baseada no ID do usuário atual, ou uma chave genérica
 * para usuários não autenticados
 */
const getCacheKey = (): string => {
  try {
    // O token de autenticação é armazenado com prefixo específico
    const tokenKey = Object.keys(localStorage).find(key => key.startsWith('sb-') && key.endsWith('-auth-token'));

    if (tokenKey) {
      try {
        const tokenData = JSON.parse(localStorage.getItem(tokenKey) || '{}');
        if (tokenData.user?.id) {
          return `VINDULA_QUERY_CACHE_${tokenData.user.id}`;
        }
      } catch (error) {
        console.error('Erro ao processar token de autenticação:', error);
      }
    }
  } catch (error) {
    console.error('Erro ao acessar informações de autenticação:', error);
  }

  // Fallback para usuários não autenticados
  return 'VINDULA_QUERY_CACHE_anonymous';
};

/**
 * Limpa todas as chaves de cache relacionadas ao usuário atual do localStorage
 */
const clearUserCache = () => {
  try {
    console.log('Iniciando limpeza do cache do usuário...');

    // Lista todas as chaves do localStorage
    const keys = Object.keys(localStorage);
    console.log('Total de chaves encontradas:', keys.length);

    // Prefixos de cache a serem limpos
    const cachePrefixes = [
      'VINDULA_QUERY_CACHE_',
      'avatar_cache_',
      'sb-',
      'supabase.',
      '@supabase/',
      'query-',
      'user_'
    ];

    let removedCount = 0;

    // Remove caches da aplicação
    keys.forEach(key => {
      if (cachePrefixes.some(prefix => key.startsWith(prefix))) {
        localStorage.removeItem(key);
        console.log(`Removida chave de cache: ${key}`);
        removedCount++;
      }
    });

    console.log(`Limpeza concluída. ${removedCount} chaves removidas.`);
  } catch (error) {
    console.error('Erro ao limpar cache do usuário:', error);
  }
};

// Criando persister que salvará as queries no localStorage com chave específica por usuário
const localStoragePersister = createSyncStoragePersister({
  storage: window.localStorage,
  key: getCacheKey(), // Chave por usuário
  throttleTime: 1000, // Tempo mínimo entre gravações no localStorage
  serialize: data => JSON.stringify(data),
  deserialize: data => JSON.parse(data),
});

// Configuração do QueryClient com opções padrão otimizadas
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Configurações padrão para todas as queries
      staleTime: 5 * 60 * 1000, // 5 minutos antes dos dados serem considerados obsoletos
      gcTime: 60 * 60 * 1000, // 1 hora em cache antes de ser removido pelo garbage collector
      refetchOnWindowFocus: false, // Não recarregar dados quando a janela ganha foco
      refetchOnMount: true, // Não recarregar dados automaticamente quando o componente monta
      refetchOnReconnect: true, // Não recarregar dados automaticamente quando a conexão é reestabelecida
      retry: 2, // Número de tentativas em caso de falha
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000), // Delay exponencial entre tentativas
    },
    mutations: {
      // Configurações padrão para todas as mutations
      retry: 1, // Tentativas para mutations
    },
  },
});

// Configurar persistência do cache
persistQueryClient({
  queryClient,
  persister: localStoragePersister,
  maxAge: 24 * 60 * 60 * 1000, // 24 horas (tempo máximo que os dados persistidos ficam válidos)
  buster: import.meta.env.VITE_APP_VERSION || 'v1', // Invalidar cache quando a versão do app mudar
  hydrateOptions: {
    // O que deve ser hidratado do localStorage (padrão: tudo)
    deserializeData: (data) => {
      try {
        return JSON.parse(data);
      } catch (error) {
        logQueryEvent('queryClient', 'Erro ao deserializar dados do cache, ignorando...', error, 'warn');
        return null;
      }
    },
  },
  dehydrateOptions: {
    // O que deve ser salvo no localStorage (padrão: tudo)
    shouldDehydrateQuery: (query) => {
      // Não persistir queries com erro ou queries que podem causar problemas na hidratação
      if (query.state?.status === 'error') {
        // Não logar erros de autenticação para não poluir console
        const isAuthError = query.state.error?.message?.includes('não autenticado') || 
                           query.state.error?.message?.includes('Usuário não autenticado');
        
        if (!isAuthError) {
          logQueryEvent('queryClient', `Query com erro não será persistida: ${JSON.stringify(query.queryKey)}`);
        }
        return false;
      }

      // Não persistir queries com estados pendentes que podem causar problemas na hidratação
      if (query.state?.status === 'pending' || query.state?.status === 'loading') {
        return false;
      }

      // Não persistir queries com dados inválidos ou estados problemáticos
      if (!query.state?.data || query.state?.data === null || query.state?.data === undefined) {
        return false;
      }

      // Não persistir queries com dados sensíveis
      const sensitiveKeys = ['auth', 'secret', 'password', 'token'];
      
      // Verificar se queryKey é um array antes de usar .some()
      try {
        if (!query.queryKey || !Array.isArray(query.queryKey)) {
          // Apenas log silencioso para não poluir o console
          return false;
        }
      } catch (e) {
        // Ignora erros silenciosamente
        return false;
      }
      
      const containsSensitiveKey = sensitiveKeys.some(key => 
        query.queryKey.some(k => 
          typeof k === 'string' && k.toLowerCase().includes(key)
        )
      );

      if (containsSensitiveKey) {
        // Reduzir logs para queries de autenticação frequentes
        const isCommonAuthQuery = 
          JSON.stringify(query.queryKey) === JSON.stringify(["auth","session"]) ||
          JSON.stringify(query.queryKey) === JSON.stringify(["customization","active-items"]) ||
          (Array.isArray(query.queryKey) && 
           query.queryKey.length === 1 && 
           (query.queryKey[0] === "auth" || query.queryKey[0] === "customization"));
        
        // Só logar se não for uma query comum de autenticação/customização
        if (!isCommonAuthQuery) {
          logQueryEvent('queryClient', `Query com dados sensíveis não será persistida: ${JSON.stringify(query.queryKey)}`);
        }
        return false;
      }

      return true;
    },
  },
});


// Atualizando a chave do cache quando o estado de autenticação mudar
supabase.auth.onAuthStateChange(async (event, session) => {
  logQueryEvent('queryClient', 'Evento de autenticação detectado:', event);

  if (event === 'SIGNED_IN' || event === 'USER_UPDATED') {
    // Atualizar chave de cache para o novo usuário
    const newKey = getCacheKey();
    logQueryEvent('queryClient', 'Login/Update: Atualizando chave de cache para:', newKey);

  } else if (event === 'SIGNED_OUT') {
    logQueryEvent('queryClient', 'Logout: Iniciando limpeza de estado...');

    try {
      // Limpar o cache do React Query

      await queryClient.cancelQueries();
      queryClient.clear();

      // Remover as chaves do localStorage relacionadas ao usuário
      clearUserCache();

      // Resetar estado das queries principais
      queryClient.setQueryData(['users', 'current'], null);
      queryClient.setQueryData(['users', 'currentRoles'], []);
      queryClient.removeQueries();

      logQueryEvent('queryClient', 'Logout: Limpeza de estado concluída com sucesso');
    } catch (error) {
      logQueryEvent('queryClient', 'Erro durante limpeza de estado no logout:', error, 'error');
    }
  }
});

// ⚠️ AVISO DE DEPRECATION
console.warn(`
🚨 DEPRECATED: Este queryClient.ts está obsoleto!

📋 PRÓXIMA AÇÃO NECESSÁRIA:
- Migrar para: @/lib/query/queryClientCentralized
- Novo sistema inclui: Domain Strategies + Cache Warming + Offline-first

🔗 Ver documentação: docs_v2/roadmaps/arquitetura-cache-centralizacao-tanstack-query.md
`);

/**
 * @deprecated Use queryClientCentralized.ts em vez deste arquivo
 */
