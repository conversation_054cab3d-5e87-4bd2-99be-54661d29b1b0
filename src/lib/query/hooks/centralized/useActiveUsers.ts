/**
 * Hook centralizado para buscar usuários ativos - Sistema EventBus + Domain Strategies
 * <AUTHOR> Internet 2025
 * 
 * Versão migrada que usa o sistema de cache centralizado com EventBus
 */
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useCacheService } from '@/lib/query/queryClientCentralized';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

export interface ActiveUser {
  id: string;
  full_name: string;
  avatar_url?: string;
  status: 'online' | 'away' | 'offline';
  last_activity_minutes: number;
}

interface UseActiveUsersOptions {
  activeThresholdMinutes?: number;
  enabled?: boolean;
  refetchInterval?: number;
}

interface ActiveUserRaw {
  id: string;
  full_name: string;
  avatar_url?: string;
  status: string;
  last_activity_minutes: number;
}

/**
 * Hook centralizado para buscar usuários ativos usando EventBus + Domain Strategies
 */
export function useActiveUsers(options: UseActiveUsersOptions = {}) {
  const {
    activeThresholdMinutes = 15,
    enabled = true,
    refetchInterval = 30000 // 30 segundos
  } = options;

  // Usar cache service centralizado
  const cacheService = useCacheService();

  return useQuery({
    queryKey: ['users', 'active', activeThresholdMinutes],
    queryFn: async (): Promise<ActiveUser[]> => {
      logQueryEvent('useActiveUsers[Centralized]', 'Iniciando busca de usuários ativos', {
        activeThresholdMinutes,
        source: 'EventBus+DomainStrategy'
      });

      // Função RPC para buscar usuários ativos
      const { data, error } = await supabase
        .rpc('get_active_users', {
          active_threshold_minutes: activeThresholdMinutes
        });

      if (error) {
        logQueryEvent(
          'useActiveUsers[Centralized]',
          `Erro ao buscar usuários ativos: ${error.message}`,
          { error, activeThresholdMinutes },
          'error'
        );
        
        // Emitir evento de erro via EventBus
        cacheService.eventBus?.emit('users.error', {
          operation: 'fetch_active_users',
          error: error.message,
          context: { activeThresholdMinutes }
        });
        
        throw error;
      }

      // Garantir que sempre retornamos um array
      const rawData = Array.isArray(data) ? data : [];
      
      const activeUsers: ActiveUser[] = rawData.map((user: ActiveUserRaw) => ({
        id: user.id,
        full_name: user.full_name || 'Usuário',
        avatar_url: user.avatar_url,
        status: user.status as 'online' | 'away' | 'offline',
        last_activity_minutes: user.last_activity_minutes || 999
      }));

      // Emitir evento de sucesso via EventBus
      cacheService.eventBus?.emit('users.active.fetched', {
        count: activeUsers.length,
        threshold: activeThresholdMinutes,
        users: activeUsers.map(u => ({ id: u.id, status: u.status }))
      });

      logQueryEvent('useActiveUsers[Centralized]', 'Usuários ativos carregados com sucesso', {
        count: activeUsers.length,
        source: 'EventBus+DomainStrategy'
      });

      return activeUsers;
    },
    enabled,
    staleTime: 20 * 1000, // 20 segundos
    refetchInterval,
    refetchIntervalInBackground: true,
    refetchOnWindowFocus: true,
  });
}

/**
 * Hook para buscar apenas usuários online (últimos 1 minuto) - Versão centralizada
 */
export function useOnlineUsers() {
  const { data: activeUsers, ...rest } = useActiveUsers({
    activeThresholdMinutes: 1,
    refetchInterval: 15000 // 15 segundos - mais frequente para usuários online
  });

  const onlineUsers = Array.isArray(activeUsers) 
    ? activeUsers.filter(user => user.status === 'online') 
    : [];

  return {
    data: onlineUsers,
    ...rest
  };
}

/**
 * Hook para contar usuários ativos por status - Versão centralizada
 */
export function useActiveUsersCount() {
  const { data: activeUsers, ...rest } = useActiveUsers();

  const counts = {
    online: Array.isArray(activeUsers) ? activeUsers.filter(user => user.status === 'online').length : 0,
    away: Array.isArray(activeUsers) ? activeUsers.filter(user => user.status === 'away').length : 0,
    offline: Array.isArray(activeUsers) ? activeUsers.filter(user => user.status === 'offline').length : 0,
    total: Array.isArray(activeUsers) ? activeUsers.length : 0
  };

  return {
    data: counts,
    users: activeUsers,
    ...rest
  };
}