/**
 * 🚀 Hook centralizado para feed de posts - PostsDomainStrategy
 * 
 * ✅ VERSÃO CENTRALIZADA com:
 * - PostsDomainStrategy (cache híbrido)
 * - EventBus integration 
 * - IndexedDB support
 * - Cache inteligente (feeds dinâmicos + posts eternos)
 * 
 * <AUTHOR> Internet 2025
 */
import {
  useInfiniteQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { QueryKeys } from "../../queryKeys";
import { useRef } from "react";
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { useCurrentUser } from '../useUsers';
import { useCacheService } from '@/lib/cache/hooks/useCacheService';
import { PostsDomainStrategy } from '@/lib/cache/strategies/PostsDomainStrategy';

// Constantes
const POSTS_PER_PAGE = 10;

// Interface para posts processados (mantendo compatibilidade)
interface ProcessedPost {
  id: string;
  content: string;
  created_at: string;
  likes: number;
  company_id: string;
  status: string;
  scheduled_at?: string;
  has_poll?: boolean;
  type?: string;
  is_edited?: boolean;
  edit_count?: number;
  last_edited_at?: string;
  last_edited_by?: string;
  metadata?: {
    audio_url?: string;
    audio_duration?: number;
    medal_id?: string;
    medal_name?: string;
    medal_type?: string;
    [key: string]: any;
  };
  author: {
    id: string;
    full_name: string;
    avatar_url?: string;
  };
  liked_by: Array<{
    profiles: {
      id: string;
      full_name: string;
      avatar_url?: string;
    };
  }>;
  audience?: {
    type: "all" | "department" | "team" | "user";
    targets?: string[];
    targetCount?: number;
  };
  post_audience?: Array<{
    target_type: string;
    target_id: string;
  }>;
  [key: string]: unknown;
}

// Interface para resposta de página
interface PostsPage {
  posts: ProcessedPost[];
  nextPage: number | undefined;
  totalCount: number;
}

// Interface para filtros de posts (mantendo compatibilidade)
export interface PostsFilters {
  search?: string;
  sortBy?: "newest" | "oldest" | "most_liked" | "most_commented";
  status?: "all" | "published" | "scheduled";
  audience?: "all" | "team" | "department" | "personal";
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  authorId?: string;
  [key: string]: unknown;
}

interface UsePostsFeedOptions {
  enabled?: boolean;
  staleTime?: number; // Será sobrescrito pela PostsDomainStrategy
}

/**
 * 🚀 Hook centralizado para feed de posts com PostsDomainStrategy
 * 
 * ✅ BENEFÍCIOS:
 * - Cache híbrido (feeds dinâmicos + posts eternos)
 * - EventBus para invalidações inteligentes
 * - IndexedDB para armazenamento offline
 * - Performance otimizada
 */
export function usePostsFeedCentralized(filters?: PostsFilters, options?: UsePostsFeedOptions) {
  const queryClient = useQueryClient();
  const { cacheService, isInitialized } = useCacheService();
  const realtimeSubscribed = useRef(false);
  
  // Para o filtro de audiência, precisamos do profile do usuário atual
  const { data: currentUser } = useCurrentUser();

  // 🎯 Obter configuração da PostsDomainStrategy (com verificação de inicialização)
  const queryKey = QueryKeys.posts.feed(filters);
  let cacheConfig;
  
  if (isInitialized && cacheService) {
    try {
      const postsDomainStrategy = cacheService.getStrategy('posts') as PostsDomainStrategy;
      cacheConfig = postsDomainStrategy?.getConfigForQueryKey(queryKey);
      logQueryEvent('usePostsFeedCentralized', '✅ Config obtida da PostsDomainStrategy', { 
        queryKey, 
        cacheConfig,
        filters 
      });
    } catch (error) {
      logQueryEvent('usePostsFeedCentralized', '⚠️ Erro ao obter config da PostsDomainStrategy, usando fallback', error, 'warn');
      cacheConfig = null;
    }
  } else {
    logQueryEvent('usePostsFeedCentralized', '⏳ CacheService não inicializado, usando configuração fallback', { 
      isInitialized,
      hasCacheService: !!cacheService 
    });
  }

  // Fallback para quando CacheService não estiver disponível
  if (!cacheConfig) {
    cacheConfig = {
      staleTime: 2 * 60 * 1000, // 2 minutos para feeds
      gcTime: 10 * 60 * 1000,   // 10 minutos na memória
      refetchOnMount: false,     // Cache-first
      refetchOnWindowFocus: true, // Refresh ao voltar
      refetchOnReconnect: true,   // Sync ao reconectar
      retry: 3,
      retryDelay: (attemptIndex: number) => Math.min(500 * 2 ** attemptIndex, 10000),
    };
  }

  // Consulta de posts com suporte a paginação infinita e filtros
  const query = useInfiniteQuery<PostsPage>({
    queryKey,
    queryFn: async ({ pageParam = 0 }) => {
      const from = (pageParam as number) * POSTS_PER_PAGE;
      const to = from + POSTS_PER_PAGE - 1;

      logQueryEvent('usePostsFeedCentralized', `🔄 Buscando página ${pageParam}`, { from, to, filters });

      let queryBuilder = supabase
        .from("posts")
        .select(
          `
          id,
          content,
          created_at,
          likes,
          company_id,
          status,
          scheduled_at,
          has_poll,
          type,
          metadata,
          is_edited,
          edit_count,
          last_edited_at,
          last_edited_by,
          author:profiles!posts_author_id_fkey (
            id,
            full_name,
            avatar_url
          ),
          liked_by:post_likes (
            profiles:profiles!post_likes_user_id_fkey (
              id,
              full_name,
              avatar_url
            )
          ),
          post_audience (
            target_type,
            target_id
          )
        `,
          { count: "exact" },
        );

      // Aplicar filtros de status
      if (filters?.status === "published") {
        queryBuilder = queryBuilder.eq("status", "published");
      } else if (filters?.status === "scheduled") {
        queryBuilder = queryBuilder.eq("status", "scheduled");
      } else {
        // Status "all" ou sem filtro - mostrar publicados e agendados que já devem aparecer
        queryBuilder = queryBuilder.or(
          `status.eq.published,and(status.eq.scheduled,scheduled_at.lte.${
            new Date().toISOString()
          })`
        );
      }

      // Aplicar filtro de busca por texto
      if (filters?.search) {
        queryBuilder = queryBuilder.ilike("content", `%${filters.search}%`);
      }

      // Aplicar filtro de autor
      if (filters?.authorId) {
        queryBuilder = queryBuilder.eq("author_id", filters.authorId);
      }

      // Aplicar filtro de data
      if (filters?.dateRange?.from) {
        queryBuilder = queryBuilder.gte("created_at", filters.dateRange.from.toISOString());
      }
      if (filters?.dateRange?.to) {
        const endDate = new Date(filters.dateRange.to);
        endDate.setHours(23, 59, 59, 999); // Incluir todo o dia
        queryBuilder = queryBuilder.lte("created_at", endDate.toISOString());
      }

      // Aplicar ordenação
      if (filters?.sortBy === "oldest") {
        queryBuilder = queryBuilder.order("created_at", { ascending: true });
      } else {
        // newest (padrão) e outros tipos de ordenação
        queryBuilder = queryBuilder.order("created_at", { ascending: false });
      }

      const { data, error, count } = await queryBuilder.range(from, to);

      if (error) {
        logQueryEvent('usePostsFeedCentralized', '❌ Erro ao buscar posts', error, 'error');
        throw error;
      }

      // Garantir que data é um array
      const postsData = Array.isArray(data) ? data : [];

      // Processar dados retornados
      let processedPosts = postsData.map((post) => {
        return {
          id: post.id,
          content: post.content,
          created_at: post.created_at,
          likes: post.liked_by?.length || 0,
          company_id: post.company_id,
          status: post.status,
          scheduled_at: post.scheduled_at,
          has_poll: post.has_poll,
          type: post.type, // Incluir tipo do post
          metadata: post.metadata, // Incluir metadata do post
          // Campos de edição
          is_edited: post.is_edited,
          edit_count: post.edit_count,
          last_edited_at: post.last_edited_at,
          last_edited_by: post.last_edited_by,
          author: post.author,
          liked_by: post.liked_by?.filter((like) =>
            like.profiles
          ).map((like) => ({
            profiles: like.profiles,
          })) || [],
          post_audience: post.post_audience,
        } as ProcessedPost;
      });

      // Aplicar filtros de audiência do lado cliente
      if (filters?.audience && filters.audience !== "all" && currentUser) {
        processedPosts = processedPosts.filter((post) => {
          if (filters.audience === "personal") {
            // Posts direcionados apenas para o usuário atual ou posts do próprio usuário
            return post.post_audience?.some(audience => 
              audience.target_type === "user" && audience.target_id === currentUser.id
            ) || post.author.id === currentUser.id;
          } else if (filters.audience === "team") {
            // Posts direcionados para equipe (implementar quando estrutura estiver definida)
            return post.post_audience?.some(audience => 
              audience.target_type === "team"
            );
          } else if (filters.audience === "department") {
            // Posts direcionados para departamento (implementar quando estrutura estiver definida)
            return post.post_audience?.some(audience => 
              audience.target_type === "department"
            );
          }
          return true;
        });
      }

      // Aplicar ordenação avançada do lado cliente (para "most_liked" e "most_commented")
      if (filters?.sortBy === "most_liked") {
        processedPosts.sort((a, b) => b.likes - a.likes);
      } else if (filters?.sortBy === "most_commented") {
        // TODO: Implementar contagem de comentários quando disponível
        // Por enquanto, manter ordenação por data
        processedPosts.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      }

      // Garantir que sempre retornamos um objeto válido
      const result: PostsPage = {
        posts: Array.isArray(processedPosts) ? processedPosts : [],
        nextPage: postsData.length === POSTS_PER_PAGE
          ? (pageParam as number) + 1
          : undefined,
        totalCount: count || 0,
      };

      logQueryEvent('usePostsFeedCentralized', '✅ Posts carregados com sucesso', { 
        postsCount: result.posts.length, 
        nextPage: result.nextPage,
        totalCount: result.totalCount 
      });

      // 🚌 Emitir evento para PostsDomainStrategy (se disponível)
      if (isInitialized && cacheService?.eventBus) {
        try {
          cacheService.eventBus.emit('posts.feed', 'fetched', {
            page: pageParam,
            count: result.posts.length,
            totalCount: result.totalCount,
            filters,
            posts: result.posts.map(post => ({ id: post.id, type: post.type }))
          });
        } catch (error) {
          logQueryEvent('usePostsFeedCentralized', '⚠️ Erro ao emitir evento EventBus', error, 'warn');
        }
      }

      return result;
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage: PostsPage | undefined) => {
      // Verificação robusta para evitar erros de propriedades undefined
      if (!lastPage || typeof lastPage !== 'object' || !('nextPage' in lastPage)) {
        return undefined;
      }
      return lastPage.nextPage;
    },
    enabled: options?.enabled !== false,
    
    // 🎯 Configuração da PostsDomainStrategy
    staleTime: cacheConfig.staleTime,
    gcTime: cacheConfig.gcTime,
    refetchOnMount: cacheConfig.refetchOnMount,
    refetchOnWindowFocus: cacheConfig.refetchOnWindowFocus,
    refetchOnReconnect: cacheConfig.refetchOnReconnect,
    retry: cacheConfig.retry,
    retryDelay: cacheConfig.retryDelay,
  });

  // 🚌 Escutar eventos do EventBus para invalidações
  // O PostsDomainStrategy via UnifiedRealtimeProvider já faz as invalidações
  // Este hook apenas consome os dados do cache otimizado

  return query;
}

/**
 * 🔄 Hook de compatibilidade para migração gradual
 * 
 * Use este para começar a testar o sistema centralizado
 * mantendo a interface original
 */
export function usePostsFeed(filters?: PostsFilters, options?: UsePostsFeedOptions) {
  // 🚨 Por enquanto, pode usar a versão centralizada diretamente
  // Adicionar flag de feature toggle no futuro se necessário
  const ENABLE_CENTRALIZED = true;

  if (ENABLE_CENTRALIZED) {
    logQueryEvent('usePostsFeed', '🚀 Usando versão CENTRALIZADA com PostsDomainStrategy');
    return usePostsFeedCentralized(filters, options);
  } else {
    // Fallback para versão original (se necessário)
    throw new Error('Versão original removida - use apenas versão centralizada');
  }
}