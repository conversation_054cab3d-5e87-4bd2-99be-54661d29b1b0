/**
 * Hook para entrar automaticamente em canais públicos.
 * <AUTHOR> Internet 2025
 */
import { useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { QueryKeys } from './useChatMessages';

interface UseChannelAutoJoinOptions {
  onJoinSuccess?: () => void;
  enabled?: boolean;
}

/**
 * Hook para entrar automaticamente em canais públicos.
 * Verifica se o canal é público e se o usuário já é membro.
 * Se não for membro e o canal for público, adiciona o usuário automaticamente.
 * 
 * @param channelId - ID do canal
 * @param userId - ID do usuário atual
 * @param options - Opções adicionais (onJoinSuccess, enabled)
 */
export function useChannelAutoJoin(
  channelId?: string,
  userId?: string,
  options: UseChannelAutoJoinOptions = {}
) {
  const { onJoinSuccess, enabled = true } = options;
  const queryClient = useQueryClient();

  // Mutation para entrar no canal
  const joinChannelMutation = useMutation({
    mutationFn: async () => {
      if (!channelId || !userId) {
        throw new Error('channelId e userId são obrigatórios');
      }

      try {
        logQueryEvent("useChannelAutoJoin", "Tentando entrar no canal", { channelId, userId });

        // 1. Verificar se o canal é público
        const { data: channelData, error: channelError } = await supabase
          .from('channels')
          .select('is_private, company_id')
          .eq('id', channelId)
          .single();

        if (channelError) {
          logQueryEvent("useChannelAutoJoin", "Erro ao buscar dados do canal", { 
            channelId, 
            error: channelError 
          }, "error");
          throw channelError;
        }

        // Se o canal for privado, não fazer nada
        if (channelData.is_private) {
          logQueryEvent("useChannelAutoJoin", "Canal é privado, não será feito auto-join", { 
            channelId 
          });
          return { joined: false, reason: 'private_channel' };
        }

        // 2. Verificar se o usuário já é membro
        const { data: memberData, error: memberError } = await supabase
          .from('channel_members')
          .select('user_id')
          .eq('channel_id', channelId)
          .eq('user_id', userId)
          .maybeSingle();

        if (memberError && memberError.code !== 'PGRST116') { // PGRST116 significa que nenhum membro foi encontrado, o que é esperado
          logQueryEvent("useChannelAutoJoin", "Erro ao verificar associação", { 
            channelId, 
            userId, 
            error: memberError 
          }, "error");
          throw memberError;
        }

        // Se o usuário já for membro, não fazer nada
        if (memberData) {
          logQueryEvent("useChannelAutoJoin", "Usuário já é membro do canal", { 
            channelId, 
            userId 
          });
          return { joined: false, reason: 'already_member' };
        }

        // 3. Adicionar o usuário como membro
        logQueryEvent("useChannelAutoJoin", "Usuário não é membro, tentando adicionar", { 
          channelId, 
          userId 
        });
        
        const { error: insertError } = await supabase
          .from('channel_members')
          .insert({ 
            channel_id: channelId, 
            user_id: userId, 
            role: 'member'
          });

        // Verificar se é um erro de chave duplicada (usuário já foi adicionado em outra operação)
        const isDuplicateKeyError = insertError && 
          insertError.code === '23505' && 
          insertError.message.includes('channel_members_pkey');
        
        if (insertError && !isDuplicateKeyError) {
          // Erro real, não relacionado à duplicação de chave
          logQueryEvent("useChannelAutoJoin", "Erro ao entrar no canal", { 
            channelId, 
            userId, 
            error: insertError 
          }, "error");
          throw insertError;
        }
        
        if (isDuplicateKeyError) {
          // Usuário já foi adicionado em outra operação
          logQueryEvent("useChannelAutoJoin", "Usuário já foi adicionado em operação paralela", { 
            channelId, 
            userId 
          });
          return { joined: true, reason: 'duplicate_key' };
        }
        
        // 4. Adicionar mensagem de sistema
        try {
          const { data: profile } = await supabase
            .from('profiles')
            .select('full_name')
            .eq('id', userId)
            .single();

          if (profile?.full_name) {
            await supabase
              .from('chat_messages')
              .insert({
                channel_id: channelId,
                sender_id: userId, // Ou um ID de sistema, se aplicável
                content: `${(await supabase.auth.getUser()).data.user?.user_metadata.full_name || 'Usuário'} entrou no canal.`,
                message_type: 'system_join', // ✅ CORREÇÃO CRÍTICA: Adicionar message_type para filtro v3
              })
              .select()
              .single();
          } else {
            logQueryEvent("useChannelAutoJoin", "Não foi possível buscar nome do perfil para mensagem de sistema", { 
              channelId, 
              userId 
            }, "error");
          }
        } catch (systemMsgError) {
          // Não impedir o fluxo principal se a mensagem de sistema falhar
          logQueryEvent("useChannelAutoJoin", "Erro ao criar mensagem de sistema", { 
            error: systemMsgError 
          }, "error");
        }

        // Operação bem-sucedida
        logQueryEvent("useChannelAutoJoin", "Entrou no canal com sucesso", { 
          channelId, 
          userId 
        });
        
        // Disparar evento para atualizar a lista de canais em outros componentes
        window.dispatchEvent(new CustomEvent('channelJoinedForceRefresh'));
        
        return { joined: true, reason: 'success' };
      } catch (error) {
        logQueryEvent("useChannelAutoJoin", "Erro inesperado ao tentar entrar no canal", { 
          channelId, 
          userId, 
          error 
        }, "error");
        throw error;
      }
    },
    onSuccess: (result) => {
      if (result.joined) {
        // Invalidar queries relacionadas ao canal
        queryClient.invalidateQueries({ queryKey: QueryKeys.chatMessages.byChannel(channelId!) });
        
        // Chamar callback de sucesso, se fornecido
        if (onJoinSuccess) {
          onJoinSuccess();
        }
      }
    }
  });

  // Efeito para tentar entrar no canal automaticamente
  useEffect(() => {
    if (!enabled || !channelId || !userId || joinChannelMutation.isPending) {
      return;
    }

    joinChannelMutation.mutate();
  }, [channelId, userId, enabled]);

  return {
    ...joinChannelMutation,
    joinChannel: joinChannelMutation.mutate
  };
}
