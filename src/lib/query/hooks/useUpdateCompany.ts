/**
 * <AUTHOR> Internet 2025
 */

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { QueryKeys } from '@/lib/query/queryKeys';
import { successWithNotification, errorWithNotification } from '@/lib/notifications/toastWithNotification';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { cleanCNPJ } from '@/lib/utils/cnpjUtils';

interface UpdateCompanyData {
  id: string;
  name: string;
  cnpj?: string;
  description?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  // Campos de gestores
  primary_manager_id?: string;
  substitute_manager_id?: string;
  project_manager_name?: string;
  project_manager_email?: string;
  project_manager_phone?: string;
  technical_manager_name?: string;
  technical_manager_email?: string;
  technical_manager_phone?: string;
  financial_manager_name?: string;
  financial_manager_email?: string;
  financial_manager_phone?: string;
  // Novos campos de contexto e cultura
  business_segment?: string;
  company_size?: string;
  mission?: string;
  vision?: string;
  company_values?: string[];
  organizational_culture?: string;
  target_market?: string;
  main_clients?: string[];
  main_products?: string[];
  ai_context_notes?: string;
}

export function useUpdateCompany() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateCompanyData) => {
      logQueryEvent('useUpdateCompany', '🔄 Atualizando dados da empresa', { 
        companyId: data.id,
        allData: data 
      }, 'info');

      const updateData = {
        name: data.name,
        cnpj: data.cnpj ? cleanCNPJ(data.cnpj) : null, // Salvar apenas números
        description: data.description || null,
        website: data.website || null,
        contact_email: data.contact_email || null,
        contact_phone: data.contact_phone || null,
        // Campos de gestores
        primary_manager_id: data.primary_manager_id || null,
        substitute_manager_id: data.substitute_manager_id || null,
        project_manager_name: data.project_manager_name || null,
        project_manager_email: data.project_manager_email || null,
        project_manager_phone: data.project_manager_phone || null,
        technical_manager_name: data.technical_manager_name || null,
        technical_manager_email: data.technical_manager_email || null,
        technical_manager_phone: data.technical_manager_phone || null,
        financial_manager_name: data.financial_manager_name || null,
        financial_manager_email: data.financial_manager_email || null,
        financial_manager_phone: data.financial_manager_phone || null,
        // Novos campos de contexto e cultura
        business_segment: data.business_segment || null,
        company_size: data.company_size || null,
        mission: data.mission || null,
        vision: data.vision || null,
        company_values: data.company_values || null,
        organizational_culture: data.organizational_culture || null,
        target_market: data.target_market || null,
        main_clients: data.main_clients || null,
        main_products: data.main_products || null,
        ai_context_notes: data.ai_context_notes || null,
        updated_at: new Date().toISOString(),
      };

      logQueryEvent('useUpdateCompany', '🔍 Dados preparados para UPDATE', { 
        updateData 
      }, 'info');

      const { data: result, error } = await supabase
        .from('companies')
        .update(updateData)
        .eq('id', data.id)
        .select();

      if (error) {
        logQueryEvent('useUpdateCompany', '❌ Erro ao atualizar empresa', { 
          error,
          companyId: data.id 
        }, 'error');
        throw error;
      }

      logQueryEvent('useUpdateCompany', '✅ Empresa atualizada com sucesso', { 
        result,
        companyId: data.id,
        rowsAffected: result?.length || 0
      }, 'success');
      return result;
    },
    onSuccess: () => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: QueryKeys.admin.tenantList() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.admin.stats() });
      
      // Invalidar todas as queries relacionadas a companies
      queryClient.invalidateQueries({ queryKey: ['companies'] });
      queryClient.invalidateQueries({ queryKey: ['admin'] });

      successWithNotification('Dados atualizados!', {
        description: 'As informações da empresa foram atualizadas com sucesso.',
      });
    },
    onError: (error: any) => {
      logQueryEvent('useUpdateCompany', 'Erro na mutação', { error }, 'error');
      
      let title = 'Erro ao atualizar';
      let description = 'Não foi possível atualizar os dados da empresa.';

      // Tratar erros específicos
      if (error?.code === '23505') {
        title = 'Dados duplicados';
        description = 'Já existe uma empresa com essas informações.';
      } else if (error?.code === '23502') {
        title = 'Dados obrigatórios';
        description = 'Alguns campos obrigatórios não foram preenchidos.';
      } else if (error?.message?.includes('email')) {
        title = 'Email inválido';
        description = 'Verifique se os emails estão no formato correto.';
      } else if (error?.message?.includes('website')) {
        title = 'Website inválido';
        description = 'Verifique se o website está no formato correto (com http/https).';
      }

      errorWithNotification(title, { description });
    },
  });
} 