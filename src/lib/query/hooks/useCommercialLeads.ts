/**
 * Hooks para o sistema de CRM de leads comerciais
 * Enhanced com Sistema de Fluxo de Addons
 * <AUTHOR> Internet 2025
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { QueryKeys } from '../queryKeys';
import { useAuthStore } from '@/stores/authStore';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { successWithNotification, errorWithNotification } from '@/lib/notifications/toastWithNotification';
import type { 
  CommercialLead, 
  CommercialLeadStats, 
  CommercialLeadFilters, 
  CommercialLeadUpdate,
  CommercialLeadCreateData,
  RequestTypeAnalysis,
  ConsolidationResult,
  SubmitLeadV2Data,
  SubmitLeadV2Response,
  DecisionPattern,
  RequestType
} from '@/types/commercial-leads.types';

/**
 * Hook para buscar todos os leads comerciais com filtros
 * 
 * ⚠️ IMPORTANTE: Esta tela é EXCLUSIVA do Vindula (equipe comercial)
 * - O Vindula precisa ver leads de TODAS as empresas para gestão comercial
 * - A política RLS permite que o Vindula acesse todos os leads
 * - Outras empresas só veem seus próprios leads (via RLS)
 * - NÃO filtrar por company_id aqui - deixar o RLS decidir
 */
export function useCommercialLeads(filters: Partial<CommercialLeadFilters> = {}) {
  const company_id = useAuthStore((state) => state.company_id);

  return useQuery({
    queryKey: QueryKeys.commercialLeads.all(filters),
    queryFn: async (): Promise<CommercialLead[]> => {
      logQueryEvent('useCommercialLeads', 'Buscando leads comerciais (Vindula - todas empresas)', { filters });

      // NOTA: Não verificamos company_id aqui porque:
      // 1. Esta tela é exclusiva do Vindula
      // 2. O Vindula precisa ver leads de todas as empresas
      // 3. A política RLS já controla o acesso adequadamente

      let query = supabase
        .from('commercial_leads')
        .select(`
          *,
          profiles:user_id(id, full_name, email),
          companies:company_id(id, name),
          current_plan:current_plan_id(id, name, price)
        `)
        .order('created_at', { ascending: false });

      // 🔒 SEGURANÇA: A política RLS controla automaticamente:
      // - Vindula: vê todos os leads de todas as empresas
      // - Outras empresas: veem apenas seus próprios leads

      // Aplicar filtros
      if (filters.search) {
        query = query.or(`profiles.full_name.ilike.%${filters.search}%,profiles.email.ilike.%${filters.search}%,commercial_notes.ilike.%${filters.search}%`);
      }

      if (filters.status && filters.status !== 'all') {
        query = query.eq('status', filters.status);
      }

      if (filters.source && filters.source !== 'all') {
        query = query.eq('source_context', filters.source);
      }

      // ===== NOVOS FILTROS DO ADDON FLOW =====
      
      if (filters.request_type && filters.request_type !== 'all') {
        query = query.eq('request_type', filters.request_type);
      }

      if (filters.priority_range) {
        query = query
          .gte('request_priority', filters.priority_range.min)
          .lte('request_priority', filters.priority_range.max);
      }

      if (filters.is_consolidated !== 'all' && typeof filters.is_consolidated === 'boolean') {
        query = query.eq('is_consolidated', filters.is_consolidated);
      }

      if (filters.urgency_level && filters.urgency_level !== 'all') {
        switch (filters.urgency_level) {
          case 'high':
            query = query.lte('request_priority', 3);
            break;
          case 'medium':
            query = query.gte('request_priority', 4).lte('request_priority', 6);
            break;
          case 'low':
            query = query.gte('request_priority', 7);
            break;
        }
      }

      // Filtro de cortesia
      if (filters.courtesy_status) {
        const now = new Date().toISOString();
        switch (filters.courtesy_status) {
          case 'active':
            query = query
              .not('courtesy_period_start', 'is', null)
              .not('courtesy_period_end', 'is', null)
              .lte('courtesy_period_start', now)
              .gte('courtesy_period_end', now);
            break;
          case 'expired':
            query = query
              .not('courtesy_period_end', 'is', null)
              .lt('courtesy_period_end', now);
            break;
          case 'none':
            query = query.is('courtesy_period_start', null);
            break;
        }
      }

      // Filtro de data
      if (filters.date_range?.start) {
        query = query.gte('created_at', filters.date_range.start);
      }
      if (filters.date_range?.end) {
        query = query.lte('created_at', filters.date_range.end);
      }

      const { data, error } = await query;

      if (error) {
        logQueryEvent('useCommercialLeads', 'Erro ao buscar leads', { error }, 'error');
        throw error;
      }

      logQueryEvent('useCommercialLeads', 'Leads encontrados', { count: data?.length });
      return data || [];
    },
    // Sempre habilitado - o RLS controla o acesso
    enabled: true,
    // 🔄 SEM CACHE: Dados sempre atualizados para equipe comercial
    staleTime: 0,
    cacheTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  });
}

/**
 * Hook para buscar estatísticas dos leads comerciais
 * 
 * ⚠️ IMPORTANTE: Estatísticas para equipe comercial do Vindula
 * - Vindula vê estatísticas de TODAS as empresas
 * - A política RLS controla automaticamente o acesso
 * - NÃO filtrar por company_id - deixar o RLS decidir
 */
export function useCommercialLeadStats() {
  const company_id = useAuthStore((state) => state.company_id);

  return useQuery({
    queryKey: QueryKeys.commercialLeads.stats(),
    queryFn: async (): Promise<CommercialLeadStats> => {
      logQueryEvent('useCommercialLeadStats', 'Buscando estatísticas de leads (Vindula - todas empresas)');

      // NOTA: Não verificamos company_id aqui porque:
      // 1. Esta tela é exclusiva do Vindula
      // 2. O Vindula precisa ver estatísticas de todas as empresas
      // 3. A política RLS já controla o acesso adequadamente

      // Buscar estatísticas básicas
      const { data: leads, error } = await supabase
        .from('commercial_leads')
        .select('*');
        // 🔒 SEGURANÇA: A política RLS controla automaticamente o acesso

      if (error) {
        logQueryEvent('useCommercialLeadStats', 'Erro ao buscar estatísticas', { error }, 'error');
        throw error;
      }

      const now = new Date().toISOString();
      
      // Calcular estatísticas
      const totalLeads = leads?.length || 0;
      const activeCourtesy = leads?.filter(lead => 
        lead.courtesy_period_start && 
        lead.courtesy_period_end &&
        lead.courtesy_period_start <= now &&
        lead.courtesy_period_end >= now
      ).length || 0;

      const wonLeads = leads?.filter(lead => lead.status === 'converted').length || 0;
      const conversionRate = totalLeads > 0 ? (wonLeads / totalLeads) * 100 : 0;

      const pipelineValue = leads?.reduce((sum, lead) => {
        return sum + (lead.estimated_monthly_value || 0);
      }, 0) || 0;

      // Agrupar por status
      const leadsByStatus = leads?.reduce((acc, lead) => {
        acc[lead.status] = (acc[lead.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

      // Agrupar por fonte
      const leadsBySource = leads?.reduce((acc, lead) => {
        const source = lead.source_context || 'unknown';
        acc[source] = (acc[source] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

      // ===== NOVAS MÉTRICAS DO ADDON FLOW =====
      
      // Contadores por tipo de request
      const leadsByRequestType = leads?.reduce((acc, lead) => {
        const type = lead.request_type || 'unknown';
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {} as Record<RequestType, number>) || {};

      // Distribuição de prioridade
      const priorityDistribution = leads?.reduce((acc, lead) => {
        const priority = lead.request_priority?.toString() || 'unknown';
        acc[priority] = (acc[priority] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

      // Estatísticas de consolidação
      const consolidatedLeads = leads?.filter(lead => lead.is_consolidated).length || 0;
      const singleRequests = leads?.filter(lead => !lead.is_consolidated).length || 0;
      const uniqueGroups = new Set(leads?.
        filter(lead => lead.consolidation_group_id)
        .map(lead => lead.consolidation_group_id)
      ).size;
      const averageConsolidationCount = uniqueGroups > 0 ? 
        (leads?.filter(lead => lead.consolidation_group_id).length || 0) / uniqueGroups : 0;

      // Análise de urgência
      const highPriorityCount = leads?.filter(lead => 
        lead.request_priority && lead.request_priority <= 3
      ).length || 0;
      const mediumPriorityCount = leads?.filter(lead => 
        lead.request_priority && lead.request_priority >= 4 && lead.request_priority <= 6
      ).length || 0;
      const lowPriorityCount = leads?.filter(lead => 
        lead.request_priority && lead.request_priority >= 7
      ).length || 0;

      // Tendência mensal simplificada (últimos 6 meses)
      const monthlyTrend = [];
      const currentDate = new Date();
      for (let i = 5; i >= 0; i--) {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
        const month = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        
        const monthData = leads?.filter(lead => 
          lead.created_at.startsWith(month)
        ) || [];
        
        const monthRequestTypes = monthData.reduce((acc, lead) => {
          const type = lead.request_type || 'unknown';
          acc[type] = (acc[type] || 0) + 1;
          return acc;
        }, {} as Record<RequestType, number>);

        monthlyTrend.push({
          month,
          leads: monthData.length,
          conversions: monthData.filter(lead => lead.status === 'converted').length,
          value: monthData.reduce((sum, lead) => sum + (lead.estimated_monthly_value || 0), 0),
          request_types: monthRequestTypes,
        });
      }

      const stats: CommercialLeadStats = {
        total_leads: totalLeads,
        active_courtesy: activeCourtesy,
        conversion_rate: conversionRate,
        pipeline_value: pipelineValue,
        leads_by_status: leadsByStatus,
        leads_by_source: leadsBySource,
        
        // Novas métricas do addon flow
        leads_by_request_type: leadsByRequestType,
        priority_distribution: priorityDistribution,
        consolidation_stats: {
          consolidated_leads: consolidatedLeads,
          single_requests: singleRequests,
          average_consolidation_count: averageConsolidationCount,
        },
        urgency_analysis: {
          high_priority_count: highPriorityCount,
          medium_priority_count: mediumPriorityCount,
          low_priority_count: lowPriorityCount,
        },
        monthly_trend: monthlyTrend,
      };

      logQueryEvent('useCommercialLeadStats', 'Estatísticas calculadas', { stats });
      return stats;
    },
    // Sempre habilitado - o RLS controla o acesso
    enabled: true,
    // 🔄 SEM CACHE: Estatísticas sempre atualizadas para equipe comercial
    staleTime: 0,
    cacheTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  });
}

/**
 * Hook para buscar um lead específico
 */
export function useCommercialLead(leadId: string) {
  const company_id = useAuthStore((state) => state.company_id);

  return useQuery({
    queryKey: QueryKeys.commercialLeads.detail(leadId),
    queryFn: async (): Promise<CommercialLead | null> => {
      logQueryEvent('useCommercialLead', 'Buscando lead específico', { leadId });

      if (!company_id || !leadId) {
        return null;
      }

      const { data, error } = await supabase
        .from('commercial_leads')
        .select(`
          *,
          user:profiles!commercial_leads_user_id_fkey(
            full_name, email
          ),
          company:companies!commercial_leads_company_id_fkey(
            name, slug
          ),
          current_plan:current_plan_id(id, name, price),
          selected_plan:selected_plan_id(id, name, price)
        `)
        .eq('id', leadId)
        .maybeSingle();
        // A política RLS já cuida da segurança - Vindula vê todos os leads, outras empresas veem apenas os próprios

      if (error) {
        logQueryEvent('useCommercialLead', 'Erro ao buscar lead', { error }, 'error');
        throw error;
      }

      logQueryEvent('useCommercialLead', 'Lead encontrado', { lead: data });
      return data;
    },
    enabled: !!company_id && !!leadId,
    // 🔄 SEM CACHE: Lead específico sempre atualizado
    staleTime: 0,
    cacheTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  });
}

/**
 * Hook para atualizar um lead comercial
 */
export function useUpdateCommercialLead() {
  const queryClient = useQueryClient();
  const company_id = useAuthStore((state) => state.company_id);

  return useMutation({
    mutationFn: async ({ leadId, updates }: { leadId: string; updates: CommercialLeadUpdate }) => {
      logQueryEvent('useUpdateCommercialLead', 'Atualizando lead', { leadId, updates });

      if (!company_id) {
        throw new Error('Company ID não encontrado');
      }

      // 🔍 Buscar estado atual do lead para histórico
      const { data: previousLead, error: fetchError } = await supabase
        .from('commercial_leads')
        .select('*')
        .eq('id', leadId)
        .single();

      if (fetchError) {
        logQueryEvent('useUpdateCommercialLead', 'Erro ao buscar lead atual', { fetchError }, 'error');
        throw fetchError;
      }

      // ✅ CORRIGIDO: Simplificar updateData sem objetos complexos
      const updateData: any = {
        ...updates,
        updated_at: new Date().toISOString(),
      };

      // Se for uma intervenção, atualizar campos relacionados
      if (updates.commercial_method) {
        updateData.commercial_contacted_at = updateData.commercial_contacted_at || new Date().toISOString();
        updateData.commercial_method = updates.commercial_method;
        
        // ✅ CORRIGIDO: Atualizar interventions_log de forma simples
        const currentLog = previousLead?.interventions_log || [];
        const newIntervention = {
          timestamp: new Date().toISOString(),
          type: updates.commercial_method,
          notes: updates.commercial_notes || null
        };
        
        updateData.interventions_log = [...currentLog, newIntervention];
      }

      // ✅ REMOVIDO: intervention_details problemático
      // O campo existe na tabela mas estava sendo usado incorretamente

      // 🔄 Atualizar o lead
      const { data: updatedLead, error } = await supabase
        .from('commercial_leads')
        .update(updateData)
        .eq('id', leadId)
        .select()
        .single();

      if (error) {
        logQueryEvent('useUpdateCommercialLead', 'Erro ao atualizar lead', { error }, 'error');
        throw error;
      }

      // 📝 Criar entrada no histórico
      try {
        // Detectar mudanças específicas
        const changes: string[] = [];
        if (updates.status && updates.status !== previousLead.status) {
          changes.push(`Status alterado de '${previousLead.status}' para '${updates.status}'`);
        }
        if (updates.commercial_notes) {
          changes.push('Notas comerciais adicionadas');
        }
        if (updates.commercial_method) {
          changes.push(`Intervenção registrada: ${updates.commercial_method}`);
        }
        if (updates.request_priority && updates.request_priority !== previousLead.request_priority) {
          changes.push(`Prioridade alterada de ${previousLead.request_priority || 'N/A'} para ${updates.request_priority}`);
        }

        const changesSummary = changes.length > 0 ? changes.join('; ') : 'Atualização do lead comercial';

        await supabase
          .from('commercial_lead_history')
          .insert({
            lead_id: leadId,
            company_id: company_id,
            user_id: previousLead.user_id,
            action_type: 'updated',
            action_details: {
              updates_applied: updates,
              changes_summary: changesSummary,
              timestamp: new Date().toISOString()
            },
            performed_by: null, // Sistema (pode ser melhorado para pegar o usuário logado)
            system_automated: false,
            previous_state: previousLead,
            current_state: updatedLead,
            business_context: 'Manual update via CRM interface',
            impact_analysis: {
              fields_changed: Object.keys(updates),
              status_transition: updates.status !== previousLead.status,
              priority_change: updates.request_priority !== previousLead.request_priority
            }
          });

        logQueryEvent('useUpdateCommercialLead', 'Histórico criado com sucesso', { leadId, changesSummary });
      } catch (historyError) {
        // Não falhar a operação principal se o histórico falhar
        logQueryEvent('useUpdateCommercialLead', 'Erro ao criar histórico (não-crítico)', { historyError }, 'warn');
      }

      logQueryEvent('useUpdateCommercialLead', 'Lead atualizado com sucesso', { leadId });
      return updatedLead;
    },
    onSuccess: (data) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.all() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.stats() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.detail(data.id) });
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.history(data.id) });

      successWithNotification('Lead atualizado!', {
        description: 'As informações do lead foram atualizadas com sucesso.',
      });
    },
    onError: (error: Error) => {
      errorWithNotification('Erro ao atualizar lead', {
        description: 'Não foi possível atualizar as informações do lead.',
      });
      logQueryEvent('useUpdateCommercialLead', 'Erro na mutação', { error }, 'error');
    }
  });
}

/**
 * Hook para criar um novo lead comercial
 */
export function useCreateCommercialLead() {
  const queryClient = useQueryClient();
  const company_id = useAuthStore((state) => state.company_id);

  return useMutation({
    mutationFn: async (leadData: CommercialLeadCreateData) => {
      logQueryEvent('useCreateCommercialLead', 'Criando novo lead', { leadData });

      if (!company_id) {
        throw new Error('Company ID não encontrado');
      }

      const createData = {
        ...leadData,
        company_id,
        status: 'pending' as const,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('commercial_leads')
        .insert(createData)
        .select()
        .single();

      if (error) {
        logQueryEvent('useCreateCommercialLead', 'Erro ao criar lead', { error }, 'error');
        throw error;
      }

      logQueryEvent('useCreateCommercialLead', 'Lead criado com sucesso', { leadId: data.id });
      return data.id;
    },
    onSuccess: () => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.all() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.stats() });

      successWithNotification('Lead criado!', {
        description: 'O novo lead foi adicionado ao sistema com sucesso.',
      });
    },
    onError: (error: Error) => {
      errorWithNotification('Erro ao criar lead', {
        description: 'Não foi possível criar o novo lead.',
      });
      logQueryEvent('useCreateCommercialLead', 'Erro na mutação', { error }, 'error');
    }
  });
}

/**
 * Hook para deletar um lead comercial
 */
export function useDeleteCommercialLead() {
  const queryClient = useQueryClient();
  const company_id = useAuthStore((state) => state.company_id);

  return useMutation({
    mutationFn: async (leadId: string) => {
      logQueryEvent('useDeleteCommercialLead', 'Deletando lead', { leadId });

      if (!company_id) {
        throw new Error('Company ID não encontrado');
      }

      const { error } = await supabase
        .from('commercial_leads')
        .delete()
        .eq('id', leadId);
        // A política RLS já cuida da segurança

      if (error) {
        logQueryEvent('useDeleteCommercialLead', 'Erro ao deletar lead', { error }, 'error');
        throw error;
      }

      logQueryEvent('useDeleteCommercialLead', 'Lead deletado com sucesso', { leadId });
      return leadId;
    },
    onSuccess: () => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.all() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.stats() });

      successWithNotification('Lead removido!', {
        description: 'O lead foi removido do sistema com sucesso.',
      });
    },
    onError: (error: Error) => {
      errorWithNotification('Erro ao remover lead', {
        description: 'Não foi possível remover o lead.',
      });
      logQueryEvent('useDeleteCommercialLead', 'Erro na mutação', { error }, 'error');
    }
  });
}

// ===== NOVOS HOOKS DO ADDON FLOW =====

export function useRequestTypeAnalysis(userId?: string) {
  return useQuery({
    queryKey: QueryKeys.commercialLeads.requestTypeAnalysis(userId),
    queryFn: async (): Promise<RequestTypeAnalysis> => {
      logQueryEvent('useRequestTypeAnalysis', 'Analisando tipo de request', { userId });

      const { data, error } = await supabase.rpc('identify_request_type_v1', {
        ...(userId && { p_user_id: userId })
      });

      if (error) {
        logQueryEvent('useRequestTypeAnalysis', 'Erro na análise', { error }, 'error');
        throw error;
      }

      const result = data?.[0];
      if (!result?.success) {
        throw new Error(result?.error_message || 'Erro na análise do tipo de request');
      }

      return {
        success: result.success,
        request_type: result.request_type,
        current_plan_id: result.current_plan_id,
        current_addons: result.current_addons,
        priority_score: result.priority_score,
        urgency_notes: result.urgency_notes,
        context_analysis: result.context_analysis
      };
    },
    enabled: !!userId,
    // 🔄 SEM CACHE: Análise sempre atualizada
    staleTime: 0,
    cacheTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  });
}

export function useSubmitLeadV2() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (leadData: SubmitLeadV2Data): Promise<SubmitLeadV2Response> => {
      logQueryEvent('useSubmitLeadV2', 'Submetendo lead v2', { leadData });

      const { data, error } = await supabase.rpc('submit_commercial_lead_v2', {
        p_requested_plan_id: leadData.requested_plan_id,
        p_requested_addons: leadData.requested_addons || [],
        p_contact_preferences: leadData.contact_preferences || {},
        p_upgrade_context: leadData.upgrade_context || {},
        p_source_context: leadData.source_context
      });

      if (error) {
        logQueryEvent('useSubmitLeadV2', 'Erro na requisição', { error }, 'error');
        throw error;
      }

      const result = data?.[0];
      if (!result?.success) {
        const structuredError = new Error(result?.error_message || 'Erro desconhecido');
        (structuredError as any).code = result?.error_code;
        throw structuredError;
      }

      logQueryEvent('useSubmitLeadV2', 'Lead v2 submetido com sucesso', { result });
      return result as SubmitLeadV2Response;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.all() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.stats() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.detail(data.lead_id) });

      const actionMessage = data.action_taken === 'consolidated' ? 
        'Request consolidado com sucesso!' : 
        data.action_taken === 'updated_existing' ?
        'Request existente atualizado!' :
        'Novo request criado!';

      successWithNotification(actionMessage, {
        description: `Tipo: ${data.request_type}, Prioridade: ${data.priority_score}`,
      });
    },
    onError: (error: any) => {
      logQueryEvent('useSubmitLeadV2', 'Erro na mutação', { error }, 'error');
      
      let title = 'Erro ao submeter request';
      let description = 'Não foi possível processar a solicitação.';

      switch (error.code) {
        case 'USER_NOT_FOUND':
          title = 'Usuário não encontrado';
          description = 'Usuário não encontrado ou sem empresa associada.';
          break;
        case 'VALIDATION_ERROR':
          title = 'Dados inválidos';
          description = 'Verifique os dados fornecidos.';
          break;
        case 'PERMISSION_DENIED':
          title = 'Sem permissão';
          description = 'Você não tem permissão para esta ação.';
          break;
      }

      errorWithNotification(title, { description, variant: 'destructive' });
    },
  });
}

export function useCommercialLeadHistory(leadId: string) {
  return useQuery({
    queryKey: QueryKeys.commercialLeads.history(leadId),
    queryFn: async (): Promise<CommercialLeadHistory[]> => {
      // Validar se leadId é válido
      if (!leadId || leadId.trim() === '') {
        logQueryEvent('useCommercialLeadHistory', 'Lead ID inválido - retornando array vazio', { leadId });
        return [];
      }

      logQueryEvent('useCommercialLeadHistory', 'Buscando histórico do lead', { leadId });

      const { data, error } = await supabase
        .from('commercial_lead_history')
        .select('*')
        .eq('lead_id', leadId)
        .order('created_at', { ascending: false });

      if (error) {
        logQueryEvent('useCommercialLeadHistory', 'Erro ao buscar histórico', { error }, 'error');
        throw error;
      }

      logQueryEvent('useCommercialLeadHistory', 'Histórico encontrado', { count: data?.length });
      return data as CommercialLeadHistory[];
    },
    enabled: !!leadId && leadId.trim() !== '', // Só executar se leadId for válido
    // 🔄 SEM CACHE: Histórico sempre atualizado
    staleTime: 0,
    cacheTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  });
}

export function useConsolidatedLeads() {
  return useQuery({
    queryKey: QueryKeys.commercialLeads.consolidated(),
    queryFn: async (): Promise<CommercialLead[]> => {
      logQueryEvent('useConsolidatedLeads', 'Buscando leads consolidados');

      const { data, error } = await supabase
        .from('commercial_leads')
        .select(`
          *,
          user:profiles!commercial_leads_user_id_fkey(
            full_name, email
          ),
          company:companies!commercial_leads_company_id_fkey(
            name, slug
          )
        `)
        .eq('is_consolidated', true)
        .order('created_at', { ascending: false });

      if (error) {
        logQueryEvent('useConsolidatedLeads', 'Erro ao buscar leads consolidados', { error }, 'error');
        throw error;
      }

      logQueryEvent('useConsolidatedLeads', 'Leads consolidados encontrados', { count: data?.length });
      return data as CommercialLead[];
    },
    // 🔄 SEM CACHE: Leads consolidados sempre atualizados
    staleTime: 0,
    cacheTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  });
}

export function useUrgentLeads() {
  return useQuery({
    queryKey: QueryKeys.commercialLeads.urgentLeads(),
    queryFn: async (): Promise<CommercialLead[]> => {
      logQueryEvent('useUrgentLeads', 'Buscando leads urgentes');

      const { data, error } = await supabase
        .from('commercial_leads')
        .select(`
          *,
          user:profiles!commercial_leads_user_id_fkey(
            full_name, email
          ),
          company:companies!commercial_leads_company_id_fkey(
            name, slug
          )
        `)
        .lte('request_priority', 3) // Prioridade alta (1-3)
        .in('status', ['pending', 'contacted'])
        .order('request_priority', { ascending: true })
        .order('created_at', { ascending: true });

      if (error) {
        logQueryEvent('useUrgentLeads', 'Erro ao buscar leads urgentes', { error }, 'error');
        throw error;
      }

      logQueryEvent('useUrgentLeads', 'Leads urgentes encontrados', { count: data?.length });
      return data as CommercialLead[];
    },
    // 🔄 SEM CACHE: Leads urgentes sempre atualizados  
    staleTime: 0,
    cacheTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  });
}

export function useDecisionPatterns() {
  return useQuery({
    queryKey: QueryKeys.commercialLeads.decisionPatterns(),
    queryFn: async (): Promise<DecisionPattern[]> => {
      logQueryEvent('useDecisionPatterns', 'Analisando padrões de decisão');

      const { data, error } = await supabase
        .from('commercial_leads')
        .select(`
          request_type,
          status,
          source_context,
          request_priority,
          decision_timeline_days,
          created_at,
          closed_at
        `)
        .not('request_type', 'is', null);

      if (error) {
        logQueryEvent('useDecisionPatterns', 'Erro ao buscar dados', { error }, 'error');
        throw error;
      }

      // Análise de padrões por tipo de request
      const patterns: Record<string, any> = {};

      data.forEach(lead => {
        const type = lead.request_type;
        if (!patterns[type]) {
          patterns[type] = {
            count: 0,
            conversions: 0,
            totalDecisionTime: 0,
            sources: {},
            priorities: {},
            timelines: []
          };
        }

        patterns[type].count++;
        
        if (lead.status === 'converted') {
          patterns[type].conversions++;
        }

        if (lead.created_at && lead.closed_at) {
          const decisionTime = Math.ceil(
            (new Date(lead.closed_at).getTime() - new Date(lead.created_at).getTime()) / 
            (1000 * 60 * 60 * 24)
          );
          patterns[type].timelines.push(decisionTime);
        }

        const source = lead.source_context || 'unknown';
        patterns[type].sources[source] = (patterns[type].sources[source] || 0) + 1;

        const priority = lead.request_priority?.toString() || 'unknown';
        patterns[type].priorities[priority] = (patterns[type].priorities[priority] || 0) + 1;
      });

      const result: DecisionPattern[] = Object.entries(patterns).map(([type, stats]: [string, any]) => {
        const conversionRate = stats.count > 0 ? (stats.conversions / stats.count) * 100 : 0;
        const avgDecisionTime = stats.timelines.length > 0 ? 
          stats.timelines.reduce((sum: number, time: number) => sum + time, 0) / stats.timelines.length : 0;

        const mostCommonSource = Object.entries(stats.sources)
          .sort(([,a], [,b]) => (b as number) - (a as number))[0]?.[0] || 'unknown';

        return {
          pattern_type: type as RequestType,
          frequency: stats.count,
          average_decision_time_days: Math.round(avgDecisionTime),
          conversion_rate: Math.round(conversionRate * 100) / 100,
          insights: {
            most_common_source: mostCommonSource,
            priority_distribution: stats.priorities,
            timeline_analysis: {
              fastest_conversion_days: Math.min(...stats.timelines) || 0,
              slowest_conversion_days: Math.max(...stats.timelines) || 0,
            }
          }
        };
      });

      logQueryEvent('useDecisionPatterns', 'Padrões analisados', { count: result.length });
      return result;
    },
    // 🔄 SEM CACHE: Padrões de decisão sempre atualizados
    staleTime: 0,
    cacheTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  });
}

// ===== HOOKS NOVOS - ADDON FLOW =====

/**
 * Hook para submeter novo lead comercial usando v2 (com addon flow)
 */
export function useSubmitCommercialLead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SubmitLeadV2Data) => {
      logQueryEvent('useSubmitCommercialLead', 'Submetendo lead comercial v2', { data }, 'info');

      const { data: result, error } = await supabase.rpc('submit_commercial_lead_v2', {
        p_requested_plan_id: data.requested_plan_id,
        p_selected_addons: data.selected_addons,
        p_lead_source: data.lead_source,
        p_contact_preferences: data.contact_preferences,
        p_upgrade_context: data.upgrade_context,
        p_metadata: data.metadata
      });

      if (error) {
        logQueryEvent('useSubmitCommercialLead', 'Erro na submissão', { error }, 'error');
        throw error;
      }

      const response = result?.[0];

      if (!response?.success) {
        const structuredError = new Error(response?.error_message || 'Erro desconhecido na submissão');
        (structuredError as any).code = response?.error_code;
        (structuredError as any).validation_details = response?.validation_details;
        throw structuredError;
      }

      logQueryEvent('useSubmitCommercialLead', 'Lead submetido com sucesso', { 
        leadId: response.lead_id,
        validationDetails: response.validation_details 
      }, 'info');

      return response;
    },
    onSuccess: async (response) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.all() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.stats() });

      const context = response.validation_details;
      
      // 🚀 ENVIO AUTOMÁTICO DE EMAIL PARA COMERCIAL
      try {
        logQueryEvent('useSubmitCommercialLead', 'Enviando email para comercial', { leadId: response.lead_id });
        
        const emailPayload = context?.email_payload;
        if (emailPayload) {
          const { error: emailError } = await supabase.functions.invoke('send-commercial-notification', {
            body: emailPayload
          });
          
          if (emailError) {
            logQueryEvent('useSubmitCommercialLead', 'Erro no envio de email comercial', { emailError }, 'error');
            // NÃO falhar o fluxo principal se email falhar
          } else {
            logQueryEvent('useSubmitCommercialLead', 'Email comercial enviado com sucesso', { leadId: response.lead_id });
          }
        }
      } catch (emailException) {
        logQueryEvent('useSubmitCommercialLead', 'Exceção no envio de email comercial', { emailException }, 'error');
        // NÃO falhar o fluxo principal se email falhar
      }
      
      successWithNotification('Lead comercial enviado!', {
        description: `Solicitação de ${context?.request_type || 'upgrade'} criada com sucesso. Nossa equipe entrará em contato em breve.`,
      });
    },
    onError: (error: any) => {
      const errorCode = error.code;
      const validationDetails = error.validation_details;
      let title = 'Erro na submissão';
      let description = 'Não foi possível submeter a solicitação.';

      switch (errorCode) {
        case 'USER_NOT_FOUND':
          title = 'Usuário não identificado';
          description = 'Faça login novamente e tente mais uma vez.';
          break;
        case 'INVALID_PLAN':
          title = 'Plano inválido';
          description = 'O plano selecionado não está disponível.';
          break;
        case 'RECENT_LEAD_EXISTS':
          title = 'Solicitação recente';
          const waitTime = validationDetails?.wait_time_hours || 24;
          description = `Você já tem uma solicitação similar nas últimas ${waitTime} horas. Aguarde ou entre em contato conosco.`;
          break;
        case 'VALIDATION_ERROR':
          title = 'Dados inválidos';
          description = 'Verifique os dados informados e tente novamente.';
          break;
        case 'INTERNAL_ERROR':
          title = 'Erro interno';
          description = 'Ocorreu um erro interno. Tente novamente em alguns minutos.';
          break;
        default:
          title = 'Erro na submissão';
          description = error.message || 'Não foi possível submeter a solicitação.';
      }

      errorWithNotification(title, { description });
    }
  });
}

/**
 * Hook para consolidar ou criar lead comercial usando função inteligente
 */
export function useConsolidateOrCreateLead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SubmitLeadV2Data) => {
      logQueryEvent('useConsolidateOrCreateLead', 'Consolidando ou criando lead', { data }, 'info');

      const { data: result, error } = await supabase.rpc('consolidate_or_create_lead_v3', {
        p_user_id: data.user_id,
        p_requested_plan_id: data.requested_plan_id,
        p_requested_addons: data.selected_addons,
        p_contact_preferences: data.contact_preferences,
        p_upgrade_context: data.upgrade_context,
        p_source_context: data.lead_source
      });

      if (error) {
        logQueryEvent('useConsolidateOrCreateLead', 'Erro na consolidação', { error }, 'error');
        throw error;
      }

      const response = result?.[0];

      if (!response?.success) {
        const structuredError = new Error(response?.error_message || 'Erro desconhecido na consolidação');
        (structuredError as any).code = response?.error_code;
        (structuredError as any).validation_details = response?.validation_details;
        throw structuredError;
      }

      logQueryEvent('useConsolidateOrCreateLead', 'Operação realizada com sucesso', { 
        leadId: response.lead_id,
        actionTaken: response.action_taken,
        validationDetails: response.validation_details 
      }, 'info');

      return response;
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.all() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.stats() });

      const context = response.validation_details;
      const actionTaken = response.action_taken;
      
      if (actionTaken === 'consolidated') {
        successWithNotification('Solicitação consolidada!', {
          description: `Sua solicitação foi consolidada com as anteriores. ${context?.changes_summary || 'Nossa equipe tem o contexto completo.'}`,
        });
      } else {
        successWithNotification('Nova solicitação criada!', {
          description: `Solicitação de ${context?.request_type || 'upgrade'} criada com sucesso. Nossa equipe entrará em contato em breve.`,
        });
      }
    },
    onError: (error: any) => {
      const errorCode = error.code;
      const validationDetails = error.validation_details;
      let title = 'Erro na operação';
      let description = 'Não foi possível processar a solicitação.';

      switch (errorCode) {
        case 'USER_NOT_FOUND':
          title = 'Usuário não identificado';
          description = 'Faça login novamente e tente mais uma vez.';
          break;
        case 'RECENT_LEAD_EXISTS':
          title = 'Solicitação recente';
          const waitTime = validationDetails?.wait_time_hours || 24;
          description = `Você já tem uma solicitação similar nas últimas ${waitTime} horas.`;
          break;
        default:
          title = 'Erro na operação';
          description = error.message || 'Não foi possível processar a solicitação.';
      }

      errorWithNotification(title, { description });
    }
  });
} 