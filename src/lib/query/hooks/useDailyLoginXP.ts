/**
 * Hook para processar XP de daily login
 * <AUTHOR> Internet 2025
 */
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
// import { useRealtimeNotifications } from '@/contexts/RealtimeNotificationsContext'; // Unused - only used for showXpGained which is handled in mutations
import { successWithNotification } from '@/lib/notifications/toastWithNotification';
import { QueryKeys } from '@/lib/query/queryKeys';

interface DailyLoginXPResult {
  success: boolean;
  wasAdded: boolean;
  message: string;
}

/**
 * Hook para processar XP de daily login
 */
export function useDailyLoginXP() {
  const queryClient = useQueryClient();

  const processDailyLoginXP = useMutation<DailyLoginXPResult, Error, string>({
    mutationFn: async (userId: string): Promise<DailyLoginXPResult> => {
      logQueryEvent('useDailyLoginXP', 'Iniciando processamento de daily login XP', { userId });

      try {
        const { data: wasAdded, error } = await supabase.rpc('add_daily_login_xp', {
          p_user_id: userId
        });

        if (error) {
          logQueryEvent('useDailyLoginXP', 'Erro ao chamar RPC add_daily_login_xp', error, 'error');
          throw new Error(`Erro ao processar daily login XP: ${error.message}`);
        }

        const result: DailyLoginXPResult = {
          success: true,
          wasAdded: wasAdded || false,
          message: wasAdded 
            ? 'XP de daily login adicionado com sucesso!' 
            : 'XP já havia sido processado hoje.'
        };

        logQueryEvent('useDailyLoginXP', 'Resultado do processamento', result);
        return result;

      } catch (error: unknown) {
        logQueryEvent('useDailyLoginXP', 'Erro inesperado ao processar daily login XP', error, 'error');
        
        const result: DailyLoginXPResult = {
          success: false,
          wasAdded: false,
          message: `Erro: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
        };
        
        return result;
      }
    },

    onSuccess: (result) => {
      if (result.success && result.wasAdded) {
        // Invalidar queries relacionadas à gamificação
        queryClient.invalidateQueries({ queryKey: QueryKeys.gamification.userLevel() });
        queryClient.invalidateQueries({ queryKey: QueryKeys.gamification.stardustBalance('current') });

        // Mostrar notificação de sucesso
        showDailyLoginNotification();
        
        logQueryEvent('useDailyLoginXP', 'Daily login XP processado e queries invalidadas');
      }
    },

    onError: (error) => {
      logQueryEvent('useDailyLoginXP', 'Erro na mutation de daily login XP', error, 'error');
      
      // Não mostrar erro para o usuário em caso de falha no daily login XP
      // É uma funcionalidade secundária que não deve interromper a experiência
    }
  });

  return {
    processDailyLoginXP: processDailyLoginXP.mutate,
    processDailyLoginXPAsync: processDailyLoginXP.mutateAsync,
    isProcessing: processDailyLoginXP.isPending,
    error: processDailyLoginXP.error,
    data: processDailyLoginXP.data
  };
}

/**
 * Função para mostrar notificação de daily login XP
 */
function showDailyLoginNotification() {
  try {
    // Toast simples e amigável
    successWithNotification("Daily Login! 🌟", {
      description: "Você ganhou 50 XP por fazer login hoje! Continue sua jornada no cosmos.",
      persist: false
    });

    logQueryEvent('useDailyLoginXP', 'Notificação de daily login exibida');
  } catch (error) {
    logQueryEvent('useDailyLoginXP', 'Erro ao exibir notificação', error, 'error');
  }
} 