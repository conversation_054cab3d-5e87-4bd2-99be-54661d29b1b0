/**
 * Hook para buscar última mensagem de um canal com atualização em tempo real
 * Conectado ao sistema de cache invalidation do UnifiedRealtimeProvider
 * <AUTHOR> Internet 2025
 */
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { QueryKeys } from '@/lib/query/queryKeys';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

interface LastMessage {
  content: string;
  created_at: string;
}

/**
 * Hook para buscar a última mensagem de um canal
 */
export function useChannelLastMessage(channelId: string, enabled = true) {
  return useQuery({
    queryKey: QueryKeys.chat.lastMessage(channelId),
    queryFn: async (): Promise<LastMessage | null> => {
      if (!channelId) {
        throw new Error('channelId é obrigatório');
      }

      try {
        logQueryEvent("useChannelLastMessage", "Buscando última mensagem", { channelId });

        const { data, error } = await supabase
          .from('chat_messages')
          .select('content, created_at')
          .eq('channel_id', channelId)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle();

        if (error) {
          logQueryEvent("useChannelLastMessage", "Erro ao buscar última mensagem", { error, channelId }, "error");
          throw error;
        }

        const lastMessage = data ? {
          content: data.content,
          created_at: data.created_at
        } : null;
        
        logQueryEvent("useChannelLastMessage", "Última mensagem carregada", { channelId, lastMessage });

        return lastMessage;
      } catch (error) {
        logQueryEvent("useChannelLastMessage", "Erro no hook", { error, channelId }, "error");
        throw error;
      }
    },
    enabled: Boolean(enabled && channelId),
    staleTime: 1000 * 30, // 30 segundos
    refetchInterval: false, // Atualização via WebSocket apenas
    retry: 2,
  });
}