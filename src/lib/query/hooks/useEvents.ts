/**
 * Hook para gerenciamento de eventos corporativos (dados reais do Supabase)
 * <AUTHOR> Internet 2025
 */
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { QueryKeys } from '../queryKeys';
import { supabase } from '@/integrations/supabase/client';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { successWithNotification, errorWithNotification } from '@/lib/notifications/toastWithNotification';

// Tipos do sistema de eventos
export type EventScope = 'company' | 'department' | 'personal';
export type EventCategory = 'corporate' | 'training' | 'social' | 'urgent' | 'personal';
export type EventPriority = 'high' | 'medium' | 'low';
export type ParticipantStatus = 'invited' | 'confirmed' | 'declined' | 'maybe';

export interface Event {
  id: string;
  title: string;
  description?: string;
  start_datetime: string;
  end_datetime?: string;
  
  // Visibilidade
  scope: EventScope;
  department_name?: string;
  
  // Categorização
  category: EventCategory;
  priority: EventPriority;
  
  // Criação e permissões
  created_by: {
    id: string;
    name: string;
    avatar_url?: string;
  };
  
  // Participação
  max_participants?: number;
  requires_confirmation: boolean;
  current_participants: number;
  user_status?: ParticipantStatus;
  
  // Localização
  location?: string;
  is_virtual: boolean;
  meeting_link?: string;
  
  // Metadados
  created_at: string;
  is_cancelled: boolean;
  is_recurring: boolean;
  parent_event_id?: string; // Para eventos filhos de séries recorrentes
  
  // Participantes (primeiros para visualização)
  participants_preview?: Array<{
    id: string;
    name: string;
    avatar_url?: string;
  }>;
}

// Interface para dados brutos do Supabase
interface SupabaseEventData {
  id: string;
  title: string;
  description?: string;
  start_datetime: string;
  end_datetime?: string;
  scope: EventScope;
  department_name?: string;
  category: EventCategory;
  priority: EventPriority;
  created_by_id: string;
  created_by_name: string;
  created_by_avatar?: string;
  max_participants?: number;
  requires_confirmation: boolean;
  current_participants: number;
  user_status?: ParticipantStatus;
  location?: string;
  is_virtual: boolean;
  meeting_link?: string;
  created_at: string;
  is_cancelled: boolean;
  is_recurring: boolean;
  parent_event_id?: string;
  participants_preview: Array<{ id: string; name: string; avatar_url?: string }> | string | null; // Pode ser JSON string ou array
}

// Transformador de dados do Supabase para a interface Event
const transformSupabaseEvent = (data: SupabaseEventData): Event => {
  // Parse do JSON dos participantes
  let participants_preview: Array<{ id: string; name: string; avatar_url?: string }> = [];
  
  if (data.participants_preview && typeof data.participants_preview === 'object') {
    try {
      participants_preview = Array.isArray(data.participants_preview) 
        ? data.participants_preview 
        : JSON.parse(JSON.stringify(data.participants_preview));
    } catch (error) {
      logQueryEvent('useEvents', 'Erro ao parsear participants_preview', { error }, 'warning');
      participants_preview = [];
    }
  }

  return {
    id: data.id,
    title: data.title,
    description: data.description,
    start_datetime: data.start_datetime,
    end_datetime: data.end_datetime,
    scope: data.scope,
    department_name: data.department_name,
    category: data.category,
    priority: data.priority,
    created_by: {
      id: data.created_by_id,
      name: data.created_by_name,
      avatar_url: data.created_by_avatar
    },
    max_participants: data.max_participants,
    requires_confirmation: data.requires_confirmation,
    current_participants: data.current_participants,
    user_status: data.user_status,
    location: data.location,
    is_virtual: data.is_virtual,
    meeting_link: data.meeting_link,
    created_at: data.created_at,
    is_cancelled: data.is_cancelled,
    is_recurring: data.is_recurring,
    parent_event_id: data.parent_event_id,
    participants_preview
  };
};

export interface UseUpcomingEventsOptions {
  limit?: number;
  scope?: EventScope | 'all';
  days_ahead?: number;
  include_personal?: boolean;
}

/**
 * Hook para buscar próximos eventos corporativos
 */
export function useUpcomingEvents(options: UseUpcomingEventsOptions = {}, queryOptions?: { enabled?: boolean }) {
  const { 
    limit = 10, 
    scope = 'all', 
    days_ahead = 7, 
    include_personal = true 
  } = options;

  // Log removido para reduzir verbosidade

  return useQuery({
    queryKey: QueryKeys.calendar.upcomingEvents({ limit, scope, days_ahead, include_personal }),
    queryFn: async (): Promise<Event[]> => {
      try {
        const { data, error } = await supabase.rpc('get_upcoming_events', {
          p_limit: limit,
          p_scope: scope,
          p_days_ahead: days_ahead,
          p_include_personal: include_personal
        });

        if (error) {
          logQueryEvent('useUpcomingEvents', 'Erro na RPC get_upcoming_events', { error }, 'error');
          // Retornar array vazio em caso de erro para evitar crash
          return [];
        }

        if (!data) {
          logQueryEvent('useUpcomingEvents', 'Nenhum evento encontrado');
          return [];
        }

        const events = data.map(transformSupabaseEvent);
        logQueryEvent('useUpcomingEvents', `${events.length} eventos carregados com sucesso`);
        
        return events;
      } catch (error) {
        logQueryEvent('useUpcomingEvents', 'Erro ao buscar eventos', { error }, 'error');
        // Retornar array vazio para evitar crash da aplicação
        return [];
      }
    },
    refetchInterval: false, // Desabilita refetch automático
    staleTime: 5 * 60 * 1000, // Dados ficam frescos por 5 minutos
    refetchOnMount: false, // Evita buscar dados frescos desnecessariamente
    refetchOnWindowFocus: false, // Evita múltiplas requisições ao focar
    retry: false, // Desabilita retry para evitar loops de erro
    meta: {
      persist: false // Não persistir queries com erro
    },
    ...queryOptions
  });
}

export interface UseCalendarEventsOptions {
  start_date?: string;
  end_date?: string;
  scope?: EventScope | 'all';
  include_personal?: boolean;
}

/**
 * Hook para buscar eventos do calendário em período específico
 */
export function useCalendarEvents(options: UseCalendarEventsOptions = {}, queryOptions?: { enabled?: boolean }) {
  const { 
    start_date = new Date().toISOString().split('T')[0], 
    end_date = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 dias
    scope = 'all',
    include_personal = true 
  } = options;

  logQueryEvent('useCalendarEvents', 'Buscando eventos do calendário', { 
    start_date, end_date, scope, include_personal 
  });

  return useQuery({
    queryKey: ['calendar-events', { start_date, end_date, scope, include_personal }],
    queryFn: async (): Promise<Event[]> => {
      try {
        const { data, error } = await supabase.rpc('get_calendar_events', {
          p_start_date: start_date,
          p_end_date: end_date,
          p_scope: scope,
          p_include_personal: include_personal
        });

        if (error) {
          logQueryEvent('useCalendarEvents', 'Erro na RPC get_calendar_events', { error }, 'error');
          // Retornar array vazio em caso de erro para evitar crash
          return [];
        }

        if (!data) {
          logQueryEvent('useCalendarEvents', 'Nenhum evento de calendário encontrado');
          return [];
        }

        const events = data.map(transformSupabaseEvent);
        logQueryEvent('useCalendarEvents', `${events.length} eventos de calendário carregados`);
        
        return events;
      } catch (error) {
        logQueryEvent('useCalendarEvents', 'Erro ao buscar eventos do calendário', { error }, 'error');
        // Retornar array vazio para evitar crash da aplicação
        return [];
      }
    },
    refetchInterval: false, // Desabilita refetch automático
    staleTime: 5 * 60 * 1000, // Dados ficam frescos por 5 minutos
    refetchOnMount: false, // Evita buscar dados frescos desnecessariamente
    refetchOnWindowFocus: false, // Evita múltiplas requisições ao focar
    retry: false, // Desabilita retry para evitar loops de erro
    meta: {
      persist: false // Não persistir queries com erro
    },
    ...queryOptions
  });
}

/**
 * Hook para buscar um evento específico por ID
 */
export function useEventById(eventId: string, options?: { enabled?: boolean }) {
  // Só fazer log se eventId for válido para evitar poluição de logs
  if (eventId) {
    logQueryEvent('useEventById', 'Buscando evento por ID', { eventId });
  }

  return useQuery({
    queryKey: ['event', eventId],
    queryFn: async (): Promise<Event | null> => {
      try {
        if (!eventId) {
          return null;
        }

        // Buscar evento básico primeiro
        const { data: eventData, error } = await supabase
          .from('corporate_events')
          .select(`
            id,
            title,
            description,
            start_datetime,
            end_datetime,
            scope,
            department_id,
            category,
            priority,
            created_by,
            max_participants,
            requires_confirmation,
            location,
            is_virtual,
            meeting_link,
            created_at,
            is_cancelled,
            is_recurring,
            parent_event_id
          `)
          .eq('id', eventId)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            // Evento não encontrado
            logQueryEvent('useEventById', 'Evento não encontrado', { eventId }, 'warning');
            return null;
          }
          throw error;
        }

        if (!eventData) {
          return null;
        }

        // Buscar dados do criador do evento
        const { data: creatorData } = await supabase
          .from('profiles')
          .select('id, full_name, email, avatar_url')
          .eq('id', eventData.created_by)
          .single();

        // Buscar nome do departamento se existir
        let departmentName: string | undefined;
        if (eventData.department_id) {
          const { data: departmentData } = await supabase
            .from('departments')
            .select('name')
            .eq('id', eventData.department_id)
            .single();
          departmentName = departmentData?.name;
        }

        // Buscar participantes confirmados (preview)
        const { data: participantsData } = await supabase
          .from('event_participants')
          .select(`
            status,
            user_id
          `)
          .eq('event_id', eventId)
          .eq('status', 'confirmed')
          .limit(3);

        // Buscar dados dos participantes em preview
        const participantsPreview = [];
        if (participantsData && participantsData.length > 0) {
          const userIds = participantsData.map(p => p.user_id);
          const { data: usersData } = await supabase
            .from('profiles')
            .select('id, full_name, email, avatar_url')
            .in('id', userIds);
          
          if (usersData) {
            participantsPreview.push(...usersData.map(user => ({
              id: user.id,
              name: user.full_name || user.email || 'Usuário',
              avatar_url: user.avatar_url
            })));
          }
        }

        // Contar total de participantes confirmados
        const { count: totalParticipants } = await supabase
          .from('event_participants')
          .select('*', { count: 'exact', head: true })
          .eq('event_id', eventId)
          .eq('status', 'confirmed');

        // Buscar status do usuário atual
        const currentUser = await supabase.auth.getUser();
        let userStatus: ParticipantStatus | undefined;
        if (currentUser.data.user?.id) {
          const { data: statusData } = await supabase
            .from('event_participants')
            .select('status')
            .eq('event_id', eventId)
            .eq('user_id', currentUser.data.user.id)
            .maybeSingle(); // Usar maybeSingle() em vez de single() para evitar erro quando não há registros
          userStatus = statusData?.status;
        }

        // Transformar dados
        const event: Event = {
          id: eventData.id,
          title: eventData.title,
          description: eventData.description,
          start_datetime: eventData.start_datetime,
          end_datetime: eventData.end_datetime,
          scope: eventData.scope,
          department_name: departmentName,
          category: eventData.category,
          priority: eventData.priority,
          created_by: {
            id: eventData.created_by,
            name: creatorData?.full_name || creatorData?.email || 'Usuário',
            avatar_url: creatorData?.avatar_url
          },
          max_participants: eventData.max_participants,
          requires_confirmation: eventData.requires_confirmation,
          current_participants: totalParticipants || 0,
          user_status: userStatus,
          location: eventData.location,
          is_virtual: eventData.is_virtual,
          meeting_link: eventData.meeting_link,
          created_at: eventData.created_at,
          is_cancelled: eventData.is_cancelled,
          is_recurring: eventData.is_recurring,
          parent_event_id: eventData.parent_event_id,
          participants_preview: participantsPreview
        };

        logQueryEvent('useEventById', 'Evento carregado com sucesso', { eventId });
        return event;
      } catch (error) {
        logQueryEvent('useEventById', 'Erro ao buscar evento', { eventId, error }, 'error');
        throw error;
      }
    },
    enabled: options?.enabled !== undefined ? options.enabled && !!eventId : !!eventId,
    staleTime: 5 * 60 * 1000, // Dados ficam frescos por 5 minutos
    retry: false, // Desabilita retry para evitar loops de erro
    meta: {
      persist: false // Não persistir queries com erro
    }
  });
}

// Interface para dados de criação de evento
export interface CreateEventData {
  title: string;
  description?: string;
  start_datetime: string; // ISO string
  end_datetime?: string;  // ISO string
  scope?: EventScope;
  department_id?: string;
  category?: EventCategory;
  priority?: EventPriority;
  max_participants?: number;
  requires_confirmation?: boolean;
  location?: string;
  is_virtual?: boolean;
  meeting_link?: string;
  is_recurring?: boolean;
  recurring_pattern?: Record<string, unknown>; // JSON objeto para padrão de recorrência
}

// Interface para resposta da criação
interface CreateEventResponse {
  id: string;
  title: string;
  message: string;
}

/**
 * Hook para criar novo evento
 */
export function useCreateEvent() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (eventData: CreateEventData): Promise<CreateEventResponse> => {
      logQueryEvent('useCreateEvent', 'Criando novo evento', eventData);
      
      const { data, error } = await supabase.rpc('create_corporate_event', {
        p_title: eventData.title,
        p_start_datetime: eventData.start_datetime,
        p_description: eventData.description,
        p_end_datetime: eventData.end_datetime,
        p_scope: eventData.scope || 'department',
        p_department_id: eventData.department_id,
        p_category: eventData.category || 'corporate',
        p_priority: eventData.priority || 'medium',
        p_max_participants: eventData.max_participants,
        p_requires_confirmation: eventData.requires_confirmation ?? true,
        p_location: eventData.location,
        p_is_virtual: eventData.is_virtual || false,
        p_meeting_link: eventData.meeting_link,
        p_is_recurring: eventData.is_recurring || false,
        p_recurring_pattern: eventData.recurring_pattern
      });
      
      if (error) {
        logQueryEvent('useCreateEvent', 'Erro ao criar evento', { error }, 'error');
        throw error;
      }
      
      if (!data || !data[0]) {
        throw new Error('Falha ao criar evento - resposta inválida');
      }
      
      return data[0];
    },
    onSuccess: (data) => {
      logQueryEvent('useCreateEvent', 'Evento criado com sucesso', data);
      
      // Invalidar TODOS os caches relacionados a eventos de forma mais agressiva
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['upcoming-events'] });
      queryClient.invalidateQueries({ queryKey: ['calendar-events'] });
      queryClient.invalidateQueries({ queryKey: ['pending-event-invites'] });
      
      // Remover qualquer cache específico que pode estar interferindo
      queryClient.removeQueries({ queryKey: ['events'] });
      
      // Forçar refetch imediato dos eventos principais com delay para garantir propagação
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ['upcoming-events'] });
        queryClient.refetchQueries({ queryKey: ['calendar-events'] });
      }, 100);
      
      // Exibir notificação de sucesso  
      successWithNotification('✅ Evento criado com sucesso!', {
        description: `O evento "${data.title}" foi criado e está disponível na agenda.`,
      });
    },
    onError: (error: any) => {
      logQueryEvent('useCreateEvent', 'Falha ao criar evento', { error }, 'error');
      
      // Tratar erros específicos do backend de forma amigável
      let title = '❌ Erro ao criar evento';
      let description = 'Não foi possível criar o evento.';
      
      if (error?.message) {
        switch (error.message) {
          case 'Data de início deve ser futura':
            title = '⏰ Data inválida';
            description = 'A data e horário do evento devem ser futuros. Verifique a data e horário selecionados.';
            break;
          case 'Data de fim deve ser posterior à data de início':
            title = '⏰ Horários inválidos';
            description = 'O horário de fim deve ser posterior ao horário de início.';
            break;
          case 'Link da reunião é obrigatório para eventos virtuais':
            title = '🔗 Link obrigatório';
            description = 'Para eventos virtuais, é necessário informar o link da reunião.';
            break;
          case 'Título do evento é obrigatório':
            title = '📝 Título obrigatório';
            description = 'O título do evento é obrigatório.';
            break;
          case 'Department ID é obrigatório para eventos de departamento':
            title = '🏢 Departamento não atribuído';
            description = 'Você precisa estar atribuído a um departamento para criar eventos departamentais.';
            break;
          case 'Usuário não encontrado ou não autenticado':
            title = '🔒 Erro de autenticação';
            description = 'Sessão expirada. Faça login novamente.';
            break;
          default:
            description = error.message;
        }
      }
      
      errorWithNotification(title, { description });
    }
  });
}

// Interface para resposta da participação
interface ParticipationResponse {
  success: boolean;
  message: string;
}

/**
 * Hook para atualizar status de participação
 */
/**
 * Hook para buscar convites de eventos pendentes do usuário atual
 */
export function usePendingEventInvites() {
  logQueryEvent('usePendingEventInvites', 'Buscando convites pendentes');

  return useQuery({
    queryKey: ['pending-event-invites'],
    queryFn: async (): Promise<Event[]> => {
      try {
        // Buscar eventos onde o usuário tem status 'invited'
        const { data, error } = await supabase.rpc('get_upcoming_events', {
          p_limit: 50, // Buscar mais eventos para filtrar os convites
          p_scope: 'all',
          p_days_ahead: 365, // 1 ano à frente para pegar todos os convites
          p_include_personal: true
        });

        if (error) {
          logQueryEvent('usePendingEventInvites', 'Erro na RPC get_upcoming_events', { error }, 'error');
          // Retornar array vazio em caso de erro
          return [];
        }

        if (!data) {
          logQueryEvent('usePendingEventInvites', 'Nenhum evento encontrado');
          return [];
        }

        // Filtrar apenas os eventos com status 'invited'
        const pendingInvites = data
          .filter((event: SupabaseEventData) => event.user_status === 'invited')
          .map(transformSupabaseEvent);

        logQueryEvent('usePendingEventInvites', `${pendingInvites.length} convites pendentes encontrados`);
        
        return pendingInvites;
      } catch (error) {
        logQueryEvent('usePendingEventInvites', 'Erro ao buscar convites pendentes', { error }, 'error');
        // Retornar array vazio para evitar crash da aplicação
        return [];
      }
    },
    refetchInterval: false, // Desabilita refetch automático
    staleTime: 2 * 60 * 1000, // Dados ficam frescos por 2 minutos
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    retry: false, // Desabilita retry para evitar loops de erro
    meta: {
      persist: false // Não persistir queries com erro
    },
  });
}

  export function useUpdateParticipation() {
    const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ eventId, status }: { eventId: string; status: ParticipantStatus }): Promise<ParticipationResponse> => {
      logQueryEvent('useUpdateParticipation', 'Atualizando participação', { eventId, status });
      
      const { data, error } = await supabase.rpc('update_event_participation', {
        p_event_id: eventId,
        p_status: status
      });
      
      if (error) {
        logQueryEvent('useUpdateParticipation', 'Erro ao atualizar participação', { error }, 'error');
        throw error;
      }
      
      if (!data || !data[0]) {
        throw new Error('Falha ao atualizar participação - resposta inválida');
      }
      
      return data[0];
    },
    onSuccess: (data, variables) => {
      logQueryEvent('useUpdateParticipation', 'Participação atualizada', data);
      
      // Exibir notificação visual baseada no status
      const statusMessages = {
        confirmed: {
          title: '✅ Presença confirmada!',
          description: 'Sua participação foi confirmada no evento.'
        },
        declined: {
          title: '❌ Convite recusado',
          description: 'Você recusou o convite para este evento.'
        },
        maybe: {
          title: '🤔 Marcado como "talvez"',
          description: 'Você marcou sua participação como incerta.'
        },
        invited: {
          title: '📝 Status atualizado',
          description: 'Status de participação atualizado com sucesso.'
        }
      };
      
      const message = statusMessages[variables.status] || statusMessages.invited;
      
      // Usar notificação de sucesso padrão para eventos
      successWithNotification(message.title, {
        description: message.description,
        persist: true,
        notificationType: 'event_participation_update'
      });
      
      // Invalidar caches relacionados ao evento específico
      queryClient.invalidateQueries({ queryKey: ['event', variables.eventId] });
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['upcoming-events'] });
      queryClient.invalidateQueries({ queryKey: ['calendar-events'] });
      queryClient.invalidateQueries({ queryKey: ['pending-event-invites'] });
    },
    onError: (error) => {
      logQueryEvent('useUpdateParticipation', 'Falha ao atualizar participação', { error }, 'error');
    }
  });
}

// Interface para resposta de convites
interface InviteResponse {
  success: boolean;
  message: string;
  invited_count: number;
}

/**
 * Hook para convidar participantes para evento
 */
export function useInviteParticipants() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (variables: {
      eventId: string;
      userIds: string[];
    }) => {
      const { data, error } = await supabase.rpc('invite_event_participants', {
        p_event_id: variables.eventId,
        p_user_ids: variables.userIds
      });

      if (error) {
        logQueryEvent('useInviteParticipants', 'Erro ao convidar participantes', { error, variables }, 'error');
        throw error;
      }

      if (!data?.[0]?.success) {
        const errorMessage = data?.[0]?.message || 'Erro desconhecido ao convidar participantes';
        logQueryEvent('useInviteParticipants', 'Função retornou erro', { message: errorMessage, variables }, 'error');
        throw new Error(errorMessage);
      }

      logQueryEvent('useInviteParticipants', 'Participantes convidados com sucesso', { 
        eventId: variables.eventId, 
        invitedCount: data[0].invited_count 
      });
      return data[0];
    },
    onSuccess: (data, variables) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['event', variables.eventId] });
      queryClient.invalidateQueries({ queryKey: ['event-participants', variables.eventId] });
      
      // Exibir notificação de sucesso
      successWithNotification('Participantes convidados!', {
        description: `${data.invited_count} participante(s) foram convidados para o evento.`,
      });
    },
    onError: (error) => {
      logQueryEvent('useInviteParticipants', 'Erro na mutação de convite', { error }, 'error');
      errorWithNotification('Erro ao convidar participantes', {
        description: error instanceof Error ? error.message : 'Não foi possível enviar os convites.',
      });
    },
  });
}

// Hook para atualizar evento corporativo
export function useUpdateEvent() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (variables: {
      eventId: string;
      title?: string;
      description?: string;
      startDate?: string;
      endDate?: string;
      location?: string;
      allDay?: boolean;
      departmentId?: string;
    }) => {
      const { data, error } = await supabase.rpc('update_corporate_event', {
        p_event_id: variables.eventId,
        p_title: variables.title,
        p_description: variables.description,
        p_start_date: variables.startDate,
        p_end_date: variables.endDate,
        p_location: variables.location,
        p_all_day: variables.allDay,
        p_department_id: variables.departmentId
      });

      if (error) {
        logQueryEvent('useUpdateEvent', 'Erro ao atualizar evento', { error, variables }, 'error');
        throw error;
      }

      if (!data?.[0]?.success) {
        const errorMessage = data?.[0]?.message || 'Erro desconhecido ao atualizar evento';
        logQueryEvent('useUpdateEvent', 'Função retornou erro', { message: errorMessage, variables }, 'error');
        throw new Error(errorMessage);
      }

      logQueryEvent('useUpdateEvent', 'Evento atualizado com sucesso', { eventId: variables.eventId });
      return data[0];
    },
    onSuccess: (data, variables) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['event', variables.eventId] });
      queryClient.invalidateQueries({ queryKey: ['calendar-events'] });
      
      // Exibir notificação de sucesso
      successWithNotification('Evento atualizado!', {
        description: 'As alterações do evento foram salvas e os participantes foram notificados.',
      });
    },
    onError: (error) => {
      logQueryEvent('useUpdateEvent', 'Erro na mutação de atualização', { error }, 'error');
      errorWithNotification('Erro ao atualizar evento', {
        description: error instanceof Error ? error.message : 'Não foi possível atualizar o evento.',
      });
    },
  });
}

// Hook para cancelar evento corporativo
export function useCancelEvent() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (variables: {
      eventId: string;
      cancellationReason?: string;
    }) => {
      const { data, error } = await supabase.rpc('cancel_corporate_event', {
        p_event_id: variables.eventId,
        p_cancellation_reason: variables.cancellationReason || 'Cancelado pelo organizador'
      });

      if (error) {
        logQueryEvent('useCancelEvent', 'Erro ao cancelar evento', { error, variables }, 'error');
        throw error;
      }

      if (!data?.[0]?.success) {
        const errorMessage = data?.[0]?.message || 'Erro desconhecido ao cancelar evento';
        logQueryEvent('useCancelEvent', 'Função retornou erro', { message: errorMessage, variables }, 'error');
        throw new Error(errorMessage);
      }

      logQueryEvent('useCancelEvent', 'Evento cancelado com sucesso', { eventId: variables.eventId });
      return data[0];
    },
    onSuccess: (data, variables) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['event', variables.eventId] });
      queryClient.invalidateQueries({ queryKey: ['calendar-events'] });
      
      // Exibir notificação de sucesso
      successWithNotification('Evento cancelado!', {
        description: 'O evento foi cancelado e todos os participantes foram notificados.',
      });
    },
    onError: (error) => {
      logQueryEvent('useCancelEvent', 'Erro na mutação de cancelamento', { error }, 'error');
      errorWithNotification('Erro ao cancelar evento', {
        description: error instanceof Error ? error.message : 'Não foi possível cancelar o evento.',
      });
    },
  });
}

// Tipos para cancelamento de eventos recorrentes
export type CancelScope = 'single' | 'future' | 'series';

/**
 * Hook para cancelar eventos recorrentes com diferentes escopos
 */
export function useCancelRecurringEvent() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (variables: {
      eventId: string;
      cancelScope: CancelScope;
      cancellationReason?: string;
    }) => {
      logQueryEvent('useCancelRecurringEvent', 'Cancelando evento recorrente', variables);
      
      const { data, error } = await supabase.rpc('cancel_recurring_event', {
        p_event_id: variables.eventId,
        p_cancel_scope: variables.cancelScope,
        p_cancellation_reason: variables.cancellationReason || 'Cancelado pelo organizador'
      });

      if (error) {
        logQueryEvent('useCancelRecurringEvent', 'Erro ao cancelar evento recorrente', { error, variables }, 'error');
        throw error;
      }

      if (!data?.[0]?.success) {
        const errorMessage = data?.[0]?.message || 'Erro desconhecido ao cancelar evento';
        logQueryEvent('useCancelRecurringEvent', 'Função retornou erro', { message: errorMessage, variables }, 'error');
        throw new Error(errorMessage);
      }

      logQueryEvent('useCancelRecurringEvent', 'Evento(s) cancelado(s) com sucesso', { 
        eventId: variables.eventId, 
        scope: variables.cancelScope,
        eventsCancelled: data[0].events_cancelled 
      });
      return data[0];
    },
    onSuccess: (data, variables) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['event', variables.eventId] });
      queryClient.invalidateQueries({ queryKey: ['calendar-events'] });
      queryClient.invalidateQueries({ queryKey: ['upcoming-events'] });
      queryClient.invalidateQueries({ queryKey: ['event-series', variables.eventId] });
      
      // Mensagens baseadas no escopo
      const scopeMessages = {
        single: {
          title: 'Evento cancelado!',
          description: 'O evento foi cancelado e os participantes foram notificados.'
        },
        future: {
          title: 'Eventos futuros cancelados!',
          description: `${data.events_cancelled} evento(s) futuro(s) foram cancelados e os participantes notificados.`
        },
        series: {
          title: 'Série cancelada!',
          description: `Toda a série (${data.events_cancelled} evento(s)) foi cancelada e os participantes notificados.`
        }
      };
      
      const message = scopeMessages[variables.cancelScope];
      successWithNotification(message.title, {
        description: message.description,
      });
    },
    onError: (error) => {
      logQueryEvent('useCancelRecurringEvent', 'Erro na mutação de cancelamento recorrente', { error }, 'error');
      errorWithNotification('Erro ao cancelar evento(s)', {
        description: error instanceof Error ? error.message : 'Não foi possível cancelar o(s) evento(s).',
      });
    },
  });
}

// Hook para enviar lembretes de eventos (função administrativa)
export function useSendEventReminders() {
  return useMutation({
    mutationFn: async () => {
      const { data, error } = await supabase.rpc('send_event_reminders');

      if (error) {
        logQueryEvent('useSendEventReminders', 'Erro ao enviar lembretes', { error }, 'error');
        throw error;
      }

      if (!data?.[0]?.success) {
        const errorMessage = data?.[0]?.message || 'Erro desconhecido ao enviar lembretes';
        logQueryEvent('useSendEventReminders', 'Função retornou erro', { message: errorMessage }, 'error');
        throw new Error(errorMessage);
      }

      logQueryEvent('useSendEventReminders', 'Lembretes enviados com sucesso', { 
        remindersSent: data[0].reminders_sent 
      });
      return data[0];
    },
    onSuccess: (data) => {
      // Exibir notificação de sucesso
      successWithNotification('Lembretes enviados!', {
        description: `${data.reminders_sent} lembrete(s) de eventos foram enviados.`,
      });
    },
    onError: (error) => {
      logQueryEvent('useSendEventReminders', 'Erro na mutação de lembretes', { error }, 'error');
      errorWithNotification('Erro ao enviar lembretes', {
        description: error instanceof Error ? error.message : 'Não foi possível enviar os lembretes.',
      });
    },
  });
}

// Alias para compatibilidade com componentes
export const useUpdateEventParticipation = useUpdateParticipation;

// ================================
// FUNCIONALIDADES DE RECORRÊNCIA
// ================================

// Interface para dados de série de eventos
export interface EventSeries {
  id: string;
  title: string;
  start_datetime: string;
  end_datetime?: string;
  is_parent: boolean;
  parent_event_id?: string;
}

/**
 * Hook para buscar série de eventos (pai + filhos)
 */
export function useEventSeries(eventId?: string) {
  return useQuery({
    queryKey: ['event-series', eventId],
    queryFn: async (): Promise<EventSeries[]> => {
      if (!eventId) return [];
      
      logQueryEvent('useEventSeries', 'Buscando série de eventos', { eventId });
      
      const { data, error } = await supabase.rpc('get_event_series', {
        p_event_id: eventId
      });
      
      if (error) {
        logQueryEvent('useEventSeries', 'Erro ao buscar série', { error }, 'error');
        throw error;
      }
      
      return data || [];
    },
    enabled: !!eventId,
    staleTime: 60 * 1000 // Cache por 1 minuto
  });
}

/**
 * Hook para gerar instâncias de eventos recorrentes
 */
export function useGenerateRecurringInstances() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (variables: {
      eventId: string;
      startDate?: string;
      endDate?: string;
    }) => {
      logQueryEvent('useGenerateRecurringInstances', 'Gerando instâncias', variables);
      
      const { data, error } = await supabase.rpc('generate_recurring_event_instances', {
        p_event_id: variables.eventId,
        p_start_date: variables.startDate,
        p_end_date: variables.endDate
      });
      
      if (error) {
        logQueryEvent('useGenerateRecurringInstances', 'Erro ao gerar instâncias', { error }, 'error');
        throw error;
      }
      
      return { instances_created: data };
    },
    onSuccess: (data, variables) => {
      logQueryEvent('useGenerateRecurringInstances', 'Instâncias geradas', data);
      
      // Invalidar caches
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['upcoming-events'] });
      queryClient.invalidateQueries({ queryKey: ['calendar-events'] });
      queryClient.invalidateQueries({ queryKey: ['event-series', variables.eventId] });
      
      successWithNotification('Instâncias geradas!', {
        description: `${data.instances_created} instância(s) do evento recorrente foram criadas.`,
      });
    },
    onError: (error) => {
      logQueryEvent('useGenerateRecurringInstances', 'Erro na geração', { error }, 'error');
      errorWithNotification('Erro ao gerar instâncias', {
        description: error instanceof Error ? error.message : 'Não foi possível gerar as instâncias.',
      });
    },
  });
}

/**
 * Hook para atualizar série de eventos
 */
export function useUpdateEventSeries() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (variables: {
      eventId: string;
      title?: string;
      description?: string;
      location?: string;
      isVirtual?: boolean;
      meetingLink?: string;
      updateFutureOnly?: boolean;
    }) => {
      logQueryEvent('useUpdateEventSeries', 'Atualizando série', variables);
      
      const { data, error } = await supabase.rpc('update_event_series', {
        p_event_id: variables.eventId,
        p_title: variables.title,
        p_description: variables.description,
        p_location: variables.location,
        p_is_virtual: variables.isVirtual,
        p_meeting_link: variables.meetingLink,
        p_update_future_only: variables.updateFutureOnly || false
      });
      
      if (error) {
        logQueryEvent('useUpdateEventSeries', 'Erro ao atualizar série', { error }, 'error');
        throw error;
      }
      
      return { events_updated: data };
    },
    onSuccess: (data, variables) => {
      logQueryEvent('useUpdateEventSeries', 'Série atualizada', data);
      
      // Invalidar caches
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: QueryKeys.calendar.upcomingEvents({}) }); // Invalida todas as queries de upcomingEvents
      queryClient.invalidateQueries({ queryKey: QueryKeys.calendar.all() });
      queryClient.invalidateQueries({ queryKey: ['calendar-events'] });
      queryClient.invalidateQueries({ queryKey: ['event-series', variables.eventId] });
      
      const scope = variables.updateFutureOnly ? 'futuros' : 'da série';
      successWithNotification('Eventos atualizados!', {
        description: `${data.events_updated} evento(s) ${scope} foram atualizados.`,
      });
    },
    onError: (error) => {
      logQueryEvent('useUpdateEventSeries', 'Erro na atualização', { error }, 'error');
      errorWithNotification('Erro ao atualizar eventos', {
        description: error instanceof Error ? error.message : 'Não foi possível atualizar os eventos.',
      });
    },
  });
}

/**
 * Hook para excluir série de eventos
 */
export function useDeleteEventSeries() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (variables: {
      eventId: string;
      deleteFutureOnly?: boolean;
    }) => {
      logQueryEvent('useDeleteEventSeries', 'Excluindo série', variables);
      
      const { data, error } = await supabase.rpc('delete_event_series', {
        p_event_id: variables.eventId,
        p_delete_future_only: variables.deleteFutureOnly || false
      });
      
      if (error) {
        logQueryEvent('useDeleteEventSeries', 'Erro ao excluir série', { error }, 'error');
        throw error;
      }
      
      return { events_deleted: data };
    },
    onSuccess: (data, variables) => {
      logQueryEvent('useDeleteEventSeries', 'Série excluída', data);
      
      // Invalidar caches
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: QueryKeys.calendar.upcomingEvents({}) }); // Invalida todas as queries de upcomingEvents
      queryClient.invalidateQueries({ queryKey: QueryKeys.calendar.all() });
      queryClient.invalidateQueries({ queryKey: ['calendar-events'] });
      queryClient.invalidateQueries({ queryKey: ['event-series', variables.eventId] });
      
      const scope = variables.deleteFutureOnly ? 'futuros' : 'da série';
      successWithNotification('Eventos excluídos!', {
        description: `${data.events_deleted} evento(s) ${scope} foram excluídos.`,
      });
    },
    onError: (error) => {
      logQueryEvent('useDeleteEventSeries', 'Erro na exclusão', { error }, 'error');
      errorWithNotification('Erro ao excluir eventos', {
        description: error instanceof Error ? error.message : 'Não foi possível excluir os eventos.',
      });
    },
  });
}