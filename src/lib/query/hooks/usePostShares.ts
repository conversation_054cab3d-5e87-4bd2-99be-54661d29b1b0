/**
 * Hook para gerenciar compartilhamentos de posts.
 * <AUTHOR> Internet 2025
 */
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

interface SharePostParams {
  postId: string;
  userId: string;
  shareType?: 'copy_link' | 'native_share' | 'internal';
}

export function useSharePost() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ postId, userId, shareType = 'copy_link' }: SharePostParams) => {
      logQueryEvent('usePostShares', 'Registrando compartilhamento de post', { postId, userId, shareType }, 'info');

      // 1. Obter usuário atual
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuário não autenticado');

      // 2. Obter company_id do perfil (OBRIGATÓRIO para RLS)
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('company_id')
        .eq('id', user.id)
        .single();

      if (profileError || !profile?.company_id) {
        throw new Error('Erro ao obter informações da empresa do usuário');
      }

      // 3. Usar INSERT normal (permite múltiplos compartilhamentos)
      const { data, error } = await supabase
        .from('post_shares')
        .insert({
          post_id: postId,
          content_type: 'post',
          content_id: postId,
          user_id: user.id,
          company_id: profile.company_id,
          share_type: shareType
        })
        .select()
        .single();

      if (error) {
        logQueryEvent('usePostShares', 'Erro ao registrar compartilhamento', error, 'error');
        throw error;
      }

      logQueryEvent('usePostShares', 'Compartilhamento registrado com sucesso', { shareId: data.id }, 'info');
      return data;
    },
    onSuccess: () => {
      // Invalidar cache de métricas de compartilhamentos
      queryClient.invalidateQueries({ queryKey: ['analytics', 'shares-metrics'] });
    },
    onError: (error: any) => {
      // Tratamento específico para erro de duplicata
      if (error.code === '23505') {
        console.warn('Tentativa de compartilhamento duplicado detectada - ignorando erro');
        return; // Não mostrar toast de erro para duplicatas
      }
      
      logQueryEvent('usePostShares', 'Falha na mutation de compartilhamento', error, 'error');
    }
  });
} 