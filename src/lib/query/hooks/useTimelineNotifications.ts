/**
 * Hook integrado com o sistema de notificações para a Timeline
 * <AUTHOR> Internet 2025
 */

import { useQuery, useMutation, useQueryClient, type UseQueryOptions } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuthStore } from '@/stores/authStore';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { successWithNotification } from '@/lib/notifications/toastWithNotification';
import type { TimelineNotification, TimelineItemType } from '@/types/timeline';
import { generateMockTimelineData } from '@/lib/timeline/mockData';
import { QueryKeys } from '@/lib/query/queryKeys';
import { useEffect, useRef } from 'react';
import { useTimelineLimits } from '@/hooks/timeline/useTimelineLimits';

// Filtros da Timeline
export interface TimelineFilters {
  types?: TimelineItemType[];
  priorities?: ('low' | 'medium' | 'high' | 'urgent')[];
  search?: string;
  read?: boolean;
}

/**
 * Hook principal para buscar notificações da timeline
 */
export function useTimelineNotifications(
  filters?: TimelineFilters, 
  options?: Partial<UseQueryOptions<TimelineNotification[], Error>>
) {
  const user = useAuthStore(state => state.user);
  const { limits } = useTimelineLimits();

  return useQuery({
    queryKey: QueryKeys.timeline.notifications(filters),
    queryFn: async (): Promise<TimelineNotification[]> => {
      logQueryEvent('useTimelineNotifications', 'Buscando notificações da timeline', { 
        filters,
        userIdFromHook: user?.id,
        timestamp: new Date().toISOString()
      });

      // Obter usuário atual no momento da execução da query
      const currentUser = useAuthStore.getState().user;
      if (!currentUser) {
        logQueryEvent('useTimelineNotifications', 'Usuário não autenticado na query', {
          userFromHook: user?.id,
          userFromStore: currentUser?.id
        }, 'warn');
        return [];
      }

      logQueryEvent('useTimelineNotifications', 'Executando query para usuário', {
        userId: currentUser.id,
        filtersApplied: !!filters,
        timestamp: new Date().toISOString()
      });

      // Query das notificações reais
      let query = supabase
        .from('notifications')
        .select('*')
        .eq('user_id', currentUser.id);

      // Aplicar limitação temporal baseada no plano do usuário
      if (limits && limits.isHistoryLimited) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - limits.maxHistoryDays);
        
        query = query.gte('created_at', cutoffDate.toISOString());
        
        logQueryEvent('useTimelineNotifications', 'Aplicando limitação temporal baseada no plano', {
          currentPlan: limits.currentPlan,
          maxHistoryDays: limits.maxHistoryDays,
          cutoffDate: cutoffDate.toISOString(),
          isHistoryLimited: limits.isHistoryLimited
        });
      }

      // Aplicar filtros de tipo
      if (filters?.types && filters.types.length > 0) {
        // Expandir tipos para incluir variações (ex: 'post' inclui 'post_published')
        const expandedTypes = filters.types.flatMap(type => {
          switch (type) {
            case 'post':
              return ['post', 'post_published', 'post_edited'];
            case 'obligation':
              return ['obligation_assigned', 'obligation_overdue'];
            case 'event':
              return ['event_invitation', 'event_reminder'];
            case 'mission':
              return ['mission_progress', 'mission_completed'];
            case 'task':
              return ['task_assigned', 'task_completed'];
            default:
              return [type];
          }
        });
        
        query = query.in('type', expandedTypes);
      }

      // Aplicar filtro de leitura
      if (filters?.read !== undefined) {

        query = query.eq('read', filters.read);
      }

      // Ordenar por data de criação (mais recentes primeiro)
      query = query.order('created_at', { ascending: false });

      // Limitar para evitar sobrecarga (últimas 500 notificações para melhor filtragem)
      query = query.limit(500);

      const { data, error } = await query;

      if (error) {
        logQueryEvent('useTimelineNotifications', 'Erro ao buscar notificações', { error }, 'error');
        throw error;
      }

      let notifications = data as TimelineNotification[] || [];

      // Aplicar filtros client-side para busca e prioridades
      if (filters?.search) {
        const searchTerm = filters.search.toLowerCase();
        notifications = notifications.filter(notification => {
          // Buscar em título, conteúdo e metadata
          const title = (notification.title || '').toLowerCase();
          const content = (notification.content || '').toLowerCase();
          const metadata = notification.metadata || {};
          
          // Buscar em campos comuns de metadata
          const metadataText = [
            metadata.title,
            metadata.description, 
            metadata.message,
            metadata.person?.name,
            metadata.medal_name,
            metadata.level_title,
            metadata.fromPosition,
            metadata.toPosition,
            metadata.location
          ].filter(Boolean).join(' ').toLowerCase();
          
          return title.includes(searchTerm) || 
                 content.includes(searchTerm) || 
                 metadataText.includes(searchTerm);
        });
      }

      // Aplicar filtros de prioridade
      if (filters?.priorities && filters.priorities.length > 0) {
        notifications = notifications.filter(notification => {
          const priority = getNotificationPriority(notification.type);
          return filters.priorities!.includes(priority);
        });
      }

      // Adicionar prioridade padrão para cada notificação
      notifications = notifications.map(notification => ({
        ...notification,
        priority: getNotificationPriority(notification.type)
      }));

logQueryEvent('useTimelineNotifications', `Query completa: ${notifications.length} notificações filtradas`, {
        userId: currentUser.id,
        totalNotifications: notifications.length,
        hasFilters: !!filters,
        timestamp: new Date().toISOString()
      });
      return notifications;
    },
    enabled: !!user?.id, // Só executar quando há usuário autenticado
    staleTime: 15 * 60 * 1000, // 15 minutos - cache mais duradouro para sessões longas
    gcTime: 30 * 60 * 1000, // 30 minutos na memória - evitar limpeza prematura
    refetchOnWindowFocus: false, // Não recarregar ao focar janela
    refetchOnMount: false, // Não recarregar ao montar se há cache válido
    refetchInterval: 10 * 60 * 1000, // Refresh automático a cada 10 minutos como backup
    ...options, // Merge com options personalizadas
  });
}

/**
 * Hook para escutar atualizações em tempo real das notificações
 * Sistema otimizado para atualizações incrementais sem invalidar cache completo
 */
export function useTimelineNotificationsRealtime() {
  const queryClient = useQueryClient();
  const user = useAuthStore(state => state.user);
  const subscriptionRef = useRef<any>(null);

  // Função helper para atualizar dados incrementalmente
  const updateTimelineData = (notification: TimelineNotification, action: 'INSERT' | 'UPDATE' | 'DELETE') => {
    // Encontrar todas as queries de timeline em cache
    queryClient.getQueryCache().findAll({ queryKey: QueryKeys.timeline.notifications() }).forEach((query) => {
      const queryKey = query.queryKey;
      
      queryClient.setQueryData<TimelineNotification[]>(queryKey, (oldData) => {
        if (!oldData) return oldData;

        logQueryEvent('useTimelineNotificationsRealtime', `Atualizando cache incrementalmente: ${action}`, {
          notificationId: notification.id,
          type: notification.type,
          oldDataLength: oldData.length
        });

        switch (action) {
          case 'INSERT':
            // Adicionar nova notificação no início (mais recente)
            // Verificar se já existe para evitar duplicatas (por ID ou por reference_id + type)
            const existsById = oldData.some(item => item.id === notification.id);
            const existsByReference = notification.reference_id && oldData.some(item => 
              item.reference_id === notification.reference_id && 
              item.type === notification.type &&
              Math.abs(new Date(item.created_at).getTime() - new Date(notification.created_at).getTime()) < 5000 // 5 segundos de diferença
            );
            
            if (existsById || existsByReference) {
              logQueryEvent('useTimelineNotificationsRealtime', 'Notificação duplicada detectada, ignorando INSERT', {
                notificationId: notification.id,
                referenceId: notification.reference_id,
                type: notification.type,
                existsById,
                existsByReference
              });
              return oldData;
            }
            
            // Adicionar prioridade à nova notificação
            const newNotification = {
              ...notification,
              priority: getNotificationPriority(notification.type)
            };
            
            logQueryEvent('useTimelineNotificationsRealtime', 'Nova notificação adicionada ao cache', {
              notificationId: notification.id,
              newDataLength: oldData.length + 1
            });
            
            return [newNotification, ...oldData];

          case 'UPDATE':
            // Atualizar notificação específica (ex: marcar como lida)
            const updatedData = oldData.map(item => 
              item.id === notification.id 
                ? { ...notification, priority: getNotificationPriority(notification.type) }
                : item
            );
            
            logQueryEvent('useTimelineNotificationsRealtime', 'Notificação atualizada no cache', {
              notificationId: notification.id,
              changes: 'updated in place'
            });
            
            return updatedData;

          case 'DELETE':
            // Remover notificação específica
            const filteredData = oldData.filter(item => item.id !== notification.id);
            
            logQueryEvent('useTimelineNotificationsRealtime', 'Notificação removida do cache', {
              notificationId: notification.id,
              newDataLength: filteredData.length
            });
            
            return filteredData;

          default:
            return oldData;
        }
      });
    });

    // Atualizar contador de não lidas apenas quando necessário
    if (action === 'INSERT' && !notification.read) {
      queryClient.setQueryData<number>(QueryKeys.timeline.unread(), (oldCount) => {
        const newCount = (oldCount || 0) + 1;
        logQueryEvent('useTimelineNotificationsRealtime', 'Contador de não lidas incrementado', {
          oldCount,
          newCount
        });
        return newCount;
      });
    } else if (action === 'UPDATE' && notification.read) {
      queryClient.setQueryData<number>(QueryKeys.timeline.unread(), (oldCount) => {
        const newCount = Math.max(0, (oldCount || 1) - 1);
        logQueryEvent('useTimelineNotificationsRealtime', 'Contador de não lidas decrementado', {
          oldCount,
          newCount
        });
        return newCount;
      });
    }
  };

  useEffect(() => {
    if (!user?.id) {
      logQueryEvent('useTimelineNotificationsRealtime', 'Usuário não disponível para subscription', {}, 'warn');
      return;
    }

    // Evitar múltiplas subscriptions
    if (subscriptionRef.current) {
      logQueryEvent('useTimelineNotificationsRealtime', 'Subscription já existe, ignorando');
      return;
    }

    logQueryEvent('useTimelineNotificationsRealtime', 'Iniciando subscription incremental para notificações', { userId: user.id });

    // Configurar subscription do Supabase Realtime
    const subscription = supabase
      .channel(`user_notifications_${user.id}`)
      .on(
        'postgres_changes',
        {
          event: '*', // INSERT, UPDATE, DELETE
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${user.id}` // Apenas notificações do usuário atual
        },
        (payload) => {
          logQueryEvent('useTimelineNotificationsRealtime', 'Mudança detectada na tabela notifications', {
            event: payload.eventType,
            notificationId: payload.new?.id || payload.old?.id,
            type: payload.new?.type || payload.old?.type
          });

          // Processar mudança incrementalmente baseado no tipo de evento
          if (payload.eventType === 'INSERT' && payload.new) {
            updateTimelineData(payload.new as TimelineNotification, 'INSERT');
            
            // Para posts, também invalidar feed de forma seletiva
            if (payload.new.type === 'post_published') {
              logQueryEvent('useTimelineNotificationsRealtime', 'Nova notificação de post detectada, refresh seletivo do feed');
              // Apenas invalidar query de posts, não toda a timeline
              queryClient.invalidateQueries({ queryKey: QueryKeys.posts.feed() });
            }
          } else if (payload.eventType === 'UPDATE' && payload.new) {
            updateTimelineData(payload.new as TimelineNotification, 'UPDATE');
          } else if (payload.eventType === 'DELETE' && payload.old) {
            updateTimelineData(payload.old as TimelineNotification, 'DELETE');
          }

          // Atualizar queries de notificações gerais apenas se necessário
          // Fazer invalidação seletiva ao invés de completa
          queryClient.invalidateQueries({ 
            queryKey: QueryKeys.notifications.all(),
            exact: false,
            stale: true // Apenas queries que já estão stale
          });
        }
      )
      .subscribe((status) => {
        logQueryEvent('useTimelineNotificationsRealtime', `Status da subscription: ${status}`);
        
        if (status === 'SUBSCRIBED') {
          logQueryEvent('useTimelineNotificationsRealtime', 'Subscription incremental ativada com sucesso');
        } else if (status === 'CHANNEL_ERROR') {
          logQueryEvent('useTimelineNotificationsRealtime', 'Erro no canal de subscription', {}, 'error');
        }
      });

    subscriptionRef.current = subscription;

    // Cleanup
    return () => {
      if (subscriptionRef.current) {
        logQueryEvent('useTimelineNotificationsRealtime', 'Removendo subscription incremental');
        supabase.removeChannel(subscriptionRef.current);
        subscriptionRef.current = null;
      }
    };
  }, [user?.id, queryClient]);

  // Retornar função para forçar refresh manual se necessário
  return {
    forceRefresh: () => {
      logQueryEvent('useTimelineNotificationsRealtime', 'Refresh manual solicitado - invalidação completa');
      queryClient.invalidateQueries({ queryKey: QueryKeys.timeline.notifications() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.timeline.unread() });
    }
  };
}

/**
 * Hook para marcar notificação como lida (integrado com sistema existente)
 */
export function useMarkTimelineItemAsRead() {
  const queryClient = useQueryClient();
  const user = useAuthStore(state => state.user);

  return useMutation({
    mutationFn: async (notificationId: string) => {
      logQueryEvent('useMarkTimelineItemAsRead', 'Marcando item da timeline como lido', { notificationId });

      // Query real para marcar como lida
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId)
        .eq('user_id', user?.id); // Segurança: só pode marcar suas próprias notificações

      if (error) {
        logQueryEvent('useMarkTimelineItemAsRead', 'Erro ao marcar como lida', { error }, 'error');
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidar todas as queries relacionadas à timeline e notificações usando QueryKeys centralizadas
      queryClient.invalidateQueries({ queryKey: QueryKeys.timeline.notifications() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.timeline.unread() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.notifications.all() });

      logQueryEvent('useMarkTimelineItemAsRead', 'Item marcado como lido com sucesso');
    },
    onError: (error) => {
      logQueryEvent('useMarkTimelineItemAsRead', 'Erro na mutação', { error }, 'error');
    }
  });
}

/**
 * Hook para marcar todos os itens como lidos
 */
export function useMarkAllTimelineItemsAsRead() {
  const queryClient = useQueryClient();
  const user = useAuthStore(state => state.user);

  return useMutation({
    mutationFn: async () => {
      logQueryEvent('useMarkAllTimelineItemsAsRead', 'Marcando todos os itens como lidos');

      // Query real para marcar todos como lidos
      if (!user) {
        throw new Error('Usuário não autenticado');
      }

      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', user.id)
        .eq('read', false);

      if (error) {
        logQueryEvent('useMarkAllTimelineItemsAsRead', 'Erro ao marcar todos como lidos', { error }, 'error');
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidar todas as queries relacionadas usando QueryKeys centralizadas
      queryClient.invalidateQueries({ queryKey: QueryKeys.timeline.notifications() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.timeline.unread() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.notifications.all() });

      successWithNotification('Todos os itens marcados como lidos!', {
        description: 'Sua timeline foi atualizada.',
      });

      logQueryEvent('useMarkAllTimelineItemsAsRead', 'Todos os itens marcados como lidos');
    }
  });
}

/**
 * Hook para contar itens não lidos da timeline
 */
export function useTimelineUnreadCount() {
  const user = useAuthStore(state => state.user);

  return useQuery({
    queryKey: QueryKeys.timeline.unread(),
    queryFn: async (): Promise<number> => {
      // Query real para contar não lidos
      if (!user) return 0;

      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .eq('read', false);

      if (error) {
        logQueryEvent('useTimelineUnreadCount', 'Erro ao contar não lidos', { error }, 'error');
        return 0;
      }

      logQueryEvent('useTimelineUnreadCount', `Contador atualizado: ${count || 0} não lidas`);
      return count || 0;
    },
    staleTime: 5 * 1000, // 5 segundos - mais responsivo para mudanças
    gcTime: 30 * 1000, // 30 segundos na memória
    refetchOnWindowFocus: true, // Recarregar ao focar janela
    enabled: !!user?.id, // Só executar quando há usuário
  });
}

/**
 * Helper para obter data segura de notificação (com fallback para estrutura legacy)
 */
export function getSafeNotificationDate(item: TimelineNotification): Date | null {
  const dateString = item.created_at || (item as any).timestamp;
  if (!dateString) return null;
  const date = new Date(dateString);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * Helper para determinar a prioridade baseada no tipo de notificação
 */
export function getNotificationPriority(type: TimelineItemType): 'low' | 'medium' | 'high' | 'urgent' {
  switch (type) {
    case 'obligation_overdue':
    case 'obligation_urgent_reminder':
    case 'ai_insight':
    case 'system_error':
      return 'urgent';
    case 'birthday':
    case 'promotion':
    case 'mission_completed':
    case 'event_invitation':
    case 'medal_earned':
    case 'level_up':
    case 'obligation_reminder':
      return 'high';
    case 'post':
    case 'post_published':
    case 'post_edited':
    case 'obligation_assigned':
    case 'task_assigned':
    case 'event_reminder':
    case 'notification':
      return 'medium';
    case 'mention':
    case 'comment':
    case 'like':
    case 'mission_progress':
    case 'task_completed':
    case 'system':
      return 'low';
    default:
      return 'low'; // Prioridade padrão como baixa
  }
}

/**
 * Helper para obter cor da prioridade
 */
export function getPriorityColor(priority: 'low' | 'medium' | 'high' | 'urgent'): string {
  switch (priority) {
    case 'urgent':
      return 'border-l-red-500 bg-red-50/30';
    case 'high':
      return 'border-l-orange-500 bg-orange-50/30';
    case 'medium':
      return 'border-l-yellow-500 bg-yellow-50/30';
    case 'low':
      return 'border-l-green-500 bg-green-50/30';
    default:
      return 'border-l-green-500 bg-green-50/30';
  }
}