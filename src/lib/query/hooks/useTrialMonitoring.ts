/**
 * Hooks para monitoramento de trials e automação
 * Sistema completo de tracking de periods de teste
 * <AUTHOR> Internet 2025
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { QueryKeys } from '../queryKeys';
import { useAuthStore } from '@/stores/authStore';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { successWithNotification, errorWithNotification } from '@/lib/notifications/toastWithNotification';

// Types para trial monitoring
export interface TrialStatistics {
  active_trials: number;
  expiring_24h: number;
  expiring_48h: number;
  expired_pending: number;
  total_trial_companies: number;
  average_trial_duration_days: number;
  conversion_rate_last_30_days: number;
}

export interface ExpiringTrial {
  company_id: string;
  company_name: string;
  owner_name: string;
  owner_email: string;
  subscription_id: string;
  current_plan_name: string;
  courtesy_until: string;
  days_remaining: number;
  trial_duration_days: number;
}

export interface TrialDashboard {
  statistics: TrialStatistics;
  recent_alerts: Array<{
    company_name: string;
    notification_type: string;
    status: string;
    created_at: string;
    recipient_email: string;
  }>;
  recent_activity: Array<{
    company_name: string;
    activation_type: string;
    created_at: string;
    previous_plan: string;
  }>;
  last_updated: string;
  system_status: {
    cron_enabled: boolean;
    alerts_enabled: boolean;
    auto_expiration_enabled: boolean;
  };
}

export interface TrialAlert {
  type: 'expiring' | 'expired' | 'reminder';
  company_id: string;
  company_name: string;
  owner_name: string;
  owner_email: string;
  current_plan_name: string;
  courtesy_until: string;
  days_remaining?: number;
  metadata?: any;
}

/**
 * Hook para buscar estatísticas de trials
 */
export function useTrialStatistics() {
  const company_id = useAuthStore((state) => state.company_id);

  return useQuery({
    queryKey: QueryKeys.trials.statistics(),
    queryFn: async (): Promise<TrialStatistics> => {
      logQueryEvent('useTrialStatistics', 'Buscando estatísticas de trials');

      const { data, error } = await supabase.rpc('get_trial_statistics_v2');

      if (error) {
        logQueryEvent('useTrialStatistics', 'Erro ao buscar estatísticas', { error }, 'error');
        throw error;
      }

      const stats = data?.[0];
      if (!stats) {
        throw new Error('Nenhuma estatística encontrada');
      }

      logQueryEvent('useTrialStatistics', 'Estatísticas encontradas', { stats });
      return stats as TrialStatistics;
    },
    enabled: !!company_id,
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchInterval: 5 * 60 * 1000, // Atualizar a cada 5 minutos
  });
}

/**
 * Hook para buscar trials expirando
 */
export function useExpiringTrials(daysAhead: number = 2) {
  const company_id = useAuthStore((state) => state.company_id);

  return useQuery({
    queryKey: QueryKeys.trials.expiring(daysAhead),
    queryFn: async (): Promise<ExpiringTrial[]> => {
      logQueryEvent('useExpiringTrials', 'Buscando trials expirando', { daysAhead });

      const { data, error } = await supabase.rpc('get_expiring_trials_v2', {
        p_days_ahead: daysAhead
      });

      if (error) {
        logQueryEvent('useExpiringTrials', 'Erro ao buscar trials expirando', { error }, 'error');
        throw error;
      }

      logQueryEvent('useExpiringTrials', 'Trials expirando encontrados', { count: data?.length });
      return data as ExpiringTrial[];
    },
    enabled: !!company_id,
    staleTime: 2 * 60 * 1000, // 2 minutos
    refetchInterval: 2 * 60 * 1000, // Atualizar a cada 2 minutos
  });
}

/**
 * Hook para dashboard completo de trials
 */
export function useTrialDashboard() {
  const company_id = useAuthStore((state) => state.company_id);

  return useQuery({
    queryKey: QueryKeys.trials.dashboard(),
    queryFn: async (): Promise<TrialDashboard> => {
      logQueryEvent('useTrialDashboard', 'Buscando dashboard de trials');

      const { data, error } = await supabase.rpc('get_trial_dashboard_v2');

      if (error) {
        logQueryEvent('useTrialDashboard', 'Erro ao buscar dashboard', { error }, 'error');
        throw error;
      }

      const dashboard = data?.[0]?.dashboard_data;
      if (!dashboard) {
        throw new Error('Dashboard não encontrado');
      }

      logQueryEvent('useTrialDashboard', 'Dashboard carregado', { 
        activeTrials: dashboard.statistics?.active_trials,
        recentAlerts: dashboard.recent_alerts?.length 
      });
      
      return dashboard as TrialDashboard;
    },
    enabled: !!company_id,
    staleTime: 3 * 60 * 1000, // 3 minutos
    refetchInterval: 3 * 60 * 1000, // Atualizar a cada 3 minutos
  });
}

/**
 * Hook para processar trials expirados manualmente
 */
export function useProcessExpiredTrials() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      logQueryEvent('useProcessExpiredTrials', 'Processando trials expirados manualmente');

      const { data, error } = await supabase.rpc('process_expired_trials_v1');

      if (error) {
        logQueryEvent('useProcessExpiredTrials', 'Erro ao processar trials', { error }, 'error');
        throw error;
      }

      const result = data?.[0];
      if (!result) {
        throw new Error('Nenhum resultado retornado');
      }

      logQueryEvent('useProcessExpiredTrials', 'Trials processados', { result });
      return result;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.trials.all() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.trials.dashboard() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.trials.statistics() });

      successWithNotification('Trials processados!', {
        description: `${data.processed_count} trials foram processados com sucesso.`,
      });
    },
    onError: (error: Error) => {
      errorWithNotification('Erro ao processar trials', {
        description: 'Não foi possível processar os trials expirados.',
      });
      logQueryEvent('useProcessExpiredTrials', 'Erro na mutação', { error }, 'error');
    }
  });
}

/**
 * Hook para enviar alertas de trial manualmente
 */
export function useSendTrialAlerts() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (alerts: TrialAlert[]) => {
      logQueryEvent('useSendTrialAlerts', 'Enviando alertas de trial', { count: alerts.length });

      const { data, error } = await supabase.functions.invoke('send-trial-alerts', {
        body: { alerts }
      });

      if (error) {
        logQueryEvent('useSendTrialAlerts', 'Erro ao enviar alertas', { error }, 'error');
        throw error;
      }

      logQueryEvent('useSendTrialAlerts', 'Alertas enviados', { result: data });
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.trials.dashboard() });

      successWithNotification('Alertas enviados!', {
        description: `${data.sent} alertas foram enviados com sucesso.`,
      });
    },
    onError: (error: Error) => {
      errorWithNotification('Erro ao enviar alertas', {
        description: 'Não foi possível enviar os alertas de trial.',
      });
      logQueryEvent('useSendTrialAlerts', 'Erro na mutação', { error }, 'error');
    }
  });
}

/**
 * Hook para processar alertas de trial automaticamente
 */
export function useProcessTrialAlerts() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      logQueryEvent('useProcessTrialAlerts', 'Processando alertas de trial');

      const { data, error } = await supabase.rpc('process_trial_alerts_v1');

      if (error) {
        logQueryEvent('useProcessTrialAlerts', 'Erro ao processar alertas', { error }, 'error');
        throw error;
      }

      const result = data?.[0];
      logQueryEvent('useProcessTrialAlerts', 'Alertas processados', { result });
      return result;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.trials.dashboard() });

      successWithNotification('Alertas processados!', {
        description: `${data.alerts_sent} alertas foram processados.`,
      });
    },
    onError: (error: Error) => {
      errorWithNotification('Erro ao processar alertas', {
        description: 'Não foi possível processar os alertas automaticamente.',
      });
      logQueryEvent('useProcessTrialAlerts', 'Erro na mutação', { error }, 'error');
    }
  });
}

/**
 * Hook para enviar lembretes de trial
 */
export function useSendTrialReminders() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      logQueryEvent('useSendTrialReminders', 'Enviando lembretes de trial');

      const { data, error } = await supabase.rpc('send_trial_reminders_v1');

      if (error) {
        logQueryEvent('useSendTrialReminders', 'Erro ao enviar lembretes', { error }, 'error');
        throw error;
      }

      const result = data?.[0];
      logQueryEvent('useSendTrialReminders', 'Lembretes enviados', { result });
      return result;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.trials.dashboard() });

      successWithNotification('Lembretes enviados!', {
        description: `${data.reminders_sent} lembretes foram enviados.`,
      });
    },
    onError: (error: Error) => {
      errorWithNotification('Erro ao enviar lembretes', {
        description: 'Não foi possível enviar os lembretes de trial.',
      });
      logQueryEvent('useSendTrialReminders', 'Erro na mutação', { error }, 'error');
    }
  });
}