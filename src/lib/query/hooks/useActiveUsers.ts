/**
 * ⚠️ DEPRECATED: Hook para buscar usuários ativos baseados em sessões recentes.
 * 
 * 🚨 ESTE HOOK FOI MIGRADO PARA O SISTEMA CENTRALIZADO:
 * 
 * ❌ ANTIGO: import { useActiveUsers } from "@/lib/query/hooks/useActiveUsers";
 * ✅ NOVO:   import { useActiveUsers } from "@/lib/query/hooks/centralized/useActiveUsers";
 * 
 * O hook centralizado oferece:
 * - EventBus integration para invalidações inteligentes
 * - Domain Strategies para cache otimizado
 * - IndexedDB support para armazenamento offline
 * - Melhor performance e confiabilidade
 * 
 * MIGRE SEUS COMPONENTES PARA A VERSÃO CENTRALIZADA!
 * 
 * @deprecated Use /centralized/useActiveUsers instead
 * <AUTHOR> Internet 2025
 */
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { QueryKeys } from '../queryKeys';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

export interface ActiveUser {
  id: string;
  full_name: string;
  avatar_url?: string;
  status: 'online' | 'away' | 'offline';
  last_activity_minutes: number;
}

interface UseActiveUsersOptions {
  activeThresholdMinutes?: number;
  enabled?: boolean;
  refetchInterval?: number;
}

interface ActiveUserRaw {
  id: string;
  full_name: string;
  avatar_url?: string;
  status: string;
  last_activity_minutes: number;
}

/**
 * ⚠️ DEPRECATED: Hook para buscar usuários ativos da empresa baseado em sessões recentes.
 * 
 * 🚨 USE A VERSÃO CENTRALIZADA: @/lib/query/hooks/centralized/useActiveUsers
 * 
 * @param options Opções de configuração
 * @param options.activeThresholdMinutes Limite em minutos para considerar usuário como "away" (padrão: 15)
 * @param options.enabled Se a query deve ser executada (padrão: true)
 * @param options.refetchInterval Intervalo de atualização automática em ms (padrão: 30000 - 30 segundos)
 * @deprecated Use /centralized/useActiveUsers instead
 */
export function useActiveUsers(options: UseActiveUsersOptions = {}) {

  const {
    activeThresholdMinutes = 15,
    enabled = true,
    refetchInterval = 30000 // 30 segundos
  } = options;

  return useQuery({
    queryKey: QueryKeys.users.active(activeThresholdMinutes),
    queryFn: async (): Promise<ActiveUser[]> => {
      // Log removido para reduzir verbosidade

      // Função criada por migração, tipagem manual necessária
      const { data, error } = await supabase
        .rpc('get_active_users', {
          active_threshold_minutes: activeThresholdMinutes
        });

      if (error) {
        logQueryEvent(
          'useActiveUsers',
          `Erro ao buscar usuários ativos: ${error.message}`,
          { error, activeThresholdMinutes },
          'error'
        );
        throw error;
      }

      // Garantir que sempre retornamos um array, mesmo se data for undefined/null
      const rawData = Array.isArray(data) ? data : [];
      
      const activeUsers: ActiveUser[] = rawData.map((user: ActiveUserRaw) => ({
        id: user.id,
        full_name: user.full_name || 'Usuário',
        avatar_url: user.avatar_url,
        status: user.status as 'online' | 'away' | 'offline',
        last_activity_minutes: user.last_activity_minutes || 999
      }));

      // Log removido para reduzir verbosidade

      return activeUsers;
    },
    enabled,
    staleTime: 20 * 1000, // 20 segundos
    refetchInterval,
    refetchIntervalInBackground: true,
    refetchOnWindowFocus: true,
  });
}

/**
 * ⚠️ DEPRECATED: Hook para buscar apenas usuários online (últimos 1 minuto).
 * 
 * 🚨 USE A VERSÃO CENTRALIZADA: @/lib/query/hooks/centralized/useActiveUsers
 * @deprecated Use /centralized/useActiveUsers instead
 */
export function useOnlineUsers() {
  const { data: activeUsers, ...rest } = useActiveUsers({
    activeThresholdMinutes: 1,
    refetchInterval: 15000 // 15 segundos - mais frequente para usuários online
  });

  const onlineUsers = Array.isArray(activeUsers) 
    ? activeUsers.filter(user => user.status === 'online') 
    : [];

  return {
    data: onlineUsers,
    ...rest
  };
}

/**
 * ⚠️ DEPRECATED: Hook para contar usuários ativos por status.
 * 
 * 🚨 USE A VERSÃO CENTRALIZADA: @/lib/query/hooks/centralized/useActiveUsers
 * @deprecated Use /centralized/useActiveUsers instead
 */
export function useActiveUsersCount() {
  const { data: activeUsers, ...rest } = useActiveUsers();

  const counts = {
    online: Array.isArray(activeUsers) ? activeUsers.filter(user => user.status === 'online').length : 0,
    away: Array.isArray(activeUsers) ? activeUsers.filter(user => user.status === 'away').length : 0,
    offline: Array.isArray(activeUsers) ? activeUsers.filter(user => user.status === 'offline').length : 0,
    total: Array.isArray(activeUsers) ? activeUsers.length : 0
  };

  return {
    data: counts,
    users: activeUsers,
    ...rest
  };
} 