/**
 * useAbsenceTypes - Hooks para Gerenciamento de Tipos de Ausência
 * 
 * Hooks React Query para operações CRUD de tipos de ausência:
 * - Listar tipos de ausência da empresa
 * - Criar novo tipo
 * - Atualizar tipo existente
 * - Deletar tipo
 * 
 * <AUTHOR> Internet 2025
 */
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { QueryKeys } from '@/lib/query/queryKeys';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

// =====================================================
// INTERFACES
// =====================================================

interface AbsenceType {
  id: string;
  company_id: string;
  name: string;
  description: string | null;
  color: string;
  icon: string;
  requires_approval: boolean;
  max_days_in_advance: number;
  min_duration_hours: number;
  max_duration_days: number;
  is_active: boolean;
  allows_self_registration: boolean;
  requires_justification: boolean;
  counts_as_work_day: boolean;
  created_at: string;
  updated_at: string;
  created_by: string | null;
}

interface CreateAbsenceTypeData {
  name: string;
  description?: string;
  color: string;
  icon: string;
  requires_approval: boolean;
  allows_self_registration: boolean;
  requires_justification: boolean;
  max_days_in_advance: number;
  max_duration_days: number;
  is_active: boolean;
}

interface UpdateAbsenceTypeData extends CreateAbsenceTypeData {
  id: string;
}

// =====================================================
// FUNÇÕES DE API
// =====================================================

const fetchAbsenceTypes = async (): Promise<AbsenceType[]> => {
  logQueryEvent('fetchAbsenceTypes', 'Buscando tipos de ausência');
  
  const { data, error } = await supabase
    .from('absence_types')
    .select('*')
    .order('name', { ascending: true });

  if (error) {
    logQueryEvent('fetchAbsenceTypes', 'Erro ao buscar tipos de ausência', { error });
    throw new Error(`Erro ao buscar tipos de ausência: ${error.message}`);
  }

  logQueryEvent('fetchAbsenceTypes', 'Tipos de ausência carregados', { count: data?.length });
  return data || [];
};

const createAbsenceType = async (absenceTypeData: CreateAbsenceTypeData): Promise<AbsenceType> => {
  logQueryEvent('createAbsenceType', 'Criando tipo de ausência', { name: absenceTypeData.name });

  // Obter usuário atual
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('Usuário não autenticado');
  }

  // Obter company_id do perfil do usuário
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('company_id')
    .eq('id', user.id)
    .single();

  if (profileError || !profile?.company_id) {
    logQueryEvent('createAbsenceType', 'Erro ao obter company_id', { error: profileError });
    throw new Error('Erro ao obter informações da empresa do usuário');
  }

  // Inserir com company_id
  const { data, error } = await supabase
    .from('absence_types')
    .insert({
      ...absenceTypeData,
      company_id: profile.company_id,
    })
    .select()
    .single();

  if (error) {
    logQueryEvent('createAbsenceType', 'Erro ao criar tipo de ausência', { error });
    throw new Error(`Erro ao criar tipo de ausência: ${error.message}`);
  }

  logQueryEvent('createAbsenceType', 'Tipo de ausência criado com sucesso', { id: data.id });
  return data;
};

const updateAbsenceType = async (absenceTypeData: UpdateAbsenceTypeData): Promise<AbsenceType> => {
  logQueryEvent('updateAbsenceType', 'Atualizando tipo de ausência', { id: absenceTypeData.id });

  const { id, ...updateData } = absenceTypeData;

  const { data, error } = await supabase
    .from('absence_types')
    .update(updateData)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    logQueryEvent('updateAbsenceType', 'Erro ao atualizar tipo de ausência', { error });
    throw new Error(`Erro ao atualizar tipo de ausência: ${error.message}`);
  }

  logQueryEvent('updateAbsenceType', 'Tipo de ausência atualizado com sucesso', { id: data.id });
  return data;
};

const deleteAbsenceType = async (id: string): Promise<void> => {
  logQueryEvent('deleteAbsenceType', 'Deletando tipo de ausência', { id });

  const { error } = await supabase
    .from('absence_types')
    .delete()
    .eq('id', id);

  if (error) {
    logQueryEvent('deleteAbsenceType', 'Erro ao deletar tipo de ausência', { error });
    throw new Error(`Erro ao deletar tipo de ausência: ${error.message}`);
  }

  logQueryEvent('deleteAbsenceType', 'Tipo de ausência deletado com sucesso', { id });
};

// =====================================================
// HOOKS
// =====================================================

/**
 * Hook para buscar tipos de ausência da empresa
 */
export const useAbsenceTypes = () => {
  return useQuery({
    queryKey: QueryKeys.absenceTypes.list(),
    queryFn: fetchAbsenceTypes,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

/**
 * Hook para criar novo tipo de ausência
 */
export const useCreateAbsenceType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createAbsenceType,
    onSuccess: (data) => {
      // Invalidar e atualizar cache
      queryClient.invalidateQueries({ queryKey: QueryKeys.absenceTypes.list() });
      
      // Adicionar o novo tipo ao cache existente (otimização)
      queryClient.setQueryData(
        QueryKeys.absenceTypes.list(),
        (oldData: AbsenceType[] | undefined) => {
          if (!oldData) return [data];
          return [...oldData, data].sort((a, b) => a.name.localeCompare(b.name));
        }
      );

      logQueryEvent('useCreateAbsenceType', 'Cache atualizado após criação', { id: data.id });
    },
    onError: (error) => {
      logQueryEvent('useCreateAbsenceType', 'Erro na mutation', { error: error.message });
    },
  });
};

/**
 * Hook para atualizar tipo de ausência existente
 */
export const useUpdateAbsenceType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateAbsenceType,
    onSuccess: (data) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: QueryKeys.absenceTypes.list() });
      
      // Atualizar o item específico no cache
      queryClient.setQueryData(
        QueryKeys.absenceTypes.list(),
        (oldData: AbsenceType[] | undefined) => {
          if (!oldData) return [data];
          return oldData.map(item => 
            item.id === data.id ? data : item
          ).sort((a, b) => a.name.localeCompare(b.name));
        }
      );

      logQueryEvent('useUpdateAbsenceType', 'Cache atualizado após edição', { id: data.id });
    },
    onError: (error) => {
      logQueryEvent('useUpdateAbsenceType', 'Erro na mutation', { error: error.message });
    },
  });
};

/**
 * Hook para deletar tipo de ausência
 */
export const useDeleteAbsenceType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteAbsenceType,
    onSuccess: (_, deletedId) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: QueryKeys.absenceTypes.list() });
      
      // Remover do cache
      queryClient.setQueryData(
        QueryKeys.absenceTypes.list(),
        (oldData: AbsenceType[] | undefined) => {
          if (!oldData) return [];
          return oldData.filter(item => item.id !== deletedId);
        }
      );

      logQueryEvent('useDeleteAbsenceType', 'Cache atualizado após deleção', { id: deletedId });
    },
    onError: (error) => {
      logQueryEvent('useDeleteAbsenceType', 'Erro na mutation', { error: error.message });
    },
  });
};

/**
 * Hook para buscar um tipo específico de ausência
 */
export const useAbsenceType = (id: string | undefined) => {
  return useQuery({
    queryKey: QueryKeys.absenceTypes.detail(id),
    queryFn: async () => {
      if (!id) throw new Error('ID não fornecido');
      
      logQueryEvent('fetchAbsenceType', 'Buscando tipo de ausência específico', { id });
      
      const { data, error } = await supabase
        .from('absence_types')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        logQueryEvent('fetchAbsenceType', 'Erro ao buscar tipo específico', { error });
        throw new Error(`Erro ao buscar tipo de ausência: ${error.message}`);
      }

      logQueryEvent('fetchAbsenceType', 'Tipo específico carregado', { id });
      return data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};