/**
 * Hooks centralizados para consultas relacionadas a posts
 * <AUTHOR> Internet 2025
 */
import {
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { QueryKeys } from "../queryKeys";
import { useEffect, useRef } from "react";
import { v4 as uuidv4 } from "uuid";
import { logQueryEvent } from '@/lib/logs/showQueryLogs'; // Importar logQueryEvent
import { useCurrentUser } from '@/lib/query/hooks/useUsers';
// import { usePostsFeedCentralized } from './centralized/usePostsFeed'; // Disponível para migração futura

// Constantes
const POSTS_PER_PAGE = 10;

// Interface para posts processados
interface ProcessedPost {
  id: string;
  content: string;
  created_at: string;
  likes: number;
  company_id: string;
  status: string;
  scheduled_at?: string;
  has_poll?: boolean;
  type?: string; // Tipo do post (standard, medal_celebration, etc.)
  // Campos de edição
  is_edited?: boolean;
  edit_count?: number;
  last_edited_at?: string;
  last_edited_by?: string;
  metadata?: {
    audio_url?: string;
    audio_duration?: number;
    medal_id?: string;
    medal_name?: string;
    medal_type?: string;
    [key: string]: any;
  };
  author: {
    id: string;
    full_name: string;
    avatar_url?: string;
  };
  liked_by: Array<{
    profiles: {
      id: string;
      full_name: string;
      avatar_url?: string;
    };
  }>;
  // Propriedade audience requerida pelo PostCard
  audience?: {
    type: "all" | "department" | "team" | "user";
    targets?: string[];
    targetCount?: number;
  };
  post_audience?: Array<{
    target_type: string;
    target_id: string;
  }>;
  images?: Array<{
    id: string;
    image_url: string;
    storage_path: string;
    size: number;
    created_at: string;
  }>;
  [key: string]: unknown;
}

// Interface para resposta de página
interface PostsPage {
  posts: ProcessedPost[];
  nextPage: number | undefined;
  totalCount: number;
}

// Interface para filtros de posts
export interface PostsFilters {
  search?: string;
  sortBy?: "newest" | "oldest" | "most_liked" | "most_commented";
  status?: "all" | "published" | "scheduled";
  audience?: "all" | "team" | "department" | "personal";
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  authorId?: string;
  [key: string]: unknown;
}

interface UsePostsFeedOptions {
  enabled?: boolean;
  staleTime?: number;
}

/**
 * Hook para buscar feed de posts com paginação infinita e filtros
 * 
 * 💡 NOVA VERSÃO DISPONÍVEL: ./centralized/usePostsFeed
 * - PostsDomainStrategy (cache híbrido) 
 * - EventBus integration
 * - Performance otimizada
 */
export function usePostsFeed(filters?: PostsFilters, options?: UsePostsFeedOptions) {
  // 🚀 MIGRAÇÃO FUTURA: PostsDomainStrategy disponível em ./centralized/usePostsFeed
  // const ENABLE_CENTRALIZED = false; // Para ativar quando sistema estiver 100% estável
  // if (ENABLE_CENTRALIZED) return usePostsFeedCentralized(filters, options);

  // 📜 IMPLEMENTAÇÃO ATUAL (estável e funcionando)
  const queryClient = useQueryClient();
  const realtimeSubscribed = useRef(false);
  
  // Para o filtro de audiência, precisamos do profile do usuário atual
  const { data: currentUser } = useCurrentUser();

  // Consulta de posts com suporte a paginação infinita e filtros
  const query = useInfiniteQuery<PostsPage>({
    queryKey: QueryKeys.posts.feed(filters),
    queryFn: async ({ pageParam = 0 }) => {
      const from = (pageParam as number) * POSTS_PER_PAGE;
      const to = from + POSTS_PER_PAGE - 1;

      let queryBuilder = supabase
        .from("posts")
        .select(
          `
          id,
          content,
          created_at,
          likes,
          company_id,
          status,
          scheduled_at,
          has_poll,
          type,
          metadata,
          is_edited,
          edit_count,
          last_edited_at,
          last_edited_by,
          author:profiles!posts_author_id_fkey (
            id,
            full_name,
            avatar_url
          ),
          liked_by:post_likes (
            profiles:profiles!post_likes_user_id_fkey (
              id,
              full_name,
              avatar_url
            )
          ),
          post_audience (
            target_type,
            target_id
          )
        `,
          { count: "exact" },
        );

      // Aplicar filtros de status
      if (filters?.status === "published") {
        queryBuilder = queryBuilder.eq("status", "published");
      } else if (filters?.status === "scheduled") {
        queryBuilder = queryBuilder.eq("status", "scheduled");
      } else {
        // Status "all" ou sem filtro - mostrar publicados e agendados que já devem aparecer
        queryBuilder = queryBuilder.or(
          `status.eq.published,and(status.eq.scheduled,scheduled_at.lte.${
            new Date().toISOString()
          })`
        );
      }

      // Aplicar filtro de busca por texto
      if (filters?.search) {
        queryBuilder = queryBuilder.ilike("content", `%${filters.search}%`);
      }

      // Aplicar filtro de autor
      if (filters?.authorId) {
        queryBuilder = queryBuilder.eq("author_id", filters.authorId);
      }

      // Aplicar filtro de data
      if (filters?.dateRange?.from) {
        queryBuilder = queryBuilder.gte("created_at", filters.dateRange.from.toISOString());
      }
      if (filters?.dateRange?.to) {
        const endDate = new Date(filters.dateRange.to);
        endDate.setHours(23, 59, 59, 999); // Incluir todo o dia
        queryBuilder = queryBuilder.lte("created_at", endDate.toISOString());
      }

      // Aplicar filtro de audiência (filtro no lado cliente por simplicidade inicial)
      // TODO: Implementar filtro no servidor para melhor performance
      
      // Aplicar ordenação
      if (filters?.sortBy === "oldest") {
        queryBuilder = queryBuilder.order("created_at", { ascending: true });
      } else {
        // newest (padrão) e outros tipos de ordenação
        queryBuilder = queryBuilder.order("created_at", { ascending: false });
      }

      const { data, error, count } = await queryBuilder.range(from, to);

      if (error) {
        throw error;
      }

      // Garantir que data é um array
      const postsData = Array.isArray(data) ? data : [];

      // Processar dados retornados
      let processedPosts = postsData.map((post) => {
        return {
          id: post.id,
          content: post.content,
          created_at: post.created_at,
          likes: post.liked_by?.length || 0,
          company_id: post.company_id,
          status: post.status,
          scheduled_at: post.scheduled_at,
          has_poll: post.has_poll,
          type: post.type, // Incluir tipo do post
          metadata: post.metadata, // Incluir metadata do post
          // Campos de edição
          is_edited: post.is_edited,
          edit_count: post.edit_count,
          last_edited_at: post.last_edited_at,
          last_edited_by: post.last_edited_by,
          author: post.author,
          liked_by: post.liked_by?.filter((like) =>
            like.profiles
          ).map((like) => ({
            profiles: like.profiles,
          })) || [],
          post_audience: post.post_audience,
        } as ProcessedPost;
      });

      // Buscar imagens para todos os posts em uma única query
      if (processedPosts.length > 0) {
        const postIds = processedPosts.map(post => post.id);
        console.log("🔍 Buscando imagens para posts:", postIds);
        
        const { data: imagesData, error: imagesError } = await supabase
          .from("post_images")
          .select("*")
          .in("post_id", postIds);

        if (imagesError) {
          console.error("❌ Erro ao buscar imagens:", imagesError);
        } else {
          console.log("📸 Imagens encontradas:", imagesData);
        }

        // Agrupar imagens por post_id
        const imagesByPostId = (imagesData || []).reduce((acc, image) => {
          if (!acc[image.post_id]) {
            acc[image.post_id] = [];
          }
          acc[image.post_id].push({
            id: image.id,
            image_url: image.image_url,
            storage_path: image.storage_path,
            size: image.size,
            created_at: image.created_at,
          });
          return acc;
        }, {} as Record<string, any[]>);

        console.log("🗂️ Imagens agrupadas por post:", imagesByPostId);

        // Adicionar imagens aos posts processados
        processedPosts = processedPosts.map(post => ({
          ...post,
          images: imagesByPostId[post.id] || []
        }));

        console.log("✅ Posts com imagens processados:", processedPosts.map(p => ({ id: p.id, images: p.images?.length || 0 })));
      }

      // Aplicar filtros de audiência do lado cliente
      if (filters?.audience && filters.audience !== "all" && currentUser) {
        processedPosts = processedPosts.filter((post) => {
          if (filters.audience === "personal") {
            // Posts direcionados apenas para o usuário atual ou posts do próprio usuário
            return post.post_audience?.some(audience => 
              audience.target_type === "user" && audience.target_id === currentUser.id
            ) || post.author.id === currentUser.id;
          } else if (filters.audience === "team") {
            // Posts direcionados para equipe (implementar quando estrutura estiver definida)
            return post.post_audience?.some(audience => 
              audience.target_type === "team"
            );
          } else if (filters.audience === "department") {
            // Posts direcionados para departamento (implementar quando estrutura estiver definida)
            return post.post_audience?.some(audience => 
              audience.target_type === "department"
            );
          }
          return true;
        });
      }

      // Aplicar ordenação avançada do lado cliente (para "most_liked" e "most_commented")
      if (filters?.sortBy === "most_liked") {
        processedPosts.sort((a, b) => b.likes - a.likes);
      } else if (filters?.sortBy === "most_commented") {
        // TODO: Implementar contagem de comentários quando disponível
        // Por enquanto, manter ordenação por data
        processedPosts.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      }

      // Garantir que sempre retornamos um objeto válido
      const result: PostsPage = {
        posts: Array.isArray(processedPosts) ? processedPosts : [],
        nextPage: postsData.length === POSTS_PER_PAGE
          ? (pageParam as number) + 1
          : undefined,
        totalCount: count || 0,
      };

      console.log("🔄 Resultado final usePostsFeed:", result.posts.map(p => ({ id: p.id, hasImages: !!p.images, imagesCount: p.images?.length || 0 })));

      return result;
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage: PostsPage | undefined) => {
      // Verificação robusta para evitar erros de propriedades undefined
      if (!lastPage || typeof lastPage !== 'object' || !('nextPage' in lastPage)) {
        return undefined;
      }
      return lastPage.nextPage;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5 minutos (ou valor customizado)
    refetchOnMount: true,
    refetchOnWindowFocus: true, // Recarregar ao voltar para a aba
  });

  // Nota: A lógica de Realtime foi movida para o hook usePostsRealtime
  // para evitar duplicação de listeners e melhorar a modularidade
  // Veja: src/components/enhanced/feed/hooks/usePostsRealtime.ts

  return query;
}

/**
 * Hook para buscar detalhes de um post específico
 */
export function usePostDetails(postId: string | undefined) {
  return useQuery({
    queryKey: QueryKeys.posts.details(postId || ""),
    queryFn: async () => {
      if (!postId) {
        throw new Error("ID do post não fornecido");
      }

      const { data, error } = await supabase
        .from("posts")
        .select(`
          id,
          content,
          created_at,
          likes,
          company_id,
          status,
          scheduled_at,
          has_poll,
          type,
          metadata,
          author:profiles!posts_author_id_fkey (
            id,
            full_name,
            avatar_url
          ),
          liked_by:post_likes (
            profiles:profiles!post_likes_user_id_fkey (
              id,
              full_name,
              avatar_url
            )
          ),
          post_audience (
            target_type,
            target_id
          )
        `)
        .eq("id", postId)
        .single();

      if (error) {
        throw error;
      }

      // Buscar imagens do post
      console.log("🔍 Buscando imagens para post:", postId);
      
      const { data: imagesData, error: imagesError } = await supabase
        .from("post_images")
        .select("*")
        .eq("post_id", postId);

      if (imagesError) {
        console.error("❌ Erro ao buscar imagens:", imagesError);
      } else {
        console.log("📸 Imagens encontradas para post:", imagesData);
      }

      const images = (imagesData || []).map(image => ({
        id: image.id,
        image_url: image.image_url,
        storage_path: image.storage_path,
        size: image.size,
        created_at: image.created_at,
      }));

      console.log("✅ Post processado com imagens:", { postId, imagesCount: images.length });

      return {
        id: data.id,
        content: data.content,
        created_at: data.created_at,
        likes: data.liked_by?.length || 0,
        company_id: data.company_id,
        status: data.status,
        scheduled_at: data.scheduled_at,
        has_poll: data.has_poll,
        type: data.type,
        metadata: data.metadata,
        author: data.author,
        liked_by: data.liked_by?.filter((like) =>
          like.profiles
        ).map((like) => ({
          profiles: like.profiles,
        })) || [],
        post_audience: data.post_audience,
        images: images,
      } as ProcessedPost;
    },
    enabled: !!postId,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: (failureCount, error: any) => {
      // Não fazer retry para posts deletados (406/404/PGRST116)
      if (
        error?.status === 406 || 
        error?.status === 404 ||
        error?.code === 'PGRST116' ||
        error?.message?.includes('Not Acceptable')
      ) {
        return false;
      }
      return failureCount < 3;
    },
  });
}

/**
 * Hook para dar/remover like em um post
 */
export function useTogglePostLike() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      { postId, userId, isLiked }: {
        postId: string;
        userId: string;
        isLiked: boolean;
      },
    ) => {
      if (isLiked) {
        // Remover like
        const { error } = await supabase
          .from("post_likes")
          .delete()
          .eq("post_id", postId)
          .eq("user_id", userId);

        if (error) {
          throw error;
        }

        return { postId, liked: false };
      } else {
        // Usar UPSERT para evitar duplicatas
        const { error } = await supabase
          .from("post_likes")
          .upsert({
            post_id: postId,
            user_id: userId,
          }, {
            onConflict: 'post_id,user_id'
          });

        if (error) {
          throw error;
        }

        return { postId, liked: true };
      }
    },
    onSuccess: (data) => {
      // Invalidar queries relevantes
      queryClient.invalidateQueries({ queryKey: QueryKeys.posts.feed() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.notifications.list() });
    },
    onError: (error: any) => {
      console.error('Erro ao curtir post:', error);
      
      // Tratamento específico para erro de duplicata
      if (error.code === '23505') {
        console.warn('Tentativa de like duplicado detectada - ignorando erro');
        return; // Não mostrar toast de erro para duplicatas
      }
      
      // Log do erro para debug
      logQueryEvent('useTogglePostLike', 'Erro ao curtir post', { error }, 'error');
    }
  });
}

/**
 * Hook para buscar posts agendados
 */
export function useScheduledPosts() {
  return useQuery({
    queryKey: QueryKeys.posts.scheduled(),
    queryFn: async () => {
      const { data, error } = await supabase
        .from("posts")
        .select(`
          id,
          content,
          created_at,
          scheduled_at,
          status,
          company_id,
          author:profiles!posts_author_id_fkey (
            id,
            full_name,
            avatar_url
          ),
          post_audience (
            target_type,
            target_id
          )
        `)
        .eq("status", "scheduled")
        .gt("scheduled_at", new Date().toISOString())
        .order("scheduled_at", { ascending: true });

      if (error) {
        throw error;
      }

      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
}

/**
 * Hook para contar posts agendados
 */
export function useScheduledPostsCount() {
  return useQuery({
    queryKey: QueryKeys.posts.scheduledCount(),
    queryFn: async () => {
      logQueryEvent('useScheduledPostsCount', 'Contando posts agendados');
      
      const { count, error } = await supabase
        .from("posts")
        .select("*", { count: "exact", head: true })
        .eq("status", "scheduled")
        .gt("scheduled_at", new Date().toISOString());

      if (error) {
        logQueryEvent('useScheduledPostsCount', 'Erro ao contar posts agendados', { error });
        throw error;
      }

      logQueryEvent('useScheduledPostsCount', 'Posts agendados contados', { count });
      return count || 0;
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
}

/**
 * Hook para buscar posts de um usuário específico
 */
export function useUserPosts(userId: string | undefined) {
  return useQuery({
    queryKey: QueryKeys.posts.byUser(userId || ""),
    queryFn: async () => {
      if (!userId) {
        throw new Error("ID do usuário não fornecido");
      }

      const { data, error } = await supabase
        .from("posts")
        .select(`
          id,
          content,
          created_at,
          likes,
          status,
          author:profiles!posts_author_id_fkey (
            id,
            full_name,
            avatar_url
          ),
          liked_by:post_likes (
            profiles:profiles!post_likes_user_id_fkey (
              id,
              full_name,
              avatar_url
            )
          )
        `)
        .eq("author_id", userId)
        .eq("status", "published")
        .order("created_at", { ascending: false })
        .limit(5);

      if (error) {
        throw error;
      }

      return data.map((post) => {
        return {
          id: post.id,
          content: post.content,
          created_at: post.created_at,
          likes: post.liked_by?.length || 0,
          status: post.status,
          author: post.author,
          liked_by: post.liked_by?.filter((like) =>
            like.profiles
          ).map((like) => ({
            profiles: like.profiles,
          })) || [],
        } as ProcessedPost;
      });
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
}

/**
 * Hook para criar um novo post
 * <AUTHOR> Internet 2025
 */
export function useCreatePost() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      content,
      publishMode,
      scheduledDate,
      scheduledTime,
      poll,
      audience,
      tempPostId,
      audioData,
      videoData,
    }: {
      content: string;
      publishMode: "now" | "scheduled";
      scheduledDate?: Date;
      scheduledTime: string;
      poll?: {
        question: string;
        options: Array<{ id: string; text: string }>;
        allowMultipleAnswers: boolean;
        duration: number;
      } | null;
      audience: {
        type: "all" | "department" | "team" | "user";
        targets?: string[];
      };
      tempPostId?: string; // ID temporário para associar imagens antes da criação do post
      audioData?: {
        url: string;
        duration: number;
      }; // Dados de áudio anexado ao post
      videoData?: {
        url: string;
        duration: number;
        poster?: string;
      }; // Dados de vídeo anexado ao post
    }) => {
      const { data: userData, error: userError } = await supabase.auth
        .getUser();
      if (userError) throw userError;

      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("company_id")
        .eq("id", userData.user.id)
        .single();
      if (profileError) throw profileError;

      // Verificar se há imagens base64 no conteúdo
      const hasBase64Images = /data:image\/[^;]+;base64,/.test(content);
      logQueryEvent('useCreatePost', `Verificando imagens base64 no conteúdo: ${hasBase64Images ? "Encontradas" : "Não encontradas"}`);

      // Usar o ID temporário fornecido ou gerar um novo se houver imagens base64
      // Esse ID será usado para associar as imagens durante o upload
      // e depois será atualizado para o ID real do post
      let tempId: string | null = null;

      if (typeof tempPostId === "string" && tempPostId.trim() !== "") {
        // Usar o ID temporário fornecido pelo componente
        tempId = tempPostId;
        logQueryEvent('useCreatePost', `Usando ID temporário fornecido: ${tempId}`);
      } else if (hasBase64Images) {
        // Contar quantas imagens base64 existem no conteúdo
        const base64Matches =
          content.match(/data:image\/[^;]+;base64,[^\s"'<>)]+/g) || [];
        logQueryEvent('useCreatePost', `Encontradas ${base64Matches.length} imagens base64 no conteúdo`);

        // Gerar ID temporário para o post (UUID v4 válido para compatibilidade com Supabase)
        tempId = uuidv4();
        logQueryEvent('useCreatePost', `UUID temporário gerado para o post: ${tempId}`);
      }

      // Preparar dados do post
      const postData: {
        content: string;
        author_id: string;
        company_id: string;
        has_poll: boolean;
        status: "published" | "scheduled";
        scheduled_at?: string;
        metadata?: any;
      } = {
        // Se tiver imagens base64, inicialmente salvar conteúdo vazio
        // O conteúdo será atualizado após o processamento das imagens
        content: hasBase64Images ? "" : content,
        author_id: userData.user.id,
        company_id: profile.company_id,
        has_poll: !!poll,
        status: "published", // valor padrão
      };

      // Adicionar metadados de áudio e vídeo se fornecidos
      if (audioData || videoData) {
        postData.metadata = {};
        
        if (audioData) {
          postData.metadata.audio_url = audioData.url;
          postData.metadata.audio_duration = audioData.duration;
          logQueryEvent('useCreatePost', `Adicionando metadados de áudio: ${audioData.duration}s`);
        }
        
        if (videoData) {
          postData.metadata.video_url = videoData.url;
          postData.metadata.video_duration = videoData.duration;
          if (videoData.poster) {
            postData.metadata.video_poster = videoData.poster;
          }
          logQueryEvent('useCreatePost', `Adicionando metadados de vídeo: ${videoData.duration}s${videoData.poster ? ' com poster' : ''}`);
        }
      }

      // Se for agendado, adicionar status e data
      if (publishMode === "scheduled" && scheduledDate) {
        const [hours, minutes] = scheduledTime.split(":").map(Number);
        const scheduledDateTime = new Date(scheduledDate);
        scheduledDateTime.setHours(hours, minutes, 0, 0);

        postData.status = "scheduled" as const;
        postData.scheduled_at = scheduledDateTime.toISOString();
      }

      // Inserir o post
      const { data: postResult, error: postError } = await supabase
        .from("posts")
        .insert(postData)
        .select("id")
        .single();

      if (postError) {
        logQueryEvent("useCreatePost", "Erro ao criar post:", { error: postError }, "error");
        throw postError;
      }

      if (!postResult) {
        throw new Error("Falha ao criar post: nenhum resultado retornado");
      }

      // Se temos um ID temporário, atualizar as referências das imagens primeiro
      if (tempId) {
        try {
          logQueryEvent('useCreatePost', `Post criado com sucesso, ID: ${postResult.id}. Atualizando imagens do ID temporário...`);

          // Importar dinamicamente o serviço para evitar referência circular
          const { PostImageService } = await import("@/services/post-images");

          // Atualizar as referências das imagens do ID temporário para o ID real
          logQueryEvent('useCreatePost', `Atualizando imagens do ID temporário ${tempId} para o ID real ${postResult.id}...`);
          const updatedCount = await PostImageService.updateTempPostId(
            tempId,
            postResult.id
          );
          logQueryEvent('useCreatePost', `${updatedCount} imagens atualizadas com sucesso de ID temporário para ID real`);
        } catch (tempIdError: unknown) {
          logQueryEvent("useCreatePost", "Erro ao atualizar IDs temporários das imagens:", { error: tempIdError }, "error");
          // Não interromper o fluxo se a atualização de IDs temporários falhar
        }
      }

      // Se houver imagens base64, processá-las e atualizar o conteúdo do post
      if (hasBase64Images) {
        try {
          logQueryEvent('useCreatePost', `Iniciando processamento de imagens base64...`);

          // Importar dinamicamente o serviço para evitar referência circular
          const { PostImageService } = await import("@/services/post-images");

          // Processar imagens do conteúdo e obter conteúdo atualizado
          logQueryEvent('useCreatePost', `Tentando processar imagens do conteúdo...`);
          const processedContent = await PostImageService.processContentImages(
            content, // Conteúdo original com base64
            postResult.id, // ID real do post
            profile.company_id // Adicionado company_id
          );
          logQueryEvent('useCreatePost', `Processamento de imagens do conteúdo concluído.`, { hasAssociations: processedContent.length > 0 });

          // Atualizar o post com o conteúdo processado
          logQueryEvent('useCreatePost', `Atualizando post com conteúdo processado...`);
          const { error: updateError } = await supabase
            .from("posts")
            .update({ content: processedContent })
            .eq("id", postResult.id);

          if (updateError) {
            logQueryEvent("useCreatePost", "Erro ao atualizar conteúdo do post com imagens processadas:", { error: updateError }, "error");
            // Se houver erro na atualização, excluir o post para evitar inconsistências
            await supabase.from("posts").delete().eq("id", postResult.id);
            throw updateError;
          }

          // Atualizar o conteúdo para uso posterior
          content = processedContent;
        } catch (imageError: unknown) {
          logQueryEvent("useCreatePost", "Erro ao processar imagens do post", { error: imageError }, "error");
          // Se houver erro no processamento de imagens, excluir o post para evitar inconsistências
          await supabase.from("posts").delete().eq("id", postResult.id);
          throw imageError;
        }
      }

      // Configurar a audiência do post
      try {
        // Importar dinamicamente o serviço para evitar referência circular
        const { PostAudienceService } = await import(
          "@/services/post-audience"
        );
        await PostAudienceService.setPostAudience(postResult.id, audience);
      } catch (audienceError: unknown) {
        logQueryEvent("useCreatePost", "Erro ao configurar audiência do post", { error: audienceError }, "error");
        // Se houver erro na configuração de audiência, excluir o post para evitar inconsistências
        await supabase.from("posts").delete().eq("id", postResult.id);
        throw audienceError;
      }

      // Se tiver enquete, inserir os dados da enquete
      if (poll && postResult) {
        try {
          // Inserir a enquete
          const pollData = {
            post_id: postResult.id,
            question: poll.question,
            allow_multiple_answers: poll.allowMultipleAnswers,
            expires_at: new Date(
              Date.now() + poll.duration * 24 * 60 * 60 * 1000,
            ).toISOString(),
          };

          const { data: pollResult, error: pollError } = await supabase
            .from("polls")
            .insert(pollData)
            .select("id")
            .single();

          if (pollError) {
            logQueryEvent("useCreatePost", "Erro ao criar enquete:", { error: pollError }, "error");
            throw pollError;
          }

          if (!pollResult) {
            throw new Error(
              "Falha ao criar enquete: nenhum resultado retornado",
            );
          }

          // Inserir as opções da enquete
          const pollOptionsData = poll.options.map(
            (option) => ({
              poll_id: pollResult.id,
              text: option.text,
            }),
          );

          const { error: optionsError } = await supabase
            .from("poll_options")
            .insert(pollOptionsData);

          if (optionsError) {
            console.error("Erro ao criar opções de enquete:", optionsError);
            throw optionsError;
          }
        } catch (error: unknown) {
          logQueryEvent("useCreatePost", "Erro ao processar enquete", { error }, "error");
          // Se houver erro na criação da enquete, excluir o post para evitar inconsistências
          await supabase.from("posts").delete().eq("id", postResult.id);
          throw error;
        }
      }

      return {
        publishMode,
        scheduledDateTime: postData.scheduled_at,
        postId: postResult.id,
        createdPost: {
          id: postResult.id,
          content,
          created_at: new Date().toISOString(),
          likes: 0,
          company_id: profile.company_id,
          status: postData.status,
          scheduled_at: postData.scheduled_at,
          has_poll: !!poll,
          author: {
            id: userData.user.id,
            full_name: userData.user.user_metadata?.full_name || "",
            avatar_url: userData.user.user_metadata?.avatar_url,
          },
          liked_by: [],
          post_audience: audience.type === "all"
            ? []
            : audience.targets?.map((targetId) => ({
              target_type: audience.type,
              target_id: targetId,
            })) || [],
          audience: audience,
        },
      };
    },
    onSuccess: async (data) => {
      // Atualizar o cache de forma otimista - adicionar post diretamente ao feed
      if (data.publishMode === "now" && data.createdPost) {
        queryClient.setQueryData(
          QueryKeys.posts.feed(),
          (oldData: { pages: PostsPage[] } | undefined) => {
            if (!oldData || !oldData.pages || oldData.pages.length === 0) {
              return oldData;
            }

            // Criar uma cópia da primeira página com o novo post no início
            const firstPage = {
              ...oldData.pages[0],
              posts: [data.createdPost, ...oldData.pages[0].posts],
              totalCount: oldData.pages[0].totalCount + 1,
            };

            // Retornar um novo objeto de dados com a primeira página atualizada
            return {
              ...oldData,
              pages: [firstPage, ...oldData.pages.slice(1)],
            };
          },
        );
      }

      // Verificar se o post contém imagens
      const hasImages = data.createdPost?.content?.includes("<img") || false;

      if (hasImages) {
        logQueryEvent('useCreatePost', "Post contém imagens, invalidando cache de armazenamento");

        try {
          // Buscar company_id do usuário para invalidar as queries de armazenamento
          const { data: { user } } = await supabase.auth.getUser();
          const { data: profile } = await supabase
            .from("profiles")
            .select("company_id")
            .eq("id", user?.id)
            .single();

          const companyId = profile?.company_id;

          if (companyId) {
            // Invalidar todas as queries relacionadas ao armazenamento
            queryClient.invalidateQueries({
              queryKey: QueryKeys.storage.dashboard(companyId),
            });

            queryClient.invalidateQueries({
              queryKey: QueryKeys.storage.usage(companyId),
            });

            queryClient.invalidateQueries({
              queryKey: QueryKeys.storage.breakdown(companyId),
            });

            queryClient.invalidateQueries({
              queryKey: QueryKeys.storage.history(companyId),
            });

            logQueryEvent('useCreatePost', "Cache de armazenamento invalidado com sucesso");
          }
        } catch (error) {
          logQueryEvent("useCreatePost", "Erro ao invalidar cache de armazenamento", { error }, "error");
          // Não interrompe o fluxo se a invalidação do cache falhar
        }
      }

      // Invalidar queries relevantes para garantir a consistência dos dados
      queryClient.invalidateQueries({ queryKey: QueryKeys.posts.feed() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.posts.scheduled() });
      
      // ✨ INVALIDAR TIMELINE: Garantir que notificações do próprio usuário apareçam na timeline
      queryClient.invalidateQueries({ queryKey: QueryKeys.timeline.notifications() });
      
      logQueryEvent('useCreatePost', 'Timeline invalidada para mostrar notificação do próprio post', {
        postId: data.createdPost?.id,
        publishMode: data.publishMode
      });
    },
  });
}

/**
 * Hook para verificar se usuário pode editar um post
 * <AUTHOR> Internet 2025
 */
export function useCanEditPost(postId: string | undefined) {
  return useQuery({
    queryKey: QueryKeys.posts.canEdit(postId || ""),
    queryFn: async () => {
      if (!postId) {
        throw new Error("ID do post não fornecido");
      }

      const { data, error } = await supabase
        .rpc('can_user_edit_post', { p_post_id: postId });

      if (error) {
        logQueryEvent('useCanEditPost', `Erro ao verificar permissão de edição do post ${postId}`, error, 'error');
        throw error;
      }

      logQueryEvent('useCanEditPost', `Verificação de edição do post ${postId}`, data);
      return data;
    },
    enabled: !!postId,
    staleTime: 1 * 60 * 1000, // 1 minuto (pode mudar rapidamente com time limit)
  });
}

/**
 * Hook para buscar histórico de edições de um post
 * <AUTHOR> Internet 2025
 */
export function usePostEditHistory(postId: string | undefined) {
  return useQuery({
    queryKey: QueryKeys.posts.editHistory(postId || ""),
    queryFn: async () => {
      if (!postId) {
        throw new Error("ID do post não fornecido");
      }

      const { data, error } = await supabase
        .from("post_edit_history")
        .select(`
          id,
          content,
          content_diff,
          edit_reason,
          edit_type,
          created_at,
          editor:profiles!post_edit_history_editor_id_fkey (
            id,
            full_name,
            avatar_url
          )
        `)
        .eq("post_id", postId)
        .order("created_at", { ascending: false });

      if (error) {
        logQueryEvent('usePostEditHistory', `Erro ao buscar histórico do post ${postId}`, error, 'error');
        throw error;
      }

      logQueryEvent('usePostEditHistory', `Histórico do post ${postId} carregado`, { count: data?.length || 0 });
      return data || [];
    },
    enabled: !!postId,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
}

/**
 * Hook para atualizar/editar um post existente
 * <AUTHOR> Internet 2025
 */
export function useUpdatePost() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      postId,
      content,
      editReason,
      metadata,
    }: {
      postId: string;
      content: string;
      editReason?: string;
      metadata?: {
        preserveImages?: boolean;
        newImages?: string[];
      };
    }) => {
      logQueryEvent('useUpdatePost', `Iniciando edição do post ${postId}`);

      // Primeiro verificar se o usuário pode editar
      const { data: canEditData, error: canEditError } = await supabase
        .rpc('can_user_edit_post', { p_post_id: postId });

      if (canEditError) {
        logQueryEvent('useUpdatePost', `Erro ao verificar permissão de edição`, canEditError, 'error');
        throw canEditError;
      }

      if (!canEditData?.can_edit) {
        const errorMessage = canEditData?.message || 'Não é possível editar este post';
        logQueryEvent('useUpdatePost', `Edição negada: ${errorMessage}`, canEditData, 'error');
        throw new Error(errorMessage);
      }

      // Verificar se há imagens base64 no conteúdo
      const hasBase64Images = /data:image\/[^;]+;base64,/.test(content);
      let processedContent = content;

      if (hasBase64Images) {
        try {
          logQueryEvent('useUpdatePost', `Processando imagens base64 na edição do post ${postId}`);

          // Obter company_id do usuário
          const { data: userData } = await supabase.auth.getUser();
          const { data: profile } = await supabase
            .from("profiles")
            .select("company_id")
            .eq("id", userData.user?.id)
            .single();

          if (!profile?.company_id) {
            throw new Error("Company ID não encontrado");
          }

          // Importar dinamicamente o serviço para evitar referência circular
          const { PostImageService } = await import("@/services/post-images");

          // Processar imagens do conteúdo e obter conteúdo atualizado
          processedContent = await PostImageService.processContentImages(
            content,
            postId,
            profile.company_id
          );

          logQueryEvent('useUpdatePost', `Processamento de imagens concluído para post ${postId}`);
        } catch (imageError: unknown) {
          logQueryEvent("useUpdatePost", "Erro ao processar imagens na edição", { error: imageError }, "error");
          throw imageError;
        }
      }

      // Atualizar o post
      const { data: updatedPost, error: updateError } = await supabase
        .from("posts")
        .update({
          content: processedContent,
          updated_at: new Date().toISOString(),
        })
        .eq("id", postId)
        .select(`
          id,
          content,
          created_at,
          updated_at,
          likes,
          company_id,
          status,
          scheduled_at,
          has_poll,
          type,
          metadata,
          is_edited,
          edit_count,
          last_edited_at,
          last_edited_by,
          author:profiles!posts_author_id_fkey (
            id,
            full_name,
            avatar_url
          ),
          liked_by:post_likes (
            profiles:profiles!post_likes_user_id_fkey (
              id,
              full_name,
              avatar_url
            )
          ),
          post_audience (
            target_type,
            target_id
          )
        `)
        .single();

      if (updateError) {
        logQueryEvent("useUpdatePost", "Erro ao atualizar post", { error: updateError }, "error");
        throw updateError;
      }

      if (!updatedPost) {
        throw new Error("Falha ao atualizar post: nenhum resultado retornado");
      }

      // Adicionar razão da edição se fornecida (para planos que suportam)
      if (editReason && canEditData.plan !== 'Grátis') {
        try {
          await supabase
            .from("post_edit_history")
            .update({ edit_reason: editReason })
            .eq("post_id", postId)
            .order("created_at", { ascending: false })
            .limit(1);
        } catch (reasonError) {
          // Não falhar a edição se não conseguir salvar a razão
          logQueryEvent("useUpdatePost", "Erro ao salvar razão da edição", { error: reasonError }, "warn");
        }
      }

      logQueryEvent('useUpdatePost', `Post ${postId} editado com sucesso`);

      return {
        id: updatedPost.id,
        content: updatedPost.content,
        created_at: updatedPost.created_at,
        updated_at: updatedPost.updated_at,
        likes: updatedPost.liked_by?.length || 0,
        company_id: updatedPost.company_id,
        status: updatedPost.status,
        scheduled_at: updatedPost.scheduled_at,
        has_poll: updatedPost.has_poll,
        type: updatedPost.type,
        metadata: updatedPost.metadata,
        is_edited: updatedPost.is_edited,
        edit_count: updatedPost.edit_count,
        last_edited_at: updatedPost.last_edited_at,
        author: updatedPost.author,
        liked_by: updatedPost.liked_by?.filter((like) =>
          like.profiles
        ).map((like) => ({
          profiles: like.profiles,
        })) || [],
        post_audience: updatedPost.post_audience,
      } as ProcessedPost;
    },
    onMutate: async ({ postId, content }) => {
      logQueryEvent('useUpdatePost', 'onMutate - Cancelando queries em andamento', { postId });

      // Cancel ongoing queries to prevent race conditions
      await queryClient.cancelQueries({ queryKey: QueryKeys.posts.feed() });
      await queryClient.cancelQueries({ queryKey: QueryKeys.posts.details(postId) });

      // Snapshot previous values for potential rollback
      const previousFeedData = queryClient.getQueryData(QueryKeys.posts.feed());
      const previousPostData = queryClient.getQueryData(QueryKeys.posts.details(postId));

      // SIMPLIFICADO: Não fazer atualização otimística para evitar problemas de cache
      // Vamos confiar na invalidação no onSuccess para atualizar corretamente

      return { previousFeedData, previousPostData };
    },
    onSuccess: (updatedPost) => {
      logQueryEvent('useUpdatePost', 'onSuccess - Processando edição bem-sucedida', { postId: updatedPost.id });

      // Update specific post cache
      queryClient.setQueryData(QueryKeys.posts.details(updatedPost.id), updatedPost);

      // ESTRATÉGIA SIMPLIFICADA: Invalidar feed para garantir consistência total
      // Isso resolve qualquer problema de cache e garante que o UI mostre os dados corretos
      queryClient.invalidateQueries({ queryKey: QueryKeys.posts.feed() });

      // Invalidar caches relacionados
      queryClient.invalidateQueries({ queryKey: QueryKeys.posts.editHistory(updatedPost.id) });
      queryClient.invalidateQueries({ queryKey: QueryKeys.posts.canEdit(updatedPost.id) });
      queryClient.invalidateQueries({ queryKey: QueryKeys.notifications.list() });

      logQueryEvent('useUpdatePost', 'onSuccess - Invalidação de cache concluída');
    },
    onError: (error: any, { postId }, context) => {
      // Rollback optimistic updates
      if (context?.previousFeedData) {
        queryClient.setQueryData(QueryKeys.posts.feed(), context.previousFeedData);
      }
      if (context?.previousPostData) {
        queryClient.setQueryData(QueryKeys.posts.details(postId), context.previousPostData);
      }

      logQueryEvent('useUpdatePost', 'Erro ao editar post', { error }, 'error');
    },
  });
}

/**
 * Hook para buscar um post específico
 */
export function usePost(postId: string | undefined) {
  return useQuery({
    queryKey: QueryKeys.posts.detail(postId || ''),
    queryFn: async () => {
      if (!postId) throw new Error('Post ID is required');

      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          profiles:author_id (
            id,
            full_name,
            avatar_url
          ),
          post_audience (
            target_type,
            target_id
          )
        `)
        .eq('id', postId)
        .single();

      if (error) throw error;
      if (!data) throw new Error('Post not found');

      return data;
    },
    enabled: !!postId,
  });
}

/**
 * Hook para excluir um post
 * <AUTHOR> Internet 2025
 */
export function useDeletePost() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (postId: string) => {
      if (!postId) {
        throw new Error('ID do post é obrigatório');
      }

      logQueryEvent('useDeletePost', 'Iniciando exclusão do post', { postId });

      const { error } = await supabase
        .from('posts')
        .delete()
        .eq('id', postId);

      if (error) {
        logQueryEvent('useDeletePost', 'Erro ao excluir post', { error }, 'error');
        throw error;
      }

      logQueryEvent('useDeletePost', 'Post excluído com sucesso', { postId });
      return postId;
    },
    onSuccess: (deletedPostId) => {
      logQueryEvent('useDeletePost', 'onSuccess - Iniciando limpeza de cache', { postId: deletedPostId });

      // 🔥 CORREÇÃO: Remover do cache posts específicos em vez de invalidar (evita erro 406)
      
      // 1. Atualização otimística do cache do feed (remove o post imediatamente)
      queryClient.setQueryData(QueryKeys.posts.feed(), (oldData: any) => {
        if (!oldData?.pages) return oldData;
        
        return {
          ...oldData,
          pages: oldData.pages.map((page: any) => ({
            ...page,
            posts: page.posts.filter((post: any) => post.id !== deletedPostId),
            totalCount: Math.max(0, (page.totalCount || 0) - 1)
          }))
        };
      });

      // 2. REMOVER (não invalidar) queries específicas do post deletado
      queryClient.removeQueries({ queryKey: QueryKeys.posts.details(deletedPostId) });
      queryClient.removeQueries({ queryKey: QueryKeys.posts.detail(deletedPostId) });
      queryClient.removeQueries({ queryKey: QueryKeys.posts.comments(deletedPostId) });
      queryClient.removeQueries({ queryKey: QueryKeys.posts.likes(deletedPostId) });
      queryClient.removeQueries({ queryKey: QueryKeys.posts.editHistory(deletedPostId) });
      queryClient.removeQueries({ queryKey: QueryKeys.posts.canEdit(deletedPostId) });
      
      // 3. Invalidar apenas listas gerais (não queries específicas)
      queryClient.invalidateQueries({ queryKey: QueryKeys.posts.all() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.posts.trending() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.posts.scheduled() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.timeline.notifications() });

      logQueryEvent('useDeletePost', 'onSuccess - Limpeza de cache concluída', { postId: deletedPostId });
    },
    onError: (error: any, postId) => {
      logQueryEvent('useDeletePost', 'Erro ao excluir post', { postId, error }, 'error');
    },
  });
}
