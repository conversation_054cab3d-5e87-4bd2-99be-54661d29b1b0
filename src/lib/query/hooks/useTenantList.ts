/**
 * Hook para buscar listagem de tenants com dados reais
 * <AUTHOR> Internet 2025
 */

import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { QueryKeys } from '@/lib/query/queryKeys';
import { formatCNPJ } from '@/lib/utils/cnpjUtils';

interface TenantData {
  id: string;
  name: string;
  cnpj: string;
  description?: string;
  website?: string;
  contact_email?: string;
  contact_phone?: string;
  project_manager_name?: string;
  project_manager_email?: string;
  project_manager_phone?: string;
  technical_manager_name?: string;
  technical_manager_email?: string;
  technical_manager_phone?: string;
  financial_manager_name?: string;
  financial_manager_email?: string;
  financial_manager_phone?: string;
  status: string;
  plan: string;
  users_count: number; // Alterado para snake_case
  users_limit: number;
  storage_used: number;
  storage_limit: number;
  created_at: string; // Alterado para snake_case
  last_access: string;
  // Campos de trial/cortesia
  courtesy_period_start?: string | null;
  courtesy_period_end?: string | null;
  activation_status?: string | null;
  selected_plan_info?: any;
}

export function useTenantList() {
  return useQuery({
    queryKey: QueryKeys.admin.tenantList(),
    queryFn: async (): Promise<TenantData[]> => {
      logQueryEvent('useTenantList', 'Buscando listagem de tenants reais');

      try {
        // SEGURANÇA CRÍTICA: Verificar se usuário é da empresa Vindula
        const { data: user, error: userError } = await supabase.auth.getUser();
        if (userError) {
          logQueryEvent('useTenantList', 'Erro ao obter usuário autenticado', { error: userError }, 'error');
          throw new Error('Não foi possível verificar autenticação');
        }

        if (!user.user?.id) {
          logQueryEvent('useTenantList', 'Usuário não autenticado', {}, 'error');
          throw new Error('Usuário não autenticado');
        }

        // Verificar se usuário pertence à empresa Vindula
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select(`
            id,
            company_id,
            companies!profiles_company_id_fkey(
              id,
              slug,
              name
            )
          `)
          .eq('id', user.user.id)
          .single();

        if (profileError || !profile) {
          logQueryEvent('useTenantList', 'Erro ao buscar perfil do usuário', { error: profileError }, 'error');
          throw new Error('Perfil de usuário não encontrado');
        }

        if (profile.companies?.slug !== 'vindula-intranet') {
          logQueryEvent('useTenantList', 'Acesso negado - usuário não é da Vindula', { 
            userCompany: profile.companies?.slug 
          }, 'error');
          throw new Error('Acesso negado: Apenas usuários da Vindula podem acessar esta funcionalidade');
        }

        logQueryEvent('useTenantList', 'Verificação de segurança aprovada', { 
          userId: user.user.id,
          company: profile.companies?.name 
        });

        // Buscar companies com informações completas
        const { data: companiesData, error: companiesError } = await supabase
          .from('companies')
          .select(`
            id, 
            name,
            slug,
            created_at,
            cnpj,
            description,
            website,
            contact_email,
            contact_phone,
            project_manager_name,
            project_manager_email,
            project_manager_phone,
            technical_manager_name,
            technical_manager_email,
            technical_manager_phone,
            financial_manager_name,
            financial_manager_email,
            financial_manager_phone
          `)
          .order('created_at', { ascending: false });

        if (companiesError) {
          logQueryEvent('useTenantList', 'Erro ao buscar companies', { error: companiesError }, 'error');
          throw companiesError;
        }

        // Buscar subscriptions
        const { data: subscriptionsData, error: subscriptionsError } = await supabase
          .from('subscriptions')
          .select(`
            company_id,
            status,
            user_limit,
            storage_limit,
            plan_id,
            subscription_plans!subscriptions_plan_id_fkey (
              name,
              user_limit,
              storage_limit
            )
          `);

        if (subscriptionsError) {
          logQueryEvent('useTenantList', 'Erro ao buscar subscriptions', { error: subscriptionsError }, 'error');
          throw subscriptionsError;
        }

        // Buscar plano grátis padrão da tabela subscription_plans
        const { data: freePlanData, error: freePlanError } = await supabase
          .from('subscription_plans')
          .select('name, user_limit, storage_limit')
          .eq('name', 'Grátis')
          .single();

        if (freePlanError) {
          logQueryEvent('useTenantList', 'Erro ao buscar plano grátis', { error: freePlanError }, 'error');
          // Usar valores padrão se não conseguir buscar
        }

        // Buscar contagem de usuários por company
        const { data: userCountData, error: userCountError } = await supabase
          .from('profiles')
          .select('company_id')
          .not('company_id', 'is', null);

        if (userCountError) {
          logQueryEvent('useTenantList', 'Erro ao buscar contagem de usuários', { error: userCountError }, 'error');
          throw userCountError;
        }

        // Log debug da contagem de usuários
        logQueryEvent('useTenantList', 'Debug contagem de usuários', { 
          totalProfiles: userCountData?.length || 0,
          sampleData: userCountData?.slice(0, 5) || []
        });

        // Buscar dados de storage por company
        const { data: storageData, error: storageError } = await supabase
          .from('storage_usage')
          .select('company_id, used_storage, updated_at');

        if (storageError) {
          logQueryEvent('useTenantList', 'Erro ao buscar dados de storage', { error: storageError }, 'error');
          throw storageError;
        }

        // Buscar dados de commercial_leads para identificar trials ativos
        const { data: courtesyData, error: courtesyError } = await supabase
          .from('commercial_leads')
          .select(`
            company_id,
            courtesy_period_start,
            courtesy_period_end,
            activation_status,
            plan_activated_at,
            selected_plan
          `)
          .eq('activation_status', 'activated')
          .not('courtesy_period_start', 'is', null);

        if (courtesyError) {
          logQueryEvent('useTenantList', 'Erro ao buscar dados de cortesia', { error: courtesyError }, 'warn');
          // Não throw - fallback sem dados de cortesia
        }

        // Buscar último acesso real por empresa (da tabela user_sessions)
        // Como user_sessions já tem company_id, podemos usá-lo diretamente
        const { data: lastAccessData, error: lastAccessError } = await supabase
          .from('user_sessions')
          .select(`
            user_id,
            company_id,
            started_at,
            last_activity_at
          `)
          .order('started_at', { ascending: false });

        if (lastAccessError) {
          logQueryEvent('useTenantList', 'Erro ao buscar dados de último acesso', { error: lastAccessError }, 'warn');
          // Não throw - fallback para created_at
        }

        // Criar mapas para facilitar associação
        const subscriptionMap = new Map();
        subscriptionsData?.forEach((sub: any) => {
          if (sub.company_id) {
            subscriptionMap.set(sub.company_id, sub);
          }
        });

        const userCountMap = new Map();
        userCountData?.forEach((profile: any) => {
          if (profile.company_id) {
            const currentCount = userCountMap.get(profile.company_id) || 0;
            userCountMap.set(profile.company_id, currentCount + 1);
          }
        });

        // Log debug da contagem de usuários por empresa
        const companiesUserCount = companiesData?.map(company => ({
          companyId: company.id,
          companyName: company.name,
          userCount: userCountMap.get(company.id) || 0
        }));
        
        logQueryEvent('useTenantList', 'Debug contagem de usuários por empresa', { 
          totalProfiles: userCountData?.length || 0,
          companiesUserCount: companiesUserCount?.slice(0, 5) || []
        });

        const storageMap = new Map();
        storageData?.forEach((storage: any) => {
          if (storage.company_id) {
            storageMap.set(storage.company_id, storage);
          }
        });

        // Criar mapa de dados de cortesia por empresa
        const courtesyMap = new Map();
        if (courtesyData && !courtesyError) {
          courtesyData.forEach((courtesy: any) => {
            if (courtesy.company_id) {
              courtesyMap.set(courtesy.company_id, courtesy);
            }
          });
        }

        // Criar mapa de último acesso por empresa
        const lastAccessMap = new Map();
        if (lastAccessData && !lastAccessError) {
          // Agrupar sessões por empresa e encontrar a mais recente
          const sessionsByCompany = new Map();
          
          lastAccessData.forEach((session: any) => {
            const companyId = session.company_id;
            if (companyId) {
              const existingSessions = sessionsByCompany.get(companyId) || [];
              existingSessions.push(session);
              sessionsByCompany.set(companyId, existingSessions);
            }
          });

          // Para cada empresa, encontrar a sessão mais recente
          sessionsByCompany.forEach((sessions, companyId) => {
            let mostRecentAccess = null;
            
            sessions.forEach((session: any) => {
              // Usar a data mais recente entre started_at e last_activity_at
              const sessionLastAccess = session.last_activity_at || session.started_at;
              
              if (!mostRecentAccess || (sessionLastAccess && sessionLastAccess > mostRecentAccess)) {
                mostRecentAccess = sessionLastAccess;
              }
            });

            if (mostRecentAccess) {
              lastAccessMap.set(companyId, mostRecentAccess);
            }
          });
        }

        // FUNÇÕES HELPER PARA LÓGICA CORRETA
        const determineStatus = (company: any, subscription: any, courtesyInfo: any) => {
          // Empresa Vindula sempre ativa
          if (company.slug === 'vindula-intranet') return 'active';
          
          // PRIORIDADE: Verificar se tem trial ativo via courtesy_period
          if (courtesyInfo && courtesyInfo.courtesy_period_start) {
            const now = new Date();
            const endDate = new Date(courtesyInfo.courtesy_period_end);
            
            if (now <= endDate) {
              return 'trial'; // EM TRIAL ATIVO!
            } else {
              return 'active'; // Trial expirado, volta para status normal
            }
          }
          
          // Sem subscription = Plano Grátis ativo (NÃO cancelado!)
          if (!subscription) return 'active';
          
          // Com subscription - verificar status real
          switch (subscription.status) {
            case 'active': return 'active';
            case 'trialing': return 'trial';
            case 'pending': return 'trial'; // Aguardando ativação = trial
            case 'past_due':
            case 'unpaid': 
            case 'paused': return 'suspended';
            case 'cancelled':
            case 'incomplete_expired': return 'cancelled';
            default: return 'active'; // Default seguro
          }
        };

        const determinePlan = (subscription: any, courtesyInfo: any) => {
          // Se tem trial ativo e plano selecionado no commercial_lead
          if (courtesyInfo && courtesyInfo.selected_plan) {
            return courtesyInfo.selected_plan.name || 'Pro'; // Assumir Pro se não especificado
          }
          
          // Sem subscription = Plano Grátis
          if (!subscription) return 'Grátis';
          
          // Usar nome real do plano da tabela subscription_plans
          return subscription.subscription_plans?.name || 'Grátis';
        };

        const determineUserLimit = (subscription: any, plan: string) => {
          // Plano Grátis - usar dados reais da tabela subscription_plans
          if (plan === 'Grátis') {
            return freePlanData?.user_limit || 10; // fallback para 10
          }
          
          // Para planos pagos, usar o limite do plano (não da subscription que pode ser 0)
          return subscription?.subscription_plans?.user_limit || subscription?.user_limit || 0;
        };

        const determineStorageLimit = (subscription: any, plan: string) => {
          // Plano Grátis - converter de bytes para GB se necessário
          if (plan === 'Grátis') {
            const freePlanStorageBytes = freePlanData?.storage_limit || 1073741824; // 1GB em bytes
            return Math.round(freePlanStorageBytes / (1024 * 1024 * 1024) * 100) / 100;
          }
          
          // Para planos pagos, converter limite do plano de bytes para GB
          if (subscription?.subscription_plans?.storage_limit) {
            const storageLimitBytes = subscription.subscription_plans.storage_limit;
            return Math.round(storageLimitBytes / (1024 * 1024 * 1024) * 100) / 100;
          }
          
          // Fallback: converter de bytes para GB se necessário
          const storageLimitBytes = subscription?.storage_limit || 0;
          return Math.round(storageLimitBytes / (1024 * 1024 * 1024) * 100) / 100;
        };

        // Processar dados
        const tenants: TenantData[] = companiesData?.map((company: any) => {
          const subscription = subscriptionMap.get(company.id);
          const userCount = userCountMap.get(company.id) || 0;
          const storage = storageMap.get(company.id);
          const courtesyInfo = courtesyMap.get(company.id);

          // Determinar status com lógica corrigida (incluindo dados de cortesia)
          const status = determineStatus(company, subscription, courtesyInfo);

          // Determinar plano com lógica corrigida (incluindo dados de cortesia)
          const plan = determinePlan(subscription, courtesyInfo);

          // Determinar limites usando as novas funções
          const userLimit = determineUserLimit(subscription, plan);
          const storageLimitGB = determineStorageLimit(subscription, plan);

          // Converter storage usado para GB
          const usedStorageBytes = storage?.used_storage || 0;
          const storageUsedGB = Math.round(usedStorageBytes / (1024 * 1024 * 1024) * 100) / 100;

          return {
            id: company.id,
            name: company.name,
            cnpj: company.cnpj ? formatCNPJ(company.cnpj) : '',
            description: company.description,
            website: company.website,
            contact_email: company.contact_email,
            contact_phone: company.contact_phone,
            project_manager_name: company.project_manager_name,
            project_manager_email: company.project_manager_email,
            project_manager_phone: company.project_manager_phone,
            technical_manager_name: company.technical_manager_name,
            technical_manager_email: company.technical_manager_email,
            technical_manager_phone: company.technical_manager_phone,
            financial_manager_name: company.financial_manager_name,
            financial_manager_email: company.financial_manager_email,
            financial_manager_phone: company.financial_manager_phone,
            status,
            plan,
            users_count: userCount,
            users_limit: userLimit,
            storage_used: storageUsedGB,
            storage_limit: storageLimitGB,
            created_at: company.created_at, // Manter formato ISO para parsing
            last_access: lastAccessMap.get(company.id) || company.created_at, // Fallback inteligente: se não há sessões, usar data de criação
            // Campos de trial/cortesia
            courtesy_period_start: courtesyInfo?.courtesy_period_start || null,
            courtesy_period_end: courtesyInfo?.courtesy_period_end || null,
            activation_status: courtesyInfo?.activation_status || null,
            selected_plan_info: courtesyInfo?.selected_plan || null
          };
        }) || [];

        // Log para debug da contagem de usuários e último acesso
        const debugInfo = tenants.map(t => ({
          name: t.name,
          plan: t.plan,
          users_count: t.users_count,
          users_limit: t.users_limit,
          hasSubscription: !!subscriptionMap.get(t.id),
          created_at: t.created_at,
          last_access: t.last_access,
          hasRealLastAccess: !!lastAccessMap.get(t.id)
        }));
        
        logQueryEvent('useTenantList', 'Listagem de tenants carregada com sucesso', { 
          count: tenants.length,
          debugInfo,
          totalSessions: lastAccessData?.length || 0,
          companiesWithSessions: lastAccessMap.size,
          lastAccessError: !!lastAccessError
        });
        
        return tenants;
      } catch (error) {
        logQueryEvent('useTenantList', 'Erro na query de listagem de tenants', { error }, 'error');
        throw error;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutos
    gcTime: 5 * 60 * 1000, // 5 minutos
    retry: 2,
  });
} 