/**
 * Hooks centralizados para marketplace estratégico
 * <AUTHOR> Internet 2025
 */
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { QueryKeys } from '../queryKeys';
import { useAuthStore } from '@/stores/authStore';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { toast } from '@/hooks/use-toast';
import type {
  StrategicCategory,
  StrategicItem,
  StrategicPurchase,
  SpecialOffer,
  MarketplaceStats,
  CreateStrategicCategoryData,
  UpdateStrategicCategoryData,
  CreateStrategicItemData,
  UpdateStrategicItemData,
  PurchaseStrategicItemRequest,
  PurchaseStrategicItemResponse,
  CreateSpecialOfferData,
  UpdateSpecialOfferData
} from '@/types/marketplace.types';

// ===== HOOKS PARA CATEGORIAS ESTRATÉGICAS =====

/**
 * Hook para buscar categorias estratégicas da empresa
 * SEGURANÇA: Não passa company_id - função SQL usa auth.uid() + profiles internamente
 */
export function useStrategicCategories() {
  const company_id = useAuthStore((state) => state.company_id);

  return useQuery({
    queryKey: QueryKeys.marketplace.strategicCategories(company_id || ''),
    queryFn: async (): Promise<StrategicCategory[]> => {
      logQueryEvent('useStrategicCategories', 'Buscando categorias estratégicas');

      // SEGURANÇA: Não passar company_id - função usa auth.uid() + profiles
      const { data, error } = await supabase.rpc('get_strategic_categories');

      if (error) {
        logQueryEvent('useStrategicCategories', 'Erro ao buscar categorias', { error }, 'error');
        throw error;
      }

      logQueryEvent('useStrategicCategories', 'Categorias carregadas', { count: data?.length || 0 });
      return data || [];
    },
    enabled: true,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
  });
}

/**
 * Hook para buscar TODAS as categorias estratégicas (incluindo inativas) para uso administrativo
 * SEGURANÇA: Não passa company_id - função SQL usa auth.uid() + profiles internamente
 */
export function useAllStrategicCategories() {
  const company_id = useAuthStore((state) => state.company_id);

  return useQuery({
    queryKey: QueryKeys.marketplace.strategicCategories(`${company_id || ''}_all`),
    queryFn: async (): Promise<StrategicCategory[]> => {
      logQueryEvent('useAllStrategicCategories', 'Buscando todas as categorias estratégicas (incluindo inativas)');

      // SEGURANÇA: Não passar company_id - função usa auth.uid() + profiles
      const { data, error } = await supabase.rpc('get_all_strategic_categories');

      if (error) {
        logQueryEvent('useAllStrategicCategories', 'Erro ao buscar todas as categorias', { error }, 'error');
        throw error;
      }

      logQueryEvent('useAllStrategicCategories', 'Todas as categorias carregadas', { count: data?.length || 0 });
      return data || [];
    },
    enabled: true,
    staleTime: 3 * 60 * 1000, // 3 minutos (menor que o público)
    gcTime: 8 * 60 * 1000, // 8 minutos
  });
}

/**
 * Hook para criar categoria estratégica
 */
export function useCreateStrategicCategory() {
  const queryClient = useQueryClient();
  const company_id = useAuthStore((state) => state.company_id);

  return useMutation({
    mutationFn: async (data: CreateStrategicCategoryData): Promise<StrategicCategory> => {
      logQueryEvent('useCreateStrategicCategory', 'Criando categoria estratégica', data);

      const { data: result, error } = await supabase
        .from('strategic_categories')
        .insert({
          ...data,
          company_id: company_id!
        })
        .select()
        .single();

      if (error) {
        logQueryEvent('useCreateStrategicCategory', 'Erro ao criar categoria', { error }, 'error');
        throw error;
      }

      logQueryEvent('useCreateStrategicCategory', 'Categoria criada', { id: result.id });
      return result;
    },
    onSuccess: (newCategory) => {
      // Invalidar todo o cache do marketplace
      queryClient.invalidateQueries({ 
        queryKey: QueryKeys.marketplace.all()
      });

      // Forçar refetch das categorias (público e admin)
      queryClient.refetchQueries({
        queryKey: QueryKeys.marketplace.strategicCategories(company_id || ''),
        type: 'active'
      });
      queryClient.refetchQueries({
        queryKey: QueryKeys.marketplace.strategicCategories(`${company_id || ''}_all`),
        type: 'active'
      });

      toast({
        title: 'Categoria criada!',
        description: `Categoria "${newCategory.name}" foi criada com sucesso.`,
      });
    },
    onError: (error) => {
      toast({
        title: 'Erro ao criar categoria',
        description: 'Não foi possível criar a categoria. Tente novamente.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook para atualizar categoria estratégica
 */
export function useUpdateStrategicCategory() {
  const queryClient = useQueryClient();
  const company_id = useAuthStore((state) => state.company_id);

  return useMutation({
    mutationFn: async ({ id, ...data }: { id: string } & UpdateStrategicCategoryData): Promise<StrategicCategory> => {
      logQueryEvent('useUpdateStrategicCategory', 'Atualizando categoria estratégica', { id, ...data });

      const { data: result, error } = await supabase
        .from('strategic_categories')
        .update({
          ...data,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        logQueryEvent('useUpdateStrategicCategory', 'Erro ao atualizar categoria', { error }, 'error');
        throw error;
      }

      logQueryEvent('useUpdateStrategicCategory', 'Categoria atualizada', { id: result.id });
      return result;
    },
    onSuccess: (updatedCategory) => {
      // Invalidar todas as queries relacionadas ao marketplace
      queryClient.invalidateQueries({ 
        queryKey: QueryKeys.marketplace.all()
      });

      // Forçar refetch das categorias (público e admin)
      queryClient.refetchQueries({
        queryKey: QueryKeys.marketplace.strategicCategories(company_id || ''),
        type: 'active'
      });
      queryClient.refetchQueries({
        queryKey: QueryKeys.marketplace.strategicCategories(`${company_id || ''}_all`),
        type: 'active'
      });

      toast({
        title: 'Categoria atualizada!',
        description: `Categoria "${updatedCategory.name}" foi atualizada com sucesso.`,
      });
    },
    onError: (error) => {
      toast({
        title: 'Erro ao atualizar categoria',
        description: 'Não foi possível atualizar a categoria. Tente novamente.',
        variant: 'destructive',
      });
    },
  });
}

// ===== HOOKS PARA ITENS ESTRATÉGICOS =====

/**
 * Hook para buscar itens de uma categoria estratégica
 */
export function useStrategicItems(categoryId?: string, userId?: string) {
  return useQuery({
    queryKey: QueryKeys.marketplace.strategicItems(categoryId || 'all', userId || ''),
    queryFn: async (): Promise<StrategicItem[]> => {
      logQueryEvent('useStrategicItems', 'Buscando itens estratégicos', { categoryId, userId });

      if (categoryId) {
        // Buscar itens de uma categoria específica
        const { data, error } = await supabase.rpc('get_strategic_items', {
          p_category_id: categoryId,
          p_user_id: userId || null
        });

        if (error) {
          logQueryEvent('useStrategicItems', 'Erro ao buscar itens', { error }, 'error');
          throw error;
        }

        logQueryEvent('useStrategicItems', 'Itens carregados', { count: data?.length || 0 });
        return data || [];
      } else {
        // Buscar todos os itens da empresa (para admin)
        const company_id = useAuthStore.getState().company_id;
        
        if (!company_id) {
          logQueryEvent('useStrategicItems', 'Company ID não disponível, retornando array vazio');
          return [];
        }
        
        const { data, error } = await supabase
          .from('strategic_items')
          .select(`
            *,
            category:strategic_categories(name, gradient)
          `)
          .eq('company_id', company_id)
          .order('created_at', { ascending: false });

        if (error) {
          logQueryEvent('useStrategicItems', 'Erro ao buscar todos os itens', { error }, 'error');
          throw error;
        }

        logQueryEvent('useStrategicItems', 'Todos os itens carregados', { count: data?.length || 0 });
        return data || [];
      }
    },
    enabled: true, // Sempre habilitado agora
    staleTime: 3 * 60 * 1000, // 3 minutos
    gcTime: 8 * 60 * 1000, // 8 minutos
  });
}

/**
 * Hook para criar item estratégico
 */
export function useCreateStrategicItem() {
  const queryClient = useQueryClient();
  const company_id = useAuthStore((state) => state.company_id);

  return useMutation({
    mutationFn: async (data: CreateStrategicItemData): Promise<StrategicItem> => {
      logQueryEvent('useCreateStrategicItem', 'Criando item estratégico', data);

      const { data: result, error } = await supabase
        .from('strategic_items')
        .insert({
          ...data,
          company_id: company_id!,
          current_stock: data.total_stock // Inicializar estoque atual com total
        })
        .select()
        .single();

      if (error) {
        logQueryEvent('useCreateStrategicItem', 'Erro ao criar item', { error }, 'error');
        throw error;
      }

      logQueryEvent('useCreateStrategicItem', 'Item criado', { id: result.id });
      return result;
    },
    onSuccess: (newItem) => {
      // Invalidar cache das categorias e itens
      queryClient.invalidateQueries({ 
        queryKey: QueryKeys.marketplace.strategicCategories(company_id || '') 
      });
      queryClient.invalidateQueries({ 
        queryKey: QueryKeys.marketplace.strategicItems(newItem.category_id) 
      });

      toast({
        title: 'Item criado!',
        description: `Item "${newItem.name}" foi criado com sucesso.`,
      });
    },
    onError: (error) => {
      toast({
        title: 'Erro ao criar item',
        description: 'Não foi possível criar o item. Tente novamente.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook para atualizar item estratégico
 */
export function useUpdateStrategicItem() {
  const queryClient = useQueryClient();
  const company_id = useAuthStore((state) => state.company_id);

  return useMutation({
    mutationFn: async ({ id, ...data }: { id: string } & UpdateStrategicItemData): Promise<StrategicItem> => {
      logQueryEvent('useUpdateStrategicItem', 'Atualizando item estratégico', { id, ...data });

      const { data: result, error } = await supabase
        .from('strategic_items')
        .update({
          ...data,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        logQueryEvent('useUpdateStrategicItem', 'Erro ao atualizar item', { error }, 'error');
        throw error;
      }

      logQueryEvent('useUpdateStrategicItem', 'Item atualizado', { id: result.id });
      return result;
    },
    onSuccess: (updatedItem) => {
      // Invalidar cache das categorias e itens
      queryClient.invalidateQueries({ 
        queryKey: QueryKeys.marketplace.strategicCategories(company_id || '') 
      });
      queryClient.invalidateQueries({ 
        queryKey: QueryKeys.marketplace.strategicItems(updatedItem.category_id) 
      });

      toast({
        title: 'Item atualizado!',
        description: `Item "${updatedItem.name}" foi atualizado com sucesso.`,
      });
    },
    onError: (error) => {
      toast({
        title: 'Erro ao atualizar item',
        description: 'Não foi possível atualizar o item. Tente novamente.',
        variant: 'destructive',
      });
    },
  });
}

// ===== HOOKS PARA COMPRAS =====

/**
 * Hook para comprar item estratégico
 */
export function usePurchaseStrategicItem() {
  const queryClient = useQueryClient();
  const company_id = useAuthStore((state) => state.company_id);

  return useMutation({
    mutationFn: async ({ item_id, quantity = 1 }: PurchaseStrategicItemRequest): Promise<PurchaseStrategicItemResponse> => {
      logQueryEvent('usePurchaseStrategicItem', 'Comprando item estratégico', { item_id, quantity });

      const { data: authData } = await supabase.auth.getSession();
      const userId = authData.session?.user?.id;

      if (!userId) {
        throw new Error('Usuário não autenticado');
      }

      const { data, error } = await supabase.rpc('purchase_strategic_item', {
        p_user_id: userId,
        p_item_id: item_id,
        p_quantity: quantity
      });

      if (error) {
        logQueryEvent('usePurchaseStrategicItem', 'Erro ao comprar item', { error }, 'error');
        throw error;
      }

      logQueryEvent('usePurchaseStrategicItem', 'Item comprado', { result: data });
      return data;
    },
    onSuccess: (result, variables) => {
      if (result.success) {
        // Invalidar caches relevantes
        queryClient.invalidateQueries({ queryKey: QueryKeys.gamification.stardustBalance('current') });
        queryClient.invalidateQueries({ queryKey: QueryKeys.marketplace.strategicItems(variables.item_id) });
        queryClient.invalidateQueries({ queryKey: QueryKeys.marketplace.purchaseHistory() });

        toast({
          title: 'Compra realizada!',
          description: result.message,
        });
      } else {
        toast({
          title: 'Erro na compra',
          description: result.message,
          variant: 'destructive',
        });
      }
    },
    onError: (error) => {
      toast({
        title: 'Erro ao processar compra',
        description: 'Não foi possível processar a compra. Tente novamente.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook para buscar histórico de compras do usuário
 */
export function usePurchaseHistory(userId?: string, limit = 50, offset = 0) {
  return useQuery({
    queryKey: QueryKeys.marketplace.purchaseHistory(userId, limit, offset),
    queryFn: async (): Promise<StrategicPurchase[]> => {
      logQueryEvent('usePurchaseHistory', 'Buscando histórico de compras', { userId, limit, offset });

      const { data, error } = await supabase.rpc('get_user_strategic_purchases', {
        p_user_id: userId || null,
        p_limit: limit,
        p_offset: offset
      });

      if (error) {
        logQueryEvent('usePurchaseHistory', 'Erro ao buscar histórico', { error }, 'error');
        throw error;
      }

      logQueryEvent('usePurchaseHistory', 'Histórico carregado', { count: data?.length || 0 });
      return data || [];
    },
    staleTime: 2 * 60 * 1000, // 2 minutos
    gcTime: 5 * 60 * 1000, // 5 minutos
  });
}

// ===== HOOKS PARA OFERTAS ESPECIAIS =====

/**
 * Hook para buscar ofertas especiais ativas
 */
export function useActiveSpecialOffers(companyId?: string) {
  const currentCompanyId = useAuthStore((state) => state.company_id);
  const targetCompanyId = companyId || currentCompanyId;

  return useQuery({
    queryKey: QueryKeys.marketplace.specialOffers(targetCompanyId || ''),
    queryFn: async (): Promise<SpecialOffer[]> => {
      logQueryEvent('useActiveSpecialOffers', 'Buscando ofertas especiais ativas', { companyId: targetCompanyId });

      const { data, error } = await supabase.rpc('get_active_special_offers', {
        p_company_id: targetCompanyId
      });

      if (error) {
        logQueryEvent('useActiveSpecialOffers', 'Erro ao buscar ofertas', { error }, 'error');
        throw error;
      }

      logQueryEvent('useActiveSpecialOffers', 'Ofertas carregadas', { count: data?.length || 0 });
      return data || [];
    },
    enabled: !!targetCompanyId,
    staleTime: 1 * 60 * 1000, // 1 minuto (ofertas mudam rapidamente)
    gcTime: 3 * 60 * 1000, // 3 minutos
  });
}

// ===== HOOKS PARA ESTATÍSTICAS =====

/**
 * Hook para buscar estatísticas do marketplace (admin)
 */
export function useMarketplaceStats(companyId?: string) {
  const currentCompanyId = useAuthStore((state) => state.company_id);
  const targetCompanyId = companyId || currentCompanyId;

  return useQuery({
    queryKey: QueryKeys.marketplace.stats(targetCompanyId || ''),
    queryFn: async (): Promise<MarketplaceStats> => {
      logQueryEvent('useMarketplaceStats', 'Buscando estatísticas do marketplace', { companyId: targetCompanyId });

      const { data, error } = await supabase.rpc('get_marketplace_stats', {
        p_company_id: targetCompanyId
      });

      if (error) {
        logQueryEvent('useMarketplaceStats', 'Erro ao buscar estatísticas', { error }, 'error');
        throw error;
      }

      logQueryEvent('useMarketplaceStats', 'Estatísticas carregadas', { stats: data });
      return data;
    },
    enabled: !!targetCompanyId,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
  });
}