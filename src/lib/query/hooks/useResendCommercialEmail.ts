/**
 * Hook para reenviar email comercial
 * <AUTHOR> Internet 2025
 */

import { useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { successWithNotification, errorWithNotification } from '@/lib/notifications/toastWithNotification';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

export interface ResendEmailData {
  leadId: string;
}

export interface ResendEmailResult {
  success: boolean;
  message: string;
  commercial_email?: {
    recipient: string;
    subject: string;
    status: string;
  };
  user_email?: {
    recipient: string;
    subject: string;
    status: string;
  };
  error?: string;
}

export function useResendCommercialEmail() {
  return useMutation({
    mutationFn: async (data: ResendEmailData): Promise<ResendEmailResult> => {
      logQueryEvent('useResendCommercialEmail', 'Reenviando email comercial', { 
        leadId: data.leadId
      });

      // Chamar a Edge Function para reenviar o email
      const { data: result, error } = await supabase.functions.invoke('send-commercial-notification', {
        body: { 
          lead_id: data.leadId
        }
      });

      if (error) {
        logQueryEvent('useResendCommercialEmail', 'Erro na requisição', { error }, 'error');
        throw error;
      }

      if (!result || result.error) {
        const emailError = new Error(result?.error || 'Erro desconhecido no envio de email');
        logQueryEvent('useResendCommercialEmail', 'Erro no resultado', { result }, 'error');
        throw emailError;
      }

      logQueryEvent('useResendCommercialEmail', 'Email reenviado com sucesso', { 
        leadId: data.leadId,
        result
      });

      return result;
    },
    onSuccess: (result, variables) => {
      const commercialStatus = result.commercial_email?.status;
      const userStatus = result.user_email?.status;
      
      if (commercialStatus === 'sent' && userStatus === 'sent') {
        successWithNotification('Emails Reenviados!', {
          description: 'Emails comercial e de confirmação foram reenviados com sucesso.',
        });
      } else if (commercialStatus === 'sent') {
        successWithNotification('Email Comercial Reenviado!', {
          description: 'Email comercial foi reenviado. Email de confirmação falhou.',
        });
      } else {
        errorWithNotification('Erro no Reenvio', {
          description: 'Falha ao reenviar os emails. Verifique os logs.',
        });
      }
    },
    onError: (error: any) => {
      let title = 'Erro no Reenvio';
      let description = 'Não foi possível reenviar o email comercial.';

      // Tentar extrair mais detalhes do erro
      if (error.message?.includes('Lead not found')) {
        title = 'Lead Não Encontrado';
        description = 'O lead especificado não foi encontrado.';
      } else if (error.message?.includes('User email not found')) {
        title = 'Email do Usuário Não Encontrado';
        description = 'Não foi possível encontrar o email do usuário no lead.';
      } else if (error.message?.includes('MAILERSEND_API_KEY')) {
        title = 'Configuração de Email';
        description = 'Erro na configuração do serviço de email.';
      } else if (error.message) {
        description = error.message;
      }

      logQueryEvent('useResendCommercialEmail', 'Erro no reenvio', { error }, 'error');
      errorWithNotification(title, { description });
    }
  });
}