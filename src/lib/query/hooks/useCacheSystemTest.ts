/**
 * Hook de teste para o sistema de cache centralizado
 * <AUTHOR> Internet 2025
 * @description Hook para validar funcionamento do novo sistema
 */

import { useQuery } from '@tanstack/react-query';
import { useCacheService, useNetworkStatus } from '../queryClientCentralized';
import { supabase } from '@/integrations/supabase/client';

/**
 * Hook de teste para verificar sistema centralizado
 */
export function useCacheSystemTest() {
  const cacheService = useCacheService();
  const { isOnline, isOffline } = useNetworkStatus();

  // Query de teste usando configuração padrão do sistema
  const testQuery = useQuery({
    queryKey: ['cache-system-test', 'basic'],
    queryFn: async () => {
      console.log('[CacheTest] Executando query de teste...');
      
      // Simular chamada para API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return {
        timestamp: new Date().toISOString(),
        online: navigator.onLine,
        cacheService: !!cacheService,
        message: 'Sistema de cache centralizado funcionando! ✅'
      };
    },
    // Usar configuração padrão do sistema - não especificar staleTime etc
  });

  // Query de teste para usuário atual
  const userTestQuery = useQuery({
    queryKey: ['cache-system-test', 'user'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { error: 'Usuário não autenticado' };
      }

      return {
        userId: user.id,
        email: user.email,
        cacheKey: `VINDULA_CACHE_CENTRALIZED_${user.id}`,
        message: 'Cache por usuário funcionando! ✅'
      };
    },
    enabled: !!supabase.auth.getUser,
  });

  // Funções de teste do cache service
  const testCacheOperations = () => {
    try {
      console.log('[CacheTest] Testando operações do cache service...');
      
      // Testar invalidação
      cacheService.invalidateQuery(['cache-system-test']);
      
      // Testar warm cache (se disponível)
      if ('warmCache' in cacheService) {
        console.log('[CacheTest] Cache warming disponível ✅');
      }

      // Testar event bus (se disponível)
      if (cacheService.eventBus) {
        cacheService.eventBus.emit('cache.test', {
          timestamp: new Date().toISOString(),
          source: 'useCacheSystemTest'
        });
        console.log('[CacheTest] Event bus funcionando ✅');
      }

      return true;
    } catch (error) {
      console.error('[CacheTest] Erro nas operações:', error);
      return false;
    }
  };

  // Informações de debug
  const getDebugInfo = () => ({
    networkStatus: { isOnline, isOffline },
    testQuery: {
      status: testQuery.status,
      data: testQuery.data,
      error: testQuery.error
    },
    userTestQuery: {
      status: userTestQuery.status,
      data: userTestQuery.data,  
      error: userTestQuery.error
    },
    cacheService: {
      available: !!cacheService,
      hasEventBus: !!(cacheService as any)?.eventBus,
      type: cacheService.constructor.name
    },
    localStorage: {
      keys: Object.keys(localStorage).filter(key => 
        key.includes('VINDULA_CACHE') || key.includes('cache')
      ).length,
      cacheKeys: Object.keys(localStorage).filter(key => 
        key.startsWith('VINDULA_CACHE_CENTRALIZED_')
      )
    }
  });

  return {
    // Queries de teste
    testQuery,
    userTestQuery,
    
    // Status do sistema
    isOnline,
    isOffline,
    cacheService,
    
    // Funções de teste
    testCacheOperations,
    getDebugInfo,
    
    // Utilitários
    refetchTests: () => {
      testQuery.refetch();
      userTestQuery.refetch();
    },
    
    // Status geral
    isSystemReady: !!cacheService && (testQuery.isSuccess || testQuery.isLoading),
    
    // Mensagem de status
    getStatusMessage: () => {
      if (!cacheService) return '❌ Cache Service não disponível';
      if (isOffline) return '📱 Modo offline - cache funcionando';
      if (testQuery.isLoading) return '⏳ Testando sistema...';
      if (testQuery.isSuccess) return '✅ Sistema funcionando perfeitamente!';
      if (testQuery.isError) return '⚠️ Erro no teste básico';
      return '🔄 Iniciializando...';
    }
  };
}

/**
 * Hook simplificado para verificar se sistema está funcionando
 */
export function useCacheSystemStatus() {
  const { isSystemReady, getStatusMessage, isOnline } = useCacheSystemTest();
  
  return {
    isReady: isSystemReady,
    message: getStatusMessage(),
    isOnline
  };
}

/**
 * Hook para debug do sistema em desenvolvimento
 */
export function useCacheSystemDebug() {
  const test = useCacheSystemTest();
  
  // Só funcionar em desenvolvimento
  if (import.meta.env.PROD) {
    return { debug: () => console.log('Debug só disponível em desenvolvimento') };
  }

  const debug = () => {
    const info = test.getDebugInfo();
    
    console.group('🔍 DEBUG: Sistema de Cache Centralizado');
    console.log('📊 Status Geral:', test.getStatusMessage());
    console.log('🌐 Network:', info.networkStatus);
    console.log('🧪 Test Query:', info.testQuery);
    console.log('👤 User Query:', info.userTestQuery);
    console.log('⚙️ Cache Service:', info.cacheService);
    console.log('💾 LocalStorage:', info.localStorage);
    console.groupEnd();
    
    // Testar operações
    const operationsOk = test.testCacheOperations();
    console.log(operationsOk ? '✅ Operações OK' : '❌ Erro nas operações');
  };

  const testOffline = async () => {
    console.log('🧪 Simulando modo offline...');
    
    // Forçar modo offline temporariamente
    const originalOnLine = navigator.onLine;
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: false,
    });
    
    // Refetch para testar comportamento offline
    await test.refetchTests();
    
    setTimeout(() => {
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: originalOnLine,
      });
      console.log('🌐 Modo online restaurado');
    }, 3000);
  };

  return {
    debug,
    testOffline,
    ...test
  };
}