/**
 * Hook para gerenciar read receipts de mensagens (Issue #13)
 * Permite visualizar quem leu cada mensagem nos canais/chats
 * <AUTHOR> Internet 2025
 */
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { QueryKeys } from '@/lib/query/queryKeys';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { toastWithNotification } from '@/lib/notifications/toastWithNotification';

/**
 * Helper para obter channelId de uma mensagem para invalidação específica
 */
async function getChannelIdFromMessage(messageId: string): Promise<string | null> {
  try {
    const { data, error } = await supabase
      .rpc('get_channel_id_from_message', { p_message_id: messageId });
    
    if (error) {
      logQueryEvent("getChannelIdFromMessage", "Erro ao buscar channelId", { error, messageId }, "error");
      return null;
    }
    
    return data;
  } catch (error) {
    logQueryEvent("getChannelIdFromMessage", "Erro na helper function", { error, messageId }, "error");
    return null;
  }
}

/**
 * Helper para invalidação específica por canal ao invés de global
 */
async function invalidateChannelSpecificQueries(queryClient: any, messageId: string) {
  const channelId = await getChannelIdFromMessage(messageId);
  
  if (channelId) {
    // ✅ CORREÇÃO: Invalidação específica por canal
    logQueryEvent("invalidateChannelSpecificQueries", "Invalidando cache específico", { 
      messageId, 
      channelId,
      queryKey: QueryKeys.chat.unreadCount(channelId)
    });
    
    queryClient.invalidateQueries({
      queryKey: QueryKeys.chat.unreadCount(channelId)
    });
  } else {
    // Fallback: invalidação ampla apenas se não conseguir determinar o canal
    logQueryEvent("invalidateChannelSpecificQueries", "Fallback: invalidação ampla", { messageId }, "warning");
    
    queryClient.invalidateQueries({
      queryKey: ['chat', 'unread-count']
    });
  }
  
  // Invalidar contador global (necessário para badge geral)
  queryClient.invalidateQueries({
    queryKey: ['unread-chat-count']
  });
}

export interface MessageReadReceipt {
  user_id: string;
  user_name: string;
  user_avatar_url: string | null;
  read_at: string;
}

interface MarkMessageAsReadResponse {
  success: boolean;
  message_id?: string;
  user_id?: string;
  read_at?: string;
  error_code?: string;
  message?: string;
}

interface MarkMessagesAsReadResponse {
  success: boolean;
  processed_count: number;
  total_requested: number;
  errors: string[];
}

/**
 * Hook para buscar read receipts de uma mensagem específica
 */
export function useMessageReadReceipts(messageId: string, enabled = true) {
  const queryClient = useQueryClient();

  // Verificar se a mensagem ainda existe no cache
  const messageExists = queryClient.getQueriesData({
    queryKey: ['chat', 'messages']
  }).some(([_, data]: [any, any]) => {
    if (!data) return false;
    
    // Verificar em arrays diretos
    if (Array.isArray(data)) {
      return data.some((msg: any) => msg.id === messageId);
    }
    
    // Verificar em objetos com propriedade messages
    if (data.messages && Array.isArray(data.messages)) {
      return data.messages.some((msg: any) => msg.id === messageId);
    }
    
    // Verificar em dados paginados
    if (data.pages && Array.isArray(data.pages)) {
      return data.pages.some((page: any) => 
        page.messages && Array.isArray(page.messages) && 
        page.messages.some((msg: any) => msg.id === messageId)
      );
    }
    
    return false;
  });

  return useQuery({
    queryKey: QueryKeys.chat.readReceipts(messageId),
    queryFn: async (): Promise<MessageReadReceipt[]> => {
      if (!messageId) {
        throw new Error('messageId é obrigatório');
      }

      try {
        const { data, error } = await supabase
          .rpc('get_message_read_receipts_v1', { p_message_id: messageId });

        if (error) {
          // Se o erro for que a mensagem não existe, retornar array vazio em vez de erro
          if (error.message?.includes('No access to this message') || 
              error.message?.includes('Message not found')) {
            return [];
          }
          logQueryEvent("useMessageReadReceipts", "Erro ao buscar read receipts", { error, messageId }, "error");
          throw error;
        }

        return data || [];
      } catch (error) {
        logQueryEvent("useMessageReadReceipts", "Erro no hook", { error, messageId }, "error");
        throw error;
      }
    },
    enabled: Boolean(enabled && messageId && messageExists),
    staleTime: 1000 * 30, // 30 segundos - read receipts podem mudar rapidamente
    refetchInterval: messageExists ? 1000 * 60 : false, // Só refetch se mensagem existe
    retry: (failureCount, error: any) => {
      // Não retry se a mensagem não existe
      if (error?.message?.includes('No access to this message') || 
          error?.message?.includes('Message not found')) {
        return false;
      }
      return failureCount < 3;
    }
  });
}

/**
 * Hook para marcar uma mensagem como lida
 */
export function useMarkMessageAsRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (messageId: string): Promise<MarkMessageAsReadResponse> => {

      const { data, error } = await supabase
        .rpc('mark_message_as_read_v1', { p_message_id: messageId });

      if (error) {
        logQueryEvent("useMarkMessageAsRead", "Erro ao marcar como lida", { error, messageId }, "error");
        throw error;
      }

      return data;
    },
    onSuccess: (data, messageId) => {
      if (data.success) {
        logQueryEvent("useMarkMessageAsRead", "Mensagem marcada como lida", { messageId, data });
        
        // Invalidar read receipts da mensagem
        queryClient.invalidateQueries({
          queryKey: QueryKeys.chat.readReceipts(messageId)
        });

        // ✅ CORREÇÃO: Invalidação específica por canal (não pode ser await em onSuccess)
        invalidateChannelSpecificQueries(queryClient, messageId);
      } else {
        logQueryEvent("useMarkMessageAsRead", "Falha ao marcar como lida", { messageId, data }, "error");
        toastWithNotification(
          'Erro ao marcar mensagem como lida',
          data.message || 'Erro desconhecido',
          'error'
        );
      }
    },
    onError: (error, messageId) => {
      logQueryEvent("useMarkMessageAsRead", "Erro na mutation", { error, messageId }, "error");
      toastWithNotification(
        'Erro ao marcar mensagem como lida',
        'Tente novamente em alguns instantes',
        'error'
      );
    }
  });
}

/**
 * Hook para marcar múltiplas mensagens como lidas (batch operation)
 */
export function useMarkMessagesAsRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (messageIds: string[]): Promise<MarkMessagesAsReadResponse> => {

      const { data, error } = await supabase
        .rpc('mark_messages_as_read_v1', { p_message_ids: messageIds });

      if (error) {
        logQueryEvent("useMarkMessagesAsRead", "Erro no batch read", { error, messageIds }, "error");
        throw error;
      }

      return data;
    },
    onSuccess: (data, messageIds) => {
      if (data.success) {
        logQueryEvent("useMarkMessagesAsRead", "Mensagens marcadas como lidas (batch)", { 
          messageIds, 
          processedCount: data.processed_count,
          totalRequested: data.total_requested,
          errors: data.errors
        });
        
        // Invalidar read receipts de todas as mensagens processadas
        messageIds.forEach(messageId => {
          queryClient.invalidateQueries({
            queryKey: QueryKeys.chat.readReceipts(messageId)
          });
          
          // ✅ CORREÇÃO: Invalidação específica por canal para cada mensagem
          invalidateChannelSpecificQueries(queryClient, messageId);
        });

        if (data.errors.length > 0) {
          toastWithNotification(
            'Parcialmente processado',
            `${data.processed_count}/${data.total_requested} mensagens marcadas como lidas`,
            'warning'
          );
        }
      } else {
        logQueryEvent("useMarkMessagesAsRead", "Falha no batch read", { messageIds, data }, "error");
        toastWithNotification(
          'Erro ao marcar mensagens como lidas',
          'Tente novamente em alguns instantes',
          'error'
        );
      }
    },
    onError: (error, messageIds) => {
      logQueryEvent("useMarkMessagesAsRead", "Erro na batch mutation", { error, messageIds }, "error");
      toastWithNotification(
        'Erro ao marcar mensagens como lidas',
        'Tente novamente em alguns instantes',
        'error'
      );
    }
  });
}

/**
 * Hook para buscar contagem de mensagens não lidas de um canal
 */
export function useUnreadMessagesCount(channelId: string, enabled = true) {
  // logQueryEvent("useUnreadMessagesCount", "Inicializando hook", { channelId, enabled });

  return useQuery({
    queryKey: QueryKeys.chat.unreadCount(channelId),
    queryFn: async (): Promise<number> => {
      if (!channelId) {
        throw new Error('channelId é obrigatório');
      }

      try {
        logQueryEvent("useUnreadMessagesCount", "Buscando contagem de não lidas", { channelId });

        const { data, error } = await supabase
          .rpc('get_unread_messages_count_v3', { p_channel_id: channelId });

        if (error) {
          logQueryEvent("useUnreadMessagesCount", "Erro ao buscar contagem", { error, channelId }, "error");
          throw error;
        }

        const count = data || 0;
        
        logQueryEvent("useUnreadMessagesCount", "Contagem carregada", { channelId, count });

        return count;
      } catch (error) {
        logQueryEvent("useUnreadMessagesCount", "Erro no hook", { error, channelId }, "error");
        throw error;
      }
    },
    enabled: Boolean(enabled && channelId),
    staleTime: 1000 * 30, // 30 segundos
    refetchInterval: 1000 * 60, // Revalidar a cada minuto
  });
}

/**
 * Hook para verificar se o usuário atual leu uma mensagem específica
 */
export function useCurrentUserReadStatus(messageId: string, userId: string, enabled = true) {
  // logQueryEvent("useCurrentUserReadStatus", "Inicializando hook", { messageId, userId, enabled });

  return useQuery({
    queryKey: QueryKeys.chat.messageReadStatus(messageId, userId),
    queryFn: async (): Promise<boolean> => {
      if (!messageId || !userId) {
        throw new Error('messageId e userId são obrigatórios');
      }

      try {
        logQueryEvent("useCurrentUserReadStatus", "Verificando status de leitura", { messageId, userId });

        const { data, error } = await supabase
          .from('message_read_receipts')
          .select('id')
          .eq('message_id', messageId)
          .eq('user_id', userId)
          .maybeSingle();

        if (error) {
          logQueryEvent("useCurrentUserReadStatus", "Erro ao verificar status", { error, messageId, userId }, "error");
          throw error;
        }

        const hasRead = Boolean(data);
        
        logQueryEvent("useCurrentUserReadStatus", "Status verificado", { messageId, userId, hasRead });

        return hasRead;
      } catch (error) {
        logQueryEvent("useCurrentUserReadStatus", "Erro no hook", { error, messageId, userId }, "error");
        throw error;
      }
    },
    enabled: Boolean(enabled && messageId && userId),
    staleTime: 1000 * 30, // 30 segundos
  });
}

/**
 * Hook para configurar subscriptions em tempo real para read receipts
 * 
 * ⚠️ DEPRECIADO: Agora é processado automaticamente pelo UnifiedRealtimeProvider
 * Este hook permanece apenas para compatibilidade e pode ser removido no futuro.
 */
export function useMessageReadReceiptsRealtime(messageIds: string[]) {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (messageIds.length === 0) return;

    logQueryEvent("useMessageReadReceiptsRealtime", "🚀 REFATORADO: UnifiedRealtimeProvider processa automaticamente", { 
      messageIds, 
      count: messageIds.length,
      note: "Subscription individual não é mais necessária" 
    });

    // 🚀 REFATORADO: Os eventos de message_read_receipts agora são processados 
    // automaticamente pelo UnifiedRealtimeProvider, então não precisamos mais
    // de subscription individual aqui.
    
    // Escutar eventos do UnifiedRealtimeProvider como fallback
    const handleReadReceiptEvent = (event: CustomEvent) => {
      const { receipt } = event.detail;
      
      logQueryEvent("useMessageReadReceiptsRealtime", "Evento via UnifiedRealtimeProvider", {
        messageId: receipt.message_id,
        userId: receipt.user_id,
        eventType: event.detail.eventType
      });
      
      // Cache já foi invalidado pelo UnifiedRealtimeProvider, 
      // então não precisamos fazer nada aqui
    };

    window.addEventListener('vindula-message-read-receipt-changed', handleReadReceiptEvent as EventListener);

    return () => {
      window.removeEventListener('vindula-message-read-receipt-changed', handleReadReceiptEvent as EventListener);
    };
  }, [messageIds.join(','), queryClient]);
}