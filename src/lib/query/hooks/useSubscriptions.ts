import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { QueryKeys } from "../queryKeys";

type SubscriptionPlan = {
  id: string;
  name: string;
  description: string | null;
  storage_limit: number;
  user_limit: number;
  price: number;
  interval: string;
  stripe_price_id: string | null;
  features: Record<string, unknown>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
};

type Subscription = {
  id: string;
  company_id: string | null;
  stripe_subscription_id: string | null;
  stripe_customer_id: string | null;
  price_id: string;
  status: string;
  user_limit: number;
  current_period_start: string | null;
  current_period_end: string | null;
  created_at: string;
  updated_at: string;
  plan_id: string | null;
  storage_limit: number | null;
  features_override: Record<string, unknown> | null;
  subscription_plans?: SubscriptionPlan;
};

type UserAddon = {
  id: string;
  type: 'user_pack' | 'storage_pack' | 'ai_credits';
  name: string;
  description: string | null;
  capacity: number; // Para user_pack: número de usuá<PERSON>s, para storage_pack: MB de armazenamento, para ai_credits: quantidade de créditos
  price: number;
  stripe_price_id: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  // Campos específicos para AI Credits
  metadata?: {
    ai_feature_keys?: string[]; // Features de IA que este pacote pode ser usado
    expires_in_days?: number; // Validade dos créditos em dias
  };
};

type UserAddonPurchase = {
  id: string;
  company_id: string;
  addon_id: string;
  quantity: number;
  purchase_date: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  total_price: number;
  stripe_payment_intent_id: string | null;
  created_at: string;
  updated_at: string;
  user_addons?: UserAddon;
};

type SubscriptionLimits = {
  company_id: string;
  plan: {
    name: string;
    price: number;
  };
  limits: {
    users: {
      base: number;
      addons: number;
      total: number;
    };
    storage: {
      base: number;
      addons: number;
      total: number;
    };
  };
};

/**
 * Hook para buscar dados da assinatura atual da empresa
 * <AUTHOR> Internet 2025
 */
export function useCurrentSubscription() {
  return useQuery({
    queryKey: QueryKeys.subscription.current(),
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Usuário não encontrado");

      const { data: profile } = await supabase
        .from("profiles")
        .select("company_id")
        .eq("id", user.id)
        .single();

      if (!profile?.company_id) throw new Error("Empresa não encontrada");

      // Buscar dados da assinatura
      const { data: subscription } = await supabase
        .from("subscriptions")
        .select(`
          *,
          subscription_plans!subscriptions_plan_id_fkey(*)
        `)
        .eq("company_id", profile.company_id)
        .single();

      return subscription as Subscription;
    },
  });
}

/**
 * Hook para buscar todos os planos de assinatura disponíveis
 * <AUTHOR> Internet 2025
 */
export function useAvailablePlans() {
  return useQuery({
    queryKey: QueryKeys.subscription.available(),
    queryFn: async () => {
      const { data: plans } = await supabase
        .from("subscription_plans")
        .select("*")
        .eq("is_active", true)
        .order("price");

      return plans as SubscriptionPlan[] || [];
    },
  });
}

/**
 * Alias para useAvailablePlans - mantido para compatibilidade
 * <AUTHOR> Internet 2025
 */
export const useSubscriptionPlans = useAvailablePlans;

/**
 * Hook para buscar add-ons disponíveis (user packs e storage packs)
 * <AUTHOR> Internet 2025
 */
export function useAvailableAddons() {
  return useQuery({
    queryKey: QueryKeys.subscription.addons(),
    queryFn: async () => {
      const { data: addons } = await supabase
        .from("user_addons")
        .select("*")
        .eq("is_active", true)
        .order("type, price");

      return addons as UserAddon[] || [];
    },
  });
}

/**
 * Alias para useAvailableAddons - mantido para compatibilidade
 * <AUTHOR> Internet 2025
 */
export const useUserAddons = useAvailableAddons;

/**
 * Hook para buscar compras de add-ons da empresa
 * <AUTHOR> Internet 2025
 */
export function useCompanyAddonPurchases() {
  return useQuery({
    queryKey: QueryKeys.subscription.addonPurchases(),
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Usuário não encontrado");

      const { data: profile } = await supabase
        .from("profiles")
        .select("company_id")
        .eq("id", user.id)
        .single();

      if (!profile?.company_id) throw new Error("Empresa não encontrada");

      const { data: purchases } = await supabase
        .from("user_addon_purchases")
        .select(`
          *,
          user_addons(*)
        `)
        .eq("company_id", profile.company_id)
        .eq("status", "completed")
        .order("purchase_date", { ascending: false });

      return purchases as UserAddonPurchase[] || [];
    },
  });
}

/**
 * Hook para buscar limites totais da assinatura (base + add-ons)
 * Usa função RPC para cálculos otimizados no servidor
 * <AUTHOR> Internet 2025
 */
export function useSubscriptionLimits() {
  return useQuery({
    queryKey: QueryKeys.subscription.limits(),
    queryFn: async () => {
      const { data: limits, error } = await supabase.rpc('get_subscription_limits_with_addons');
      
      if (error) {
        console.error('Erro ao buscar limites de assinatura:', error);
        throw error;
      }
      
      return limits as SubscriptionLimits;
    },
  });
}

/**
 * Alias para useSubscriptionLimits - mantido para compatibilidade
 * <AUTHOR> Internet 2025
 */
export const useCompanyLimits = useSubscriptionLimits;

/**
 * Hook para verificar se upgrade é obrigatório
 * <AUTHOR> Internet 2025
 */
export function useUpgradeRequirements() {
  return useQuery({
    queryKey: QueryKeys.subscription.upgradeRequirements(),
    queryFn: async () => {
      const { data: requirements, error } = await supabase.rpc('check_mandatory_upgrade_requirements');
      
      if (error) throw error;
      return requirements;
    },
  });
}

/**
 * Hook para atualizar a assinatura da empresa (mudar de plano)
 * CORRIGE ISSUE #322: Agora cria entrada automática no histórico de cobrança
 * <AUTHOR> Internet 2025
 */
export function useUpdateSubscription() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ planId }: { planId: string }) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Usuário não encontrado");

      const { data: profile } = await supabase
        .from("profiles")
        .select("company_id")
        .eq("id", user.id)
        .single();

      if (!profile?.company_id) throw new Error("Empresa não encontrada");

      // Buscar dados do plano selecionado
      const { data: plan } = await supabase
        .from("subscription_plans")
        .select("*")
        .eq("id", planId)
        .single();
        
      if (!plan) throw new Error("Plano não encontrado");

      // Buscar subscription atual para obter o ID
      const { data: currentSubscription } = await supabase
        .from("subscriptions")
        .select("id")
        .eq("company_id", profile.company_id)
        .single();

      if (!currentSubscription) throw new Error("Assinatura atual não encontrada");

      // Atualizar a assinatura com os dados do plano
      const { data, error } = await supabase
        .from("subscriptions")
        .update({ 
          plan_id: planId,
          storage_limit: (plan as SubscriptionPlan).storage_limit,
          user_limit: (plan as SubscriptionPlan).user_limit,
          status: "active", // Em um cenário real, isso dependeria da integração de pagamento
        })
        .eq("company_id", profile.company_id)
        .select();

      if (error) throw error;

      // CORREÇÃO ISSUE #322: Criar entrada no histórico de cobrança após upgrade
      try {
        const currentPeriodStart = new Date();
        currentPeriodStart.setDate(1); // Primeiro dia do mês atual
        currentPeriodStart.setHours(0, 0, 0, 0);
        
        const currentPeriodEnd = new Date(currentPeriodStart);
        currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1);
        currentPeriodEnd.setTime(currentPeriodEnd.getTime() - 1); // Último momento do mês

        const { data: billingResult, error: billingError } = await supabase.rpc(
          'create_billing_entry_v1',
          {
            p_company_id: profile.company_id,
            p_subscription_id: currentSubscription.id,
            p_plan_id: planId,
            p_billing_period_start: currentPeriodStart.toISOString(),
            p_billing_period_end: currentPeriodEnd.toISOString()
          }
        );

        if (billingError) {
          console.warn('Aviso: Não foi possível criar entrada no histórico de cobrança:', billingError);
          // Não falhar a operação principal por causa do histórico
        } else if (billingResult?.[0] && !billingResult[0].success) {
          console.warn('Aviso: Falha ao criar entrada no histórico:', billingResult[0].error_message);
        }
      } catch (billingError) {
        console.warn('Aviso: Erro inesperado ao criar histórico de cobrança:', billingError);
        // Não falhar a operação principal
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.subscription.current() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.subscription.limits() });
      // Invalidar queries do histórico de cobrança também
      queryClient.invalidateQueries({ queryKey: ['billing'] });
    },
  });
}

/**
 * Hook para processar compra de add-ons (cria registro pendente)
 * <AUTHOR> Internet 2025
 */
export function usePurchaseAddon() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ addonId, quantity = 1 }: { addonId: string; quantity?: number }) => {
      const { data, error } = await supabase.rpc('process_addon_purchase', {
        p_addon_id: addonId,
        p_quantity: quantity
      });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.subscription.addonPurchases() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.subscription.limits() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.subscription.purchaseHistory() });
    },
  });
}

/**
 * Hook para confirmar compra de add-on (simula pagamento aprovado)
 * <AUTHOR> Internet 2025
 */
export function useConfirmAddonPurchase() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ purchaseId, paymentIntentId }: { purchaseId: string; paymentIntentId?: string }) => {
      const { data, error } = await supabase.rpc('confirm_addon_purchase', {
        p_purchase_id: purchaseId,
        p_payment_intent_id: paymentIntentId
      });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.subscription.addonPurchases() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.subscription.limits() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.subscription.purchaseHistory() });
    },
  });
}

/**
 * Hook para cancelar compra de add-on pendente
 * <AUTHOR> Internet 2025
 */
export function useCancelAddonPurchase() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ purchaseId }: { purchaseId: string }) => {
      const { data, error } = await supabase.rpc('cancel_addon_purchase', {
        p_purchase_id: purchaseId
      });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.subscription.addonPurchases() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.subscription.purchaseHistory() });
    },
  });
}

/**
 * Hook para buscar compras pendentes de add-ons
 * <AUTHOR> Internet 2025
 */
export function usePendingAddonPurchases() {
  return useQuery({
    queryKey: QueryKeys.subscription.pendingPurchases(),
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_pending_addon_purchases');
      
      if (error) throw error;
      return data || [];
    },
  });
}

/**
 * Hook para buscar histórico completo de compras de add-ons
 * <AUTHOR> Internet 2025
 */
export function useAddonPurchaseHistory() {
  return useQuery({
    queryKey: QueryKeys.subscription.purchaseHistory(),
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_addon_purchase_history');
      
      if (error) throw error;
      return data || [];
    },
  });
}

// Exportar tipos para uso em outros componentes
export type { SubscriptionPlan, UserAddon, Subscription, UserAddonPurchase, SubscriptionLimits }; 