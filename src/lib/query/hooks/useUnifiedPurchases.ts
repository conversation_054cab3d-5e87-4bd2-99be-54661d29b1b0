/**
 * Hook Unificado para Todas as Compras Administrativas
 * Consolida compras de ofertas especiais e marketplace estratégico
 * <AUTHOR> Internet 2025
 */
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuthStore } from '@/stores/authStore';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

export type PurchaseType = 'special_offer' | 'strategic_item' | 'stardust_store';

export interface UnifiedPurchase {
  id: string;
  user_id: string;
  company_id: string;
  type: PurchaseType;
  
  // Dados do usuário
  user_name: string;
  user_email: string;
  
  // Dados do item/oferta
  title: string;
  description?: string;
  category?: string;
  
  // Dados financeiros
  quantity: number;
  unit_price: number;
  total_cost: number;
  discount_percent?: number;
  original_price?: number;
  
  // Status e timing
  status: 'pending' | 'confirmed' | 'delivered' | 'cancelled' | 'completed';
  fulfillment_status?: string;
  purchased_at: string;
  confirmed_at?: string;
  delivered_at?: string;
  
  // Notas administrativas
  admin_notes?: string;
  fulfillment_notes?: string;
  
  // Metadados específicos do tipo
  metadata: {
    level_required?: number;
    is_popular?: boolean;
    is_hot?: boolean;
    items_purchased?: any[];
    gradient?: string;
  };
}

export interface PurchaseStats {
  total: number;
  pending: number;
  confirmed: number;
  delivered: number;
  cancelled: number;
  completed: number;
  totalRevenue: number;
  byType: {
    special_offer: number;
    strategic_item: number;
    stardust_store: number;
  };
}

/**
 * Hook para buscar compras unificadas do usuário atual
 */
export function useUnifiedUserPurchases() {
  const company_id = useAuthStore((state) => state.company_id);
  const user_id = useAuthStore((state) => state.user?.id);

  return useQuery({
    queryKey: ['user-purchases', 'unified', user_id, company_id],
    queryFn: async (): Promise<{ purchases: UnifiedPurchase[]; stats: PurchaseStats }> => {
      if (!company_id || !user_id) {
        throw new Error('Company ID ou User ID não encontrado');
      }

      logQueryEvent('useUnifiedUserPurchases', 'Buscando compras unificadas do usuário');

      // Buscar ofertas especiais do usuário
      const { data: specialOffers, error: specialError } = await supabase
        .from('special_offer_purchases')
        .select(`
          *,
          special_offers!inner(
            title,
            description,
            discount_percent
          ),
          profiles!inner(
            full_name,
            email
          )
        `)
        .eq('user_id', user_id)
        .order('purchased_at', { ascending: false });

      if (specialError) {
        logQueryEvent('useUnifiedUserPurchases', 'Erro ao buscar ofertas especiais', { error: specialError }, 'error');
      }

      // Buscar compras estratégicas do usuário
      const { data: strategicPurchases, error: strategicError } = await supabase
        .from('strategic_purchases')
        .select(`
          *,
          strategic_items!inner(
            name,
            description,
            level_required,
            is_popular,
            is_hot,
            strategic_categories(
              name,
              gradient
            )
          ),
          profiles!inner(
            full_name,
            email
          )
        `)
        .eq('user_id', user_id)
        .order('purchased_at', { ascending: false });

      if (strategicError) {
        logQueryEvent('useUnifiedUserPurchases', 'Erro ao buscar compras estratégicas', { error: strategicError }, 'error');
      }

      // Consolidar dados
      const unifiedPurchases: UnifiedPurchase[] = [];

      // Processar ofertas especiais
      if (specialOffers) {
        specialOffers.forEach((purchase: any) => {
          unifiedPurchases.push({
            id: purchase.id,
            user_id: purchase.user_id,
            company_id: purchase.company_id,
            type: 'special_offer',
            user_name: purchase.profiles.full_name,
            user_email: purchase.profiles.email,
            title: purchase.special_offers.title,
            description: purchase.special_offers.description,
            quantity: purchase.total_items,
            unit_price: purchase.total_discounted_price,
            total_cost: purchase.total_discounted_price,
            discount_percent: purchase.special_offers.discount_percent,
            original_price: purchase.total_original_price,
            status: purchase.status,
            purchased_at: purchase.purchased_at,
            confirmed_at: purchase.confirmed_at,
            delivered_at: purchase.delivered_at,
            admin_notes: purchase.admin_notes,
            metadata: {
              items_purchased: purchase.items_purchased
            }
          });
        });
      }

      // Função para normalizar status entre diferentes tipos de compra
      const normalizeStatus = (status: string, type: 'special_offer' | 'strategic_item'): string => {
        // Para compras estratégicas, mapear 'completed' para 'delivered' para consistência
        if (type === 'strategic_item' && status === 'completed') {
          logQueryEvent('useUnifiedUserPurchases', `Normalizando status: ${status} -> delivered`, { originalStatus: status }, 'debug');
          return 'delivered';
        }
        return status;
      };

      // Processar compras estratégicas
      if (strategicPurchases) {
        strategicPurchases.forEach((purchase: any) => {
          const normalizedStatus = normalizeStatus(purchase.status, 'strategic_item');
          
          unifiedPurchases.push({
            id: purchase.id,
            user_id: purchase.user_id,
            company_id: purchase.company_id,
            type: 'strategic_item',
            user_name: purchase.profiles.full_name,
            user_email: purchase.profiles.email,
            title: purchase.strategic_items.name,
            description: purchase.strategic_items.description,
            category: purchase.strategic_items.strategic_categories?.name,
            quantity: purchase.quantity,
            unit_price: purchase.unit_price,
            total_cost: purchase.total_cost,
            status: normalizedStatus,
            fulfillment_status: purchase.fulfillment_status,
            purchased_at: purchase.purchased_at,
            delivered_at: purchase.fulfilled_at,
            admin_notes: purchase.fulfillment_notes,
            metadata: {
              level_required: purchase.strategic_items.level_required,
              is_popular: purchase.strategic_items.is_popular,
              is_hot: purchase.strategic_items.is_hot,
              gradient: purchase.strategic_items.strategic_categories?.gradient
            }
          });
        });
      }

      // Ordenar por data mais recente
      unifiedPurchases.sort((a, b) => 
        new Date(b.purchased_at).getTime() - new Date(a.purchased_at).getTime()
      );

      // Calcular estatísticas
      const stats: PurchaseStats = {
        total: unifiedPurchases.length,
        pending: unifiedPurchases.filter(p => p.status === 'pending').length,
        confirmed: unifiedPurchases.filter(p => p.status === 'confirmed').length,
        delivered: unifiedPurchases.filter(p => p.status === 'delivered').length,
        cancelled: unifiedPurchases.filter(p => p.status === 'cancelled').length,
        completed: unifiedPurchases.filter(p => p.status === 'completed').length,
        totalRevenue: unifiedPurchases
          .filter(p => !['cancelled'].includes(p.status))
          .reduce((sum, p) => sum + p.total_cost, 0),
        byType: {
          special_offer: unifiedPurchases.filter(p => p.type === 'special_offer').length,
          strategic_item: unifiedPurchases.filter(p => p.type === 'strategic_item').length,
          stardust_store: unifiedPurchases.filter(p => p.type === 'stardust_store').length,
        }
      };

      logQueryEvent('useUnifiedUserPurchases', 'Compras unificadas do usuário carregadas', { 
        total: unifiedPurchases.length,
        specialOffers: specialOffers?.length || 0,
        strategicPurchases: strategicPurchases?.length || 0
      });

      return { purchases: unifiedPurchases, stats };
    },
    enabled: !!company_id && !!user_id,
    staleTime: 3 * 60 * 1000, // 3 minutos
    retry: 2,
  });
}

/**
 * Hook para buscar todas as compras unificadas da empresa
 */
export function useUnifiedAdminPurchases() {
  const company_id = useAuthStore((state) => state.company_id);

  return useQuery({
    queryKey: ['admin-purchases', 'unified', company_id],
    queryFn: async (): Promise<{ purchases: UnifiedPurchase[]; stats: PurchaseStats }> => {
      if (!company_id) {
        throw new Error('Company ID não encontrado');
      }

      logQueryEvent('useUnifiedAdminPurchases', 'Buscando todas as compras unificadas da empresa');

      // Buscar ofertas especiais
      const { data: specialOffers, error: specialError } = await supabase
        .from('special_offer_purchases')
        .select(`
          *,
          special_offers!inner(
            title,
            description,
            discount_percent
          ),
          profiles!inner(
            full_name,
            email
          )
        `)
        .eq('company_id', company_id)
        .order('purchased_at', { ascending: false });

      if (specialError) {
        logQueryEvent('useUnifiedAdminPurchases', 'Erro ao buscar ofertas especiais', { error: specialError }, 'error');
      }

      // Buscar compras estratégicas
      const { data: strategicPurchases, error: strategicError } = await supabase
        .from('strategic_purchases')
        .select(`
          *,
          strategic_items!inner(
            name,
            description,
            level_required,
            is_popular,
            is_hot,
            strategic_categories(
              name,
              gradient
            )
          ),
          profiles!inner(
            full_name,
            email
          )
        `)
        .eq('company_id', company_id)
        .order('purchased_at', { ascending: false });

      if (strategicError) {
        logQueryEvent('useUnifiedAdminPurchases', 'Erro ao buscar compras estratégicas', { error: strategicError }, 'error');
      }

      // Consolidar dados
      const unifiedPurchases: UnifiedPurchase[] = [];

      // Processar ofertas especiais
      if (specialOffers) {
        specialOffers.forEach((purchase: any) => {
          unifiedPurchases.push({
            id: purchase.id,
            user_id: purchase.user_id,
            company_id: purchase.company_id,
            type: 'special_offer',
            user_name: purchase.profiles.full_name,
            user_email: purchase.profiles.email,
            title: purchase.special_offers.title,
            description: purchase.special_offers.description,
            quantity: purchase.total_items,
            unit_price: purchase.total_discounted_price,
            total_cost: purchase.total_discounted_price,
            discount_percent: purchase.special_offers.discount_percent,
            original_price: purchase.total_original_price,
            status: purchase.status,
            purchased_at: purchase.purchased_at,
            confirmed_at: purchase.confirmed_at,
            delivered_at: purchase.delivered_at,
            admin_notes: purchase.admin_notes,
            metadata: {
              items_purchased: purchase.items_purchased
            }
          });
        });
      }

      // Função para normalizar status entre diferentes tipos de compra (admin)
      const normalizeAdminStatus = (status: string, type: 'special_offer' | 'strategic_item'): string => {
        // Para compras estratégicas, mapear 'completed' para 'delivered' para consistência
        if (type === 'strategic_item' && status === 'completed') {
          logQueryEvent('useUnifiedAdminPurchases', `Normalizando status: ${status} -> delivered`, { originalStatus: status }, 'debug');
          return 'delivered';
        }
        return status;
      };

      // Processar compras estratégicas
      if (strategicPurchases) {
        strategicPurchases.forEach((purchase: any) => {
          const normalizedStatus = normalizeAdminStatus(purchase.status, 'strategic_item');
          
          unifiedPurchases.push({
            id: purchase.id,
            user_id: purchase.user_id,
            company_id: purchase.company_id,
            type: 'strategic_item',
            user_name: purchase.profiles.full_name,
            user_email: purchase.profiles.email,
            title: purchase.strategic_items.name,
            description: purchase.strategic_items.description,
            category: purchase.strategic_items.strategic_categories?.name,
            quantity: purchase.quantity,
            unit_price: purchase.unit_price,
            total_cost: purchase.total_cost,
            status: normalizedStatus,
            fulfillment_status: purchase.fulfillment_status,
            purchased_at: purchase.purchased_at,
            delivered_at: purchase.fulfilled_at,
            admin_notes: purchase.fulfillment_notes,
            metadata: {
              level_required: purchase.strategic_items.level_required,
              is_popular: purchase.strategic_items.is_popular,
              is_hot: purchase.strategic_items.is_hot,
              gradient: purchase.strategic_items.strategic_categories?.gradient
            }
          });
        });
      }

      // Ordenar por data mais recente
      unifiedPurchases.sort((a, b) => 
        new Date(b.purchased_at).getTime() - new Date(a.purchased_at).getTime()
      );

      // Calcular estatísticas
      const stats: PurchaseStats = {
        total: unifiedPurchases.length,
        pending: unifiedPurchases.filter(p => p.status === 'pending').length,
        confirmed: unifiedPurchases.filter(p => p.status === 'confirmed').length,
        delivered: unifiedPurchases.filter(p => p.status === 'delivered').length,
        cancelled: unifiedPurchases.filter(p => p.status === 'cancelled').length,
        completed: unifiedPurchases.filter(p => p.status === 'completed').length,
        totalRevenue: unifiedPurchases
          .filter(p => !['cancelled'].includes(p.status))
          .reduce((sum, p) => sum + p.total_cost, 0),
        byType: {
          special_offer: unifiedPurchases.filter(p => p.type === 'special_offer').length,
          strategic_item: unifiedPurchases.filter(p => p.type === 'strategic_item').length,
          stardust_store: unifiedPurchases.filter(p => p.type === 'stardust_store').length,
        }
      };

      logQueryEvent('useUnifiedAdminPurchases', 'Compras unificadas carregadas', { 
        total: unifiedPurchases.length,
        specialOffers: specialOffers?.length || 0,
        strategicPurchases: strategicPurchases?.length || 0
      });

      return { purchases: unifiedPurchases, stats };
    },
    enabled: !!company_id,
    staleTime: 3 * 60 * 1000, // 3 minutos
    retry: 2,
  });
}