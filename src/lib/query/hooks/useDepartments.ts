/**
 * Hook para gerenciamento de departamentos
 * <AUTHOR> Internet 2025
 */
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useEffect, useRef } from "react";
import { RealtimeChannel } from "@supabase/supabase-js";

// Definição dos tipos
export interface Department {
  id: string;
  name: string;
  description?: string;
  parent_department_id?: string;
  primary_manager_id?: string;
  substitute_manager_id?: string;
  company_id: string;
  primary_manager?: {
    id?: string;
    full_name: string;
    avatar_url: string;
  };
  substitute_manager?: {
    id?: string;
    full_name: string;
    avatar_url: string;
  };
}

export interface CreateDepartmentParams {
  name: string;
  description?: string;
  parent_department_id?: string;
  primary_manager_id?: string;
  substitute_manager_id?: string;
}

export interface UpdateDepartmentParams {
  id: string;
  values: {
    name: string;
    description?: string;
    parent_department_id?: string;
    primary_manager_id?: string;
    substitute_manager_id?: string;
  }
}

export interface MutationError {
  message: string;
}

// Query keys
export const DepartmentsQueryKeys = {
  all: ["departments"] as const,
  byCompany: (companyId: string) => [...DepartmentsQueryKeys.all, "company", companyId] as const,
  current: () => [...DepartmentsQueryKeys.all, "current"] as const,
  hierarchy: () => [...DepartmentsQueryKeys.all, "hierarchy"] as const,
};

/**
 * Hook para buscar departamentos da mesma empresa do usuário atual
 */
export function useDepartments() {
  const queryClient = useQueryClient();
  const channelRef = useRef<RealtimeChannel | null>(null);

  const query = useQuery({
    queryKey: DepartmentsQueryKeys.hierarchy(),
    queryFn: async (): Promise<Department[]> => {
      try {
        // Buscar usuário autenticado
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return [];

        // Buscar perfil do usuário para obter company_id
        const { data: profile, error: profileError } = await supabase
          .from("profiles")
          .select("company_id")
          .eq("id", user.id)
          .single();

        if (profileError || !profile || !profile.company_id) {
          console.error("Erro ao buscar perfil:", profileError);
          return [];
        }

        // Buscar departamentos com informações completas
        const { data, error } = await supabase
          .from("departments")
          .select(`
            id, 
            name, 
            description,
            parent_department_id, 
            primary_manager_id,
            substitute_manager_id,
            company_id,
            primary_manager:profiles!departments_primary_manager_id_fkey (
              id,
              full_name,
              avatar_url
            ),
            substitute_manager:profiles!departments_substitute_manager_id_fkey (
              id,
              full_name,
              avatar_url
            )
          `)
          .eq("company_id", profile.company_id);

        if (error) {
          console.error("Erro ao buscar departamentos:", error);
          return [];
        }

        return data as unknown as Department[];
      } catch (error) {
        console.error("Erro ao buscar departamentos:", error);
        return [];
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutos - dados ficam "frescos" por mais tempo
    gcTime: 15 * 60 * 1000, // 15 minutos - mantém em cache por mais tempo
    refetchOnWindowFocus: false, // Não recarregar ao focar na janela
    refetchOnMount: false, // Não recarregar ao montar se tem dados em cache
  });

  // Configurar listener para atualizações em tempo real
  useEffect(() => {
    if (channelRef.current) return;

    // Função para configurar o canal
    const setupChannel = async () => {
      try {
        // Buscar company_id do usuário atual
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        const { data: profile } = await supabase
          .from("profiles")
          .select("company_id")
          .eq("id", user.id)
          .single();

        if (!profile || !profile.company_id) return;

        // Configurar canal para changes na tabela departments
        const channel = supabase
          .channel("departments-changes")
          .on(
            "postgres_changes",
            {
              event: "*",
              schema: "public",
              table: "departments",
              filter: `company_id=eq.${profile.company_id}`,
            },
            () => {
              // Invalidar query para recarregar os dados
              queryClient.invalidateQueries({ queryKey: DepartmentsQueryKeys.hierarchy() });
            }
          )
          .subscribe();

        channelRef.current = channel;
      } catch (error) {
        console.error("Erro ao configurar Realtime para departamentos:", error);
      }
    };

    setupChannel();

    // Limpeza
    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
        channelRef.current = null;
      }
    };
  }, [queryClient]);

  const result = {
    departments: Array.isArray(query.data) ? query.data : [],
    isLoading: query.isLoading && !query.data, // Só está loading se não tem dados em cache
    isError: query.isError,
    error: query.error,
    isFetching: query.isFetching, // Para mostrar indicador de refresh quando necessário
  };

  return result;
}

/**
 * Hook para criar um novo departamento
 * @param onSuccess - Callback executado em caso de sucesso
 * @param onError - Callback executado em caso de erro
 * @returns Mutation para criar departamento
 * <AUTHOR> Internet 2025
 */
export function useCreateDepartment(
  onSuccess?: (newDepartment: Department) => void,
  onError?: (error: MutationError) => void
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (values: CreateDepartmentParams): Promise<Department> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Usuário não encontrado");

      const { data: profile } = await supabase
        .from("profiles")
        .select("company_id")
        .eq("id", user.id)
        .single();

      // Filtrar valores vazios para evitar erro de UUID inválido
      const sanitizedValues = Object.entries(values).reduce((acc, [key, value]) => {
        if (value !== "" && value !== null && value !== undefined) {
          acc[key] = value;
        } else if (key === 'primary_manager_id' || key === 'substitute_manager_id' || key === 'parent_department_id') {
          // Para campos UUID opcionais, não incluir se estiver vazio
          // (não definir como null no insert, deixar undefined para não incluir na query)
          return acc;
        } else if (value !== undefined) {
          // Para outros campos (como description), manter string vazia
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);

      const result = await supabase
        .from("departments")
        .insert({
          ...sanitizedValues,
          company_id: profile.company_id,
        })
        .select(`
          id,
          name,
          description,
          parent_department_id,
          primary_manager_id,
          substitute_manager_id,
          company_id,
          primary_manager:profiles!departments_primary_manager_id_fkey (
            id,
            full_name,
            avatar_url
          ),
          substitute_manager:profiles!departments_substitute_manager_id_fkey (
            id,
            full_name,
            avatar_url
          )
        `);

      if (result.error) throw result.error;
      return result.data[0] as unknown as Department;
    },
    onSuccess: (newDepartment) => {
      // Atualização otimista do cache
      queryClient.setQueryData(
        DepartmentsQueryKeys.hierarchy(),
        (oldData: Department[] | undefined) => {
          if (!oldData) return [newDepartment];
          return [...oldData, newDepartment];
        }
      );
      
      // Executar callback de sucesso se fornecido
      if (onSuccess) onSuccess(newDepartment);
    },
    onError: (error) => {
      if (onError) onError(error as MutationError);
    }
  });
}

/**
 * Hook para atualizar um departamento existente
 * @param onSuccess - Callback executado em caso de sucesso
 * @param onError - Callback executado em caso de erro
 * @returns Mutation para atualizar departamento
 * <AUTHOR> Internet 2025
 */
export function useUpdateDepartment(
  onSuccess?: (updatedDepartment: Department) => void,
  onError?: (error: MutationError) => void
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: UpdateDepartmentParams): Promise<Department> => {
      // Filtrar valores vazios para evitar erro de UUID inválido
      const sanitizedValues = Object.entries(params.values).reduce((acc, [key, value]) => {
        if (value !== "" && value !== null && value !== undefined) {
          acc[key] = value;
        } else if (key === 'primary_manager_id' || key === 'substitute_manager_id' || key === 'parent_department_id') {
          // Para campos UUID opcionais, definir como null quando vazio
          acc[key] = null;
        } else if (value !== undefined) {
          // Para outros campos (como description), manter string vazia
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);

      const result = await supabase
        .from("departments")
        .update(sanitizedValues)
        .eq("id", params.id)
        .select(`
          id,
          name,
          description,
          parent_department_id,
          primary_manager_id,
          substitute_manager_id,
          company_id,
          primary_manager:profiles!departments_primary_manager_id_fkey (
            id,
            full_name,
            avatar_url
          ),
          substitute_manager:profiles!departments_substitute_manager_id_fkey (
            id,
            full_name,
            avatar_url
          )
        `);

      if (result.error) throw result.error;
      return result.data[0] as unknown as Department;
    },
    onSuccess: (updatedDepartment) => {
      // Atualização otimista do cache
      queryClient.setQueryData(
        DepartmentsQueryKeys.hierarchy(),
        (oldData: Department[] | undefined) => {
          if (!oldData) return [updatedDepartment];
          return oldData.map(dept => 
            dept.id === updatedDepartment.id ? updatedDepartment : dept
          );
        }
      );
      
      // Executar callback de sucesso se fornecido
      if (onSuccess) onSuccess(updatedDepartment);
    },
    onError: (error) => {
      if (onError) onError(error as MutationError);
    }
  });
} 