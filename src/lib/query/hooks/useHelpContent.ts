/**
 * Hooks TanStack Query para gestão de conteúdo de ajuda (GLOBAL)
 * <AUTHOR> Internet 2025
 */
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import type { 
  HelpSection, 
  HelpFeature, 
  HelpTip, 
  HelpGuide, 
  HelpResource,
  HelpSectionCreateData,
  HelpFeatureCreateData,
  HelpTipCreateData,
  HelpGuideCreateData,
  HelpResourceCreateData,
  PageHelpData
} from '@/types/help';

// Query Keys
const HELP_KEYS = {
  all: ['help'] as const,
  sections: () => [...HELP_KEYS.all, 'sections'] as const,
  pageData: (pageKey: string) => [...HELP_KEYS.all, 'pageData', pageKey] as const,
  resources: () => [...HELP_KEYS.all, 'resources'] as const,
  guides: () => [...HELP_KEYS.all, 'guides'] as const,
} as const;

// ============================================================================
// QUERIES
// ============================================================================

/**
 * Hook para buscar todas as seções de ajuda com features e tips
 */
export function useHelpSections() {
  return useQuery({
    queryKey: HELP_KEYS.sections(),
    queryFn: async () => {
      logQueryEvent('useHelpSections', 'Buscando seções de ajuda globais');
      
      const { data: sections, error } = await supabase
        .from('help_sections')
        .select(`
          *,
          help_features(
            id,
            feature_text,
            display_order,
            is_active
          ),
          help_tips(
            id,
            tip_text,
            tip_type,
            is_active
          )
        `)
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) {
        logQueryEvent('useHelpSections', 'Erro ao buscar seções de ajuda', error, 'error');
        throw error;
      }

      logQueryEvent('useHelpSections', `Encontradas ${sections?.length} seções de ajuda`);
      return sections as HelpSection[];
    },
  });
}

/**
 * Hook para buscar dados de ajuda de uma página específica
 */
export function usePageHelpData(pageKey: string) {
  return useQuery({
    queryKey: HELP_KEYS.pageData(pageKey),
    queryFn: async () => {
      logQueryEvent('usePageHelpData', `Buscando dados de ajuda para página: ${pageKey}`);
      
      const { data: sections, error } = await supabase
        .from('help_sections')
        .select(`
          *,
          help_features(
            id,
            feature_text,
            display_order,
            is_active
          ),
          help_tips(
            id,
            tip_text,
            tip_type,
            is_active
          )
        `)
        .eq('page_key', pageKey)
        .eq('is_active', true);

      if (error) {
        logQueryEvent('usePageHelpData', `Erro ao buscar dados da página ${pageKey}`, error, 'error');
        return null;
      }

      // Se não encontrou dados para a página específica, retorna null para usar fallback
      if (!sections || sections.length === 0) {
        logQueryEvent('usePageHelpData', `Nenhum dado encontrado para página: ${pageKey}. Usando fallback.`);
        return null;
      }

      // Retorna o primeiro resultado encontrado
      const section = sections[0];
      logQueryEvent('usePageHelpData', `Dados encontrados para página: ${pageKey}`);
      return section as PageHelpData;
    },
    enabled: !!pageKey, // Só executa se pageKey estiver definido
  });
}

/**
 * Hook para buscar recursos de suporte
 */
export function useHelpResources() {
  return useQuery({
    queryKey: HELP_KEYS.resources(),
    queryFn: async () => {
      logQueryEvent('useHelpResources', 'Buscando recursos de suporte globais');
      
      const { data: resources, error } = await supabase
        .from('help_resources')
        .select('*')
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) {
        logQueryEvent('useHelpResources', 'Erro ao buscar recursos de suporte', error, 'error');
        throw error;
      }

      logQueryEvent('useHelpResources', `Encontrados ${resources?.length} recursos de suporte`);
      return resources as HelpResource[];
    },
  });
}

/**
 * Hook para buscar guias detalhados
 */
export function useHelpGuides() {
  return useQuery({
    queryKey: HELP_KEYS.guides(),
    queryFn: async () => {
      logQueryEvent('useHelpGuides', 'Buscando guias de ajuda globais');
      
      const { data: guides, error } = await supabase
        .from('help_guides')
        .select('*')
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) {
        logQueryEvent('useHelpGuides', 'Erro ao buscar guias de ajuda', error, 'error');
        throw error;
      }

      logQueryEvent('useHelpGuides', `Encontrados ${guides?.length} guias de ajuda`);
      return guides as HelpGuide[];
    },
  });
}

// ============================================================================
// MUTATIONS (apenas para usuários da Vindula com isVindulaCompany = true)
// ============================================================================

/**
 * Mutation para criar nova seção de ajuda
 */
export function useCreateHelpSection() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: HelpSectionCreateData) => {
      logQueryEvent('useCreateHelpSection', 'Criando nova seção de ajuda', data);
      
      const { data: newSection, error } = await supabase
        .from('help_sections')
        .insert(data)
        .select()
        .single();

      if (error) {
        logQueryEvent('useCreateHelpSection', 'Erro ao criar seção de ajuda', error, 'error');
        throw error;
      }

      logQueryEvent('useCreateHelpSection', 'Seção de ajuda criada com sucesso', newSection);
      return newSection as HelpSection;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: HELP_KEYS.sections() });
    },
  });
}

/**
 * Mutation para atualizar seção de ajuda
 */
export function useUpdateHelpSection() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<HelpSectionCreateData> }) => {
      logQueryEvent('useUpdateHelpSection', 'Atualizando seção de ajuda', { id, data });
      
      const { data: updatedSection, error } = await supabase
        .from('help_sections')
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        logQueryEvent('useUpdateHelpSection', 'Erro ao atualizar seção de ajuda', error, 'error');
        throw error;
      }

      logQueryEvent('useUpdateHelpSection', 'Seção de ajuda atualizada com sucesso', updatedSection);
      return updatedSection as HelpSection;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: HELP_KEYS.sections() });
    },
  });
}

/**
 * Mutation para deletar seção de ajuda
 */
export function useDeleteHelpSection() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string) => {
      logQueryEvent('useDeleteHelpSection', 'Deletando seção de ajuda', { id });
      
      const { error } = await supabase
        .from('help_sections')
        .delete()
        .eq('id', id);

      if (error) {
        logQueryEvent('useDeleteHelpSection', 'Erro ao deletar seção de ajuda', error, 'error');
        throw error;
      }

      logQueryEvent('useDeleteHelpSection', 'Seção de ajuda deletada com sucesso');
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: HELP_KEYS.sections() });
    },
  });
}

// Mutations similares para Features, Tips, Guides e Resources...
// (Mantendo apenas as principais para o exemplo - podem ser expandidas conforme necessário)

/**
 * Mutation para criar nova feature
 */
export function useCreateHelpFeature() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: HelpFeatureCreateData) => {
      logQueryEvent('useCreateHelpFeature', 'Criando nova feature', data);
      
      const { data: newFeature, error } = await supabase
        .from('help_features')
        .insert(data)
        .select()
        .single();

      if (error) {
        logQueryEvent('useCreateHelpFeature', 'Erro ao criar feature', error, 'error');
        throw error;
      }

      logQueryEvent('useCreateHelpFeature', 'Feature criada com sucesso', newFeature);
      return newFeature as HelpFeature;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: HELP_KEYS.sections() });
    },
  });
}

/**
 * Mutation para atualizar feature
 */
export function useUpdateHelpFeature() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<HelpFeatureCreateData> }) => {
      logQueryEvent('useUpdateHelpFeature', 'Atualizando feature', { id, data });
      
      const { data: updatedFeature, error } = await supabase
        .from('help_features')
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        logQueryEvent('useUpdateHelpFeature', 'Erro ao atualizar feature', error, 'error');
        throw error;
      }

      logQueryEvent('useUpdateHelpFeature', 'Feature atualizada com sucesso', updatedFeature);
      return updatedFeature as HelpFeature;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: HELP_KEYS.sections() });
    },
  });
}

/**
 * Mutation para deletar feature
 */
export function useDeleteHelpFeature() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string) => {
      logQueryEvent('useDeleteHelpFeature', 'Deletando feature', { id });
      
      const { error } = await supabase
        .from('help_features')
        .delete()
        .eq('id', id);

      if (error) {
        logQueryEvent('useDeleteHelpFeature', 'Erro ao deletar feature', error, 'error');
        throw error;
      }

      logQueryEvent('useDeleteHelpFeature', 'Feature deletada com sucesso');
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: HELP_KEYS.sections() });
    },
  });
}

/**
 * Mutation para criar nova tip
 */
export function useCreateHelpTip() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: HelpTipCreateData) => {
      logQueryEvent('useCreateHelpTip', 'Criando nova tip', data);
      
      const { data: newTip, error } = await supabase
        .from('help_tips')
        .insert(data)
        .select()
        .single();

      if (error) {
        logQueryEvent('useCreateHelpTip', 'Erro ao criar tip', error, 'error');
        throw error;
      }

      logQueryEvent('useCreateHelpTip', 'Tip criada com sucesso', newTip);
      return newTip as HelpTip;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: HELP_KEYS.sections() });
    },
  });
}

/**
 * Mutation para atualizar tip
 */
export function useUpdateHelpTip() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<HelpTipCreateData> }) => {
      logQueryEvent('useUpdateHelpTip', 'Atualizando tip', { id, data });
      
      const { data: updatedTip, error } = await supabase
        .from('help_tips')
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        logQueryEvent('useUpdateHelpTip', 'Erro ao atualizar tip', error, 'error');
        throw error;
      }

      logQueryEvent('useUpdateHelpTip', 'Tip atualizada com sucesso', updatedTip);
      return updatedTip as HelpTip;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: HELP_KEYS.sections() });
    },
  });
}

/**
 * Mutation para deletar tip
 */
export function useDeleteHelpTip() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string) => {
      logQueryEvent('useDeleteHelpTip', 'Deletando tip', { id });
      
      const { error } = await supabase
        .from('help_tips')
        .delete()
        .eq('id', id);

      if (error) {
        logQueryEvent('useDeleteHelpTip', 'Erro ao deletar tip', error, 'error');
        throw error;
      }

      logQueryEvent('useDeleteHelpTip', 'Tip deletada com sucesso');
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: HELP_KEYS.sections() });
    },
  });
}

/**
 * Mutation para criar novo recurso de suporte
 */
export function useCreateHelpResource() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: HelpResourceCreateData) => {
      logQueryEvent('useCreateHelpResource', 'Criando novo recurso', data);
      
      const { data: newResource, error } = await supabase
        .from('help_resources')
        .insert(data)
        .select()
        .single();

      if (error) {
        logQueryEvent('useCreateHelpResource', 'Erro ao criar recurso', error, 'error');
        throw error;
      }

      logQueryEvent('useCreateHelpResource', 'Recurso criado com sucesso', newResource);
      return newResource as HelpResource;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: HELP_KEYS.resources() });
    },
  });
}

/**
 * Mutation para criar novo guia
 */
export function useCreateHelpGuide() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: HelpGuideCreateData) => {
      logQueryEvent('useCreateHelpGuide', 'Criando novo guia', data);
      
      const { data: newGuide, error } = await supabase
        .from('help_guides')
        .insert(data)
        .select()
        .single();

      if (error) {
        logQueryEvent('useCreateHelpGuide', 'Erro ao criar guia', error, 'error');
        throw error;
      }

      logQueryEvent('useCreateHelpGuide', 'Guia criado com sucesso', newGuide);
      return newGuide as HelpGuide;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: HELP_KEYS.guides() });
    },
  });
}

/**
 * Mutation para atualizar guia
 */
export function useUpdateHelpGuide() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<HelpGuideCreateData> }) => {
      logQueryEvent('useUpdateHelpGuide', 'Atualizando guia', { id, data });
      
      const { data: updatedGuide, error } = await supabase
        .from('help_guides')
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        logQueryEvent('useUpdateHelpGuide', 'Erro ao atualizar guia', error, 'error');
        throw error;
      }

      logQueryEvent('useUpdateHelpGuide', 'Guia atualizado com sucesso', updatedGuide);
      return updatedGuide as HelpGuide;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: HELP_KEYS.guides() });
    },
  });
}

/**
 * Mutation para deletar guia
 */
export function useDeleteHelpGuide() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string) => {
      logQueryEvent('useDeleteHelpGuide', 'Deletando guia', { id });
      
      const { error } = await supabase
        .from('help_guides')
        .delete()
        .eq('id', id);

      if (error) {
        logQueryEvent('useDeleteHelpGuide', 'Erro ao deletar guia', error, 'error');
        throw error;
      }

      logQueryEvent('useDeleteHelpGuide', 'Guia deletado com sucesso');
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: HELP_KEYS.guides() });
    },
  });
} 