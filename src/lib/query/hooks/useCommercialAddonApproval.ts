/**
 * Hook para aprovação de add-ons comerciais
 * <AUTHOR> Internet 2025
 */

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { QueryKeys } from '@/lib/query/queryKeys';
import { successWithNotification, errorWithNotification } from '@/lib/notifications/toastWithNotification';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

export interface ApproveAddonRequestData {
  leadId: string;
  action: 'approve' | 'reject';
  approvalNotes?: string;
}

export interface ApprovalResult {
  success: boolean;
  lead_id: string;
  approval_status: string;
  activation_status: string;
  error_code?: string;
  error_message?: string;
  activation_details?: any;
}

export function useApproveAddonRequest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ApproveAddonRequestData): Promise<ApprovalResult> => {
      logQueryEvent('useApproveAddonRequest', 'Iniciando aprovação de add-on', { 
        leadId: data.leadId, 
        action: data.action 
      });

      const { data: result, error } = await supabase.rpc('approve_commercial_addon_request_v2', {
        p_lead_id: data.leadId,
        p_action: data.action,
        p_approval_notes: data.approvalNotes || null
      });

      if (error) {
        logQueryEvent('useApproveAddonRequest', 'Erro na requisição', { error }, 'error');
        throw error;
      }

      const response = result?.[0];

      if (!response?.success) {
        const structuredError = new Error(response?.error_message || 'Erro desconhecido na aprovação');
        (structuredError as any).code = response?.error_code;
        throw structuredError;
      }

      logQueryEvent('useApproveAddonRequest', 'Aprovação realizada com sucesso', { 
        leadId: data.leadId,
        approvalStatus: response.approval_status,
        activationStatus: response.activation_status
      });

      return response;
    },
    onSuccess: (result, variables) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.all() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.detail(variables.leadId) });
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.history(variables.leadId) });

      // Notificação de sucesso baseada na ação
      if (variables.action === 'approve') {
        if (result.activation_status === 'activated') {
          successWithNotification('Add-ons Aprovados e Ativados!', {
            description: 'Os add-ons foram aprovados e ativados automaticamente no tenant.',
          });
        } else if (result.activation_status === 'activation_failed') {
          errorWithNotification('Aprovado mas Falha na Ativação', {
            description: 'Add-ons aprovados, mas houve falha na ativação automática. Verifique os detalhes.',
          });
        } else {
          successWithNotification('Add-ons Aprovados!', {
            description: 'Solicitação aprovada. Ativação em andamento...',
          });
        }
      } else {
        successWithNotification('Solicitação Rejeitada', {
          description: 'A solicitação de add-ons foi rejeitada com sucesso.',
        });
      }
    },
    onError: (error: any) => {
      const errorCode = error.code;
      let title = 'Erro na Aprovação';
      let description = 'Não foi possível processar a aprovação.';

      switch (errorCode) {
        case 'PERMISSION_DENIED':
          title = 'Sem Permissão';
          description = 'Você não tem permissão para aprovar solicitações de add-ons.';
          break;
        case 'LEAD_NOT_FOUND':
          title = 'Lead Não Encontrado';
          description = 'A solicitação não foi encontrada.';
          break;
        case 'INVALID_STATUS':
          title = 'Status Inválido';
          description = 'Esta solicitação não pode ser aprovada no status atual.';
          break;
        case 'INVALID_ACTION':
          title = 'Ação Inválida';
          description = 'Ação deve ser "aprovar" ou "rejeitar".';
          break;
        default:
          description = error.message || 'Erro interno do sistema.';
      }

      errorWithNotification(title, { description });
    }
  });
}

export interface ActivatePlanSubscriptionData {
  leadId: string;
  activationNotes?: string;
}

export interface PlanActivationResult {
  success: boolean;
  lead_id: string;
  subscription_id: string;
  billing_id?: string;
  courtesy_end_date: string;
  error_code?: string;
  error_message?: string;
  activation_details?: any;
}

export function useActivatePlanSubscription() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ActivatePlanSubscriptionData): Promise<PlanActivationResult> => {
      logQueryEvent('useActivatePlanSubscription', 'Iniciando ativação de plano com cobrança', { 
        leadId: data.leadId
      });

      const { data: result, error } = await supabase.rpc('activate_plan_subscription_with_billing_v1', {
        p_lead_id: data.leadId,
        p_activation_notes: data.activationNotes || null
      });

      if (error) {
        logQueryEvent('useActivatePlanSubscription', 'Erro na requisição', { error }, 'error');
        throw error;
      }

      const response = result?.[0];

      if (!response?.success) {
        const structuredError = new Error(response?.error_message || 'Erro desconhecido na ativação');
        (structuredError as any).code = response?.error_code;
        throw structuredError;
      }

      logQueryEvent('useActivatePlanSubscription', 'Plano ativado e cobrança gerada com sucesso', { 
        leadId: data.leadId,
        subscriptionId: response.subscription_id,
        billingId: response.billing_id
      });

      return response;
    },
    onSuccess: (result, variables) => {
      // Invalidar queries relacionadas - REFRESH COMPLETO
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.all() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.detail(variables.leadId) });
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.history(variables.leadId) });
      
      // Invalidar também dados de billing, trials e subscription
      queryClient.invalidateQueries({ queryKey: QueryKeys.trials.all() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.trials.dashboard() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.subscription.current() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.subscription.plans() });
      // Invalidar todas as queries de subscription
      queryClient.invalidateQueries({ 
        predicate: (query) => query.queryKey[0] === 'subscription'
      });
      
      // FORÇAR refresh da página toda para garantir atualização
      queryClient.refetchQueries({ queryKey: QueryKeys.commercialLeads.all() });

      successWithNotification('Plano Ativado e Cobrança Gerada!', {
        description: 'O plano foi ativado e a cobrança foi gerada no sistema de billing.',
      });
    },
    onError: (error: any) => {
      const errorCode = error.code;
      let title = 'Erro na Ativação';
      let description = 'Não foi possível ativar o plano.';

      switch (errorCode) {
        case 'PERMISSION_DENIED':
          title = 'Sem Permissão';
          description = 'Você não tem permissão para ativar planos.';
          break;
        case 'LEAD_NOT_FOUND':
          title = 'Lead Não Encontrado';
          description = 'A solicitação não foi encontrada.';
          break;
        case 'PLAN_NOT_FOUND':
          title = 'Plano Não Encontrado';
          description = 'O plano selecionado não foi encontrado.';
          break;
        case 'BILLING_ERROR':
          title = 'Erro no Billing';
          description = 'Plano ativado, mas houve erro ao gerar a cobrança.';
          break;
        default:
          description = error.message || 'Erro interno do sistema.';
      }

      errorWithNotification(title, { description });
    }
  });
}

export function useGetAddonApprovalDetails() {
  return {
    getActivationDetails: (activationDetails: any) => {
      if (!activationDetails) return null;

      return {
        totalActivated: activationDetails.total_activated || 0,
        totalFailed: activationDetails.total_failed || 0,
        activatedAddons: activationDetails.activated_addons || [],
        failedAddons: activationDetails.failed_addons || [],
        processedAt: activationDetails.processed_at,
        success: activationDetails.success
      };
    },
    
    getApprovalStatusBadge: (approvalStatus: string) => {
      const statusMap = {
        'pending': { label: 'Pendente', variant: 'secondary' as const, color: 'text-yellow-600' },
        'approved': { label: 'Aprovado', variant: 'default' as const, color: 'text-green-600' },
        'rejected': { label: 'Rejeitado', variant: 'destructive' as const, color: 'text-red-600' },
        'activated': { label: 'Ativado', variant: 'default' as const, color: 'text-blue-600' }
      };
      
      return statusMap[approvalStatus as keyof typeof statusMap] || statusMap.pending;
    },

    getActivationStatusBadge: (activationStatus: string) => {
      const statusMap = {
        'not_activated': { label: 'Não Ativado', variant: 'secondary' as const, color: 'text-gray-600' },
        'activating': { label: 'Ativando...', variant: 'secondary' as const, color: 'text-blue-600' },
        'activated': { label: 'Ativado', variant: 'default' as const, color: 'text-green-600' },
        'activation_failed': { label: 'Falha na Ativação', variant: 'destructive' as const, color: 'text-red-600' }
      };
      
      return statusMap[activationStatus as keyof typeof statusMap] || statusMap.not_activated;
    }
  };
}

/**
 * Hook para ativar plano automaticamente com período de cortesia
 */
export function useActivatePlanWithCourtesy() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: {
      leadId: string;
      userId: string;
      selectedPlan: any;
      selectedAddons?: any[];
    }) => {
      logQueryEvent('useActivatePlanWithCourtesy', 'Ativando plano com cortesia', data);

      const { data: result, error } = await supabase.rpc('activate_plan_with_courtesy_v2', {
        p_lead_id: data.leadId,
        p_user_id: data.userId,
        p_selected_plan: data.selectedPlan,
        p_selected_addons: data.selectedAddons || []
      });

      if (error) {
        logQueryEvent('useActivatePlanWithCourtesy', 'Erro na ativação', { error }, 'error');
        throw error;
      }

      const response = result?.[0];
      if (!response?.success) {
        const activationError = new Error(response?.error_message || 'Erro na ativação do plano');
        (activationError as any).code = response?.error_code;
        throw activationError;
      }

      logQueryEvent('useActivatePlanWithCourtesy', 'Plano ativado com sucesso', response);
      return response;
    },
    onSuccess: (data) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: QueryKeys.subscription.current() });
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.all() });
      
      logQueryEvent('useActivatePlanWithCourtesy', 'Plano ativado - invalidando queries');
    },
    onError: (error: any) => {
      const errorCode = error.code;
      let title = 'Erro na ativação do plano';
      let description = 'Não foi possível ativar o plano automaticamente.';

      switch (errorCode) {
        case 'INVALID_PLAN':
          title = 'Plano inválido';
          description = 'O plano selecionado não é válido.';
          break;
        case 'USER_NOT_FOUND':
          title = 'Usuário não encontrado';
          description = 'Não foi possível identificar o usuário.';
          break;
        default:
          description = error.message || description;
      }

      logQueryEvent('useActivatePlanWithCourtesy', 'Erro na ativação', { error, errorCode }, 'error');
      errorWithNotification(title, { description });
    }
  });
}

/**
 * Interface para dados de lead comercial
 */
export interface CommercialLeadData {
  user_id: string;
  requested_plan_id?: string | null;
  selected_addons: any[];
  lead_source: string;
  contact_preferences: any;
  upgrade_context: any;
  metadata?: any;
}

/**
 * Hook para consolidar ou criar lead comercial
 */
export function useConsolidateOrCreateLead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CommercialLeadData) => {
      logQueryEvent('useConsolidateOrCreateLead', 'Consolidando ou criando lead', data);

      const { data: result, error } = await supabase.rpc('consolidate_or_create_lead_v3', {
        p_user_id: data.user_id,
        p_requested_plan_id: data.requested_plan_id,
        p_requested_addons: data.selected_addons,
        p_contact_preferences: data.contact_preferences,
        p_upgrade_context: data.upgrade_context,
        p_source_context: data.lead_source
      });

      if (error) {
        logQueryEvent('useConsolidateOrCreateLead', 'Erro na requisição', { error }, 'error');
        throw error;
      }

      const response = result?.[0];
      if (!response?.success) {
        const leadError = new Error(response?.error_message || 'Erro na criação do lead');
        (leadError as any).code = response?.error_code;
        throw leadError;
      }

      logQueryEvent('useConsolidateOrCreateLead', 'Lead processado com sucesso', response);
      return response;
    },
    onSuccess: async (response) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: QueryKeys.commercialLeads.all() });
      
      logQueryEvent('useConsolidateOrCreateLead', 'Lead criado - invalidando queries');
      
      // ✅ NOVO: Enviar email comercial via Edge Function (simplificado)
      if (response.lead_id) {
        try {
          logQueryEvent('useConsolidateOrCreateLead', 'Enviando email comercial', { leadId: response.lead_id });
          
          const emailResult = await supabase.functions.invoke('send-commercial-notification', {
            body: { 
              lead_id: response.lead_id
              // Edge Function buscará os dados automaticamente
            }
          });
          
          if (emailResult.error) {
            console.error('Erro ao enviar email comercial:', emailResult.error);
            logQueryEvent('useConsolidateOrCreateLead', 'Erro no envio de email', { error: emailResult.error }, 'error');
          } else {
            logQueryEvent('useConsolidateOrCreateLead', 'Email comercial enviado com sucesso', emailResult.data);
          }
        } catch (emailError) {
          console.error('Erro na chamada da Edge Function:', emailError);
          logQueryEvent('useConsolidateOrCreateLead', 'Erro na Edge Function', { error: emailError }, 'error');
          // Não falhar o processo por causa do email
        }
      }
    },
    onError: (error: any) => {
      const errorCode = error.code;
      let title = 'Erro na solicitação';
      let description = 'Não foi possível processar sua solicitação.';

      switch (errorCode) {
        case 'USER_NOT_FOUND':
          title = 'Usuário não encontrado';
          description = 'Não foi possível identificar o usuário.';
          break;
        case 'ANALYSIS_FAILED':
          title = 'Erro na análise';
          description = 'Falha na análise do tipo de solicitação.';
          break;
        default:
          description = error.message || description;
      }

      logQueryEvent('useConsolidateOrCreateLead', 'Erro na criação do lead', { error, errorCode }, 'error');
      errorWithNotification(title, { description });
    }
  });
} 