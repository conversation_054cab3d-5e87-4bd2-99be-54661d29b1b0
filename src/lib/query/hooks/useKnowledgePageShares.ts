/**
 * Hook para gerenciar compartilhamentos de páginas de conhecimento.
 * <AUTHOR> Internet 2025
 */
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

interface ShareKnowledgePageParams {
  pageId: string;
  shareType?: 'copy_link' | 'native_share' | 'internal';
}

export function useShareKnowledgePage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ pageId, shareType = 'copy_link' }: ShareKnowledgePageParams) => {
      logQueryEvent('useKnowledgePageShares', 'Registrando compartilhamento de página de conhecimento', { pageId, shareType }, 'info');

      // 1. Obter usuário atual
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuário não autenticado');

      // 2. Obter company_id do perfil (OBRIGATÓRIO para RLS)
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('company_id')
        .eq('id', user.id)
        .single();

      if (profileError || !profile?.company_id) {
        throw new Error('Erro ao obter informações da empresa do usuário');
      }

      // 3. Usar INSERT normal (não UPSERT) pois pode haver múltiplos compartilhamentos
      const insertData: any = {
        content_type: 'knowledge_page',
        content_id: pageId,
        user_id: user.id,
        company_id: profile.company_id,
        share_type: shareType
      };

      // Não incluir post_id para knowledge_pages para evitar foreign key constraint
      // post_id será NULL para knowledge_pages

      const { data, error } = await supabase
        .from('post_shares')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        logQueryEvent('useKnowledgePageShares', 'Erro ao registrar compartilhamento', error, 'error');
        throw error;
      }

      logQueryEvent('useKnowledgePageShares', 'Compartilhamento registrado com sucesso', { shareId: data.id }, 'info');
      return data;
    },
    onSuccess: () => {
      // Invalidar cache de métricas de compartilhamentos
      queryClient.invalidateQueries({ queryKey: ['analytics', 'shares-metrics'] });
      queryClient.invalidateQueries({ queryKey: ['knowledge', 'pages'] });
    },
    onError: (error: any) => {
      // Tratamento específico para erro de duplicata
      if (error.code === '23505') {
        console.warn('Tentativa de compartilhamento duplicado detectada - ignorando erro');
        return; // Não mostrar toast de erro para duplicatas
      }
      
      logQueryEvent('useKnowledgePageShares', 'Falha na mutation de compartilhamento', error, 'error');
    }
  });
}