/**
 * Hook para contar total de mensagens não lidas do usuário (canais + chats)
 * 
 * ✅ OTIMIZADO: Usa UnifiedRealtimeProvider catch-all para invalidação em tempo real.
 * ChatHandler invalida automaticamente query ['unread-chat-count'] quando mensagens chegam.
 * Removido polling desnecessário - agora 100% real-time via WebSocket.
 * 
 * <AUTHOR> Internet 2025
 */
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useCurrentUser } from "@/hooks/use-current-user";

export function useUnreadChatCount() {
  const currentUserId = useCurrentUser();

  return useQuery({
    queryKey: ['unread-chat-count', currentUserId],
    queryFn: async () => {
      if (!currentUserId) {
        return 0;
      }

      // Buscar contagem de mensagens não lidas para todos os canais do usuário
      const { data: channelsData, error: channelsError } = await supabase
        .from('channel_members')
        .select('channel_id')
        .eq('user_id', currentUserId);

      if (channelsError) {
        console.error('❌ [useUnreadChatCount] Erro ao buscar canais:', channelsError);
        throw channelsError;
      }

      const channels = channelsData || [];
      let totalUnreadCount = 0;

      // Para cada canal, buscar contagem de mensagens não lidas
      for (const channel of channels) {
        const { data: count, error: countError } = await supabase
          .rpc('get_unread_messages_count_v3', { p_channel_id: channel.channel_id });
        
        if (countError) {
          console.error(`❌ [useUnreadChatCount] Erro ao contar mensagens do canal ${channel.channel_id}:`, countError);
          continue;
        }

        totalUnreadCount += count || 0;
      }


      return totalUnreadCount;
    },
    enabled: !!currentUserId,
    // ✅ OTIMIZAÇÃO: Removido polling - UnifiedRealtimeProvider invalida em tempo real
    // refetchInterval: 5000, // ❌ REMOVIDO: Substituído por invalidação real-time
    staleTime: 30 * 1000, // ✅ OTIMIZADO: 30 segundos - dados são atualizados via WebSocket
    gcTime: 10 * 60 * 1000, // ✅ OTIMIZADO: 10 minutos - cache mais longo pois é real-time
  });
}