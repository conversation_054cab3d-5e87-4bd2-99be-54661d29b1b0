/**
 * Chaves centralizadas para o React Query
 * <AUTHOR> Internet 2025
 */

// Namespaces para organizar as keys por domínio
export const QueryKeys = {
  // Usuários
  users: {
    all: ["users"] as const,
    details: (userId: string) => ["users", userId] as const,
    roles: (userId: string) => ["users", userId, "roles"] as const,
    permissions: (userId: string, permissionIds?: string[]) =>
      permissionIds
        ? (["users", userId, "permissions", ...permissionIds] as const)
        : (["users", userId, "permissions"] as const),
    permission: (userId: string, permissionId: string) =>
      ["users", userId, "permissions", permissionId] as const,
    permissionBase: (userId: string) => ["users", userId, "permissions"] as const,
    profile: (userId: string) => ["users", userId, "profile"] as const,
    avatar: (userId: string) => ["users", userId, "avatar"] as const,
    current: () => ["users", "current"] as const,
    currentPermissions: () => ["users", "current", "permissions"] as const,
    currentRoles: () => ["users", "current", "roles"] as const,
    currentProfile: () => ["users", "current", "profile"] as const,
    company: () => ["users", "company"] as const,
    tenantUsers: (companyId: string) => ["users", "tenant", companyId] as const,
    tenantLicense: () => ["users", "tenant-license"] as const,
    isVindulaCompany: () => ["users", "is-vindula-company"] as const,
    active: (thresholdMinutes = 15) => ["users", "active", thresholdMinutes] as const,
    online: () => ["users", "online"] as const,
    activeCount: () => ["users", "active-count"] as const,
    recentAdmissions: (days: number = 30) => ["users", "recent-admissions", days] as const,
    companyOwner: () => ["users", "company-owner"] as const,
    skillsWithLevels: (userId: string) => ["users", "skillsWithLevels", userId] as const,
  },

  // Habilidades (Skills)
  skills: {
    all: () => ["skills"] as const,
    available: () => ["skills", "available"] as const,
    levels: () => ["skills", "levels"] as const,
    byType: (type: 'system' | 'company') => ["skills", "type", type] as const,
    company: (companyId: string) => ["skills", "company", companyId] as const,
    system: () => ["skills", "system"] as const,
  },

  // Empresa
  company: {
    details: () => ["company"] as const,
    employees: () => ["company", "employees"] as const,
    departments: () => ["company", "departments"] as const,
    jobTitles: () => ["company", "jobTitles"] as const,
    units: () => ["company", "units"] as const,
    locations: () => ["company", "locations"] as const,
    roles: (companyId?: string) =>
      companyId ? (["company", "roles", companyId] as const) : (["company", "roles"] as const),
  },

  // Departamentos
  departments: {
    all: ["departments"] as const,
    details: (departmentId: string) => ["departments", departmentId] as const,
    members: (departmentId: string) => ["departments", departmentId, "members"] as const,
    validation: (companyId: string) => ["departments", "validation", companyId] as const,
  },

  // Cargos
  jobTitles: {
    all: ["job_titles"] as const,
    details: (jobTitleId: string) => ["job_titles", jobTitleId] as const,
    members: (jobTitleId: string) => ["job_titles", jobTitleId, "members"] as const,
    validation: (companyId: string) => ["job_titles", "validation", companyId] as const,
  },

  // People Hub
  people: {
    all: () => ["people"] as const,
    directory: () => ["people", "directory"] as const,
    analytics: () => ["people", "analytics"] as const,
    networking: () => ["people", "networking"] as const,
    connections: (userId: string) => ["people", "connections", userId] as const,
    networkingStats: (userId: string) => ["people", "networking-stats", userId] as const,
    suggestions: (userId: string) => ["people", "suggestions", userId] as const,
    birthdays: {
      thisMonth: () => ["people", "birthdays", "thisMonth"] as const,
      today: () => ["people", "birthdays", "today"] as const,
      stats: () => ["people", "birthdays", "stats"] as const,
      upcoming: (days: number = 7) => ["people", "birthdays", "upcoming", days] as const,
    },
    // Organizational Analytics
    organizationalPulse: () => ["people", "organizational-pulse"] as const,
    communicationHealth: () => ["people", "communication-health"] as const,
    crossCollaboration: () => ["people", "cross-collaboration"] as const,
    productivityMoments: () => ["people", "productivity-moments"] as const,
    bottleneckDetection: () => ["people", "bottleneck-detection"] as const,
    communicationEfficiency: () => ["people", "communication-efficiency"] as const,
    knowledgeGaps: () => ["people", "knowledge-gaps"] as const,
  },

  // Promoções e Mudanças de Cargo
  promotions: {
    all: () => ["promotions"] as const,
    recent: (days: number = 30) => ["promotions", "recent", days] as const,
    stats: () => ["promotions", "stats"] as const,
    history: (userId: string) => ["promotions", "history", userId] as const,
    byUser: (userId: string) => ["promotions", "user", userId] as const,
    byDepartment: (departmentId: string) => ["promotions", "department", departmentId] as const,
    analytics: (timeframe?: string) =>
      timeframe
        ? (["promotions", "analytics", timeframe] as const)
        : (["promotions", "analytics"] as const),
    filtered: (filters: Record<string, any>) => ["promotions", "filtered", filters] as const,
  },

  // Unidades
  units: {
    all: ["units"] as const,
    detail: (unitId: string) => ["units", unitId] as const,
    members: (unitId: string) => ["units", unitId, "members"] as const,
    validation: (companyId: string) => ["units", "validation", companyId] as const,
  },

  // Localizações
  locations: {
    all: ["locations"] as const,
    detail: (locationId: string) => ["locations", locationId] as const,
    byState: (state: string) => ["locations", "state", state] as const,
    byCity: (city: string) => ["locations", "city", city] as const,
    validation: (companyId: string) => ["locations", "validation", companyId] as const,
  },

  // Posts e Feed
  posts: {
    all: () => ["posts"] as const,
    feed: (filters?: Record<string, unknown>) =>
      filters ? (["posts", "feed", filters] as const) : (["posts", "feed"] as const),
    trending: (limit = 10) => ["posts", "trending", limit] as const,
    scheduled: () => ["posts", "scheduled"] as const,
    scheduledCount: () => ["posts", "scheduled-count"] as const,
    details: (postId: string) => ["posts", postId] as const,
    detail: (postId: string) => ["posts", postId, "detail"] as const,
    exists: (postId: string) => ["posts", postId, "exists"] as const,
    byUser: (userId: string) => ["posts", "user", userId] as const,
    likes: (postId: string) => ["posts", postId, "likes"] as const,
    comments: (postId: string) => ["posts", postId, "comments"] as const,
    viewCount: (postId: string) => ["posts", postId, "view-count"] as const,
    // Edição de posts
    canEdit: (postId: string) => ["posts", postId, "can-edit"] as const,
    editHistory: (postId: string | null | undefined) => 
      postId ? (["posts", postId, "edit-history"] as const) : (["posts", "edit-history"] as const),
    latestEdit: (postId: string | null | undefined) => 
      postId ? (["posts", postId, "latest-edit"] as const) : (["posts", "latest-edit"] as const),
  },

  // Notificações
  notifications: {
    all: () => ["notifications"] as const,
    unread: () => ["notifications", "unread"] as const,
    list: (filters?: Record<string, any>) => 
      filters ? (["notifications", "list", filters] as const) : (["notifications", "list"] as const),
  },

  // Timeline
  timeline: {
    notifications: (filters?: Record<string, any>) =>
      filters ? (["timeline", "notifications", filters] as const) : (["timeline", "notifications"] as const),
    posts: (filters?: Record<string, any>) =>
      filters ? (["timeline", "posts", filters] as const) : (["timeline", "posts"] as const),
    unread: () => ["timeline", "unread"] as const,
    limits: () => ["timeline", "limits"] as const,
  },

  // Leitura Obrigatória
  obligations: {
    all: () => ["obligations"] as const,
    pending: () => ["obligations", "pending"] as const,
    completed: () => ["obligations", "completed"] as const,
    overdue: () => ["obligations", "overdue"] as const,
    details: (obligationId: string) => ["obligations", obligationId] as const,
    company: () => ["obligations", "company"] as const,
  },

  // Equipes
  teams: {
    all: () => ["teams"] as const,
    details: (teamId: string) => ["teams", teamId] as const,
    members: (teamId: string) => ["teams", teamId, "members"] as const,
  },

  // Trials
  trials: {
    all: () => ["trials"] as const,
    statistics: () => ["trials", "statistics"] as const,
    expiring: (daysAhead: number) => ["trials", "expiring", daysAhead] as const,
    dashboard: () => ["trials", "dashboard"] as const,
  },

  // Assets Visuais
  visualAssets: {
    all: () => ["visualAssets"] as const,
    byType: (type: string) => ["visualAssets", type] as const,
  },

  // Itens da Loja
  storeItems: {
    all: () => ["store-items"] as const,
    list: () => ["store-items", "list"] as const,
    detail: (id: string) => ["store-items", "detail", id] as const,
    byCategory: (category: string) => ["store-items", "category", category] as const,
    byType: (type: string) => ["store-items", "type", type] as const,
    active: () => ["store-items", "active"] as const,
  },

  // Packs de Reações Premium
  reactionPacks: {
    all: () => ["reactionPacks"] as const,
    adminList: () => ["reactionPacks", "admin"] as const,
    userEmojis: () => ["reactionPacks", "user-emojis"] as const,
    packEmojis: (packId: string) => ["reactionPacks", "pack-emojis", packId] as const,
    details: (packId: string) => ["reactionPacks", "details", packId] as const,
    search: (query: string) => ["reactionPacks", "search", query] as const,
    userPacks: () => ["reactionPacks", "user-packs"] as const,
    packDetail: (packId: string) => ["reactionPacks", "detail", packId] as const,
    emojiByCode: (code: string) => ["reactionPacks", "emoji", code] as const,
  },

  // Personalização (Via-Láctea Plus)
  customization: {
    slots: () => ["customization", "slots"] as const,
    userSlots: (userId: string) => ["customization", "slots", userId] as const,
    activeItems: () => ["customization", "active-items"] as const,
    collection: () => ["customization", "collection"] as const,
    badges: () => ["customization", "badges"] as const,
    profiles: (userId?: string) => 
      userId ? (["customization", "profiles", userId] as const) : (["customization", "profiles"] as const),
    preview: (itemId: string, slotType: string) => 
      ["customization", "preview", itemId, slotType] as const,
  },

  // Gamificação
  gamification: {
    medals: () => ["gamification", "medals"] as const,
    levels: () => ["gamification", "levels"] as const,
    userProgress: (userId: string) => ["gamification", "user-progress", userId] as const,
    stardustBalance: (userId: string = "current") => ["gamification", "stardust", "balance", userId] as const,
    stardustTransactions: (page: number, pageSize: number) => 
      ["gamification", "stardust-transactions", { page, pageSize }] as const,
    userLevel: () => ["gamification", "user-level"] as const,
    ranking: (limit: number, offset: number) =>
      ["gamification", "ranking", { limit, offset }] as const,
    userRankingPosition: (userId: string) =>
      ["gamification", "ranking", "position", userId] as const,
    globalMetrics: () => ["gamificationGlobalMetrics"] as const, // Nova chave
    inventory: (itemType?: string) => 
      itemType ? (["gamification", "inventory", itemType] as const) : (["gamification", "inventory"] as const),
    availableStoreItems: (category?: string) => 
      ["gamification", "available-store-items", category || "all"] as const,
    storeItemPurchase: () => ["gamification", "store-item-purchase"] as const,
    debugStoreOwnership: (storeItemId?: string, userId?: string) =>
      ["gamification", "debug-store-ownership", storeItemId, userId] as const,
    autoGrantCheck: () => ["gamification", "auto-grant-check"] as const,
  },

  // Convites pendentes
  invites: {
    all: () => ["invites"] as const,
  },

  // Armazenamento
  storage: {
    overview: (companyId: string) => ["storage", "overview", companyId] as const,
    breakdown: (companyId: string) => ["storage", "breakdown", companyId] as const,
    history: (companyId: string) => ["storage", "history", companyId] as const,
    addons: (companyId?: string) =>
      companyId ? (["storage", "addons", companyId] as const) : (["storage", "addons"] as const),
    detailedStats: (companyId: string) => ["storage", "detailedStats", companyId] as const,
    notifications: (companyId: string) => ["storage", "notifications", companyId] as const,
    simulateGrowth: (companyId: string, params = {}) =>
      ["storage", "simulateGrowth", companyId, params] as const,
    addonImpact: (companyId: string, addonId: string) =>
      ["storage", "addonImpact", companyId, addonId] as const,
    dashboard: (companyId: string) => ["storage", "dashboard", companyId] as const,
    addonsHistory: (companyId: string) => ["storage", "addonsHistory", companyId] as const,
    usage: (companyId: string) => ["storage", "usage", companyId] as const,
    // 🆕 Storage Files Audit System
    filesAudit: (companyId: string, filters?: Record<string, unknown>) =>
      filters
        ? (["storage", "filesAudit", companyId, filters] as const)
        : (["storage", "filesAudit", companyId] as const),
    statsByModule: (companyId: string) => ["storage", "statsByModule", companyId] as const,
    duplicateFiles: (companyId: string) => ["storage", "duplicateFiles", companyId] as const,
    fileDetails: (fileId: string) => ["storage", "fileDetails", fileId] as const,
    auditSummary: (companyId: string) => ["storage", "auditSummary", companyId] as const,
  },

  // Planos e Features
  subscription: {
    plans: () => ["subscription", "plans"] as const,
    userPlan: (companyId: string) => ["subscription", "plans", companyId] as const,
    features: (companyId: string) => ["subscription", "features", companyId] as const,
    current: () => ["subscription", "current"] as const,
    available: () => ["subscription", "available"] as const,
    addons: () => ["subscription", "addons"] as const,
    addonPurchases: () => ["subscription", "addon-purchases"] as const,
    pendingPurchases: () => ["subscription", "pending-purchases"] as const,
    purchaseHistory: () => ["subscription", "purchase-history"] as const,
    limits: () => ["subscription", "limits"] as const,
    upgradeRequirements: () => ["subscription", "upgrade-requirements"] as const,
  },

  // Features e Flags
  features: {
    all: () => ["features"] as const,
    detail: (featureKey: string, companyId?: string) =>
      companyId
        ? (["features", featureKey, companyId] as const)
        : (["features", featureKey] as const),
    availability: (featureKey: string) => ["features", "availability", featureKey] as const,
    development: () => ["features", "development"] as const,
  },

  // Histórico de Cobrança
  billing: {
    currentSummary: (companyId?: string) =>
      companyId
        ? (["billing", "current-summary", companyId] as const)
        : (["billing", "current-summary"] as const),
    history: (companyId?: string, limit?: number, offset?: number) =>
      ["billing", "history", companyId, limit, offset] as const,
    details: (billingId: string) => ["billing", "details", billingId] as const,
    addonDetails: (billingId: string | null) =>
      billingId
        ? (["billing", "addon-details", billingId] as const)
        : (["billing", "addon-details"] as const),
    cycles: (companyId?: string) =>
      companyId ? (["billing", "cycles", companyId] as const) : (["billing", "cycles"] as const),
    monthlyTotals: (companyId?: string, year?: number) =>
      ["billing", "monthly-totals", companyId, year] as const,
    financialSummary: (companyId?: string) =>
      companyId
        ? (["billing", "financial-summary", companyId] as const)
        : (["billing", "financial-summary"] as const),
    // Bulk Billing (Geração em Lote)
    bulkPreview: (month?: number, year?: number) =>
      month && year
        ? (["billing", "bulk-preview", month, year] as const)
        : (["billing", "bulk-preview"] as const),
    // Bulk Billing V2 (Versão melhorada)
    bulkPreviewV2: (month?: number, year?: number) =>
      month && year
        ? (["billing", "bulk-preview-v2", month, year] as const)
        : (["billing", "bulk-preview-v2"] as const),
    // Admin - Todas as empresas
    adminHistory: (
      limit?: number,
      offset?: number,
      companyFilter?: string,
      statusFilter?: string
    ) => ["billing", "admin-history", limit, offset, companyFilter, statusFilter] as const,
  },

  // IA e Créditos
  ai: {
    features: () => ["ai", "features"] as const,
    balance: () => ["ai", "balance"] as const,
    usageHistory: (limit = 50, offset = 0) => ["ai", "usage-history", { limit, offset }] as const,
    featureAvailability: (featureKey: string) =>
      ["ai", "feature-availability", featureKey] as const,
    credits: {
      check: (featureKey: string) => ["ai", "credits", "check", featureKey] as const,
      monthly: (monthYear: string) => ["ai", "credits", "monthly", monthYear] as const,
      addons: () => ["ai", "credits", "addons"] as const,
    },
    analytics: {
      usage: (timeframe?: string, params?: Record<string, any>) =>
        timeframe
          ? (["ai", "analytics", "usage", timeframe, params] as const)
          : (["ai", "analytics", "usage", params] as const),
      costs: (provider?: string, periodMonths?: number) =>
        provider
          ? (["ai", "analytics", "costs", provider, periodMonths] as const)
          : (["ai", "analytics", "costs", periodMonths] as const),
      performance: () => ["ai", "analytics", "performance"] as const,
      // Admin analytics (apenas para Vindula)
      admin: () => ["ai", "analytics", "admin"] as const,
      tenants: (monthYear?: string) => ["ai", "analytics", "tenants", monthYear] as const,
      history: (params?: Record<string, any>) => ["ai", "analytics", "history", params] as const,
      export: (type: string, filters?: any) => ["ai", "analytics", "export", type, filters] as const,
    },
  },

  // Ausências (People Hub)
  absences: {
    recentRegistrations: (days: number = 7) => ["absences", "recent-registrations", days] as const,
    all: () => ["absences"] as const,
    types: () => ["absences", "types"] as const,
    list: (filters: Record<string, unknown> = {}) => ["absences", "list", filters] as const,
    myAbsences: (dateRange?: Record<string, unknown>) =>
      dateRange ? (["absences", "my", dateRange] as const) : (["absences", "my"] as const),
    pending: () => ["absences", "pending"] as const,
    calendar: (dateRange?: Record<string, unknown>) =>
      dateRange
        ? (["absences", "calendar", dateRange] as const)
        : (["absences", "calendar"] as const),
    analytics: (timeframe?: string) =>
      timeframe
        ? (["absences", "analytics", timeframe] as const)
        : (["absences", "analytics"] as const),
    byUser: (userId: string) => ["absences", "user", userId] as const,
    byStatus: (status: string) => ["absences", "status", status] as const,
    approvals: () => ["absences", "approvals"] as const,
  },

  // Tipos de Ausência (Absence Types)
  absenceTypes: {
    all: () => ["absence-types"] as const,
    list: () => ["absence-types", "list"] as const,
    detail: (id: string | undefined) => 
      id ? (["absence-types", "detail", id] as const) : (["absence-types", "detail"] as const),
    active: () => ["absence-types", "active"] as const,
    byCompany: (companyId: string) => ["absence-types", "company", companyId] as const,
  },

  // Biblioteca de Documentos
  documents: {
    all: (filters = {}) => ["documents", "list", filters] as const,
    detail: (id: string) => ["documents", "detail", id] as const,
    categories: () => ["documents", "categories"] as const,
    tags: (filterByAuthor = false) => ["documents", "tags", filterByAuthor] as const,
    tagsWithCount: (filterByAuthor = false) =>
      ["documents", "tags-with-count", filterByAuthor] as const,
    search: (term: string) => ["documents", "search", term] as const,
  },

  // Base de Conhecimento
  knowledge: {
    spaces: () => ["knowledge", "spaces"] as const,
    space: (spaceId: string) => ["knowledge", "spaces", spaceId] as const,
    pages: (spaceId?: string, filters?: Record<string, any>) =>
      spaceId
        ? (["knowledge", "pages", spaceId, filters] as const)
        : (["knowledge", "pages", filters] as const),
    page: (pageId: string) => ["knowledge", "pages", pageId] as const,
    categories: () => ["knowledge", "categories"] as const,
    search: (term: string, spaceId?: string) =>
      spaceId
        ? (["knowledge", "search", term, spaceId] as const)
        : (["knowledge", "search", term] as const),
    analytics: (spaceId?: string) =>
      spaceId
        ? (["knowledge", "analytics", spaceId] as const)
        : (["knowledge", "analytics"] as const),
  },

  // Templates de Conhecimento
  knowledgeTemplates: {
    all: (filters?: Record<string, any>) =>
      filters
        ? (["knowledge-templates", "list", filters] as const)
        : (["knowledge-templates", "list"] as const),
    detail: (templateId: string) => ["knowledge-templates", "detail", templateId] as const,
    global: (category?: string) =>
      category
        ? (["knowledge-templates", "global", category] as const)
        : (["knowledge-templates", "global"] as const),
    byCategory: (category: string) => ["knowledge-templates", "category", category] as const,
    combined: (filters?: Record<string, any>) =>
      filters
        ? (["knowledge-templates", "combined", filters] as const)
        : (["knowledge-templates", "combined"] as const),
    categories: () => ["knowledge-templates", "categories"] as const,
    featured: () => ["knowledge-templates", "featured"] as const,
    vindula: () => ["knowledge-templates", "vindula"] as const,
    analytics: () => ["knowledge-templates", "analytics"] as const,
  },

  // Administração
  admin: {
    resourceTypes: () => ["admin", "resource-types"] as const,
    permissionActions: () => ["admin", "permission-actions"] as const,
    defaultPermissions: () => ["admin", "default-permissions"] as const,
    allPermissions: () => ["admin", "all-permissions"] as const,
    stats: () => ["admin", "stats"] as const,
    tenantList: () => ["admin", "tenant-list"] as const,
  },

  // Cartões de Aniversário
  birthdayCards: {
    all: () => ["birthday-cards"] as const,
    received: (userId: string) => ["birthday-cards", "received", userId] as const,
    sent: (userId: string) => ["birthday-cards", "sent", userId] as const,
    detail: (cardId: string) => ["birthday-cards", "detail", cardId] as const,
    unviewed: (userId: string) => ["birthday-cards", "unviewed", userId] as const,
  },

  // Estatísticas
  stats: {
    feed: () => ["stats", "feed"] as const,
    feedWithPeriod: (period: string) => ["stats", "feed", period] as const,
  },

  // Tarefas
  tasks: {
    all: () => ["tasks"] as const,
    assignments: () => ["tasks", "assignments"] as const,
    assignmentRequests: () => ["tasks", "assignment-requests"] as const,
    myRequests: () => ["tasks", "my-requests"] as const,
    receivedRequests: () => ["tasks", "received-requests"] as const,
    workflows: () => ["tasks", "workflows"] as const,
    detail: (taskId: string) => ["tasks", "detail", taskId] as const,
    byUser: (userId: string) => ["tasks", "user", userId] as const,
    byWorkflow: (workflowId: string) => ["tasks", "workflow", workflowId] as const,
  },

  // Sistema Unificado de Emails
  emails: {
    all: () => ["emails"] as const,
    
    // Logs de entrega com filtros avançados
    deliveryLogs: (companyId?: string, filters?: Record<string, any>) =>
      companyId
        ? filters
          ? (["emails", "delivery-logs", companyId, filters] as const)
          : (["emails", "delivery-logs", companyId] as const)
        : (["emails", "delivery-logs"] as const),
    
    // Estatísticas com período customizável
    stats: (companyId?: string, daysBack?: number) =>
      companyId 
        ? daysBack 
          ? (["emails", "stats", companyId, daysBack] as const)
          : (["emails", "stats", companyId] as const)
        : (["emails", "stats"] as const),
    
    // Templates com filtro por tipo
    templates: (companyId?: string, contextType?: string) =>
      companyId
        ? contextType
          ? (["emails", "templates", companyId, contextType] as const)
          : (["emails", "templates", companyId] as const)
        : (["emails", "templates"] as const),
    
    // Sistema contextual expandido
    contextual: {
      ai: () => ["emails", "contextual", "ai"] as const,
      storage: () => ["emails", "contextual", "storage"] as const,
      users: () => ["emails", "contextual", "users"] as const,
      all: () => ["emails", "contextual"] as const,
    },
    
    // Engine unificada
    unified: {
      all: () => ["emails", "unified"] as const,
      byType: (emailType: string) => ["emails", "unified", "type", emailType] as const,
      byRecipient: (email: string) => ["emails", "unified", "recipient", email] as const,
      rateLimit: () => ["emails", "unified", "rate-limit"] as const,
    },
    
    // Analytics avançados
    analytics: {
      delivery: (timeframe?: string, companyId?: string) =>
        timeframe && companyId
          ? (["emails", "analytics", "delivery", timeframe, companyId] as const)
          : timeframe
          ? (["emails", "analytics", "delivery", timeframe] as const)
          : (["emails", "analytics", "delivery"] as const),
      engagement: (timeframe?: string, companyId?: string) =>
        timeframe && companyId
          ? (["emails", "analytics", "engagement", timeframe, companyId] as const)
          : timeframe
          ? (["emails", "analytics", "engagement", timeframe] as const)
          : (["emails", "analytics", "engagement"] as const),
      conversion: (contextType?: string, companyId?: string) =>
        contextType && companyId
          ? (["emails", "analytics", "conversion", contextType, companyId] as const)
          : contextType
          ? (["emails", "analytics", "conversion", contextType] as const)
          : (["emails", "analytics", "conversion"] as const),
      funnel: (companyId?: string) =>
        companyId 
          ? (["emails", "analytics", "funnel", companyId] as const)
          : (["emails", "analytics", "funnel"] as const),
      performance: (period: string, companyId?: string) =>
        companyId
          ? (["emails", "analytics", "performance", period, companyId] as const)
          : (["emails", "analytics", "performance", period] as const),
    },
    
    // Sistema de testes expandido
    testing: {
      all: () => ["emails", "testing"] as const,
      delivery: (contextType: string, testEmails: string[]) =>
        ["emails", "testing", "delivery", contextType, testEmails] as const,
      rendering: (contextType: string, testEmails: string[]) =>
        ["emails", "testing", "rendering", contextType, testEmails] as const,
      links: (contextType: string, testEmails: string[]) =>
        ["emails", "testing", "links", contextType, testEmails] as const,
      comprehensive: (testType: string, contextType: string, testEmails: string[]) =>
        ["emails", "testing", "comprehensive", testType, contextType, testEmails] as const,
      results: (testId: string) => ["emails", "testing", "results", testId] as const,
      history: (companyId?: string) =>
        companyId
          ? (["emails", "testing", "history", companyId] as const)
          : (["emails", "testing", "history"] as const),
      suite: (suiteId: string) => ["emails", "testing", "suite", suiteId] as const,
      ab: (testId: string) => ["emails", "testing", "ab", testId] as const,
    },
    
    // Campanhas e automação
    campaigns: {
      all: (companyId?: string) =>
        companyId 
          ? (["emails", "campaigns", companyId] as const)
          : (["emails", "campaigns"] as const),
      details: (campaignId: string) => ["emails", "campaigns", campaignId] as const,
      analytics: (campaignId: string) => ["emails", "campaigns", campaignId, "analytics"] as const,
      templates: (campaignId: string) => ["emails", "campaigns", campaignId, "templates"] as const,
      segments: (companyId?: string) =>
        companyId 
          ? (["emails", "campaigns", "segments", companyId] as const)
          : (["emails", "campaigns", "segments"] as const),
    },
    
    // Configurações e compliance
    settings: {
      company: (companyId?: string) =>
        companyId 
          ? (["emails", "settings", companyId] as const)
          : (["emails", "settings"] as const),
      templates: (companyId?: string) =>
        companyId 
          ? (["emails", "settings", "templates", companyId] as const)
          : (["emails", "settings", "templates"] as const),
      smtp: (companyId?: string) =>
        companyId 
          ? (["emails", "settings", "smtp", companyId] as const)
          : (["emails", "settings", "smtp"] as const),
      compliance: (companyId?: string) =>
        companyId 
          ? (["emails", "settings", "compliance", companyId] as const)
          : (["emails", "settings", "compliance"] as const),
    },
  },

  // Sistema de Monitoramento Proativo
  monitoring: {
    all: () => ["monitoring"] as const,
    engagementMetrics: () => ["monitoring", "engagement-metrics"] as const,
    riskSignals: () => ["monitoring", "risk-signals"] as const,
    interventions: () => ["monitoring", "interventions"] as const,
    settings: () => ["monitoring", "settings"] as const,
    analytics: () => ["monitoring", "analytics"] as const,
    // Novas funcionalidades implementadas
    dashboardMetrics: () => ["monitoring", "dashboard-metrics"] as const,
    interventionHistory: (params: Record<string, any>) =>
      ["monitoring", "intervention-history", params] as const,
    healthScoreTrends: (daysBack: number) =>
      ["monitoring", "health-score-trends", daysBack] as const,
    activeAlerts: () => ["monitoring", "active-alerts"] as const,
    detection: {
      inactivity: (thresholdHours?: number) =>
        thresholdHours
          ? (["monitoring", "detection", "inactivity", thresholdHours] as const)
          : (["monitoring", "detection", "inactivity"] as const),
      zeroUsage: (thresholdDays?: number) =>
        thresholdDays
          ? (["monitoring", "detection", "zero-usage", thresholdDays] as const)
          : (["monitoring", "detection", "zero-usage"] as const),
      excessiveQuestions: (threshold?: number) =>
        threshold
          ? (["monitoring", "detection", "excessive-questions", threshold] as const)
          : (["monitoring", "detection", "excessive-questions"] as const),
    },
    userMetrics: (userId: string) => ["monitoring", "user-metrics", userId] as const,
    healthScores: () => ["monitoring", "health-scores"] as const,
    reports: {
      daily: (date: string) => ["monitoring", "reports", "daily", date] as const,
      weekly: (week: string) => ["monitoring", "reports", "weekly", week] as const,
      monthly: (month: string) => ["monitoring", "reports", "monthly", month] as const,
    },
  },

  // Calendário e Eventos
  calendar: {
    all: () => ["calendar"] as const,
    events: (filters?: Record<string, any>) =>
      filters ? (["calendar", "events", filters] as const) : (["calendar", "events"] as const),
    eventDetail: (eventId: string) => ["calendar", "events", eventId] as const,
    userEvents: (userId: string, filters?: Record<string, any>) =>
      filters
        ? (["calendar", "events", "user", userId, filters] as const)
        : (["calendar", "events", "user", userId] as const),
    upcomingEvents: (params: {
      limit?: number;
      scope?: string;
      days_ahead?: number;
      include_personal?: boolean;
    }) => ["calendar", "upcomingEvents", params] as const,
    types: () => ["calendar", "types"] as const,
    settings: () => ["calendar", "settings"] as const,
  },
  // Leads Comerciais
  commercialLeads: {
    all: (filters?: Record<string, any>) =>
      filters
        ? (["commercial-leads", "list", filters] as const)
        : (["commercial-leads", "list"] as const),
    detail: (leadId: string) => ["commercial-leads", "detail", leadId] as const,
    stats: () => ["commercial-leads", "stats"] as const,
    byStatus: (status: string) => ["commercial-leads", "status", status] as const,
    bySource: (source: string) => ["commercial-leads", "source", source] as const,
    byUser: (userId: string) => ["commercial-leads", "user", userId] as const,
    search: (term: string) => ["commercial-leads", "search", term] as const,
    activeCourtesy: () => ["commercial-leads", "active-courtesy"] as const,
    riskAnalysis: () => ["commercial-leads", "risk-analysis"] as const,
    pipeline: (timeframe?: string) =>
      timeframe
        ? (["commercial-leads", "pipeline", timeframe] as const)
        : (["commercial-leads", "pipeline"] as const),

    // ===== ADDON FLOW QUERIES =====
    byRequestType: (requestType: string) =>
      ["commercial-leads", "request-type", requestType] as const,
    byPriority: (minPriority: number, maxPriority: number) =>
      ["commercial-leads", "priority", minPriority, maxPriority] as const,
    consolidated: () => ["commercial-leads", "consolidated"] as const,
    consolidationGroup: (groupId: string) =>
      ["commercial-leads", "consolidation-group", groupId] as const,
    decisionPatterns: () => ["commercial-leads", "decision-patterns"] as const,
    urgentLeads: () => ["commercial-leads", "urgent"] as const,
    requestTypeAnalysis: (userId?: string) =>
      userId
        ? (["commercial-leads", "request-type-analysis", userId] as const)
        : (["commercial-leads", "request-type-analysis"] as const),
    history: (leadId: string) => ["commercial-leads", "history", leadId] as const,
    allHistory: (filters?: Record<string, any>) =>
      filters
        ? (["commercial-leads", "all-history", filters] as const)
        : (["commercial-leads", "all-history"] as const),

    analytics: {
      conversion: (period?: string) =>
        period
          ? (["commercial-leads", "analytics", "conversion", period] as const)
          : (["commercial-leads", "analytics", "conversion"] as const),
      sources: () => ["commercial-leads", "analytics", "sources"] as const,
      performance: (startDate?: string, endDate?: string) =>
        startDate && endDate
          ? (["commercial-leads", "analytics", "performance", startDate, endDate] as const)
          : (["commercial-leads", "analytics", "performance"] as const),

      // ===== NOVAS ANALYTICS DO ADDON FLOW =====
      requestTypes: (timeframe?: string) =>
        timeframe
          ? (["commercial-leads", "analytics", "request-types", timeframe] as const)
          : (["commercial-leads", "analytics", "request-types"] as const),
      priorityDistribution: () =>
        ["commercial-leads", "analytics", "priority-distribution"] as const,
      consolidationStats: (period?: string) =>
        period
          ? (["commercial-leads", "analytics", "consolidation", period] as const)
          : (["commercial-leads", "analytics", "consolidation"] as const),
      urgencyTrends: (daysBack = 30) =>
        ["commercial-leads", "analytics", "urgency-trends", daysBack] as const,
      decisionTimelines: (requestType?: string) =>
        requestType
          ? (["commercial-leads", "analytics", "decision-timelines", requestType] as const)
          : (["commercial-leads", "analytics", "decision-timelines"] as const),
    },
  },

  // Sistema de Missões
  missions: {
    all: () => ["missions"] as const,
    list: (filters?: Record<string, any>) =>
      filters ? (["missions", "list", filters] as const) : (["missions", "list"] as const),
    detail: (missionId: string) => ["missions", "detail", missionId] as const,
    statistics: () => ["missions", "statistics"] as const,
    userMissions: (userId?: string, filters?: Record<string, any>) =>
      userId
        ? (["missions", "user", userId, filters] as const)
        : (["missions", "user", "current", filters] as const),
    assignable: (userId?: string, missionType?: string) =>
      ["missions", "assignable", userId, missionType] as const,
    weeklyTemplates: () => ["missions", "weekly-templates"] as const,
    weeklyTemplate: (templateId: string) => ["missions", "weekly-templates", templateId] as const,
    
    // Templates globais (Vindula)
    globalTemplates: {
      _def: ["missions", "global-templates"] as const,
      list: (filters?: Record<string, any>) =>
        filters 
          ? (["missions", "global-templates", "list", filters] as const) 
          : (["missions", "global-templates", "list"] as const),
      detail: (templateId: string) => ["missions", "global-templates", "detail", templateId] as const,
      stats: () => ["missions", "global-templates", "stats"] as const,
    },
    
    // Templates incluindo globais
    templates: {
      _def: ["missions", "templates"] as const,
      withGlobal: (companyId?: string, includeGlobal?: boolean) =>
        ["missions", "templates", "with-global", companyId, includeGlobal] as const,
      company: (companyId: string) => ["missions", "templates", "company", companyId] as const,
    },
  },

  // Add-ons (Pacotes de Expansão)
  addons: {
    all: () => ["addons"] as const,
    detail: (addonId: string) => ["addons", "detail", addonId] as const,
    byType: (type: "user_pack" | "storage_pack" | "ai_credits") =>
      ["addons", "type", type] as const,
    active: () => ["addons", "active"] as const,
    popular: () => ["addons", "popular"] as const,
    recommended: (context?: string) =>
      context
        ? (["addons", "recommended", context] as const)
        : (["addons", "recommended"] as const),
    pricing: () => ["addons", "pricing"] as const,
    orderBump: (primaryType: string) => ["addons", "order-bump", primaryType] as const,
    analytics: {
      sales: (period?: string) =>
        period
          ? (["addons", "analytics", "sales", period] as const)
          : (["addons", "analytics", "sales"] as const),
      conversion: (source?: string) =>
        source
          ? (["addons", "analytics", "conversion", source] as const)
          : (["addons", "analytics", "conversion"] as const),
      popular: (timeframe?: string) =>
        timeframe
          ? (["addons", "analytics", "popular", timeframe] as const)
          : (["addons", "analytics", "popular"] as const),
    },
  },

  // Marketplace Estratégico
  marketplace: {
    // Categorias estratégicas
    strategicCategories: (companyId: string) =>
      ["marketplace", "strategic-categories", companyId] as const,
    strategicCategory: (id: string) => ["marketplace", "strategic-category", id] as const,

    // Itens estratégicos
    strategicItems: (categoryId: string, userId?: string) =>
      ["marketplace", "strategic-items", categoryId, userId] as const,
    strategicItem: (id: string) => ["marketplace", "strategic-item", id] as const,

    // Compras
    purchaseHistory: (userId?: string, limit?: number, offset?: number) =>
      ["marketplace", "purchases", userId, limit, offset] as const,

    // User Purchases (Histórico do usuário)
    userPurchases: {
      list: (userId?: string) => ["marketplace", "user-purchases", userId] as const,
      details: (purchaseId: string) => ["marketplace", "user-purchases", "details", purchaseId] as const,
    },

    // Ofertas especiais
    specialOffers: (companyId: string) => ["marketplace", "special-offers", companyId] as const,
    specialOffer: (id: string) => ["marketplace", "special-offer", id] as const,
    activeOffers: (companyId: string) =>
      ["marketplace", "special-offers", "active", companyId] as const,
    offerUsage: (offerId: string) => ["marketplace", "special-offer", "usage", offerId] as const,

    // Estatísticas
    stats: (companyId: string) => ["marketplace", "stats", companyId] as const,

    // Catchall
    all: () => ["marketplace"] as const,
  } as const,

  // Chat e Mensagens
  chat: {
    messages: (channelId: string, chatId?: string) =>
      chatId ? (["chat", "messages", channelId, chatId] as const) : (["chat", "messages", channelId] as const),
    channels: () => ["chat", "channels"] as const,
    channel: (channelId: string) => ["chat", "channels", channelId] as const,
    unreadCount: (channelId: string) => ["chat", "unread-count", channelId] as const,
    lastMessage: (channelId: string) => ["chat", "last-message", channelId] as const,
    participants: (chatId: string) => ["chat", "participants", chatId] as const,
    
    // Read Receipts - Issue #13
    readReceipts: (messageId: string) => ["chat", "read-receipts", messageId] as const,
    messageReadStatus: (messageId: string, userId: string) => 
      ["chat", "message-read-status", messageId, userId] as const,
    unreadMessages: (channelId: string, userId?: string) => 
      ["chat", "unread-messages", channelId, userId] as const,
    channelReadStatus: (channelId: string) => ["chat", "channel-read-status", channelId] as const,
  } as const,

  // Auditoria
  audit: {
    all: () => ["audit"] as const,
    logs: (filters?: Record<string, any>) => ["audit", "logs", filters] as const,
    visualAssets: (filters?: Record<string, any>) => ["audit", "visual-assets", filters] as const,
    visualAssetHistory: (assetId: string) => ["audit", "visual-assets", "history", assetId] as const,
    visualAssetsStats: () => ["audit", "visual-assets", "stats"] as const,
    isVisualAssetDeleted: (assetId: string) => ["audit", "visual-assets", "deleted", assetId] as const,
    userActions: (userId: string) => ["audit", "user-actions", userId] as const,
    recentActions: (hours: number = 24) => ["audit", "recent-actions", hours] as const,
  } as const,

  // Audit Logs (Sistema de Auditoria)
  auditLogs: {
    all: () => ["auditLogs"] as const,
    list: (companyId?: string, filters?: any) => 
      [...QueryKeys.auditLogs.all(), "list", companyId, filters] as const,
    filtered: (filters: Record<string, any>, page: number, pageSize: number) =>
      [...QueryKeys.auditLogs.all(), "filtered", filters, page, pageSize] as const,
    stats: (companyId?: string) => 
      [...QueryKeys.auditLogs.all(), "stats", companyId] as const,
    detail: (id: string) => 
      [...QueryKeys.auditLogs.all(), "detail", id] as const,
    byEventType: (eventType: string, companyId?: string) =>
      [...QueryKeys.auditLogs.all(), "eventType", eventType, companyId] as const,
    byTable: (tableName: string, companyId?: string) =>
      [...QueryKeys.auditLogs.all(), "table", tableName, companyId] as const,
    byUser: (userId: string, companyId?: string) =>
      [...QueryKeys.auditLogs.all(), "user", userId, companyId] as const,
    recent: (hours: number = 24, companyId?: string) =>
      [...QueryKeys.auditLogs.all(), "recent", hours, companyId] as const,
  } as const,

  // Floating Tab Bar
  floatingTabBar: {
    all: () => ["floating-tab-bar"] as const,
    settings: (companyId: string, userId: string) => 
      ["floating-tab-bar", "settings", companyId, userId] as const,
    config: (companyId: string) => 
      ["floating-tab-bar", "config", companyId] as const,
  } as const,

  // Portal de Privacidade LGPD
  privacyRequests: {
    all: () => ["privacy-requests"] as const,
    list: (userId?: string, companyId?: string) =>
      userId && companyId
        ? (["privacy-requests", "list", userId, companyId] as const)
        : (["privacy-requests", "list"] as const),
    admin: (companyId?: string) =>
      companyId
        ? (["privacy-requests", "admin", companyId] as const)
        : (["privacy-requests", "admin"] as const),
    detail: (requestId: string) => ["privacy-requests", "detail", requestId] as const,
    stats: (companyId?: string) =>
      companyId
        ? (["privacy-requests", "stats", companyId] as const)
        : (["privacy-requests", "stats"] as const),
    byStatus: (status: string, companyId?: string) =>
      ["privacy-requests", "status", status, companyId] as const,
    byType: (type: string, companyId?: string) =>
      ["privacy-requests", "type", type, companyId] as const,
  } as const,

  // Conformidade LGPD
  privacyCompliance: {
    all: (companyId?: string) =>
      companyId
        ? (["privacy-compliance", "all", companyId] as const)
        : (["privacy-compliance", "all"] as const),
    score: (companyId?: string) =>
      companyId
        ? (["privacy-compliance", "score", companyId] as const)
        : (["privacy-compliance", "score"] as const),
    recommendations: (companyId?: string) =>
      companyId
        ? (["privacy-compliance", "recommendations", companyId] as const)
        : (["privacy-compliance", "recommendations"] as const),
  } as const,

  // Configurações de Privacidade
  privacySettings: {
    all: () => ["privacy-settings"] as const,
    current: (userId?: string, companyId?: string) =>
      userId && companyId
        ? (["privacy-settings", "current", userId, companyId] as const)
        : (["privacy-settings", "current"] as const),
    history: (userId?: string, companyId?: string) =>
      userId && companyId
        ? (["privacy-settings", "history", userId, companyId] as const)
        : (["privacy-settings", "history"] as const),
    stats: (companyId?: string) =>
      companyId
        ? (["privacy-settings", "stats", companyId] as const)
        : (["privacy-settings", "stats"] as const),
  } as const,

  // Histórico de Consentimentos
  consentHistory: {
    all: () => ["consent-history"] as const,
    list: (userId?: string, companyId?: string) =>
      userId && companyId
        ? (["consent-history", "list", userId, companyId] as const)
        : (["consent-history", "list"] as const),
    byType: (userId?: string, companyId?: string, consentType?: string) =>
      userId && companyId && consentType
        ? (["consent-history", "type", userId, companyId, consentType] as const)
        : userId && companyId
        ? (["consent-history", "type", userId, companyId] as const)
        : (["consent-history", "type"] as const),
    user: (userId: string, companyId?: string) =>
      ["consent-history", "user", userId, companyId] as const,
    stats: (companyId?: string) =>
      companyId
        ? (["consent-history", "stats", companyId] as const)
        : (["consent-history", "stats"] as const),
    current: (userId: string, consentType: string) =>
      ["consent-history", "current", userId, consentType] as const,
  } as const,

  // Exportação de Dados
  dataExport: {
    all: () => ["data-export"] as const,
    list: (userId?: string, companyId?: string) =>
      userId && companyId
        ? (["data-export", "list", userId, companyId] as const)
        : (["data-export", "list"] as const),
    detail: (exportId: string) => ["data-export", "detail", exportId] as const,
    stats: (companyId?: string) =>
      companyId
        ? (["data-export", "stats", companyId] as const)
        : (["data-export", "stats"] as const),
    ready: (userId?: string) =>
      userId
        ? (["data-export", "ready", userId] as const)
        : (["data-export", "ready"] as const),
    byStatus: (status: string, companyId?: string) =>
      ["data-export", "status", status, companyId] as const,
  } as const,
};

// Export das query keys específicas para facilitar importação
export const missionQueryKeys = QueryKeys.missions;
export const privacyQueryKeys = {
  privacyRequests: QueryKeys.privacyRequests,
  privacySettings: QueryKeys.privacySettings,
  consentHistory: QueryKeys.consentHistory,
  dataExport: QueryKeys.dataExport,
};

// Export principal para facilitar uso
export const queryKeys = QueryKeys;
