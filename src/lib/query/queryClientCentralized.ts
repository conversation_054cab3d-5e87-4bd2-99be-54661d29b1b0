/**
 * Query Client Centralizado com Sistema de Cache Integrado
 * <AUTHOR> Internet 2025
 * @description Nova arquitetura de cache centralizada com Domain Strategies
 */

import { QueryClient } from '@tanstack/react-query';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';
import { persistQueryClient } from '@tanstack/query-persist-client-core';
import { openDB } from 'idb';
import { supabase } from '@/integrations/supabase/client';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

// Importar sistema de cache centralizado
import { setupCacheSystem, getDefaultQueryClientConfig, setupCacheDebug } from '@/lib/cache/config/setupCache';
import { CacheService } from '@/lib/cache/core/CacheService';
import type { ICacheService } from '@/lib/cache/types';

/**
 * Instâncias globais do sistema centralizado
 */
let globalQueryClient: QueryClient | null = null;
let globalCacheService: ICacheService | null = null;

/**
 * Configuração inteligente de localStorage com offline-first
 */
const getCacheKey = (): string => {
  try {
    const tokenKey = Object.keys(localStorage).find(key => 
      key.startsWith('sb-') && key.endsWith('-auth-token')
    );

    if (tokenKey) {
      try {
        const tokenData = JSON.parse(localStorage.getItem(tokenKey) || '{}');
        if (tokenData.user?.id) {
          return `VINDULA_CACHE_CENTRALIZED_${tokenData.user.id}`;
        }
      } catch (error) {
        console.error('[CacheSystem] Erro ao processar token:', error);
      }
    }
  } catch (error) {
    console.error('[CacheSystem] Erro ao acessar localStorage:', error);
  }

  return 'VINDULA_CACHE_CENTRALIZED_anonymous';
};

/**
 * Logout inteligente - padrão WhatsApp Web
 * Preserva dados não-sensíveis da empresa
 */
const intelligentLogoutCleanup = () => {
  try {
    console.log('[CacheSystem] Iniciando logout inteligente...');

    const keys = Object.keys(localStorage);
    
    // Dados que devem ser PRESERVADOS no logout
    const preservePatterns = [
      'company_settings',
      'public_domains',
      'app_config',
      'feature_flags', 
      'translations',
      'ui_preferences',
      'theme_settings'
    ];

    // Dados que devem ser REMOVIDOS no logout
    const removePatterns = [
      'VINDULA_CACHE_CENTRALIZED_',
      'user_',
      'profile_',
      'stardust_',
      'notifications_',
      'chat_',
      'personal_'
    ];

    let removedCount = 0;
    let preservedCount = 0;

    keys.forEach(key => {
      const shouldPreserve = preservePatterns.some(pattern => 
        key.includes(pattern)
      );

      const shouldRemove = removePatterns.some(pattern => 
        key.startsWith(pattern)
      );

      if (shouldRemove && !shouldPreserve) {
        localStorage.removeItem(key);
        removedCount++;
      } else if (shouldPreserve) {
        preservedCount++;
      }
    });

    console.log(`[CacheSystem] Logout concluído: ${removedCount} removidas, ${preservedCount} preservadas`);
  } catch (error) {
    console.error('[CacheSystem] Erro no logout inteligente:', error);
  }
};

/**
 * Criar storage IndexedDB para ultrapassar limitação de 5MB do localStorage
 */
const createIndexedDBStorage = async () => {
  const dbName = 'vindulacosmos';
  const storeName = getCacheKey(); // query_cache_${userId}
  
  try {
    const db = await openDB(dbName, 1, {
      upgrade(db) {
        if (!db.objectStoreNames.contains(storeName)) {
          db.createObjectStore(storeName);
        }
      },
    });

    return {
      getItem: async (key: string) => {
        try {
          const result = await db.get(storeName, key);
          return result || null;
        } catch (error) {
          console.error('[IndexedDB] Erro ao recuperar item:', error);
          return null;
        }
      },
      setItem: async (key: string, value: string) => {
        try {
          await db.put(storeName, value, key);
        } catch (error) {
          console.error('[IndexedDB] Erro ao salvar item:', error);
        }
      },
      removeItem: async (key: string) => {
        try {
          await db.delete(storeName, key);
        } catch (error) {
          console.error('[IndexedDB] Erro ao remover item:', error);
        }
      },
    };
  } catch (error) {
    console.error('[IndexedDB] Erro ao inicializar banco:', error);
    // Fallback para localStorage em caso de erro
    return {
      getItem: (key: string) => Promise.resolve(localStorage.getItem(key)),
      setItem: (key: string, value: string) => Promise.resolve(localStorage.setItem(key, value)),
      removeItem: (key: string) => Promise.resolve(localStorage.removeItem(key)),
    };
  }
};

/**
 * Persister customizado para IndexedDB compatível com TanStack Query v5
 */
const createIndexedDBPersister = async () => {
  const indexedDBStorage = await createIndexedDBStorage();
  
  return {
    persistClient: async (persistedClient: any) => {
      try {
        await indexedDBStorage.setItem('tanstack-query-cache', JSON.stringify(persistedClient));
        logQueryEvent('IndexedDBPersister', 'Cache persistido com sucesso no IndexedDB');
      } catch (error) {
        logQueryEvent('IndexedDBPersister', 'Erro ao persistir cache:', error, 'error');
      }
    },
    restoreClient: async () => {
      try {
        const cached = await indexedDBStorage.getItem('tanstack-query-cache');
        if (cached) {
          logQueryEvent('IndexedDBPersister', 'Cache restaurado com sucesso do IndexedDB');
          return JSON.parse(cached);
        }
        return null;
      } catch (error) {
        logQueryEvent('IndexedDBPersister', 'Erro ao restaurar cache:', error, 'error');
        return null;
      }
    },
    removeClient: async () => {
      try {
        await indexedDBStorage.removeItem('tanstack-query-cache');
        logQueryEvent('IndexedDBPersister', 'Cache removido do IndexedDB');
      } catch (error) {
        logQueryEvent('IndexedDBPersister', 'Erro ao remover cache:', error, 'error');
      }
    }
  };
};

/**
 * Política de persistência menos restritiva para offline
 */
const createOfflineFirstDehydrationPolicy = () => ({
  shouldDehydrateQuery: (query: any) => {
    // Permitir persistir mais estados para funcionalidade offline
    if (query.state?.status === 'error') {
      // Só bloquear erros críticos, não todos os erros
      const isCriticalError = query.state.error?.message?.includes('403') ||
                             query.state.error?.message?.includes('unauthorized');
      return !isCriticalError;
    }

    // Permitir persistir estados loading para offline
    if (query.state?.status === 'pending') {
      return false; // Ainda bloquear pending para evitar problemas
    }

    // Permitir persistir dados nulos/vazios válidos
    if (query.state?.data === null || query.state?.data === undefined) {
      // Se o estado é success mas data é null, pode ser um resultado válido
      return query.state?.status === 'success';
    }

    // Política menos restritiva para dados sensíveis
    const sensitiveKeys = ['password', 'secret', 'private_key'];
    
    try {
      if (!query.queryKey || !Array.isArray(query.queryKey)) {
        return false;
      }
      
      const containsCriticalSensitiveKey = sensitiveKeys.some(key => 
        query.queryKey.some((k: any) => 
          typeof k === 'string' && k.toLowerCase().includes(key)
        )
      );

      return !containsCriticalSensitiveKey;
    } catch (e) {
      return false;
    }
  }
});

/**
 * Criar Query Client com sistema centralizado
 */
export async function createCentralizedQueryClient() {
  // Usar configuração padrão do sistema de cache
  const config = getDefaultQueryClientConfig();
  
  // Aplicar melhorias offline-first
  const offlineFirstConfig = {
    ...config,
    defaultOptions: {
      ...config.defaultOptions,
      queries: {
        ...config.defaultOptions.queries,
        // Configurações offline-first
        networkMode: 'offlineFirst' as const,
        staleTime: 15 * 60 * 1000, // 15 minutos - mais tempo para offline
        gcTime: 60 * 60 * 1000, // 1 hora - manter mais tempo na memória
        refetchOnMount: false, // Não sempre buscar rede
        refetchOnWindowFocus: true, // Mas atualizar ao focar
        refetchOnReconnect: true, // Atualizar ao reconectar
        retry: (failureCount, error: any) => {
          // Não fazer retry se estiver offline
          if (!navigator.onLine) return false;
          // Retry normal se online
          return failureCount < 2;
        },
      },
    },
  };

  const queryClient = new QueryClient(offlineFirstConfig);

  // Configurar sistema de cache centralizado
  const { cacheService, cacheWarmer } = setupCacheSystem(queryClient);

  // Configurar persistência offline-first com IndexedDB
  try {
    const persister = await createIndexedDBPersister();
    
    persistQueryClient({
      queryClient,
      persister,
      maxAge: 24 * 60 * 60 * 1000, // 24 horas
      buster: import.meta.env.VITE_APP_VERSION || 'v1',
      hydrateOptions: {
        deserializeData: (data) => {
          try {
            return typeof data === 'string' ? JSON.parse(data) : data;
          } catch (error) {
            logQueryEvent('CentralizedCache', 'Erro ao deserializar cache', error, 'warn');
            return null;
          }
        },
      },
      dehydrateOptions: createOfflineFirstDehydrationPolicy(),
    });
    
    logQueryEvent('CentralizedCache', 'IndexedDB persister configurado com sucesso');
  } catch (error) {
    logQueryEvent('CentralizedCache', 'Erro ao configurar IndexedDB persister:', error, 'error');
  }

  // Configurar eventos de autenticação com sistema centralizado
  supabase.auth.onAuthStateChange(async (event, session) => {
    logQueryEvent('CentralizedCache', `Evento de auth: ${event}`);

    if (event === 'SIGNED_IN' || event === 'USER_UPDATED') {
      // Notificar sistema de cache sobre login
      if (session?.user) {
        cacheService.eventBus?.emit('auth.login', {
          userId: session.user.id,
          companyId: (session.user as any).user_metadata?.company_id,
        });

        // Warm cache essencial
        cacheService.eventBus?.emit('cache.warm.login', {
          userId: session.user.id,
          companyId: (session.user as any).user_metadata?.company_id,
        });
      }

    } else if (event === 'SIGNED_OUT') {
      logQueryEvent('CentralizedCache', 'Logout: Iniciando limpeza inteligente...');

      try {
        // Notificar sistema de cache sobre logout
        cacheService.eventBus?.emit('auth.logout', {});

        // Cancelar queries pendentes
        await queryClient.cancelQueries();
        
        // Limpar cache do React Query
        queryClient.clear();

        // Limpeza inteligente do localStorage (padrão WhatsApp)
        intelligentLogoutCleanup();

        // Resetar dados críticos
        queryClient.setQueryData(['users', 'current'], null);
        queryClient.setQueryData(['users', 'currentRoles'], []);

        logQueryEvent('CentralizedCache', 'Logout inteligente concluído');
      } catch (error) {
        logQueryEvent('CentralizedCache', 'Erro durante logout', error, 'error');
      }
    }
  });

  // Configurar debug em desenvolvimento
  if (import.meta.env.DEV) {
    setupCacheDebug(true);
  }

  // Armazenar instâncias globais
  globalQueryClient = queryClient;
  globalCacheService = cacheService;

  console.log('[CacheSystem] Query Client Centralizado inicializado ✅');

  return { queryClient, cacheService, cacheWarmer };
}

/**
 * Obter instâncias globais (para hooks e componentes)
 */
export async function getCentralizedQueryClient(): Promise<QueryClient> {
  if (!globalQueryClient) {
    const { queryClient } = await createCentralizedQueryClient();
    return queryClient;
  }
  return globalQueryClient;
}

export async function getCentralizedCacheService(): Promise<ICacheService> {
  if (!globalCacheService) {
    const { cacheService } = await createCentralizedQueryClient();
    return cacheService;
  }
  return globalCacheService;
}

/**
 * Hook para usar o sistema de cache centralizado
 */
export function useCacheService(): ICacheService {
  if (!globalCacheService) {
    throw new Error('CacheService não foi inicializado. Use initializeCentralizedQueryClient() primeiro.');
  }
  return globalCacheService;
}

/**
 * Hook para detectar estado online/offline
 */
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      logQueryEvent('NetworkStatus', 'Conexão restaurada');
    };
    
    const handleOffline = () => {
      setIsOnline(false);
      logQueryEvent('NetworkStatus', 'Conexão perdida - modo offline');
    };
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  return { isOnline, isOffline: !isOnline };
}

// Adicionar import necessário
import { useState, useEffect } from 'react';

/**
 * Função para inicializar sistema centralizado
 */
export async function initializeCentralizedQueryClient() {
  if (!globalQueryClient || !globalCacheService) {
    const result = await createCentralizedQueryClient();
    return result;
  }
  return { 
    queryClient: globalQueryClient, 
    cacheService: globalCacheService, 
    cacheWarmer: null 
  };
}

/**
 * Função sync para obter queryClient após inicialização
 */
export function getQueryClient(): QueryClient {
  if (!globalQueryClient) {
    throw new Error('QueryClient não foi inicializado. Use initializeCentralizedQueryClient() primeiro.');
  }
  return globalQueryClient;
}

/**
 * Export padrão - getter sincronizado
 */
export default getQueryClient;