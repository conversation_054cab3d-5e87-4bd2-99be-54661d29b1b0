/**
 * Utilitários para manipulação e exibição de ações de gamificação no Vindula Cosmos.
 * Contém funções para obter ícones, nomes amigáveis e links para diferentes tipos de ações.
 * <AUTHOR> Internet 2025
 */
import { 
  Scroll, 
  ThumbsUp, 
  MessageSquare, 
  FileText, 
  UserCircle, 
  Calendar, 
  Star,
  ExternalLink,
  BookOpen,
  Package,
  Layout
} from "lucide-react";
import { Link } from "react-router-dom";
import { ExperienceHistory } from "@/types/gamification.types";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";

/**
 * Constantes para ações de XP e seus respectivos bônus
 */
export const XP_ACTIONS = [
  { action: "Criar publicação", xp: 25, bonus: "Primeira ação do dia: +50%" },
  { action: "Receber like", xp: 10, bonus: "Primeira ação do dia: +50%" },
  { action: "Comentar", xp: 15, bonus: "Primeira ação do dia: +50%" },
  { action: "Receber comentário", xp: 8, bonus: "Primeira ação do dia: +50%" },
  {
    action: "Upload de documento",
    xp: 30,
    bonus: "Primeira ação do dia: +50%",
  },
  {
    action: "Comentar em documento",
    xp: 20,
    bonus: "Primeira ação do dia: +50%",
  },
  {
    action: "Login diário",
    xp: 50,
    bonus: "Streak: 7 dias (+20%), 30 dias (+50%), 365 dias (+100%)",
  },
  { action: "Completar perfil", xp: 100, bonus: "Uma vez" },
  // Knowledge Hub actions
  { action: "Criar página de conhecimento", xp: 75, bonus: "Primeira ação do dia: +50%" },
  { action: "Criar espaço de conhecimento", xp: 100, bonus: "Primeira ação do dia: +50%" },
  { action: "Criar template de conhecimento", xp: 50, bonus: "Primeira ação do dia: +50%" },
];

/**
 * Retorna o ícone adequado para cada tipo de ação
 * @param actionType Tipo da ação
 * @returns Componente React com o ícone correspondente
 */
export function getActionIcon(actionType: string) {
  switch (actionType) {
    case 'post_created':
      return <Scroll className="h-4 w-4 text-blue-500" />;
    case 'post_liked':
      return <ThumbsUp className="h-4 w-4 text-pink-500" />;
    case 'comment_created':
      return <MessageSquare className="h-4 w-4 text-green-500" />;
    case 'comment_received':
      return <MessageSquare className="h-4 w-4 text-purple-500" />;
    case 'document_uploaded':
      return <FileText className="h-4 w-4 text-amber-500" />;
    case 'profile_updated':
      return <UserCircle className="h-4 w-4 text-indigo-500" />;
    case 'daily_login':
      return <Calendar className="h-4 w-4 text-teal-500" />;
    // Knowledge Hub actions
    case 'knowledge_page_created':
      return <BookOpen className="h-4 w-4 text-emerald-500" />;
    case 'knowledge_space_created':
      return <Package className="h-4 w-4 text-blue-600" />;
    case 'knowledge_template_created':
      return <Layout className="h-4 w-4 text-purple-600" />;
    // Knowledge Hub medals
    case 'knowledge_pages':
      return <BookOpen className="h-4 w-4 text-emerald-600" />;
    case 'knowledge_spaces':
      return <Package className="h-4 w-4 text-blue-700" />;
    case 'knowledge_templates':
      return <Layout className="h-4 w-4 text-purple-700" />;
    default:
      return <Star className="h-4 w-4 text-yellow-500" />;
  }
}

/**
 * Traduz o tipo de ação para um nome amigável em português
 * @param actionType Tipo da ação
 * @returns Nome amigável da ação
 */
export function getActionName(actionType: string) {
  switch (actionType) {
    case 'post_created':
      return 'Publicação criada';
    case 'post_liked':
      return 'Publicação curtida';
    case 'comment_created':
      return 'Comentário adicionado';
    case 'comment_received':
      return 'Comentário recebido';
    case 'document_uploaded':
      return 'Documento carregado';
    case 'profile_updated':
      return 'Perfil atualizado';
    case 'daily_login':
      return 'Login diário';
    case 'achievement_unlocked':
      return 'Conquista desbloqueada';
    // Knowledge Hub actions
    case 'knowledge_page_created':
      return 'Página de conhecimento criada';
    case 'knowledge_space_created':
      return 'Espaço de conhecimento criado';
    case 'knowledge_template_created':
      return 'Template de conhecimento criado';
    // Knowledge Hub missions
    case 'knowledge_pages':
      return 'Páginas de conhecimento';
    case 'knowledge_spaces':
      return 'Espaços de conhecimento';
    case 'knowledge_templates':
      return 'Templates de conhecimento';
    default:
      return actionType;
  }
}

/**
 * Renderiza links para recursos relacionados com base nos metadados da ação
 * @param exp Objeto de histórico de experiência
 * @returns Componente React com o link correspondente ou null
 */
export function renderMetadataLink(exp: ExperienceHistory) {
  const { metadata, action_type } = exp;
  
  if (!metadata) return null;
  
  try {
    // Extrair IDs ou referências dos metadados
    const postId = metadata.post_id || metadata.id;
    const documentId = metadata.document_id;
    const commentId = metadata.comment_id;
    const knowledgePageId = metadata.knowledge_page_id;
    const knowledgeSpaceId = metadata.knowledge_space_id;
    const knowledgeTemplateId = metadata.knowledge_template_id;
    
    if (action_type === 'post_created' || action_type === 'post_liked') {
      return (
        <Link to={`/feed/post/${postId}`} className="flex items-center gap-1 text-primary hover:underline">
          <span>Ver publicação</span>
          <ExternalLink className="h-3 w-3" />
        </Link>
      );
    }
    
    if (action_type === 'comment_created' || action_type === 'comment_received') {
      return (
        <Link to={`/post/${postId}?comment=${commentId}`} className="flex items-center gap-1 text-primary hover:underline">
          <span>Ver comentário</span>
          <ExternalLink className="h-3 w-3" />
        </Link>
      );
    }
    
    if (action_type === 'document_uploaded') {
      return (
        <Link to={`/library/document/${documentId}`} className="flex items-center gap-1 text-primary hover:underline">
          <span>Ver documento</span>
          <ExternalLink className="h-3 w-3" />
        </Link>
      );
    }
    
    // Knowledge Hub actions
    if (action_type === 'knowledge_page_created' && knowledgePageId) {
      return (
        <Link to={`/knowledge/page/${knowledgePageId}`} className="flex items-center gap-1 text-primary hover:underline">
          <span>Ver página</span>
          <ExternalLink className="h-3 w-3" />
        </Link>
      );
    }
    
    if (action_type === 'knowledge_space_created' && knowledgeSpaceId) {
      return (
        <Link to={`/knowledge/space/${knowledgeSpaceId}`} className="flex items-center gap-1 text-primary hover:underline">
          <span>Ver espaço</span>
          <ExternalLink className="h-3 w-3" />
        </Link>
      );
    }
    
    if (action_type === 'knowledge_template_created' && knowledgeTemplateId) {
      return (
        <Link to={`/knowledge/templates/${knowledgeTemplateId}`} className="flex items-center gap-1 text-primary hover:underline">
          <span>Ver template</span>
          <ExternalLink className="h-3 w-3" />
        </Link>
      );
    }
  } catch (error) {
    logQueryEvent('actionUtils', 'Erro ao renderizar link de metadados', { error, exp }, 'error');
  }
  
  // Para outros tipos de ação onde não temos um link específico
  return null;
}
