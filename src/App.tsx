import { Browser<PERSON>outer, Route, Routes, Navigate, useNavigate } from "react-router-dom";
import Index from "./pages/Index";
import LandingConversao from "./pages/LandingConversao";
import LandingComparacaoReal from "./pages/LandingComparacaoReal";
import Auth from "./pages/Auth";
import Register from "./pages/Register";
import RegisterDirect from "./pages/RegisterDirect";
import ResetPassword from "./pages/ResetPassword";
import Plans from "./pages/Plans";
import Profile from "./pages/Profile";
import UserProfile from "./pages/UserProfile";
import Feed from "./pages/Feed";
import Post from "./pages/Post";
import Chat from "./pages/Chat";
import ChatPreview from "./pages/ChatPreview";
import ScheduledPosts from "./pages/ScheduledPosts";
import { TeamView } from "./components/team/TeamView";
import { TeamsList } from "./components/team/TeamsList";
import { TeamDetail } from "./components/team/TeamDetail";
import { TeamIntelligence } from "./components/team/TeamIntelligence";
import Notifications from "./pages/Notifications";
import { FloatingHeartProvider, FloatingHeartContainer } from "./contexts/FloatingHeartContext";
import { FloatingChatsProvider } from "./contexts/FloatingChatsContext";
// import { RealtimeNotificationsProvider } from "./contexts/RealtimeNotificationsContext"; // TEMPORÁRIO: Desabilitado para teste do POC
import { UnifiedRealtimeProvider } from "./contexts/UnifiedRealtimeProvider"; // POC: Sistema unificado de realtime
import { SearchProvider } from "./contexts/search-context";
import { SearchDialog } from "./components/search";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/sonner"; // MODIFICADO: Importar o Toaster customizado
import { ProtectedRoute } from "./components/auth/ProtectedRoute";
import { ProtectedRouteSimple } from "./components/auth/ProtectedRouteSimple";
import { FeatureProtectedRoute } from "./components/auth/FeatureProtectedRoute";
// A inicialização do authStore agora é feita no componente Loading.tsx
import { Library } from "./pages/Library";
import { LibraryCategories } from "./pages/LibraryCategories";
import { CompanySetup } from "./pages/CompanySetup";
import Progress from "./pages/Progress";
import Marketplace from "./pages/Marketplace";
import Tasks from "./pages/Tasks";
import TasksSimplified from "./pages/TasksSimplified";
import TasksNew from "./pages/TasksNew";
import VisualAssets from "./pages/admin/VisualAssets";
import GamificationAdmin from "./pages/admin/GamificationAdmin";
import StoreItems from "./pages/admin/StoreItems";
import { PostItProvider } from "@/contexts/PostItContext";
import { UsersProvider } from "@/contexts/UsersContext";
import { PostItContainer } from "@/components/post-it/PostItContainer";
import { SidebarTools } from "@/components/ui/SidebarTools";
import PlanComparison from "./pages/PlanComparison";
import Loading from "./pages/Loading";
import { useEffect, useState, useRef } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuthManager } from "@/hooks/useAuthManager";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
// Importando componentes do Sistema de Leitura Obrigatória
import ObligationsPage from "./pages/obligations";
import ObligationReaderPage from "./pages/obligations/read/[documentId]";
import ManageObligationsPage from "./pages/manage-obligations"; // Adicionado
import ManageObligationDetailsPage from "./pages/manage-obligations/[id]"; // Adicionado
import AdminUsers from "./pages/admin/AdminUsers";
import AdminRoles from "./pages/admin/AdminRoles";
import AdminCompany from "./pages/admin/AdminCompany";
import AdminOAuth from "./pages/admin/AdminOAuth";
import AdminPublicDomains from "./pages/admin/AdminPublicDomains";
import OAuthTest from "./pages/auth/OAuthTest";
import AdminHR from "./pages/admin/AdminHR";
import AdminAll from "./pages/admin/AdminAll";
import AdminDepartments from "./pages/admin/AdminDepartments";
import AdminJobTitles from "./pages/admin/AdminJobTitles";
import AdminUnits from "./pages/admin/AdminUnits";
import AdminLocations from "./pages/admin/AdminLocations";
import AdminPromotions from "./pages/admin/AdminPromotions";
import AdminAbsences from "./pages/admin/AdminAbsences";
import StorageReportPage from "./pages/admin/storage-report";
import { StorageAuditPage } from "./pages/admin/StorageAuditPage";
import FeatureFlagsPage from "./pages/admin/features/FeatureFlagsPage";
import PermissionsManagerPage from "./pages/admin/permissions/PermissionsManagerPage";
import AdminHelpContent from "./pages/admin/AdminHelpContent";
import { TenantDashboard } from "@/components/vindula-admin/TenantDashboard";
import PlanManagement from "./pages/PlanManagement";
import AIAnalytics from "./pages/admin/AIAnalytics";
import EmailDashboard from "./pages/admin/EmailDashboard";
import MonitoringDashboard from "./pages/admin/MonitoringDashboard";
import CommercialLeads from "./pages/admin/CommercialLeads";
import TrialMonitoring from "./pages/admin/TrialMonitoring";
// Importando componentes de Relatórios
import { MainDashboard } from "@/components/reports/dashboard/MainDashboard";
import { EngagementReport } from "@/pages/reports/EngagementReport";
import { GamificationReport } from "@/components/reports/gamification/GamificationReport";
import Reports from "./pages/Reports";
// Importando queryClient centralizado (sistema antigo localStorage)
import { queryClient } from "@/lib/query";
// Importar o EnhancedCreatePost em vez do CreatePost
import EnhancedCreatePost from "./pages/EnhancedCreatePost";
// Importando componentes do Sistema de Controle de Armazenamento
import StorageDashboard from "./pages/storage";
import StorageAddons from "./pages/storage/addons";
import UserAddons from "./pages/users/addons";
// Importando a página unificada de upgrade
import UpgradePage from "./pages/UpgradePage";
import { MainLayout } from "@/components/layout/MainLayout";
import { ChatProtectedContent } from "@/components/layout/ChatProtectedContent";
import { ChannelMembersPage } from "./pages/chat/ChannelMembersPage"; // Nova importação
import { SidebarProvider } from "@/components/ui/sidebar";
import Ranking from "./pages/Ranking";
import Events from "./pages/Events";
import KnowledgeHub from "./pages/KnowledgeHub";
import PeopleHub from "./pages/PeopleHub";
import EmailAction from "./pages/EmailAction";
import FloatingTabBarConfig from "./pages/admin/FloatingTabBarConfig";
import EmailDemo from "./pages/EmailDemo";
import { EmailSettingsPanel } from "./components/admin/EmailSettingsPanel";
import {
  KnowledgeSpaceDetail,
  KnowledgeSpaceEdit,
  KnowledgePageDetail,
  KnowledgeCreatePage,
  KnowledgeCreateTemplate,
} from "./pages/knowledge";
import { HotkeyProvider } from "@/components/shortcuts/HotkeyProvider";
import BirthdayCardsPage from "./pages/BirthdayCardsPage";

// import { SessionHealthProvider } from "@/components/auth/SessionHealthProvider"; // REMOVIDO: AuthManager já faz health check
// Importar páginas de missões
import { MissionsPage } from "./pages/MissionsPage";
import { MissionsAdminPage } from "./pages/admin/MissionsAdminPage";
import { MissionAnalyticsPage } from "./pages/admin/MissionAnalyticsPage";
// Importar sistema de timeout por inatividade
import { IdleTimeoutProvider } from "@/contexts/IdleTimeoutContext";
import AdminBilling from "./pages/AdminBilling";
import AdminMarketplace from "./pages/admin/AdminMarketplace";
import AdminGlobalTemplatesPage from "./pages/admin/AdminGlobalTemplatesPage";
import DocsVindula from "./pages/admin/DocsVindula";
import ReactionPacksAdmin from "./pages/admin/ReactionPacksAdmin";
import AuditLogs from "./pages/admin/AuditLogs";
import DefaultXPActions from "./pages/admin/DefaultXPActions";
import DefaultStardustActions from "./pages/admin/DefaultStardustActions";
import OfferPurchases from "./pages/OfferPurchases";
import OfferSales from "./pages/admin/OfferSales";
import EarlyAccess from "./pages/EarlyAccess";
import EarlyAccess2 from "./pages/EarlyAccess2";
import LandingVendas from "./pages/LandingVendas";
import LandingVendas2 from "./pages/LandingVendas2";
import LandingVendas3 from "./pages/LandingVendas3";
import LandingVendasUnificada from "./pages/LandingVendasUnificada";
import LandingVendasNext from "./pages/LandingVendasNext";
import LandingVendasCosmos from "./pages/LandingVendasCosmos";
import FuelStationLanding from "./pages/FuelStationLanding";
import FuelStationLandingCosmic from "./pages/FuelStationLandingCosmic";
import { InteractionToast } from "@/components/ui/sonner";
import { CustomizationProvider } from "@/contexts/CustomizationContext";
import LandingVendaVindulaCosmos from "./pages/LandingVendaVindulaCosmos";
import VendasCosmos from "./pages/VendasCosmos";
import PrivacyPortal from "./pages/PrivacyPortal";
import PrivacyAdmin from "./pages/admin/PrivacyAdmin";
// Error Boundary imports
import { AppErrorBoundary } from "@/lib/error/AppErrorBoundary";
import { SimpleCriticalErrorFallback } from "@/components/error/SimpleCriticalErrorFallback";
import { useDynamicTitle } from "@/hooks/useDynamicTitle";

/**
 * Componente para conteúdo protegido com post-its e controle de inatividade
 *
 * 🚀 NOTA PÚBLICA PARA O TIME:
 * Criei uma landing page espacial incrível para o "Cosmos Early Access Program"
 * em docs/corporation/landing-page-cosmos-early-access.html
 *
 * A página implementa nossa estratégia viral inspirada no Gmail, com:
 * - Design espacial matching das cores do Vindula (cosmic purple, stardust gold)
 * - Scarcity marketing real ("2.847 na fila, apenas 31 convites esta semana")
 * - Gamificação com Cosmos Score e Pioneer badges
 * - FOMO criado pelo countdown do Workplace (89 dias)
 * - Sistema de convites exclusivos com tracking
 *
 * É uma demonstração prática de como transformar o Vindula Cosmos de
 * "mais uma ferramenta" para "convite mais valioso do RH brasileiro"
 *
 * Vejam o arquivo e vamos discutir como implementar elementos visuais similares
 * nas páginas principais! As cores e animações estão lindas. 🎨✨
 *
 * <AUTHOR> Internet 2025
 */
function ProtectedContent({
  children,
  useAdminLayout = false,
}: {
  children: React.ReactNode;
  useAdminLayout?: boolean;
}) {
  // ✅ ISSUE #176: Hook para notificações dinâmicas no título
  useDynamicTitle({
    showChatCount: true,
    showNotificationCount: true,
    baseTitle: 'Vindula Cosmos'
  });

  return (
    <ProtectedRouteSimple enableLogging={true}>
      {/* IdleTimeoutProvider aplicado apenas para conteúdo protegido */}
      <IdleTimeoutProvider
        idleTimeoutMinutes={30}
        warningTimeoutSeconds={180}
        enabled={true}
        testMode={false}
      >
        {/* SessionHealthProvider removido - AuthManager já faz health check automaticamente */}
        <SearchProvider>
          {/* PostItContainer é mantido fora dos layouts específicos para persistir entre navegações */}
          <PostItContainer />

          {/* SidebarTools para controles laterais (Post-its, etc.) */}
          <SidebarTools />

          {useAdminLayout ? (
            <SidebarProvider>
              {children}
              <SearchDialog />
            </SidebarProvider>
          ) : (
            <MainLayout>
              <>
                {children}
                <SearchDialog />
              </>
            </MainLayout>
          )}
        </SearchProvider>
      </IdleTimeoutProvider>
    </ProtectedRouteSimple>
  );
}

// Componente para páginas de setup que precisam de autenticação mas não têm busca
function ProtectedSetupContent({ children }: { children: React.ReactNode }) {
  return <ProtectedRouteSimple enableLogging={true}>{children}</ProtectedRouteSimple>;
}

/**
 * Componente para redirecionamento da rota raiz com timeout de carregamento
 * <AUTHOR> Internet 2025
 */
function RootRedirect() {
  const navigate = useNavigate();
  // isAuthenticated é usado para a lógica inicial, company_id e isAuthLoading para a decisão de rota
  const { isAuthenticated, company_id, isLoading: isAuthLoading } = useAuthManager();
  const appAlreadyLoaded = sessionStorage.getItem("cosmosAppFullyLoaded") === "true";

  // Hooks sempre no topo - não podem ser condicionais
  const [needsAuth, setNeedsAuth] = useState(false);
  const [showSpinner, setShowSpinner] = useState(false);
  const hasChecked = useRef(false);
  const [isChecking, setIsChecking] = useState(true);

  // Verificar se precisa de autenticação - useEffect sempre após useState
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;

    const checkAuth = async () => {
      // Prevenir múltiplas execuções
      if (hasChecked.current) {
        return;
      }
      hasChecked.current = true;

      try {
        // Verificação imediata: se o usuário já está autenticado, decidir entre /feed e /loading
        if (isAuthenticated) {
          if (appAlreadyLoaded && company_id && !isAuthLoading) {
            logQueryEvent(
              "RootRedirect",
              "Usuário autenticado e app JÁ CARREGADO, redirecionando para /feed",
              { appAlreadyLoaded, company_id, isAuthLoading }
            );
            navigate("/feed", { replace: true });
          } else {
            logQueryEvent(
              "RootRedirect",
              "Usuário autenticado, MAS app NÃO totalmente carregado ou AuthManager ocupado, redirecionando para /loading",
              { appAlreadyLoaded, company_id, isAuthLoading }
            );
            navigate("/loading", { replace: true, state: { destination: "/feed" } });
          }
          setIsChecking(false);
          return;
        }

        // Iniciar o spinner após um breve delay
        timeoutId = setTimeout(() => {
          setShowSpinner(true);
        }, 200);

        // Verificar se há uma sessão no localStorage
        const { data } = await supabase.auth.getSession();

        // Se não há sessão, mostrar a landing page
        if (!data.session) {
          logQueryEvent("RootRedirect", "Nenhuma sessão encontrada, mostrando landing page");
          setNeedsAuth(true);
          setIsChecking(false);
        } else {
          // Se há sessão, mas não está autenticado no store, inicializar o store
          logQueryEvent(
            "RootRedirect",
            "Sessão encontrada, mas não autenticado no store, redirecionando para Loading"
          );
          navigate("/loading", { replace: true, state: { destination: "/feed" } });
          setIsChecking(false);
        }
      } catch (error) {
        logQueryEvent("RootRedirect", "Erro ao verificar sessão", error, "error");
        setNeedsAuth(true);
        setIsChecking(false);
      } finally {
        // Limpar o timeout do spinner se a verificação terminar rapidamente
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
      }
    };

    checkAuth();

    return () => {
      // Limpar o timeout ao desmontar
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      // Resetar o flag de verificação ao desmontar
      hasChecked.current = false;
      setIsChecking(true);
    };
  }, [navigate, isAuthenticated]);

  // Se precisa de autenticação, mostrar a landing page
  if (needsAuth) {
    return <LandingConversao />;
  }

  // Se ainda está verificando, mostrar um spinner simples
  if (isChecking) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        {showSpinner && (
          <>
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#E8A95B] mb-4"></div>
            <p className="text-gray-500 text-sm">Verificando sessão...</p>
          </>
        )}
      </div>
    );
  }

  // Se chegou aqui, alguma coisa deu errado, redirecionar para landing
  return <LandingConversao />;
}

export default function App() {
  return (
    <AppErrorBoundary fallback={SimpleCriticalErrorFallback}>
      <QueryClientProvider client={queryClient}>
          <BrowserRouter>
          <HotkeyProvider>
            <CustomizationProvider>
              <FloatingChatsProvider>
                {/* <RealtimeNotificationsProvider> TEMPORÁRIO: Desabilitado para teste do POC */}
                <UnifiedRealtimeProvider enableDebug={true}>
                  <UsersProvider>
                    <FloatingHeartProvider>
                      <PostItProvider>
                      <Routes>
                        <Route path="/" element={<RootRedirect />} />
                        <Route path="/dashboard" element={<Navigate to="/feed" replace />} />
                        <Route path="/home" element={<Index />} />
                        <Route path="/auth" element={<Auth />} />
                        <Route path="/auth/oauth-test" element={<OAuthTest />} />
                        {/* <Route path="/register" element={<Register />} /> */}
                        <Route path="/register-direct" element={<RegisterDirect />} />
                        <Route path="/reset-password" element={<ResetPassword />} />
                        <Route path="/plans" element={<Plans />} />
                        <Route path="/company-setup" element={<CompanySetup />} />
                        <Route path="/plans/compare" element={<PlanComparison />} />
                        <Route path="/loading" element={<Loading />} />

                        {/* Nao está em uso. Aqui apenas por base histórica 
                      {/* <Route path="/early-access" element={<EarlyAccess />} />                     
                      {/* <Route path="/landing-vendas" element={<LandingVendas />} /> 
                      {/* <Route path="/landing-vendas-2" element={<LandingVendas2 />} />
                      {/* <Route
                        path="/landing-vendas-unificada"
                        element={<LandingVendasUnificada />}
                      /> 
                      {/* <Route path="/landing-vendas-next" element={<LandingVendasNext />} />
                      <Route path="/landing-vendas-cosmos" element={<LandingVendasCosmos />} /> 
                      <Route path="/postos-combustiveis" element={<FuelStationLanding />} />
                      */}

                        <Route path="/early-access-2" element={<EarlyAccess2 />} />
                        <Route
                          path="/LandingVendaVindulaCosmos"
                          element={<LandingVendaVindulaCosmos />}
                        />
                        <Route path="/vendas-cosmos" element={<VendasCosmos />} />
                        <Route path="/conversao" element={<LandingConversao />} />
                        <Route path="/landing-real" element={<LandingComparacaoReal />} />
                        <Route path="/landing-vendas-3" element={<LandingVendas3 />} />
                        <Route
                          path="/postos-combustiveis-cosmic"
                          element={<FuelStationLandingCosmic />}
                        />

                        {/* Rotas protegidas com post-its */}
                        <Route
                          path="/feed"
                          element={
                            <ProtectedContent>
                              <Feed />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/profile"
                          element={
                            <ProtectedContent>
                              <Profile />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/user/:userId"
                          element={
                            <ProtectedContent>
                              <UserProfile />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminAll />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/users"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminUsers />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/roles"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminRoles />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/company"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminCompany />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/oauth"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminOAuth />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/public-domains"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminPublicDomains />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/tenants"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <TenantDashboard />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/tenants"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <TenantDashboard />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/hr"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminHR />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/departments"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminDepartments />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/job-titles"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminJobTitles />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/units"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminUnits />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/locations"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminLocations />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/promotions"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminPromotions />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/absences"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminAbsences />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/all"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminAll />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/ai-analytics"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AIAnalytics />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/emails"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <EmailDashboard />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/monitoring"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <MonitoringDashboard />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/commercial-leads"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <CommercialLeads />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/trial-monitoring"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <TrialMonitoring />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/team"
                          element={
                            <ProtectedContent>
                              <TeamView />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/team/:id"
                          element={
                            <ProtectedContent>
                              <TeamView />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/team/:id/inteligencia"
                          element={
                            <ProtectedContent>
                              <TeamIntelligence />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/teams"
                          element={
                            <ProtectedContent>
                              <TeamsList />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/teams/:id"
                          element={
                            <ProtectedContent>
                              <TeamDetail />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/post/create"
                          element={
                            <ProtectedContent>
                              <EnhancedCreatePost />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/post/scheduled"
                          element={
                            <ProtectedContent>
                              <ScheduledPosts />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/post/:postId"
                          element={
                            <ProtectedContent>
                              <Post />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/chat"
                          element={
                            <ChatProtectedContent>
                              <Chat />
                            </ChatProtectedContent>
                          }
                        >
                          <Route path=":chatId" element={<Chat />} />
                          <Route path="channel/:channelId" element={<Chat />} />
                          {/* A rota de membros foi movida para fora deste aninhamento */}
                        </Route>
                        {/* Rota de preview do chat moderno - MOCKADO PARA AVALIAÇÃO */}
                        <Route
                          path="/chatpreview"
                          element={
                            <ProtectedContent>
                              <ChatPreview />
                            </ProtectedContent>
                          }
                        />
                        {/* Rota para gerenciar membros do canal (agora de nível superior) */}
                        <Route
                          path="/chat/canal/:channelId/membros"
                          element={
                            <ChatProtectedContent>
                              <ChannelMembersPage />
                            </ChatProtectedContent>
                          }
                        />
                        <Route
                          path="/library"
                          element={
                            <ProtectedContent>
                              <Library />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/library/categories"
                          element={
                            <ProtectedContent>
                              <LibraryCategories />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/progress"
                          element={
                            <ProtectedContent>
                              <Progress />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/marketplace"
                          element={
                            <ProtectedContent>
                              <Marketplace />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/offer-purchases"
                          element={
                            <ProtectedContent>
                              <OfferPurchases />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/tasks"
                          element={
                            <ProtectedContent>
                              <FeatureProtectedRoute featureKey="tasks_feature">
                                <TasksSimplified />
                              </FeatureProtectedRoute>
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/tasks-new"
                          element={
                            <ProtectedContent>
                              <FeatureProtectedRoute featureKey="tasks_feature">
                                <TasksNew />
                              </FeatureProtectedRoute>
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/tasks_old"
                          element={
                            <ProtectedContent>
                              <FeatureProtectedRoute featureKey="tasks_feature">
                                <Tasks />
                              </FeatureProtectedRoute>
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/knowledge"
                          element={
                            <ProtectedContent>
                              <KnowledgeHub />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/people"
                          element={
                            <ProtectedContent>
                              <PeopleHub />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/privacy"
                          element={
                            <ProtectedContent>
                              <PrivacyPortal />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/privacy"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <PrivacyAdmin />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/knowledge/space/:spaceId"
                          element={
                            <ProtectedContent>
                              <KnowledgeSpaceDetail />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/knowledge/space/:spaceId/edit"
                          element={
                            <ProtectedContent>
                              <KnowledgeSpaceEdit />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/knowledge/page/:pageIdentifier"
                          element={
                            <ProtectedContent>
                              <KnowledgePageDetail />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/knowledge/create-page"
                          element={
                            <ProtectedContent>
                              <KnowledgeCreatePage />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/knowledge/page/:pageIdentifier/edit"
                          element={
                            <ProtectedContent>
                              <KnowledgeCreatePage />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/knowledge/create-template"
                          element={
                            <ProtectedContent>
                              <KnowledgeCreateTemplate />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/knowledge/template/:templateId/edit"
                          element={
                            <ProtectedContent>
                              <KnowledgeCreateTemplate />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/visual-assets"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <VisualAssets />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/reaction-packs"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <ReactionPacksAdmin />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/gamification"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <GamificationAdmin />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/store-items"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <StoreItems />
                            </ProtectedContent>
                          }
                        />

                        {/* Rotas do Sistema de Missões */}
                        <Route
                          path="/missions"
                          element={
                            <ProtectedContent>
                              <MissionsPage />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/missions"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <MissionsAdminPage />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/admin/mission-analytics"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <MissionAnalyticsPage />
                            </ProtectedContent>
                          }
                        />

                        {/* Rotas do Sistema de Leitura Obrigatória */}
                        <Route
                          path="/obligations"
                          element={
                            <ProtectedContent>
                              <ObligationsPage />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/obligations/read/:documentId"
                          element={
                            <ProtectedContent>
                              <ObligationReaderPage />
                            </ProtectedContent>
                          }
                        />
                        {/* Novas rotas para Gerenciamento de Obrigações */}
                        <Route
                          path="/manage-obligations"
                          element={
                            <ProtectedContent>
                              <ManageObligationsPage />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/manage-obligations/:id"
                          element={
                            <ProtectedContent>
                              <ManageObligationDetailsPage />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para relatório detalhado de armazenamento */}
                        <Route
                          path="/admin/storage-report"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <StorageReportPage />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para auditoria de arquivos de armazenamento */}
                        <Route
                          path="/admin/storage-audit"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <StorageAuditPage />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para gerenciamento de feature flags */}
                        <Route
                          path="/admin/features"
                          element={
                            <ProtectedContent>
                              <FeatureFlagsPage />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para gerenciamento de permissões */}
                        <Route
                          path="/admin/permissions"
                          element={
                            <ProtectedContent>
                              <PermissionsManagerPage />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para gestão de conteúdo de ajuda */}
                        <Route
                          path="/admin/help-content"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminHelpContent />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para configurações de email */}
                        <Route
                          path="/admin/email-settings"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <EmailSettingsPanel />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para gerenciamento de planos */}
                        <Route
                          path="/plan-management"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <PlanManagement />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para administração de billing */}
                        <Route
                          path="/admin/billing"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminBilling />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para administração do marketplace estratégico */}
                        <Route
                          path="/admin/marketplace"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminMarketplace />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para configuração do floating tab bar */}
                        <Route
                          path="/admin/floating-tab-bar"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <FloatingTabBarConfig />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para gerenciar vendas de ofertas especiais */}
                        <Route
                          path="/admin/offer-sales"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <OfferSales />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para gerenciamento de templates globais */}
                        <Route
                          path="/admin/global-templates"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AdminGlobalTemplatesPage />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para logs de auditoria */}
                        <Route
                          path="/admin/audit-logs"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <AuditLogs />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para documentação do Vindula */}
                        <Route
                          path="/admin/docsdovindula"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <DocsVindula />
                            </ProtectedContent>
                          }
                        />

                        {/* Configurações Padrão de XP - Vindula Only */}
                        <Route
                          path="/admin/default-xp-actions"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <DefaultXPActions />
                            </ProtectedContent>
                          }
                        />

                        {/* Configurações Padrão de Stardust - Vindula Only */}
                        <Route
                          path="/admin/default-stardust-actions"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <DefaultStardustActions />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota unificada de upgrade com parâmetros de contexto */}
                        <Route
                          path="/upgrade"
                          element={
                            <ProtectedContent>
                              <UpgradePage />
                            </ProtectedContent>
                          }
                        />

                        {/* Rotas de Relatórios */}
                        <Route
                          path="/reports"
                          element={
                            <ProtectedContent>
                              <Reports />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/reports/:reportType"
                          element={
                            <ProtectedContent>
                              <Reports />
                            </ProtectedContent>
                          }
                        />

                        {/* Rotas de Controle de Armazenamento */}
                        <Route
                          path="/storage"
                          element={
                            <ProtectedContent>
                              <StorageDashboard />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/storage/simulate"
                          element={
                            <ProtectedContent>
                              <StorageDashboard />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/storage/addons"
                          element={
                            <ProtectedContent useAdminLayout={true}>
                              <StorageAddons />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/users/addons"
                          element={
                            <ProtectedContent>
                              <UserAddons />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/ranking"
                          element={
                            <ProtectedContent>
                              <Ranking />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/events"
                          element={
                            <ProtectedContent>
                              <Events />
                            </ProtectedContent>
                          }
                        />
                        <Route
                          path="/email-demo"
                          element={
                            <ProtectedContent>
                              <EmailDemo />
                            </ProtectedContent>
                          }
                        />

                        {/* Rota para Cartões de Aniversário */}
                        <Route
                          path="/birthday-cards"
                          element={
                            <ProtectedContent>
                              <BirthdayCardsPage />
                            </ProtectedContent>
                          }
                        />                     


                        {/* Rotas do Sistema de Email Inteligente de Eventos (públicas para tokens) */}
                        <Route
                          path="/events/email-action/:action/:token"
                          element={<EmailAction />}
                        />
                        <Route
                          path="/events/email-action/web-view/:token"
                          element={<EmailAction />}
                        />
                        </Routes>
                        <Toaster position="top-right" /> {/* MODIFICADO: Removido richColors */}
                        
                      </PostItProvider>
                    </FloatingHeartProvider>
                  </UsersProvider>
                {/* </RealtimeNotificationsProvider> TEMPORÁRIO: Desabilitado para teste do POC */}
                </UnifiedRealtimeProvider>
              </FloatingChatsProvider>
            </CustomizationProvider>

            {/* Container para corações flutuantes em toda a tela */}
            <FloatingHeartContainer />

            {/* Toast premium para interações (comentários, reações, etc.) */}
            <InteractionToast />
          </HotkeyProvider>
        </BrowserRouter>
      </QueryClientProvider>
    </AppErrorBoundary>
  );
}
