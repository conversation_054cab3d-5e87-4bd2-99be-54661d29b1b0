import { Post, PostImage } from "@/types/post.types";

type PostWithLikes = {
  id: string;
  content: string;
  created_at: string;
  company_id: string;
  status: string;
  scheduled_at: string | null;
  has_poll: boolean | null;
  author: {
    id: string;
    full_name: string | null;
    avatar_url: string | null;
  };
  liked_by: Array<{
    profiles: {
      id: string;
      full_name: string | null;
      avatar_url: string | null;
    };
  }>;
  post_audience: Array<{
    target_type: string;
    target_id: string | null;
  }>;
  images: Array<{
    id: string;
    image_url: string;
    storage_path: string;
    size: number;
    created_at: string;
  }>;
};

export const formatPostResponse = (post: PostWithLikes): Post => {
  const likedBy = post.liked_by.map((like) => like.profiles);

  // Processar a audiência
  let audience = { type: "all" as const };
  
  if (post.post_audience && post.post_audience.length > 0) {
    const firstAudience = post.post_audience[0];
    const audienceType = firstAudience.target_type as "all" | "department" | "team" | "user";
    
    if (audienceType === "all") {
      audience = { type: "all" };
    } else {
      const targets = post.post_audience
        .map(a => a.target_id)
        .filter((id): id is string => id !== null);
      
      audience = {
        type: audienceType,
        targets,
        targetCount: targets.length
      };
    }
  }

  return {
    id: post.id,
    content: post.content,
    created_at: post.created_at,
    company_id: post.company_id,
    status: post.status as 'draft' | 'scheduled' | 'published',
    scheduled_at: post.scheduled_at || undefined,
    has_poll: post.has_poll || false,
    likes: likedBy.length,
    liked_by: likedBy,
    author: post.author,
    audience,
    images: post.images || []
  };
};