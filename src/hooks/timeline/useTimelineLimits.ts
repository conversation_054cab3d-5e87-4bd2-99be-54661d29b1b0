/**
 * Hook especializado para verificar limites da Timeline do Feed
 * Combina feature flags com verificação de plano de assinatura
 * <AUTHOR> Internet 2025
 */
import { useMemo } from 'react';
import { useFeatureAvailability } from '@/lib/query/hooks/useFeatureFlags';
import { useCurrentSubscription } from '@/lib/query/hooks/useSubscriptions';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

export interface TimelineLimits {
  // Limitações específicas da timeline
  maxHistoryDays: number;
  maxItems: number;
  hasAdvancedFilters: boolean;
  hasExport: boolean;
  hasAnalytics: boolean;
  
  // Verificações de plano
  isFreePlan: boolean;
  isProPlan: boolean;
  isMaxPlan: boolean;
  currentPlan: string;
  
  // Utilitários
  isHistoryLimited: boolean;
  isItemsLimited: boolean;
}

export const useTimelineLimits = () => {
  const { data: featureData, isLoading: isLoadingFeature } = useFeatureAvailability('feed_timeline_feature');
  const { data: subscription, isLoading: isLoadingSubscription } = useCurrentSubscription();
  
  const limits = useMemo((): TimelineLimits | null => {
    // Se ainda está carregando, retornar null para manter loading state
    if (isLoadingFeature || isLoadingSubscription) {
      return null;
    }
    
    // Determinar plano atual com fallback defensivo
    let currentPlan = 'Grátis'; // Fallback padrão
    
    if (subscription?.subscription_plans?.name) {
      currentPlan = subscription.subscription_plans.name;
    } else {
      logQueryEvent('useTimelineLimits', 'Subscription não encontrada, usando plano Grátis como fallback');
    }
    
    // Se feature flag não existe, criar limites padrão baseados no plano
    if (!featureData?.featureFlag?.access_levels) {
      logQueryEvent('useTimelineLimits', 'Feature flag não encontrada, usando limites padrão', { currentPlan });
      
      // Limites padrão por plano quando feature flag não existe
      const defaultLimitsByPlan = {
        'Grátis': { 
          history_days: 7,  // Apenas 7 dias para plano gratuito
          max_items: 100, 
          hasAdvancedFilters: false,
          hasExport: false,
          hasAnalytics: false
        },
        'Pro': { 
          history_days: 90, 
          max_items: 500,
          hasAdvancedFilters: true,
          hasExport: true,
          hasAnalytics: true
        },
        'Max': { 
          history_days: -1, 
          max_items: -1,
          hasAdvancedFilters: true,
          hasExport: true,
          hasAnalytics: true
        },
      };
      
      const defaultLimits = defaultLimitsByPlan[currentPlan as keyof typeof defaultLimitsByPlan] || defaultLimitsByPlan['Grátis'];
      
      return {
        maxHistoryDays: defaultLimits.history_days,
        maxItems: defaultLimits.max_items,
        hasAdvancedFilters: defaultLimits.hasAdvancedFilters,
        hasExport: defaultLimits.hasExport,
        hasAnalytics: defaultLimits.hasAnalytics,
        isFreePlan: currentPlan === 'Grátis',
        isProPlan: currentPlan === 'Pro',
        isMaxPlan: currentPlan === 'Max',
        currentPlan,
        isHistoryLimited: defaultLimits.history_days !== -1,
        isItemsLimited: defaultLimits.max_items !== -1,
      };
    }
    
    // Se feature está desabilitada, retornar limites mínimos
    if (!featureData.isFeatureEnabled) {
      logQueryEvent('useTimelineLimits', 'Feature desabilitada, usando limites mínimos', { currentPlan });
      
      return {
        maxHistoryDays: 7,
        maxItems: 50,
        hasAdvancedFilters: false,
        hasExport: false,
        hasAnalytics: false,
        isFreePlan: currentPlan === 'Grátis',
        isProPlan: currentPlan === 'Pro',
        isMaxPlan: currentPlan === 'Max',
        currentPlan,
        isHistoryLimited: true,
        isItemsLimited: true,
      };
    }
    
    const planLimits = featureData.featureFlag.access_levels[currentPlan]?.limits;
    
    if (!planLimits) {
      logQueryEvent('useTimelineLimits', `Plano ${currentPlan} não encontrado nos access_levels`);
      return null;
    }
    
    // Log removido para reduzir verbosidade
    
    // Extrair limites com valores padrão
    const historyDays = planLimits.timeline_history_days ?? 30;
    const maxItems = planLimits.max_timeline_items ?? 100;
    const hasAdvancedFilters = planLimits.has_advanced_filters ?? false;
    const hasExport = planLimits.has_export ?? false;
    const hasAnalytics = planLimits.has_analytics ?? false;
    
    return {
      maxHistoryDays: historyDays,
      maxItems: maxItems,
      hasAdvancedFilters,
      hasExport,
      hasAnalytics,
      isFreePlan: currentPlan === 'Grátis',
      isProPlan: currentPlan === 'Pro',
      isMaxPlan: currentPlan === 'Max',
      currentPlan,
      isHistoryLimited: historyDays !== -1,
      isItemsLimited: maxItems !== -1,
    };
  }, [featureData, subscription, isLoadingFeature, isLoadingSubscription]);
  
  return {
    limits,
    isLoading: isLoadingFeature || isLoadingSubscription,
    isEnabled: featureData?.isFeatureEnabled ?? false,
    featureFlag: featureData?.featureFlag,
  };
};

/**
 * Hook para validação de datas baseado nos limites da timeline
 */
export const useTimelineDateLimit = () => {
  const { limits } = useTimelineLimits();
  
  return useMemo(() => {
    if (!limits) {
      return {
        getMaxDate: () => new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        isDateAllowed: (date: Date) => false,
        getDateLimitMessage: () => 'Carregando limites...',
      };
    }
    
    const getMaxDate = () => {
      if (limits.maxHistoryDays === -1) {
        return new Date('2020-01-01'); // Ilimitado
      }
      return new Date(Date.now() - limits.maxHistoryDays * 24 * 60 * 60 * 1000);
    };
    
    const isDateAllowed = (date: Date) => {
      if (limits.maxHistoryDays === -1) return true;
      return date >= getMaxDate();
    };
    
    const getDateLimitMessage = () => {
      if (limits.maxHistoryDays === -1) return 'Histórico ilimitado';
      return `Limitado aos últimos ${limits.maxHistoryDays} dias`;
    };
    
    return { getMaxDate, isDateAllowed, getDateLimitMessage, maxDays: limits.maxHistoryDays };
  }, [limits]);
};