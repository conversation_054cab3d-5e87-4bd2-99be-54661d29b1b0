/**
 * Hook para gerenciar filtros rápidos da timeline baseado no histórico de uso
 * <AUTHOR> Internet 2025
 */

import { useLocalStorage } from '@/hooks/useLocalStorage';
import { useCallback, useMemo } from 'react';
import type { TimelineItemType, TimelinePriority } from '@/types/timeline';

interface FilterUsage {
  type: TimelineItemType | TimelinePriority;
  filterCategory: 'type' | 'priority';
  count: number;
  lastUsed: string;
}

interface QuickFiltersData {
  usage: FilterUsage[];
  version: number;
}

const STORAGE_KEY = 'timeline_quick_filters';
const MAX_QUICK_FILTERS = 4;
const CURRENT_VERSION = 1;

// Mapeamento de tipos para labels legíveis - sincronizado com TimelineFilters.tsx
const TYPE_LABELS: Record<TimelineItemType, string> = {
  post: 'Posts',
  notification: 'Notificações',
  birthday: 'Aniversário<PERSON>',
  promotion: 'Promoções', 
  obligation: 'Obriga<PERSON>õ<PERSON>',
  event: 'Eventos',
  mission: 'Missões',
  task: 'Tarefas',
  ai_insight: 'AI Insights',
  medal_earned: 'Medalhas',
  level_up: 'Level Up',
  // Tipos adicionais (compatibilidade)
  post_published: 'Posts',
  post_edited: 'Posts Editados',
  mention: 'Menções',
  comment: 'Comentários',
  like: 'Curtidas',
  event_invitation: 'Convites',
  event_reminder: 'Lembretes',
  mission_progress: 'Progresso',
  mission_completed: 'Missões Concluídas'
};

// Mapeamento de prioridades para labels legíveis - sincronizado com TimelineFilters.tsx
const PRIORITY_LABELS: Record<TimelinePriority, string> = {
  urgent: 'Urgente',
  high: 'Alta',
  medium: 'Média',
  low: 'Baixa'
};

// Ícones para tipos de atividade - sincronizado com TimelineFilters.tsx
const TYPE_ICONS: Record<TimelineItemType, string> = {
  post: '📝',
  notification: '🔔',
  birthday: '🎂',
  promotion: '📈',
  obligation: '📋',
  event: '📅',
  mission: '🎯',
  task: '✅',
  ai_insight: '🤖',
  medal_earned: '🏅',
  level_up: '🚀',
  // Tipos adicionais (compatibilidade)
  post_published: '📝',
  post_edited: '✏️',
  mention: '💬',
  comment: '💬',
  like: '❤️',
  event_invitation: '📧',
  event_reminder: '⏰',
  mission_progress: '📊',
  mission_completed: '🏆'
};

// Ícones para prioridades - sincronizado com TimelineFilters.tsx
const PRIORITY_ICONS: Record<TimelinePriority, string> = {
  urgent: '🔴',
  high: '🟠',
  medium: '🟡',
  low: '🔵'
};

// Cores para cada tipo de filtro - sincronizado com TimelineFilters.tsx
const TYPE_COLORS: Record<TimelineItemType, { bg: string; text: string; border: string }> = {
  post: { bg: 'bg-blue-50', text: 'text-blue-700', border: 'border-blue-200' },
  notification: { bg: 'bg-purple-50', text: 'text-purple-700', border: 'border-purple-200' },
  birthday: { bg: 'bg-pink-50', text: 'text-pink-700', border: 'border-pink-200' },
  promotion: { bg: 'bg-green-50', text: 'text-green-700', border: 'border-green-200' },
  obligation: { bg: 'bg-orange-50', text: 'text-orange-700', border: 'border-orange-200' },
  event: { bg: 'bg-indigo-50', text: 'text-indigo-700', border: 'border-indigo-200' },
  mission: { bg: 'bg-red-50', text: 'text-red-700', border: 'border-red-200' },
  task: { bg: 'bg-gray-50', text: 'text-gray-700', border: 'border-gray-200' },
  ai_insight: { bg: 'bg-cyan-50', text: 'text-cyan-700', border: 'border-cyan-200' },
  medal_earned: { bg: 'bg-yellow-50', text: 'text-yellow-700', border: 'border-yellow-200' },
  level_up: { bg: 'bg-purple-50', text: 'text-purple-700', border: 'border-purple-200' },
  // Tipos adicionais (compatibilidade)
  post_published: { bg: 'bg-blue-50', text: 'text-blue-700', border: 'border-blue-200' },
  post_edited: { bg: 'bg-orange-50', text: 'text-orange-700', border: 'border-orange-200' },
  mention: { bg: 'bg-yellow-50', text: 'text-yellow-700', border: 'border-yellow-200' },
  comment: { bg: 'bg-blue-50', text: 'text-blue-700', border: 'border-blue-200' },
  like: { bg: 'bg-red-50', text: 'text-red-700', border: 'border-red-200' },
  event_invitation: { bg: 'bg-indigo-50', text: 'text-indigo-700', border: 'border-indigo-200' },
  event_reminder: { bg: 'bg-indigo-50', text: 'text-indigo-700', border: 'border-indigo-200' },
  mission_progress: { bg: 'bg-red-50', text: 'text-red-700', border: 'border-red-200' },
  mission_completed: { bg: 'bg-green-50', text: 'text-green-700', border: 'border-green-200' }
};

// Cores para prioridades - sincronizado com TimelineFilters.tsx
const PRIORITY_COLORS: Record<TimelinePriority, { bg: string; text: string; border: string }> = {
  urgent: { bg: 'bg-red-50', text: 'text-red-700', border: 'border-red-200' },
  high: { bg: 'bg-orange-50', text: 'text-orange-700', border: 'border-orange-200' },
  medium: { bg: 'bg-yellow-50', text: 'text-yellow-700', border: 'border-yellow-200' },
  low: { bg: 'bg-blue-50', text: 'text-blue-700', border: 'border-blue-200' }
};

export function useQuickFilters() {
  const [data, setData] = useLocalStorage<QuickFiltersData>(STORAGE_KEY, {
    usage: [],
    version: CURRENT_VERSION
  });

  // Migrar dados se necessário
  const currentData = useMemo(() => {
    if (data.version !== CURRENT_VERSION) {
      return {
        usage: [],
        version: CURRENT_VERSION
      };
    }
    return data;
  }, [data]);

  // Registrar uso de filtros (tipos e prioridades)
  const trackFilterUsage = useCallback((types: TimelineItemType[], priorities?: TimelinePriority[]) => {
    if ((!types || types.length === 0) && (!priorities || priorities.length === 0)) return;

    setData(prevData => {
      const newUsage = [...(prevData.usage || [])];
      const now = new Date().toISOString();

      // Rastrear tipos
      types?.forEach(type => {
        const existingIndex = newUsage.findIndex(item => item.type === type && item.filterCategory === 'type');
        
        if (existingIndex >= 0) {
          newUsage[existingIndex].count += 1;
          newUsage[existingIndex].lastUsed = now;
        } else {
          newUsage.push({
            type,
            filterCategory: 'type',
            count: 1,
            lastUsed: now
          });
        }
      });

      // Rastrear prioridades
      priorities?.forEach(priority => {
        const existingIndex = newUsage.findIndex(item => item.type === priority && item.filterCategory === 'priority');
        
        if (existingIndex >= 0) {
          newUsage[existingIndex].count += 1;
          newUsage[existingIndex].lastUsed = now;
        } else {
          newUsage.push({
            type: priority,
            filterCategory: 'priority',
            count: 1,
            lastUsed: now
          });
        }
      });

      // Ordenar por frequência e depois por uso recente
      newUsage.sort((a, b) => {
        if (a.count !== b.count) {
          return b.count - a.count;
        }
        return new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime();
      });

      return {
        ...prevData,
        usage: newUsage,
        version: CURRENT_VERSION
      };
    });
  }, [setData]);

  // Filtros padrão para novos usuários (tipos e prioridades) - sincronizado com TimelineFilters.tsx
  const defaultTypeFilters: TimelineItemType[] = ['post', 'notification', 'obligation'];
  const defaultPriorityFilters: TimelinePriority[] = ['high', 'medium'];

  // Obter os filtros rápidos mais usados
  const quickFilters = useMemo(() => {
    // Se o usuário tem histórico de uso, usar os dados reais
    if (currentData.usage.length > 0) {
      const topFilters = currentData.usage
        .slice(0, MAX_QUICK_FILTERS)
        .map(item => {
          if (item.filterCategory === 'type') {
            const type = item.type as TimelineItemType;
            return {
              id: `type-${type}`,
              type,
              category: 'type' as const,
              label: TYPE_LABELS[type] || type,
              icon: TYPE_ICONS[type] || '📌',
              colors: TYPE_COLORS[type] || TYPE_COLORS.notification,
              lastUsed: item.lastUsed
            };
          } else {
            const priority = item.type as TimelinePriority;
            return {
              id: `priority-${priority}`,
              type: priority,
              category: 'priority' as const,
              label: PRIORITY_LABELS[priority] || priority,
              icon: PRIORITY_ICONS[priority] || '⚪',
              colors: PRIORITY_COLORS[priority] || PRIORITY_COLORS.medium,
              lastUsed: item.lastUsed
            };
          }
        });

      // Se não temos 4 filtros, completar com padrões
      if (topFilters.length < MAX_QUICK_FILTERS) {
        const usedTypes = topFilters.filter(f => f.category === 'type').map(f => f.type as TimelineItemType);
        const usedPriorities = topFilters.filter(f => f.category === 'priority').map(f => f.type as TimelinePriority);
        
        // Adicionar tipos padrão que não estão sendo usados
        const missingDefaultTypes = defaultTypeFilters.filter(type => !usedTypes.includes(type));
        const missingDefaultPriorities = defaultPriorityFilters.filter(priority => !usedPriorities.includes(priority));
        
        const additionalTypeFilters = missingDefaultTypes.map(type => ({
          id: `type-${type}`,
          type,
          category: 'type' as const,
          label: TYPE_LABELS[type] || type,
          icon: TYPE_ICONS[type] || '📌',
          colors: TYPE_COLORS[type] || TYPE_COLORS.notification,
          lastUsed: new Date().toISOString()
        }));
        
        const additionalPriorityFilters = missingDefaultPriorities.map(priority => ({
          id: `priority-${priority}`,
          type: priority,
          category: 'priority' as const,
          label: PRIORITY_LABELS[priority] || priority,
          icon: PRIORITY_ICONS[priority] || '⚪',
          colors: PRIORITY_COLORS[priority] || PRIORITY_COLORS.medium,
          lastUsed: new Date().toISOString()
        }));
        
        const additionalFilters = [...additionalTypeFilters, ...additionalPriorityFilters];
        const finalFilters = [...topFilters, ...additionalFilters].slice(0, MAX_QUICK_FILTERS);
        
        return finalFilters;
      }

      return topFilters;
    }

    // Senão, mostrar filtros padrão para novos usuários
    const defaultTypes = defaultTypeFilters.map(type => ({
      id: `type-${type}`,
      type,
      category: 'type' as const,
      label: TYPE_LABELS[type] || type,
      icon: TYPE_ICONS[type] || '📌',
      colors: TYPE_COLORS[type] || TYPE_COLORS.notification,
      lastUsed: new Date().toISOString()
    }));

    const defaultPriorities = defaultPriorityFilters.map(priority => ({
      id: `priority-${priority}`,
      type: priority,
      category: 'priority' as const,
      label: PRIORITY_LABELS[priority] || priority,
      icon: PRIORITY_ICONS[priority] || '⚪',
      colors: PRIORITY_COLORS[priority] || PRIORITY_COLORS.medium,
      lastUsed: new Date().toISOString()
    }));

    return [...defaultTypes, ...defaultPriorities].slice(0, MAX_QUICK_FILTERS);
  }, [JSON.stringify(currentData.usage)]);

  // Verificar se há filtros rápidos disponíveis (sempre true para mostrar filtros padrão)
  const hasQuickFilters = true;

  // Limpar dados (para reset se necessário)
  const clearData = useCallback(() => {
    setData({
      usage: [],
      version: CURRENT_VERSION
    });
  }, [setData]);

  return {
    quickFilters,
    hasQuickFilters,
    trackFilterUsage,
    clearData
  };
}

export type { FilterUsage, QuickFiltersData };