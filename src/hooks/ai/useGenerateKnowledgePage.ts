/**
 * Hook para gerar páginas de conhecimento com IA
 * <AUTHOR> Internet 2025
 */
import { useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAIFeatureWithCredits } from '@/hooks/ai/useAIFeatureWithCredits';
import { successWithNotification, errorWithNotification } from '@/lib/notifications/toastWithNotification';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

export interface GenerateKnowledgePageData {
  prompt: string;
  category: string;
  spaceId: string;
  formData: Record<string, string>;
}

export interface GeneratedKnowledgePage {
  title: string;
  excerpt: string;
  content: string;
  tags: string[];
}

export function useGenerateKnowledgePage() {
  const { executeWithCredits } = useAIFeatureWithCredits('knowledge_page_generation');

  return useMutation({
    mutationFn: async (data: GenerateKnowledgePageData): Promise<GeneratedKnowledgePage> => {
      logQueryEvent('useGenerateKnowledgePage', 'Iniciando geração de página', { 
        category: data.category, 
        spaceId: data.spaceId 
      });

      return executeWithCredits(async () => {
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session?.access_token) {
          throw new Error('Usuário não autenticado');
        }

        logQueryEvent('useGenerateKnowledgePage', 'Chamando edge function', { 
          category: data.category 
        });

        const response = await supabase.functions.invoke('generate-knowledge-page', {
          body: {
            prompt: data.prompt,
            category: data.category,
            spaceId: data.spaceId,
            formData: data.formData
          },
          headers: {
            Authorization: `Bearer ${session.access_token}`,
          },
        });

        if (response.error) {
          logQueryEvent('useGenerateKnowledgePage', 'Erro na edge function', { 
            error: response.error 
          }, 'error');
          throw new Error(response.error.message || 'Erro ao gerar página');
        }

        const result = response.data as GeneratedKnowledgePage;
        
        if (!result.title || !result.content) {
          logQueryEvent('useGenerateKnowledgePage', 'Resposta incompleta da IA', { 
            hasTitle: !!result.title, 
            hasContent: !!result.content 
          }, 'error');
          throw new Error('Resposta incompleta da IA');
        }

        logQueryEvent('useGenerateKnowledgePage', 'Página gerada com sucesso', {
          title: result.title,
          contentLength: result.content.length,
          tagsCount: result.tags?.length || 0
        });

        return result;
      });
    },
    onSuccess: (result) => {
      logQueryEvent('useGenerateKnowledgePage', 'Geração concluída', { 
        title: result.title 
      });
      
      successWithNotification('Página gerada com sucesso!', {
        description: `"${result.title}" foi criada e está pronta para edição.`,
      });
    },
    onError: (error: Error) => {
      logQueryEvent('useGenerateKnowledgePage', 'Erro na geração', { 
        error: error.message 
      }, 'error');

      // Tratamento de erros específicos
      let title = 'Erro ao gerar página';
      let description = 'Não foi possível gerar a página de conhecimento.';

      if (error.message.includes('créditos insuficientes')) {
        title = 'Créditos insuficientes';
        description = 'Você não possui créditos suficientes para gerar uma página.';
      } else if (error.message.includes('não autenticado')) {
        title = 'Erro de autenticação';
        description = 'Faça login novamente para continuar.';
      } else if (error.message.includes('não encontrado')) {
        title = 'Espaço não encontrado';
        description = 'O espaço de conhecimento selecionado não foi encontrado.';
      } else if (error.message.includes('Groq') || error.message.includes('OpenAI')) {
        title = 'Erro no serviço de IA';
        description = 'Tente novamente em alguns instantes.';
      }

      errorWithNotification(title, { description });
    }
  });
} 