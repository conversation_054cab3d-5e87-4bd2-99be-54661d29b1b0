import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { formatPostResponse } from "@/utils/post.utils";
import { Post } from "@/types/post.types";
import { PostgrestError } from "@supabase/supabase-js";

// Definições de tipos para as respostas do Supabase
type PostIdResult = {
  id: string;
};

type PostDetailResult = {
  id: string;
  content: string;
  created_at: string;
  status: string;
  scheduled_at: string | null;
  has_poll: boolean | null;
  company_id: string;
  author: {
    id: string;
    full_name: string | null;
    avatar_url: string | null;
  };
  liked_by: Array<{
    profiles: {
      id: string;
      full_name: string | null;
      avatar_url: string | null;
    };
  }>;
  post_audience: Array<{
    target_type: string;
    target_id: string | null;
  }>;
  images: Array<{
    id: string;
    image_url: string;
    storage_path: string;
    size: number;
    created_at: string;
  }>;
};

export const useFetchPosts = (currentUserId: string | null) => {
  return useQuery({
    queryKey: ["posts"],
    queryFn: async () => {
      if (!currentUserId) throw new Error("Usuário não autenticado");

      try {
        // 1. Primeiro obter apenas os IDs dos posts visíveis para o usuário
        // Isso usa as políticas RLS sem problemas de ambiguidade
        const { data: postsData, error: postsError } = await supabase
          .from("posts")
          .select("id")
          .eq("status", "published")
          .order("created_at", { ascending: false });

        if (postsError) {
          console.error("Erro ao buscar posts:", postsError);
          throw new Error(`Erro ao carregar posts: ${postsError.message}`);
        }

        if (!postsData || postsData.length === 0) {
          return [];
        }

        // 2. Para cada post, buscar detalhes com relacionamentos
        const postIds = (postsData as PostIdResult[]).map(post => post.id);
        
        const { data: detailedPosts, error: detailsError } = await supabase
          .from("posts")
          .select(`
            id,
            content,
            created_at,
            status,
            scheduled_at,
            has_poll,
            company_id,
            author:profiles!posts_author_id_fkey (
              id,
              full_name,
              avatar_url
            ),
            liked_by:post_likes (
              profiles:profiles!post_likes_user_id_fkey (
                id,
                full_name,
                avatar_url
              )
            ),
            post_audience (
              target_type,
              target_id
            ),
            images:post_images (
              id,
              image_url,
              storage_path,
              size,
              created_at
            )
          `)
          .in("id", postIds)
          .order("created_at", { ascending: false });

        if (detailsError) {
          console.error("Erro ao buscar detalhes dos posts:", detailsError);
          throw new Error(`Erro ao carregar detalhes dos posts: ${detailsError.message}`);
        }

        // Garantir que detailedPosts seja do tipo esperado antes de passar para formatPostResponse
        return ((detailedPosts || []) as PostDetailResult[]).map(formatPostResponse);
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
        console.error("Erro detalhado:", error);
        throw new Error(`Erro ao carregar posts: ${errorMessage}`);
      }
    },
    enabled: !!currentUserId,
  });
};