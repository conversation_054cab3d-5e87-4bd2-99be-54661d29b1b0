/**
 * Hook para usar QueryClient com IndexedDB (Sistema Híbrido)
 * <AUTHOR> Internet 2025
 * 
 * Hook que permite componentes específicos optarem pelo sistema IndexedDB
 * enquanto mantém compatibilidade com o sistema antigo localStorage
 */
import { useQueryClient } from '@tanstack/react-query';
import { useIndexedDBQueryClient as useIndexedDBContext } from '@/providers/QueryClientProvider';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

interface UseIndexedDBQueryClientOptions {
  /**
   * Se deve usar o sistema IndexedDB ou fallback para localStorage
   * @default true
   */
  useIndexedDB?: boolean;
  /**
   * Se deve logar a escolha do sistema
   * @default true
   */
  enableLogging?: boolean;
}

export function useIndexedDBQueryClient(options: UseIndexedDBQueryClientOptions = {}) {
  const { useIndexedDB = true, enableLogging = true } = options;
  
  // Hook do sistema antigo (localStorage)
  const localStorageQueryClient = useQueryClient();
  
  // Hook do sistema novo (IndexedDB)
  const { queryClient: indexedDBQueryClient, isReady, error } = useIndexedDBContext();

  // Decidir qual sistema usar
  const shouldUseIndexedDB = useIndexedDB && isReady && !error && indexedDBQueryClient;
  const activeQueryClient = shouldUseIndexedDB ? indexedDBQueryClient : localStorageQueryClient;
  const storageType = shouldUseIndexedDB ? 'IndexedDB' : 'localStorage';

  if (enableLogging) {
    logQueryEvent('useIndexedDBQueryClient', `Usando sistema: ${storageType}`, {
      useIndexedDB,
      isReady,
      hasError: !!error,
      hasIndexedDBClient: !!indexedDBQueryClient,
      hasLocalStorageClient: !!localStorageQueryClient,
      activeStorage: storageType
    });
  }

  return {
    queryClient: activeQueryClient,
    storageType,
    isIndexedDBReady: isReady,
    indexedDBError: error,
    usingIndexedDB: shouldUseIndexedDB,
  };
}