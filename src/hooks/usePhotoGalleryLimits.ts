/**
 * Hook para gerenciar limites de galeria de fotos por plano
 * <AUTHOR> Internet 2025
 */
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export function usePhotoGalleryLimits() {
  const { data: limits, isLoading, error, refetch } = useQuery({
    queryKey: ['photogallery-limits'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuário não autenticado');

      const { data: profile } = await supabase
        .from('profiles')
        .select('company_id')
        .eq('id', user.id)
        .single();
      
      if (!profile?.company_id) throw new Error('Perfil não encontrado');

      const { data, error } = await supabase.rpc(
        'validate_content_type_creation',
        {
          tenant_id: profile.company_id,
          content_type: 'photogallery'
        }
      );

      if (error) throw error;
      return data;
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });

  return {
    limits,
    isLoading,
    error,
    canCreate: limits?.can_create ?? false,
    currentCount: limits?.current_count ?? 0,
    maxLimit: limits?.limit,
    remainingSlots: limits?.remaining_slots,
    isUnlimited: limits?.is_unlimited ?? false,
    subscriptionPlan: limits?.subscription_plan ?? 'Grátis',
    refetch
  };
}

/**
 * Hook específico para validar número de fotos em um post
 */
export function usePhotoGalleryValidation() {
  const { maxLimit, isUnlimited, subscriptionPlan } = usePhotoGalleryLimits();
  
  const validatePhotoCount = (photoCount: number): { isValid: boolean; message?: string } => {
    if (isUnlimited) {
      return { isValid: true };
    }
    
    if (photoCount > (maxLimit || 0)) {
      return {
        isValid: false,
        message: `Limite de fotos excedido! Seu plano ${subscriptionPlan} permite até ${maxLimit} fotos por post.`
      };
    }
    
    return { isValid: true };
  };

  const getMaxPhotosAllowed = (): number => {
    return isUnlimited ? -1 : (maxLimit || 15);
  };

  return {
    validatePhotoCount,
    getMaxPhotosAllowed,
    maxLimit: isUnlimited ? -1 : maxLimit,
    subscriptionPlan
  };
}