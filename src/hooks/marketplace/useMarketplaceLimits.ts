/**
 * Hook para gerenciar limitações do marketplace por plano
 * <AUTHOR> Internet 2025
 */
import { useMemo, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useCurrentSubscription } from '@/lib/query/hooks/useSubscriptions';
import { useFeatureAvailability } from '@/lib/query/hooks/useFeatureFlags';
import { useAuthStore } from '@/stores/authStore';
import { QueryKeys } from '@/lib/query/queryKeys';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

export interface MarketplaceLimits {
  // Limitações específicas de marketplace
  maxCategories: number;
  currentCategories: number;
  canCreateCategory: boolean;
  remainingCategorySlots: number | null;
  
  maxItemsPerCategory: number;
  canCreateItem: (categoryId: string) => Promise<boolean>;
  
  maxSpecialOffers: number;
  currentSpecialOffers: number;
  canCreateSpecialOffer: boolean;
  remainingOfferSlots: number | null;
  
  // Verificações de plano
  isFreePlan: boolean;
  isProPlan: boolean;
  isMaxPlan: boolean;
  currentPlan: string;
  
  // Utilitários
  isUnlimited: boolean;
  isPremiumFeaturesEnabled: boolean;
}

export const useMarketplaceLimits = () => {
  const { data: featureData, isLoading: isLoadingFeature } = useFeatureAvailability('marketplace');
  const { data: subscription, isLoading: isLoadingSubscription } = useCurrentSubscription();
  const companyId = useAuthStore(state => state.company_id);
  
  // Query para validação específica de categorias - SEGURANÇA: Sem company_id
  const { data: categoriesValidation, isLoading: isLoadingCategoriesValidation, refetch: refetchCategoriesValidation } = useQuery({
    queryKey: [...QueryKeys.marketplace.all(), 'categories-validation', companyId],
    queryFn: async () => {
      // SEGURANÇA: Não passar company_id - função usa auth.uid() + profiles
      const { data, error } = await supabase
        .rpc('validate_marketplace_category_creation');
      
      if (error) {
        console.error('Erro ao validar categorias do marketplace:', error?.message || error);
        return null;
      }
      
      logQueryEvent('useMarketplaceLimits', 'Validação de categorias executada', { 
        data,
        timestamp: new Date().toISOString()
      });
      
      return data;
    },
    enabled: !!companyId && !isLoadingFeature && !isLoadingSubscription,
    staleTime: 0, // Sempre buscar dados frescos
    cacheTime: 1000 * 60 * 5, // Cache por 5 minutos
    refetchOnWindowFocus: true,
    refetchOnMount: 'always',
  });

  // Query para validação específica de ofertas especiais - SEGURANÇA: Sem company_id
  const { data: offersValidation, isLoading: isLoadingOffersValidation, refetch: refetchOffersValidation } = useQuery({
    queryKey: [...QueryKeys.marketplace.all(), 'offers-validation', companyId],
    queryFn: async () => {
      // SEGURANÇA: Não passar company_id - função usa auth.uid() + profiles
      const { data, error } = await supabase
        .rpc('validate_marketplace_offer_creation');
      
      if (error) {
        console.error('Erro ao validar ofertas especiais do marketplace:', error?.message || error);
        return null;
      }
      
      logQueryEvent('useMarketplaceLimits', 'Validação de ofertas especiais executada', { 
        data,
        timestamp: new Date().toISOString()
      });
      
      return data;
    },
    enabled: !!companyId && !isLoadingFeature && !isLoadingSubscription,
    staleTime: 0,
    cacheTime: 1000 * 60 * 5,
    refetchOnWindowFocus: true,
    refetchOnMount: 'always',
  });
  
  const limits = useMemo((): MarketplaceLimits | null => {
    // Se ainda está carregando, retornar null para manter loading state
    if (isLoadingFeature || isLoadingSubscription || isLoadingCategoriesValidation || isLoadingOffersValidation) {
      return null;
    }
    
    // Determinar plano atual com fallback defensivo
    let currentPlan = 'Grátis'; // Fallback padrão
    
    if (subscription?.subscription_plans?.name) {
      currentPlan = subscription.subscription_plans.name;
    } else {
      logQueryEvent('useMarketplaceLimits', 'Subscription não encontrada, usando plano Grátis como fallback');
    }
    
    // Se não temos dados de validação, usar limites padrão da feature flag
    if (!categoriesValidation || !offersValidation) {
      logQueryEvent('useMarketplaceLimits', 'Dados de validação não disponíveis, usando limites padrão', { currentPlan });
      
      // Limites padrão por plano quando não temos dados do banco
      const defaultLimitsByPlan = {
        'Grátis': { 
          maxCategories: 6, 
          maxItemsPerCategory: 5, 
          maxSpecialOffers: 1,
          canCreate: true 
        },
        'Pro': { 
          maxCategories: 12, 
          maxItemsPerCategory: 10, 
          maxSpecialOffers: 2,
          canCreate: true 
        },
        'Max': { 
          maxCategories: -1, 
          maxItemsPerCategory: -1, 
          maxSpecialOffers: -1,
          canCreate: true 
        },
      };
      
      const defaultLimits = defaultLimitsByPlan[currentPlan as keyof typeof defaultLimitsByPlan] || defaultLimitsByPlan['Grátis'];
      
      return {
        maxCategories: defaultLimits.maxCategories,
        currentCategories: 0,
        canCreateCategory: defaultLimits.canCreate,
        remainingCategorySlots: defaultLimits.maxCategories === -1 ? null : defaultLimits.maxCategories,
        
        maxItemsPerCategory: defaultLimits.maxItemsPerCategory,
        canCreateItem: () => defaultLimits.canCreate,
        
        maxSpecialOffers: defaultLimits.maxSpecialOffers,
        currentSpecialOffers: 0,
        canCreateSpecialOffer: defaultLimits.canCreate,
        remainingOfferSlots: defaultLimits.maxSpecialOffers === -1 ? null : defaultLimits.maxSpecialOffers,
        
        isFreePlan: currentPlan === 'Grátis',
        isProPlan: currentPlan === 'Pro',
        isMaxPlan: currentPlan === 'Max',
        currentPlan,
        isUnlimited: defaultLimits.maxCategories === -1,
        isPremiumFeaturesEnabled: currentPlan !== 'Grátis',
      };
    }
    
    // Se feature está desabilitada, retornar limites mínimos
    if (!featureData?.isFeatureEnabled) {
      logQueryEvent('useMarketplaceLimits', 'Feature desabilitada, usando limites mínimos', { currentPlan });
      
      return {
        maxCategories: 1,
        currentCategories: categoriesValidation.current_count ?? 0,
        canCreateCategory: false,
        remainingCategorySlots: 0,
        
        maxItemsPerCategory: 1,
        canCreateItem: () => false,
        
        maxSpecialOffers: 0,
        currentSpecialOffers: offersValidation.current_count ?? 0,
        canCreateSpecialOffer: false,
        remainingOfferSlots: 0,
        
        isFreePlan: currentPlan === 'Grátis',
        isProPlan: currentPlan === 'Pro',
        isMaxPlan: currentPlan === 'Max',
        currentPlan,
        isUnlimited: false,
        isPremiumFeaturesEnabled: false,
      };
    }
    
    logQueryEvent('useMarketplaceLimits', `Limites carregados para plano ${currentPlan}`, {
      categoriesValidation,
      offersValidation,
      plan: currentPlan,
    });
    
    // Extrair dados da validação de categorias
    const maxCategories = categoriesValidation.limit ?? 6;
    const currentCategories = categoriesValidation.current_count ?? 0;
    const canCreateCategory = categoriesValidation.can_create ?? true;
    const remainingCategorySlots = categoriesValidation.remaining_slots;
    const isCategoriesUnlimited = categoriesValidation.is_unlimited ?? false;
    
    // Extrair dados da validação de ofertas especiais
    const maxSpecialOffers = offersValidation.limit ?? 1;
    const currentSpecialOffers = offersValidation.current_count ?? 0;
    const canCreateSpecialOffer = offersValidation.can_create ?? true;
    const remainingOfferSlots = offersValidation.remaining_slots;
    const isOffersUnlimited = offersValidation.is_unlimited ?? false;
    
    // Extrair limite de itens por categoria da feature flag
    const planLimits = featureData?.featureFlag?.access_levels?.[currentPlan]?.limits;
    const maxItemsPerCategory = planLimits?.max_items_per_category ?? 5;
    const isItemsUnlimited = maxItemsPerCategory === -1;
    
    // Função para validar criação de item em categoria específica - SEGURANÇA: Sem company_id
    const canCreateItem = async (categoryId: string): Promise<boolean> => {
      if (isItemsUnlimited) return true;
      
      try {
        // SEGURANÇA: Só passar category_id - função usa auth.uid() + profiles para company_id
        const { data, error } = await supabase
          .rpc('validate_marketplace_item_creation', {
            p_category_id: categoryId
          });
        
        if (error) {
          console.error('Erro ao validar item do marketplace');
          return false;
        }
        
        return data?.can_create ?? false;
      } catch (error) {
        console.error('Erro ao validar item do marketplace (catch)');
        return false;
      }
    };
    
    return {
      maxCategories,
      currentCategories,
      canCreateCategory,
      remainingCategorySlots,
      
      maxItemsPerCategory,
      canCreateItem,
      
      maxSpecialOffers,
      currentSpecialOffers,
      canCreateSpecialOffer,
      remainingOfferSlots,
      
      isFreePlan: currentPlan === 'Grátis',
      isProPlan: currentPlan === 'Pro',
      isMaxPlan: currentPlan === 'Max',
      currentPlan,
      isUnlimited: isCategoriesUnlimited && isOffersUnlimited && isItemsUnlimited,
      isPremiumFeaturesEnabled: currentPlan !== 'Grátis',
    };
  }, [
    featureData,
    subscription,
    categoriesValidation,
    offersValidation,
    isLoadingFeature,
    isLoadingSubscription,
    isLoadingCategoriesValidation,
    isLoadingOffersValidation,
    companyId,
  ]);
  
  // Force refetch quando há mudanças significativas
  useEffect(() => {
    if (companyId) {
      refetchCategoriesValidation();
      refetchOffersValidation();
    }
  }, [companyId, refetchCategoriesValidation, refetchOffersValidation]);
  
  return {
    limits,
    isLoading: isLoadingFeature || isLoadingSubscription || isLoadingCategoriesValidation || isLoadingOffersValidation,
    isEnabled: featureData?.isFeatureEnabled ?? true,
    featureFlag: featureData?.featureFlag,
    
    // Função para forçar atualização
    refetch: () => {
      refetchCategoriesValidation();
      refetchOffersValidation();
    },
    
    // Função helper para verificar se pode criar categoria
    canCreateCategory: () => limits?.canCreateCategory ?? false,
    
    // Função helper para verificar se pode criar oferta especial
    canCreateSpecialOffer: () => limits?.canCreateSpecialOffer ?? false,
    
    // Função helper para obter mensagem de limite de categorias
    getCategoriesLimitMessage: () => {
      if (!limits) return 'Carregando limites...';
      
      if (limits.isUnlimited) {
        return 'Categorias ilimitadas';
      }
      
      if (limits.remainingCategorySlots === 0) {
        return `Limite atingido: ${limits.currentCategories}/${limits.maxCategories}`;
      }
      
      if (limits.remainingCategorySlots === 1) {
        return `Você pode criar mais 1 categoria`;
      }
      
      return `Você pode criar mais ${limits.remainingCategorySlots} categorias`;
    },
    
    // Função helper para obter mensagem de limite de ofertas especiais
    getOffersLimitMessage: () => {
      if (!limits) return 'Carregando limites...';
      
      if (limits.isUnlimited) {
        return 'Ofertas especiais ilimitadas';
      }
      
      if (limits.remainingOfferSlots === 0) {
        return `Limite atingido: ${limits.currentSpecialOffers}/${limits.maxSpecialOffers}`;
      }
      
      if (limits.remainingOfferSlots === 1) {
        return `Você pode criar mais 1 oferta especial`;
      }
      
      return `Você pode criar mais ${limits.remainingOfferSlots} ofertas especiais`;
    },
    
    // Função helper para obter mensagem de limite de itens por categoria
    getItemsPerCategoryLimitMessage: () => {
      if (!limits) return 'Carregando limites...';
      
      if (limits.maxItemsPerCategory === -1) {
        return 'Itens ilimitados por categoria';
      }
      
      return `Máximo de ${limits.maxItemsPerCategory} itens por categoria`;
    },
    
    // Função helper para obter dados específicos de uma categoria
    getCategoryItemsInfo: async (categoryId: string) => {
      if (!limits) return null;
      
      try {
        const { data, error } = await supabase
          .rpc('validate_marketplace_item_creation', {
            p_category_id: categoryId
          });
        
        if (error) {
          console.error('Erro ao buscar dados da categoria');
          return null;
        }
        
        return {
          canCreate: data?.can_create ?? false,
          currentCount: data?.current_count ?? 0,
          limit: data?.limit ?? limits.maxItemsPerCategory,
          remainingSlots: data?.remaining_slots,
          isUnlimited: data?.is_unlimited ?? false,
        };
      } catch (error) {
        console.error('Erro ao buscar dados da categoria (catch)');
        return null;
      }
    },
  };
};