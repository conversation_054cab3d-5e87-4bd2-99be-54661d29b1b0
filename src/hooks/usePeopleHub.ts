/**
 * Hook para buscar dados de pessoas para o PeopleHub
 * <AUTHOR> Internet 2025
 */
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { QueryKeys } from '@/lib/query/queryKeys';
import { extractDateParts } from '@/lib/utils/dateUtils';

export interface Person {
  id: string;
  full_name: string;
  email: string;
  avatar_url: string;
  phone?: string;
  job_title?: string;
  department?: string;
  location?: string;
  hire_date?: string;
  birthday?: string;
  status: "online" | "away" | "busy" | "offline";
  skills?: string[];
  bio?: string;
  teams?: string[];
  manager_id?: string;
  reports_count?: number;
  last_seen?: string;
  absence_info?: {
    type: "vacation" | "sick_leave" | "maternity_leave" | "personal_leave";
    subtype?: "annual" | "partial" | "medical" | "emergency";
    start_date: string;
    end_date: string;
    return_date: string;
    description?: string;
  };
  social_links?: {
    linkedin?: string;
    github?: string;
    twitter?: string;
  };
  languages?: string[];
}

export interface PeopleAnalytics {
  totalEmployees: number;
  activeToday: number;
  newHires: number;
  departments: number;
  avgTenure: number;
  birthdaysThisWeek: number;
  topSkills: { skill: string; count: number }[];
  locations: { location: string; count: number }[];
}

/**
 * Hook para buscar todas as pessoas da empresa
 */
export function usePeopleDirectory() {
  return useQuery({
    queryKey: QueryKeys.users.all,
    queryFn: async () => {
      // Obter company_id do usuário atual
      console.log("🔍 [usePeopleDirectory] Iniciando busca...");
      
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError) {
        console.error("❌ [usePeopleDirectory] Erro de autenticação:", authError);
        throw authError;
      }
      if (!user) {
        console.error("❌ [usePeopleDirectory] Usuário não autenticado");
        throw new Error("Usuário não autenticado");
      }

      console.log("✅ [usePeopleDirectory] Usuário autenticado:", user.id);

      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("company_id")
        .eq("id", user.id)
        .single();

      if (profileError) {
        console.error("❌ [usePeopleDirectory] Erro ao buscar perfil:", profileError);
        throw profileError;
      }
      if (!profile?.company_id) {
        console.error("❌ [usePeopleDirectory] Company ID não encontrado:", profile);
        throw new Error("Empresa não encontrada");
      }

      console.log("✅ [usePeopleDirectory] Company ID encontrado:", profile.company_id);

      // Buscar todos os perfis da empresa com dados relacionados
      console.log("🔍 [usePeopleDirectory] Buscando perfis da empresa...");
      
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          avatar_url,
          phone,
          bio,
          hire_date,
          birthdate,
          active,
          created_at,
          linkedin_url,
          github_url,
          twitter_url,
          unit_id,
          job_titles!job_title_id (
            id,
            title
          ),
          departments!department_id (
            id,
            name
          )
        `)
        .eq('company_id', profile.company_id)
        .eq('active', true)
        .order('full_name');

      if (error) {
        console.error("❌ [usePeopleDirectory] Erro na query principal:", error);
        throw error;
      }

      console.log("✅ [usePeopleDirectory] Perfis encontrados:", data?.length || 0);

      // Buscar informações adicionais de unidades se necessário
      const unitIds = data.filter(p => p.unit_id).map(p => p.unit_id);
      let unitsData: any = { data: [] };
      if (unitIds.length > 0) {
        unitsData = await supabase
          .from('units')
          .select('id, name, location_id, locations!location_id(id, city, state)')
          .in('id', unitIds);
        
        if (unitsData.error) {
          console.warn('Erro ao buscar unidades:', unitsData.error);
          unitsData = { data: [] };
        }
      }

      // Criar maps para facilitar busca
      const unitsMap = new Map(unitsData.data?.map((unit: any) => [unit.id, unit]) || []);

      // Buscar skills e languages para todos os usuários
      const userIds = data.map(p => p.id);
      
      const [skillsData, languagesData] = await Promise.all([
        // Buscar skills dos usuários
        supabase
          .from('user_skills')
          .select(`
            user_id,
            skill_id,
            company_skill_id,
            skill_type
          `)
          .in('user_id', userIds),
        
        // Buscar languages dos usuários
        supabase
          .from('user_languages')
          .select(`
            user_id,
            language_id
          `)
          .in('user_id', userIds)
      ]);

      if (skillsData.error) console.warn('Erro ao buscar skills:', skillsData.error);
      if (languagesData.error) console.warn('Erro ao buscar languages:', languagesData.error);

      // Organizar skills e languages por usuário
      const skillsByUser = new Map<string, string[]>();
      const languagesByUser = new Map<string, string[]>();

      // Buscar nomes das skills
      if (skillsData.data && skillsData.data.length > 0) {
        const systemSkillIds = skillsData.data.filter(s => s.skill_type === 'system' && s.skill_id).map(s => s.skill_id);
        const companySkillIds = skillsData.data.filter(s => s.skill_type === 'company' && s.company_skill_id).map(s => s.company_skill_id);

        const [systemSkills, companySkills] = await Promise.all([
          systemSkillIds.length > 0 ? supabase.from('skills').select('id, name').in('id', systemSkillIds) : { data: [] },
          companySkillIds.length > 0 ? supabase.from('company_skills').select('id, name').in('id', companySkillIds) : { data: [] }
        ]);

        const systemSkillsMap = new Map((systemSkills.data || []).map(s => [s.id, s.name]));
        const companySkillsMap = new Map((companySkills.data || []).map(s => [s.id, s.name]));

        skillsData.data.forEach((skill: any) => {
          if (!skillsByUser.has(skill.user_id)) {
            skillsByUser.set(skill.user_id, []);
          }
          
          let skillName = '';
          if (skill.skill_type === 'system' && skill.skill_id) {
            skillName = systemSkillsMap.get(skill.skill_id) || '';
          } else if (skill.skill_type === 'company' && skill.company_skill_id) {
            skillName = companySkillsMap.get(skill.company_skill_id) || '';
          }
          
          if (skillName) {
            skillsByUser.get(skill.user_id)!.push(skillName);
          }
        });
      }

      // Buscar nomes dos idiomas
      if (languagesData.data && languagesData.data.length > 0) {
        const languageIds = [...new Set(languagesData.data.map(l => l.language_id))];
        const { data: languages } = await supabase
          .from('languages')
          .select('id, name')
          .in('id', languageIds);

        const languagesMap = new Map((languages || []).map(l => [l.id, l.name]));

        languagesData.data.forEach((lang: any) => {
          if (!languagesByUser.has(lang.user_id)) {
            languagesByUser.set(lang.user_id, []);
          }
          
          const languageName = languagesMap.get(lang.language_id);
          if (languageName) {
            languagesByUser.get(lang.user_id)!.push(languageName);
          }
        });
      }

      // Transformar dados para o formato esperado pelo PeopleHub
      const people: Person[] = data.map(profile => {
        // Determinar status baseado na atividade recente (simplificado)
        let status: Person['status'] = 'offline';
        if (profile.created_at) {
          const lastActivity = new Date(profile.created_at);
          const now = new Date();
          const daysSinceActivity = (now.getTime() - lastActivity.getTime()) / (1000 * 3600 * 24);
          
          if (daysSinceActivity < 1) status = 'online';
          else if (daysSinceActivity < 7) status = 'away';
          else status = 'offline';
        }

        // Buscar informações da unidade e localização
        const unit = profile.unit_id ? unitsMap.get(profile.unit_id) : null;
        const location = unit?.locations ? `${unit.locations.city}, ${unit.locations.state}` : null;

        return {
          id: profile.id,
          full_name: profile.full_name || '',
          email: profile.email || '',
          avatar_url: profile.avatar_url || '/placeholder.svg',
          phone: profile.phone || undefined,
          job_title: profile.job_titles?.title || undefined,
          department: profile.departments?.name || undefined,
          location: location || undefined,
          hire_date: profile.hire_date || undefined,
          birthday: profile.birthdate || undefined,
          status,
          skills: skillsByUser.get(profile.id) || [], // Skills do banco de dados
          bio: profile.bio || undefined,
          teams: [], // TODO: Implementar relação com teams
          manager_id: undefined, // TODO: Implementar hierarquia
          reports_count: 0, // TODO: Calcular subordinados
          last_seen: profile.created_at,
          social_links: {
            linkedin: profile.linkedin_url || undefined,
            github: profile.github_url || undefined,
            twitter: profile.twitter_url || undefined,
          },
          languages: languagesByUser.get(profile.id) || [], // Languages do banco de dados
        };
      });

      return people;
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
}

/**
 * Hook para buscar analytics das pessoas da empresa
 */
export function usePeopleAnalytics() {
  return useQuery({
    queryKey: ['people', 'analytics'],
    queryFn: async () => {
      // Obter company_id do usuário atual
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Usuário não autenticado");

      const { data: profile } = await supabase
        .from("profiles")
        .select("company_id")
        .eq("id", user.id)
        .single();

      if (!profile?.company_id) throw new Error("Empresa não encontrada");

      // Buscar dados para analytics
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select(`
          id,
          hire_date,
          birthdate,
          created_at,
          active,
          unit_id,
          departments:department_id (name)
        `)
        .eq('company_id', profile.company_id)
        .eq('active', true);

      if (profilesError) throw profilesError;

      // Buscar informações de unidades e localizações para analytics
      const unitIds = profiles.filter(p => p.unit_id).map(p => p.unit_id);
      let unitsData: any = { data: [] };
      if (unitIds.length > 0) {
        unitsData = await supabase
          .from('units')
          .select('id, name, location_id, locations!location_id(id, city, state)')
          .in('id', unitIds);
        
        if (unitsData.error) {
          console.warn('Erro ao buscar unidades para analytics:', unitsData.error);
          unitsData = { data: [] };
        }
      }

      const unitsMap = new Map(unitsData.data?.map((unit: any) => [unit.id, unit]) || []);

      const now = new Date();
      const thisWeekStart = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
      const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);

      // Calcular métricas
      const totalEmployees = profiles.length;
      
      // Considerar todos como ativos por enquanto (pode ser melhorado com log de sessões)
      const activeToday = totalEmployees;
      
      // Novos contratados este mês
      const newHires = profiles.filter(p => {
        if (!p.hire_date) return false;
        const hireDate = new Date(p.hire_date);
        return hireDate >= thisMonthStart;
      }).length;

      // Departamentos únicos
      const departments = [...new Set(profiles.map(p => p.departments?.name).filter(Boolean))].length;

      // Tenure médio (simplificado)
      const avgTenure = profiles
        .filter(p => p.hire_date)
        .reduce((acc, p) => {
          const hireDate = new Date(p.hire_date!);
          const tenure = (now.getTime() - hireDate.getTime()) / (1000 * 3600 * 24 * 365);
          return acc + tenure;
        }, 0) / profiles.filter(p => p.hire_date).length || 0;

      // Aniversários esta semana
      const birthdaysThisWeek = profiles.filter(p => {
        if (!p.birthdate) return false;
        const { day, month } = extractDateParts(p.birthdate);
        const currentYear = now.getFullYear();
        const birthdayThisYear = new Date(currentYear, month - 1, day); // month-1 porque Date usa 0-indexed
        
        // Se já passou este ano, considerar o próximo
        if (birthdayThisYear < now) {
          birthdayThisYear.setFullYear(currentYear + 1);
        }
        
        const daysDiff = (birthdayThisYear.getTime() - now.getTime()) / (1000 * 3600 * 24);
        return daysDiff <= 7 && daysDiff >= 0;
      }).length;

      // Top skills baseado em dados reais
      const { data: skillsStats, error: skillsStatsError } = await supabase
        .from('user_skills')
        .select(`
          skill_id,
          company_skill_id,
          skill_type,
          profiles!inner (company_id)
        `)
        .eq('profiles.company_id', profile.company_id);

      let topSkills: { skill: string; count: number }[] = [];
      
      if (!skillsStatsError && skillsStats && skillsStats.length > 0) {
        // Buscar nomes das skills
        const systemSkillIds = skillsStats.filter(s => s.skill_type === 'system' && s.skill_id).map(s => s.skill_id);
        const companySkillIds = skillsStats.filter(s => s.skill_type === 'company' && s.company_skill_id).map(s => s.company_skill_id);

        const [systemSkills, companySkills] = await Promise.all([
          systemSkillIds.length > 0 ? supabase.from('skills').select('id, name').in('id', systemSkillIds) : { data: [] },
          companySkillIds.length > 0 ? supabase.from('company_skills').select('id, name').in('id', companySkillIds) : { data: [] }
        ]);

        const systemSkillsMap = new Map((systemSkills.data || []).map(s => [s.id, s.name]));
        const companySkillsMap = new Map((companySkills.data || []).map(s => [s.id, s.name]));

        const skillCounts = new Map<string, number>();
        
        skillsStats.forEach((skill: any) => {
          let skillName = '';
          if (skill.skill_type === 'system' && skill.skill_id) {
            skillName = systemSkillsMap.get(skill.skill_id) || '';
          } else if (skill.skill_type === 'company' && skill.company_skill_id) {
            skillName = companySkillsMap.get(skill.company_skill_id) || '';
          }
          
          if (skillName) {
            skillCounts.set(skillName, (skillCounts.get(skillName) || 0) + 1);
          }
        });

        topSkills = Array.from(skillCounts.entries())
          .map(([skill, count]) => ({ skill, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5);
      }

      // Distribuição por localização
      const locationCounts = profiles.reduce((acc, p) => {
        const unit = p.unit_id ? unitsMap.get(p.unit_id) : null;
        const location = unit?.locations ? `${unit.locations.city}, ${unit.locations.state}` : 'Não informado';
        acc[location] = (acc[location] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const locations = Object.entries(locationCounts)
        .map(([location, count]) => ({ location, count }))
        .sort((a, b) => b.count - a.count);

      const analytics: PeopleAnalytics = {
        totalEmployees,
        activeToday,
        newHires,
        departments,
        avgTenure: Number(avgTenure.toFixed(1)),
        birthdaysThisWeek,
        topSkills,
        locations,
      };

      return analytics;
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
}

/**
 * Hook para buscar sugestões inteligentes de networking
 */
export function usePeopleNetworking() {
  return useQuery({
    queryKey: QueryKeys.people.networking(),
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Usuário não autenticado");

      // Usar a função SQL para gerar sugestões inteligentes
      // A função usa auth.uid() internamente para segurança
      const { data: suggestions, error: suggestionsError } = await supabase
        .rpc('generate_networking_suggestions');

      if (suggestionsError) throw suggestionsError;

      if (!suggestions || suggestions.length === 0) {
        return [];
      }

      // Buscar dados completos dos usuários sugeridos
      const userIds = suggestions.map((s: any) => s.suggested_user_id);
      
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          avatar_url,
          phone,
          bio,
          linkedin_url,
          github_url,
          twitter_url,  
          unit_id,
          job_titles:job_title_id (title),
          departments:department_id (name)
        `)
        .in('id', userIds);

      if (profilesError) throw profilesError;

      // Buscar informações de unidades para essas pessoas
      const networkingUnitIds = profiles.filter(p => p.unit_id).map(p => p.unit_id);
      let networkingUnitsData: any = { data: [] };
      if (networkingUnitIds.length > 0) {
        networkingUnitsData = await supabase
          .from('units')
          .select('id, name, location_id, locations!location_id(id, city, state)')
          .in('id', networkingUnitIds);
        
        if (networkingUnitsData.error) {
          console.warn('Erro ao buscar unidades para networking:', networkingUnitsData.error);
          networkingUnitsData = { data: [] };
        }
      }

      const networkingUnitsMap = new Map(networkingUnitsData.data?.map((unit: any) => [unit.id, unit]) || []);

      // Buscar skills e languages para essas pessoas
      const [skillsData, languagesData] = await Promise.all([
        supabase
          .from('user_skills')
          .select(`
            user_id,
            skill_id,
            company_skill_id,
            skill_type
          `)
          .in('user_id', userIds),
        
        supabase
          .from('user_languages')
          .select(`
            user_id,
            language_id
          `)
          .in('user_id', userIds)
      ]);

      // Organizar skills e languages por usuário
      const skillsByUser = new Map<string, string[]>();
      const languagesByUser = new Map<string, string[]>();

      // Processar skills
      if (skillsData.data && skillsData.data.length > 0) {
        const systemSkillIds = skillsData.data.filter(s => s.skill_type === 'system' && s.skill_id).map(s => s.skill_id);
        const companySkillIds = skillsData.data.filter(s => s.skill_type === 'company' && s.company_skill_id).map(s => s.company_skill_id);

        const [systemSkills, companySkills] = await Promise.all([
          systemSkillIds.length > 0 ? supabase.from('skills').select('id, name').in('id', systemSkillIds) : { data: [] },
          companySkillIds.length > 0 ? supabase.from('company_skills').select('id, name').in('id', companySkillIds) : { data: [] }
        ]);

        const systemSkillsMap = new Map((systemSkills.data || []).map(s => [s.id, s.name]));
        const companySkillsMap = new Map((companySkills.data || []).map(s => [s.id, s.name]));

        skillsData.data.forEach((skill: any) => {
          if (!skillsByUser.has(skill.user_id)) {
            skillsByUser.set(skill.user_id, []);
          }
          
          let skillName = '';
          if (skill.skill_type === 'system' && skill.skill_id) {
            skillName = systemSkillsMap.get(skill.skill_id) || '';
          } else if (skill.skill_type === 'company' && skill.company_skill_id) {
            skillName = companySkillsMap.get(skill.company_skill_id) || '';
          }
          
          if (skillName) {
            skillsByUser.get(skill.user_id)!.push(skillName);
          }
        });
      }

      // Processar languages
      if (languagesData.data && languagesData.data.length > 0) {
        const languageIds = [...new Set(languagesData.data.map(l => l.language_id))];
        const { data: languages } = await supabase
          .from('languages')
          .select('id, name')
          .in('id', languageIds);

        const languagesMap = new Map((languages || []).map(l => [l.id, l.name]));

        languagesData.data.forEach((lang: any) => {
          if (!languagesByUser.has(lang.user_id)) {
            languagesByUser.set(lang.user_id, []);
          }
          
          const languageName = languagesMap.get(lang.language_id);
          if (languageName) {
            languagesByUser.get(lang.user_id)!.push(languageName);
          }
        });
      }

      // Combinar sugestões com dados dos perfis
      const people: (Person & { suggestion_reasons: any; suggestion_score: number })[] = suggestions.map((suggestion: any) => {
        const profile = profiles?.find(p => p.id === suggestion.suggested_user_id);
        if (!profile) return null;

        // Buscar informações da unidade e localização
        const unit = profile.unit_id ? networkingUnitsMap.get(profile.unit_id) : null;
        const location = unit?.locations ? `${unit.locations.city}, ${unit.locations.state}` : undefined;

        return {
          id: profile.id,
          full_name: profile.full_name || '',
          email: profile.email || '',
          avatar_url: profile.avatar_url || '/placeholder.svg',
          phone: profile.phone || undefined,
          job_title: profile.job_titles?.title || undefined,
          department: profile.departments?.name || undefined,
          location: location,
          status: 'online' as const,
          skills: skillsByUser.get(profile.id) || [],
          bio: profile.bio || undefined,
          social_links: {
            linkedin: profile.linkedin_url || undefined,
            github: profile.github_url || undefined,
            twitter: profile.twitter_url || undefined,
          },
          languages: languagesByUser.get(profile.id) || [],
          suggestion_reasons: suggestion.reasons,
          suggestion_score: suggestion.score
        };
      }).filter(Boolean);

      return people;
    },
    staleTime: 10 * 60 * 1000, // 10 minutos
  });
}