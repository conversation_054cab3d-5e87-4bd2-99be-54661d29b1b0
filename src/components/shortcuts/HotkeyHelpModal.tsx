/**
 * Central de Ajuda - Modal para exibir atalhos, tutoriais e conteúdo de help contextual
 * Acionado por F1 ou através do menu do usuário
 * <AUTHOR> Internet 2025
 */
import React, { useState, useEffect, useMemo } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Keyboard, X, Sparkles, Zap, Lightbulb, BookOpen, 
  Video, FileText, HelpCircle, ChevronRight, Search,
  MessageSquare, Users, Settings, Database, Shield, RocketIcon,
  Phone, Mail, MessageCircle, ExternalLink, ArrowLeft
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { useLocation } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { usePageHelpData, useHelpResources, useHelpGuides } from "@/lib/query/hooks/useHelpContent";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
import { PlanSummaryWidget } from "@/components/plans/PlanSummaryWidget";

interface Hotkey {
  key: string;
  description: string;
  category: string;
  adminOnly?: boolean;
}

interface HotkeyHelpModalProps {
  isOpen: boolean;
  onClose: () => void;
  globalHotkeys: Hotkey[];
  contextualHotkeys?: Hotkey[];
}

/**
 * Função para forçar reset do pointer-events no body
 * Solução para bug crítico de travamento da interface após fechar dialogs
 */
const resetPointerEvents = () => {
  // Força remoção do pointer-events: none do body
  document.body.style.pointerEvents = 'auto';
  
  // Força remoção do pointer-events de todos os elementos overlay
  const overlays = document.querySelectorAll('[data-radix-popper-content-wrapper]');
  overlays.forEach(overlay => {
    if (overlay instanceof HTMLElement) {
      overlay.style.pointerEvents = 'auto';
    }
  });
  
  // Remove atributos que podem causar conflito
  document.body.removeAttribute('data-scroll-locked');
  document.body.style.overflow = '';
  document.body.style.paddingRight = '';
};

// Fallback data para quando não há dados no banco
const fallbackHelpSections = [
  {
    id: 'feed',
    title: 'Feed & Publicações',
    icon: MessageSquare,
    description: 'Aprenda a criar, compartilhar e interagir com posts de forma eficiente',
    topics: [
      'Como criar publicações envolventes e atrativas',
      'Estratégias de uso de menções (@) e hashtags (#)',
      'Agendamento inteligente de posts para máximo alcance',
      'Entendendo a política de moderação e boas práticas',
      'Dominando formatação de texto e inserção de mídia'
    ]
  },
  {
    id: 'knowledge',
    title: 'Knowledge Hub',
    icon: BookOpen,
    description: 'Domine a gestão de conhecimento e documentação colaborativa',
    topics: [
      'Estruturando espaços de conhecimento eficientes',
      'Sistema de tags e categorias para organização máxima',
      'Colaboração em tempo real: melhor que Google Docs',
      'Criando templates reutilizáveis para produtividade',
      'Versionamento e histórico: nunca perca alterações'
    ]
  },
  {
    id: 'chat',
    title: 'Chat & Comunicação',
    icon: MessageSquare,
    description: 'Otimize a comunicação em equipe com canais e mensagens inteligentes',
    topics: [
      'Arquitetura de canais: organizando conversas por tema',
      'Mensagens diretas vs. grupos: quando usar cada um',
      'Compartilhamento eficiente de arquivos e documentos',
      'Configurações avançadas de notificação por contexto',
      'Threads e organização: mantendo conversas focadas'
    ]
  },
  {
    id: 'team',
    title: 'Gestão de Equipe',
    icon: Users,
    description: 'Coordene pessoas, perfis e hierarquias organizacionais',
    topics: [
      'Configurando perfis completos para networking interno',
      'Estrutura hierárquica: departamentos e cargos',
      'Sistema de permissões granulares por função',
      'Dashboards de acompanhamento de atividades da equipe',
      'Relatórios de produtividade e engajamento'
    ]
  },
  {
    id: 'admin',
    title: 'Administração',
    icon: Settings,
    description: 'Configurações avançadas para gestores e administradores da plataforma',
    topics: [
      'Configurações master da empresa e branding',
      'Gerenciamento avançado de usuários e permissões',
      'Integrações com sistemas externos via API',
      'Políticas de backup automático e recuperação',
      'Compliance, auditoria e políticas de segurança'
    ]
  },
  {
    id: 'security',
    title: 'Segurança & Privacidade',
    icon: Shield,
    description: 'Proteja dados sensíveis e configure segurança avançada',
    topics: [
      'Configurações de privacidade pessoal e empresarial',
      'Autenticação de dois fatores (2FA) para máxima segurança',
      'Controles de acesso baseados em função (RBAC)',
      'Auditoria completa: quem fez o quê e quando',
      'Conformidade com LGPD e outras regulamentações'
    ]
  }
];

// Fallback para recursos de suporte
const fallbackResources = [
  {
    id: 'videos',
    resource_type: 'video' as const,
    title: 'Vídeos Tutoriais',
    description: 'Aprenda com vídeos passo-a-passo gravados pela nossa equipe. Desde conceitos básicos até funcionalidades avançadas.',
    icon_name: 'Video'
  },
  {
    id: 'articles',
    resource_type: 'article' as const,
    title: 'Base de Conhecimento',
    description: 'Artigos detalhados, FAQs e guias escritos. Busque por qualquer tópico específico ou navegue pelas categorias organizadas.',
    icon_name: 'FileText'
  },
  {
    id: 'support',
    resource_type: 'live_support' as const,
    title: 'Suporte ao Vivo',
    description: 'Chat direto com nossa equipe de suporte especializada. Resolva dúvidas complexas em tempo real.',
    icon_name: 'MessageSquare'
  },
  {
    id: 'emergency',
    resource_type: 'emergency' as const,
    title: 'Precisa de Ajuda Urgente?',
    description: 'Para questões críticas ou emergenciais, entre em contato pelo nosso canal prioritário de suporte técnico.',
    icon_name: 'HelpCircle'
  }
];

const containerVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.2,
      ease: "easeOut",
      staggerChildren: 0.05
    }
  },
  exit: { opacity: 0, scale: 0.95, transition: { duration: 0.15 } }
};

const itemVariants = {
  hidden: { opacity: 0, x: -10 },
  visible: { opacity: 1, x: 0 }
};

export function HotkeyHelpModal({ 
  isOpen, 
  onClose, 
  globalHotkeys, 
  contextualHotkeys = [] 
}: HotkeyHelpModalProps) {
  const location = useLocation();
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showContactPage, setShowContactPage] = useState(false);
  
  // Determinar página atual para mostrar contexto
  const getCurrentPageKey = () => {
    const path = location.pathname;
    if (path.includes('knowledge')) return 'knowledge';
    if (path.includes('feed')) return 'feed';
    if (path.includes('chat')) return 'chat';
    if (path.includes('tasks')) return 'tasks';
    if (path.includes('library')) return 'biblioteca';
    if (path.includes('team')) return 'equipe';
    if (path.includes('admin')) return 'admin';
    return 'geral';
  };

  const currentPageKey = getCurrentPageKey();
  
    // Buscar dados dinâmicos do banco
  const { data: pageHelpData, isLoading: pageLoading, error: pageError } = usePageHelpData(currentPageKey);
  const { data: helpResources, isLoading: resourcesLoading, error: resourcesError } = useHelpResources();
  const { data: helpGuides, isLoading: guidesLoading, error: guidesError } = useHelpGuides();

  // Log de debug para troubleshooting
  useEffect(() => {
    if (isOpen) {
      logQueryEvent('HotkeyHelpModal', `Modal aberto para página: ${currentPageKey}`, {
        pageHelpData,
        helpResources,
        helpGuides,
        pageLoading,
        resourcesLoading,
        guidesLoading
      });
    }
  }, [isOpen, currentPageKey, pageHelpData, helpResources, helpGuides, pageLoading, resourcesLoading, guidesLoading]);
  
  // Reset states when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedSection(null);
      setSearchQuery("");
      setShowContactPage(false);
    }
  }, [isOpen]);

  // 🛡️ ANTI-FREEZE: Cleanup automático quando componente desmonta
  useEffect(() => {
    return () => {
      // Cleanup para prevenir travamento da interface
      setTimeout(() => {
        resetPointerEvents();
      }, 100);
    };
  }, []);

  // 🛡️ ANTI-FREEZE: Enhanced close handler with proper cleanup
  const handleClose = () => {
    setSelectedSection(null);
    setSearchQuery("");
    setShowContactPage(false);
    
    // Força reset antes de fechar
    resetPointerEvents();
    
    // Fecha o modal com delay para garantir limpeza
    setTimeout(() => {
      onClose();
      // Segundo reset após fechamento
      setTimeout(() => {
        resetPointerEvents();
      }, 50);
    }, 50);
  };

  // Handler para abrir página de contato
  const handleEmergencyContact = () => {
    setShowContactPage(true);
    logQueryEvent('HotkeyHelpModal', 'Página de contato de emergência acessada');
  };

  // Handler para voltar da página de contato
  const handleBackFromContact = () => {
    setShowContactPage(false);
  };

  // Handler para voltar da página de seção
  const handleBackFromSection = () => {
    setSelectedSection(null);
  };

  // Organizar atalhos por categoria
  const categorizedHotkeys = useMemo(() => {
    const categories: Record<string, Hotkey[]> = {};
    
    globalHotkeys.forEach(hotkey => {
      if (!categories[hotkey.category]) {
        categories[hotkey.category] = [];
      }
      categories[hotkey.category].push(hotkey);
    });
    
    return categories;
  }, [globalHotkeys]);
  
  // Função para obter nome da página em português
  const getCurrentPageName = () => {
    const pageNames: Record<string, string> = {
      'knowledge': 'Knowledge Hub',
      'feed': 'Feed',
      'chat': 'Chat',
      'tasks': 'Tasks',
      'biblioteca': 'Biblioteca',
      'equipe': 'Equipe',
      'admin': 'Administração',
      'geral': 'Geral'
    };
    return pageNames[currentPageKey] || 'Geral';
  };

  const currentPage = getCurrentPageName();
  
  // Funções para obter informações específicas da página atual (com fallback)
  const getPageIcon = (page: string) => {
    const icons: Record<string, JSX.Element> = {
      'Feed': <MessageSquare className="h-4 w-4 text-white" />,
      'Knowledge Hub': <BookOpen className="h-4 w-4 text-white" />,
      'Chat': <MessageSquare className="h-4 w-4 text-white" />,
      'Tasks': <FileText className="h-4 w-4 text-white" />,
      'Biblioteca': <Database className="h-4 w-4 text-white" />,
      'Equipe': <Users className="h-4 w-4 text-white" />,
      'Administração': <Settings className="h-4 w-4 text-white" />,
      'Geral': <HelpCircle className="h-4 w-4 text-white" />
    };
    return icons[page] || icons['Geral'];
  };

  const getPageEmoji = (page: string): string => {
    const emojis: Record<string, string> = {
      'Feed': '📢',
      'Knowledge Hub': '📚',
      'Chat': '💬', 
      'Tasks': '✅',
      'Biblioteca': '📁',
      'Equipe': '👥',
      'Administração': '⚙️',
      'Geral': '🏠'
    };
    return emojis[page] || emojis['Geral'];
  };

  const getPageTitle = (): string => {
    if (pageHelpData?.title) {
      return `Sobre ${pageHelpData.title}`;
    }
    
    const fallbackTitles: Record<string, string> = {
      'Feed': 'Sobre o Feed de Publicações',
      'Knowledge Hub': 'Sobre o Knowledge Hub',
      'Chat': 'Sobre o Sistema de Chat',
      'Tasks': 'Sobre o Gerenciador de Tasks',
      'Biblioteca': 'Sobre a Biblioteca de Arquivos',
      'Equipe': 'Sobre a Gestão de Equipe',
      'Administração': 'Sobre o Painel Administrativo',
      'Geral': 'Sobre o Vindula Cosmos'
    };
    return fallbackTitles[currentPage] || fallbackTitles['Geral'];
  };

  const getPageDescription = (): string => {
    if (pageHelpData?.description) {
      return pageHelpData.description;
    }
    
    const fallbackDescriptions: Record<string, string> = {
      'Feed': 'O Feed é o coração social do Vindula Cosmos. Aqui você compartilha ideias, colabora com colegas e mantém-se atualizado com as atividades da empresa.',
      'Knowledge Hub': 'O Knowledge Hub é sua biblioteca inteligente de conhecimento organizacional. Crie, organize e compartilhe documentos, artigos e informações importantes.',
      'Chat': 'O Sistema de Chat conecta sua equipe em tempo real. Converse por mensagens diretas, participe de canais temáticos e mantenha comunicação fluida.',
      'Tasks': 'O Gerenciador de Tasks organiza seu trabalho e o da equipe. Crie, delegue e acompanhe tarefas com prazos, prioridades e status.',
      'Biblioteca': 'A Biblioteca é seu repositório seguro de arquivos e documentos. Organize, compartilhe e gerencie todos os recursos digitais da empresa.',
      'Equipe': 'A Gestão de Equipe centraliza informações sobre pessoas, departamentos e hierarquia organizacional.',
      'Administração': 'O Painel Administrativo oferece controle total sobre configurações, usuários, permissões e integrações.',
      'Geral': 'O Vindula Cosmos é uma plataforma completa de colaboração empresarial que conecta pessoas, organiza conhecimento e potencializa produtividade.'
    };
    return fallbackDescriptions[currentPage] || fallbackDescriptions['Geral'];
  };

  const getPageFeatures = (): string[] => {
    if (pageHelpData?.help_features && pageHelpData.help_features.length > 0) {
      return pageHelpData.help_features
        .filter(f => f.is_active)
        .sort((a, b) => a.display_order - b.display_order)
        .map(f => f.feature_text);
    }
    
    const fallbackFeatures: Record<string, string[]> = {
      'Feed': [
        'Publicar posts com texto, imagens e vídeos',
        'Mencionar colegas com @ para notificações',
        'Usar hashtags (#) para categorizar conteúdo',
        'Curtir, comentar e compartilhar publicações',
        'Agendar posts para publicação futura'
      ],
      'Knowledge Hub': [
        'Criar artigos e documentos colaborativos',
        'Organizar conteúdo em espaços temáticos',
        'Usar tags para facilitar buscas',
        'Colaborar em tempo real com outros usuários',
        'Controlar versões e histórico de alterações'
      ],
      'Chat': [
        'Conversas diretas entre usuários',
        'Canais temáticos para equipes',
        'Compartilhar arquivos e mídia',
        'Threads para organizar discussões',
        'Configurar notificações personalizadas'
      ],
      'Tasks': [
        'Criar tarefas com descrição e prazo',
        'Definir prioridades (baixa, média, alta)',
        'Delegar tarefas para outros usuários',
        'Acompanhar progresso e status',
        'Adicionar comentários e atualizações'
      ],
      'Biblioteca': [
        'Upload e organização de arquivos',
        'Estrutura de pastas hierárquica',
        'Controle de permissões por arquivo',
        'Versionamento automático',
        'Busca avançada por conteúdo'
      ],
      'Equipe': [
        'Visualizar perfis completos dos colegas',
        'Organograma e estrutura hierárquica',
        'Diretório de departamentos',
        'Informações de contato e função',
        'Acompanhar atividades da equipe'
      ],
      'Administração': [
        'Gerenciar usuários e permissões',
        'Configurar integrações e APIs',
        'Relatórios e métricas de uso',
        'Backup e configurações de segurança',
        'Customização visual e branding'
      ],
      'Geral': [
        'Interface intuitiva e responsiva',
        'Sistema de notificações inteligentes',
        'Busca global unificada (Ctrl+K)',
        'Gamificação com pontos e medalhas',
        'Atalhos de teclado para produtividade'
      ]
    };
    return fallbackFeatures[currentPage] || fallbackFeatures['Geral'];
  };

  const getPageTip = (): string => {
    // Priorizar dicas de teclado da base de dados
    const keyboardTips = pageHelpData?.help_tips?.filter(tip => 
      tip.is_active && tip.tip_type === 'keyboard_shortcut'
    );
    
    if (keyboardTips && keyboardTips.length > 0) {
      return keyboardTips[0].tip_text;
    }
    
    const fallbackTips: Record<string, string> = {
      'Feed': 'Use Alt+P para criar uma nova publicação rapidamente de qualquer lugar da plataforma!',
      'Knowledge Hub': 'Pressione N para criar um novo artigo ou Ctrl+K para buscar conteúdo específico.',
      'Chat': 'Use / para buscar conversas rapidamente ou N para iniciar uma nova conversa.',
      'Tasks': 'Pressione N para criar nova tarefa ou X para marcar como concluída.',
      'Biblioteca': 'Use U para fazer upload de arquivos ou S para buscar documentos específicos.',
      'Equipe': 'Explore os perfis dos colegas para conhecer melhor sua equipe e facilitar colaboração.',
      'Administração': 'Acesse relatórios detalhados e configure integrações para automatizar processos.',
      'Geral': 'Pressione F1 em qualquer página para ver ajuda contextual específica daquele local!'
    };
    return fallbackTips[currentPage] || fallbackTips['Geral'];
  };

  // Função para obter dicas contextuais específicas da página
  const getContextualTips = (): string[] => {
    const bestPracticeTips = pageHelpData?.help_tips?.filter(tip => 
      tip.is_active && tip.tip_type === 'best_practice'
    ).map(tip => tip.tip_text);
    
    if (bestPracticeTips && bestPracticeTips.length > 0) {
      return bestPracticeTips;
    }
    
    const fallbackTips: Record<string, string[]> = {
      'Knowledge Hub': [
        'Use as tags para organizar e encontrar conteúdos relacionados mais facilmente',
        'Crie templates para padronizar documentos recorrentes da sua equipe',
        'Utilize a busca avançada com filtros para encontrar informações específicas',
        'Colabore em tempo real - várias pessoas podem editar simultaneamente'
      ],
      'Feed': [
        'Mencione colegas com @ para chamar atenção e gerar notificações',
        'Use hashtags (#) para categorizar seus posts e facilitar descobertas',
        'Publique conteúdo visual - imagens e vídeos geram mais engajamento',
        'Agende publicações para horários de maior movimento da equipe'
      ],
      'Chat': [
        'Crie canais temáticos para organizar discussões por projeto ou área',
        'Use threads para manter conversas organizadas em tópicos específicos',
        'Fixe mensagens importantes no topo dos canais para fácil acesso',
        'Compartilhe arquivos diretamente nas conversas para colaboração eficiente'
      ],
      'Equipe': [
        'Configure perfis completos para facilitar networking interno',
        'Use a estrutura hierárquica para organizar departamentos',
        'Acompanhe atividades da equipe através dos relatórios',
        'Configure permissões específicas por função e departamento'
      ],
      'Administração': [
        'Configure backups automáticos para proteção de dados',
        'Use auditoria para acompanhar atividades críticas',
        'Configure integrações para automatizar fluxos de trabalho',
        'Mantenha políticas de segurança atualizadas'
      ],
      'Geral': [
        'Use F1 para abrir esta Central de Ajuda em qualquer página',
        'Explore a gamificação - complete ações para ganhar pontos e medalhas',
        'Personalize suas notificações nas configurações do perfil',
        'Configure seu avatar e informações para melhor identificação'
      ]
    };
    
    return fallbackTips[currentPage] || fallbackTips['Geral'];
  };

  // Usar dados do banco ou fallback
  const sections = helpGuides && helpGuides.length > 0 ? helpGuides.map(guide => ({
    id: guide.category_key,
    title: guide.title,
    icon: fallbackHelpSections.find(f => f.id === guide.category_key)?.icon || BookOpen,
    description: guide.description,
    topics: guide.topics || []
  })) : fallbackHelpSections;

  const resources = helpResources && helpResources.length > 0 ? helpResources : fallbackResources;

  // Filtrar seções por busca
  const filteredSections = sections.filter(section =>
    section.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    section.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    section.topics.some((topic: string) => topic.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Função para obter ícone baseado no nome
  const getIconByName = (iconName?: string) => {
    const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
      'Video': Video,
      'FileText': FileText,
      'MessageSquare': MessageSquare,
      'HelpCircle': HelpCircle,
      'BookOpen': BookOpen,
      'Users': Users,
      'Settings': Settings,
      'Shield': Shield,
      'Database': Database
    };
    
    const IconComponent = iconName ? iconMap[iconName] : null;
    return IconComponent || HelpCircle;
  };

  // Renderizar página específica de uma seção
  const renderSectionPage = () => {
    // Buscar dados reais do banco primeiro
    const realGuide = helpGuides?.find(guide => guide.category_key === selectedSection);
    
    // Fallback para dados estáticos se não houver no banco
    const fallbackSection = fallbackHelpSections.find(s => s.id === selectedSection);
    
    if (!realGuide && !fallbackSection) return null;

    // Priorizar dados do banco se disponíveis
    const section = {
      id: selectedSection!,
      title: realGuide?.title || fallbackSection?.title || 'Seção não encontrada',
      icon: fallbackSection?.icon || BookOpen,
      description: realGuide?.description || fallbackSection?.description || 'Descrição não disponível',
      topics: realGuide?.topics && realGuide.topics.length > 0 ? realGuide.topics : (fallbackSection?.topics || []),
      content: realGuide?.content // HTML/Markdown do banco
    };

    const IconComponent = section.icon;

    return (
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="h-[70vh] overflow-y-auto custom-scrollbar"
      >
        <div className="p-6 space-y-6">
          {/* Header com botão voltar */}
          <div className="flex items-center gap-3 mb-6">
            <button
              onClick={handleBackFromSection}
              className="p-2 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors"
            >
              <ArrowLeft className="h-4 w-4 text-slate-300" />
            </button>
            <div>
              <h2 className="text-lg font-bold text-white flex items-center gap-2">
                <IconComponent className="h-5 w-5 text-blue-400" />
                {section.title}
              </h2>
              <p className="text-slate-400 text-sm">{section.description}</p>
            </div>
          </div>

          {/* Conteúdo principal da seção */}
          <motion.div variants={itemVariants} className="space-y-4">
            {/* Se há conteúdo HTML/Markdown do banco, mostrar primeiro */}
            {section.content && (
              <div className="space-y-3">
                <h3 className="font-bold text-white">📖 Conteúdo Detalhado</h3>
                <div 
                  className="prose prose-invert prose-sm max-w-none bg-slate-800/30 rounded-lg p-4 border border-slate-600/30"
                  dangerouslySetInnerHTML={{ __html: section.content }}
                />
              </div>
            )}
            
            {/* Lista de tópicos/guias */}
            {section.topics.length > 0 && (
              <div className="space-y-3">
                <h3 className="font-bold text-white">📚 Guias e Tutoriais</h3>
                <div className="grid gap-3">
                  {section.topics.map((topic, index) => (
                    <motion.button
                      key={index}
                      variants={itemVariants}
                      className="flex items-start gap-3 p-4 rounded-lg bg-gradient-to-r from-slate-800/50 to-slate-700/50 border border-slate-600/30 hover:from-slate-700/60 hover:to-slate-600/60 transition-all duration-200 text-left group"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => {
                        logQueryEvent('HotkeyHelpModal', `Guia acessado: ${topic}`, { section: section.id, topic, hasRealData: !!realGuide });
                        // Aqui pode adicionar navegação para página específica do guia
                        // Por exemplo: window.open(`/help/guides/${section.id}/${index}`, '_blank');
                      }}
                    >
                      <div className="bg-gradient-to-r from-blue-500 to-purple-500 p-2 rounded-lg shadow-lg group-hover:from-blue-400 group-hover:to-purple-400 transition-colors">
                        <BookOpen className="h-4 w-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-white text-sm mb-1 group-hover:text-blue-300 transition-colors">
                          {topic}
                        </h4>
                        <p className="text-slate-400 text-xs group-hover:text-slate-300 transition-colors">
                          {realGuide ? 'Conteúdo personalizado pela Vindula' : 'Clique para acessar o guia completo com exemplos práticos'}
                        </p>
                      </div>
                      <ChevronRight className="h-4 w-4 text-slate-400 group-hover:text-white transition-colors" />
                    </motion.button>
                  ))}
                </div>
              </div>
            )}
          </motion.div>

          {/* Recursos relacionados */}
          <motion.div variants={itemVariants} className="space-y-3">
            <h3 className="font-bold text-white">🎯 Recursos Relacionados</h3>
            <div className="grid gap-3">
              <motion.button
                className="flex items-center gap-3 p-4 rounded-lg bg-gradient-to-r from-blue-900/30 to-cyan-900/30 border border-blue-600/20 hover:from-blue-800/40 hover:to-cyan-800/40 transition-all duration-200 text-left group"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  logQueryEvent('HotkeyHelpModal', `Vídeos Tutoriais acessados - Seção: ${section.id}`);
                  // Aqui pode adicionar navegação para vídeos específicos
                }}
              >
                <Video className="h-5 w-5 text-blue-400 group-hover:text-blue-300 transition-colors" />
                <div className="flex-1">
                  <span className="text-white font-medium group-hover:text-blue-200 transition-colors">Vídeos Tutoriais</span>
                  <p className="text-blue-200 text-sm group-hover:text-blue-100 transition-colors">Aprenda visualmente com exemplos práticos</p>
                </div>
                <ExternalLink className="h-4 w-4 text-blue-400 group-hover:text-blue-300 transition-colors" />
              </motion.button>

              <motion.button
                className="flex items-center gap-3 p-4 rounded-lg bg-gradient-to-r from-green-900/30 to-emerald-900/30 border border-green-600/20 hover:from-green-800/40 hover:to-emerald-800/40 transition-all duration-200 text-left group"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  logQueryEvent('HotkeyHelpModal', `Documentação acessada - Seção: ${section.id}`);
                  // Aqui pode adicionar navegação para documentação específica
                }}
              >
                <FileText className="h-5 w-5 text-green-400 group-hover:text-green-300 transition-colors" />
                <div className="flex-1">
                  <span className="text-white font-medium group-hover:text-green-200 transition-colors">Documentação</span>
                  <p className="text-green-200 text-sm group-hover:text-green-100 transition-colors">Guias detalhados e referências</p>
                </div>
                <ExternalLink className="h-4 w-4 text-green-400 group-hover:text-green-300 transition-colors" />
              </motion.button>

              <motion.button
                className="flex items-center gap-3 p-4 rounded-lg bg-gradient-to-r from-purple-900/30 to-pink-900/30 border border-purple-600/20 hover:from-purple-800/40 hover:to-pink-800/40 transition-all duration-200 text-left group"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  logQueryEvent('HotkeyHelpModal', `Suporte Especializado acessado - Seção: ${section.id}`);
                  setShowContactPage(true); // Navega para página de contato
                }}
              >
                <MessageSquare className="h-5 w-5 text-purple-400 group-hover:text-purple-300 transition-colors" />
                <div className="flex-1">
                  <span className="text-white font-medium group-hover:text-purple-200 transition-colors">Suporte Especializado</span>
                  <p className="text-purple-200 text-sm group-hover:text-purple-100 transition-colors">Tire dúvidas com nossa equipe</p>
                </div>
                <ExternalLink className="h-4 w-4 text-purple-400 group-hover:text-purple-300 transition-colors" />
              </motion.button>
            </div>
          </motion.div>

          {/* Dicas específicas da seção */}
          <motion.div variants={itemVariants} className="space-y-3">
            <h3 className="font-bold text-white">💡 Dicas Avançadas</h3>
            <div className="bg-slate-800/30 rounded-lg p-4 border border-slate-600/30">
              <div className="space-y-3">
                {realGuide ? (
                  <div className="flex items-start gap-3">
                    <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></div>
                    <p className="text-slate-300 text-sm">
                      ✨ <strong>Conteúdo personalizado:</strong> Este guia foi criado especificamente pela equipe Vindula para sua empresa
                    </p>
                  </div>
                ) : null}
                <div className="flex items-start gap-3">
                  <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2"></div>
                  <p className="text-slate-300 text-sm">
                    Explore os atalhos de teclado específicos desta área para aumentar sua produtividade
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2"></div>
                  <p className="text-slate-300 text-sm">
                    Use F1 em qualquer tela desta área para obter ajuda contextual específica
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2"></div>
                  <p className="text-slate-300 text-sm">
                    Personalize as configurações desta área de acordo com suas preferências
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>
    );
  };

  // Renderizar página de contato de emergência
  const renderContactPage = () => (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="h-[70vh] overflow-y-auto custom-scrollbar"
    >
      <div className="p-6 space-y-6">
        {/* Header com botão voltar */}
        <div className="flex items-center gap-3 mb-6">
          <button
            onClick={handleBackFromContact}
            className="p-2 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 text-slate-300" />
          </button>
          <div>
            <h2 className="text-lg font-bold text-white flex items-center gap-2">
              <HelpCircle className="h-5 w-5 text-red-400" />
              Contato de Emergência
            </h2>
            <p className="text-slate-400 text-sm">Para situações críticas e urgentes</p>
          </div>
        </div>

        {/* Alerta importante */}
        <motion.div variants={itemVariants} className="bg-gradient-to-r from-red-900/30 to-orange-900/30 rounded-xl p-4 border border-red-600/20">
          <div className="flex items-start gap-3">
            <div className="bg-gradient-to-r from-red-500 to-orange-500 p-2 rounded-lg shadow-lg">
              <Phone className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="font-bold text-red-300 mb-2">🚨 Situação Crítica?</h3>
              <p className="text-red-200 text-sm">
                Use os canais abaixo se você está enfrentando problemas que impedem o funcionamento normal da plataforma ou situações que afetam múltiplos usuários.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Opções de contato */}
        <motion.div variants={itemVariants} className="space-y-4">
          <h3 className="font-bold text-white">📞 Canais de Suporte Prioritário</h3>
          
          <div className="grid gap-3">
            {/* WhatsApp Comercial */}
            <motion.a
              href="https://api.whatsapp.com/send?phone=5511987489487&text=🆘%20URGENTE%20-%20Preciso%20de%20suporte%20técnico%20no%20Vindula%20Cosmos"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-3 p-4 rounded-lg bg-gradient-to-r from-green-900/30 to-emerald-900/30 border border-green-600/20 hover:from-green-800/40 hover:to-emerald-800/40 transition-all"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <MessageCircle className="h-5 w-5 text-green-400" />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="text-white font-medium">WhatsApp Comercial</span>
                  <Badge className="bg-green-500/20 text-green-300 border-green-500/30 text-xs">
                    Mais Rápido
                  </Badge>
                </div>
                <p className="text-green-200 text-sm">(11) 98748-9487 - Resposta em até 30 minutos</p>
              </div>
              <ExternalLink className="h-4 w-4 text-green-400" />
            </motion.a>

            {/* Email de Emergência */}
            <motion.a
              href="mailto:<EMAIL>?subject=🆘%20URGENTE%20-%20Suporte%20Técnico%20Vindula%20Cosmos&body=Descreva%20sua%20situação%20de%20emergência:%0A%0A1.%20Problema%20encontrado:%0A2.%20Quando%20começou:%0A3.%20Usuários%20afetados:%0A4.%20Mensagens%20de%20erro:%0A%0AEmpresa:%0AContato:"
              className="flex items-center gap-3 p-4 rounded-lg bg-gradient-to-r from-blue-900/30 to-cyan-900/30 border border-blue-600/20 hover:from-blue-800/40 hover:to-cyan-800/40 transition-all"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Mail className="h-5 w-5 text-blue-400" />
              <div className="flex-1">
                <span className="text-white font-medium">Email de Emergência</span>
                <p className="text-blue-200 text-sm"><EMAIL> - Resposta em até 2 horas</p>
              </div>
              <ExternalLink className="h-4 w-4 text-blue-400" />
            </motion.a>

            {/* Telefone Direto */}
            <motion.a
              href="tel:+551126268537"
              className="flex items-center gap-3 p-4 rounded-lg bg-gradient-to-r from-purple-900/30 to-pink-900/30 border border-purple-600/20 hover:from-purple-800/40 hover:to-pink-800/40 transition-all"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Phone className="h-5 w-5 text-purple-400" />
              <div className="flex-1">
                <span className="text-white font-medium">Linha Direta de Suporte</span>
                <p className="text-purple-200 text-sm">(11) 2626-8537 - Segunda a Sexta, 8h às 18h</p>
              </div>
            </motion.a>
          </div>
        </motion.div>

        {/* Informações que ajudam no suporte */}
        <motion.div variants={itemVariants} className="space-y-3">
          <h3 className="font-bold text-white">📋 Para Agilizar o Atendimento</h3>
          <div className="bg-slate-800/30 rounded-lg p-4 border border-slate-600/30">
            <p className="text-slate-300 text-sm mb-3">Tenha essas informações em mãos:</p>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center gap-2 text-slate-300">
                <div className="w-1 h-1 bg-blue-400 rounded-full"></div>
                <span>Nome da empresa e usuário afetado</span>
              </li>
              <li className="flex items-center gap-2 text-slate-300">
                <div className="w-1 h-1 bg-blue-400 rounded-full"></div>
                <span>Descrição detalhada do problema</span>
              </li>
              <li className="flex items-center gap-2 text-slate-300">
                <div className="w-1 h-1 bg-blue-400 rounded-full"></div>
                <span>Quando o problema começou</span>
              </li>
              <li className="flex items-center gap-2 text-slate-300">
                <div className="w-1 h-1 bg-blue-400 rounded-full"></div>
                <span>Mensagens de erro (se houver)</span>
              </li>
              <li className="flex items-center gap-2 text-slate-300">
                <div className="w-1 h-1 bg-blue-400 rounded-full"></div>
                <span>Número de usuários afetados</span>
              </li>
            </ul>
          </div>
        </motion.div>

        {/* SLA de atendimento */}
        <motion.div variants={itemVariants} className="space-y-3">
          <h3 className="font-bold text-white">⏱️ Tempos de Resposta</h3>
          <div className="grid gap-2">
            <div className="flex items-center justify-between py-2 px-3 rounded-lg bg-red-900/20 border border-red-600/20">
              <span className="text-red-300 text-sm font-medium">Emergência Crítica</span>
              <span className="text-red-200 text-sm">15-30 minutos</span>
            </div>
            <div className="flex items-center justify-between py-2 px-3 rounded-lg bg-orange-900/20 border border-orange-600/20">
              <span className="text-orange-300 text-sm font-medium">Problema Urgente</span>
              <span className="text-orange-200 text-sm">1-2 horas</span>
            </div>
            <div className="flex items-center justify-between py-2 px-3 rounded-lg bg-blue-900/20 border border-blue-600/20">
              <span className="text-blue-300 text-sm font-medium">Suporte Geral</span>
              <span className="text-blue-200 text-sm">4-8 horas</span>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );

  return (
    <Dialog 
      open={isOpen} 
      onOpenChange={handleClose}
    >
      <DialogContent 
        className="max-w-7xl max-h-[90vh] overflow-hidden bg-slate-900 border-slate-700 p-0"
      >
        <DialogHeader className="p-6 pb-4">
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-3 text-2xl text-white">
              <motion.div
                animate={{ rotate: [0, 5, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <HelpCircle className="h-7 w-7 text-blue-400" />
              </motion.div>
              Central de Ajuda
              {contextualHotkeys.length > 0 && (
                <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                  <Sparkles className="h-3 w-3 mr-1" />
                  {currentPage}
                </Badge>
              )}
            </DialogTitle>
          </div>
          <DialogDescription className="text-slate-400 text-sm">
            Atalhos de teclado, tutoriais e guias para maximizar sua produtividade no Vindula Cosmos
          </DialogDescription>
        </DialogHeader>

        {showContactPage ? (
          renderContactPage()
        ) : selectedSection ? (
          renderSectionPage()
        ) : (
          <div className="flex h-[70vh] overflow-hidden">
            {/* PAINEL ESQUERDO - ATALHOS DE TECLADO */}
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="w-1/2 border-r border-slate-700 overflow-y-auto custom-scrollbar"
            >
            <div className="p-6 space-y-6">
              <div className="flex items-center gap-2 mb-4">
                <Keyboard className="h-5 w-5 text-blue-400" />
                <h2 className="text-lg font-bold text-white">Atalhos de Teclado</h2>
              </div>

              {/* ATALHOS CONTEXTUAIS PRIMEIRO */}
              {contextualHotkeys.length > 0 && (
                <motion.div variants={itemVariants} className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="bg-gradient-to-r from-blue-500 to-purple-500 p-2 rounded-lg shadow-lg">
                      <Zap className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="font-bold text-blue-300">
                        Para {currentPage}
                      </h3>
                      <p className="text-xs text-blue-200">Atalhos desta página</p>
                    </div>
                  </div>

                  <div className="grid gap-2">
                    {contextualHotkeys.map((hotkey, index) => (
                      <motion.div
                        key={`${hotkey.key}-${index}`}
                        variants={itemVariants}
                        className="flex items-center justify-between py-2 px-3 rounded-lg bg-gradient-to-r from-blue-900/40 to-purple-900/40 border border-blue-600/30"
                      >
                        <span className="text-white text-sm font-medium">
                          {hotkey.description}
                        </span>
                        <kbd className="px-2 py-1 text-xs font-bold rounded border-2 bg-gradient-to-b from-blue-800 to-blue-900 text-blue-100 border-blue-600">
                          {hotkey.key.toUpperCase()}
                        </kbd>
                      </motion.div>
                    ))}
                  </div>
                  
                  <Separator className="bg-slate-600" />
                </motion.div>
              )}

              {/* ATALHOS GLOBAIS */}
              {Object.entries(categorizedHotkeys).map(([category, hotkeys]) => (
                <motion.div key={category} variants={itemVariants} className="space-y-3">
                  <h3 className="font-semibold text-sm uppercase tracking-wide text-slate-300">
                    {category}
                  </h3>

                  <div className="grid gap-1">
                    {hotkeys.map((hotkey, index) => (
                      <motion.div
                        key={`${hotkey.key}-${index}`}
                        variants={itemVariants}
                        className="flex items-center justify-between py-2 px-3 rounded-lg bg-slate-800/50 hover:bg-slate-800/70 transition-colors"
                      >
                        <div className="flex items-center gap-2">
                          <span className="text-white text-sm">
                            {hotkey.description}
                          </span>
                          {hotkey.adminOnly && (
                            <Badge className="bg-red-500/20 text-red-300 border-red-500/30 text-xs">
                              Admin
                            </Badge>
                          )}
                        </div>
                        
                        <kbd className="px-2 py-1 text-xs font-semibold rounded border bg-slate-700 text-slate-200 border-slate-600">
                          {hotkey.key.replace('+', ' + ')}
                        </kbd>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* PAINEL DIREITO - CENTRAL DE AJUDA */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="w-1/2 overflow-y-auto custom-scrollbar"
          >
            <div className="p-6 space-y-6">
              <div className="flex items-center gap-2 mb-4">
                <HelpCircle className="h-5 w-5 text-emerald-400" />
                <h2 className="text-lg font-bold text-white">Tutoriais & Guias</h2>
              </div>

              {/* EXPLICAÇÃO DA FUNCIONALIDADE ATUAL */}
              <motion.div variants={itemVariants} className="space-y-4">
                <div className="bg-gradient-to-r from-blue-900/30 to-purple-900/30 rounded-xl p-4 border border-blue-600/20">
                  <div className="flex items-start gap-3">
                    <div className="bg-gradient-to-r from-blue-500 to-purple-500 p-2 rounded-lg shadow-lg mt-1">
                      {getPageIcon(currentPage)}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-bold text-blue-300 mb-2">
                        {getPageEmoji(currentPage)} {getPageTitle()}
                      </h3>
                      <div className="space-y-2 text-sm text-slate-300">
                        <p className="text-slate-200">
                          {getPageDescription()}
                        </p>
                        
                        <div className="mt-3">
                          <h4 className="text-blue-300 font-semibold mb-2">🎯 Principais funcionalidades:</h4>
                          <ul className="space-y-1 ml-2">
                            {getPageFeatures().map((feature, index) => (
                              <li key={index} className="flex items-center gap-2">
                                <div className="w-1 h-1 bg-blue-400 rounded-full"></div>
                                <span>{feature}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        
                        <div className="mt-3 p-2 bg-slate-800/50 rounded-lg border border-slate-600/30">
                          <p className="text-xs text-slate-400">
                            💡 <strong>Dica:</strong> {getPageTip()}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>



              {/* BUSCA DE AJUDA */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  type="text"
                  placeholder="Buscar ajuda..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-slate-800 border-slate-600 text-white placeholder:text-slate-400"
                />
              </div>

              {/* DICAS CONTEXTUAIS */}
              {contextualHotkeys.length > 0 && (
                <motion.div variants={itemVariants} className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="bg-gradient-to-r from-emerald-500 to-teal-500 p-2 rounded-lg shadow-lg">
                      <Lightbulb className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="font-bold text-emerald-300">
                        Dicas para {currentPage}
                      </h3>
                      <p className="text-xs text-emerald-200">Melhores práticas</p>
                    </div>
                  </div>

                  <div className="grid gap-2">
                    {getContextualTips().map((tip, index) => (
                      <motion.div
                        key={index}
                        variants={itemVariants}
                        className="flex items-start gap-3 py-2 px-3 rounded-lg bg-gradient-to-r from-emerald-900/30 to-teal-900/30 border border-emerald-600/20"
                      >
                        <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full mt-2"></div>
                        <span className="text-white text-sm">
                          {tip}
                        </span>
                      </motion.div>
                    ))}
                  </div>
                  
                  <Separator className="bg-slate-600" />
                </motion.div>
              )}

              {/* SEÇÕES DE AJUDA */}
              <motion.div variants={itemVariants} className="space-y-3">
                <h3 className="font-bold text-white">Explorar Outras Áreas</h3>
                
                <div className="grid gap-2">
                  {filteredSections.map((section) => {
                    const IconComponent = section.icon;
                    const hasRealData = helpGuides?.some(guide => guide.category_key === section.id);
                    
                    return (
                      <motion.button
                        key={section.id}
                        onClick={() => setSelectedSection(section.id)}
                        className="flex items-center justify-between p-3 rounded-lg bg-slate-800/50 hover:bg-slate-800/70 transition-all duration-200 text-left group"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-lg bg-slate-700 group-hover:bg-slate-600 transition-colors">
                            <IconComponent className="h-4 w-4 text-slate-300" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium text-white text-sm">
                                {section.title}
                              </h4>
                              {hasRealData && (
                                <Badge className="bg-green-500/20 text-green-300 border-green-500/30 text-xs px-1 py-0">
                                  ✨ Personalizado
                                </Badge>
                              )}
                            </div>
                            <p className="text-xs text-slate-400">
                              {section.description}
                            </p>
                          </div>
                        </div>
                        <ChevronRight className="h-4 w-4 text-slate-400 group-hover:text-white transition-colors" />
                      </motion.button>
                    );
                  })}
                </div>
              </motion.div>

              {/* RECURSOS ADICIONAIS */}
              <motion.div variants={itemVariants} className="space-y-3">
                <h3 className="font-bold text-white">🎯 Recursos de Suporte</h3>
                
                <div className="grid gap-2">
                  {resources.map((resource) => {
                    const IconComponent = getIconByName(resource.icon_name);
                    const bgClasses = {
                      'video': 'bg-slate-800/30 border-slate-700 hover:bg-slate-800/50',
                      'article': 'bg-slate-800/30 border-slate-700 hover:bg-slate-800/50',
                      'live_support': 'bg-slate-800/30 border-slate-700 hover:bg-slate-800/50',
                      'emergency': 'bg-gradient-to-r from-orange-900/30 to-red-900/30 border-orange-600/20 hover:from-orange-800/40 hover:to-red-800/40'
                    };
                    
                    const iconColors = {
                      'video': 'text-blue-400',
                      'article': 'text-green-400',
                      'live_support': 'text-purple-400',
                      'emergency': 'text-orange-400'
                    };
                    
                    const emojis = {
                      'video': '📹',
                      'article': '📖',
                      'live_support': '💬',
                      'emergency': '🆘'
                    };
                    
                    return resource.resource_type === 'emergency' ? (
                      <motion.button
                        key={resource.id}
                        onClick={handleEmergencyContact}
                        className={cn(
                          "flex items-start gap-3 p-3 rounded-lg border transition-all cursor-pointer",
                          bgClasses[resource.resource_type]
                        )}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <IconComponent className={cn("h-4 w-4 mt-1", iconColors[resource.resource_type])} />
                        <div className="flex-1 text-left">
                          <span className="text-white text-sm font-medium">
                            {emojis[resource.resource_type]} {resource.title}
                          </span>
                          <p className="text-orange-200 text-xs mt-1">
                            {resource.description}
                          </p>
                        </div>
                        <ChevronRight className="h-4 w-4 text-orange-400 mt-1" />
                      </motion.button>
                    ) : (
                      <div key={resource.id} className={cn(
                        "flex items-start gap-3 p-3 rounded-lg border transition-all",
                        bgClasses[resource.resource_type]
                      )}>
                        <IconComponent className={cn("h-4 w-4 mt-1", iconColors[resource.resource_type])} />
                        <div className="flex-1">
                          <span className="text-white text-sm font-medium">
                            {emojis[resource.resource_type]} {resource.title}
                          </span>
                          <p className={cn(
                            "text-xs mt-1",
                            resource.resource_type === 'emergency' ? 'text-orange-200' : 'text-slate-400'
                          )}>
                            {resource.description}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </motion.div>
            </div>
          </motion.div>
          </div>
        )}

        <Separator className="bg-slate-700" />
        
        <div className="flex items-center justify-between text-xs text-slate-400 p-4">
          <div className="flex items-center gap-4">
            <span>💡 Pressione ESC para fechar</span>
            <span>🔍 Use a busca para encontrar tópicos específicos</span>
            {(pageLoading || resourcesLoading || guidesLoading) && (
              <span className="text-blue-400">🔄 Carregando conteúdo personalizado...</span>
            )}
            {helpGuides && helpGuides.length > 0 && (
              <span className="text-green-400">✨ {helpGuides.length} guias personalizados disponíveis</span>
            )}
          </div>
          <span>F1 para reabrir a qualquer momento</span>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Estilos para scrollbar customizada
const styles = `
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgb(71 85 105) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgb(71 85 105);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(100 116 139);
}
`;

// Adicionar estilos ao head
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
} 