/**
 * Editor de postagens do Cosmos.
 * <AUTHOR> Internet 2025
 */
import { useEditor, EditorContent } from "@tiptap/react";
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import StarterKit from "@tiptap/starter-kit";
import { SlashCommandsExtension } from "./extensions/SlashCommandsExtension";
import Image from "@tiptap/extension-image";
import Link from "@tiptap/extension-link";
import TextAlign from "@tiptap/extension-text-align";
import { CustomHeading } from "./extensions/CustomHeading";
import { useCallback, useRef, useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { PostImageService } from "@/services/post-images";
import { toast } from "sonner";
import { ContentSuggestionModal } from "./ContentSuggestionModal";
import { ImageGenerationModal } from "./ImageGenerationModal";
import { TextEnhancementModal } from "./TextEnhancementModal";
import { EditorBubbleMenu } from "./EditorBubbleMenu";
import { LinkModal } from "./LinkModal";
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Quote,
  Code,
  Heading1,
  Heading2,
  Image as ImageIcon,
  Undo,
  Redo,
  Sparkles,
  Link as LinkIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Strikethrough,
  Underline,
  UploadCloud,
  Upload,
  MessageSquareMore,
  Wand2,
  ImagePlus
} from "lucide-react";
import { Button } from "../ui/button";
import { Separator } from "../ui/separator";
import { cn } from "@/lib/utils";
import { EmojiPickerButton } from "@/components/feed/EmojiPickerButton";

interface PostEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  onImageUpload?: (file: File) => Promise<string>;
  tempPostId?: string; // ID temporário para associar imagens antes da criação do post
  generatedImageToInsert?: string | null; // Imagem gerada para inserir automaticamente
  onImageInserted?: () => void; // Callback quando a imagem for inserida
}

export function PostEditor({
  content,
  onChange,
  placeholder = "Compartilhe suas ideias com o cosmos...",
  onImageUpload,
  tempPostId,
  generatedImageToInsert,
  onImageInserted,
}: PostEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isContentModalOpen, setIsContentModalOpen] = useState(false);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [isTextEnhancementModalOpen, setIsTextEnhancementModalOpen] = useState(false);
  const [selectedTextForEnhancement, setSelectedTextForEnhancement] = useState("");
  const [fullTextForEnhancement, setFullTextForEnhancement] = useState("");
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);

  // Definir handleImageUpload antes de qualquer hook
  const handleImageUpload = async (file: File): Promise<string> => {
    logQueryEvent('PostEditor', 
      `[PostEditor] Iniciando upload de imagem. tempPostId: ${
        tempPostId || "não fornecido"
      }`
    );

    if (onImageUpload) {
      logQueryEvent('PostEditor', 
        `[PostEditor] Usando função onImageUpload fornecida pelo componente pai`
      );
      return onImageUpload(file);
    }

    // Fallback para upload usando PostImageService se não for fornecido um callback
    try {
      logQueryEvent('PostEditor', 
        `[PostEditor] Iniciando upload de imagem via PostImageService...`
      );

      // Usar o PostImageService para fazer upload da imagem
      const { url, path } = await PostImageService.uploadImage(file);

      if (!url) {
        throw new Error("URL da imagem não retornada após upload");
      }

      logQueryEvent('PostEditor', `[PostEditor] Imagem enviada com sucesso. URL: ${url}`);

      // Associar a imagem com um post temporário
      // Usar o ID temporário fornecido pelo componente pai ou gerar um novo UUID válido
      const postId = tempPostId || crypto.randomUUID();
      logQueryEvent('PostEditor', 
        `[PostEditor] Tentando associar imagem ao post com ID: ${postId}`
      );
      logQueryEvent('PostEditor', 
        `[PostEditor] Detalhes da imagem - URL: ${url.substring(
          0,
          50
        )}..., Path: ${path}`
      );

      try {
        // Obter company_id do usuário atual
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          throw new Error('Usuário não autenticado');
        }

        const { data: profile } = await supabase
          .from('profiles')
          .select('company_id')
          .eq('id', user.id)
          .single();

        if (!profile?.company_id) {
          throw new Error('Company ID não encontrado');
        }

        // Passar todos os parâmetros necessários incluindo company_id
        const imageId = await PostImageService.associateImageWithPost(
          postId,
          profile.company_id,
          url,
          path,
          file.size
        );
        logQueryEvent('PostEditor', 
          `[PostEditor] Imagem associada com sucesso! ID da imagem: ${imageId}`
        );
      } catch (associationError) {
        logQueryEvent('PostEditor', 
          `[PostEditor] ERRO ao associar imagem:`,
          associationError
        );
      }

      logQueryEvent('PostEditor', 
        `[PostEditor] Processo de associação de imagem concluído para post ID: ${postId}`
      );

      return url;
    } catch (error) {
      logQueryEvent('PostEditor', "Erro ao fazer upload da imagem:", error);
      toast.error("Erro ao fazer upload da imagem");
      return "";
    }
  };

  // Funções para lidar com conteúdo gerado pelos modais
  const handleContentGenerated = (generatedContent: string) => {
    if (editor) {
      // Substituir todo o conteúdo com o conteúdo gerado
      editor.commands.setContent(generatedContent);
      onChange(generatedContent);
    }
  };

  const handleImageGenerated = (imageUrl: string) => {
    if (editor && imageUrl) {
      // Inserir a imagem na posição atual do cursor
      editor.chain().focus().setImage({ src: imageUrl }).run();
    }
  };

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: false,
        bulletList: {
          keepAttributes: true,
          keepMarks: true,
        },
        orderedList: {
          keepAttributes: true,
          keepMarks: true,
        },
        blockquote: {
          HTMLAttributes: {
            class: "border-l-4 border-primary pl-4 italic my-4",
          },
        },
        codeBlock: {
          HTMLAttributes: {
            class: "bg-muted rounded-md p-4 my-4 text-sm font-mono overflow-x-auto whitespace-pre-wrap break-words max-w-full",
          },
        },
        strike: {},
        paragraph: {
          HTMLAttributes: {
            class: "mb-4 leading-relaxed break-words overflow-wrap-anywhere",
          },
        },
        hardBreak: {},
      }),
      CustomHeading,
      Image.configure({
        HTMLAttributes: {
          class: "rounded-lg max-w-full my-4",
        },
        allowBase64: true,
      }),

      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: "text-primary underline hover:text-primary/80",
        },
      }),
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
      SlashCommandsExtension,
    ],
    content: content,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    // Adicionar editorProps para interceptar paste e restaurar attributes
    editorProps: {
      // Restaurar os atributos que definem a aparência e altura mínima
      attributes: {
        class:
          "prose prose-slate dark:prose-invert max-w-none focus:outline-none min-h-[200px] px-4 py-4 break-words overflow-wrap-anywhere prose-p:mb-0",
      },
      handlePaste: (view, event, slice) => {
        const items = Array.from(event.clipboardData?.items || []);
        let imagePasted = false;

        items.forEach(item => {
          if (item.type.startsWith('image/')) {
            const file = item.getAsFile();
            if (file) {
              imagePasted = true;
              event.preventDefault(); // Impedir o Tiptap de colar a imagem padrão (base64 ou blob)
              toast.loading("Processando imagem colada...");
              handleImageUpload(file).then(imageUrl => {
                if (imageUrl) {
                  const { state } = view;
                  const { tr } = state;
                  const node = state.schema.nodes.image.create({ src: imageUrl });
                  const transaction = tr.replaceSelectionWith(node);
                  view.dispatch(transaction);
                  toast.dismiss();
                  toast.success("Imagem colada e enviada com sucesso!");
                } else {
                  toast.dismiss();
                  // A falha já é tratada em handleImageUpload
                }
              }).catch(error => {
                toast.dismiss();
                logQueryEvent('PostEditor', 'Erro no handlePaste ao processar imagem:', error, 'error');
                toast.error("Erro ao processar imagem colada.");
              });
            }
          }
        });

        // Se uma imagem foi colada e tratada, retorna true para indicar que lidamos com o evento.
        // Caso contrário, retorna false para deixar o Tiptap/navegador lidar com outros tipos de conteúdo.
        return imagePasted;
      },
      // Opcional: Adicionar handleDrop se o drag-and-drop também for problemático no Safari
      handleDrop: (view, event, slice, moved) => {
        if (moved) return false; // Ignorar se for um movimento interno do editor

        const files = Array.from(event.dataTransfer?.files || []);
        let imageDropped = false;

        files.forEach(file => {
          if (file.type.startsWith('image/')) {
            imageDropped = true;
            event.preventDefault();
            toast.loading("Processando imagem arrastada...");
            handleImageUpload(file).then(imageUrl => {
              if (imageUrl) {
                const coordinates = view.posAtCoords({ left: event.clientX, top: event.clientY });
                if (coordinates) {
                    const { state } = view;
                    const { tr } = state;
                    const node = state.schema.nodes.image.create({ src: imageUrl });
                    const transaction = tr.insert(coordinates.pos, node); // Inserir na posição do drop
                    view.dispatch(transaction);
                    toast.dismiss();
                    toast.success("Imagem arrastada e enviada com sucesso!");
                }
              } else {
                  toast.dismiss();
              }
            }).catch(error => {
                toast.dismiss();
                logQueryEvent('PostEditor', 'Erro no handleDrop ao processar imagem:', error, 'error');
                toast.error("Erro ao processar imagem arrastada.");
            });
          }
        });

        return imageDropped; // Retorna true se uma imagem foi tratada
      }
    },
  });

  // Implementação de drag & drop removida - usando apenas a implementação nativa do TipTap nos editorProps

  // Sincronizar conteúdo externo com o editor
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      logQueryEvent('PostEditor', 'Sincronizando conteúdo externo com editor', {
        contentLength: content.length,
        editorContentLength: editor.getHTML().length
      });
      editor.commands.setContent(content);
    }
  }, [editor, content]);

  // Inserir imagem gerada quando o editor estiver pronto
  useEffect(() => {
    if (editor && generatedImageToInsert) {
      // Aguardar um pouco para garantir que o editor está totalmente carregado
      const timer = setTimeout(() => {
        editor.chain().focus().setImage({ src: generatedImageToInsert }).run();
        onImageInserted?.();
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [editor, generatedImageToInsert, onImageInserted]);

  if (!editor) {
    return null;
  }

  const handleTextEnhancement = useCallback(
    (selectedText: string, fullText: string) => {
      setSelectedTextForEnhancement(selectedText);
      setFullTextForEnhancement(fullText);
      setIsTextEnhancementModalOpen(true);
    },
    []
  );

  const handleTextEnhanced = useCallback(
    (enhancedText: string) => {
      if (editor && selectedTextForEnhancement) {
        // Encontrar e substituir o texto selecionado pelo texto melhorado
        const { from, to } = editor.state.selection;
        editor.chain().focus().deleteRange({ from, to }).insertContent(enhancedText).run();
      }
    },
    [editor, selectedTextForEnhancement]
  );

  const addLink = () => {
    setIsLinkModalOpen(true);
  };

  const handleInsertLink = (url: string, text?: string) => {
    if (text) {
      // Se foi fornecido um texto personalizado, inserir como link com texto
      editor.chain().focus().insertContent(`<a href="${url}">${text}</a>`).run();
    } else {
      // Se não foi fornecido texto, usar o comportamento padrão do TipTap
      editor.chain().focus().setLink({ href: url }).run();
    }
  };

  // Função para inserir emoji no editor
  const insertEmoji = (emoji: string) => {
    if (editor) {
      editor.chain().focus().insertContent(emoji).run();
    }
  };

  return (
    <div className="relative w-full rounded-lg border bg-background">
      <div className="sticky top-0 z-10 flex flex-wrap items-center gap-1 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/75 p-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={cn(editor.isActive("bold") && "bg-muted")}
          title="Negrito (Ctrl+B)"
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={cn(editor.isActive("italic") && "bg-muted")}
          title="Itálico (Ctrl+I)"
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={cn(editor.isActive("strike") && "bg-muted")}
          title="Tachado"
        >
          <Strikethrough className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={addLink}
          className={cn(editor.isActive("link") && "bg-muted")}
          title="Adicionar link"
        >
          <LinkIcon className="h-4 w-4" />
        </Button>
        <Separator orientation="vertical" className="mx-1 h-6" />
        <Button
          variant="ghost"
          size="sm"
          onClick={() =>
            editor.chain().focus().toggleHeading({ level: 1 }).run()
          }
          className={cn(editor.isActive("heading", { level: 1 }) && "bg-muted")}
          title="Título 1"
        >
          <Heading1 className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() =>
            editor.chain().focus().toggleHeading({ level: 2 }).run()
          }
          className={cn(editor.isActive("heading", { level: 2 }) && "bg-muted")}
          title="Título 2"
        >
          <Heading2 className="h-4 w-4" />
        </Button>
        <Separator orientation="vertical" className="mx-1 h-6" />
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign("left").run()}
          className={cn(editor.isActive({ textAlign: "left" }) && "bg-muted")}
          title="Alinhar à esquerda"
        >
          <AlignLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign("center").run()}
          className={cn(editor.isActive({ textAlign: "center" }) && "bg-muted")}
          title="Centralizar"
        >
          <AlignCenter className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign("right").run()}
          className={cn(editor.isActive({ textAlign: "right" }) && "bg-muted")}
          title="Alinhar à direita"
        >
          <AlignRight className="h-4 w-4" />
        </Button>
        <Separator orientation="vertical" className="mx-1 h-6" />
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={cn(editor.isActive("bulletList") && "bg-muted")}
          title="Lista com marcadores"
        >
          <List className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={cn(editor.isActive("orderedList") && "bg-muted")}
          title="Lista numerada"
        >
          <ListOrdered className="h-4 w-4" />
        </Button>
        <Separator orientation="vertical" className="mx-1 h-6" />
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
          className={cn(editor.isActive("blockquote") && "bg-muted")}
          title="Citação"
        >
          <Quote className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleCodeBlock().run()}
          className={cn(editor.isActive("codeBlock") && "bg-muted")}
          title="Bloco de código"
        >
          <Code className="h-4 w-4" />
        </Button>
        <Separator orientation="vertical" className="mx-1 h-6" />
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().undo().run()}
          disabled={!editor.can().undo()}
          title="Desfazer (Ctrl+Z)"
        >
          <Undo className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().redo().run()}
          disabled={!editor.can().redo()}
          title="Refazer (Ctrl+Y)"
        >
          <Redo className="h-4 w-4" />
        </Button>
        <div className="flex-1" />
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            const input = document.createElement("input");
            input.type = "file";
            input.accept = "image/*";
            input.onchange = async (event) => {
              const target = event.target as HTMLInputElement;
              const file = target.files?.[0];
              if (file) {
                toast.loading("Fazendo upload da imagem...");
                try {
                  const imageUrl = await handleImageUpload(file);
                  if (imageUrl) {
                    editor.chain().focus().setImage({ src: imageUrl }).run();
                    toast.dismiss();
                    toast.success("Imagem adicionada com sucesso");
                  }
                } catch (error) {
                  toast.dismiss();
                  toast.error("Erro ao fazer upload da imagem");
                  logQueryEvent('PostEditor', error);
                }
              }
            };
            input.click();
          }}
          title="Fazer upload de imagem"
        >
          <ImageIcon className="h-4 w-4" />
        </Button>
        <EmojiPickerButton 
          onEmojiSelect={insertEmoji}
          className="h-8 w-8 hover:bg-orange-50 hover:text-orange-600"
        />
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsContentModalOpen(true)}
          title="✨ Gerar Texto com IA - Crie conteúdo personalizado usando inteligência artificial"
          className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
        >
          <MessageSquareMore className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsImageModalOpen(true)}
          title="🎨 Gerar Imagem com IA - Crie imagens personalizadas usando inteligência artificial"
          className="text-purple-600 hover:text-purple-700 hover:bg-purple-50"
        >
          <ImagePlus className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            // Se há texto selecionado, usar a seleção
            const { from, to } = editor.state.selection;
            const selectedText = editor.state.doc.textBetween(from, to, ' ');
            
            if (selectedText.trim()) {
              handleTextEnhancement(selectedText, editor.getText());
            } else {
              // Se não há texto selecionado, usar todo o conteúdo
              const fullText = editor.getText();
              if (fullText.trim()) {
                handleTextEnhancement(fullText, fullText);
              } else {
                toast.error('Digite algum conteúdo primeiro para melhorar com IA');
              }
            }
          }}
          title="✨ Melhorar Texto com IA - Aplique melhorias inteligentes ao texto selecionado ou todo o conteúdo"
          className="text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50"
        >
          <Wand2 className="h-4 w-4" />
        </Button>
      </div>
      <div className="relative" ref={editorRef}>
        <div 
          className="relative"
          onContextMenu={(e) => {
            if (editor) {
              const { from, to } = editor.state.selection;
              const selectedText = editor.state.doc.textBetween(from, to, ' ');
              
              if (selectedText.trim()) {
                e.preventDefault();
                handleTextEnhancement(selectedText, editor.getText());
              }
            }
          }}
        >
          <EditorContent editor={editor} />
          {editor && <EditorBubbleMenu editor={editor} onTextEnhancement={handleTextEnhancement} />}
          {(editor.isEmpty || editor.getHTML() === "<p></p>") && (
            <div
              className="absolute inset-0 pointer-events-none px-4 py-4 text-muted-foreground"
              onClick={() => editor.chain().focus().run()}
            >
              {placeholder}
            </div>
          )}
        </div>
        <div className="absolute bottom-0 inset-x-0 p-2 bg-muted/60 rounded-b-lg text-center pointer-events-none flex items-center justify-center text-muted-foreground text-sm">
          <UploadCloud className="h-4 w-4 mr-2" />
          Arraste e solte imagens aqui ou use o botão na barra de ferramentas
        </div>
      </div>

      {/* Modais de IA */}
      <ContentSuggestionModal
        open={isContentModalOpen}
        onOpenChange={setIsContentModalOpen}
        onContentGenerated={handleContentGenerated}
        currentContent={content}
      />
      
      <ImageGenerationModal
        open={isImageModalOpen}
        onOpenChange={setIsImageModalOpen}
        onImageGenerated={handleImageGenerated}
      />
      
      <TextEnhancementModal
        open={isTextEnhancementModalOpen}
        onOpenChange={setIsTextEnhancementModalOpen}
        onTextEnhanced={handleTextEnhanced}
        selectedText={selectedTextForEnhancement}
        fullText={fullTextForEnhancement}
      />
      
      <LinkModal
        isOpen={isLinkModalOpen}
        onClose={() => setIsLinkModalOpen(false)}
        onInsertLink={handleInsertLink}
      />
    </div>
  );
}
