/**
 * Versão aprimorada do componente PostCard utilizando arquitetura de queries centralizada
 * <AUTHOR> Internet 2025
 */
import { HybridCard } from "@/components/ui/HybridCard";
import { usePlatform } from "@/hooks/usePlatform";
import { EnhancedPostActions } from "@/components/enhanced/feed/EnhancedPostActions";
import { cn } from "@/lib/utils";
import DOMPurify from "dompurify";
import { EnhancedOptimizedAvatar } from "@/components/common/EnhancedOptimizedAvatar";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  Globe,
  Building2,
  Users,
  UserSquare2,
  MoreVertical,
  Edit3,
  History,
  Trash2,
} from "lucide-react";
import { format, isAfter } from "date-fns";
import { ptBR } from "date-fns/locale";
import { PollVoting } from "@/components/poll/PollVoting";
import { PostPhotoGallery } from "@/components/feed/PostPhotoGallery";
import { PostImage } from "@/types/post.types";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { memo, useMemo, useEffect, useState } from "react";
import { useViewTracking } from "@/hooks/useViewTracking";
import { Link, useNavigate } from "react-router-dom";
import { EnhancedCommentList } from "./EnhancedCommentList";
import { AudioPlayer } from "@/components/ui/AudioPlayer";
import { VideoPlayer } from "@/components/ui/VideoPlayer";
import { useCanEditPost, useUpdatePost, usePostEditHistory, useDeletePost } from "@/lib/query/hooks/usePosts";
import { PostAudioTranscription } from "@/components/transcription/PostAudioTranscription";
import { EditPostDialog } from "@/components/feed/EditPostDialog";
import { PostEditHistoryDialog } from "@/components/feed/PostEditHistoryDialog";
import { 
  successWithNotification, 
  errorWithNotification 
} from "@/lib/notifications/toastWithNotification";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Interface para post processado
interface ProcessedPost {
  id: string;
  content: string;
  created_at: string;
  likes: number;
  company_id: string;
  status: string;
  scheduled_at?: string;
  has_poll?: boolean;
  type?: string; // Tipo do post (standard, medal_celebration, etc.)
  // Campos de edição
  is_edited?: boolean;
  edit_count?: number;
  last_edited_at?: string;
  last_edited_by?: string;
  metadata?: {
    audio_url?: string;
    audio_duration?: number;
    video_url?: string;
    video_duration?: number;
    video_poster?: string;
    medal_id?: string;
    medal_name?: string;
    medal_type?: string;
    [key: string]: any;
  };
  author: {
    id: string;
    full_name: string;
    avatar_url?: string;
  };
  liked_by: Array<{
    profiles: {
      id: string;
      full_name: string;
      avatar_url?: string;
    };
  }>;
  audience?: {
    type: 'all' | 'department' | 'team' | 'user';
    targets?: string[];
    targetCount?: number;
  };
  post_audience?: Array<{
    target_type: string;
    target_id: string;
  }>;
  images?: PostImage[];
  [key: string]: unknown;
}

interface EnhancedPostCardProps {
  post: ProcessedPost;
  currentUserId: string;
  formatDate: (date: string) => string;
  onEdit?: (postId: string) => void;
  onDelete?: (postId: string) => void;
  onViewHistory?: (postId: string) => void;
  highlightCommentId?: string | null;
}

function EnhancedPostCardComponent({
  post,
  currentUserId,
  formatDate,
  onEdit,
  onDelete,
  onViewHistory,
  highlightCommentId,
}: EnhancedPostCardProps) {
  const navigate = useNavigate();
  
  // Estado para controlar se o post já foi visualizado
  const [hasBeenViewed, setHasBeenViewed] = useState(false);
  
  // Estados para controlar dialogs de edição
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [historyDialogOpen, setHistoryDialogOpen] = useState(false);
  const [selectedPost, setSelectedPost] = useState<ProcessedPost | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  
  // Função de cleanup anti-freeze para dialogs
  const resetPointerEvents = () => {
    document.body.style.removeProperty('pointer-events');
    document.body.style.pointerEvents = '';
  };

  // Cleanup automático quando o componente é desmontado
  useEffect(() => {
    return () => {
      resetPointerEvents();
    };
  }, []);
  
  // Hook para verificar se usuário pode editar o post
  const { data: canEditData } = useCanEditPost(post.author.id === currentUserId ? post.id : undefined);
  
  // Hook para excluir post
  const deletePostMutation = useDeletePost();
  
  // Verificar se usuário é o autor do post
  const isAuthor = post.author.id === currentUserId;
  
  // Usar o hook de rastreamento de visualizações
  const postCardRef = useViewTracking({
    actionType: 'view_post',
    contentId: post.id,
    metadata: {
      author_id: post.author.id,
      author_name: post.author.full_name,
      has_poll: post.has_poll || false,
      likes_count: post.likes || 0,
      post_date: post.created_at,
      audience_type: post.audience?.type || 'all'
    },
    threshold: 0.2, // 20% do post deve estar visível (apenas o cabeçalho e parte do conteúdo)
    minVisibleTime: 1000, // 1 segundo de visualização mínima
    trackOnlyOnce: true, // Rastrear apenas uma vez
    onViewed: () => setHasBeenViewed(true) // Atualizar estado quando visualizado
  });
  // Processamento de audiência memoizado para evitar recálculos desnecessários
  const { audience, audienceType, audienceTargets, audienceTargetCount } =
    useMemo(() => {
      const postAudience = post.post_audience;
      let processedAudience = post.audience;

      if (
        !processedAudience &&
        postAudience &&
        Array.isArray(postAudience) &&
        postAudience.length > 0
      ) {
        const firstAudience = postAudience[0];
        const audienceType = firstAudience.target_type as
          | "all"
          | "department"
          | "team"
          | "user";

        if (audienceType === "all") {
          processedAudience = { type: "all" };
        } else {
          const targets = postAudience
            .map((a) => a.target_id)
            .filter((id): id is string => id !== null);

          processedAudience = {
            type: audienceType,
            targets,
            targetCount: targets.length,
          };
        }
      }

      return {
        audience: processedAudience,
        audienceType: processedAudience?.type,
        audienceTargets: processedAudience?.targets,
        audienceTargetCount: processedAudience?.targetCount,
      };
    }, [post.audience, post.post_audience]);

  // Sanitizar e renderizar o HTML de forma segura
  const sanitizedContent = useMemo(() => {
    return DOMPurify.sanitize(post.content);
  }, [post.content]);

  // Verificar se o post foi agendado e já foi publicado
  const isScheduledAndPublished =
    post.status === "scheduled" &&
    post.scheduled_at &&
    new Date(post.scheduled_at) <= new Date();

  // Renderizar o ícone de audiência baseado no tipo
  const renderAudienceIcon = () => {
    switch (audienceType) {
      case "all":
        return <Globe className="h-3 w-3" />;
      case "department":
        return <Building2 className="h-3 w-3" />;
      case "team":
        return <Users className="h-3 w-3" />;
      case "user":
        return <UserSquare2 className="h-3 w-3" />;
      default:
        return <Globe className="h-3 w-3" />;
    }
  };

  // Buscar nomes dos usuários quando a audiência for específica
  const { data: targetUsers } = useQuery({
    queryKey: ["target-users", post.id],
    queryFn: async () => {
      if (!audience || audience.type !== "user" || !audience.targets) {
        return [];
      }

      const { data } = await supabase
        .from("profiles")
        .select("id, full_name")
        .in("id", audience.targets)
        .order("full_name");

      return data || [];
    },
    enabled:
      !!audience && audience.type === "user" && !!audience.targets?.length,
  });

  // Obter texto da audiência
  const getAudienceText = () => {
    if (!audience || audienceType === "all") {
      return "Todos os funcionários";
    }

    const count = audienceTargetCount || audienceTargets?.length || 0;
    let userCount;

    switch (audienceType) {
      case "department":
        return `${count} departamento${count !== 1 ? "s" : ""}`;
      case "team":
        return `${count} equipe${count !== 1 ? "s" : ""}`;
      case "user":
        userCount = targetUsers?.length || count;
        return `${userCount} pessoa${userCount !== 1 ? "s" : ""}`;
      default:
        return "Todos os funcionários";
    }
  };

  // Obter texto detalhado da audiência para o tooltip
  const getAudienceTooltipText = () => {
    if (!audience || audienceType === "all") {
      return "Visível para todos os funcionários da empresa";
    }

    let targetNames = "";

    switch (audienceType) {
      case "department":
        return `Visível apenas para os departamentos: ${
          audienceTargets?.join(", ") || ""
        }`;
      case "team":
        return `Visível apenas para as equipes: ${
          audienceTargets?.join(", ") || ""
        }`;
      case "user":
        targetNames = targetUsers
          ? targetUsers.map((user) => user.full_name).join(", ")
          : audienceTargets?.join(", ") || "";
        return `Visível apenas para: ${targetNames}`;
      default:
        return "Visível para todos os funcionários da empresa";
    }
  };

  const { isNative } = usePlatform();

  // Handlers para edição
  const handleEdit = (postId: string) => {
    if (onEdit) {
      onEdit(postId);
    } else {
      // Navegar para a página de edição
      navigate(`/post/create?edit=${postId}`);
    }
  };

  const handleViewHistory = (postId: string) => {
    if (onViewHistory) {
      onViewHistory(postId);
    } else {
      // Fallback para dialog interno
      setSelectedPost(post);
      setHistoryDialogOpen(true);
    }
  };

  const handleDelete = async (postId: string) => {
    if (onDelete) {
      onDelete(postId);
    } else {
      // Fallback para confirmação com modal
      setIsDeleteDialogOpen(true);
    }
  };

  const confirmDelete = async () => {
    setIsDeleteDialogOpen(false);
    
    try {
      await deletePostMutation.mutateAsync(post.id);
      successWithNotification("Post excluído!", {
        description: "A publicação foi excluída com sucesso.",
      });
    } catch (error: any) {
      errorWithNotification("Erro ao excluir", {
        description: error.message || "Não foi possível excluir a publicação.",
      });
    }
  };

  const handleEditSuccess = () => {
    // Callback chamado após edição bem-sucedida
    setEditDialogOpen(false);
    setSelectedPost(null);
    // Cleanup anti-freeze com delay
    setTimeout(() => {
      resetPointerEvents();
    }, 100);
  };

  // Handlers de fechamento de dialogs com cleanup
  const closeEditDialog = () => {
    setEditDialogOpen(false);
    setSelectedPost(null);
    setTimeout(() => {
      resetPointerEvents();
    }, 100);
  };

  const closeHistoryDialog = () => {
    setHistoryDialogOpen(false);
    setSelectedPost(null);
    setTimeout(() => {
      resetPointerEvents();
    }, 100);
  };

  // Handlers de mudança de estado dos dialogs
  const handleEditDialogChange = (open: boolean) => {
    if (!open) {
      closeEditDialog();
    } else {
      setEditDialogOpen(open);
    }
  };

  const handleHistoryDialogChange = (open: boolean) => {
    if (!open) {
      closeHistoryDialog();
    } else {
      setHistoryDialogOpen(open);
    }
  };

  return (
    <div ref={postCardRef as React.RefObject<HTMLDivElement>}>
      <HybridCard 
        padding="none"
        shadow="md"
        className={cn(
          "overflow-hidden transition-all duration-500",
          // Desktop: manter estilo atual
          !isNative && "border-gray-200/60 dark:border-gray-700/50 rounded-2xl shadow-lg hover:shadow-xl bg-white dark:bg-gray-900 backdrop-blur-sm hover:border-orange-200/60 dark:hover:border-orange-700/30 hover:-translate-y-1",
          // Mobile: forçar fundo branco sempre
          isNative && "bg-white border-gray-200 rounded-[var(--native-radius-large)] shadow-[var(--native-shadow-medium)]",
          hasBeenViewed && "viewed-post" // Classe opcional para estilizar posts já visualizados
        )}>
        {/* Header do post */}
        <div className={cn(
          "px-6 pt-6 pb-5",
          isNative && "px-2 pt-2 pb-1" // Padding muito menor no mobile
        )}>
          <div className="flex items-start gap-4">
            <Link to={`/user/${post.author.id}`} className="shrink-0 relative group">
              <div className="absolute -inset-1.5 bg-gradient-to-br from-orange-500/20 to-orange-600/0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <EnhancedOptimizedAvatar
                src={post.author.avatar_url || undefined}
                fallback={post.author.full_name?.substring(0, 2) || "??"}
                alt={post.author.full_name || "Usuário"}
                className="h-11 w-11 border-2 border-white dark:border-gray-800 shadow-sm z-10 relative"
              />
            </Link>

            <div className="flex-1 min-w-0">
              <div className="flex flex-col">
                <div className="flex items-center justify-between">
                  <Link
                    to={`/user/${post.author.id}`}
                    className="font-semibold text-gray-900 dark:text-gray-100 hover:text-orange-600 dark:hover:text-orange-400 hover:underline transition-colors truncate text-base"
                  >
                    {post.author.full_name}
                  </Link>
                  
                  {/* Menu de opções do post - apenas para o autor */}
                  {isAuthor && (
                    <DropdownMenu modal={false}>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="h-8 w-8 p-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                        >
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        {/* Opção de editar - apenas se permitido */}
                        {canEditData?.can_edit && (
                          <DropdownMenuItem 
                            onClick={() => handleEdit(post.id)}
                            className="flex items-center gap-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          >
                            <Edit3 className="h-4 w-4" />
                            Editar publicação
                          </DropdownMenuItem>
                        )}
                        
                        {/* Mostrar restrição se não pode editar */}
                        {canEditData && !canEditData.can_edit && (
                          <DropdownMenuItem disabled className="flex items-center gap-2 text-gray-400">
                            <Edit3 className="h-4 w-4" />
                            <div className="flex flex-col">
                              <span>Editar publicação</span>
                              <span className="text-xs text-gray-500">{canEditData.message}</span>
                            </div>
                          </DropdownMenuItem>
                        )}
                        
                        {/* Opção de ver histórico - apenas se post foi editado */}
                        {post.is_edited && (
                          <DropdownMenuItem 
                            onClick={() => handleViewHistory(post.id)}
                            className="flex items-center gap-2"
                          >
                            <History className="h-4 w-4" />
                            Ver histórico de edições
                          </DropdownMenuItem>
                        )}
                        
                        {(canEditData?.can_edit || post.is_edited) && <DropdownMenuSeparator />}
                        
                        {/* Opção de excluir */}
                        <DropdownMenuItem 
                          onClick={() => handleDelete(post.id)}
                          className="flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                          Excluir publicação
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>

                <div className="flex flex-wrap items-center gap-1.5 text-xs text-gray-500 dark:text-gray-400 mt-1">
                  <span className="font-medium text-gray-600 dark:text-gray-300">
                    {formatDate(post.created_at)}
                  </span>
                  
                  {/* Badge para posts agendados */}
                  {post.status === "scheduled" && post.scheduled_at && (
                    <Badge
                      variant="outline"
                      className="ml-1 flex items-center gap-1 text-xs bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-400 border-amber-200/50 dark:border-amber-700/30"
                    >
                      <Calendar className="h-3 w-3" />
                      {isScheduledAndPublished ? (
                        <span>Publicado automaticamente</span>
                      ) : (
                        <span>
                          Agendado para{" "}
                          {format(
                            new Date(post.scheduled_at),
                            "d MMM 'às' HH:mm",
                            {
                              locale: ptBR,
                            }
                          )}
                        </span>
                      )}
                    </Badge>
                  )}
                  
                  {/* Badge de audiência com tooltip */}
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Badge
                          variant="outline"
                          className="flex items-center gap-1 text-xs bg-orange-100/80 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-800/50 px-2 py-0.5 rounded-full"
                        >
                          {renderAudienceIcon()}
                          <span>{getAudienceText()}</span>
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{getAudienceTooltipText()}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Conteúdo do post */}
        <div className={cn(
          "px-6 py-6 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm",
          isNative && "px-2 py-2 bg-white" // Forçar fundo branco no mobile
        )}>
          <div
            className={cn(
              // Desktop: styling completo com prose incluindo listas
              !isNative && "prose prose-gray dark:prose-invert max-w-none whitespace-pre-wrap prose-headings:font-heading prose-h1:text-3xl prose-h1:font-bold prose-h1:text-gray-900 dark:prose-h1:text-gray-100 prose-h1:mb-5 prose-h1:tracking-tight prose-h2:text-2xl prose-h2:font-semibold prose-h2:text-gray-800 dark:prose-h2:text-gray-200 prose-h2:mb-4 prose-h3:text-xl prose-h3:font-medium prose-h3:text-gray-700 dark:prose-h3:text-gray-300 prose-h3:mb-3 prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-p:leading-relaxed prose-a:text-orange-600 dark:prose-a:text-orange-400 prose-a:font-medium prose-ul:list-disc prose-ul:pl-6 prose-ul:my-4 prose-ol:list-decimal prose-ol:pl-6 prose-ol:my-4 prose-li:text-gray-700 dark:prose-li:text-gray-300 prose-li:mb-0 prose-li:mt-0 prose-li:leading-normal",
              // Mobile: forçar texto preto sempre com listas
              isNative && "whitespace-pre-wrap text-black leading-relaxed text-base [&_ul]:list-disc [&_ul]:pl-6 [&_ol]:list-decimal [&_ol]:pl-6 [&_li]:my-2"
            )}
            dangerouslySetInnerHTML={{ __html: sanitizedContent }}
          />
          
          {/* Indicador de edição */}
          {post.is_edited && post.last_edited_at && (
            <div className={cn(
              "mt-3 text-xs text-gray-500 dark:text-gray-400",
              isNative && "text-black"
            )}>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="cursor-pointer hover:text-gray-700 dark:hover:text-gray-300">
                      • Editado
                    </span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="flex flex-col gap-1">
                      <p>Última edição: {formatDate(post.last_edited_at)}</p>
                      {post.edit_count && post.edit_count > 1 && (
                        <p>{post.edit_count} edições realizadas</p>
                      )}
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}
          
        </div>

        {/* Player de áudio integrado ao conteúdo (se existir) */}
        {post.metadata?.audio_url && (
          <div className={cn(
            "px-6 py-3 border-l-4 border-orange-400/60 bg-gradient-to-r from-orange-50/30 to-amber-50/30 dark:from-orange-900/10 dark:to-amber-900/10 mx-6 mb-4 rounded-r-lg",
            isNative && "px-3 py-2 mx-2 mb-3"
          )}>
            <AudioPlayer 
              src={post.metadata.audio_url}
              duration={post.metadata.audio_duration || 0}
              className="w-full"
            />
            
            {/* Botão de transcrição do áudio */}
            <PostAudioTranscription 
              postId={post.id}
              audioUrl={post.metadata.audio_url}
              className="mt-3"
            />
          </div>
        )}

        {/* Player de vídeo integrado ao conteúdo (se existir) */}
        {post.metadata?.video_url && (
          <div className={cn(
            "px-6 py-4 mx-6 mb-4",
            isNative && "px-3 py-3 mx-2 mb-3"
          )}>
            <VideoPlayer 
              src={post.metadata.video_url}
              duration={post.metadata.video_duration || 0}
              poster={post.metadata.video_poster}
              className="w-full"
              aspectRatio="auto"
            />
          </div>
        )}

        {/* Galeria de fotos (se existir) */}
        {(() => {
          console.log("🖼️ Post images debug:", { postId: post.id, images: post.images, hasImages: post.images && post.images.length > 0 });
          return post.images && post.images.length > 0;
        })() && (
          <div className={cn(
            "px-6 py-4 mx-6 mb-4",
            isNative && "px-3 py-3 mx-2 mb-3"
          )}>
            <PostPhotoGallery images={post.images} />
          </div>
        )}

        {/* Enquete (se existir) */}
        {post.has_poll === true && (
          <div className={cn(
            "px-6 py-5 border-t border-gray-200/30 dark:border-gray-800/30 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-gray-900/80 dark:to-amber-900/20",
            isNative && "px-[var(--native-space-md)] py-[var(--native-space-sm)] border-t border-[var(--native-divider-color)] bg-[var(--native-bg-secondary)]"
          )}>
            <PollVoting
              postId={post.id}
              onVoteSuccess={() => {
                // Opcional: atualizar o feed após o voto
              }}
            />
          </div>
        )}

        {/* Ações do post (curtir, comentar, compartilhar) */}
        <div className={cn(
          "px-6 py-4 bg-gradient-to-r from-gray-50/80 to-gray-100/80 dark:from-gray-800/40 dark:to-gray-800/20 border-t border-gray-200/50 dark:border-gray-800/50",
          isNative && "px-2 py-1 bg-gray-50 border-t border-gray-200" // Forçar fundo claro no mobile
        )}>
          <EnhancedPostActions
            // @ts-expect-error - Diferenças de tipo entre ProcessedPost e Post
            post={post}
            currentUserId={currentUserId}
          />
        </div>

        {/* Seção de comentários */}
        <div className={cn(
          "bg-gray-50/80 dark:bg-gray-900/40 backdrop-blur-sm rounded-b-xl border-t border-gray-100 dark:border-gray-800/30",
          isNative && "bg-gray-50 rounded-b-[var(--native-radius-large)] border-t border-gray-200" // Forçar fundo claro
        )}>
          <EnhancedCommentList postId={post.id} currentUserId={currentUserId} highlightCommentId={highlightCommentId} />
        </div>
      </HybridCard>
      
      {/* Dialogs de edição (fallback se não houver handlers externos) */}
      {!onEdit && (
        <EditPostDialog
          // @ts-expect-error - Diferenças de tipo entre ProcessedPost e Post
          post={selectedPost}
          open={editDialogOpen}
          onOpenChange={handleEditDialogChange}
          onSuccess={handleEditSuccess}
        />
      )}
      
      {!onViewHistory && (
        <PostEditHistoryDialog
          postId={selectedPost?.id || null}
          open={historyDialogOpen}
          onOpenChange={handleHistoryDialogChange}
        />
      )}
      
      {/* Dialog de confirmação de exclusão (fallback se não houver handler externo) */}
      {!onDelete && (
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Excluir publicação</AlertDialogTitle>
              <AlertDialogDescription>
                Tem certeza que deseja excluir esta publicação? Esta ação não pode ser desfeita.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                disabled={deletePostMutation.isPending}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {deletePostMutation.isPending ? "Excluindo..." : "Sim, excluir"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
}

// Memoizar o componente para evitar renderizações desnecessárias
export const EnhancedPostCard = memo(EnhancedPostCardComponent); 