/**
 * Versão aprimorada do componente PostActions utilizando arquitetura de queries centralizada
 * <AUTHOR> Internet 2025
 */
import { Button } from "@/components/ui/button";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { SmartAvatar } from "@/components/ui/smart-avatar";
import { MessageSquare, ThumbsUp, Share2, Eye } from "lucide-react";
import { useEffect, useState, useCallback } from "react";
import { useFloatingHeart } from '@/contexts/FloatingHeartContext';
import { useToast } from "@/hooks/use-toast";
import { usePostChat } from "@/components/feed/post/usePostChat";
import { useTogglePostLike } from "@/lib/query/hooks/usePosts";
import { usePostViewCount } from "@/lib/query/hooks/usePostViewCount";
import { useSharePost } from "@/lib/query/hooks/usePostShares";
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { useAuthStore } from "@/stores/authStore";
import { useCurrentUser } from "@/lib/query/hooks/useUsers";
import { debounce } from "lodash";
import { useQueryClient } from "@tanstack/react-query";

interface Author {
  id: string;
  full_name: string | null;
  avatar_url: string | null;
}

interface ProcessedPost {
  id: string;
  likes: number;
  liked_by?: Array<{
    profiles: {
      id: string;
      full_name: string | null;
      avatar_url: string | null;
    };
  }> | null;
  content: string;
  author: Author;
}

interface EnhancedPostActionsProps {
  post: ProcessedPost;
  currentUserId: string;
}

export const EnhancedPostActions = ({ post, currentUserId }: EnhancedPostActionsProps) => {
  const { showHeart } = useFloatingHeart();
  const { toast } = useToast();
  const { handleStartChat, isLoading } = usePostChat();
  const toggleLikeMutation = useTogglePostLike();
  const sharePostMutation = useSharePost();
  const queryClient = useQueryClient();
  
  // Obter contagem de visualizações do post
  const { data: viewCount = 0, isLoading: isLoadingViewCount } = usePostViewCount(post.id);
  
  // Obter dados completos do usuário atual
  const authUser = useAuthStore(state => state.user);
  const { data: currentUserProfile } = useCurrentUser();
  const effectiveUserId = currentUserId || authUser?.id || '';

  // CORREÇÃO: Obter dados atualizados do cache para sincronizar com mudanças em tempo real
  const getCachedPostData = useCallback(() => {
    // 🔍 DEBUG: Listar TODAS as query keys no cache para descobrir onde estão os dados
    const cache = queryClient.getQueryCache();
    const allQueries = cache.getAll();
    const queryKeys = allQueries.map(q => q.queryKey);
    
    // 🔍 Filtrar query keys relevantes para timeline/posts
    const relevantQueries = queryKeys.filter(key => 
      key[0] === 'timeline' || 
      key[0] === 'posts' || 
      key[0] === 'notifications' ||
      key[0] === 'feed' ||
      key.some(k => typeof k === 'string' && (k.includes('timeline') || k.includes('post') || k.includes('feed')))
    );

    // Debug removido para reduzir poluição de logs

    // ✅ Verificar query keys possíveis baseadas na análise
    const timelineNotifications = queryClient.getQueryData(['timeline', 'notifications']) as any[];
    const timelineWithFilters = queryClient.getQueryData(['timeline', 'notifications', {}]) as any[];
    const feedPosts = queryClient.getQueryData(['posts', 'feed']) as any[];
    const feedWithFilters = queryClient.getQueryData(['posts', 'feed', {}]) as any[];
    const notificationsList = queryClient.getQueryData(['notifications', 'list']) as any[];
    
    // Debug de estrutura removido para reduzir poluição de logs
    
    // Extrair posts de diferentes estruturas de dados
    const extractPostsFromTimeline = (data: any[]) => {
      if (!data) return [];
      return data
        .filter(item => item?.type === 'post' && item?.data?.post)
        .map(item => item.data.post);
    };

    const extractPostsFromFeed = (data: any[]) => {
      if (!data) return [];
      return data.filter(item => item?.id && item?.content);
    };

    // Procurar o post em todas as queries
    const allPosts = [
      ...extractPostsFromTimeline(timelineNotifications),
      ...extractPostsFromTimeline(timelineWithFilters),
      ...extractPostsFromFeed(feedPosts),
      ...extractPostsFromFeed(feedWithFilters),
      ...extractPostsFromFeed(notificationsList)
    ];
    
    const cachedPost = allPosts.find(p => p?.id === post.id);
    
    // Debug de cache lookup removido para reduzir poluição de logs
    
    return cachedPost || post;
  }, [queryClient, post]);

  // State para forçar re-render quando cache muda
  const [, setForceUpdate] = useState({});
  const forceUpdate = useCallback(() => setForceUpdate({}), []);

  // Usar dados do cache quando disponíveis
  const currentPost = getCachedPostData();

  // CORREÇÃO: Escutar mudanças no cache para atualizar componente
  useEffect(() => {
    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event?.type === 'updated') {
        const query = event.query;
        const queryKey = query.queryKey;
        
        // ✅ QUERY KEYS CORRETAS para timeline
        const isRelevantQuery = (
          // Timeline queries
          (queryKey[0] === 'timeline' && queryKey[1] === 'notifications') ||
          // Feed queries  
          (queryKey[0] === 'posts' && queryKey[1] === 'feed') ||
          // Notification queries
          (queryKey[0] === 'notifications' && queryKey[1] === 'list')
        );
        
        if (isRelevantQuery) {
          forceUpdate();
        }
      }
    });

    return unsubscribe;
  }, [queryClient, post.id, forceUpdate]);

  const isLiked = currentPost.liked_by?.some(
    (like) => like.profiles?.id === effectiveUserId
  ) ?? false;

  const handleLikeClick = useCallback(async (position: { x: number; y: number } | null) => {
    // Verificar se os IDs são válidos
    if (!post.id || !effectiveUserId) {
      logQueryEvent('EnhancedPostActions', 'IDs inválidos ao curtir post', { postId: post.id, userId: effectiveUserId, propUserId: currentUserId, authUserId: authUser?.id }, 'error');
      toast({
        title: "Erro",
        description: "Não foi possível curtir o post. Dados inválidos.",
        variant: "destructive",
      });
      return;
    }

    // Capturar estado atual antes da atualização
    const currentLikeState = isLiked;
    
    // Log de like removido para reduzir poluição
    
    // Mostrar animação de coração apenas quando dar like (não quando remover)
    // Usar posição fornecida ou fallback para centro da tela se não disponível
    if (!currentLikeState) {
      const heartPosition = position || { x: window.innerWidth / 2, y: window.innerHeight / 2 };
      showHeart(heartPosition);
    }
    
    // Curtir via mutation do TanStack Query (sem estado local)
    try {
      await toggleLikeMutation.mutateAsync({
        postId: post.id,
        userId: effectiveUserId,
        isLiked: currentLikeState
      });
    } catch (error) {
      logQueryEvent('EnhancedPostActions', 'Erro ao curtir post', error, 'error');
      toast({
        title: "Erro",
        description: "Não foi possível curtir o post. Tente novamente.",
        variant: "destructive",
      });
    }
  }, [post.id, effectiveUserId, isLiked, currentUserId, authUser?.id, currentUserProfile, toggleLikeMutation, toast, showHeart]);

  // Debounce mínimo apenas para evitar double-clicks acidentais
  const debouncedLikeHandler = useCallback(
    debounce(async (position: { x: number; y: number } | null) => {
      await handleLikeClick(position);
    }, 50), // Reduzido para 50ms - quase instantâneo
    [handleLikeClick]
  );

  const handleButtonClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    // Capturar a posição do clique ANTES do debounce para evitar currentTarget null
    const currentTarget = event.currentTarget;
    const position = currentTarget ? (() => {
      const buttonRect = currentTarget.getBoundingClientRect();
      return {
        x: buttonRect.left + buttonRect.width / 2, // Centro do botão (horizontal)
        y: buttonRect.top, // Topo do botão
      };
    })() : null;
    
    // Chamar função debounced com posição já capturada
    debouncedLikeHandler(position);
  };

  const handleShare = async () => {
    const postContent = (post as ProcessedPost).content;
    if (!postContent) return;

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = postContent;
    const textContent = tempDiv.textContent || tempDiv.innerText;
    const shareText = textContent.trim().slice(0, 100) + (textContent.length > 100 ? '...' : '');
    
    const baseUrl = window.location.origin;
    const shareUrl = `${baseUrl}/post/${post.id}`;

    try {
      // Registrar compartilhamento no banco de dados
      let shareType: 'copy_link' | 'native_share' = 'copy_link';
      
      await navigator.clipboard.writeText(`${shareText}\n\n${shareUrl}`);
      toast({
        description: "Link copiado para a área de transferência!"
      });

      // Tentar usar API nativa de compartilhamento se disponível
      if (navigator.share && window.isSecureContext) {
        try {
          await navigator.share({
            title: 'Compartilhar post',
            text: shareText,
            url: shareUrl
          });
          shareType = 'native_share';
          toast({
            description: "Post compartilhado com sucesso!"
          });
        } catch (shareError) {
          // Share cancelado ou não disponível - silencioso
        }
      }

      // Registrar o compartilhamento no banco de dados
      if (effectiveUserId && post.id) {
        try {
          await sharePostMutation.mutateAsync({
            postId: post.id,
            userId: effectiveUserId,
            shareType
          });
          // Compartilhamento registrado com sucesso
        } catch (shareDbError) {
          logQueryEvent('EnhancedPostActions', 'Erro ao registrar compartilhamento no banco', shareDbError, 'error');
          // Não mostrar erro para o usuário, pois o compartilhamento funcionou
        }
      }
    } catch (error) {
      logQueryEvent('EnhancedPostActions', 'Erro ao copiar para área de transferência', error, 'error');
      toast({
        variant: "destructive",
        description: "Não foi possível copiar o link. Tente novamente."
      });
    }
  };

  return (
    <div className="flex gap-4 relative overflow-hidden">
      <HoverCard openDelay={100} closeDelay={200}>
        <HoverCardTrigger asChild>
          <Button 
            variant="ghost" 
            size="sm"
            onClick={handleButtonClick}
            className={isLiked ? "text-blue-500 flex items-center" : "flex items-center"}
            disabled={toggleLikeMutation.isPending}
          >
            <div className="flex items-center">
              <ThumbsUp className="h-4 w-4 mr-2" />
              {/* Exibir o número total de curtidas */}
              <span className="mr-1">{currentPost.likes || 0}</span>
              
              {/* Exibir avatares das últimas 5 pessoas que curtiram */}
              {currentPost.liked_by && currentPost.liked_by.length > 0 && (
                <div className="flex -space-x-2 ml-1.5 overflow-hidden">
                  {currentPost.liked_by.slice(0, 5).map((like, index) => (
                    <SmartAvatar 
                      key={`${like.profiles?.id || ''}-${index}`}
                      className="h-5 w-5 border border-white dark:border-gray-800 inline-block text-[10px]"
                      src={like.profiles?.avatar_url}
                      name={like.profiles?.full_name}
                    />
                  ))}
                  {currentPost.liked_by.length > 5 && (
                    <div className="h-5 w-5 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-[10px] border border-white dark:border-gray-800">
                      +{currentPost.liked_by.length - 5}
                    </div>
                  )}
                </div>
              )}
            </div>
          </Button>
        </HoverCardTrigger>
        <HoverCardContent 
          className="w-80 z-50" 
          side="top" 
          align="start"
          sideOffset={5}
        >
          <div className="space-y-3">
            <h4 className="text-sm font-semibold">
              {currentPost.likes > 0 
                ? `Curtido por ${currentPost.likes} ${currentPost.likes === 1 ? 'pessoa' : 'pessoas'}` 
                : 'Curtidas'}
            </h4>
            {currentPost.liked_by && currentPost.liked_by.length > 0 ? (
              <div className="space-y-2 max-h-[300px] overflow-y-auto pr-1">
                {currentPost.liked_by.map((like, index) => {
                  // Verificar se temos dados válidos do perfil
                  if (!like.profiles) return null;
                  
                  return (
                    <div 
                      key={`${like.profiles.id || ''}-${index}`} 
                      className="flex items-center gap-2 py-1 px-1 hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-md transition-colors"
                    >
                      <SmartAvatar 
                        className="h-8 w-8 border border-gray-100 dark:border-gray-700 bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300"
                        src={like.profiles.avatar_url}
                        name={like.profiles.full_name}
                      />
                      <span className="text-sm font-medium">{like.profiles.full_name || "Usuário"}</span>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="flex items-center justify-center py-4">
                <span className="text-sm text-muted-foreground">
                  Ninguém curtiu este post ainda
                </span>
              </div>
            )}
          </div>
        </HoverCardContent>
      </HoverCard>

      {effectiveUserId !== post.author.id && (
        <HoverCard openDelay={100} closeDelay={200}>
          <HoverCardTrigger asChild>
            <Button variant="ghost" size="sm" onClick={() => handleStartChat(post.author)} disabled={isLoading}>
              <MessageSquare className="h-4 w-4" />
            </Button>
          </HoverCardTrigger>
          <HoverCardContent 
            className="w-auto p-2"
            side="top" 
            align="center"
            sideOffset={5}
          >
            <div className="text-sm text-gray-600 dark:text-gray-300">
              {post.author.full_name ? `Conversar com ${post.author.full_name}` : "Conversar com o autor"}
            </div>
          </HoverCardContent>
        </HoverCard>
      )}

      <Button variant="ghost" size="sm" onClick={handleShare}>
        <Share2 className="h-4 w-4 mr-2" />
        Compartilhar
      </Button>
      <div className="flex items-center ml-1">
        <HoverCard openDelay={100} closeDelay={200}>
          <HoverCardTrigger asChild>
            <Button 
              variant="ghost" 
              size="sm"
              className="text-gray-500 dark:text-gray-400"
              tabIndex={-1} // Não focável, apenas informativo
            >
              <Eye className="h-4 w-4 mr-2" />
              {isLoadingViewCount ? '...' : viewCount.toLocaleString()}
            </Button>
          </HoverCardTrigger>
          <HoverCardContent 
            className="w-60 z-50" 
            side="top" 
            align="center"
            sideOffset={5}
          >
            <div className="text-center space-y-1">
              <h4 className="text-sm font-semibold">
                {viewCount === 0 ? 'Nenhuma visualização' : 
                 viewCount === 1 ? '1 visualização' : 
                 `${viewCount.toLocaleString()} visualizações`}
              </h4>
              <p className="text-xs text-muted-foreground">
                Número de vezes que este post foi visualizado
              </p>
            </div>
          </HoverCardContent>
        </HoverCard>
      </div>
    </div>
  );
}; 