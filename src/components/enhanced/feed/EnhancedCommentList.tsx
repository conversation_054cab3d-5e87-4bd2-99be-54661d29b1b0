/**
 * Componente aprimorado para exibição de comentários de posts
 * <AUTHOR> Internet 2025
 */

import { useState, useEffect, useRef, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronUp, ArrowDownAZ, ArrowUpAZ, Volume2, VolumeX, MessageSquarePlus } from "lucide-react";
import { EnhancedCommentItem } from "./EnhancedCommentItem";
import { EnhancedCommentForm } from "./EnhancedCommentForm";
import { CommentsPagination } from "../../feed/CommentsPagination";
import { usePostComments } from "@/lib/query/hooks/useComments";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { usePlatform } from "@/hooks/usePlatform";
import { playSound, SoundEffects, isSoundEnabled } from "@/lib/sound-effects";

const COMMENTS_PER_PAGE = 10;
const SORT_PREFERENCE_KEY = "comments_sort_preference";
const SOUND_PREFERENCE_KEY = "comments_sound_preference";

interface EnhancedCommentListProps {
  postId: string;
  currentUserId: string | null;
  highlightCommentId?: string | null;
}

export function EnhancedCommentList({ postId, currentUserId, highlightCommentId }: EnhancedCommentListProps) {
  const { isNative } = usePlatform();
  const [showAllComments, setShowAllComments] = useState(false);
  const [sortOldestFirst, setSortOldestFirst] = useState(false);
  const [localSoundEnabled, setLocalSoundEnabled] = useState(true);
  const [newCommentCount, setNewCommentCount] = useState(0);
  const highlightRef = useRef<HTMLDivElement>(null);
  const [collapsedComments, setCollapsedComments] = useState<Record<string, boolean>>({});
  const [showComments, setShowComments] = useState(true);
  const { comments: rawComments, isLoading, refetch } = usePostComments(postId);
  
  const commentsCountRef = useRef(0);
  
  // Carregar preferências do localStorage
  useEffect(() => {
    const savedSortPreference = localStorage.getItem(SORT_PREFERENCE_KEY);
    if (savedSortPreference !== null) {
      setSortOldestFirst(savedSortPreference === "oldest");
    }
    
    const savedSoundPreference = localStorage.getItem(SOUND_PREFERENCE_KEY);
    if (savedSoundPreference !== null) {
      setLocalSoundEnabled(savedSoundPreference === "enabled");
    }
  }, []);
  
  // Scroll automático para comentário destacado
  useEffect(() => {
    if (highlightCommentId && rawComments) {
      // Se há um comentário para destacar, expandir todos os comentários
      setShowAllComments(true);
      
      // Aguardar um pouco para que os comentários sejam renderizados
      const timer = setTimeout(() => {
        const element = document.getElementById(`comment-${highlightCommentId}`);
        if (element) {
          element.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center' 
          });
          
          // Adicionar destaque visual temporário
          element.style.backgroundColor = 'rgba(34, 197, 94, 0.1)';
          element.style.border = '2px solid rgba(34, 197, 94, 0.3)';
          element.style.borderRadius = '8px';
          
          // Remover destaque após 3 segundos
          setTimeout(() => {
            element.style.backgroundColor = '';
            element.style.border = '';
            element.style.borderRadius = '';
          }, 3000);
        }
      }, 500);
      
      return () => clearTimeout(timer);
    }
  }, [highlightCommentId, rawComments]);

  // Detectar novos comentários
  useEffect(() => {
    if (!rawComments) return;
    
    // Se já temos um valor anterior
    if (commentsCountRef.current > 0 && rawComments.length > commentsCountRef.current) {
      const newCount = rawComments.length - commentsCountRef.current;
      setNewCommentCount((prev) => prev + newCount);
      
      // Tocar som apenas se: (1) Som global habilitado E (2) Som local habilitado
      if (isSoundEnabled() && localSoundEnabled) {
        playSound(SoundEffects.NEW_COMMENT);
      }
    }
    
    // Atualizar referência
    commentsCountRef.current = rawComments.length;
  }, [rawComments, localSoundEnabled]);
  
  const toggleSortOrder = () => {
    const newValue = !sortOldestFirst;
    setSortOldestFirst(newValue);
    localStorage.setItem(SORT_PREFERENCE_KEY, newValue ? "oldest" : "newest");
  };
  
  const toggleLocalSound = () => {
    const newValue = !localSoundEnabled;
    setLocalSoundEnabled(newValue);
    localStorage.setItem(SOUND_PREFERENCE_KEY, newValue ? "enabled" : "disabled");
  };
  
  const toggleShowComments = () => {
    setShowComments(!showComments);
  };
  
  const handleCommentSuccess = () => {
    // Tocar som ao adicionar comentário apenas se ambos habilitados
    if (isSoundEnabled() && localSoundEnabled) {
      playSound(SoundEffects.COMMENT_ADDED);
    }
    
    refetch();
    // Resetar contagem de novos comentários
    setNewCommentCount(0);
  };
  
  const viewNewComments = () => {
    refetch();
    setNewCommentCount(0);
  };
  
  const toggleCommentCollapse = (commentId: string) => {
    setCollapsedComments(prev => ({
      ...prev,
      [commentId]: !prev[commentId]
    }));
  };

  const displayedComments = useMemo(() => {
    if (!rawComments) return [];
    
    // Ordenar comentários
    const sortedComments = [...rawComments].sort((a, b) => {
      const dateA = new Date(a.created_at).getTime();
      const dateB = new Date(b.created_at).getTime();
      return sortOldestFirst ? dateA - dateB : dateB - dateA;
    });
    
    // Limitar número de comentários se não estiver mostrando todos
    return showAllComments ? sortedComments : sortedComments.slice(0, COMMENTS_PER_PAGE);
  }, [rawComments, sortOldestFirst, showAllComments]);

  if (isLoading) {
    return (
      <div className="relative rounded-md p-4 mt-3 bg-gray-50/50 dark:bg-gray-800/20">
        <div className="flex items-center justify-center py-4">
          <div className="inline-block h-6 w-6 animate-spin rounded-full border-3 border-solid border-primary border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
          <span className="ml-3 text-sm font-medium">Carregando comentários...</span>
        </div>
      </div>
    );
  }

  const hasMoreComments = rawComments.length > COMMENTS_PER_PAGE;

  return (
    <div className={cn(
      "relative overflow-hidden",
      isNative ? "px-2 py-1 space-y-1" : "px-4 py-3 space-y-3" // Padding muito menor no mobile
    )}>
      {/* Gradiente de fundo sutil */}
      <div className="absolute inset-0 bg-gradient-to-br from-orange-50/10 to-transparent dark:from-orange-950/5 dark:to-transparent -z-10 pointer-events-none"></div>
      {/* Cabeçalho com controles */}
      <div className="flex items-center justify-between border-b border-gray-100 dark:border-gray-800/50 pb-2">
        <div className="flex items-center gap-2">
          <h3 className="text-sm font-medium text-gray-800 dark:text-gray-200">
            Comentários ({rawComments?.length || 0})
          </h3>
          
          {newCommentCount > 0 && (
            <Badge 
              variant="outline" 
              className="bg-orange-100/80 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-800/50 px-2 py-0.5 rounded-full text-xs cursor-pointer hover:bg-orange-200/80 dark:hover:bg-orange-800/30 transition-colors"
              onClick={viewNewComments}
            >
              {newCommentCount} {newCommentCount === 1 ? 'novo' : 'novos'}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          {/* Botão de ordenação */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-gray-500 hover:text-orange-600 dark:text-gray-400 dark:hover:text-orange-400 hover:bg-orange-50 dark:hover:bg-orange-950/20 rounded-full"
                  onClick={toggleSortOrder}
                >
                  {sortOldestFirst ? (
                    <ArrowUpAZ className="h-4 w-4" />
                  ) : (
                    <ArrowDownAZ className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>{sortOldestFirst ? "Mais antigos primeiro" : "Mais recentes primeiro"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          {/* Botão de som */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleLocalSound}
                  className="h-8 w-8 text-gray-500 hover:text-orange-600 dark:text-gray-400 dark:hover:text-orange-400 hover:bg-orange-50 dark:hover:bg-orange-950/20 rounded-full"
                >
                  {(isSoundEnabled() && localSoundEnabled) ? 
                    <Volume2 className="h-4 w-4" /> : 
                    <VolumeX className="h-4 w-4" />
                  }
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>
                  {!isSoundEnabled() ? 'Som global desabilitado' : 
                   localSoundEnabled ? 'Desativar sons de comentários' : 'Ativar sons de comentários'}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
      
      {/* Formulário de comentário */}
      {currentUserId && showComments && (
        <div className="bg-gradient-to-r from-gray-50/90 to-gray-50/70 dark:from-gray-900/40 dark:to-gray-900/20 rounded-lg p-3 border border-gray-100 dark:border-gray-800/30 mb-3 shadow-sm backdrop-blur-sm transition-all duration-300 hover:shadow-md hover:border-orange-200/30 dark:hover:border-orange-800/20">
          <EnhancedCommentForm
            postId={postId}
            onSuccess={handleCommentSuccess}
            placeholder="Deixe seu comentário..."
          />
        </div>
      )}
      
      {/* Lista de comentários */}
      <AnimatePresence initial={false} mode="wait">
        {showComments && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.15 }}
            className="space-y-2"
          >
            {displayedComments.length === 0 ? (
              <div className={cn(
                "flex flex-col items-center justify-center rounded-lg bg-muted/5 text-center",
                isNative ? "p-2" : "p-6" // Padding MUITO menor no mobile
              )}>
                <MessageSquarePlus className={cn(
                  "text-muted-foreground/50",
                  isNative ? "mb-1 h-4 w-4" : "mb-3 h-10 w-10" // Ícone MUITO menor no mobile
                )} />
                <h3 className={cn(
                  "font-medium",
                  isNative ? "mb-0 text-xs" : "mb-1 text-md" // Texto MUITO menor no mobile
                )}>Nenhum comentário ainda</h3>
                {!isNative && ( // Esconder o subtexto no mobile
                  <p className="text-sm text-muted-foreground">
                    Seja o primeiro a compartilhar seus pensamentos sobre este post!
                  </p>
                )}
              </div>
            ) : (
              <div className="space-y-4 mt-4">
                {displayedComments.map((comment, index) => (
                  <motion.div
                    key={`comment-${postId}-${comment.id}`}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ 
                      duration: 0.15,
                      delay: index * 0.02,
                      ease: [0.4, 0, 0.2, 1]
                    }}
                    className="mb-3 last:mb-0"
                  >
                    <div 
                      id={`comment-${comment.id}`}
                      className="rounded-lg bg-white dark:bg-gray-800/40 p-3 transition-all duration-300 hover:shadow-md border border-gray-100 dark:border-gray-800/30 hover:border-orange-200/50 dark:hover:border-orange-800/30 hover:bg-gradient-to-r hover:from-white hover:to-orange-50/30 dark:hover:from-gray-800/40 dark:hover:to-orange-950/10"
                    >
                      <EnhancedCommentItem
                        comment={comment}
                        postId={postId}
                        currentUserId={currentUserId}
                        onReplySuccess={handleCommentSuccess}
                        expanded={!collapsedComments[comment.id]}
                        onToggleExpand={() => toggleCommentCollapse(comment.id)}
                      />
                    </div>
                    
                    {/* Renderizar respostas apenas se o comentário pai não estiver colapsado */}
                    
                    <AnimatePresence>
                      {!collapsedComments[comment.id] && comment.replies && comment.replies.length > 0 && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.1 }}
                          className="ml-8 mt-2 space-y-3 pl-4 border-l-2 border-orange-200/30 dark:border-orange-800/20"
                        >
                          <AnimatePresence initial={false}>
                            {comment.replies.map((reply, replyIndex) => (
                              <motion.div
                                key={`reply-${postId}-${comment.id}-${reply.id}`}
                                initial={{ opacity: 0, x: -5 }}
                                animate={{ opacity: 1, x: 0 }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ 
                                  duration: 0.12,
                                  delay: replyIndex * 0.01,
                                  ease: "easeOut"
                                }}
                                id={`comment-${reply.id}`}
                                className="rounded-lg bg-gray-50/80 dark:bg-gray-900/30 p-2 border border-gray-100/50 dark:border-gray-800/20 mt-2 transition-all duration-300 hover:shadow-sm hover:border-orange-200/30 dark:hover:border-orange-800/20 hover:bg-gradient-to-r hover:from-gray-50/80 hover:to-orange-50/20 dark:hover:from-gray-900/30 dark:hover:to-orange-950/5"
                              >
                                <EnhancedCommentItem
                                  comment={reply}
                                  postId={postId}
                                  currentUserId={currentUserId}
                                  onReplySuccess={handleCommentSuccess}
                                  isReply={true}
                                />
                              </motion.div>
                            ))}
                          </AnimatePresence>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                ))}
              </div>
            )}
            
            {hasMoreComments && (
              <div className="flex justify-center mt-3">
                <CommentsPagination
                  hasMoreComments={hasMoreComments}
                  showAllComments={showAllComments}
                  onShowMore={() => setShowAllComments(true)}
                />
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
} 