/**
 * Componente para exibição da evolução de XP e conquistas nos últimos 6 meses.
 * <AUTHOR> Internet 2025
 */
import { useMemo } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
import { useAuthStore } from "@/stores/authStore";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { AdvancedRefreshButton } from "@/components/ui/advanced-refresh-button";
import { TrendingUp, Award } from "lucide-react";
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from "recharts";

interface MonthlyStatsData {
  month_name: string;
  month_year: string;
  total_xp: number;
  total_achievements: number;
}

export function MonthlyGamificationStats() {
  const company_id = useAuthStore((state) => state.company_id);
  const queryClient = useQueryClient();

  // Buscar estatísticas mensais usando a função RPC
  const { data: monthlyStats, isLoading, refetch } = useQuery({
    queryKey: ['gamification', 'monthly-stats', company_id],
    queryFn: async () => {
      try {
        logQueryEvent("MonthlyGamificationStats", "Buscando estatísticas mensais de gamificação");
        
        if (!company_id) {
          throw new Error("ID da empresa não disponível");
        }
        
        // Chamar a função RPC para obter as estatísticas mensais
        const { data, error } = await supabase
          .rpc('get_monthly_gamification_stats', { 
            p_company_id: company_id,
            p_months_back: 6
          });
        
        if (error) {
          logQueryEvent("MonthlyGamificationStats", "Erro ao chamar RPC get_monthly_gamification_stats", error, "error");
          throw error;
        }
        
        if (!data || data.length === 0) {
          logQueryEvent("MonthlyGamificationStats", "Nenhum dado retornado pela RPC", { company_id }, "warning");
          return [];
        }
        
        // Converter os tipos de dados para corresponder à interface MonthlyStatsData
        const stats: MonthlyStatsData[] = data.map(item => ({
          month_name: item.month_name,
          month_year: item.month_year,
          total_xp: Number(item.total_xp) || 0,
          total_achievements: Number(item.total_achievements) || 0
        }));
        
        logQueryEvent("MonthlyGamificationStats", "Estatísticas mensais obtidas", { stats });
        
        return stats;
      } catch (error) {
        logQueryEvent("MonthlyGamificationStats", "Erro ao buscar estatísticas mensais", error, "error");
        return [];
      }
    },
    enabled: !!company_id,
    staleTime: 1000 * 60 * 5, // 5 minutos
  });

  // Formatar dados para os gráficos
  const chartData = useMemo(() => {
    if (!monthlyStats || monthlyStats.length === 0) return [];
    
    // Ordenar os dados por mês/ano para garantir a sequência cronológica
    return [...monthlyStats].sort((a, b) => {
      const dateA = new Date(a.month_year);
      const dateB = new Date(b.month_year);
      return dateA.getTime() - dateB.getTime();
    });
  }, [monthlyStats]);

  // Função para atualizar os dados
  const handleRefresh = async () => {
    try {
      logQueryEvent("MonthlyGamificationStats", "Atualizando dados manualmente");
      
      // Usar apenas invalidateQueries com refetchType para evitar dupla execução
      await queryClient.invalidateQueries({ 
        queryKey: ['gamification', 'monthly-stats', company_id],
        refetchType: 'active' // Garante que apenas queries ativas sejam refetchadas
      });
      
      logQueryEvent("MonthlyGamificationStats", "Dados atualizados com sucesso");
    } catch (error) {
      logQueryEvent("MonthlyGamificationStats", "Erro ao atualizar dados", error, "error");
      console.error("Erro no refresh:", error);
    }
  };

  // Função para formatar números grandes
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  // Customização do tooltip para o gráfico
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded-md shadow-md">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name === "XP" ? "XP ganho: " : "Conquistas: "}
              <span className="font-semibold">
                {entry.name === "XP" ? formatNumber(entry.value) : entry.value}
              </span>
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h2 className="text-2xl font-bold">Evolução nos Últimos 6 Meses</h2>
          <AdvancedRefreshButton
            onRefresh={handleRefresh}
            size="sm"
            variant="outline"
            operationName="Estatísticas Mensais"
            successMessage="Dados atualizados!"
            errorMessage="Erro ao atualizar dados"
            enableSound={true}
          >
            Atualizar
          </AdvancedRefreshButton>
        </div>
        
        {/* Estatísticas Resumidas */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          {isLoading ? (
            Array(2).fill(0).map((_, i) => (
              <Card key={i} className="border rounded-lg overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-white">
                  <Skeleton className="h-5 w-40" />
                  <Skeleton className="h-5 w-5 rounded-full" />
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="space-y-2">
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                </CardContent>
              </Card>
            ))
          ) : chartData.length > 0 ? (
            <>
              <Card className="border rounded-lg overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-white">
                  <CardTitle className="text-lg font-medium">Total de XP</CardTitle>
                  <div className="h-5 w-5 rounded-full bg-orange-500 flex items-center justify-center text-white text-xs">
                    Σ
                  </div>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="flex flex-col">
                    <div className="text-3xl font-bold">
                      {formatNumber(chartData.reduce((sum, item) => sum + item.total_xp, 0))}
                    </div>
                    <div className="text-muted-foreground text-sm">
                      XP acumulado nos últimos 6 meses
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border rounded-lg overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-white">
                  <CardTitle className="text-lg font-medium">Total de Conquistas</CardTitle>
                  <div className="h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs">
                    🏆
                  </div>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="flex flex-col">
                    <div className="text-3xl font-bold">
                      {chartData.reduce((sum, item) => sum + item.total_achievements, 0)}
                    </div>
                    <div className="text-muted-foreground text-sm">
                      Conquistas obtidas nos últimos 6 meses
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          ) : null}
        </div>
        
        {/* Gráfico de XP Ganho */}
        <Card className="border rounded-lg overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-white">
            <CardTitle className="text-lg font-medium">XP Ganho Mensalmente</CardTitle>
            <div className="h-5 w-5 rounded-full bg-orange-500 flex items-center justify-center text-white text-xs">
              <TrendingUp className="h-3 w-3" />
            </div>
          </CardHeader>
          <div className="h-80 w-full">
            {isLoading ? (
              <div className="h-full w-full flex items-center justify-center">
                <Skeleton className="h-full w-full" />
              </div>
            ) : chartData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={chartData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 10,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis 
                    dataKey="month_name" 
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis 
                    tickFormatter={(value) => formatNumber(value)}
                    tick={{ fontSize: 12 }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="total_xp"
                    name="XP"
                    stroke="#FF6B00"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-full w-full flex items-center justify-center">
                <p className="text-muted-foreground">Nenhum dado disponível</p>
              </div>
            )}
          </div>
        </Card>
        
        {/* Gráfico de Conquistas */}
        <Card className="border rounded-lg overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-white">
            <CardTitle className="text-lg font-medium">Conquistas Obtidas Mensalmente</CardTitle>
            <div className="h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs">
              <Award className="h-3 w-3" />
            </div>
          </CardHeader>
          <div className="h-80 w-full">
            {isLoading ? (
              <div className="h-full w-full flex items-center justify-center">
                <Skeleton className="h-full w-full" />
              </div>
            ) : chartData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={chartData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 10,
                  }}
                  barSize={40}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis 
                    dataKey="month_name" 
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis 
                    tick={{ fontSize: 12 }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Bar
                    dataKey="total_achievements"
                    name="Conquistas"
                    fill="#3B82F6"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-full w-full flex items-center justify-center">
                <p className="text-muted-foreground">Nenhum dado disponível</p>
              </div>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
}
