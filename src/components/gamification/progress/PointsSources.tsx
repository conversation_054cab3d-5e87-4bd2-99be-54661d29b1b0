/**
 * Componente para exibição das fontes de pontos na gamificação.
 * <AUTHOR> Internet 2025
 */
import { useMemo } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
import { useAuthStore } from "@/stores/authStore";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { AdvancedRefreshButton } from "@/components/ui/advanced-refresh-button";

interface PointsSource {
  source_name: string;
  points: number;
  percentage: number;
}

export function PointsSources() {
  const company_id = useAuthStore((state) => state.company_id);
  const queryClient = useQueryClient();

  // Buscar fontes de pontos usando a função RPC
  const { data: pointsSources, isLoading, refetch } = useQuery({
    queryKey: ['gamification', 'points-sources', company_id],
    queryFn: async () => {
      try {
        logQueryEvent("PointsSources", "Buscando fontes de pontos via RPC");
        
        if (!company_id) {
          throw new Error("ID da empresa não disponível");
        }
        
        // Chamar a função RPC que criamos para obter as fontes de pontos
        const { data, error } = await supabase
          .rpc('get_points_sources', { p_company_id: company_id });
        
        if (error) {
          logQueryEvent("PointsSources", "Erro ao chamar RPC get_points_sources", error, "error");
          throw error;
        }
        
        if (!data || data.length === 0) {
          logQueryEvent("PointsSources", "Nenhum dado retornado pela RPC", { company_id }, "warning");
          return [];
        }
        
        // Converter os tipos de dados para corresponder à interface PointsSource
        const sources: PointsSource[] = data.map(item => ({
          source_name: item.source_name,
          points: Number(item.points),
          percentage: Number(item.percentage)
        }));
        
        logQueryEvent("PointsSources", "Fontes de pontos obtidas via RPC", { sources });
        
        return sources;
      } catch (error) {
        logQueryEvent("PointsSources", "Erro ao buscar fontes de pontos", error, "error");
        throw error;
      }
    },
    enabled: !!company_id,
    staleTime: 1000 * 60 * 5, // 5 minutos
  });

  // Função para atualizar os dados
  const handleRefresh = async () => {
    try {
      logQueryEvent("PointsSources", "Atualizando dados manualmente");
      
      // Usar apenas invalidateQueries com refetchType para evitar dupla execução
      await queryClient.invalidateQueries({ 
        queryKey: ['gamification', 'points-sources', company_id],
        refetchType: 'active' // Garante que apenas queries ativas sejam refetchadas
      });
      
      logQueryEvent("PointsSources", "Dados atualizados com sucesso");
    } catch (error) {
      logQueryEvent("PointsSources", "Erro ao atualizar dados", error, "error");
      console.error("Erro no refresh:", error);
    }
  };

  // Cores para as barras de progresso
  const getColorClass = (index: number) => {
    const colors = [
      "bg-orange-500", // Laranja primário
      "bg-blue-500",   // Azul
      "bg-green-500",  // Verde
      "bg-purple-500", // Roxo
      "bg-yellow-500", // Amarelo
      "bg-red-500",    // Vermelho
    ];
    return colors[index % colors.length];
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-2xl font-bold">Fontes de Pontos de Experiência</h2>
        <AdvancedRefreshButton
          onRefresh={handleRefresh}
          size="sm"
          variant="outline"
          operationName="Fontes de Pontos"
          successMessage="Dados atualizados!"
          errorMessage="Erro ao atualizar dados"
          enableSound={true}
        >
          Atualizar
        </AdvancedRefreshButton>
      </div>

      <Card className="border rounded-lg overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-white">
          <CardTitle className="text-lg font-medium">Conheça quais atividades geraram mais pontos de experiência</CardTitle>
          <div className="text-sm text-muted-foreground">Como os pontos de experiência foram distribuídos</div>
        </CardHeader>
        <CardContent className="pt-4">
          {isLoading ? (
            <div className="space-y-4">
              {Array(5).fill(0).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-6 w-full" />
                </div>
              ))}
            </div>
          ) : pointsSources && pointsSources.length > 0 ? (
            <div className="space-y-4">
              {pointsSources.map((source, index) => (
                <div key={index} className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{source.source_name}</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-bold">{source.points} pontos</span>
                      <span className="text-xs text-muted-foreground">({source.percentage}%)</span>
                    </div>
                  </div>
                  <div className="h-2.5 w-full bg-gray-100 rounded-full overflow-hidden">
                    <div 
                      className={`h-full ${getColorClass(index)} rounded-full`} 
                      style={{ width: `${source.percentage}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="py-8 text-center">
              <p className="text-muted-foreground">Nenhum dado de pontos disponível</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
