/**
 * Componente para exibição do comparativo de níveis entre departamentos.
 * Mostra barras horizontais coloridas representando o nível médio de cada departamento.
 * <AUTHOR> Internet 2025
 */
import { useMemo, useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { AdvancedRefreshButtonIcon } from '@/components/ui/advanced-refresh-button';
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { useDepartmentLevels } from '@/lib/query/hooks/useDepartmentLevels';
import { logQueryEvent } from "@/lib/logs/showQueryLogs";

import { useQueryClient } from "@tanstack/react-query";
import { useAuthStore } from '@/stores/authStore';
import { supabase } from "@/integrations/supabase/client";

// Interface importada do hook useDepartmentLevels

export function DepartmentComparison() {
  const queryClient = useQueryClient();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const user = useAuthStore((state) => state.user);
  const [userDepartment, setUserDepartment] = useState<string | null>(null);
  
  // Buscar dados reais de níveis por departamento
  const { data: departmentLevels = [], isLoading, error, refetch } = useDepartmentLevels();
  
  // Buscar o departamento do usuário
  useEffect(() => {
    const fetchUserDepartment = async () => {
      if (!user?.id) return;
      
      try {
        // Primeiro, buscar o department_id do perfil do usuário
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('department_id')
          .eq('id', user.id)
          .single();
          
        if (profileError) {
          logQueryEvent("DepartmentComparison", "Erro ao buscar department_id do usuário", profileError, "error");
          return;
        }
        
        if (!profileData?.department_id) return;
        
        // Depois, buscar o nome do departamento
        const { data: departmentData, error: departmentError } = await supabase
          .from('departments')
          .select('name')
          .eq('id', profileData.department_id)
          .single();
          
        if (departmentError) {
          logQueryEvent("DepartmentComparison", "Erro ao buscar nome do departamento", departmentError, "error");
          return;
        }
        
        if (departmentData?.name) {
          setUserDepartment(departmentData.name);
        }
      } catch (err) {
        logQueryEvent("DepartmentComparison", "Erro ao buscar departamento do usuário", err, "error");
      }
    };
    
    fetchUserDepartment();
  }, [user?.id]);
  
  // Logar erros se ocorrerem
  useMemo(() => {
    if (error) {
      logQueryEvent("DepartmentComparison", "Erro ao carregar dados de departamentos", error, "error");
    }
  }, [error]);
  
  // Função para limpar o cache e buscar novamente os dados
  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      logQueryEvent("DepartmentComparison", "Atualizando dados de departamentos manualmente");
      
      // Invalidar o cache para a chave "departmentLevels"
      await queryClient.invalidateQueries({ queryKey: ["departmentLevels"] });
      
      // Buscar novamente os dados
      await refetch();
      
      logQueryEvent("DepartmentComparison", "Dados de departamentos atualizados com sucesso");
    } catch (error) {
      logQueryEvent("DepartmentComparison", "Erro ao atualizar dados de departamentos", error, "error");
    } finally {
      setIsRefreshing(false);
    }
  };

  // Encontrar o nível máximo para calcular a largura relativa das barras
  const maxLevel = useMemo(() => {
    if (!departmentLevels.length) return 0;
    return Math.max(...departmentLevels.map(dept => dept.level));
  }, [departmentLevels]);

  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">
            <Skeleton className="h-6 w-3/4" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center gap-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-6 w-full" />
                <Skeleton className="h-4 w-8" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }
  
  if (error) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Comparativo entre Departamentos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-destructive p-4 rounded-lg bg-destructive/10">
            <p>Não foi possível carregar os dados dos departamentos. Tente novamente mais tarde.</p>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  if (departmentLevels.length === 0) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Comparativo entre Departamentos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground p-4">
            <p>Nenhum dado de departamento disponível.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg">Comparativo entre Departamentos</CardTitle>
            <p className="text-xs text-muted-foreground">Nível médio, comparação e análise por departamento</p>
          </div>
          <AdvancedRefreshButtonIcon
            onRefresh={handleRefresh}
            operationName="Departamentos"
            successMessage="Dados atualizados!"
            errorMessage="Erro ao atualizar dados"
            enableSound={true}
            title="Atualizar dados dos departamentos"
          />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {departmentLevels.map((dept, index) => {
            const isUserDepartment = userDepartment && dept.name === userDepartment;
            const isAverageDepartment = dept.name === "Nível Médio";
            
            return (
              <div 
                key={index} 
                className={cn(
                  "flex items-center gap-2 p-2 rounded-lg transition-colors",
                  isUserDepartment && "bg-primary/10 border border-primary/20",
                  isAverageDepartment && "bg-amber-50 border border-amber-200"
                )}
              >
                <div className={cn(
                  "w-24 text-sm font-medium",
                  isAverageDepartment && "text-amber-700",
                  isUserDepartment && "text-primary"
                )}>
                  {dept.name}
                  {isUserDepartment && (
                    <span className="ml-1 text-xs bg-primary/20 text-primary px-2 py-0.5 rounded-full">
                      Você
                    </span>
                  )}
                  {isAverageDepartment && (
                    <span className="block text-xs text-amber-600 mt-0.5">
                      (todos os departamentos)
                    </span>
                  )}
                </div>
                <div className="flex-1 h-6 bg-gray-100 rounded-full overflow-hidden">
                  <div 
                    className={cn(
                      "h-full rounded-full",
                      isUserDepartment && "bg-primary",
                      isAverageDepartment && "bg-amber-500",
                      !isUserDepartment && !isAverageDepartment && "bg-orange-500"
                    )}
                    style={{ 
                      width: `${(dept.level / maxLevel) * 100}%`,
                    }}
                  />
                </div>
                <div className={cn(
                  "text-sm font-medium w-10 text-right",
                  isAverageDepartment && "text-amber-700",
                  isUserDepartment && "text-primary"
                )}>
                  Nível {dept.level}
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
