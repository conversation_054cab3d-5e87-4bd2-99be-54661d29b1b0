/**
 * Componente para exibição da distribuição de níveis dos usuários na gamificação.
 * <AUTHOR> Internet 2025
 */
import { useMemo } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
import { useAuthStore } from "@/stores/authStore";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LabelList } from "recharts";
import { QueryKeys } from "@/lib/query/queryKeys";
import { AdvancedRefreshButton } from "@/components/ui/advanced-refresh-button";
import { PointsSources } from "./PointsSources";
import { LevelTimeComparison } from "./LevelTimeComparison";
import { MonthlyGamificationStats } from "./MonthlyGamificationStats";

interface LevelDistribution {
  level_range: string;
  count: number;
  percentage: number;
}

export function UserEvolution() {
  const company_id = useAuthStore((state) => state.company_id);
  const queryClient = useQueryClient();

  // Buscar distribuição de níveis usando a função RPC
  const { data: levelDistribution, isLoading: isLoadingLevels, refetch } = useQuery({
    queryKey: ['gamification', 'level-distribution', company_id],
    queryFn: async () => {
      try {
        logQueryEvent("UserEvolution", "Buscando distribuição de níveis via RPC");
        
        if (!company_id) {
          throw new Error("ID da empresa não disponível");
        }
        
        // Chamar a função RPC que criamos para obter a distribuição de níveis
        const { data, error } = await supabase
          .rpc('get_level_distribution', { p_company_id: company_id });
        
        if (error) {
          logQueryEvent("UserEvolution", "Erro ao chamar RPC get_level_distribution", error, "error");
          throw error;
        }
        
        if (!data || data.length === 0) {
          logQueryEvent("UserEvolution", "Nenhum dado retornado pela RPC", { company_id }, "warning");
          return [];
        }
        
        // Converter os tipos de dados para corresponder à interface LevelDistribution
        const distribution: LevelDistribution[] = data.map(item => ({
          level_range: item.level_range,
          count: Number(item.count),
          percentage: Number(item.percentage)
        }));
        
        logQueryEvent("UserEvolution", "Distribuição obtida via RPC", { distribution });
        
        return distribution;
      } catch (error) {
        logQueryEvent("UserEvolution", "Erro ao buscar distribuição de níveis", error, "error");
        throw error;
      }
    },
    enabled: !!company_id,
    staleTime: 1000 * 60 * 5, // 5 minutos
  });

  // Formatar dados para o gráfico
  const chartData = useMemo(() => {
    if (!levelDistribution) return [];
    return levelDistribution.map(item => ({
      name: item.level_range,
      usuarios: item.count,
      percentual: `${item.percentage}%`,
      percentualNumerico: item.percentage
    }));
  }, [levelDistribution]);

  // Função para atualizar os dados
  const handleRefresh = async () => {
    logQueryEvent("UserEvolution", "Atualizando dados manualmente");
    await queryClient.invalidateQueries({ queryKey: ['gamification', 'level-distribution', company_id] });
    refetch();
  };

  return (
    <div className="space-y-8">
      {/* Seção de Distribuição de Níveis */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h2 className="text-2xl font-bold">Distribuição de Níveis</h2>
          <AdvancedRefreshButton
            onRefresh={handleRefresh}
            size="sm"  
            variant="outline"
            operationName="Distribuição de Níveis"
            successMessage="Dados atualizados!"
            errorMessage="Erro ao atualizar dados"
            enableSound={true}
          >
            Atualizar
          </AdvancedRefreshButton>
        </div>
        
        <Card className="border rounded-lg overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-white">
            <CardTitle className="text-lg font-medium">Distribuição de Níveis</CardTitle>
            <div className="text-sm text-muted-foreground">Distribuição de usuários por faixa de nível</div>
          </CardHeader>
          <div className="h-80 w-full">
            {isLoadingLevels ? (
              <div className="h-full w-full flex items-center justify-center">
                <Skeleton className="h-full w-full" />
              </div>
            ) : chartData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={chartData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                  barSize={30}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="name" />
                  <YAxis yAxisId="left" orientation="left" stroke="#666" />
                  <YAxis 
                    yAxisId="right" 
                    orientation="right" 
                    stroke="#666" 
                    tickFormatter={(value) => `${value}%`}
                  />
                  <Tooltip 
                    formatter={(value, name) => {
                      if (name === "Usuários") return value;
                      return `${value}`;
                    }}
                  />
                  <Bar 
                    dataKey="usuarios" 
                    fill="#FF6B00" 
                    name="Usuários" 
                    yAxisId="left"
                    radius={[4, 4, 0, 0]}
                  >
                    <LabelList 
                      dataKey="usuarios" 
                      position="top" 
                      style={{ fontSize: '12px', fill: '#666' }} 
                    />
                    <LabelList 
                      dataKey="percentual" 
                      position="insideTop" 
                      style={{ fontSize: '10px', fill: '#fff', fontWeight: 'bold' }} 
                      offset={10}
                    />
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-full w-full flex items-center justify-center">
                <p className="text-muted-foreground">Nenhum dado disponível</p>
              </div>
            )}
          </div>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {isLoadingLevels ? (
            Array(3).fill(0).map((_, i) => (
              <Card key={i} className="border rounded-lg overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-white">
                  <Skeleton className="h-5 w-40" />
                  <Skeleton className="h-5 w-5 rounded-full" />
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="space-y-2">
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-2 w-full rounded-full" />
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <>
              <Card className="border rounded-lg overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-white">
                  <CardTitle className="text-lg font-medium">Nível Médio</CardTitle>
                  <div className="h-5 w-5 rounded-full bg-orange-500 flex items-center justify-center text-white text-xs">
                    μ
                  </div>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="flex flex-col">
                    <div className="text-3xl font-bold">
                      {calculateAverageLevel(levelDistribution)}
                    </div>
                    <div className="text-muted-foreground text-sm">
                      Nível médio dos usuários
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border rounded-lg overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-white">
                  <CardTitle className="text-lg font-medium">Nível Mais Comum</CardTitle>
                  <div className="h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs">
                    #
                  </div>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="flex flex-col">
                    <div className="text-3xl font-bold">
                      {findMostCommonLevel(levelDistribution)?.level_range || "N/A"}
                    </div>
                    <div className="text-muted-foreground text-sm">
                      {findMostCommonLevel(levelDistribution)?.percentage || 0}% dos usuários
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border rounded-lg overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-white">
                  <CardTitle className="text-lg font-medium">Total de Usuários</CardTitle>
                  <div className="h-5 w-5 rounded-full bg-green-500 flex items-center justify-center text-white text-xs">
                    Σ
                  </div>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="flex flex-col">
                    <div className="text-3xl font-bold">
                      {calculateTotalUsers(levelDistribution)}
                    </div>
                    <div className="text-muted-foreground text-sm">
                      Usuários com nível atribuído
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Novo card para Tempo Médio Entre Níveis */}
              <LevelTimeComparison />
            </>
          )}
        </div>
      </div>
      
      {/* Componente de Fontes de Pontos */}
      <PointsSources />

      {/* Componente de Estatísticas Mensais */}
      <MonthlyGamificationStats />
    </div>
  );
}

// Funções auxiliares
function calculateAverageLevel(distribution?: LevelDistribution[]): string {
  if (!distribution || distribution.length === 0) return "N/A";
  
  let totalUsers = 0;
  let weightedSum = 0;
  
  distribution.forEach(item => {
    totalUsers += item.count;
    
    // Usar o ponto médio do intervalo para o cálculo
    const rangeParts = item.level_range.split(" ")[1].split("-");
    const min = parseInt(rangeParts[0]);
    const max = parseInt(rangeParts[1]);
    const midPoint = (min + max) / 2;
    
    weightedSum += midPoint * item.count;
  });
  
  if (totalUsers === 0) return "N/A";
  return (weightedSum / totalUsers).toFixed(1);
}

function findMostCommonLevel(distribution?: LevelDistribution[]): LevelDistribution | undefined {
  if (!distribution || distribution.length === 0) return undefined;
  return [...distribution].sort((a, b) => b.count - a.count)[0];
}

function calculateTotalUsers(distribution?: LevelDistribution[]): number {
  if (!distribution || distribution.length === 0) return 0;
  return distribution.reduce((sum, item) => sum + item.count, 0);
}
