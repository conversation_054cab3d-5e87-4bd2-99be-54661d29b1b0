/**
 * Componente para exibição do ranking de usuários no sistema de gamificação.
 * Mostra os usuários com maior XP, suas posições, níveis e pontuações.
 * <AUTHOR> Internet 2025
 */
import { useMemo, useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { useUserRanking } from "@/lib/query/hooks/useUserRanking";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
import { Trophy, User, Medal } from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";
import { useAuthStore } from '@/stores/authStore';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { AdvancedRefreshButtonIcon } from "@/components/ui/advanced-refresh-button";

interface UserRankingItem {
  user_id: string;
  full_name: string | null;
  avatar_url: string | null;
  current_level: number;
  current_xp: number;
  total_xp: number;
  rank: number;
  position?: number;
}

interface UserRankingData {
  ranking: UserRankingItem[];
  userPosition: number;
}

const getMedalColor = (position?: number) => {
  if (!position) return "text-muted-foreground";
  switch (position) {
    case 1:
      return "text-yellow-500";
    case 2:
      return "text-gray-400";
    case 3:
      return "text-amber-700";
    default:
      return "text-muted-foreground";
  }
};

export function UserRankingCard() {
  const queryClient = useQueryClient();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const user = useAuthStore((state) => state.user);
  
  // Buscar dados reais do ranking de usuários
  const { data: rankingData, isLoading, error, refetch } = useUserRanking({ limit: 10 });
  
  // Logar erros se ocorrerem
  useMemo(() => {
    if (error) {
      logQueryEvent("UserRankingCard", "Erro ao carregar dados de ranking de usuários", error, "error");
    }
  }, [error]);
  
  // Função para limpar o cache e buscar novamente os dados
  const handleRefresh = async () => {
    try {
      logQueryEvent("UserRankingCard", "Atualizando dados de ranking de usuários manualmente");
      
      // Invalidar o cache para a chave de ranking (baseado no QueryKeys)
      await queryClient.invalidateQueries({ 
        queryKey: ["gamification", "ranking"],
        exact: false // Invalida todas as variações da chave de ranking
      });
      
      // Buscar novamente os dados
      await refetch();
      
      logQueryEvent("UserRankingCard", "Dados de ranking de usuários atualizados com sucesso");
    } catch (error) {
      logQueryEvent("UserRankingCard", "Erro ao atualizar dados de ranking de usuários", error, "error");
      throw error; // Re-throw para que o AdvancedRefreshButton capture o erro
    }
  };

  // Processar os dados do ranking
  const { ranking, userPosition } = useMemo<UserRankingData>(() => {
    if (!rankingData) return { ranking: [], userPosition: 0 };

    const rankingWithPosition: UserRankingItem[] = rankingData.map((user, index) => ({
      ...user,
      position: index + 1
    }));

    const userPos = rankingWithPosition.findIndex(u => u.user_id === user?.id) + 1;

    return {
      ranking: rankingWithPosition,
      userPosition: userPos || 0
    };
  }, [rankingData, user?.id]);

  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">
            <Skeleton className="h-6 w-3/4" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center gap-4">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
                <Skeleton className="h-6 w-10" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }
  
  if (error) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Ranking de Usuários</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-destructive p-4 rounded-lg bg-destructive/10">
            <p>Não foi possível carregar os dados do ranking. Tente novamente mais tarde.</p>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  if (ranking.length === 0) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Ranking de Usuários</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground p-4">
            <p>Nenhum dado de ranking disponível.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg flex items-center gap-2">
              <Trophy className="h-5 w-5 text-yellow-500" />
              Ranking de Usuários
            </CardTitle>
            <p className="text-xs text-muted-foreground">Os usuários com maior pontuação na plataforma</p>
          </div>
          <AdvancedRefreshButtonIcon
            onRefresh={handleRefresh}
            operationName="Ranking"
            successMessage="Ranking atualizado!"
            successDescription="Os dados do ranking foram atualizados com sucesso"
            errorMessage="Erro ao atualizar ranking"
            errorDescription="Não foi possível atualizar os dados do ranking"
            title="Atualizar dados do ranking"
            className="h-8 w-8 flex-shrink-0"
            refreshingText=""
          />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {ranking.map((userRank) => (
            <div 
              key={userRank.user_id} 
              className={cn(
                "flex items-center gap-3 p-3 rounded-lg transition-colors",
                userRank.user_id === user?.id 
                  ? "bg-primary/10 border border-primary/20" 
                  : "bg-muted/50 hover:bg-muted/70"
              )}
            >
              <div className="flex-shrink-0 w-8 flex justify-center">
                {userRank.position <= 3 ? (
                  <Medal className={cn("h-6 w-6", getMedalColor(userRank.position))} />
                ) : (
                  <span className="text-sm font-medium text-muted-foreground">
                    {userRank.position}
                  </span>
                )}
              </div>
              
              <div className="flex items-center gap-3 flex-1 min-w-0">
                <Avatar className="h-9 w-9 border">
                  <AvatarImage src={userRank.avatar_url || undefined} alt={userRank.full_name || ''} />
                  <AvatarFallback>
                    <User className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
                
                <div className="min-w-0 flex-1">
                  <p className="font-medium truncate">
                    {userRank.full_name || 'Usuário sem nome'}
                    {userRank.user_id === user?.id && (
                      <span className="ml-2 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                        Você
                      </span>
                    )}
                  </p>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <span>Nível {userRank.current_level}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {userPosition > 10 && (
          <div className="text-center text-sm text-muted-foreground mt-4 py-2 bg-muted/30 rounded-lg">
            <p>Sua posição: {userPosition}º lugar</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}