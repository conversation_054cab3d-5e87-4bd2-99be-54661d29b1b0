/**
 * Componente que exibe a lista de conversas de chat do usuário e permite iniciar novas conversas.
 * <AUTHOR> Internet 2025
 */
import { useEffect, useState, useCallback, useMemo } from "react";
import { supabase } from "@/integrations/supabase/client";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useNavigate, useParams } from "react-router-dom";
import { NewChatDialog } from "./NewChatDialog";
import { ChatPreview, ChatPreviewProps } from "./ChatPreview";
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { LoadingState } from "@/components/ui/loading-state";
import { Search, X, MessageSquare, Archive, ArchiveRestore, MoreVertical } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useArchiveChat } from "@/hooks/useArchiveChat";
import { useQueryClient } from "@tanstack/react-query";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Interface para Chat 1-1
interface Chat {
  id: string;
  participants: {
    user_id: string;
    archived?: boolean;
    archived_at?: string;
    profiles: {
      full_name: string | null;
      avatar_url: string | null;
    };
  }[];
  last_message?: {
    id: string;
    content: string;
    created_at: string;
  };
}

// ChatList deve mostrar apenas conversas 1-1, não canais
type ChatItem = Chat;

// Variantes para animações de chat
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
};

const chatItemVariants = {
  hidden: { opacity: 0, x: -20, scale: 0.95 },
  visible: {
    opacity: 1, 
    x: 0,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  },
  exit: {
    opacity: 0,
    x: 20,
    scale: 0.95,
    transition: {
      duration: 0.2
    }
  }
};

export function ChatList() {
  const [chatItems, setChatItems] = useState<ChatItem[]>([]);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<"activity" | "name">("activity");
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [chatsWithMessageMatches, setChatsWithMessageMatches] = useState<Set<string>>(new Set());
  const [messageMatches, setMessageMatches] = useState<Map<string, { messageId: string; content: string; created_at: string }>>(new Map());
  const [isSearchingMessages, setIsSearchingMessages] = useState(false);
  const [activeTab, setActiveTab] = useState<"active" | "archived">("active");
  
  const navigate = useNavigate();
  const { chatId: currentChatId } = useParams();
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const { toggleChatArchive, isToggling, archivedCounts, refreshArchivedCounts } = useArchiveChat(queryClient);

  useEffect(() => {
    const fetchUser = async () => {
      const { data: userData } = await supabase.auth.getUser();
      if (userData.user) {
        setCurrentUserId(userData.user.id);
      }
    };
    fetchUser();
  }, []);

  // Filtrar e ordenar chats
  const filteredAndSortedChats = useMemo(() => {
    let filtered = chatItems;

    // Filtrar por aba (ativa/arquivada)
    filtered = filtered.filter(chat => {
      const currentUserParticipant = chat.participants.find(p => p.user_id === currentUserId);
      const isArchived = currentUserParticipant?.archived || false;
      return activeTab === "archived" ? isArchived : !isArchived;
    });

    // Filtrar por busca
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(chat => {
        const otherParticipant = chat.participants.find((p) => p.user_id !== currentUserId);
        return (
          // Buscar no nome do participante
          otherParticipant?.profiles.full_name?.toLowerCase().includes(searchLower) ||
          // Buscar na última mensagem visível
          chat.last_message?.content?.toLowerCase().includes(searchLower) ||
          // Buscar em mensagens do banco de dados
          chatsWithMessageMatches.has(chat.id)
        );
      });
    }

    // Ordenar
    return filtered.sort((a, b) => {
      switch (sortBy) {
        case "name": {
          const nameA = a.participants.find(p => p.user_id !== currentUserId)?.profiles.full_name || 'Usuário';
          const nameB = b.participants.find(p => p.user_id !== currentUserId)?.profiles.full_name || 'Usuário';
          return nameA.localeCompare(nameB);
        }
        case "activity":
        default: {
          const dateA = a.last_message?.created_at ? new Date(a.last_message.created_at).getTime() : 0;
          const dateB = b.last_message?.created_at ? new Date(b.last_message.created_at).getTime() : 0;
          return dateB - dateA;
        }
      }
    });
  }, [chatItems, searchTerm, sortBy, chatsWithMessageMatches, currentUserId, activeTab]);

  // Expandir automaticamente se houver busca ativa
  useEffect(() => {
    if (searchTerm && !isSearchExpanded) {
      setIsSearchExpanded(true);
    }
  }, [searchTerm, isSearchExpanded]);

  // Busca em mensagens quando há termo de busca
  useEffect(() => {
    if (!searchTerm || !currentUserId) {
      setChatsWithMessageMatches(new Set());
      setMessageMatches(new Map());
      setIsSearchingMessages(false);
      return;
    }

    const searchInMessages = async () => {
      setIsSearchingMessages(true);
      try {
        // Buscar mensagens que contêm o termo de busca em chats (não canais)
        const { data: messages } = await supabase
          .from('chat_messages')
          .select('id, chat_id, content, created_at')
          .ilike('content', `%${searchTerm}%`)
          .not('chat_id', 'is', null) // Apenas mensagens de chat (não de canal)
          .order('created_at', { ascending: false })
          .limit(100); // Limitar para performance

        if (messages) {
          const chatIds = new Set<string>();
          const matchMap = new Map<string, { messageId: string; content: string; created_at: string }>();
          
          // Para cada chat, guardar apenas a mensagem mais recente que contém o termo
          messages.forEach(msg => {
            if (msg.chat_id && !matchMap.has(msg.chat_id)) {
              chatIds.add(msg.chat_id);
              matchMap.set(msg.chat_id, {
                messageId: msg.id,
                content: msg.content,
                created_at: msg.created_at
              });
            }
          });
          
          setChatsWithMessageMatches(chatIds);
          setMessageMatches(matchMap);
        }
      } catch (error) {
        console.error('Erro ao buscar mensagens:', error);
        setChatsWithMessageMatches(new Set());
        setMessageMatches(new Map());
      } finally {
        setIsSearchingMessages(false);
      }
    };

    // Debounce: aguardar 300ms antes de buscar
    const timeoutId = setTimeout(searchInMessages, 300);
    return () => clearTimeout(timeoutId);
  }, [searchTerm, currentUserId]);

  const fetchChatData = useCallback(async (forceRefresh = false) => {
    if (!currentUserId) return;

    // Se já carregou uma vez e não é force refresh, não recarregar
    if (hasLoadedOnce && !forceRefresh) {
      logQueryEvent('ChatList', 'Using cached data, skipping fetch');
      return;
    }

    // Se é primeira carga, mostrar loading
    if (!hasLoadedOnce) {
      setIsInitialLoading(true);
    }

    try {
      // Primeiro, buscar todos os chats onde o usuário é participante
      const { data: userChats, error: userChatsError } = await supabase
        .from('chat_participants')
        .select('chat_id')
        .eq('user_id', currentUserId);

      if (userChatsError) {
        logQueryEvent('ChatList', 'Error fetching user chats:', userChatsError, 'error');
        throw userChatsError;
      }

      if (!userChats || userChats.length === 0) {
        logQueryEvent('ChatList', 'No chats found for user');
        setChatItems([]);
        return;
      }

      const chatIds = userChats.map(uc => uc.chat_id);
      logQueryEvent('ChatList', 'Found chat IDs for user:', { chatIds, count: chatIds.length });

      // Buscar detalhes dos chats com participantes e última mensagem (left join)
      const { data: chatsData, error: chatsError } = await supabase
        .from("chats")
        .select(`
          id,
          created_at,
          participants:chat_participants(
            user_id,
            archived,
            archived_at,
            profiles(full_name, avatar_url)
          ),
          last_message:chat_messages(
            id,
            content,
            created_at
          )
        `)
        .in('id', chatIds)
        .order("created_at", { foreignTable: "chat_messages", ascending: false })
        .limit(1, { foreignTable: "chat_messages" });

      if (chatsError) {
        logQueryEvent('ChatList', 'Error fetching chat details:', chatsError, 'error');
        throw chatsError;
      }

      logQueryEvent('ChatList', 'Raw chats data:', { chatsData, count: chatsData?.length });

      const formattedChats: ChatItem[] = (chatsData || [])
        .filter(chat => {
          // Filtrar apenas chats 1-1 (exatamente 2 participantes)
          const isValidChat = chat.participants && chat.participants.length === 2;
          if (!isValidChat) {
            logQueryEvent('ChatList', 'Filtering out chat (not 1-1):', { 
              chatId: chat.id, 
              participantCount: chat.participants?.length 
            });
          }
          return isValidChat;
        })
        .map((chat): ChatItem => ({
          id: chat.id,
          participants: chat.participants,
          last_message: chat.last_message?.[0],
        }));

      // Ordenar chats por data da última mensagem (ou criação se não há mensagens)
      formattedChats.sort((a, b) => {
        const dateA = a.last_message?.created_at ? 
          new Date(a.last_message.created_at).getTime() : 
          new Date(chatsData?.find(c => c.id === a.id)?.created_at || 0).getTime();
        const dateB = b.last_message?.created_at ? 
          new Date(b.last_message.created_at).getTime() : 
          new Date(chatsData?.find(c => c.id === b.id)?.created_at || 0).getTime();
        return dateB - dateA;
      });

      setChatItems(formattedChats);
      setHasLoadedOnce(true);
      setIsInitialLoading(false);
      logQueryEvent('ChatList', 'Chats fetched successfully', { 
        count: formattedChats.length,
        chats: formattedChats.map(c => ({ id: c.id, hasMessage: !!c.last_message }))
      });
    } catch (error) {
      logQueryEvent('ChatList', 'General error fetching chat data:', error, 'error');
      setIsInitialLoading(false);
    }
  }, [currentUserId, hasLoadedOnce]);

  useEffect(() => {
    if (currentUserId) {
      fetchChatData();
      refreshArchivedCounts(currentUserId);
    }

    // 🚀 REMOVIDO: Canal individual substituído pelo UnifiedRealtimeProvider

    // 🚀 REFATORADO: Usar eventos do UnifiedRealtimeProvider ao invés de canal próprio
    const handleChatMessageEvent = (event: CustomEvent) => {
      const { message } = event.detail;
      logQueryEvent('ChatList', '🎯 Nova mensagem via UnifiedRealtimeProvider', { 
        messageId: message.id,
        chatId: message.chat_id,
        channelId: message.channel_id
      });
      if (currentUserId) fetchChatData(true);
    };

    const handleChatParticipantEvent = (event: CustomEvent) => {
      logQueryEvent('ChatList', '🎯 Novo participante via UnifiedRealtimeProvider');
      if (currentUserId) fetchChatData(true);
    };

    // Escutar eventos do UnifiedRealtimeProvider
    window.addEventListener('vindula-chat-message-insert', handleChatMessageEvent as EventListener);
    window.addEventListener('vindula-chat-participant-changed', handleChatParticipantEvent as EventListener);

    return () => {
      window.removeEventListener('vindula-chat-message-insert', handleChatMessageEvent as EventListener);
      window.removeEventListener('vindula-chat-participant-changed', handleChatParticipantEvent as EventListener);
    };
  }, [fetchChatData, currentUserId]);

  const handleStartChat = async (userId: string): Promise<void> => {
    logQueryEvent('ChatList', 'Attempting to start 1-1 chat with user:', userId);
    try {
      if (!currentUserId) {
        logQueryEvent('ChatList', 'Cannot start chat without current user ID.', null, 'error');
        return;
      };

      // @ts-expect-error - RPC function 'find_chat_between_users' not typed yet
      const { data: existingChatId, error: existingError } = await supabase.rpc('find_chat_between_users', { 
        user1_id: currentUserId, 
        user2_id: userId 
      });

      if (existingError) {
        logQueryEvent('ChatList', 'Error checking existing chat via RPC:', existingError, 'error');
        throw existingError;
      }

      if (existingChatId) {
        logQueryEvent('ChatList', 'Existing 1-1 chat found via RPC:', existingChatId);
        navigate(`/chat/${existingChatId}`);
        return;
      }

      logQueryEvent('ChatList', 'No existing 1-1 chat found, creating new one...');
      
      // Usar nova função auxiliar que inclui company_id corretamente
      const { data: newChatResult, error: createError } = await supabase.rpc('create_direct_chat_between_users', {
        p_user1_id: currentUserId,
        p_user2_id: userId
      });

      if (createError) {
        logQueryEvent('ChatList', 'Error creating new chat via RPC:', createError, 'error');
        throw createError;
      }

      if (!newChatResult?.success) {
        const errorMsg = newChatResult?.message || 'Erro desconhecido ao criar chat';
        logQueryEvent('ChatList', 'Chat creation failed:', { result: newChatResult }, 'error');
        throw new Error(errorMsg);
      }

      const newChatId = newChatResult.chat_id;
      logQueryEvent('ChatList', 'New chat created successfully via RPC:', newChatId);

      // Forçar refresh da lista para mostrar a nova conversa
      await fetchChatData(true);
      
      navigate(`/chat/${newChatId}`);
    } catch (error) {
      logQueryEvent('ChatList', 'Error in handleStartChat:', error, 'error');
    }
  };

  const handleArchiveChat = async (chatId: string, archive: boolean = true) => {
    if (!currentUserId) return;
    
    const success = await toggleChatArchive(chatId, currentUserId, archive);
    if (success) {
      await fetchChatData(true); // Refresh list
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header com Busca */}
      <div className="p-3 border-b space-y-2 shrink-0">
        
        {/* Abas Ativo/Arquivado */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "active" | "archived")}>
          <TabsList className="w-full grid grid-cols-2 h-9">
            <TabsTrigger value="active" className="text-sm">
              Ativas
            </TabsTrigger>
            <TabsTrigger value="archived" className="text-sm flex items-center gap-1">
              <Archive className="h-3 w-3" />
              Arquivadas
              {archivedCounts && archivedCounts.archived_chats > 0 && (
                <Badge variant="secondary" className="h-4 text-xs">
                  {archivedCounts.archived_chats}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>
        </Tabs>
        
        {/* Botão de busca */}
        <div className="flex items-center gap-2">
          {!isSearchExpanded ? (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsSearchExpanded(true)}
              className="h-9 flex-1 justify-start gap-2"
              title={searchTerm ? `Busca ativa: "${searchTerm}"` : "Buscar conversas e mensagens"}
            >
              <Search className="h-4 w-4" />
              {searchTerm ? `"${searchTerm}"` : "Buscar conversas..."}
            </Button>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setIsSearchExpanded(false);
                setSearchTerm("");
                setSortBy("activity");
              }}
              className="h-9 w-9 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Painel de busca expandido */}
      {isSearchExpanded && (
        <div className="p-3 space-y-3 border-b shrink-0 bg-muted/20">
          {/* Busca */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar conversas e mensagens..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-9"
              autoFocus
            />
          </div>

          {/* Filtros avançados - só aparecem se houver busca ativa */}
          {searchTerm && (
            <div className="flex gap-2">
              <Select value={sortBy} onValueChange={(value: "activity" | "name") => setSortBy(value)}>
                <SelectTrigger className="flex-1 h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="activity">Última mensagem</SelectItem>
                  <SelectItem value="name">Nome A-Z</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Contadores - só quando há busca ativa */}
          {searchTerm && (
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>
                {filteredAndSortedChats.length} conversa(s)
                {messageMatches.size > 0 && (
                  <span className="ml-2 text-primary/70">
                    • {messageMatches.size} com mensagens
                  </span>
                )}
                {isSearchingMessages && (
                  <span className="ml-2 animate-pulse">🔍 buscando...</span>
                )}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Indicador de total de conversas quando não há busca */}
      {!isSearchExpanded && !searchTerm && chatItems.length > 0 && (
        <div className="px-3 py-1 bg-muted/20 border-b shrink-0">
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{chatItems.length} conversa(s) disponível(is)</span>
          </div>
        </div>
      )}

      {/* Lista de conversas */}
      <ScrollArea className="flex-1">
        <motion.div 
          className="space-y-1 p-2"
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          {isInitialLoading ? (
            <LoadingState 
              title="Carregando conversas..."
              description="Buscando suas mensagens privadas"
            />
          ) : filteredAndSortedChats.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <MessageSquare className="h-12 w-12 text-muted-foreground/50 mb-3" />
              <p className="text-sm text-muted-foreground mb-2">
                {searchTerm 
                  ? `Nenhuma conversa encontrada para "${searchTerm}"` 
                  : "Nenhuma conversa ainda"
                }
              </p>
              {searchTerm ? (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => {
                    setSearchTerm("");
                    setSortBy("activity");
                  }}
                >
                  Limpar busca
                </Button>
              ) : (
                <p className="text-xs text-muted-foreground">
                  {currentUserId ? 
                    "Use o botão acima para iniciar uma nova conversa" :
                    "Faça login para começar a conversar"
                  }
                </p>
              )}
            </div>
          ) : (
            <AnimatePresence mode="popLayout">
              {filteredAndSortedChats.map((chat) => {
              const otherParticipant = chat.participants.find((p) => p.user_id !== currentUserId);
              if (!otherParticipant) {
                logQueryEvent('ChatList', 'Skipping chat render, other participant not found yet.', { chatId: chat.id });
                return null;
              }

              // Se há uma mensagem correspondente na busca, usar ela ao invés da última mensagem
              const messageMatch = messageMatches.get(chat.id);
              const displayMessage = searchTerm && messageMatch ? {
                id: messageMatch.messageId,
                content: messageMatch.content,
                created_at: messageMatch.created_at
              } : chat.last_message;

              const currentUserParticipant = chat.participants.find(p => p.user_id === currentUserId);
              const isArchived = currentUserParticipant?.archived || false;

              const previewProps: Omit<ChatPreviewProps, 'onClick' | 'isActive'> = {
                id: chat.id,
                lastMessage: displayMessage,
                name: otherParticipant.profiles.full_name || 'Usuário',
                avatarUrl: otherParticipant.profiles.avatar_url || undefined,
                memberCount: undefined,
                type: 'chat',
                isNew: !chat.last_message && !messageMatch, // Nova conversa se não tem mensagem nem match de busca
              };

              const handleChatClick = () => {
                if (searchTerm && messageMatch) {
                  // Se há uma busca ativa e mensagem correspondente, navegar direto para a mensagem
                  navigate(`/chat/${chat.id}?highlight=${messageMatch.messageId}&search=${encodeURIComponent(searchTerm)}`);
                } else {
                  // Navegação normal
                  navigate(`/chat/${chat.id}`);
                }
              };

              return (
                <motion.div
                  key={`chat-${chat.id}`}
                  variants={chatItemVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  layout
                  className="group relative"
                >
                  <div className="relative group">
                    <ChatPreview
                      key={`chat-${chat.id}`}
                      {...previewProps}
                      onClick={handleChatClick}
                      isActive={chat.id === currentChatId}
                    />
                    
                    {/* Menu de ações - sempre visível em mobile, hover em desktop */}
                    <div className="absolute right-2 top-1/2 -translate-y-1/2 md:opacity-0 md:group-hover:opacity-100 transition-opacity">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-8 w-8 p-0 bg-background/80 hover:bg-background shadow-sm border"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleArchiveChat(chat.id, !isArchived);
                            }}
                            disabled={isToggling}
                          >
                            {isArchived ? (
                              <>
                                <ArchiveRestore className="mr-2 h-4 w-4" />
                                Desarquivar
                              </>
                            ) : (
                              <>
                                <Archive className="mr-2 h-4 w-4" />
                                Arquivar
                              </>
                            )}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </motion.div>
              );
              })}
            </AnimatePresence>
          )}
        </motion.div>
      </ScrollArea>
    </div>
  );
}