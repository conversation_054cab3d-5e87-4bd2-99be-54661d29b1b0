import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { TypingStatus } from "@/types/chat.types";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";

interface TypingIndicatorProps {
  chatId?: string;
  channelId?: string;
  currentUserId: string;
}

interface TypingStatusPayload {
  new: {
    id: string;
    user_id: string;
    chat_id?: string;
    channel_id?: string;
    last_typed_at: string;
  };
  old?: {
    id: string;
    user_id: string;
  };
  eventType: "INSERT" | "UPDATE" | "DELETE";
}

export function TypingIndicator({ chatId, channelId, currentUserId }: TypingIndicatorProps) {
  const [typingUsers, setTypingUsers] = useState<TypingStatus[]>([]);

  useEffect(() => {
    // Fetch initial typing status
    const fetchTypingStatus = async () => {
      try {
        logQueryEvent('TypingIndicator', 'Buscando status inicial de digitação', { chatId, channelId, currentUserId });
        
        const { data, error } = await supabase
          .from("typing_status")
          .select(`
            id,
            user_id,
            chat_id,
            channel_id,
            last_typed_at,
            user:profiles(
              id,
              full_name,
              avatar_url
            )
          `)
          .eq(chatId ? "chat_id" : "channel_id", chatId || channelId)
          .neq("user_id", currentUserId);

        if (error) {
          logQueryEvent('TypingIndicator', 'Erro ao buscar status inicial de digitação', { error, chatId, channelId }, 'error');
          return;
        }

        setTypingUsers(data || []);
      } catch (error) {
        logQueryEvent('TypingIndicator', 'Erro inesperado ao buscar status de digitação', { error }, 'error');
      }
    };

    fetchTypingStatus();

    // 🚀 REFATORADO: Usar eventos do UnifiedRealtimeProvider ao invés de canal próprio
    const handleTypingStatusEvent = async (event: CustomEvent) => {
      try {
        const { typingStatus, eventType } = event.detail;
        
        logQueryEvent('TypingIndicator', '🎯 Evento via UnifiedRealtimeProvider', { 
          eventType, 
          typingStatus, 
          currentUserId 
        });

        if (eventType === "INSERT" || eventType === "UPDATE") {
          // Verificar se o evento é do usuário atual (ignorar se for)
          if (typingStatus.user_id === currentUserId) {
            logQueryEvent('TypingIndicator', 'Ignorando evento do usuário atual');
            return;
          }

          // Verificar se o evento é do chat/canal correto
          if (chatId && typingStatus.chat_id !== chatId) {
            logQueryEvent('TypingIndicator', 'Evento de chat diferente, ignorando');
            return;
          }
          
          if (channelId && typingStatus.channel_id !== channelId) {
            logQueryEvent('TypingIndicator', 'Evento de canal diferente, ignorando');
            return;
          }

          // Buscar dados completos do usuário
          const { data, error } = await supabase
            .from("typing_status")
            .select(`
              id,
              user_id,
              chat_id,
              channel_id,
              last_typed_at,
              user:profiles(
                id,
                full_name,
                avatar_url
              )
            `)
            .eq("id", typingStatus.id)
            .maybeSingle();

          if (error) {
            logQueryEvent('TypingIndicator', 'Erro ao buscar dados do usuário digitando', { error, typingId: typingStatus.id }, 'error');
            return;
          }

          if (!data) {
            logQueryEvent('TypingIndicator', 'Registro de typing status não encontrado', { typingId: typingStatus.id }, 'warn');
            return;
          }

          logQueryEvent('TypingIndicator', 'Adicionando usuário à lista de digitando', { user: data.user?.full_name });
          setTypingUsers((prev) => {
            const filtered = prev.filter((u) => u.id !== data.id);
            return [...filtered, data];
          });
          
        } else if (eventType === "DELETE") {
          logQueryEvent('TypingIndicator', 'Removendo usuário da lista de digitando', { typingId: typingStatus.id });
          setTypingUsers((prev) =>
            prev.filter((u) => u.id !== typingStatus.id)
          );
        }
      } catch (error) {
        logQueryEvent('TypingIndicator', 'Erro ao processar evento de typing status', { error, event }, 'error');
      }
    };

    // Escutar eventos do UnifiedRealtimeProvider
    window.addEventListener('vindula-typing-status-changed', handleTypingStatusEvent as EventListener);

    return () => {
      window.removeEventListener('vindula-typing-status-changed', handleTypingStatusEvent as EventListener);
    };
  }, [chatId, channelId, currentUserId]);

  // Remove typing status after 3 seconds of inactivity
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      setTypingUsers((prev) =>
        prev.filter((user) => {
          const lastTyped = new Date(user.last_typed_at);
          return now.getTime() - lastTyped.getTime() < 3000;
        })
      );
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  if (typingUsers.length === 0) return null;

  return (
    <div className="px-4 py-1 text-sm text-muted-foreground">
      {typingUsers.length === 1 && (
        <span>{typingUsers[0].user?.full_name} está digitando...</span>
      )}
      {typingUsers.length === 2 && (
        <span>
          {typingUsers[0].user?.full_name} e {typingUsers[1].user?.full_name} estão
          digitando...
        </span>
      )}
      {typingUsers.length > 2 && (
        <span>
          {typingUsers[0].user?.full_name}, {typingUsers[1].user?.full_name} e{" "}
          {typingUsers.length - 2} outros estão digitando...
        </span>
      )}
    </div>
  );
} 