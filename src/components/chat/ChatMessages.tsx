/**
 * Componente para exibição de mensagens de chat e canais com funcionalidades de reações e threads.
 * <AUTHOR> Internet 2025
 */
import { useEffect, useRef, useState, useCallback, useMemo } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { MessageSquare, Smile, RefreshCw, ArrowDown } from "lucide-react";
import { ChatMessage } from "@/types/chat.types";
import { EmojiPicker } from "@/components/ui/emoji-picker";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { SmartUpgradeButton } from "@/components/ui/PlanUpgradeButton";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
import { useChatMessages } from "@/lib/query/hooks/useChatMessages";
import { useChatMessagesPaginated } from "@/lib/query/hooks/useChatMessagesPaginated";
import { useMessageReactions } from "@/lib/query/hooks/useMessageReactions";
import { useChatRealtime } from "@/lib/query/hooks/useChatRealtime";
import { useChannelAutoJoin } from "@/lib/query/hooks/useChannelAutoJoin";
import { useMarkChannelAsRead } from "@/lib/query/hooks/useMarkChannelAsRead";
import { useMarkMessagesAsRead, useMessageReadReceiptsRealtime } from "@/lib/query/hooks/useMessageReadReceipts";
import { useQueryClient } from "@tanstack/react-query";
import { QueryKeys } from "@/lib/query/hooks/useChatMessages";
import { PaginatedQueryKeys } from "@/lib/query/hooks/useChatMessagesPaginated";
import ChatMessageItem from "./ChatMessageItem";
import { useReplyToMessage } from "@/hooks/useReplyToMessage";

interface ChatMessagesProps {
  chatId?: string;
  channelId?: string;
  currentUserId: string;
  onThreadSelect?: (message: ChatMessage) => void;
  messages?: ChatMessage[];
  hideSeeAllHistoryLink?: boolean;
  usePagination?: boolean;
  highlightMessageId?: string | null;
  searchTerm?: string | null;
  isCompact?: boolean; // Para floating chat
  onReplySelect?: (message: ChatMessage, replyType: "same" | "private") => void;
}

export function ChatMessages({
  chatId,
  channelId,
  currentUserId,
  onThreadSelect,
  messages: initialMessages,
  hideSeeAllHistoryLink = false,
  usePagination = true,
  highlightMessageId,
  searchTerm,
  isCompact = false,
  onReplySelect,
}: ChatMessagesProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState<string | null>(null);
  const [previousMessageCount, setPreviousMessageCount] = useState(0);
  const [previousScrollHeight, setPreviousScrollHeight] = useState(0);
  const [hasScrolledToHighlight, setHasScrolledToHighlight] = useState(false);

  // Garantir que temos valores válidos para os IDs
  const validChatId = chatId || undefined; // Converter null para undefined
  const validChannelId = channelId || undefined; // Converter null para undefined

  // Hook do QueryClient para invalidar queries
  const queryClient = useQueryClient();

  // Hook para marcar mensagens como lidas (Read Receipts - Issue #13)
  const markMessagesAsRead = useMarkMessagesAsRead();
  const [visibleMessages, setVisibleMessages] = useState<Set<string>>(new Set());
  const [markedAsRead, setMarkedAsRead] = useState<Set<string>>(new Set());


  // Usar hook paginado ou normal baseado na prop usePagination (memoizado para performance)
  const shouldUsePagination = useMemo(() => usePagination && !initialMessages, [usePagination, initialMessages]);
  const shouldUseNormal = useMemo(() => !usePagination && !initialMessages, [usePagination, initialMessages]);

  const paginatedResult = useChatMessagesPaginated(
    shouldUsePagination ? validChatId : undefined,
    shouldUsePagination ? validChannelId : undefined,
    {
      enabled: shouldUsePagination,
    }
  );

  const normalResult = useChatMessages(
    shouldUseNormal ? validChatId : undefined,
    shouldUseNormal ? validChannelId : undefined,
    {
      enabled: shouldUseNormal,
    }
  );

  // Determinar qual resultado usar baseado na configuração (memoizado para performance)
  const isLoadingMessages = useMemo(() => 
    usePagination ? paginatedResult.isLoading : normalResult.isLoading, 
    [usePagination, paginatedResult.isLoading, normalResult.isLoading]
  );
  
  const messagesError = useMemo(() => 
    usePagination ? paginatedResult.error : normalResult.error, 
    [usePagination, paginatedResult.error, normalResult.error]
  );
  
  const refetchMessages = useMemo(() => 
    usePagination ? paginatedResult.refetch : normalResult.refetch, 
    [usePagination, paginatedResult.refetch, normalResult.refetch]
  );
  
  const isRefetching = useMemo(() => 
    usePagination ? paginatedResult.isRefetching : normalResult.isRefetching, 
    [usePagination, paginatedResult.isRefetching, normalResult.isRefetching]
  );

  // Usar mensagens iniciais se fornecidas, ou as mensagens do hook apropriado (memoizado para performance)
  const messages = useMemo(() => 
    initialMessages ||
    (usePagination
      ? paginatedResult.messages
      : normalResult.data?.messages || []), 
    [initialMessages, usePagination, paginatedResult.messages, normalResult.data?.messages]
  );
  
  const hasMoreMessages = useMemo(() => 
    initialMessages
      ? false
      : usePagination
      ? paginatedResult.hasMoreMessages
      : normalResult.data?.hasMoreMessages || false, 
    [initialMessages, usePagination, paginatedResult.hasMoreMessages, normalResult.data?.hasMoreMessages]
  );


  // Forçar refetch quando o componente montar
  useEffect(() => {
    if (!initialMessages && (chatId || channelId)) {
      refetchMessages();
    }
  }, [initialMessages, chatId, channelId, refetchMessages]);



  // Função para rolar até o final da conversa
  const scrollToBottom = useCallback(() => {
    if (messagesContainerRef.current) {
      const container = messagesContainerRef.current;
      container.scrollTop = container.scrollHeight;
    } else if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messagesContainerRef, messagesEndRef]);

  // Manter fetchMessages temporariamente para compatibilidade com o código existente
  // Será removido quando todos os hooks estiverem integrados
  const fetchMessages = useCallback(async () => {
    // Não faz nada, pois estamos usando useChatMessages
    // Será removido quando todos os hooks estiverem integrados
  }, []);

  // Usar o hook useMessageReactions para gerenciar reações
  const { mutate: toggleReaction, isPending: isTogglingReaction } =
    useMessageReactions();

  // Função para lidar com reações (agora usando o hook)
  const handleReaction = useCallback(
    (messageId: string, emoji: string) => {
      toggleReaction(
        { messageId, emoji, userId: currentUserId },
        {
          onSuccess: () => {
            // Fechar o emoji picker após adicionar/remover reação
            setShowEmojiPicker(null);
            // Não precisamos mais chamar fetchMessages() aqui, pois o Realtime cuidará da atualização
          },
          onError: (error) => {
            logQueryEvent(
              "ChatMessages:handleReaction",
              "Erro ao processar reação",
              { messageId, emoji, error },
              "error"
            );
          },
        }
      );
    },
    [toggleReaction, currentUserId]
  );

  // Este useEffect não é mais necessário, pois useChatMessages já cuida disso
  // Mantido como comentário para referência
  /*
  useEffect(() => {
    if (initialMessages) {
      setMessages(initialMessages);
      return;
    }
    fetchMessages();
  }, [initialMessages, fetchMessages]);
  */

  // Efeito para manter a posição do scroll quando carregamos mais mensagens antigas
  useEffect(() => {
    if (messages.length > 0 && messagesContainerRef.current) {
      const container = messagesContainerRef.current;

      // Se o número de mensagens aumentou (carregamos mais mensagens antigas)
      if (messages.length > previousMessageCount && previousMessageCount > 0) {
        // Calcular o quanto o conteúdo cresceu
        const newScrollHeight = container.scrollHeight;
        const heightDifference = newScrollHeight - previousScrollHeight;

        // Ajustar o scroll para manter a posição visual
        if (heightDifference > 0) {
          container.scrollTop = container.scrollTop + heightDifference;
        }
      } else if (previousMessageCount === 0) {
        // Primeira carga - rolar para o final
        setTimeout(scrollToBottom, 50);
        setTimeout(scrollToBottom, 300);
      }

      // Atualizar os valores de referência
      setPreviousMessageCount(messages.length);
      setPreviousScrollHeight(container.scrollHeight);
    }
  }, [messages, scrollToBottom, previousMessageCount, previousScrollHeight]);

  // Efeito para forçar refetch quando o usuário ficar visivel (troca de abas)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && !initialMessages) {
        refetchMessages();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [initialMessages, refetchMessages]);

  // Auto-marcar mensagens como lidas quando ficam visíveis (Read Receipts - Issue #13)
  useEffect(() => {
    if (!messagesContainerRef.current || messages.length === 0) return;

    const container = messagesContainerRef.current;
    const currentMessageIds = new Set(messages.map(m => m.id));
    
    // Limpar mensagens visíveis que não existem mais
    setVisibleMessages(prev => {
      const cleanedSet = new Set();
      prev.forEach(id => {
        if (currentMessageIds.has(id)) {
          cleanedSet.add(id);
        }
      });
      return cleanedSet;
    });
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const messageElement = entry.target as HTMLElement;
          const messageId = messageElement.dataset.messageId;
          
          if (messageId && currentMessageIds.has(messageId)) {
            if (entry.isIntersecting) {
              setVisibleMessages(prev => {
                const newSet = new Set(prev);
                newSet.add(messageId);
                return newSet;
              });
            } else {
              setVisibleMessages(prev => {
                const newSet = new Set(prev);
                newSet.delete(messageId);
                return newSet;
              });
            }
          }
        });
      },
      {
        root: container,
        rootMargin: '0px',
        threshold: 0.5, // Mensagem precisa estar 50% visível
      }
    );

    // Observar todas as mensagens
    const messageElements = container.querySelectorAll('[data-message-id]');
    messageElements.forEach((element) => observer.observe(element));

    return () => {
      observer.disconnect();
    };
  }, [messages.length]); // Usar apenas length para evitar re-criação constante

  // Marcar mensagens visíveis como lidas após um delay
  useEffect(() => {
    if (visibleMessages.size === 0) return;


    const timer = setTimeout(() => {
      const messagesToMarkAsRead = Array.from(visibleMessages).filter((messageId) => {
        // Não marcar se já foi marcada
        if (markedAsRead.has(messageId)) {
          return false;
        }
        
        // Buscar mensagem no array atual
        const message = messages.find(m => m.id === messageId);
        
        if (!message) {
          return false;
        }
        
        // Não marcar se é própria mensagem
        if (message.sender?.id === currentUserId) {
          return false;
        }
        
        // ✅ CORREÇÃO: Não marcar mensagens de sistema como lidas
        if (message.message_type && ['system_join', 'system_leave', 'system_archive', 'system_unarchive'].includes(message.message_type)) {
          logQueryEvent("ChatMessages", "Mensagem de sistema ignorada no auto-read", {
            messageId,
            messageType: message.message_type,
            content: message.content?.substring(0, 50)
          });
          return false;
        }
        
        return true;
      });


      if (messagesToMarkAsRead.length > 0) {
        markMessagesAsRead.mutate(messagesToMarkAsRead, {
          onSuccess: () => {
            setMarkedAsRead(prev => {
              const newSet = new Set(prev);
              messagesToMarkAsRead.forEach(id => newSet.add(id));
              return newSet;
            });
          },
          onError: (error) => {
            console.error('❌ [ChatMessages] Erro ao marcar mensagens:', error);
            logQueryEvent("ChatMessages", "Erro ao auto-marcar mensagens como lidas", {
              error,
              messageIds: messagesToMarkAsRead
            }, "error");
          }
        });
      }
    }, 2000); // Aguardar 2 segundos antes de marcar como lida

    return () => clearTimeout(timer);
  }, [visibleMessages]); // Remover dependencies que podem causar loops

  // Configurar subscriptions em tempo real para read receipts (Issue #13)
  const messageIds = useMemo(() => messages.map(m => m.id), [messages.length]); // Usar length para evitar recriação constante
  useMessageReadReceiptsRealtime(messageIds);

  // Determinar a query key correta baseado no modo de paginação (memoizado para evitar re-renders)
  const currentQueryKey = useMemo(() => {
    return usePagination
      ? (validChatId
          ? PaginatedQueryKeys.chatMessages.byChat(validChatId)
          : validChannelId
          ? PaginatedQueryKeys.chatMessages.byChannel(validChannelId)
          : undefined)
      : (validChatId
          ? QueryKeys.chatMessages.byChat(validChatId)
          : validChannelId
          ? QueryKeys.chatMessages.byChannel(validChannelId)
          : undefined);
  }, [usePagination, validChatId, validChannelId]);

  // Usar o hook useChatRealtime para gerenciar subscrições Realtime
  useChatRealtime(validChatId, validChannelId, {
    customQueryKey: currentQueryKey,
    onNewMessage: useCallback(
      (message) => {
        // Só processar mensagens que não são respostas em threads
        if (!message.parent_id) {
          // Forçar invalidação de AMBAS as queries (normal e paginada) para garantir que as mensagens sejam atualizadas
          const queryKey = validChatId
            ? QueryKeys.chatMessages.byChat(validChatId)
            : validChannelId
            ? QueryKeys.chatMessages.byChannel(validChannelId)
            : null;

          const paginatedQueryKey = validChatId
            ? PaginatedQueryKeys.chatMessages.byChat(validChatId)
            : validChannelId
            ? PaginatedQueryKeys.chatMessages.byChannel(validChannelId)
            : null;

          if (queryKey) {
            queryClient.invalidateQueries({ queryKey });
          }

          if (paginatedQueryKey) {
            queryClient.invalidateQueries({ queryKey: paginatedQueryKey });
          }

          // Rolar para o final
          setTimeout(scrollToBottom, 100);
        }
      },
      [scrollToBottom, validChatId, validChannelId, queryClient]
    ),
    onMessageUpdate: useCallback((message) => {
      // O realtime já atualizou o cache, não precisamos fazer refetch
    }, []),
    onReactionUpdate: useCallback((messageId, updatedMessage) => {
      // O realtime já atualiza o cache diretamente no useChatRealtime.ts
      // Não precisamos invalidar queries aqui, pois isso causa re-renderizações desnecessárias
      // A atualização otimística em useMessageReactions.ts já garante resposta imediata
    }, []),
  });

  // Usar o hook useChannelAutoJoin para entrar automaticamente em canais públicos
  const { isSuccess: autoJoinSuccess } = useChannelAutoJoin(
    validChannelId,
    currentUserId,
    {
      onJoinSuccess: useCallback(() => {
        // Invalidar a query para buscar mensagens atualizadas
        // (incluindo a mensagem de sistema de entrada no canal)
        // Não precisamos mais fazer isso, pois o hook já cuida disso internamente
      }, []),
    }
  );

  // Filtrar mensagens válidas para o hook useMarkChannelAsRead
  const validMessages = messages.filter(
    (msg): msg is ChatMessage => msg && "id" in msg
  );

  // Usar o hook useMarkChannelAsRead para marcar o canal como lido
  const { scrollContainerRef } = useMarkChannelAsRead(
    validChannelId,
    validMessages,
    {
      markOnlyOnce: true,
      onSuccess: () => {
        // Canal marcado como lido
      },
    }
  );

  // Associar a referência do container de scroll do hook ao div principal
  useEffect(() => {
    if (
      messagesContainerRef.current &&
      scrollContainerRef.current !== messagesContainerRef.current
    ) {
      scrollContainerRef.current = messagesContainerRef.current;
    }
  }, [scrollContainerRef]);

  // Navegar até mensagem específica quando highlightMessageId estiver presente
  useEffect(() => {
    if (
      highlightMessageId &&
      validMessages.length > 0 &&
      !hasScrolledToHighlight
    ) {
      const highlightMessage = validMessages.find(
        (msg) => msg.id === highlightMessageId
      );

      if (highlightMessage) {
        setTimeout(() => {
          const messageElement = document.getElementById(
            `message-${highlightMessageId}`
          );
          if (messageElement && messagesContainerRef.current) {
            messageElement.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
            setHasScrolledToHighlight(true);
          }
        }, 500); // Aguardar renderização completa
      }
    }
  }, [highlightMessageId, validMessages, hasScrolledToHighlight, searchTerm]);

  // Reset do estado de scroll quando mudar de canal/chat
  useEffect(() => {
    setHasScrolledToHighlight(false);
  }, [chatId, channelId]);

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Barra superior com informações e ações */}
      <div className="flex items-center justify-between p-2 border-b">
        {/* Contador de mensagens removido */}
        {/* <div className="text-sm">
          {!isLoadingMessages && messages.length > 0 && (
            <span className="text-muted-foreground">
              Exibindo {messages.length} mensagens
            </span>
          )}
        </div> */}

        {/* Ações restantes (Ver histórico completo e Atualizar) - manter justify-end se for o caso */}
        <div className="flex items-center gap-2 ml-auto">
          {" "}
          {/* ml-auto para alinhar à direita se for o único filho */}
          {/* Botão 'Ir para o final' removido */}
          {/* <Button 
            variant="ghost" 
            size="sm" 
            className="gap-1" 
            onClick={scrollToBottom}
            title="Rolar para o final"
          >
            <ArrowDown className="h-4 w-4" />
            Ir para o final
          </Button> */}
          {!initialMessages && hasMoreMessages && !hideSeeAllHistoryLink && (
            <Link to={`/chat/${validChatId || validChannelId}`}>
              <Button
                variant="ghost"
                size="sm"
                className="gap-1"
                title="Ver histórico completo"
              >
                <MessageSquare className="h-4 w-4" />
                Ver histórico completo
              </Button>
            </Link>
          )}
          {/* Botão Atualizar removido pois o realtime está funcionando */}
          {/* <Button 
            variant="ghost" 
            size="sm" 
            className="gap-1" 
            onClick={() => refetchMessages()}
            disabled={isRefetching}
          >
            <RefreshCw className={`h-4 w-4 ${isRefetching ? 'animate-spin' : ''}`} />
            Atualizar
          </Button> */}
        </div>
      </div>

      {/* Container principal de mensagens com rolagem */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto chat-messages-container p-2"
        style={{ display: "flex", flexDirection: "column" }}
      >
        {/* Indicador de carregamento */}
        {isLoadingMessages && !initialMessages && (
          <div className="flex justify-center p-4">
            <div className="animate-pulse text-muted-foreground">
              Carregando mensagens...
            </div>
          </div>
        )}

        {/* Mensagem de erro */}
        {messagesError && !initialMessages && (
          <div className="flex justify-center p-4">
            <Alert variant="destructive" className="max-w-md">
              <AlertDescription>
                Erro ao carregar mensagens. Por favor, tente novamente.
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Indicador de limitações de histórico */}
        {usePagination && paginatedResult.isHistoryLimited && !isLoadingMessages && (
          <div className="flex flex-col items-center p-4 space-y-3">
            <Alert className="max-w-md border-orange-200 bg-orange-50">
              <AlertDescription className="text-orange-800 text-center">
                {paginatedResult.historyLimitMessage}
              </AlertDescription>
            </Alert>
            {paginatedResult.historyLimits?.currentPlan !== 'Max' && (
              <SmartUpgradeButton
                currentPlan={paginatedResult.historyLimits?.currentPlan as 'Grátis' | 'Pro' | 'Max'}
                source="chat-history"
                size="sm"
              >
                {paginatedResult.historyLimits?.currentPlan === 'Grátis' 
                  ? "Upgrade para Pro - 6 meses de histórico"
                  : "Upgrade para Max - Histórico ilimitado"
                }
              </SmartUpgradeButton>
            )}
          </div>
        )}

        {/* Mensagens vazias */}
        {!isLoadingMessages && validMessages.length === 0 && (
          <div className="flex flex-col items-center justify-center h-full">
            <MessageSquare className="h-12 w-12 text-muted-foreground mb-2" />
            <p className="text-muted-foreground">Nenhuma mensagem ainda</p>
          </div>
        )}

        {/* Botão Carregar Mais Mensagens (aparece no topo se houver mais mensagens) */}
        {usePagination && hasMoreMessages && !isLoadingMessages && (
          <div className="flex justify-center p-4">
            <Button
              variant="outline"
              onClick={paginatedResult.loadMoreMessages}
              disabled={paginatedResult.isLoadingMore}
              className="gap-2"
            >
              {paginatedResult.isLoadingMore ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  Carregando...
                </>
              ) : (
                <>
                  <MessageSquare className="h-4 w-4" />
                  Carregar mais mensagens antigas
                </>
              )}
            </Button>
          </div>
        )}

        {/* Lista de mensagens */}
        <div className="space-y-1 flex-grow">
          {/* Exibir as mensagens na ordem correta */}
          {validMessages.map((message) => {

            return (
              <div
                key={message.id}
                id={`message-${message.id}`}
                className={`
                  transition-all duration-500 rounded-lg
                  ${
                    highlightMessageId === message.id
                      ? "bg-primary/10 ring-2 ring-primary/20 shadow-lg"
                      : ""
                  }
                `}
              >
                <ChatMessageItem
                  message={message}
                  currentUserId={currentUserId}
                  onReactionClick={(messageId) => setShowEmojiPicker(messageId)}
                  onThreadSelect={
                    onThreadSelect
                      ? (messageId, parentMessage) => onThreadSelect(message)
                      : undefined
                  }
                  onReplySelect={onReplySelect}
                  isHighlighted={highlightMessageId === message.id}
                  searchTerm={searchTerm}
                  isCompact={isCompact}
                />
              </div>
            );
          })}
        </div>

        {/* Elemento de referência para rolar até o final */}
        <div ref={messagesEndRef} className="h-1" />
      </div>

      {/* Seletor de emoji flutuante */}
      {showEmojiPicker && (
        <div className="absolute z-10 bottom-20 right-4">
          <EmojiPicker
            onEmojiSelect={(emoji) => {
              if (showEmojiPicker) {
                handleReaction(showEmojiPicker, emoji);
              }
              setShowEmojiPicker(null);
            }}
          />
        </div>
      )}
    </div>
  );
}
