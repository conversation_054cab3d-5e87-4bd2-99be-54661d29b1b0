/**
 * Modal para exibir resumo de conversa
 * <AUTHOR> Internet 2025
 */
import { memo } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { FileText, Copy, Download, Clock, Users, X, Calendar } from 'lucide-react'
import { cn } from '@/lib/utils'
import { toastWithNotification } from '@/lib/notifications/toastWithNotification'
import ReactMarkdown from 'react-markdown'

interface ConversationSummaryModalProps {
  isOpen: boolean
  onClose: () => void
  summary: string
  summaryType: 'quick' | 'detailed'
  messageCount: number
  isGenerating: boolean
  periodLabel?: string
}

export const ConversationSummaryModal = memo(function ConversationSummaryModal({
  isOpen,
  onClose,
  summary,
  summaryType,
  messageCount,
  isGenerating,
  periodLabel
}: ConversationSummaryModalProps) {
  
  const handleCopySummary = async () => {
    try {
      await navigator.clipboard.writeText(summary)
      toastWithNotification.success('Resumo copiado!', {
        description: 'O resumo foi copiado para a área de transferência'
      })
    } catch (error) {
      toastWithNotification.error('Erro ao copiar', {
        description: 'Não foi possível copiar o resumo'
      })
    }
  }

  const handleDownloadSummary = () => {
    const now = new Date()
    const filename = `resumo-conversa-${now.getFullYear()}-${(now.getMonth()+1).toString().padStart(2,'0')}-${now.getDate().toString().padStart(2,'0')}.txt`
    
    const content = `# Resumo da Conversa
    
Gerado em: ${now.toLocaleString('pt-BR')}
Tipo: ${summaryType === 'quick' ? 'Resumo Rápido' : 'Resumo Detalhado'}
Mensagens analisadas: ${messageCount}

---

${summary}

---

Gerado automaticamente pelo Vindula Cosmos`

    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    toastWithNotification.success('Download iniciado!', {
      description: `Arquivo ${filename} foi baixado`
    })
  }

  const isDetailed = summaryType === 'detailed'

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-3 text-xl">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 flex items-center justify-center">
              <FileText className="h-5 w-5 text-white" />
            </div>
            <span className="text-blue-700 dark:text-blue-300">
              📋 Resumo da Conversa{periodLabel ? ` - ${periodLabel}` : ''}
            </span>
          </DialogTitle>
        </DialogHeader>

        {/* Header com metadados */}
        <div className="flex items-center justify-between py-3 px-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
          <div className="flex items-center gap-6 text-sm text-blue-600 dark:text-blue-400">
            {periodLabel && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>{periodLabel}</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span>{messageCount} mensagens</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span>Gerado agora</span>
            </div>
          </div>
          
        </div>

        <Separator />

        {/* Conteúdo do resumo */}
        <div className="flex-1 overflow-y-auto py-4">
          {isGenerating ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-blue-600 dark:text-blue-400 font-medium">
                  Analisando conversa e gerando resumo...
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  Isso pode levar alguns segundos
                </p>
              </div>
            </div>
          ) : summary ? (
            <div className={cn(
                "prose prose-sm max-w-none",
                "prose-headings:text-blue-700 dark:prose-headings:text-blue-300",
                "prose-h2:text-xl prose-h2:font-bold prose-h2:mb-4 prose-h2:mt-6",
                "prose-h3:text-lg prose-h3:font-semibold prose-h3:mb-3 prose-h3:mt-5",
                "prose-h4:text-base prose-h4:font-medium prose-h4:mb-2 prose-h4:mt-4",
                "prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-p:mb-4 prose-p:leading-relaxed",
                "prose-strong:text-blue-600 dark:prose-strong:text-blue-400 prose-strong:font-semibold",
                "prose-em:text-gray-600 dark:prose-em:text-gray-400",
                "prose-ul:text-gray-700 dark:prose-ul:text-gray-300 prose-ul:mb-4",
                "prose-li:text-gray-700 dark:prose-li:text-gray-300 prose-li:mb-2",
                "prose-blockquote:border-l-4 prose-blockquote:border-blue-200 dark:prose-blockquote:border-blue-800",
                "prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:bg-blue-50 dark:prose-blockquote:bg-blue-950/20",
                "prose-blockquote:text-gray-700 dark:prose-blockquote:text-gray-300 prose-blockquote:italic",
                "prose-code:text-blue-600 dark:prose-code:text-blue-400 prose-code:bg-gray-100 dark:prose-code:bg-gray-800",
                "prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-sm",
                "prose-pre:bg-gray-100 dark:prose-pre:bg-gray-800 prose-pre:rounded-lg prose-pre:p-4"
              )}>
              <ReactMarkdown
              components={{
                h2: ({ children }) => (
                  <h2 className="text-xl font-bold text-blue-700 dark:text-blue-300 mb-4 mt-6 flex items-start gap-2">
                    {children}
                  </h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-3 mt-5 flex items-start gap-2">
                    {children}
                  </h3>
                ),
                h4: ({ children }) => (
                  <h4 className="text-base font-medium text-blue-600 dark:text-blue-400 mb-2 mt-4 flex items-start gap-2">
                    {children}
                  </h4>
                ),
                ul: ({ children }) => (
                  <ul className="list-disc list-inside space-y-2 mb-4 pl-4 text-gray-700 dark:text-gray-300">
                    {children}
                  </ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal list-inside space-y-2 mb-4 pl-4 text-gray-700 dark:text-gray-300">
                    {children}
                  </ol>
                ),
                li: ({ children }) => (
                  <li className="text-gray-700 dark:text-gray-300 mb-1">
                    {children}
                  </li>
                ),
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-blue-200 dark:border-blue-800 pl-4 py-2 mb-4 bg-blue-50 dark:bg-blue-950/20 text-gray-700 dark:text-gray-300 italic">
                    {children}
                  </blockquote>
                ),
                strong: ({ children }) => (
                  <strong className="font-semibold text-blue-700 dark:text-blue-300">
                    {children}
                  </strong>
                ),
                em: ({ children }) => (
                  <em className="italic text-gray-600 dark:text-gray-400">
                    {children}
                  </em>
                ),
                code: ({ children }) => (
                  <code className="bg-gray-100 dark:bg-gray-800 text-blue-600 dark:text-blue-400 px-2 py-1 rounded text-sm font-mono">
                    {children}
                  </code>
                ),
                p: ({ children }) => (
                  <p className="text-gray-700 dark:text-gray-300 mb-4 leading-relaxed">
                    {children}
                  </p>
                )
              }}
              >
                {summary}
              </ReactMarkdown>
            </div>
          ) : (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">
                  Nenhum resumo disponível
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {summary && !isGenerating && (
          <>
            <Separator />
            <div className="flex items-center justify-between py-3">
              <div className="flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400">
                <span>💡 Resumo gerado automaticamente com IA</span>
                <span className="opacity-75">
                  • {isDetailed ? 'Análise completa' : 'Visão geral'}
                </span>
              </div>
              
              <Button
                onClick={onClose}
                variant="outline"
                size="sm"
                className="h-8 px-4"
              >
                Fechar
              </Button>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
})