/**
 * Componente para exibir avatares dos membros do canal
 * Mostra até 10 avatares + contador se houver mais membros
 * <AUTHOR> Internet 2025
 */
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { SmartAvatar } from "@/components/ui/smart-avatar";
import { Badge } from "@/components/ui/badge";
import { Users } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface ChannelMember {
  user_id: string;
  profiles: {
    full_name: string | null;
    avatar_url: string | null;
  } | null;
}

interface ChannelMemberAvatarsProps {
  channelId: string;
  className?: string;
}

export function ChannelMemberAvatars({ channelId, className }: ChannelMemberAvatarsProps) {
  const [members, setMembers] = useState<ChannelMember[]>([]);
  const [totalMembers, setTotalMembers] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!channelId) return;

    const fetchMembers = async () => {
      try {
        setIsLoading(true);

        // Buscar todos os membros para contar o total
        const { data: allMembers, error: countError } = await supabase
          .from('channel_members')
          .select('user_id')
          .eq('channel_id', channelId)
          .eq('archived', false);

        if (countError) {
          console.error('Erro ao contar membros:', countError);
          return;
        }

        setTotalMembers(allMembers?.length || 0);

        // Buscar os primeiros 10 membros com seus perfis
        const { data: membersData, error } = await supabase
          .from('channel_members')
          .select(`
            user_id,
            profiles (
              full_name,
              avatar_url
            )
          `)
          .eq('channel_id', channelId)
          .eq('archived', false)
          .order('created_at', { ascending: true })
          .limit(10);

        if (error) {
          console.error('Erro ao buscar membros:', error);
          return;
        }

        setMembers(membersData || []);
      } catch (error) {
        console.error('Erro ao carregar membros do canal:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMembers();

    // 🚀 REFATORADO: Usar eventos do UnifiedRealtimeProvider ao invés de canal próprio
    const handleChannelMemberEvent = (event: CustomEvent) => {
      const { member, eventType } = event.detail;
      
      console.log('🎯 Channel members event via UnifiedRealtimeProvider:', { 
        eventType, 
        channelId: member.channel_id,
        userId: member.user_id
      });
      
      // Verificar se o evento é do canal atual
      if (member.channel_id === channelId) {
        fetchMembers();
      }
    };

    // Escutar eventos do UnifiedRealtimeProvider
    window.addEventListener('vindula-channel-member-changed', handleChannelMemberEvent as EventListener);

    return () => {
      window.removeEventListener('vindula-channel-member-changed', handleChannelMemberEvent as EventListener);
    };
  }, [channelId]);

  if (isLoading) {
    return (
      <div className={cn("flex items-center gap-1", className)}>
        <div className="flex -space-x-2">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse border border-white dark:border-gray-800"
            />
          ))}
        </div>
      </div>
    );
  }

  if (members.length === 0) {
    return null;
  }

  const displayMembers = members.slice(0, 10);
  const remainingCount = totalMembers - displayMembers.length;

  return (
    <TooltipProvider>
      <div className={cn("flex items-center gap-2", className)}>
        {/* Avatares dos membros */}
        <div className="flex -space-x-2">
          {displayMembers.map((member) => {
            const fullName = member.profiles?.full_name || "Usuário";

            return (
              <Tooltip key={member.user_id}>
                <TooltipTrigger asChild>
                  <div className="cursor-pointer">
                    <SmartAvatar
                      src={member.profiles?.avatar_url}
                      name={fullName}
                      className="w-6 h-6 border-2 border-white dark:border-gray-800 ring-1 ring-gray-200 dark:ring-gray-700 hover:ring-2 hover:ring-blue-300 dark:hover:ring-blue-600 transition-all duration-200"
                    />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="text-xs">
                  {fullName}
                </TooltipContent>
              </Tooltip>
            );
          })}
          
          {/* Contador de membros restantes */}
          {remainingCount > 0 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-800 border-2 border-white dark:border-gray-800 flex items-center justify-center cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                  <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                    +{remainingCount}
                  </span>
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="text-xs">
                +{remainingCount} membros
              </TooltipContent>
            </Tooltip>
          )}
        </div>

        {/* Badge com total de membros */}
        <Badge variant="secondary" className="text-xs px-2 py-0.5 h-5">
          <Users className="w-3 h-3 mr-1" />
          {totalMembers}
        </Badge>
      </div>
    </TooltipProvider>
  );
}