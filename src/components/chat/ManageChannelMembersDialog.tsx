import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import { Avatar } from "@/components/ui/avatar";
import { AvatarImage } from "@/components/ui/avatar";
import { AvatarFallback } from "@/components/ui/avatar";
import { X, UserPlus } from "lucide-react";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
import { useCurrentUser } from "@/hooks/use-current-user";

interface Member {
  id: string;
  full_name: string;
  avatar_url: string;
  role: string;
}

interface Props {
  channelId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ManageChannelMembersDialog({ channelId, open, onOpenChange }: Props) {
  const [members, setMembers] = useState<Member[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [channelName, setChannelName] = useState("");
  const [isTeamChannel, setIsTeamChannel] = useState(false);
  const [teamName, setTeamName] = useState("");
  const { toast } = useToast();
  const currentUserId = useCurrentUser();

  useEffect(() => {
    if (open && channelId) {
      fetchMembers();
      fetchChannelInfo();
    }
  }, [open, channelId]);
  
  const fetchChannelInfo = async () => {
    if (!channelId) return;
    
    try {
      const { data, error } = await supabase
        .from("channels")
        .select(`
          name,
          team_id,
          teams:team_id(name)
        `)
        .eq("id", channelId)
        .single();
        
      if (error) throw error;
      
      if (data) {
        setChannelName(data.name);
        setIsTeamChannel(!!data.team_id);
        setTeamName(data.teams?.name || "");
      }
    } catch (error) {
      logQueryEvent('ManageChannelMembersDialog', 'Erro ao buscar informações do canal', error, 'error');
    }
  };

  const fetchMembers = async () => {
    const { data, error } = await supabase
      .from("channel_members")
      .select(`
        user_id,
        role,
        profiles:user_id(
          id,
          full_name,
          avatar_url
        )
      `)
      .eq("channel_id", channelId);

    if (error) {
      console.error("Error fetching members:", error);
      return;
    }

    const formattedMembers = data.map((member) => ({
      id: member.profiles.id,
      full_name: member.profiles.full_name,
      avatar_url: member.profiles.avatar_url,
      role: member.role,
    }));

    setMembers(formattedMembers);
  };

  const handleAddMember = async () => {
    if (!searchTerm) return;

    // Bloquear se for canal de equipe
    if (isTeamChannel) {
      toast({
        title: "Ação não permitida",
        description: `Este é um canal de equipe (${teamName}). Os membros são gerenciados automaticamente através da equipe.`,
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Buscar usuário pelo nome
      const { data: userData, error: userError } = await supabase
        .from("profiles")
        .select("id, company_id")
        .ilike("full_name", `%${searchTerm}%`)
        .single();

      if (userError || !userData) {
        throw new Error("Usuário não encontrado");
      }

      // Verificar se já é membro
      const { data: existingMember } = await supabase
        .from("channel_members")
        .select()
        .eq("channel_id", channelId)
        .eq("user_id", userData.id)
        .single();

      if (existingMember) {
        throw new Error("Usuário já é membro do canal");
      }

      // Adicionar membro
      const { error: addError } = await supabase
        .from("channel_members")
        .insert({
          channel_id: channelId,
          user_id: userData.id,
          role: "member",
        });

      if (addError) throw addError;
      
      // Buscar nome do usuário para a mensagem do sistema
      const { data: userProfile } = await supabase
        .from("profiles")
        .select("full_name")
        .eq("id", userData.id)
        .single();
        
      // Adicionar mensagem do sistema
      if (userProfile) {
        await supabase.from('chat_messages').insert({
          channel_id: channelId,
          sender_id: currentUserId,
          content: `${userProfile.full_name} entrou no canal.`,
          message_type: 'system_join', // ✅ CORREÇÃO: Adicionar message_type para filtro v3
          metadata: {
            type: 'user_joined',
            user_id: userData.id,
            added_by: currentUserId
          }
        });
      }

      toast({
        title: "Membro adicionado",
        description: "O usuário foi adicionado ao canal com sucesso.",
      });

      setSearchTerm("");
      fetchMembers();
    } catch (error: any) {
      toast({
        title: "Erro ao adicionar membro",
        description: error.message || "Ocorreu um erro ao adicionar o membro.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    // Bloquear se for canal de equipe
    if (isTeamChannel) {
      toast({
        title: "Ação não permitida",
        description: `Este é um canal de equipe (${teamName}). Os membros são gerenciados automaticamente através da equipe.`,
        variant: "destructive",
      });
      return;
    }

    try {
      // Buscar nome do usuário para a mensagem do sistema
      const { data: userProfile } = await supabase
        .from("profiles")
        .select("full_name")
        .eq("id", memberId)
        .single();
      
      // Remover membro
      const { error } = await supabase
        .from("channel_members")
        .delete()
        .eq("channel_id", channelId)
        .eq("user_id", memberId);

      if (error) throw error;
      
      // Adicionar mensagem do sistema
      if (userProfile) {
        await supabase.from('chat_messages').insert({
          channel_id: channelId,
          sender_id: currentUserId,
          content: `${userProfile.full_name} saiu do canal.`,
          message_type: 'system_leave', // ✅ CORREÇÃO: Adicionar message_type para filtro v3
          metadata: {
            type: 'user_left',
            user_id: memberId,
            removed_by: currentUserId
          }
        });
      }

      toast({
        title: "Membro removido",
        description: "O usuário foi removido do canal com sucesso.",
      });

      fetchMembers();
    } catch (error) {
      toast({
        title: "Erro ao remover membro",
        description: "Ocorreu um erro ao remover o membro.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Gerenciar Membros do Canal</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {isTeamChannel && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <p className="text-sm text-blue-800 font-medium">
                🔗 Canal de Equipe: {teamName}
              </p>
              <p className="text-xs text-blue-600 mt-1">
                Os membros deste canal são gerenciados automaticamente através da equipe. Para adicionar ou remover membros, gerencie a equipe correspondente.
              </p>
            </div>
          )}
          
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="search">Adicionar Membro</Label>
              <Input
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Digite o nome do usuário"
                disabled={isTeamChannel}
              />
            </div>
            <Button
              className="self-end"
              disabled={!searchTerm || isLoading || isTeamChannel}
              onClick={handleAddMember}
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Adicionar
            </Button>
          </div>
          <div>
            <Label>Membros do Canal</Label>
            <ScrollArea className="h-[200px] border rounded-md mt-2">
              <div className="p-4 space-y-2">
                {members.map((member) => (
                  <div
                    key={member.id}
                    className="flex items-center justify-between gap-2 p-2 rounded-lg hover:bg-muted"
                  >
                    <div className="flex items-center gap-2">
                      <Avatar>
                        <AvatarImage src={member.avatar_url} />
                        <AvatarFallback>
                          {member.full_name.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{member.full_name}</p>
                        <p className="text-sm text-muted-foreground capitalize">
                          {member.role}
                        </p>
                      </div>
                    </div>
                    {member.role !== "owner" && !isTeamChannel && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveMember(member.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                    {isTeamChannel && (
                      <span className="text-xs text-muted-foreground">
                        Auto-gerenciado
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 