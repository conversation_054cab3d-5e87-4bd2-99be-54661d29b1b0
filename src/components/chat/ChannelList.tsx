import { useEffect, useState, use<PERSON><PERSON>back, useMemo } from "react";
import { supabase } from "@/integrations/supabase/client";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useNavigate, useParams } from "react-router-dom";
import { NewChannelDialog } from "./NewChannelDialog";
import { LoadingState } from "@/components/ui/loading-state";
import { Lock, Hash, MoreVertical, Edit, UserPlus, Trash2, LogOut, Bell, BellOff, Search, Filter, Users, MessageSquare, X, Archive, ArchiveRestore } from "lucide-react";
import { useCurrentUser } from "@/hooks/use-current-user";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { EditChannelDialog } from "./EditChannelDialog";
import { ChannelNotificationDialog } from "./ChannelNotificationDialog";
import { Badge } from "@/components/ui/badge";
import { playSound, SoundEffects } from "@/lib/sound-effects";
import { Separator } from "@/components/ui/separator";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { RealtimeChannel } from "@supabase/supabase-js";
import { useFloatingHeart } from "@/contexts/FloatingHeartContext";
import { ChannelListProvider } from "@/contexts/ChannelListContext";
import { useArchiveChat } from "@/hooks/useArchiveChat";
import { useQueryClient } from "@tanstack/react-query";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useUnreadMessagesCount } from "@/lib/query/hooks/useMessageReadReceipts";
import { useChannelLastMessage } from "@/lib/query/hooks/useChannelLastMessage";

// Interfaces --------------------------------------------------------------

interface PublicChannelResult {
  id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  is_private: boolean;
  member_count: number;
  last_message_id: string | null;
  last_message_content: string | null;
  last_message_created_at: string | null;
}

interface ChannelWithDetails {
  id: string;
  name: string;
  description: string | null;
  is_private: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
  member_count: number;
  unread_count?: number;
  archived?: boolean;
  archived_at?: string;
  notification_settings?: {
    sound_enabled: boolean;
    visual_enabled: boolean;
  };
  last_message?: {
    content: string;
    created_at: string;
  };
}

// Componente para item individual do canal com hook para unread_count ----

interface ChannelItemProps {
  channel: ChannelWithDetails;
  currentChannelId?: string;
  currentUserId?: string;
  searchTerm: string;
  messageMatches: Map<string, { messageId: string; content: string; created_at: string }>;
  channelsWithMessageMatches: Set<string>;
  onChannelClick: (id: string) => void;
  onEditChannel: (id: string) => void;
  onManageMembers: (e: React.MouseEvent | null, id: string) => void;
  onNotificationSettings: (e: React.MouseEvent, id: string) => void;
  onLeaveChannel: (id: string) => void;
  onDeleteChannel: (id: string) => void;
  onArchiveChannel: (channelId: string, archive?: boolean) => void;
  isToggling: boolean;
}

function ChannelItem({ 
  channel, 
  currentChannelId, 
  currentUserId, 
  searchTerm, 
  messageMatches, 
  channelsWithMessageMatches,
  onChannelClick,
  onEditChannel,
  onManageMembers,
  onNotificationSettings,
  onLeaveChannel,
  onDeleteChannel,
  onArchiveChannel,
  isToggling
}: ChannelItemProps) {
  // ✅ CORREÇÃO: Usar hooks conectados ao sistema de cache invalidation
  const { data: unreadCount = 0 } = useUnreadMessagesCount(channel.id, true);
  const { data: lastMessageFromHook } = useChannelLastMessage(channel.id, true);
  
  // Usar dados do hook se disponível, senão usar dados estáticos do channel
  const lastMessage = lastMessageFromHook || channel.last_message;

  return (
    <div
      key={channel.id}
      className={`group flex items-start gap-3 p-2.5 cursor-pointer hover:bg-accent/50 rounded-lg transition-colors ${currentChannelId === channel.id ? 'bg-accent' : ''}`}
      onClick={() => onChannelClick(channel.id)}
    >
      {/* Ícone com indicador de atividade */}
      <div className="flex-shrink-0 relative">
        <div className="w-7 h-7 rounded-lg bg-primary/10 flex items-center justify-center text-primary">
          {channel.is_private ? <Lock className="h-3.5 w-3.5" /> : <Hash className="h-3.5 w-3.5" />}
        </div>
        {unreadCount > 0 && channel.notification_settings?.visual_enabled && (
          <div className="absolute -top-1 -right-1 h-4 w-4 bg-destructive rounded-full flex items-center justify-center">
            <span className="text-xs text-destructive-foreground font-medium">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          </div>
        )}
      </div>

      {/* Infos */}
      <div className="flex-1 min-w-0 text-left">
        <div className="flex items-center justify-between mb-1">
          <span className="font-medium truncate text-sm">{channel.name}</span>
          <div className="flex items-center gap-1 flex-shrink-0 ml-2">
            <Users className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">
              {channel.member_count}
            </span>

            {/* menu "..." */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity" onClick={(e) => e.stopPropagation()}>
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>

              <DropdownMenuContent align="end">
                {channel.created_by === currentUserId ? (
                  <>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        onEditChannel(channel.id);
                      }}
                    >
                      <Edit className="mr-2 h-3 w-3" />
                      Editar canal
                    </DropdownMenuItem>

                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        onManageMembers(null, channel.id);
                      }}
                    >
                      <UserPlus className="mr-2 h-4 w-4" />
                      Gerenciar membros
                    </DropdownMenuItem>

                    <DropdownMenuSeparator />

                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        onArchiveChannel(channel.id, !channel.archived);
                      }}
                      disabled={isToggling}
                    >
                      {channel.archived ? (
                        <>
                          <ArchiveRestore className="mr-2 h-4 w-4" />
                          Desarquivar
                        </>
                      ) : (
                        <>
                          <Archive className="mr-2 h-4 w-4" />
                          Arquivar
                        </>
                      )}
                    </DropdownMenuItem>

                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        onNotificationSettings(e, channel.id);
                      }}
                    >
                      {channel.notification_settings?.sound_enabled || channel.notification_settings?.visual_enabled ? (
                        <Bell className="mr-2 h-4 w-4" />
                      ) : (
                        <BellOff className="mr-2 h-4 w-4" />
                      )}
                      Configurar notificações
                    </DropdownMenuItem>
                    
                    <DropdownMenuSeparator />
                    
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteChannel(channel.id);
                      }}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Excluir canal
                    </DropdownMenuItem>
                  </>
                ) : (
                  <>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        onArchiveChannel(channel.id, !channel.archived);
                      }}
                      disabled={isToggling}
                    >
                      {channel.archived ? (
                        <>
                          <ArchiveRestore className="mr-2 h-4 w-4" />
                          Desarquivar
                        </>
                      ) : (
                        <>
                          <Archive className="mr-2 h-4 w-4" />
                          Arquivar
                        </>
                      )}
                    </DropdownMenuItem>

                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        onNotificationSettings(e, channel.id);
                      }}
                    >
                      {channel.notification_settings?.sound_enabled || channel.notification_settings?.visual_enabled ? (
                        <Bell className="mr-2 h-4 w-4" />
                      ) : (
                        <BellOff className="mr-2 h-4 w-4" />
                      )}
                      Configurar notificações
                    </DropdownMenuItem>
                    
                    <DropdownMenuSeparator />
                    
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        onLeaveChannel(channel.id);
                      }}
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      Sair do canal
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="space-y-1">
          {channel.description && (
            <p className="text-xs text-muted-foreground line-clamp-1">
              {channel.description}
            </p>
          )}

          {/* Mostrar mensagem encontrada pela busca se existir */}
          {searchTerm && messageMatches.has(channel.id) ? (
            <div className="flex items-center gap-2 text-xs">
              <p className="line-clamp-1 flex-1 text-primary/80">
                <span className="font-medium">📍 Encontrado:</span> {(() => {
                  const match = messageMatches.get(channel.id);
                  if (!match) return '';
                  
                  const content = match.content;
                  const searchLower = searchTerm.toLowerCase();
                  const contentLower = content.toLowerCase();
                  const index = contentLower.indexOf(searchLower);
                  
                  if (index === -1) return content;
                  
                  // Mostrar contexto ao redor do termo encontrado
                  const start = Math.max(0, index - 20);
                  const end = Math.min(content.length, index + searchTerm.length + 20);
                  const preview = (start > 0 ? '...' : '') + 
                                content.substring(start, end) + 
                                (end < content.length ? '...' : '');
                  
                  return preview;
                })()}
              </p>
              <span className="text-xs text-primary/60 flex-shrink-0">
                {(() => {
                  const match = messageMatches.get(channel.id);
                  if (!match) return '';
                  
                  const now = new Date();
                  const msgDate = new Date(match.created_at);
                  const diffInMinutes = Math.floor((now.getTime() - msgDate.getTime()) / (1000 * 60));
                  
                  if (diffInMinutes < 1) return "agora";
                  if (diffInMinutes < 60) return `${diffInMinutes}m`;
                  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
                  return `${Math.floor(diffInMinutes / 1440)}d`;
                })()}
              </span>
            </div>
          ) : lastMessage && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <p className="line-clamp-1 flex-1">
                <span className="font-medium">Última:</span> {lastMessage.content}
              </p>
              <span className="text-xs text-muted-foreground/70 flex-shrink-0">
                {(() => {
                  const now = new Date();
                  const msgDate = new Date(lastMessage.created_at);
                  const diffInMinutes = Math.floor((now.getTime() - msgDate.getTime()) / (1000 * 60));
                  
                  if (diffInMinutes < 1) return "agora";
                  if (diffInMinutes < 60) return `${diffInMinutes}m`;
                  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
                  return `${Math.floor(diffInMinutes / 1440)}d`;
                })()}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Componente principal ----------------------------------------------------

export function ChannelList() {
  return (
    <ChannelListContent />
  );
}

// Componente interno com a lógica -----------------------------------------

function ChannelListContent() {
  const [channels, setChannels] = useState<ChannelWithDetails[]>([]);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);
  const [selectedChannelId, setSelectedChannelId] = useState<string | null>(null);
  const [isEditChannelOpen, setIsEditChannelOpen] = useState(false);
  const [isNotificationSettingsOpen, setIsNotificationSettingsOpen] = useState(false);
  const [subscriptions, setSubscriptions] = useState<RealtimeChannel[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<"all" | "public" | "private">("all");
  const [sortBy, setSortBy] = useState<"name" | "activity" | "members">("activity"); // activity = última mensagem
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [channelsWithMessageMatches, setChannelsWithMessageMatches] = useState<Set<string>>(new Set());
  const [messageMatches, setMessageMatches] = useState<Map<string, { messageId: string; content: string; created_at: string }>>(new Map());
  const [isSearchingMessages, setIsSearchingMessages] = useState(false);
  const [activeTab, setActiveTab] = useState<"active" | "archived">("active");
  const { soundEnabled } = useFloatingHeart();
  const queryClient = useQueryClient();
  const { toggleChannelArchive, isToggling, archivedCounts, refreshArchivedCounts } = useArchiveChat(queryClient);

  const navigate = useNavigate();
  const { channelId: currentChannelId } = useParams();
  const currentUserId = useCurrentUser();

  // Função para atualizar a lista de canais programaticamente
  const updateChannelList = useCallback((updater: (channels: ChannelWithDetails[]) => ChannelWithDetails[]) => {
    setChannels(updater);
  }, []);

  // Filtrar e ordenar canais
  const filteredAndSortedChannels = useMemo(() => {
    let filtered = channels;

    // Filtrar por aba (ativa/arquivada)
    filtered = filtered.filter(channel => {
      const isArchived = channel.archived || false;
      return activeTab === "archived" ? isArchived : !isArchived;
    });

    // Filtrar por busca
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(channel =>
        // Buscar no nome do canal
        channel.name.toLowerCase().includes(searchLower) ||
        // Buscar na descrição do canal
        channel.description?.toLowerCase().includes(searchLower) ||
        // Buscar na última mensagem visível
        channel.last_message?.content?.toLowerCase().includes(searchLower) ||
        // Buscar em mensagens do banco de dados
        channelsWithMessageMatches.has(channel.id)
      );
    }

    // Filtrar por tipo
    if (filterType !== "all") {
      filtered = filtered.filter(channel =>
        filterType === "public" ? !channel.is_private : channel.is_private
      );
    }

    // Ordenar
    return filtered.sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "members":
          return b.member_count - a.member_count;
        case "activity":
        default:
          if (a.last_message?.created_at && b.last_message?.created_at) {
            return new Date(b.last_message.created_at).getTime() - new Date(a.last_message.created_at).getTime();
          }
          if (a.last_message?.created_at) return -1;
          if (b.last_message?.created_at) return 1;
          return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
      }
    });
  }, [channels, searchTerm, filterType, sortBy, channelsWithMessageMatches, messageMatches, activeTab]);

  // Expandir automaticamente se houver busca ativa
  useEffect(() => {
    if (searchTerm && !isSearchExpanded) {
      setIsSearchExpanded(true);
    }
  }, [searchTerm, isSearchExpanded]);

  // Busca em mensagens quando há termo de busca
  useEffect(() => {
    if (!searchTerm || !currentUserId) {
      setChannelsWithMessageMatches(new Set());
      setMessageMatches(new Map());
      setIsSearchingMessages(false);
      return;
    }

    const searchInMessages = async () => {
      setIsSearchingMessages(true);
      try {
        // Buscar mensagens que contêm o termo de busca
        const { data: messages } = await supabase
          .from('chat_messages')
          .select('id, channel_id, content, created_at')
          .ilike('content', `%${searchTerm}%`)
          .order('created_at', { ascending: false })
          .limit(100); // Limitar para performance

        if (messages) {
          const channelIds = new Set<string>();
          const matchMap = new Map<string, { messageId: string; content: string; created_at: string }>();
          
          // Para cada canal, guardar apenas a mensagem mais recente que contém o termo
          messages.forEach(msg => {
            if (!matchMap.has(msg.channel_id)) {
              channelIds.add(msg.channel_id);
              matchMap.set(msg.channel_id, {
                messageId: msg.id,
                content: msg.content,
                created_at: msg.created_at
              });
            }
          });
          
          setChannelsWithMessageMatches(channelIds);
          setMessageMatches(matchMap);
        }
              } catch (error) {
          console.error('Erro ao buscar mensagens:', error);
          setChannelsWithMessageMatches(new Set());
          setMessageMatches(new Map());
        } finally {
        setIsSearchingMessages(false);
      }
    };

    // Debounce: aguardar 300ms antes de buscar
    const timeoutId = setTimeout(searchInMessages, 300);
    return () => clearTimeout(timeoutId);
  }, [searchTerm, currentUserId]);


  // ----------------------------------------------------------------------
  // Carrega/atualiza a lista de canais
  // ----------------------------------------------------------------------
  const fetchChannels = useCallback(async (forceRefresh = false) => {
    if (!currentUserId) return;

    // Cache reabilitado com tempo reduzido
    if (hasLoadedOnce && !forceRefresh) {
      return;
    }

    // Se é primeira carga, mostrar loading
    if (!hasLoadedOnce) {
      setIsInitialLoading(true);
    }

    // 1) Canais públicos --------------------------------------------------
    // @ts-ignore – função RPC não tipada
    const { data: publicChannelsData, error: publicError } = await supabase
      .rpc("list_public_channels");

    if (publicError) {
      console.error("Error fetching public channels:", publicError);
      return;
    }

    // 2) Canais privados do usuário --------------------------------------
    const { data: profileData } = await supabase
      .from("profiles")
      .select("company_id")
      .eq("id", currentUserId)
      .single();

    if (!profileData?.company_id) return;

    const { data: privateChannelsData, error: privateError } = await supabase
      .from("channels")
      .select(`
        id,
        name,
        description,
        is_private,
        created_at,
        updated_at,
        created_by,
        member_count:channel_members(count),
        last_message:chat_messages(
          content,
          created_at
        ),
        channel_members!inner(user_id, archived, archived_at)
      `)
      .eq("company_id", profileData.company_id)
      .eq("is_private", true)
      .eq("channel_members.user_id", currentUserId)
      .order("created_at", { ascending: false });

    if (privateError) {
      console.error("Error fetching private channels:", privateError);
      return;
    }

    // -- Formatação ------------------------------------------------------

    const formattedPrivateChannels =
      privateChannelsData?.map((channel) => {
        const lastMessage = channel.last_message?.[0]
          ? {
              content: channel.last_message[0].content,
              created_at: channel.last_message[0].created_at,
            }
          : undefined;

        // Buscar informações de arquivamento do usuário atual
        const currentUserMember = channel.channel_members?.find(m => m.user_id === currentUserId);

        return {
          id: channel.id,
          name: channel.name,
          description: channel.description,
          is_private: channel.is_private,
          created_at: channel.created_at,
          updated_at: channel.updated_at,
          created_by: channel.created_by,
          member_count: channel.member_count?.[0]?.count || 0,
          archived: currentUserMember?.archived || false,
          archived_at: currentUserMember?.archived_at,
          last_message: lastMessage,
        };
      }) || [];

    const formattedPublicChannels: ChannelWithDetails[] = Array.isArray(publicChannelsData)
      ? publicChannelsData.map((channel: PublicChannelResult) => ({
          id: channel.id,
          name: channel.name,
          description: channel.description,
          is_private: channel.is_private,
          created_at: channel.created_at,
          updated_at: channel.updated_at,
          created_by: channel.created_by,
          member_count: channel.member_count,
          last_message: channel.last_message_content
            ? {
                content: channel.last_message_content,
                created_at: channel.last_message_created_at!,
              }
            : undefined,
        }))
      : [];

    // Buscar configurações de notificação e status de arquivamento (sem unread_count - será feito via hook)
    const channelsWithSettings = await Promise.all(
      [...formattedPublicChannels, ...formattedPrivateChannels].map(async (channel) => {
        // Configurações de notificação
        const { data: notificationSettings, error: settingsError } = await supabase
          .rpc('get_channel_notification_settings', {
            p_channel_id: channel.id
          });
          
        if (settingsError) {
          logQueryEvent('ChannelList', 'Erro ao buscar configurações de notificação', settingsError, 'error');
        }

        // Para canais públicos, buscar informações de arquivamento do usuário atual
        let archived = channel.archived || false;
        let archived_at = channel.archived_at;
        
        if (!channel.is_private) {
          const { data: memberData } = await supabase
            .from('channel_members')
            .select('archived, archived_at')
            .eq('channel_id', channel.id)
            .eq('user_id', currentUserId)
            .maybeSingle();
          
          if (memberData) {
            archived = memberData.archived || false;
            archived_at = memberData.archived_at;
          }
        }
        
        return {
          ...channel,
          archived,
          archived_at,
          notification_settings: notificationSettings ? {
            sound_enabled: notificationSettings.sound_enabled,
            visual_enabled: notificationSettings.visual_enabled
          } : {
            sound_enabled: true,
            visual_enabled: true
          }
        };
      })
    );

    setChannels(channelsWithSettings);
    setHasLoadedOnce(true);
    setIsInitialLoading(false);
  }, [currentUserId, hasLoadedOnce]);

  // ----------------------------------------------------------------------
  // Inscrição em tempo real para novas mensagens - APÓS fetchChannels
  // ----------------------------------------------------------------------
  useEffect(() => {
    if (!currentUserId) return;
    
    const handleChannelMessageEvent = async (event: CustomEvent) => {
      const { message } = event.detail;
      
      logQueryEvent('ChannelList', '🎯 Nova mensagem via UnifiedRealtimeProvider', { 
        messageId: message.id,
        channelId: message.channel_id,
        senderId: message.sender_id
      });
      
      // Verificar se a mensagem não é do usuário atual e é de um canal desta lista
      if (message.sender_id !== currentUserId && message.channel_id) {
        const channelInList = channels.find(c => c.id === message.channel_id);
        if (channelInList) {
          // Buscar configurações de notificação para este canal
          const { data: settings } = await supabase
            .rpc('get_channel_notification_settings', {
              p_channel_id: message.channel_id
            });
          
          // Tocar som se habilitado
          if (settings?.sound_enabled && soundEnabled) {
            playSound(SoundEffects.NEW_MESSAGE);
          }
          
          // Atualizar contagem de não lidas
          fetchChannels();
        }
      }
    };

    // Escutar eventos do UnifiedRealtimeProvider
    window.addEventListener('vindula-chat-message-insert', handleChannelMessageEvent as EventListener);
    
    return () => {
      window.removeEventListener('vindula-chat-message-insert', handleChannelMessageEvent as EventListener);
    };
  }, [channels, currentUserId, soundEnabled, fetchChannels]);

  // ----------------------------------------------------------------------
  // Inscrições em tempo‑real + carregamento inicial
  // ----------------------------------------------------------------------
  useEffect(() => {
    fetchChannels(); // primeira carga
    if (currentUserId) {
      refreshArchivedCounts(currentUserId);
    }

    // 🚀 REMOVIDO: Canal individual substituído pelo UnifiedRealtimeProvider
    // O canal catch-all já processa eventos de channels, chat_messages e dispara eventos customizados
    
    const handleChannelEvent = () => {
      logQueryEvent('ChannelList', '🎯 Evento de canal via UnifiedRealtimeProvider');
      logQueryEvent('ChannelList', '🔄 Executando fetchChannels() com forceRefresh=true');
      fetchChannels(true); // Force refresh
    };

    const handleMessageEvent = () => {
      logQueryEvent('ChannelList', '🎯 Evento de mensagem via UnifiedRealtimeProvider');
      fetchChannels();
    };

    const handleChannelMemberEvent = (event: CustomEvent) => {
      const { member, eventType } = event.detail;
      logQueryEvent('ChannelList', '🎯 Evento de membro de canal via UnifiedRealtimeProvider', {
        eventType,
        channelId: member.channel_id,
        userId: member.user_id,
        currentUserId
      });
      
      // Se o usuário atual foi adicionado/removido de um canal, atualizar lista
      if (member.user_id === currentUserId) {
        logQueryEvent('ChannelList', '🔄 Usuário atual foi afetado, atualizando lista de canais');
        logQueryEvent('ChannelList', '🔄 Executando fetchChannels() com forceRefresh=true para membro');
        fetchChannels(true); // Force refresh
      }
    };

    // Escutar eventos do UnifiedRealtimeProvider
    window.addEventListener('vindula-channel-changed', handleChannelEvent);
    window.addEventListener('vindula-chat-message-insert', handleMessageEvent);
    window.addEventListener('vindula-channel-member-changed', handleChannelMemberEvent as EventListener);

    return () => {
      window.removeEventListener('vindula-channel-changed', handleChannelEvent);
      window.removeEventListener('vindula-chat-message-insert', handleMessageEvent);
      window.removeEventListener('vindula-channel-member-changed', handleChannelMemberEvent as EventListener);
    };
  }, [fetchChannels]);

  // Adicionado: useEffect para escutar evento de auto-join e forçar refresh da lista de canais
  useEffect(() => {
    const handleChannelJoined = () => {
      logQueryEvent('ChannelList', 'Evento channelJoinedForceRefresh recebido, atualizando canais.');
      fetchChannels();
    };

    window.addEventListener('channelJoinedForceRefresh', handleChannelJoined);

    return () => {
      window.removeEventListener('channelJoinedForceRefresh', handleChannelJoined);
    };
  }, [fetchChannels]); // Adicionado fetchChannels como dependência

  // Adicionado: useEffect para escutar evento de arquivamento de canal
  useEffect(() => {
    const handleChannelArchiveStatusChanged = (event: any) => {
      fetchChannels(true); // forceRefresh = true para garantir atualização
    };

    window.addEventListener('channelArchiveStatusChanged', handleChannelArchiveStatusChanged);

    return () => {
      window.removeEventListener('channelArchiveStatusChanged', handleChannelArchiveStatusChanged);
    };
  }, [fetchChannels]);

  // ----------------------------------------------------------------------
  // Navegação + handlers --------------------------------------------------
  // ----------------------------------------------------------------------
  const handleChannelClick = async (id: string) => {
    // Se há uma busca ativa e encontramos uma mensagem específica para este canal
    const messageMatch = messageMatches.get(id);
    
    if (searchTerm && messageMatch) {
      // Navegar para o canal com foco na mensagem específica
      navigate(`/chat/channel/${id}?highlight=${messageMatch.messageId}&search=${encodeURIComponent(searchTerm)}`);
    } else {
      // Navegação normal
      navigate(`/chat/channel/${id}`);
    }

    // Marcar canal como lido atualizando last_read_at em channel_members
    try {
      const currentUserId = supabase.auth.getUser ? (await supabase.auth.getUser()).data.user?.id : null;
      if (!currentUserId) {
        logQueryEvent('ChannelList', 'Usuário não autenticado ao tentar marcar canal como lido.', {}, 'error');
        return;
      }

      const { error } = await supabase
        .from('channel_members')
        .update({ last_read_at: new Date().toISOString() })
        .eq('channel_id', id)
        .eq('user_id', currentUserId);

      if (error) throw error;

      // Atualizar a lista de canais para remover o indicador de não lido
      fetchChannels();
    } catch (error) {
      logQueryEvent('ChannelList', 'Erro ao marcar canal como lido (update channel_members)', error, 'error');
    }
  };

  const handleManageMembers = (e: React.MouseEvent | null, id: string) => {
    e?.stopPropagation();
    navigate(`/chat/canal/${id}/membros`);
  };

  const handleEditChannel = (id: string) => {
    setSelectedChannelId(id);
    // Pequeno timeout para garantir que o estado seja atualizado antes de abrir o modal
    setTimeout(() => {
      setIsEditChannelOpen(true);
    }, 100);
  };

  const handleNotificationSettings = (e: React.MouseEvent, id: string) => {
    e.stopPropagation();
    setSelectedChannelId(id);
    setTimeout(() => {
      setIsNotificationSettingsOpen(true);
    }, 100);
  };

  const handleLeaveChannel = async (id: string) => {
    if (!currentUserId) return;

    // Buscar nome do usuário para a mensagem de sistema
    let userName = 'Usuário'; // Fallback name
    const { data: profile } = await supabase
      .from('profiles')
      .select('full_name')
      .eq('id', currentUserId)
      .single();
    if (profile?.full_name) {
      userName = profile.full_name;
    }

    const { error } = await supabase
      .from("channel_members")
      .delete()
      .eq("channel_id", id)
      .eq("user_id", currentUserId);

    if (error) {
      console.error(error);
      toast.error("Erro", { description: "Não foi possível sair do canal." });
      return;
    }

    // Inserir mensagem de sistema ANTES de notificar o usuário e navegar
    // para que ela já esteja lá quando o usuário for redirecionado ou a lista atualizar.
    const { error: systemMessageError } = await supabase.from('chat_messages').insert({
      channel_id: id,
      sender_id: currentUserId, // Ou um ID de sistema/null se preferir
      content: `${userName} saiu do canal.`,
      message_type: 'system_leave', // ✅ CORREÇÃO: Adicionar message_type para filtro v3
    });

    if (systemMessageError) {
      logQueryEvent('ChannelList:handleLeaveChannel', 'Error inserting system_leave message', { channelId: id, currentUserId, error: systemMessageError }, 'error');
      // Não bloquear a saída do canal por erro na msg de sistema, mas logar.
    }

    toast.success("Sucesso", { description: "Você saiu do canal." });
    fetchChannels();
    navigate("/chat");
  };

  const handleDeleteChannel = async (id: string) => {
    if (!currentUserId) return;

    const { error } = await supabase
      .from("channels")
      .delete()
      .eq("id", id)
      .eq("created_by", currentUserId);

    if (error) {
      console.error(error);
      toast.error("Erro", { description: "Não foi possível excluir o canal." });
      return;
    }

    toast.success("Sucesso", { description: "Canal excluído com sucesso." });
    fetchChannels();
    navigate("/chat");
  };

  const handleArchiveChannel = async (channelId: string, archive: boolean = true) => {
    if (!currentUserId) return;
    
    const success = await toggleChannelArchive(channelId, currentUserId, archive);
    if (success) {
      await fetchChannels(); // Refresh list
    }
  };

  // ----------------------------------------------------------------------
  // Render ----------------------------------------------------------------
  // ----------------------------------------------------------------------
  return (
    <ChannelListProvider updateChannelList={updateChannelList}>
    <div className="flex flex-col min-h-0 h-full">
      {/* Abas Ativo/Arquivado */}
      <div className="p-3 border-b shrink-0">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "active" | "archived")}>
          <TabsList className="w-full grid grid-cols-2 h-9">
            <TabsTrigger value="active" className="text-sm">
              Ativos
            </TabsTrigger>
            <TabsTrigger value="archived" className="text-sm flex items-center gap-1">
              <Archive className="h-3 w-3" />
              Arquivados
              {archivedCounts && archivedCounts.archived_channels > 0 && (
                <Badge variant="secondary" className="h-4 text-xs">
                  {archivedCounts.archived_channels}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Header com busca compacta */}
      <div className="p-3 border-b shrink-0">
        <div className="flex gap-2">
          {!isSearchExpanded ? (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsSearchExpanded(true)}
              className={`h-9 flex-1 justify-start gap-2 ${searchTerm ? 'ring-2 ring-primary ring-offset-1' : ''}`}
              title={searchTerm ? `Busca ativa: "${searchTerm}"` : "Buscar canais e mensagens"}
            >
              <Search className="h-4 w-4" />
              {searchTerm ? `"${searchTerm}"` : "Buscar canais..."}
            </Button>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setIsSearchExpanded(false);
                setSearchTerm("");
                setFilterType("all");
                setSortBy("activity");
              }}
              className="h-9 w-9 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Painel de busca expandido */}
      {isSearchExpanded && (
        <div className="p-3 space-y-3 border-b shrink-0 bg-muted/20">
          {/* Busca */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar canais e mensagens..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-9"
              autoFocus
            />
          </div>

          {/* Filtros avançados - só aparecem se houver busca ativa */}
          {searchTerm && (
            <div className="flex gap-2">
              <Select value={filterType} onValueChange={(value: "all" | "public" | "private") => setFilterType(value)}>
                <SelectTrigger className="flex-1 h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="public">Públicos</SelectItem>
                  <SelectItem value="private">Privados</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={(value: "name" | "activity" | "members") => setSortBy(value)}>
                <SelectTrigger className="flex-1 h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="activity">Última mensagem</SelectItem>
                  <SelectItem value="name">Nome A-Z</SelectItem>
                  <SelectItem value="members">Mais membros</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Contadores - só quando há busca ativa */}
          {searchTerm && (
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>
                {filteredAndSortedChannels.length} canal(is)
                {messageMatches.size > 0 && (
                  <span className="ml-2 text-primary/70">
                    • {messageMatches.size} com mensagens
                  </span>
                )}
                {isSearchingMessages && (
                  <span className="ml-2 animate-pulse">🔍 buscando...</span>
                )}
              </span>
              <div className="flex gap-3">
                <span className="flex items-center gap-1">
                  <Hash className="h-3 w-3" />
                  {filteredAndSortedChannels.filter(c => !c.is_private).length}
                </span>
                <span className="flex items-center gap-1">
                  <Lock className="h-3 w-3" />
                  {filteredAndSortedChannels.filter(c => c.is_private).length}
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Indicador de total de canais quando não há busca */}
      {!isSearchExpanded && !searchTerm && (
        <div className="px-3 py-1 bg-muted/20 border-b shrink-0">
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{channels.length} canal(is) disponível(is)</span>
            <div className="flex gap-3">
              <span className="flex items-center gap-1">
                <Hash className="h-3 w-3" />
                {channels.filter(c => !c.is_private).length}
              </span>
              <span className="flex items-center gap-1">
                <Lock className="h-3 w-3" />
                {channels.filter(c => c.is_private).length}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Lista de canais */}
      <ScrollArea className="flex-1">
        <div className="space-y-1 p-2">
          {isInitialLoading ? (
            <LoadingState 
              title="Carregando canais..."
              description="Buscando canais disponíveis"
            />
          ) : filteredAndSortedChannels.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <MessageSquare className="h-12 w-12 text-muted-foreground/50 mb-3" />
              <p className="text-sm text-muted-foreground mb-2">
                {searchTerm 
                  ? `Nenhum canal encontrado para "${searchTerm}"` 
                  : filterType !== "all" 
                    ? "Nenhum canal nesta categoria"
                    : "Nenhum canal disponível"
                }
              </p>
              {searchTerm ? (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => {
                    setSearchTerm("");
                    setFilterType("all");
                    setSortBy("activity");
                  }}
                >
                  Limpar busca
                </Button>
              ) : filterType !== "all" ? (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setFilterType("all")}
                >
                  Ver todos os canais
                </Button>
              ) : null}
            </div>
          ) : (
            filteredAndSortedChannels.map((channel) => (
              <ChannelItem
                key={channel.id}
                channel={channel}
                currentChannelId={currentChannelId}
                currentUserId={currentUserId}
                searchTerm={searchTerm}
                messageMatches={messageMatches}
                channelsWithMessageMatches={channelsWithMessageMatches}
                onChannelClick={handleChannelClick}
                onEditChannel={handleEditChannel}
                onManageMembers={handleManageMembers}
                onNotificationSettings={handleNotificationSettings}
                onLeaveChannel={handleLeaveChannel}
                onDeleteChannel={handleDeleteChannel}
                onArchiveChannel={handleArchiveChannel}
                isToggling={isToggling}
              />
            ))
          )}
        </div>
      </ScrollArea>

      {/* diálogos modais -------------------------------------------------- */}
      
      <EditChannelDialog
        channelId={selectedChannelId}
        open={isEditChannelOpen}
        onOpenChange={(open) => {
          setIsEditChannelOpen(open);
          // Se o modal estiver fechando, limpar o ID do canal após um timeout
          if (!open) {
            setTimeout(() => {
              if (!isNotificationSettingsOpen) {
                setSelectedChannelId(null);
              }
            }, 300);
          }
        }}
        onSuccess={fetchChannels}
      />
      
      <ChannelNotificationDialog
        channelId={selectedChannelId}
        open={isNotificationSettingsOpen}
        onOpenChange={(open) => {
          setIsNotificationSettingsOpen(open);
          // Se o modal estiver fechando, limpar o ID do canal após um timeout
          if (!open) {
            setTimeout(() => {
              if (!isEditChannelOpen) {
                setSelectedChannelId(null);
              }
            }, 300);
          }
        }}
      />
    </div>
    </ChannelListProvider>
  );
}
