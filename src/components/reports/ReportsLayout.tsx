/**
 * Layout principal para a seção de Relatórios e Analytics.
 * Redesenhado seguindo padrões do KnowledgeHub e MissionsPage.
 * <AUTHOR> Internet 2025
 */
import React, { ReactNode, useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { Calendar, Download, Filter, BarChart3, Users, Activity, PieChart, LineChart, Banknote, Brain, Leaf, Building, Heart, School, Target, GraduationCap, Settings, Sparkles, TrendingUp } from 'lucide-react';
import { ExportReportsDialog } from '@/components/reports/ExportReportsDialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useNavigate, useLocation } from 'react-router-dom';

interface ReportsLayoutProps {
  children: ReactNode;
  activeTab?: string;
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  periodSelector?: React.ReactNode;
  dateRange?: { from?: Date; to?: Date };
}

// Variantes de animação premium
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const heroVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

export function ReportsLayout({ children, activeTab = 'engagement', title, description, icon, periodSelector, dateRange }: ReportsLayoutProps) {
  const [period, setPeriod] = useState('month');
  const [department, setDepartment] = useState('all');
  const navigate = useNavigate();
  const location = useLocation();
  
  // Handle tab changes and update the URL
  const handleTabChange = (value: string) => {
    // Mapeamento especial para casos onde a URL é diferente do valor da aba
    const specialRoutes: Record<string, string> = {
      'ai': 'ai-predictive'
    };
    
    // Usa o mapeamento especial ou o próprio valor da aba
    const route = specialRoutes[value] || value;
    navigate(`/reports/${route}`);
  };
  
  // Set the active tab based on the current URL
  useEffect(() => {
    const path = location.pathname;
    if (path.includes('/reports/engagement')) {
      // Do nothing since activeTab is already set by the component
    } else if (path.includes('/reports/gamification')) {
      // Do nothing since activeTab is already set by the component
    } else if (path.includes('/reports/content')) {
      // Do nothing since activeTab is already set by the component
    }
    // Add other cases as needed
  }, [location.pathname]);
  
  // Definir título, descrição e ícone com base na activeTab
  let currentTitle = title || "Relatórios e Analytics";
  let currentDescription = description || "Visualize métricas e dados chave do sistema";
  let currentIcon = icon || <LineChart className="h-8 w-8 text-orange-500" />;

  switch (activeTab) {
    case 'engagement':
      currentTitle = "Relatório de Engajamento";
      currentDescription = "Acompanhe a atividade e interação dos usuários.";
      currentIcon = <Activity className="h-8 w-8 text-orange-500" />;
      break;
    case 'gamification':
      currentTitle = "Relatório de Gamificação";
      currentDescription = "Visualize rankings, pontos e conquistas.";
      currentIcon = <Users className="h-8 w-8 text-orange-500" />;
      break;
    case 'content':
      currentTitle = "Relatório de Conteúdo";
      currentDescription = "Analise o desempenho e alcance dos conteúdos.";
      currentIcon = <BarChart3 className="h-8 w-8 text-orange-500" />;
      break;
    case 'roi':
      currentTitle = "Relatório de ROI";
      currentDescription = "Meça o retorno sobre o investimento das ações.";
      currentIcon = <Banknote className="h-8 w-8 text-orange-500" />;
      break;
    case 'ai':
      currentTitle = "Relatório Preditivo (IA)";
      currentDescription = "Insights e previsões baseadas em inteligência artificial.";
      currentIcon = <Brain className="h-8 w-8 text-orange-500" />;
      break;
    case 'esg':
      currentTitle = "Relatório ESG";
      currentDescription = "Acompanhe métricas ambientais, sociais e de governança.";
      currentIcon = <Leaf className="h-8 w-8 text-orange-500" />;
      break;
    case 'benchmarks':
      currentTitle = "Relatório de Benchmarks";
      currentDescription = "Compare seus resultados com padrões de mercado.";
      currentIcon = <Target className="h-8 w-8 text-orange-500" />;
      break;
    case 'culture':
      currentTitle = "Relatório de Cultura";
      currentDescription = "Analise indicadores relacionados à cultura organizacional.";
      currentIcon = <Heart className="h-8 w-8 text-orange-500" />;
      break;
    case 'learning':
      currentTitle = "Relatório de Aprendizado";
      currentDescription = "Acompanhe o progresso e engajamento em treinamentos.";
      currentIcon = <GraduationCap className="h-8 w-8 text-orange-500" />;
      break;
    default:
      // Mantém os padrões se a aba não for reconhecida
      break;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      <div className="container py-1 px-4 space-y-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Hero Section Premium */}
          <motion.div variants={heroVariants} className="space-y-4 mb-6">
            {/* Header Principal com Gradiente Integrado */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 p-6 bg-gradient-to-r from-orange-600 via-red-600 to-pink-600 rounded-2xl text-white shadow-xl">
              <div className="flex items-center gap-4">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="p-3 bg-white/10 rounded-xl backdrop-blur-sm"
                >
                  {currentIcon}
                </motion.div>
                <div>
                  <h1 className="text-3xl lg:text-4xl font-bold text-white drop-shadow-lg">{currentTitle}</h1>
                  <p className="text-white/90 text-lg font-medium">{currentDescription}</p>
                </div>
              </div>
              
              {/* Ações do Header */}
              <div className="flex flex-col sm:flex-row items-center gap-4">
                {/* Seletor de Período */}
                {periodSelector && (
                  <div className="min-w-[200px]">
                    {periodSelector}
                  </div>
                )}
                
                <ExportReportsDialog
                  reportType={activeTab as 'engagement' | 'content'}
                  dateRange={dateRange}
                  trigger={
                    <Button 
                      variant="outline" 
                      className="bg-white/10 border-white/20 text-white hover:bg-white/20 backdrop-blur-sm"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Exportar
                    </Button>
                  }
                />
              </div>
            </div>
          </motion.div>

          {/* Main Content - Abas sempre aparecem */}
          <Tabs value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="grid grid-cols-2 h-auto p-2 bg-background/80 backdrop-blur-sm rounded-xl border shadow-lg">
              <TabsTrigger 
                value="engagement" 
                className="flex items-center gap-2 py-4 px-6 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white"
              >
                <Activity className="h-4 w-4" />
                <span className="font-medium">Engajamento</span>
              </TabsTrigger>
              <TabsTrigger 
                value="content" 
                className="flex items-center gap-2 py-4 px-6 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-indigo-500 data-[state=active]:text-white"
              >
                <BarChart3 className="h-4 w-4" />
                <span className="font-medium">Conteúdo</span>
              </TabsTrigger>
            </TabsList>

            {/* Conteúdo das abas */}
            <div className="mt-6">
              {children}
            </div>
          </Tabs>
        </motion.div>
      </div>
    </div>
  );
} 