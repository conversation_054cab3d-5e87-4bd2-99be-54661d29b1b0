/**
 * Componente para exportação de relatórios de engajamento e conteúdo.
 * Oferece múltiplos formatos: CSV, Excel e PDF com dados e gráficos.
 * <AUTHOR> Internet 2025
 */
import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Download, 
  BarChart3, 
  Users,
  Clock,
  Shield,
  Loader2,
  FileSpreadsheet,
  FileImage,
  TrendingUp,
  Activity,
  MessageSquare
} from 'lucide-react';
import { successWithNotification, errorWithNotification } from '@/lib/notifications/toastWithNotification';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { ReportDateRange } from '@/components/reports/ReportPeriodSelector';
import { generateCSVReport, generateExcelData, generatePDFContent, ReportData } from '@/lib/utils/reportExport';

interface ExportReportsDialogProps {
  reportType: 'engagement' | 'content';
  dateRange?: ReportDateRange;
  trigger?: React.ReactNode;
  disabled?: boolean;
}

interface ReportOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  recommended?: boolean;
  fileExtension: string;
  estimatedSize: string;
}

const getReportOptions = (reportType: 'engagement' | 'content'): ReportOption[] => {
  const baseOptions = [
    {
      id: 'excel-detailed',
      name: 'Relatório Analítico (Excel)',
      description: 'Formato Excel otimizado com seções organizadas, análises comparativas e insights estratégicos.',
      icon: <FileSpreadsheet className="h-8 w-8 text-green-600" />,
      features: [
        'Múltiplas seções organizadas',
        'Análises comparativas e tendências',
        'Cálculos de performance',
        'Classificações e recomendações',
        'Formatação Excel-optimized',
        'Pronto para análise avançada'
      ],
      recommended: true,
      fileExtension: 'XLSX',
      estimatedSize: '200-500 KB'
    },
    {
      id: 'csv-raw',
      name: 'Dados Simples (CSV)',
      description: 'Dados básicos em formato CSV simples, sem análises adicionais ou formatação.',
      icon: <FileText className="h-8 w-8 text-blue-600" />,
      features: [
        'Dados básicos sem análise',
        'Formato simples e limpo',
        'Compatível com qualquer ferramenta',
        'Leve e rápido para download',
        'Ideal para processamento próprio',
        'Codificação UTF-8'
      ],
      fileExtension: 'CSV',
      estimatedSize: '50-150 KB'
    },
    {
      id: 'pdf-visual',
      name: 'Relatório Executivo (PDF)',
      description: 'Documento PDF profissional com formatação visual, tabelas organizadas e pronto para apresentação.',
      icon: <FileImage className="h-8 w-8 text-red-600" />,
      features: [
        'Documento PDF real',
        'Design profissional e limpo',
        'Tabelas bem formatadas',
        'Pronto para apresentação',
        'Otimizado para impressão',
        'Branding Vindula Cosmos'
      ],
      fileExtension: 'PDF',
      estimatedSize: '200-800 KB'
    }
  ];

  if (reportType === 'engagement') {
    return baseOptions.map(option => ({
      ...option,
      features: option.features.concat([
        'Métricas de interação',
        'Análise de engajamento',
        'Tempo de sessão',
        'Distribuição por horário'
      ])
    }));
  } else {
    return baseOptions.map(option => ({
      ...option,
      features: option.features.concat([
        'Performance de conteúdo',
        'Análise de alcance',
        'Métricas de visualização',
        'Top conteúdos'
      ])
    }));
  }
};

export function ExportReportsDialog({ 
  reportType,
  dateRange,
  trigger, 
  disabled = false 
}: ExportReportsDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [selectedOption, setSelectedOption] = useState<string>('excel-detailed');

  const reportOptions = getReportOptions(reportType);
  const reportTitle = reportType === 'engagement' ? 'Engajamento' : 'Conteúdo';
  const reportIcon = reportType === 'engagement' ? 
    <Activity className="h-5 w-5" /> : 
    <BarChart3 className="h-5 w-5" />;

  const handleExport = async () => {
    if (!selectedOption) return;

    setIsExporting(true);
    try {
      logQueryEvent('ExportReportsDialog', 'Iniciando exportação de relatório', { 
        reportType, 
        format: selectedOption,
        dateRange
      });

      // Preparar dados do relatório
      const reportData: ReportData = {
        reportType,
        dateRange,
        data: {} // Os dados são gerados internamente nas funções
      };

      let fileName: string;
      let successMessage: string;
      let content: string;
      let mimeType: string;

      const dateStr = new Date().toISOString().split('T')[0];
      
      switch (selectedOption) {
        case 'excel-detailed':
          content = await generateExcelData(reportData);
          fileName = `relatorio-${reportType}-completo-${dateStr}.xlsx`; // Formato Excel otimizado
          successMessage = 'Relatório Excel exportado com sucesso! Formato otimizado para análise.';
          mimeType = 'text/csv;charset=utf-8;';
          break;

        case 'csv-raw':
          content = await generateCSVReport(reportData);
          fileName = `dados-${reportType}-${dateStr}.csv`;
          successMessage = 'Dados CSV exportados com sucesso!';
          mimeType = 'text/csv;charset=utf-8;';
          break;

        case 'pdf-visual':
          const pdfBytes = await generatePDFContent(reportData);
          fileName = `relatorio-${reportType}-visual-${dateStr}.pdf`;
          successMessage = 'Relatório PDF gerado com sucesso!';
          
          // Criar blob com os bytes do PDF
          const pdfBlob = new Blob([pdfBytes], { type: 'application/pdf' });
          
          // Criar URL para download
          const pdfUrl = window.URL.createObjectURL(pdfBlob);
          const pdfAnchor = document.createElement('a');
          pdfAnchor.href = pdfUrl;
          pdfAnchor.download = fileName;
          document.body.appendChild(pdfAnchor);
          pdfAnchor.click();
          window.URL.revokeObjectURL(pdfUrl);
          document.body.removeChild(pdfAnchor);

          successWithNotification("Relatório exportado", {
            description: successMessage,
            persist: true,
            notificationType: "system",
          });

          setIsOpen(false);
          setIsExporting(false);
          return; // Sair da função aqui pois já fizemos o download

        default:
          throw new Error('Tipo de exportação não reconhecido');
      }

      // Criar blob com o conteúdo gerado
      const blob = new Blob([content], { type: mimeType });
      
      // Criar URL para download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      successWithNotification("Relatório exportado", {
        description: successMessage,
        persist: true,
        notificationType: "system",
      });

      setIsOpen(false);
    } catch (error) {
      logQueryEvent('ExportReportsDialog', 'Erro ao exportar relatório', { 
        error, 
        reportType, 
        format: selectedOption 
      }, 'error');
      
      errorWithNotification("Erro ao exportar relatório", {
        description: "Não foi possível gerar o relatório. Tente novamente mais tarde.",
        persist: true,
        notificationType: "system",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" disabled={disabled}>
            <Download className="mr-2 h-4 w-4" />
            Exportar
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {reportIcon}
            Exportar Relatório de {reportTitle}
          </DialogTitle>
          <DialogDescription>
            Escolha o formato de exportação que melhor atende às suas necessidades.
            {dateRange && (
              <span className="block mt-1 text-sm">
                Período: {dateRange.from?.toLocaleDateString()} até {dateRange.to?.toLocaleDateString()}
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Informações importantes */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-blue-50 p-4 rounded-lg border border-blue-200"
          >
            <div className="flex items-start gap-3">
              <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900 mb-1">Dados Seguros e Atualizados</h4>
                <p className="text-sm text-blue-700">
                  Todos os relatórios contêm dados em tempo real respeitando as permissões de acesso.
                  As informações são geradas no momento da exportação.
                </p>
              </div>
            </div>
          </motion.div>

          {/* Opções de relatório */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Escolha o formato de exportação:</h3>
            
            <div className="grid gap-4">
              {reportOptions.map((option, index) => (
                <motion.div
                  key={option.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all ${
                    selectedOption === option.id
                      ? 'border-blue-500 bg-blue-50 shadow-md'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedOption(option.id)}
                >
                  {option.recommended && (
                    <Badge className="absolute -top-2 -right-2 bg-gradient-to-r from-blue-500 to-purple-600">
                      Recomendado
                    </Badge>
                  )}
                  
                  <div className="flex gap-4">
                    <div className="flex-shrink-0">
                      {option.icon}
                    </div>
                    
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold text-lg">{option.name}</h4>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Clock className="h-4 w-4" />
                          {option.estimatedSize}
                        </div>
                      </div>
                      
                      <p className="text-gray-600">{option.description}</p>
                      
                      <div className="grid grid-cols-2 gap-2 mt-3">
                        {option.features.map((feature, idx) => (
                          <div key={idx} className="flex items-center gap-2 text-sm">
                            <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                            <span>{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Informações adicionais */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="bg-gray-50 p-4 rounded-lg"
          >
            <h4 className="font-medium mb-2 flex items-center gap-2">
              <Users className="h-4 w-4" />
              Dados Incluídos no Relatório de {reportTitle}
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm text-gray-600">
              {reportType === 'engagement' ? (
                <>
                  <div>• Total de acessos</div>
                  <div>• Usuários únicos</div>
                  <div>• Tempo médio de sessão</div>
                  <div>• Taxa de retenção</div>
                  <div>• Reações e comentários</div>
                  <div>• Compartilhamentos</div>
                  <div>• Engajamento por departamento</div>
                  <div>• Distribuição por horário</div>
                  <div>• Acesso por dispositivo</div>
                </>
              ) : (
                <>
                  <div>• Performance de conteúdo</div>
                  <div>• Visualizações por post</div>
                  <div>• Alcance e impressões</div>
                  <div>• Conteúdo mais popular</div>
                  <div>• Análise temporal</div>
                  <div>• Engajamento médio</div>
                  <div>• Categorização por tipo</div>
                  <div>• Análise de hashtags</div>
                  <div>• Métricas de crescimento</div>
                </>
              )}
            </div>
          </motion.div>

          {/* Botões de ação */}
          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isExporting}>
              Cancelar
            </Button>
            <Button 
              onClick={handleExport} 
              disabled={!selectedOption || isExporting}
              className="min-w-[120px]"
            >
              {isExporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Gerando...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Exportar
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

