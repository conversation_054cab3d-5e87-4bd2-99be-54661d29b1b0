/**
 * Aba de Habilidades do Perfil - Componente Separado (Sistema Híbrido com Níveis)
 * <AUTHOR> Internet 2025
 */
import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Award, Languages, Plus, X, Edit3, Link, Info, Search, AlertCircle, CheckCircle } from "lucide-react";
import { toastWithNotification } from "@/lib/notifications/toastWithNotification";
import { useState, useEffect } from "react";
import { 
  useUserSkillsWithLevels, 
  useAvailableSkills, 
  useAddSkillWithLevel,
  useUpdateSkillLevel,
  useRemoveSkill,
  useCreateCompanySkill,
  getSkillName,
  isSkillAlreadySelected,
  UserSkillWithLevel 
} from "@/lib/query/hooks/useUserSkillsWithLevels";
import { SkillLevelSelector, SkillLevelBadge, StarRating } from "@/components/skills/SkillLevelSelector";
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/components/ui/tooltip";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { 
  findSimilarSkills, 
  findExactMatch, 
  normalizeSkillName, 
  validateSkillName,
  SkillSuggestion 
} from "@/lib/utils/skillSimilarity";

// Variantes de animação
const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

const tabVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  }
};

// Interfaces mantidas para compatibilidade com idiomas
interface UserSkill {
  user_id: string;
  skill_id?: string;
  company_skill_id?: string;
  skill_type: 'system' | 'company';
  skills?: {
    id: string;
    name: string;
  };
  company_skills?: {
    id: string;
    name: string;
  };
}

interface UserLanguage {
  id: string;
  language: {
    id: string;
    name: string;
    code: string;
  };
  proficiency: 'basic' | 'intermediate' | 'advanced' | 'native';
}

interface Skill {
  id: string;
  name: string;
  skill_type: 'system' | 'company';
}

interface Language {
  id: string;
  name: string;
  code: string;
}

// Interfaces simplificadas para a nova implementação
interface ProfileSkillsTabProps {
  userId?: string; // Opcional - usa usuário atual se não fornecido
  userLanguages: UserLanguage[];
  availableLanguages: Language[];
  selectedLanguage: string;
  setSelectedLanguage: (language: string) => void;
  selectedProficiency: 'basic' | 'intermediate' | 'advanced' | 'native';
  setSelectedProficiency: (proficiency: 'basic' | 'intermediate' | 'advanced' | 'native') => void;
  onAddLanguage: () => void;
  onRemoveLanguage: (languageId: string) => void;
  getProficiencyLabel: (proficiency: string) => string;
  isLanguageSelected: (languageId: string) => boolean;
  addLanguageMutationPending: boolean;
}

export function ProfileSkillsTab({
  userId,
  userLanguages,
  availableLanguages,
  selectedLanguage,
  setSelectedLanguage,
  selectedProficiency,
  setSelectedProficiency,
  onAddLanguage,
  onRemoveLanguage,
  getProficiencyLabel,
  isLanguageSelected,
  addLanguageMutationPending
}: ProfileSkillsTabProps) {
  // Novos hooks para sistema de níveis
  const { data: userSkills = [], isLoading: isLoadingSkills } = useUserSkillsWithLevels(userId);
  const { data: availableSkills = [], isLoading: isLoadingAvailable, error: availableSkillsError } = useAvailableSkills();
  
  // Debug para availableSkills
  useEffect(() => {
    if (availableSkills && availableSkills.length > 0) {
      console.log('Debug - availableSkills carregadas:', availableSkills.length);
    } else if (availableSkillsError) {
      console.error('Debug - Erro ao carregar availableSkills:', availableSkillsError);
    }
  }, [availableSkills, availableSkillsError]);
  const addSkillMutation = useAddSkillWithLevel();
  const updateSkillMutation = useUpdateSkillLevel();
  const removeSkillMutation = useRemoveSkill();
  const createSkillMutation = useCreateCompanySkill();

  // Estados locais para formulários
  const [selectedSkill, setSelectedSkill] = useState("");
  const [selectedLevel, setSelectedLevel] = useState("");
  const [selectedEvidence, setSelectedEvidence] = useState("");
  
  // Estados para edição
  const [editingSkill, setEditingSkill] = useState<UserSkillWithLevel | null>(null);
  const [editLevel, setEditLevel] = useState("");
  const [editEvidence, setEditEvidence] = useState("");
  
  // Estados para criação de habilidade customizada
  const [isCreatingCustom, setIsCreatingCustom] = useState(false);
  const [customSkillName, setCustomSkillName] = useState("");
  const [suggestions, setSuggestions] = useState<SkillSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // Cleanup timeout on unmount or mode change
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  useEffect(() => {
    // Limpar estado quando sair do modo de criação
    if (!isCreatingCustom) {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
        setSearchTimeout(null);
      }
      setCustomSkillName("");
      setSuggestions([]);
      setShowSuggestions(false);
      setIsSearching(false);
    }
  }, [isCreatingCustom, searchTimeout]);

  // Handlers
  const handleAddSkill = async () => {
    try {
      if (isCreatingCustom) {
        // Verificar se deve mostrar sugestões primeiro
        if (customSkillName.length >= 3) {
          const exactMatch = findExactMatch(customSkillName, availableSkills);
          if (exactMatch) {
            toastWithNotification.warning('Habilidade já existe', {
              description: 'Esta habilidade já está disponível. Use a lista de habilidades existentes.',
            });
            return;
          }
          
          const similarSkills = findSimilarSkills(customSkillName, availableSkills);
          if (similarSkills.length > 0) {
            setSuggestions(similarSkills);
            setShowSuggestions(true);
            return;
          }
          
          // Nenhuma sugestão encontrada, criar diretamente
          await handleCreateCustomSkill();
          return;
        }
      } else {
        if (!selectedSkill) {
          toastWithNotification.error('Seleção obrigatória', {
            description: 'Por favor, selecione uma habilidade primeiro.',
          });
          return;
        }
        
        // Verificar se as habilidades ainda estão carregando
        if (isLoadingAvailable || availableSkills.length === 0) {
          toastWithNotification.warning('Aguarde o carregamento', {
            description: 'As habilidades ainda estão sendo carregadas. Tente novamente em alguns segundos.',
          });
          return;
        }

        // Parse the combined value (type-id)
        // selectedSkill format: "system-f9e956f1-6ff3-4ff8-8280-8299d7cb1590" 
        const dashIndex = selectedSkill.indexOf('-');
        const skillType = selectedSkill.substring(0, dashIndex);
        const skillId = selectedSkill.substring(dashIndex + 1);
        
        // Debug logs para investigar o problema
        console.log('Debug - selectedSkill:', selectedSkill);
        console.log('Debug - skillType:', skillType);
        console.log('Debug - skillId:', skillId);
        console.log('Debug - availableSkills:', availableSkills);
        
        const skill = availableSkills.find(s => s.id === skillId && s.skill_type === skillType);
        
        if (!skill) {
          console.log('Debug - Skill não encontrada. Skills disponíveis:');
          availableSkills.forEach(s => console.log(`- ${s.skill_type}-${s.id}: ${s.name}`));
          
          toastWithNotification.error('Habilidade não encontrada', {
            description: `Erro: não foi possível encontrar a habilidade selecionada (${skillType}-${skillId}). Recarregue a página.`,
          });
          return;
        }

        await addSkillMutation.mutateAsync({
          skillId: skill.id,
          skillType: skill.skill_type,
          level: selectedLevel || undefined,
          evidence: selectedEvidence || undefined
        });

        // Reset form
        setSelectedSkill("");
        setSelectedLevel("");
        setSelectedEvidence("");
        
        // Notificação de sucesso
        toastWithNotification.success('Habilidade adicionada!', {
          description: 'A habilidade foi adicionada ao seu perfil com sucesso.',
        });
      }
    } catch (error: any) {
      console.error('Erro ao adicionar habilidade:', error);
      toastWithNotification.error('Erro ao adicionar habilidade', {
        description: error.message || 'Erro desconhecido. Tente novamente.',
      });
    }
  };

  const handleUpdateSkill = async () => {
    if (!editingSkill) return;

    try {
      await updateSkillMutation.mutateAsync({
        userSkill: editingSkill,
        level: editLevel || undefined,
        evidence: editEvidence || undefined
      });

      setEditingSkill(null);
      setEditLevel("");
      setEditEvidence("");
      
      toastWithNotification.success('Habilidade atualizada!', {
        description: 'As informações da habilidade foram atualizadas com sucesso.',
      });
    } catch (error: any) {
      console.error('Erro ao atualizar habilidade:', error);
      toastWithNotification.error('Erro ao atualizar habilidade', {
        description: error.message || 'Erro desconhecido. Tente novamente.',
      });
    }
  };

  const handleRemoveSkill = async (userSkill: UserSkillWithLevel) => {
    try {
      await removeSkillMutation.mutateAsync(userSkill);
      
      toastWithNotification.success('Habilidade removida!', {
        description: 'A habilidade foi removida do seu perfil.',
      });
    } catch (error: any) {
      console.error('Erro ao remover habilidade:', error);
      toastWithNotification.error('Erro ao remover habilidade', {
        description: error.message || 'Erro desconhecido. Tente novamente.',
      });
    }
  };

  const handleEditSkill = (userSkill: UserSkillWithLevel) => {
    setEditingSkill(userSkill);
    setEditLevel(userSkill.skill_level || "");
    setEditEvidence(userSkill.skill_evidence || "");
  };

  const isSkillSelected = (skillId: string, skillType: 'system' | 'company') => {
    return isSkillAlreadySelected(skillId, skillType, userSkills);
  };
  
  // Handler para buscar similaridades com debounce
  const handleCustomSkillSearch = (value: string) => {
    setCustomSkillName(value);
    
    // Limpar timeout anterior
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    
    // Reset states imediatamente
    setSuggestions([]);
    setShowSuggestions(false);
    setIsSearching(false);
    
    // Não buscar se muito curto
    if (value.length < 5) { // Aumentado de 3 para 5 caracteres
      return;
    }
    
    // Verificar se já existe match exato
    const exactMatch = findExactMatch(value, availableSkills);
    if (exactMatch) {
      return;
    }
    
    // Debounce: aguardar 800ms sem digitação
    const newTimeout = setTimeout(() => {
      setIsSearching(true);
      
      // Buscar similaridades
      const similarSkills = findSimilarSkills(value, availableSkills);
      setSuggestions(similarSkills);
      setShowSuggestions(similarSkills.length > 0);
      setIsSearching(false);
    }, 800);
    
    setSearchTimeout(newTimeout);
  };
  
  // Handler para usar habilidade sugerida
  const handleUseSuggestion = (suggestion: SkillSuggestion) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
      setSearchTimeout(null);
    }
    setSelectedSkill(`${suggestion.skill_type}-${suggestion.id}`);
    setCustomSkillName("");
    setShowSuggestions(false);
    setSuggestions([]);
    setIsCreatingCustom(false);
    // Manter os níveis e evidências já preenchidos
  };
  
  // Handler para criar habilidade nova
  const handleCreateCustomSkill = async () => {
    try {
      const validation = validateSkillName(customSkillName);
      if (!validation.valid) {
        toastWithNotification.error('Nome inválido', {
          description: validation.message,
        });
        return;
      }
      
      const normalizedName = normalizeSkillName(customSkillName);
      
      // Criar a company skill
      const newSkill = await createSkillMutation.mutateAsync({
        name: normalizedName
      });
      
      // Adicionar automaticamente ao usuário com o nível selecionado
      await addSkillMutation.mutateAsync({
        skillId: newSkill.id,
        skillType: 'company',
        level: selectedLevel || undefined,
        evidence: selectedEvidence || undefined
      });
      
      // Reset all states
      if (searchTimeout) {
        clearTimeout(searchTimeout);
        setSearchTimeout(null);
      }
      setCustomSkillName("");
      setShowSuggestions(false);
      setSuggestions([]);
      setIsCreatingCustom(false);
      setSelectedLevel("");
      setSelectedEvidence("");
      
      // Notificação de sucesso
      toastWithNotification.success('Habilidade criada!', {
        description: `"${normalizedName}" foi criada e adicionada ao seu perfil.`,
      });
    } catch (error: any) {
      console.error('Erro ao criar habilidade:', error);
      toastWithNotification.error('Erro ao criar habilidade', {
        description: error.message || 'Erro desconhecido. Tente novamente.',
      });
    }
  };
  return (
    <TooltipProvider>
      <motion.div
        variants={tabVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="space-y-6">
          {/* Skills Section - Premium com Níveis */}
          <motion.div variants={cardVariants}>
            <Card className="backdrop-blur-sm bg-white/80 shadow-xl border-0 ring-1 ring-black/5">
              <CardHeader className="bg-gradient-to-r from-emerald-50 to-green-50 rounded-t-lg">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <motion.div
                    animate={{ rotate: [0, 15, -15, 0] }}
                    transition={{ duration: 4, repeat: Infinity }}
                  >
                    <Award className="h-6 w-6 text-emerald-600" />
                  </motion.div>
                  Habilidades e Competências
                  <Badge variant="secondary" className="ml-auto bg-emerald-100 text-emerald-700">
                    Com Níveis ⭐
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-6">
                  {/* Current Skills with Levels */}
                  <div>
                    <label className="text-sm font-medium mb-3 block">
                      Suas Habilidades 
                      <span className="ml-2 text-xs text-muted-foreground">
                        ({userSkills.length} {userSkills.length === 1 ? 'habilidade' : 'habilidades'})
                      </span>
                    </label>
                    <div className="space-y-3 min-h-[80px] p-4 bg-gray-50 rounded-md">
                      {isLoadingSkills ? (
                        <div className="text-sm text-muted-foreground">Carregando habilidades...</div>
                      ) : userSkills.length > 0 ? (
                        userSkills.map((userSkill, index) => (
                          <div
                            key={`user-skill-${userSkill.skill_type}-${userSkill.skill_id || userSkill.company_skill_id}-${index}`}
                            className="flex items-center justify-between p-3 bg-white rounded-lg border shadow-sm"
                          >
                            <div className="flex items-center gap-3 flex-1">
                              <div className="flex-1">
                                <div className="flex items-center gap-2">
                                  <span className="font-medium">{getSkillName(userSkill)}</span>
                                  {userSkill.skill_type === 'company' && (
                                    <Badge variant="outline" className="text-xs">Empresa</Badge>
                                  )}
                                </div>
                                
                                <div className="flex items-center gap-2 mt-1">
                                  {userSkill.skill_level ? (
                                    <>
                                      <StarRating level={userSkill.skill_level} size="sm" />
                                      <SkillLevelBadge level={userSkill.skill_level} showStars={false} />
                                    </>
                                  ) : (
                                    <Badge variant="outline" className="text-xs">
                                      <Info className="h-3 w-3 mr-1" />
                                      Sem nível definido
                                    </Badge>
                                  )}
                                  
                                  {userSkill.skill_evidence && (
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <Link className="h-3 w-3 text-blue-500" />
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p className="max-w-xs">{userSkill.skill_evidence}</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  )}
                                </div>
                              </div>
                              
                              <div className="flex items-center gap-1">
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      className="h-8 w-8 p-0 hover:bg-blue-100"
                                      onClick={() => handleEditSkill(userSkill)}
                                    >
                                      <Edit3 className="h-3 w-3" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>Editar nível e evidência</p>
                                  </TooltipContent>
                                </Tooltip>
                                
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      className="h-8 w-8 p-0 hover:bg-red-100"
                                      onClick={() => handleRemoveSkill(userSkill)}
                                      disabled={removeSkillMutation.isPending}
                                    >
                                      <X className="h-3 w-3" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>Remover habilidade</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <Award className="h-12 w-12 text-muted-foreground mx-auto mb-3 opacity-50" />
                          <p className="text-muted-foreground text-sm">Nenhuma habilidade adicionada ainda</p>
                          <p className="text-xs text-muted-foreground mt-1">
                            Adicione suas competências e defina seus níveis de proficiência
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Add Skills Form */}
                  <div className="space-y-4 p-4 border rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <Plus className="h-4 w-4" />
                      Adicionar Nova Habilidade
                    </Label>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label className="text-xs">Habilidade</Label>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="text-xs h-6 px-2"
                            onClick={() => setIsCreatingCustom(!isCreatingCustom)}
                          >
                            {isCreatingCustom ? (
                              <>Voltar para Lista</>
                            ) : (
                              <><Plus className="h-3 w-3 mr-1" />Criar Nova</>
                            )}
                          </Button>
                        </div>
                        
                        {!isCreatingCustom ? (
                          <Select 
                            value={selectedSkill} 
                            onValueChange={setSelectedSkill}
                            disabled={isLoadingAvailable}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder={
                                isLoadingAvailable ? "Carregando..." : "Selecione uma habilidade"
                              } />
                            </SelectTrigger>
                            <SelectContent>
                              {availableSkills
                                .filter(skill => !isSkillSelected(skill.id, skill.skill_type))
                                .map((skill, index) => (
                                  <SelectItem key={`existing-${skill.skill_type}-${skill.id}-${index}`} value={`${skill.skill_type}-${skill.id}`}>
                                    <div className="flex items-center gap-2">
                                      <span>{skill.name}</span>
                                      {skill.skill_type === 'company' && (
                                        <Badge variant="outline" className="text-xs">Empresa</Badge>
                                      )}
                                    </div>
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className="relative">
                            <div className="relative">
                              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                              <Input
                                value={customSkillName}
                                onChange={(e) => handleCustomSkillSearch(e.target.value)}
                                placeholder="Digite o nome da nova habilidade..."
                                className="pl-10"
                              />
                              {isSearching && (
                                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                  <div className="animate-spin h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                                </div>
                              )}
                            </div>
                            
                            {/* Exact match warning */}
                            {customSkillName.length >= 3 && findExactMatch(customSkillName, availableSkills) && (
                              <div className="mt-2 p-2 bg-amber-50 border border-amber-200 rounded-md">
                                <div className="flex items-center gap-2 text-amber-700">
                                  <AlertCircle className="h-4 w-4" />
                                  <span className="text-sm">Esta habilidade já existe! Use a lista acima.</span>
                                </div>
                              </div>
                            )}
                            
                            {/* Search indicator */}
                            {customSkillName.length >= 3 && customSkillName.length < 5 && (
                              <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                                <div className="flex items-center gap-2 text-blue-700">
                                  <Info className="h-4 w-4" />
                                  <span className="text-sm">Continue digitando para buscar habilidades similares...</span>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <Label className="text-xs">Nível (Opcional)</Label>
                        <SkillLevelSelector
                          value={selectedLevel}
                          onValueChange={setSelectedLevel}
                          placeholder="Definir nível..."
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label className="text-xs">Evidência/Certificação (Opcional)</Label>
                      <Input
                        value={selectedEvidence}
                        onChange={(e) => setSelectedEvidence(e.target.value)}
                        placeholder="Ex: Link do certificado, projeto, portfólio..."
                        className="text-sm"
                      />
                    </div>
                    
                    <Button 
                      onClick={handleAddSkill}
                      disabled={
                        (!selectedSkill && !isCreatingCustom) || 
                        (isCreatingCustom && (!customSkillName || customSkillName.length < 3)) ||
                        addSkillMutation.isPending || 
                        createSkillMutation.isPending ||
                        isLoadingAvailable
                      }
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {(addSkillMutation.isPending || createSkillMutation.isPending) ? "Processando..." : 
                       isCreatingCustom ? "Criar e Adicionar" : "Adicionar Habilidade"}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Languages Section - Premium (mantido igual) */}
          <motion.div variants={cardVariants}>
            <Card className="backdrop-blur-sm bg-white/80 shadow-xl border-0 ring-1 ring-black/5">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-t-lg">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <motion.div
                    animate={{ scale: [1, 1.3, 1] }}
                    transition={{ duration: 2.5, repeat: Infinity }}
                  >
                    <Languages className="h-6 w-6 text-blue-600" />
                  </motion.div>
                  Idiomas
                  <Badge variant="secondary" className="ml-auto bg-blue-100 text-blue-700">
                    Global
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Current Languages */}
                  <div>
                    <label className="text-sm font-medium mb-3 block">Seus Idiomas</label>
                    <div className="space-y-2 min-h-[40px]">
                      {(userLanguages || []).length > 0 ? (
                        (userLanguages || []).map((userLanguage) => (
                          <div
                            key={userLanguage.id}
                            className="flex items-center justify-between p-3 bg-gray-50 rounded-md"
                          >
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{userLanguage.language.name}</span>
                              <Badge variant="outline">
                                {getProficiencyLabel(userLanguage.proficiency)}
                              </Badge>
                            </div>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-8 w-8 p-0 hover:bg-red-100"
                              onClick={() => onRemoveLanguage(userLanguage.id)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))
                      ) : (
                        <div className="p-3 bg-gray-50 rounded-md">
                          <span className="text-muted-foreground text-sm">Nenhum idioma adicionado</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Add Languages */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Adicionar Idioma</label>
                    <div className="flex gap-2">
                      <Select 
                        value={selectedLanguage} 
                        onValueChange={setSelectedLanguage}
                      >
                        <SelectTrigger className="flex-1">
                          <SelectValue placeholder="Selecione um idioma" />
                        </SelectTrigger>
                        <SelectContent>
                          {(availableLanguages || [])
                            .filter(language => !isLanguageSelected(language.id))
                            .map((language) => (
                              <SelectItem key={language.id} value={language.id}>
                                {language.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <Select 
                        value={selectedProficiency} 
                        onValueChange={(value) => setSelectedProficiency(value as any)}
                      >
                        <SelectTrigger className="w-[150px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="basic">Básico</SelectItem>
                          <SelectItem value="intermediate">Intermediário</SelectItem>
                          <SelectItem value="advanced">Avançado</SelectItem>
                          <SelectItem value="native">Nativo</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button 
                        onClick={onAddLanguage}
                        disabled={!selectedLanguage || addLanguageMutationPending}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Adicionar
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Modal de Sugestões de Habilidades */}
        <Dialog open={showSuggestions} onOpenChange={setShowSuggestions}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Habilidades Similares Encontradas
              </DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4 py-4">
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <div className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                  <div className="text-sm text-blue-700">
                    <p className="font-medium">Encontramos habilidades similares!</p>
                    <p className="mt-1">Para manter a consistência, recomendamos usar uma das opções abaixo:</p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-3">
                {suggestions.map((suggestion, index) => (
                  <div key={`suggestion-${suggestion.skill_type}-${suggestion.id}-${index}`} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{suggestion.name}</span>
                        {suggestion.skill_type === 'company' && (
                          <Badge variant="outline" className="text-xs">Empresa</Badge>
                        )}
                        <Badge variant="secondary" className="text-xs">
                          {suggestion.similarity}% similar
                        </Badge>
                      </div>
                      {suggestion.commonKeywords.length > 0 && (
                        <div className="mt-1 flex items-center gap-1 text-xs text-muted-foreground">
                          <span>Palavras em comum:</span>
                          <span className="font-medium">{suggestion.commonKeywords.join(', ')}</span>
                        </div>
                      )}
                      {suggestion.userCount && suggestion.userCount > 0 && (
                        <div className="mt-1 text-xs text-muted-foreground">
                          {suggestion.userCount} {suggestion.userCount === 1 ? 'pessoa tem' : 'pessoas têm'} esta habilidade
                        </div>
                      )}
                    </div>
                    <Button
                      onClick={() => handleUseSuggestion(suggestion)}
                      variant="outline"
                      size="sm"
                      className="ml-3"
                    >
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Usar Esta
                    </Button>
                  </div>
                ))}
              </div>
              
              <div className="pt-4 border-t">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Não encontrou o que procura?
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setShowSuggestions(false)}
                    >
                      Voltar
                    </Button>
                    <Button
                      onClick={handleCreateCustomSkill}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Criar "{normalizeSkillName(customSkillName)}"
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Modal de Edição de Habilidade */}
        <Dialog open={!!editingSkill} onOpenChange={() => setEditingSkill(null)}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Edit3 className="h-5 w-5" />
                Editar Habilidade
              </DialogTitle>
            </DialogHeader>
            
            {editingSkill && (
              <div className="space-y-4 py-4">
                <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-md">
                  <Award className="h-4 w-4 text-emerald-600" />
                  <span className="font-medium">{getSkillName(editingSkill)}</span>
                  {editingSkill.skill_type === 'company' && (
                    <Badge variant="outline" className="text-xs">Empresa</Badge>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label>Nível de Proficiência</Label>
                  <SkillLevelSelector
                    value={editLevel}
                    onValueChange={setEditLevel}
                    placeholder="Selecione um nível..."
                  />
                  {editLevel && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <StarRating level={editLevel} size="sm" />
                      <span>Prévia do nível selecionado</span>
                    </div>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label>Evidência/Certificação (Opcional)</Label>
                  <Textarea
                    value={editEvidence}
                    onChange={(e) => setEditEvidence(e.target.value)}
                    placeholder="Ex: Link do certificado, URL do projeto, descrição da experiência..."
                    rows={3}
                    className="resize-none"
                  />
                  {editingSkill.skill_evidence && (
                    <p className="text-xs text-muted-foreground">
                      Evidência atual: {editingSkill.skill_evidence}
                    </p>
                  )}
                </div>
                
                <div className="flex gap-2 pt-4">
                  <Button
                    onClick={handleUpdateSkill}
                    disabled={updateSkillMutation.isPending}
                    className="flex-1"
                  >
                    {updateSkillMutation.isPending ? "Salvando..." : "Salvar Alterações"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setEditingSkill(null)}
                    disabled={updateSkillMutation.isPending}
                  >
                    Cancelar
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </motion.div>
    </TooltipProvider>
  );
} 