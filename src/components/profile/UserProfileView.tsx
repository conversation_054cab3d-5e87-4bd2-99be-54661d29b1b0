/**
 * Componente de Visualização de Perfil Público - Premium Design
 * <AUTHOR> Internet 2025
 */
import { useState } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { EnhancedOptimizedAvatar } from "@/components/common/EnhancedOptimizedAvatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  MessageSquare,
  Building2,
  Briefcase,
  CalendarDays,
  User,
  FileText,
  ThumbsUp,
  Share2,
  Activity,
  Trophy,
  Calendar,
  Sparkles,
  Users,
  Star,
  Crown,
  Phone,
  Mail,
  MapPin,
  Globe,
  Heart,
  Zap,
  Award,
  Target,
  TrendingUp,
  BookOpen,
  Coffee,
  Rocket,
  Brain,
  Languages,
  ArrowLeft,
  Edit
} from "lucide-react";
import { motion } from "framer-motion";
import { useUserLevel } from "@/lib/query/hooks/useUserLevel";
import { useStardustBalance } from "@/lib/query/hooks/useStardust";

// Variantes de animação otimizadas
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.1,
      duration: 0.4
    }
  }
};

const cardVariants = {
  hidden: { opacity: 0, y: 15 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

const heroVariants = {
  hidden: { opacity: 0, scale: 0.98 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

const statsVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

const avatarVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  }
};

interface Profile {
  id: string;
  full_name: string | null;
  avatar_url: string | null;
  job_title_id: string | null;
  job_title_name?: string | null;
  company_id: string | null;
  company_name?: string | null;
  department_id?: string | null;
  department_name?: string | null;
  phone?: string | null;
  bio?: string | null;
}

interface Post {
  id: string;
  content: string;
  created_at: string;
  likes: number | null;
}

interface UserSkill {
  id: string;
  skill_name: string;
  skill_type: 'system' | 'company';
  proficiency_level?: string;
}

interface UserLanguage {
  id: string;
  language: {
    name: string;
    code: string;
  };
  proficiency: string;
}

interface UserProfileViewProps {
  userId: string;
  currentUserId: string;
  onStartChat?: (userId: string) => void;
  isOwnProfile?: boolean;
}

export function UserProfileView({ userId, currentUserId, onStartChat, isOwnProfile = false }: UserProfileViewProps) {
  const [selectedTab, setSelectedTab] = useState("overview");
  const navigate = useNavigate();

  // DEBUG: Log inicial do componente
  console.log('UserProfileView montado:', {
    userId,
    currentUserId,
    isOwnProfile,
    userIdType: typeof userId,
    currentUserIdType: typeof currentUserId
  });

  // Buscar dados do perfil do usuário
  const { data: profile, isLoading: profileLoading, error: profileError } = useQuery({
    queryKey: ["user-profile", userId],
    queryFn: async () => {
      if (!userId) {
        console.error("UserProfileView: ID de usuário inválido", { userId });
        throw new Error("ID de usuário inválido");
      }
      
      console.log("UserProfileView: Buscando dados do perfil para userId:", userId);
      
      // Buscar dados básicos do perfil
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select(`
          id,
          full_name,
          avatar_url,
          job_title_id,
          hire_date,
          company_id,
          department_id,
          phone,
          bio
        `)
        .eq("id", userId)
        .single();

      if (profileError) {
        console.error("UserProfileView: Erro ao buscar perfil:", profileError);
        throw profileError;
      }

      console.log("UserProfileView: Dados do perfil encontrados:", profileData);
      
      const profileResult: Profile = {
        id: profileData.id,
        full_name: profileData.full_name,
        avatar_url: profileData.avatar_url,
        job_title_id: profileData.job_title_id,
        company_id: profileData.company_id,
        department_id: profileData.department_id,
        phone: profileData.phone,
        bio: profileData.bio
      };
      
      // Buscar informações da empresa
      if (profileData.company_id) {
        const { data: companyData } = await supabase
          .from("companies")
          .select("name")
          .eq("id", profileData.company_id)
          .single();
        
        if (companyData) profileResult.company_name = companyData.name;
      }
      
      // Buscar informações do cargo
      if (profileData.job_title_id) {
        const { data: jobTitleData } = await supabase
          .from("job_titles")
          .select("title")
          .eq("id", profileData.job_title_id)
          .single();
        
        if (jobTitleData) profileResult.job_title_name = jobTitleData.title;
      }
      
      // Buscar informações do departamento
      if (profileData.department_id) {
        const { data: departmentData } = await supabase
          .from("departments")
          .select("name")
          .eq("id", profileData.department_id)
          .single();
        
        if (departmentData) profileResult.department_name = departmentData.name;
      }
      
      return profileResult;
    },
    enabled: !!userId,
    retry: 2,
    staleTime: 5 * 60 * 1000, // 5 minutos
    onError: (error) => {
      console.error("UserProfileView: Erro na query do perfil:", error);
    }
  });

  // Buscar habilidades do usuário
  const { data: userSkills } = useQuery({
    queryKey: ["user-skills", userId],
    queryFn: async () => {
      if (!userId) return [] as UserSkill[];
      
      const { data, error } = await supabase
        .from("user_skills")
        .select(`
          user_id,
          skill_id,
          skill_type,
          skills!inner(name)
        `)
        .eq("user_id", userId);

      if (error) {
        console.warn("UserProfileView: Erro ao buscar habilidades (não crítico):", error);
        return [] as UserSkill[]; // Retorna array vazio em vez de lançar erro
      }
      
      return (data || []).map(skill => ({
        id: `${skill.user_id}-${skill.skill_id}`, // Criar ID único baseado na combinação
        skill_name: skill.skills.name,
        skill_type: skill.skill_type,
        proficiency_level: undefined // Não disponível na estrutura atual
      })) as UserSkill[];
    },
    enabled: !!userId,
    retry: 1,
    staleTime: 10 * 60 * 1000, // 10 minutos
    onError: (error) => {
      console.warn("UserProfileView: Erro ao buscar habilidades:", error);
    }
  });

  // Buscar idiomas do usuário
  const { data: userLanguages } = useQuery({
    queryKey: ["user-languages", userId],
    queryFn: async () => {
      if (!userId) return [] as UserLanguage[];
      
      const { data, error } = await supabase
        .from("user_languages")
        .select(`
          id,
          proficiency,
          language_id,
          languages!inner(name, code)
        `)
        .eq("user_id", userId);

      if (error) {
        console.warn("UserProfileView: Erro ao buscar idiomas (não crítico):", error);
        return [] as UserLanguage[]; // Retorna array vazio em vez de lançar erro
      }
      
      return (data || []).map(lang => ({
        id: lang.id,
        language: lang.languages,
        proficiency: lang.proficiency
      })) as UserLanguage[];
    },
    enabled: !!userId,
    retry: 1,
    staleTime: 10 * 60 * 1000, // 10 minutos
    onError: (error) => {
      console.warn("UserProfileView: Erro ao buscar idiomas:", error);
    }
  });

  // Buscar publicações recentes do usuário
  const { data: recentPosts, isLoading: postsLoading } = useQuery({
    queryKey: ["user-recent-posts", userId],
    queryFn: async () => {
      if (!userId) return [] as Post[];
      
      const { data, error } = await supabase
        .from("posts")
        .select(`id, content, created_at, likes`)
        .eq("author_id", userId)
        .eq("status", "published")
        .order("created_at", { ascending: false })
        .limit(5);

      if (error) {
        console.warn("UserProfileView: Erro ao buscar posts recentes (não crítico):", error);
        return [] as Post[]; // Retorna array vazio em vez de lançar erro
      }
      return (data || []) as Post[];
    },
    enabled: !!userId,
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5 minutos
    onError: (error) => {
      console.warn("UserProfileView: Erro ao buscar posts recentes:", error);
    }
  });

  // Buscar estatísticas do usuário
  const { data: stats } = useQuery({
    queryKey: ["user-stats", userId],
    queryFn: async () => {
      if (!userId) return null;

      // 1. Fetch total likes and posts count
      const { data: postsData, error: postsError } = await supabase
        .from("posts")
        .select("likes")
        .eq("author_id", userId)
        .eq("status", "published");

      if (postsError) {
        console.warn("UserProfileView: Erro ao buscar posts/likes (não crítico):", postsError);
      }

      const totalLikes = (postsData || []).reduce((acc, post) => acc + (post.likes || 0), 0);
      const totalPosts = postsData?.length || 0;
      
      return {
        totalPosts: totalPosts,
        totalLikes: totalLikes
        // stardust será obtido do stardustBalance separadamente
        // joinedDate será obtido do profile.hire_date
        // level será obtido do userLevel separadamente
      };
    },
    enabled: !!userId,
    retry: 1,
    staleTime: 2 * 60 * 1000, // 2 minutos
    onError: (error) => {
      console.warn("UserProfileView: Erro ao buscar estatísticas:", error);
    }
  });

  // Buscar nível real do usuário usando o hook centralizado
  const { level: userLevel, isLoading: levelLoading } = useUserLevel({ 
    enabled: !!userId && userId === currentUserId // Só busca se for o próprio perfil
  });

  // Buscar saldo real de stardust usando o hook centralizado
  const { balance: stardustBalance, isLoading: stardustLoading } = useStardustBalance();

  // Função para compartilhar o perfil
  const shareProfile = () => {
    const url = window.location.href;
    const title = `Perfil de ${profile?.full_name || "Usuário"}`;
    
    if (navigator.share) {
      navigator.share({ title, url })
        .then(() => toast.success("Perfil compartilhado!", { description: "Compartilhado com sucesso." }))
        .catch(() => toast.error("Erro ao compartilhar", { description: "Não foi possível compartilhar." }));
    } else {
      // Fallback para copiar o link
      navigator.clipboard.writeText(url).then(() => {
        toast.success("Link copiado!", { description: "URL do perfil copiada para a área de transferência." });
      }).catch(() => {
        toast.error("Erro ao copiar", { description: "Não foi possível copiar o link." });
      });
    }
  };

  // Se há erro na query principal, mostrar erro específico
  if (profileError) {
    console.log('UserProfileView: Erro crítico na query do perfil:', profileError);
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 via-rose-50 to-pink-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="text-center p-8 bg-white rounded-lg shadow-xl max-w-md"
        >
          <User className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-800 mb-2">Erro ao Carregar Perfil</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar os dados do perfil. 
            {profileError.message && ` (${profileError.message})`}
          </p>
          <Button
            onClick={() => window.location.reload()}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            Tentar Novamente
          </Button>
        </motion.div>
      </div>
    );
  }

  if (profileLoading) {
    console.log('UserProfileView: Renderizando estado de loading');
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 via-indigo-50 to-blue-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="text-center"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
            className="mx-auto mb-4 w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full"
          />
          <p className="text-lg font-semibold text-gray-700">Carregando perfil...</p>
          <p className="text-sm text-gray-500">Buscando informações do usuário</p>
        </motion.div>
      </div>
    );
  }

  if (!profile) {
    console.log('UserProfileView: Perfil não encontrado', { userId, profile });
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 via-rose-50 to-pink-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="text-center p-8"
        >
          <User className="h-20 w-20 text-gray-300 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Perfil não encontrado</h2>
          <p className="text-gray-600">O usuário que você está procurando não existe ou foi removido.</p>
        </motion.div>
      </div>
    );
  }

  const svgPattern = `data:image/svg+xml,${encodeURIComponent(
    '<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="#ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="4"/></g></g></svg>'
  )}`;

  // Fallbacks para dados que podem falhar sem quebrar a UI
  const safeStats = {
    totalPosts: stats?.totalPosts || 0,
    totalLikes: stats?.totalLikes || 0,
    stardust: stardustBalance || 0, // Usar saldo real do sistema de stardust
    joinedDate: profile?.hire_date || profile?.created_at || "2024", // Usar hire_date do profile
    level: userLevel?.current_level || 1 // Usar nível real do sistema de gamificação
  };

  const safeUserSkills = userSkills || [];
  const safeUserLanguages = userLanguages || [];
  const safeRecentPosts = recentPosts || [];

  console.log('UserProfileView: Renderizando conteúdo principal', { 
    profile: profile ? { id: profile.id, full_name: profile.full_name } : 'não encontrado',
    stats: safeStats,
    userSkills: safeUserSkills.length,
    userLanguages: safeUserLanguages.length,
    recentPosts: safeRecentPosts.length,
    postsLoading
  });

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="min-h-screen bg-gradient-to-br from-purple-50 via-indigo-50 to-blue-50"
    >
      {/* Hero Header */}
      <motion.div
        variants={heroVariants}
        className="relative bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 text-white overflow-hidden"
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div 
            className="absolute inset-0" 
            style={{ backgroundImage: `url("${svgPattern}")` }}
          />
        </div>
        
        <div className="relative px-6 py-16">
          {/* Botões de Navegação */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
            className="absolute top-4 left-6 right-6 flex items-center justify-between z-20"
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(-1)}
              className="text-white hover:bg-white/20 backdrop-blur-sm"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Voltar
            </Button>

            {isOwnProfile && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate("/profile")}
                className="bg-white/10 hover:bg-white/20 text-white border-white/30 backdrop-blur-sm"
              >
                <Edit className="h-4 w-4 mr-2" />
                Editar
              </Button>
            )}
          </motion.div>

          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col lg:flex-row items-center gap-8">
              {/* Avatar e Info Principal */}
              <div className="flex flex-col lg:flex-row items-center gap-6">
                <motion.div 
                  variants={avatarVariants}
                  className="relative"
                >
                  <div className="absolute inset-0 bg-white/20 rounded-full blur-xl" />
                  <div className="relative">
                    <EnhancedOptimizedAvatar
                      src={profile.avatar_url}
                      fallback={profile.full_name?.charAt(0).toUpperCase() || "U"}
                      alt={profile.full_name || "Avatar do usuário"}
                      className="h-32 w-32 lg:h-40 lg:w-40 border-4 border-white/30 shadow-2xl [&>span]:bg-white [&>span]:text-purple-600 [&>span]:font-bold [&>span]:text-2xl lg:[&>span]:text-3xl"
                      userId={profile.id}
                    />
                  </div>
                  <motion.div
                    animate={{ 
                      rotate: [0, 10, 0, -10, 0],
                      scale: [1, 1.1, 1]
                    }}
                    transition={{ 
                      duration: 4, 
                      repeat: Infinity, 
                      ease: "easeInOut",
                      repeatDelay: 2
                    }}
                    className="absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 p-2 rounded-full shadow-lg"
                  >
                    <Crown className="h-6 w-6 text-white" />
                  </motion.div>
                </motion.div>
                
                <div className="text-center lg:text-left">
                  <motion.h1
                    variants={cardVariants}
                    className="text-3xl lg:text-4xl font-bold mb-2"
                  >
                    {profile.full_name || "Nome não informado"}
                  </motion.h1>
                  
                  <motion.div
                    variants={cardVariants}
                    className="flex flex-wrap items-center justify-center lg:justify-start gap-3 mb-4"
                  >
                    {profile.job_title_name && (
                      <Badge className="bg-white/20 text-white border-white/30 px-4 py-2 flex items-center gap-2">
                        <Briefcase className="h-4 w-4" />
                        {profile.job_title_name}
                      </Badge>
                    )}
                    
                    {profile.department_name && (
                      <Badge className="bg-white/20 text-white border-white/30 px-4 py-2 flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        {profile.department_name}
                      </Badge>
                    )}
                    
                    {profile.company_name && (
                      <Badge className="bg-white/20 text-white border-white/30 px-4 py-2 flex items-center gap-2">
                        <Building2 className="h-4 w-4" />
                        {profile.company_name}
                      </Badge>
                    )}
                  </motion.div>
                  
                  {profile.bio && (
                    <motion.p
                      variants={cardVariants}
                      className="text-white/90 text-lg max-w-2xl"
                    >
                      {profile.bio}
                    </motion.p>
                  )}
                </div>
              </div>

              {/* Stats Cards Melhorados */}
              <div className="flex-1 w-full lg:w-auto">
                <motion.div 
                  variants={containerVariants}
                  className="grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4"
                >
                  <motion.div
                    variants={statsVariants}
                    whileHover={{ scale: 1.02, y: -2 }}
                    className="bg-white/15 backdrop-blur-md rounded-xl p-3 lg:p-4 text-center border border-white/30 shadow-lg hover:bg-white/20 transition-all duration-300"
                  >
                    <motion.div
                      animate={{ scale: [1, 1.05, 1] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    >
                      <FileText className="h-5 w-5 lg:h-6 lg:w-6 text-white mx-auto mb-2" />
                    </motion.div>
                    <div className="text-xl lg:text-2xl font-bold text-white mb-1">{safeStats.totalPosts}</div>
                    <div className="text-white/90 text-xs lg:text-sm font-medium">Publicações</div>
                  </motion.div>
                  
                  <motion.div
                    variants={statsVariants}
                    whileHover={{ scale: 1.02, y: -2 }}
                    className="bg-white/15 backdrop-blur-md rounded-xl p-3 lg:p-4 text-center border border-white/30 shadow-lg hover:bg-white/20 transition-all duration-300"
                  >
                    <motion.div
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                    >
                      <Heart className="h-5 w-5 lg:h-6 lg:w-6 text-white mx-auto mb-2" />
                    </motion.div>
                    <div className="text-xl lg:text-2xl font-bold text-white mb-1">{safeStats.totalLikes}</div>
                    <div className="text-white/90 text-xs lg:text-sm font-medium">Curtidas</div>
                  </motion.div>
                  
                  <motion.div
                    variants={statsVariants}
                    whileHover={{ scale: 1.02, y: -2 }}
                    className="bg-white/15 backdrop-blur-md rounded-xl p-3 lg:p-4 text-center border border-white/30 shadow-lg hover:bg-white/20 transition-all duration-300"
                  >
                    <motion.div
                      animate={{ 
                        rotate: [0, 8, 0, -8, 0],
                        scale: [1, 1.05, 1]
                      }}
                      transition={{ duration: 2.5, repeat: Infinity, ease: "easeInOut" }}
                    >
                      <Sparkles className="h-5 w-5 lg:h-6 lg:w-6 text-white mx-auto mb-2" />
                    </motion.div>
                    <div className="text-xl lg:text-2xl font-bold text-white mb-1">{safeStats.stardust}</div>
                    <div className="text-white/90 text-xs lg:text-sm font-medium">Stardust</div>
                  </motion.div>
                  
                </motion.div>
              </div>
            </div>

            {/* Action Buttons */}
            <motion.div
              variants={cardVariants}
              className="flex flex-wrap justify-center lg:justify-start gap-4 mt-8"
            >
              {!isOwnProfile && currentUserId !== userId && (
                <Button
                  onClick={() => onStartChat?.(userId)}
                  size="lg"
                  className="bg-white text-purple-600 hover:bg-white/90 px-8 py-3 font-semibold shadow-lg"
                >
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Enviar Mensagem
                </Button>
              )}
              
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <Tabs defaultValue="overview" onValueChange={setSelectedTab} className="w-full">
          <TabsList className="grid w-full grid-cols-1 mb-8 bg-white shadow-lg border-0 h-14">
            <TabsTrigger
              value="overview"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-indigo-500 data-[state=active]:text-white font-semibold"
            >
              <User className="h-4 w-4 mr-2" />
              Visão Geral
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-8">
            <motion.div 
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="grid grid-cols-1 lg:grid-cols-3 gap-8"
            >
              {/* Informações Profissionais */}
              <motion.div variants={cardVariants} className="lg:col-span-2">
                <Card className="backdrop-blur-sm bg-white/80 shadow-xl border-0 ring-1 ring-black/5">
                  <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
                    <CardTitle className="flex items-center gap-3 text-xl">
                      <motion.div
                        animate={{ rotateY: [0, 180, 360] }}
                        transition={{ duration: 6, repeat: Infinity, ease: "easeInOut" }}
                      >
                        <Briefcase className="h-6 w-6 text-blue-600" />
                      </motion.div>
                      Informações Profissionais
                      <Badge variant="secondary" className="ml-auto bg-blue-100 text-blue-700">
                        Premium
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6 space-y-6">
                    {profile.job_title_name && (
                      <motion.div 
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 }}
                        className="flex items-start gap-4 p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl border border-blue-200/50"
                      >
                        <div className="bg-blue-100 p-3 rounded-lg">
                          <Briefcase className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-semibold text-gray-800">Cargo</div>
                          <div className="text-gray-600 mt-1">{profile.job_title_name}</div>
                        </div>
                      </motion.div>
                    )}
                    
                    {profile.department_name && (
                      <motion.div 
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.2 }}
                        className="flex items-start gap-4 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border border-purple-200/50"
                      >
                        <div className="bg-purple-100 p-3 rounded-lg">
                          <Users className="h-5 w-5 text-purple-600" />
                        </div>
                        <div>
                          <div className="font-semibold text-gray-800">Departamento</div>
                          <div className="text-gray-600 mt-1">{profile.department_name}</div>
                        </div>
                      </motion.div>
                    )}

                    {/* Habilidades */}
                    {safeUserSkills.length > 0 && (
                      <motion.div 
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3 }}
                        className="p-4 bg-gradient-to-r from-emerald-50 to-green-50 rounded-xl border border-emerald-200/50"
                      >
                        <div className="flex items-start gap-4">
                          <div className="bg-emerald-100 p-3 rounded-lg">
                            <Brain className="h-5 w-5 text-emerald-600" />
                          </div>
                          <div className="flex-1">
                            <div className="font-semibold text-gray-800 mb-3">Habilidades</div>
                                                         <div className="flex flex-wrap gap-2">
                               {safeUserSkills.map((skill) => (
                                 <Badge
                                   key={skill.id}
                                   variant="outline"
                                   className="bg-white/80 border-emerald-300 text-emerald-700 hover:bg-emerald-50"
                                 >
                                   {skill.skill_name}
                                   <span className="ml-1 text-xs opacity-60">
                                     {skill.skill_type === 'system' ? '• Sistema' : '• Empresa'}
                                   </span>
                                 </Badge>
                               ))}
                             </div>
                          </div>
                        </div>
                      </motion.div>
                    )}

                    {/* Idiomas */}
                    {safeUserLanguages.length > 0 && (
                      <motion.div 
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.4 }}
                        className="p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl border border-amber-200/50"
                      >
                        <div className="flex items-start gap-4">
                          <div className="bg-amber-100 p-3 rounded-lg">
                            <Languages className="h-5 w-5 text-amber-600" />
                          </div>
                          <div className="flex-1">
                            <div className="font-semibold text-gray-800 mb-3">Idiomas</div>
                            <div className="flex flex-wrap gap-2">
                              {safeUserLanguages.map((language) => (
                                <Badge
                                  key={language.id}
                                  variant="outline"
                                  className="bg-white/80 border-amber-300 text-amber-700 hover:bg-amber-50"
                                >
                                  {language.language?.name || 'Nome não encontrado'}
                                  {language.proficiency && (
                                    <span className="ml-1 text-xs opacity-75">
                                      ({language.proficiency})
                                    </span>
                                  )}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>

              {/* Level e Progresso */}
              <motion.div variants={cardVariants}>
                <Card className="backdrop-blur-sm bg-white/80 shadow-xl border-0 ring-1 ring-black/5">
                  <CardHeader className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-t-lg">
                    <CardTitle className="flex items-center gap-3 text-xl">
                      <motion.div
                        animate={{ 
                          y: [0, -5, 0],
                          rotate: [0, 5, 0, -5, 0]
                        }}
                        transition={{ 
                          duration: 3, 
                          repeat: Infinity, 
                          ease: "easeInOut",
                          repeatDelay: 1
                        }}
                      >
                        <Rocket className="h-6 w-6 text-orange-600" />
                      </motion.div>
                      Progressão
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6 space-y-6">
                    <div className="text-center">
                      <motion.div 
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                        className="text-3xl font-bold text-orange-600 mb-2"
                      >
                        Nível {safeStats.level}
                      </motion.div>
                      <Progress value={75} className="h-3 mb-4" />
                      <div className="text-sm text-gray-600">
                        750 / 1000 XP para próximo nível
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-3">
                      <motion.div 
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                        className="flex items-center justify-between"
                      >
                        <span className="text-sm font-medium">Stardust Total</span>
                        <span className="text-lg font-bold text-purple-600">
                          {safeStats.stardust}
                        </span>
                      </motion.div>
                      
                      <motion.div 
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5 }}
                        className="flex items-center justify-between"
                      >
                        <span className="text-sm font-medium">Engajamento</span>
                        <Badge className="bg-gradient-to-r from-green-400 to-blue-500 text-white">
                          Alto
                        </Badge>
                      </motion.div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </motion.div>

            {/* Publicações Recentes */}
            <motion.div variants={cardVariants}>
              <Card className="backdrop-blur-sm bg-white/80 shadow-xl border-0 ring-1 ring-black/5">
                <CardHeader className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-t-lg">
                  <CardTitle className="flex items-center gap-3 text-xl">
                    <motion.div
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    >
                      <FileText className="h-6 w-6 text-indigo-600" />
                    </motion.div>
                    Atividade Recente
                    <motion.div
                      animate={{ rotate: [0, 360] }}
                      transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                    >
                      <Sparkles className="h-5 w-5 text-amber-500 ml-auto" />
                    </motion.div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  {postsLoading ? (
                    <div className="text-center py-8">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
                        className="mx-auto w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full mb-4"
                      />
                      <p className="text-gray-600">Carregando atividade...</p>
                    </div>
                  ) : safeRecentPosts.length > 0 ? (
                    <motion.div 
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                      className="space-y-4"
                    >
                      {safeRecentPosts.map((post, index) => (
                        <motion.div
                          key={post.id}
                          variants={cardVariants}
                          whileHover={{ scale: 1.02, y: -2 }}
                          className="p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200/50 hover:shadow-lg transition-all duration-300"
                        >
                          <div className="flex items-start gap-3">
                            <div className="bg-gradient-to-r from-purple-500 to-indigo-500 p-2 rounded-lg">
                              <FileText className="h-4 w-4 text-white" />
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-gray-800">Publicação</div>
                              <div className="text-gray-600 mt-1 text-sm line-clamp-2">
                                {post.content.replace(/<[^>]*>/g, '').substring(0, 120)}
                                {post.content.length > 120 ? '...' : ''}
                              </div>
                              <div className="flex items-center gap-4 mt-3 text-xs">
                                <Badge variant="outline" className="bg-white">
                                  <CalendarDays className="h-3 w-3 mr-1" />
                                  {format(new Date(post.created_at), 'dd MMM yyyy', { locale: ptBR })}
                                </Badge>
                                <Badge variant="outline" className="bg-white">
                                  <ThumbsUp className="h-3 w-3 mr-1" />
                                  {post.likes || 0} curtidas
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </motion.div>
                  ) : (
                    <motion.div 
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="text-center py-12 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl"
                    >
                      <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-600 font-medium">Nenhuma atividade recente</p>
                      <p className="text-gray-500 text-sm">Este usuário ainda não fez publicações.</p>
                    </motion.div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

        </Tabs>
      </div>
    </motion.div>
  );
} 