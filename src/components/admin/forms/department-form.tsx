/**
 * Formulário de Departamento Premium com seletores inteligentes de gestores
 * <AUTHOR> Internet 2025
 */
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ManagerSelect } from "./manager-select";
import { Textarea } from "@/components/ui/textarea";
import { useDepartments } from "@/lib/query/hooks/useDepartments";
import { Skeleton } from "@/components/ui/skeleton";
import { DialogFooter } from "@/components/ui/dialog";
import { Building, FileText, Layers, Crown, Users, X } from "lucide-react";

interface DepartmentFormProps {
  initialValue?: {
    name: string;
    description?: string;
    parent_department_id?: string;
    primary_manager_id?: string;
    substitute_manager_id?: string;
  };
  onSubmit: (values: {
    name: string;
    description?: string;
    parent_department_id?: string;
    primary_manager_id?: string;
    substitute_manager_id?: string;
  }) => void;
  onCancel: () => void;
  submitText?: string;
  isSubmitting?: boolean;
}


export function DepartmentForm({
  initialValue = {
    name: "",
    description: "",
  },
  onSubmit,
  onCancel,
  submitText = "Adicionar",
  isSubmitting = false,
}: DepartmentFormProps) {
  const [values, setValues] = useState(initialValue);
  const { departments, isLoading, isError } = useDepartments();

  // Validação básica
  const isValid = values.name.trim().length > 0;

  const handleSubmit = () => {
    onSubmit(values);
  };

  return (
    <div className="space-y-6">
      {/* Nome do Departamento */}
      <div className="space-y-2">
        <label className="text-sm font-medium flex items-center gap-2">
          <Building className="h-4 w-4 text-blue-500" />
          Nome do Departamento
          <span className="text-red-500">*</span>
        </label>
        <Input
          value={values.name}
          onChange={(e) =>
            setValues((prev) => ({ ...prev, name: e.target.value }))
          }
          placeholder="Ex: Recursos Humanos, Vendas, Marketing..."
          className="transition-all duration-200 focus:ring-2 focus:ring-blue-500/20"
        />
        {values.name.trim().length === 0 && values.name.length > 0 && (
          <p className="text-xs text-red-500">O nome do departamento é obrigatório</p>
        )}
      </div>

      {/* Descrição */}
      <div className="space-y-2">
        <label className="text-sm font-medium flex items-center gap-2">
          <FileText className="h-4 w-4 text-green-500" />
          Descrição
        </label>
        <Textarea
          value={values.description}
          onChange={(e) =>
            setValues((prev) => ({ ...prev, description: e.target.value }))
          }
          placeholder="Descreva as responsabilidades e funções deste departamento..."
          className="resize-none transition-all duration-200 focus:ring-2 focus:ring-green-500/20"
          rows={3}
        />
        <p className="text-xs text-muted-foreground">
          Descrição opcional para melhor identificação do departamento
        </p>
      </div>

      {/* Departamento Superior */}
      <div className="space-y-2">
        <label className="text-sm font-medium flex items-center gap-2">
          <Layers className="h-4 w-4 text-purple-500" />
          Departamento Superior
        </label>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-10 w-full rounded-md" />
            <p className="text-xs text-muted-foreground">Carregando departamentos...</p>
          </div>
        ) : (
          <>
            <Select
              value={values.parent_department_id}
              onValueChange={(value) =>
                setValues((prev) => ({ ...prev, parent_department_id: value }))
              }
            >
              <SelectTrigger className="transition-all duration-200 focus:ring-2 focus:ring-purple-500/20">
                <SelectValue placeholder="Selecione o departamento superior (opcional)" />
              </SelectTrigger>
              <SelectContent
                className="z-[1000001]"
                style={{ zIndex: 1000001 }}
                side="bottom"
                align="start"
                sideOffset={4}
              >
                {isError ? (
                  <SelectItem value="error" disabled>
                    Erro ao carregar departamentos
                  </SelectItem>
                ) : departments.length > 0 ? (
                  departments.map((department) => (
                    <SelectItem key={department.id} value={department.id}>
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-purple-500" />
                        {department.name}
                      </div>
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-department" disabled>
                    Nenhum departamento encontrado
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              Defina a hierarquia organizacional vinculando a um departamento superior
            </p>
          </>
        )}
      </div>

      {/* Divisor Visual */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-gray-200" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-white px-3 text-muted-foreground font-medium flex items-center gap-2">
            <Crown className="h-4 w-4" />
            Gestão e Liderança
          </span>
        </div>
      </div>

      {/* Gestor Principal */}
      <div>
        <ManagerSelect
          value={values.primary_manager_id}
          onChange={(value) => {
            setValues((prev) => ({ 
              ...prev, 
              primary_manager_id: value,
              // Se o novo gerente principal for o mesmo que o substituto, limpar o substituto
              substitute_manager_id: prev.substitute_manager_id === value ? "" : prev.substitute_manager_id
            }));
          }}
          label="Gestor Principal"
          placeholder="Selecionar gestor principal (opcional)"
        />
        <p className="text-xs text-muted-foreground mt-1">
          O gestor principal será responsável pela supervisão e tomada de decisões do departamento
        </p>
      </div>

      {/* Gestor Substituto */}
      <div>
        <ManagerSelect
          value={values.substitute_manager_id}
          onChange={(value) =>
            setValues((prev) => ({ ...prev, substitute_manager_id: value }))
          }
          label="Gestor Substituto"
          placeholder="Selecionar gestor substituto (opcional)"
          excludeId={values.primary_manager_id} // Excluir o gerente principal da lista
        />
        <p className="text-xs text-muted-foreground mt-1">
          O gestor substituto assumirá as responsabilidades em caso de ausência do gestor principal
        </p>
        {values.primary_manager_id && values.substitute_manager_id === values.primary_manager_id && (
          <p className="text-xs text-amber-600 bg-amber-50 p-2 rounded-md border border-amber-200 mt-2">
            <strong>Atenção:</strong> O gestor substituto deve ser diferente do gestor principal
          </p>
        )}
      </div>

      {/* Footer */}
      <div>
        <DialogFooter className="gap-3 pt-4 border-t">
          <Button 
            variant="outline" 
            onClick={onCancel} 
            disabled={isSubmitting}
            className="min-w-[100px]"
          >
            <X className="h-4 w-4 mr-2" />
            Cancelar
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={!isValid || isSubmitting}
            className="min-w-[140px] bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
          >
            {isSubmitting ? (
              <>
                <div className="mr-2">
                  <Users className="h-4 w-4" />
                </div>
                Processando...
              </>
            ) : (
              <>
                <Building className="h-4 w-4 mr-2" />
                {submitText}
              </>
            )}
          </Button>
        </DialogFooter>
      </div>
    </div>
  );
}