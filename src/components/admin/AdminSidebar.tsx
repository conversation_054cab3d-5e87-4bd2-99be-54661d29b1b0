/**
 * AdminSidebar - Menu de navegação lateral administrativo moderno
 * Layout premium integrado com design system do Vindula
 * Utiliza sistema de permissões genéricas com GenericPermissionGate
 * <AUTHOR> Internet 2025
 */
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  Sparkles,
  Users,
  Shield,
  Building2,
  UserCog,
  Settings,
  Palette,
  Trophy,
  ClipboardCheck,
  LayoutGrid,
  ChevronRight,
  Building,
  Briefcase,
  MapPin,
  Flag,
  Layers,
  ArrowLeft,
  Home,
  FileText,
  HelpCircle,
  Zap,
  HardDrive,
  TrendingUp,
  Mail,
  Bot,
  Target,
  Crown,
  ShoppingCart,
  Book,
  CreditCard,
  Key,
  Timer,
} from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import { motion } from "framer-motion";
import { useSidebar } from "@/components/ui/sidebar";
import React, { useState, useEffect } from "react";
import { GenericPermissionGate } from "@/components/auth/GenericPermissionGate";
import { useIsVindulaCompany } from "@/lib/query/hooks/useIsVindulaCompany";
import { useFeatureAvailability } from "@/lib/query/hooks/useFeatureFlags";

type AdminNavItem = {
  title: string;
  icon: React.ElementType;
  href: string;
  category?: string;
  color?: string;
  vindulaOnly?: boolean;
  // Permissões necessárias para ver o item
  permission?: {
    resourceTypeKey: string;
    actionKey: string;
    resourceId?: string;
  };
  // Feature flag para verificação automática
  featureKey?: string;
};

const adminNavItems: AdminNavItem[] = [
  {
    title: "Todas as Funcionalidades",
    icon: LayoutGrid,
    href: "/admin",
    category: "Principal",
    color: "from-slate-500 to-gray-500",
    // Sem permissão específica - sempre visível para quem acessa admin
  },
  {
    title: "Usuários",
    icon: Users,
    href: "/admin/users",
    category: "Usuários e Permissões",
    color: "from-blue-500 to-indigo-500",
    permission: {
      resourceTypeKey: "user",
      actionKey: "view_users",
    },
  },
  {
    title: "Roles de Usuários",
    icon: Shield,
    href: "/admin/roles",
    category: "Usuários e Permissões",
    color: "from-emerald-500 to-green-500",
    permission: {
      resourceTypeKey: "user_role",
      actionKey: "view_user_roles",
    },
  },
  {
    title: "Empresa",
    icon: Building2,
    href: "/admin/company",
    category: "Empresa e Organização",
    color: "from-purple-500 to-violet-500",
    permission: {
      resourceTypeKey: "company",
      actionKey: "edit_company",
    },
  },
  {
    title: "Configurações OAuth",
    icon: Key,
    href: "/admin/oauth",
    category: "Empresa e Organização",
    color: "from-blue-500 to-indigo-500",
    permission: {
      resourceTypeKey: "company_settings",
      actionKey: "oauth_view",
    },
  },
  {
    title: "Domínios Públicos Bloqueados",
    icon: Shield,
    href: "/admin/public-domains",
    category: "Configurações dos Tenants",
    color: "from-red-500 to-orange-500",
    vindulaOnly: true,
    permission: {
      resourceTypeKey: "system_config",
      actionKey: "manage_public_domains",
    },
  },
  {
    title: "Departamentos",
    icon: Building,
    href: "/admin/departments",
    category: "Empresa e Organização",
    color: "from-cyan-500 to-blue-500",
    permission: {
      resourceTypeKey: "department",
      actionKey: "view_department",
    },
  },
  {
    title: "Cargos",
    icon: Briefcase,
    href: "/admin/job-titles",
    category: "Empresa e Organização",
    color: "from-amber-500 to-orange-500",
    permission: {
      resourceTypeKey: "job_title",
      actionKey: "view_job_titles",
    },
  },
  {
    title: "Unidades",
    icon: Building,
    href: "/admin/units",
    category: "Empresa e Organização",
    color: "from-teal-500 to-cyan-500",
    permission: {
      resourceTypeKey: "unit",
      actionKey: "view_units",
    },
  },
  {
    title: "Localizações",
    icon: MapPin,
    href: "/admin/locations",
    category: "Empresa e Organização",
    color: "from-red-500 to-pink-500",
    permission: {
      resourceTypeKey: "location",
      actionKey: "view_locations",
    },
  },
  {
    title: "Assets Visuais",
    icon: Palette,
    href: "/admin/visual-assets",
    category: "Personalização e Engajamento",
    color: "from-pink-500 to-rose-500",
    permission: {
      resourceTypeKey: "visual_asset",
      actionKey: "view_visual_assets",
    },
  },
  {
    title: "Loja Stardust",
    icon: Crown,
    href: "/admin/store-items",
    category: "Personalização e Engajamento",
    color: "from-emerald-500 to-teal-500",
    permission: {
      resourceTypeKey: "admin",
      actionKey: "manage_visual_assets",
    },
  },
  {
    title: "Packs de Emojis",
    icon: Sparkles,
    href: "/admin/reaction-packs",
    category: "Configurações dos Tenants",
    color: "from-purple-600 to-pink-600",
    vindulaOnly: true,
    permission: {
      resourceTypeKey: "visual_asset",
      actionKey: "create_visual_assets",
    },
  },
  {
    title: "Configurações Padrão de XP",
    icon: Trophy,
    href: "/admin/default-xp-actions",
    category: "Configurações dos Tenants",
    color: "from-yellow-600 to-amber-600",
    vindulaOnly: true,
    permission: {
      resourceTypeKey: "system_config",
      actionKey: "manage_default_actions",
    },
  },
  {
    title: "Configurações Padrão de Stardust",
    icon: Sparkles,
    href: "/admin/default-stardust-actions",
    category: "Configurações dos Tenants",
    color: "from-purple-600 to-indigo-600",
    vindulaOnly: true,
    permission: {
      resourceTypeKey: "system_config",
      actionKey: "manage_default_actions",
    },
  },
  {
    title: "Gamificação",
    icon: Trophy,
    href: "/admin/gamification",
    category: "Personalização e Engajamento",
    color: "from-yellow-500 to-amber-500",
    permission: {
      resourceTypeKey: "gamification",
      actionKey: "view_gamification",
    },
  },
  {
    title: "Missões",
    icon: Target,
    href: "/admin/missions",
    category: "Personalização e Engajamento",
    color: "from-emerald-500 to-teal-500",
    featureKey: "missions_feature",
    permission: {
      resourceTypeKey: "missions",
      actionKey: "view_missions",
    },
  },
  {
    title: "Configurações de Email (Quebrado)",
    icon: Mail,
    href: "/admin/email-settings",
    category: "Personalização e Engajamento",
    color: "from-blue-500 to-cyan-500",
    vindulaOnly: true,
    permission: {
      resourceTypeKey: "email_settings",
      actionKey: "view_email_settings",
    },
  },
  {
    title: "Feature Flags",
    icon: Flag,
    href: "/admin/features",
    category: "Configurações dos Tenants",
    color: "from-violet-500 to-purple-500",
    vindulaOnly: true, // Flag especial para Vindula - controla features do sistema
    permission: {
      resourceTypeKey: "system_config",
      actionKey: "manage_features",
    },
  },
  {
    title: "Permissões Padrão",
    icon: Settings,
    href: "/admin/permissions",
    category: "Configurações dos Tenants",
    color: "from-indigo-500 to-blue-500",
    vindulaOnly: true,
    permission: {
      resourceTypeKey: "permission",
      actionKey: "view_permissions",
    },
  },
  {
    title: "Conteúdo de Ajuda",
    icon: HelpCircle,
    href: "/admin/help-content",
    category: "Configurações dos Tenants",
    color: "from-emerald-500 to-teal-500",
    vindulaOnly: true,
    permission: {
      resourceTypeKey: "help_content",
      actionKey: "view_help_content",
    },
  },
  {
    title: "Docs do Vindula",
    icon: Book,
    href: "/admin/docsdovindula",
    category: "Configurações dos Tenants",
    color: "from-purple-500 to-violet-500",
    vindulaOnly: true, // Documentação técnica específica da Vindula
    permission: {
      resourceTypeKey: "documentation",
      actionKey: "view_documentation", 
    },
  },
  {
    title: "Logs de Auditoria",
    icon: Shield,
    href: "/admin/audit-logs",
    category: "Utilização",
    color: "from-red-500 to-orange-500",
    permission: {
      resourceTypeKey: "audit_logs",
      actionKey: "view_audit_logs",
    },
  },
  {
    title: "Gerenciamento de Planos",
    icon: Zap,
    href: "/plan-management",
    category: "Utilização",
    color: "from-blue-500 to-indigo-500",
    permission: {
      resourceTypeKey: "subscription",
      actionKey: "view_subscription",
    },
  },
  {
    title: "Dashboard de Storage (Quebrado)",
    icon: HardDrive,
    href: "/storage",
    category: "Utilização",
    color: "from-emerald-500 to-teal-500",
    vindulaOnly: true,
    permission: {
      resourceTypeKey: "storage",
      actionKey: "view_storage",
    },
  },
  {
    title: "Analytics de IA",
    icon: Bot,
    href: "/admin/ai-analytics",
    category: "Configurações dos Tenants",
    color: "from-purple-500 to-pink-500",
    vindulaOnly: true, // Flag especial para Vindula - dados sensíveis de tenants
    // Sem permissão definida - será filtrado apenas pela empresa
  },
  {
    title: "Dashboard de Emails",
    icon: Mail,
    href: "/admin/emails",
    category: "Configurações dos Tenants",
    color: "from-blue-600 to-purple-600",
    vindulaOnly: true,
    permission: {
      resourceTypeKey: "email_management",
      actionKey: "view_email_dashboard",
    },
  },
  {
    title: "Monitoramento Proativo",
    icon: Shield,
    href: "/admin/monitoring",
    category: "Configurações dos Tenants",
    color: "from-emerald-600 to-teal-600",
    vindulaOnly: true,
    permission: {
      resourceTypeKey: "monitoring",
      actionKey: "view_monitoring_dashboard",
    },
  },
  {
    title: "Leads Comerciais",
    icon: Target,
    href: "/admin/commercial-leads",
    category: "Configurações dos Tenants",
    color: "from-orange-600 to-red-600",
    vindulaOnly: true,
    permission: {
      resourceTypeKey: "commercial_lead",
      actionKey: "view_commercial_leads",
    },
  },
  {
    title: "Monitora‌mento de Trials",
    icon: Timer,
    href: "/admin/trial-monitoring",
    category: "Configurações dos Tenants",
    color: "from-blue-600 to-indigo-600",
    vindulaOnly: true,
    permission: {
      resourceTypeKey: "trial_monitoring",
      actionKey: "view_trial_monitoring",
    },
  },
  {
    title: "Gerenciamento de Tenants",
    icon: Building2,
    href: "/admin/tenants",
    category: "Configurações dos Tenants",
    color: "from-blue-600 to-purple-600",
    vindulaOnly: true, // Flag especial para Vindula
    // Sem permissão definida - será filtrado apenas pela empresa
  },
  {
    title: "Promoções",
    icon: TrendingUp,
    href: "/admin/promotions",
    category: "Recursos Humanos",
    color: "from-amber-500 to-orange-500",
    permission: {
      resourceTypeKey: "promotions",
      actionKey: "promotions_manage",
    },
  },
  {
    title: "Gestão de Ausências",
    icon: ClipboardCheck,
    href: "/admin/absences",
    category: "Recursos Humanos",
    color: "from-rose-500 to-pink-500",
    permission: {
      resourceTypeKey: "user_absence",
      actionKey: "manage_user_absences",
    },
  },
  {
    title: "Marketplace Estratégico",
    icon: Crown,
    href: "/admin/marketplace",
    category: "Personalização e Engajamento",
    color: "from-purple-600 to-pink-600",
    permission: {
      resourceTypeKey: "strategic_marketplace",
      actionKey: "view_strategic_marketplace",
    },
  },
  {
    title: "Vendas de Ofertas",
    icon: ShoppingCart,
    href: "/admin/offer-sales",
    category: "Personalização e Engajamento",
    color: "from-emerald-600 to-green-600",
    permission: {
      resourceTypeKey: "gamification",
      actionKey: "manage_purchases",
    },
  },
  {
    title: "Templates Globais",
    icon: FileText,
    href: "/admin/global-templates",
    category: "Configurações dos Tenants",
    color: "from-indigo-600 to-blue-600",
    vindulaOnly: true,
    permission: {
      resourceTypeKey: "knowledge_hub",
      actionKey: "manage_global_templates",
    },
  },
  {
    title: "Sistema de Cobrança",
    icon: CreditCard,
    href: "/admin/billing",
    category: "Configurações dos Tenants",
    color: "from-emerald-500 to-teal-500",
    vindulaOnly: true,
    permission: {
      resourceTypeKey: "billing",
      actionKey: "manage_billing",
    },
  },
  {
    title: "Navegação Aplicativo Mobile",
    icon: Settings,
    href: "/admin/floating-tab-bar",
    category: "Personalização e Engajamento",
    color: "from-blue-500 to-cyan-500",
    permission: {
      resourceTypeKey: "ui_customization",
      actionKey: "manage_floating_tab_bar",
    },
  },
];

// Configuração de categorias com cores e ícones
const categoryConfig = {
  Principal: {
    color: "from-slate-500 to-gray-500",
    icon: LayoutGrid,
    gradient: "bg-gradient-to-r from-slate-50 to-gray-50",
  },
  "Usuários e Permissões": {
    color: "from-blue-500 to-indigo-500",
    icon: Users,
    gradient: "bg-gradient-to-r from-blue-50 to-indigo-50",
  },
  "Empresa e Organização": {
    color: "from-purple-500 to-violet-500",
    icon: Building2,
    gradient: "bg-gradient-to-r from-purple-50 to-violet-50",
  },
  "Personalização e Engajamento": {
    color: "from-pink-500 to-rose-500",
    icon: Palette,
    gradient: "bg-gradient-to-r from-pink-50 to-rose-50",
  },
  Utilização: {
    color: "from-emerald-500 to-teal-500",
    icon: TrendingUp,
    gradient: "bg-gradient-to-r from-emerald-50 to-teal-50",
  },
  "Recursos Humanos": {
    color: "from-amber-500 to-orange-500",
    icon: Users,
    gradient: "bg-gradient-to-r from-amber-50 to-orange-50",
  },

  "Configurações dos Tenants": {
    color: "from-violet-500 to-purple-500",
    icon: Settings,
    gradient: "bg-gradient-to-r from-violet-50 to-purple-50",
  },
};

// Variantes de animação para categorias
const categoryVariants = {
  hidden: { opacity: 0, y: -10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      staggerChildren: 0.05,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, x: -10 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  },
};

// Hook para verificar se um item deve ser exibido baseado em feature flags
const useAdminItemAvailability = (item: AdminNavItem) => {
  const { data: featureAvailability, isLoading } = useFeatureAvailability(item.featureKey || '');
  
  // Se não tem featureKey, o item está sempre disponível (comportamento padrão)
  if (!item.featureKey) {
    return { 
      isAvailable: true, 
      isLoading: false,
      isDevelopmentOnly: false
    };
  }
  
  // Se tem featureKey, verificar se está habilitada
  const isFeatureEnabled = featureAvailability?.isFeatureEnabled ?? false;
  const isDevelopmentOnly = featureAvailability?.featureFlag?.development_only ?? false;
  
  return { 
    isAvailable: isFeatureEnabled, 
    isLoading,
    isDevelopmentOnly
  };
};

// Componente de item do menu moderno com permissões
const NavItem = React.memo(function NavItem({
  item,
  isActive,
}: {
  item: AdminNavItem;
  isActive: boolean;
}) {
  const navigate = useNavigate();
  const { state } = useSidebar();
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const Icon = item.icon;
  
  // SEMPRE chamar todos os hooks primeiro (regra dos hooks)
  const { isAvailable, isDevelopmentOnly } = useAdminItemAvailability(item);
  
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Definir displayState antes dos useCallback que dependem dele
  const displayState = isMobile ? "collapsed" : state;

  // Memoizar handler de navegação para evitar re-renders - SEMPRE antes da verificação condicional
  const handleNavigation = React.useCallback(() => {
    navigate(item.href);
  }, [navigate, item.href]);

  // Componente do conteúdo do item (sem wrapper de permissão) - MOVER PARA ANTES DA VERIFICAÇÃO CONDICIONAL
  const ItemContent = React.useCallback(() => {
    if (displayState === "collapsed") {
      return (
        <TooltipProvider>
          <Tooltip delayDuration={300}>
            <TooltipTrigger asChild>
              <motion.div
                variants={itemVariants}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="mb-2"
              >
                <div
                  className={cn(
                    "w-12 h-12 rounded-xl cursor-pointer transition-all duration-300 flex items-center justify-center group relative overflow-hidden",
                    isActive
                      ? `bg-gradient-to-r ${item.color} shadow-lg`
                      : "bg-white/80 backdrop-blur-sm border border-gray-200 hover:border-orange-300 hover:shadow-md"
                  )}
                  onClick={handleNavigation}
                >
                  <Icon
                    className={cn(
                      "h-5 w-5 transition-colors",
                      isActive ? "text-white" : "text-gray-600 group-hover:text-orange-500"
                    )}
                  />
                  {isActive && <div className="absolute inset-0 bg-white/10 rounded-xl" />}
                </div>
              </motion.div>
            </TooltipTrigger>
            <TooltipContent
              side="right"
              sideOffset={12}
              className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-xl border-0"
            >
              <div className="flex flex-col">
                <span className="font-semibold">{item.title}</span>
                {item.category && (
                  <span className="text-xs text-gray-300 mt-1">{item.category}</span>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    return (
      <motion.div
        variants={itemVariants}
        whileHover={{ x: 4 }}
        whileTap={{ scale: 0.98 }}
        className="mb-1"
      >
        <div
          className={cn(
            "flex items-center gap-3 p-3 rounded-xl cursor-pointer transition-all duration-300 group relative overflow-hidden",
            isActive
              ? `bg-gradient-to-r ${item.color} text-white shadow-lg`
              : "bg-white/60 backdrop-blur-sm border border-gray-200 hover:border-orange-300 hover:bg-white/80 hover:shadow-md text-gray-700"
          )}
          onClick={handleNavigation}
        >
          <Icon
            className={cn(
              "h-5 w-5 transition-colors",
              isActive ? "text-white" : "text-gray-600 group-hover:text-orange-500"
            )}
          />
          <span
            className={cn(
              "font-medium transition-colors",
              isActive ? "text-white" : "text-gray-700 group-hover:text-gray-900"
            )}
          >
            {item.title}
          </span>
          {isActive && <ChevronRight className="h-4 w-4 ml-auto text-white" />}
          {isActive && <div className="absolute inset-0 bg-white/10 rounded-xl" />}
        </div>
      </motion.div>
    );
  }, [displayState, isActive, item, Icon, handleNavigation]);
  
  // Só depois da execução de todos os hooks, fazer verificações condicionais
  if (!isAvailable) {
    return null;
  }

  // TEMPORARIAMENTE DESABILITADO - GenericPermissionGate causa loop infinito
  // TODO: Implementar verificação de permissão que não cause loops
  // if (item.permission) {
  //   return (
  //     <GenericPermissionGate>...</GenericPermissionGate>
  //   );
  // }

  // Se não tem permissão definida, mostrar sempre
  return <ItemContent />;
});

export const AdminSidebar = React.memo(function AdminSidebar({
  className,
}: {
  className?: string;
}) {
  const navigate = useNavigate();
  const location = useLocation();
  const { state } = useSidebar();
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const { isVindulaCompany } = useIsVindulaCompany();


  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Agrupar itens por categoria - memoizado para evitar recalculação
  const groupedItems = React.useMemo(() => {
    const categories = [
      "Principal",
      "Usuários e Permissões",
      "Empresa e Organização",
      "Personalização e Engajamento",
      "Utilização",
      "Recursos Humanos",
      "Configurações dos Tenants",
    ];

    return categories
      .map(category => {
        const items = adminNavItems.filter(item => {
          // Filtro básico por categoria
          if (item.category !== category) return false;
          
          // Filtro por empresa Vindula
          if (item.vindulaOnly && !isVindulaCompany) return false;
          
          // Verificar feature flag se definida
          if (item.featureKey) {
            // Para o cálculo da lista, assumimos que items com featureKey podem estar disponíveis
            // A verificação real acontece no componente NavItem
          }
          
          return true;
        });
        
        return {
          category,
          items,
          config: categoryConfig[category as keyof typeof categoryConfig],
        };
      })
      .filter(group => group.items.length > 0);
  }, [isVindulaCompany]); // Dependência do isVindulaCompany

  const displayState = isMobile ? "collapsed" : state;

  // Memoizar handler de navegação para feed
  const handleBackToFeed = React.useCallback(() => {
    navigate("/feed");
  }, [navigate]);

  return (
    <div
      className={cn(
        "h-full bg-gradient-to-b from-slate-50/50 to-white/50 backdrop-blur-sm relative z-10 overflow-hidden",
        displayState === "collapsed" ? "w-20" : "w-64",
        className
      )}
    >
      <div className="h-[calc(100vh-160px)] p-4 overflow-y-auto no-scrollbar">
        <motion.div
        initial="hidden"
        animate="visible"
        variants={{
          hidden: { opacity: 0 },
          visible: {
            opacity: 1,
            transition: {
              staggerChildren: 0.1,
              delayChildren: 0.1,
            },
          },
        }}
        className="space-y-6"
      >
        {groupedItems.map((group, groupIndex) => {
          const CategoryIcon = group.config.icon;

          return (
            <motion.div key={group.category} variants={categoryVariants}>
              {/* Header da categoria moderno */}
              {displayState !== "collapsed" && (
                <motion.div
                  className={cn(
                    "flex items-center gap-2 p-2 rounded-lg mb-3",
                    group.config.gradient
                  )}
                  variants={itemVariants}
                >
                  <div className={cn("p-1.5 rounded-md bg-gradient-to-r", group.config.color)}>
                    <CategoryIcon className="h-4 w-4 text-white" />
                  </div>
                  <h3 className="text-sm font-semibold text-gray-700 uppercase tracking-wide">
                    {group.category}
                  </h3>
                </motion.div>
              )}

              {/* Items da categoria */}
              <motion.div
                className={cn(displayState === "collapsed" ? "space-y-2" : "space-y-1")}
                variants={{
                  hidden: { opacity: 0 },
                  visible: {
                    opacity: 1,
                    transition: {
                      staggerChildren: 0.03,
                    },
                  },
                }}
              >
                {group.items.map(item => {
                  const isActive =
                    location.pathname === item.href ||
                    (item.href !== "/admin" && location.pathname.startsWith(item.href));

                  return <NavItem key={item.href} item={item} isActive={isActive} />;
                })}
              </motion.div>
            </motion.div>
          );
        })}

        {/* Botão de voltar ao feed no sidebar expandido */}
        {displayState !== "collapsed" && (
          <motion.div variants={itemVariants} className="pt-4 border-t border-gray-200">
            <div
              className="flex items-center gap-3 p-3 rounded-xl cursor-pointer transition-all duration-300 group bg-gradient-to-r from-orange-100 to-amber-100 hover:from-orange-200 hover:to-amber-200"
              onClick={handleBackToFeed}
            >
              <ArrowLeft className="h-5 w-5 text-orange-600" />
              <span className="font-medium text-orange-700 group-hover:text-orange-800">
                Voltar ao Feed
              </span>
            </div>
          </motion.div>
        )}

        {/* Botão de voltar ao feed no sidebar collapsed com tooltip */}
        {displayState === "collapsed" && (
          <motion.div variants={itemVariants} className="pt-4 border-t border-gray-200">
            <TooltipProvider>
              <Tooltip delayDuration={300}>
                <TooltipTrigger asChild>
                  <div
                    className="w-12 h-12 rounded-xl cursor-pointer transition-all duration-300 flex items-center justify-center group bg-gradient-to-r from-orange-100 to-amber-100 hover:from-orange-200 hover:to-amber-200 hover:scale-105"
                    onClick={handleBackToFeed}
                  >
                    <ArrowLeft className="h-5 w-5 text-orange-600" />
                  </div>
                </TooltipTrigger>
                <TooltipContent
                  side="right"
                  sideOffset={12}
                  className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-xl border-0"
                >
                  <span className="font-semibold">Voltar ao Feed</span>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </motion.div>
        )}
      </motion.div>
      </div>
    </div>
  );
});

// Versão mobile simplificada e moderna
export const MobileAdminNav = React.memo(function MobileAdminNav() {
  const navigate = useNavigate();
  const location = useLocation();
  const { isVindulaCompany } = useIsVindulaCompany();

  // Memoizar handler de navegação para feed
  const handleBackToFeed = React.useCallback(() => {
    navigate("/feed");
  }, [navigate]);

  // Filtrar itens para mobile baseado em vindulaOnly e feature flags
  const filteredNavItems = React.useMemo(() => {
    return adminNavItems.filter(item => {
      // Filtro por empresa Vindula
      if (item.vindulaOnly && !isVindulaCompany) return false;
      
      // Para mobile, também incluímos items com featureKey
      // A verificação real de disponibilidade acontece no render
      return true;
    });
  }, [isVindulaCompany]);

  return (
    <div className="md:hidden w-full bg-gradient-to-r from-slate-50 to-white border-b border-gray-200 shadow-sm">
      <div className="overflow-x-auto py-3 px-4 flex gap-2 no-scrollbar">
        {/* Botão de voltar moderno */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="flex-shrink-0"
        >
          <div
            className="flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer transition-all bg-gradient-to-r from-orange-100 to-amber-100 hover:from-orange-200 hover:to-amber-200 whitespace-nowrap"
            onClick={handleBackToFeed}
          >
            <ArrowLeft className="h-4 w-4 text-orange-600" />
            <span className="text-sm font-medium text-orange-700">Voltar</span>
          </div>
        </motion.div>

        {/* Items do menu mobile com permissões e feature flags */}
        {filteredNavItems.map((item, index) => {
          // Hook para verificar disponibilidade dentro do componente funcional
          const ItemWithAvailabilityCheck = () => {
            const { isAvailable } = useAdminItemAvailability(item);
            
            // Se não está disponível, não renderizar
            if (!isAvailable) {
              return null;
            }
            
            const isActive =
              location.pathname === item.href ||
              (item.href !== "/admin" && location.pathname.startsWith(item.href));

            // Função simples para navegação (sem useCallback dentro do loop)
            const handleItemNavigation = () => {
              navigate(item.href);
            };

            return (
              <motion.div
                key={item.href}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="flex-shrink-0"
              >
                <div
                  className={cn(
                    "flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer transition-all whitespace-nowrap",
                    isActive
                      ? `bg-gradient-to-r ${item.color} text-white shadow-md`
                      : "bg-white/80 border border-gray-200 hover:border-orange-300 hover:bg-white text-gray-700"
                  )}
                  onClick={handleItemNavigation}
                >
                  <item.icon className={cn("h-4 w-4", isActive ? "text-white" : "text-gray-600")} />
                  <span
                    className={cn("text-sm font-medium", isActive ? "text-white" : "text-gray-700")}
                  >
                    {item.title}
                  </span>
                </div>
              </motion.div>
            );
          };

          return <ItemWithAvailabilityCheck key={item.href} />;
        })}
      </div>
    </div>
  );
});
