/**
 * Componente para gerenciar usuários de um cargo específico
 * Permite visualizar, adicionar e remover usuários do cargo
 * <AUTHOR> Internet 2025
 */
import { useState, useMemo, useCallback } from "react";
import { motion } from "framer-motion";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuthStore } from "@/stores/authStore";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogOverlay,
  DialogPortal,
} from "@/components/ui/dialog";
import { DataTablePagination } from "@/components/ui/data-table/DataTablePagination";
import { successWithNotification, errorWithNotification } from "@/lib/notifications/toastWithNotification";
import {
  Users,
  UserPlus,
  UserMinus,
  Search,
  Loader2,
  Award,
  UserCheck
} from "lucide-react";

interface JobTitleMembersProps {
  jobTitle: string;
  jobTitleId: string;
}

interface User {
  id: string;
  full_name: string;
  email: string;
  avatar_url?: string;
  job_title_id?: string;
  job_title_name?: string;
  active: boolean;
}

interface PaginationState {
  page: number;
  pageSize: number;
}

const rowVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.2, ease: "easeOut" }
  }
};

export function JobTitleMembers({ jobTitle, jobTitleId }: JobTitleMembersProps) {
  const queryClient = useQueryClient();
  const company_id = useAuthStore((state) => state.company_id);
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [pagination, setPagination] = useState<PaginationState>({ page: 1, pageSize: 10 });

  // Buscar usuários com este cargo
  const { data: currentMembers = [], isLoading: isLoadingMembers } = useQuery({
    queryKey: ["job_title_members", jobTitleId, company_id],
    queryFn: async () => {
      if (!company_id || !jobTitleId) return [];
      
      const { data, error } = await supabase
        .from("profiles")
        .select(`
          id, 
          full_name, 
          email, 
          avatar_url, 
          job_title_id,
          active,
          job_titles!inner(title)
        `)
        .eq("company_id", company_id)
        .eq("job_title_id", jobTitleId)
        .order("full_name");
        
      if (error) throw error;
      
      // Transformar dados para incluir nome do cargo
      return data.map(user => ({
        ...user,
        job_title_name: user.job_titles?.title || jobTitle
      })) as User[];
    },
    enabled: !!company_id && !!jobTitleId,
  });

  // Buscar usuários disponíveis (sem cargo ou com cargo diferente)
  const { data: availableUsers = [], isLoading: isLoadingAvailable } = useQuery({
    queryKey: ["available_users", company_id, jobTitleId],
    queryFn: async () => {
      if (!company_id || !jobTitleId) return [];
      
      const { data, error } = await supabase
        .from("profiles")
        .select(`
          id, 
          full_name, 
          email, 
          avatar_url, 
          job_title_id,
          active,
          job_titles(title)
        `)
        .eq("company_id", company_id)
        .eq("active", true)
        .or(`job_title_id.is.null,job_title_id.neq.${jobTitleId}`)
        .order("full_name");
        
      if (error) throw error;
      
      // Transformar dados para incluir nome do cargo
      return data.map(user => ({
        ...user,
        job_title_name: user.job_titles?.title || null
      })) as User[];
    },
    enabled: !!company_id && !!jobTitleId && showAddDialog,
  });

  // Filtrar usuários disponíveis
  const filteredAvailableUsers = useMemo(() => {
    if (!availableUsers) return [];
    
    let filtered = [...availableUsers];
    
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(user => 
        user.full_name.toLowerCase().includes(searchLower) ||
        user.email.toLowerCase().includes(searchLower)
      );
    }
    
    return filtered;
  }, [availableUsers, searchTerm]);

  // Paginação
  const paginatedAvailableUsers = useMemo(() => {
    const startIndex = (pagination.page - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredAvailableUsers.slice(startIndex, endIndex);
  }, [filteredAvailableUsers, pagination]);

  // Mutation para atribuir cargo
  const assignJobTitleMutation = useMutation({
    mutationFn: async (userId: string) => {
      const { error } = await supabase
        .from("profiles")
        .update({ job_title_id: jobTitleId })
        .eq("id", userId);
        
      if (error) throw error;
      
      return userId;
    },
    onSuccess: () => {
      successWithNotification("Usuário adicionado!", {
        description: `O usuário foi adicionado ao cargo "${jobTitle}" com sucesso`,
        persist: false
      });
      queryClient.invalidateQueries({ queryKey: ["job_title_members"] });
      queryClient.invalidateQueries({ queryKey: ["available_users"] });
      queryClient.invalidateQueries({ queryKey: ["users_by_job_title"] });
    },
    onError: (error: Error) => {
      errorWithNotification("Erro ao adicionar usuário", {
        description: error.message || "Não foi possível adicionar o usuário ao cargo",
        persist: true
      });
    }
  });

  // Mutation para remover cargo
  const removeJobTitleMutation = useMutation({
    mutationFn: async (userId: string) => {
      const { error } = await supabase
        .from("profiles")
        .update({ job_title_id: null })
        .eq("id", userId);
        
      if (error) throw error;
      
      return userId;
    },
    onSuccess: () => {
      successWithNotification("Usuário removido!", {
        description: `O usuário foi removido do cargo "${jobTitle}" com sucesso`,
        persist: false
      });
      queryClient.invalidateQueries({ queryKey: ["job_title_members"] });
      queryClient.invalidateQueries({ queryKey: ["available_users"] });
      queryClient.invalidateQueries({ queryKey: ["users_by_job_title"] });
    },
    onError: (error: Error) => {
      errorWithNotification("Erro ao remover usuário", {
        description: error.message || "Não foi possível remover o usuário do cargo",
        persist: true
      });
    }
  });

  // Handlers
  const handleAssignUser = useCallback((userId: string) => {
    assignJobTitleMutation.mutate(userId);
  }, [assignJobTitleMutation]);

  const handleRemoveUser = useCallback((userId: string) => {
    removeJobTitleMutation.mutate(userId);
  }, [removeJobTitleMutation]);

  const handlePageChange = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, page }));
  }, []);

  const handlePageSizeChange = useCallback((pageSize: number) => {
    setPagination({ page: 1, pageSize });
  }, []);

  if (isLoadingMembers) {
    return (
      <div className="flex items-center justify-center py-10">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-amber-500 mx-auto" />
          <p className="text-muted-foreground">Carregando usuários...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com estatísticas */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Award className="h-5 w-5 text-amber-500" />
            Usuários do Cargo
          </h3>
          <p className="text-sm text-muted-foreground">
            {currentMembers.length} usuário(s) com o cargo "{jobTitle}"
          </p>
        </div>
        
        <Button 
          onClick={() => setShowAddDialog(true)}
          className="bg-amber-500 hover:bg-amber-600"
        >
          <UserPlus className="h-4 w-4 mr-2" />
          Adicionar Usuário
        </Button>
      </div>

      {/* Lista atual de usuários com o cargo */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Usuário</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-12">Ação</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentMembers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-10">
                  <div className="text-center space-y-2">
                    <UserCheck className="h-8 w-8 text-muted-foreground mx-auto" />
                    <p className="text-muted-foreground">Nenhum usuário com este cargo</p>
                    <p className="text-sm text-muted-foreground">
                      Clique em "Adicionar Usuário" para começar
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              currentMembers.map((user, index) => (
                <motion.tr
                  key={user.id}
                  variants={rowVariants}
                  initial="hidden"
                  animate="visible"
                  transition={{ delay: index * 0.05 }}
                >
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.avatar_url} />
                        <AvatarFallback className="bg-amber-100 text-amber-600">
                          {user.full_name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{user.full_name}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Badge variant={user.active ? "default" : "secondary"}>
                      {user.active ? "Ativo" : "Inativo"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveUser(user.id)}
                      disabled={removeJobTitleMutation.isPending}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      {removeJobTitleMutation.isPending ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <UserMinus className="h-3 w-3" />
                      )}
                    </Button>
                  </TableCell>
                </motion.tr>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Dialog para adicionar usuários - z-index corrigido */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogPortal>
          <DialogOverlay className="!z-[1000000]" />
          <DialogContent className="max-w-2xl !z-[1000000]">
          <DialogHeader>
            <DialogTitle>Adicionar Usuário ao Cargo</DialogTitle>
            <DialogDescription>
              Selecione usuários para atribuir o cargo "{jobTitle}"
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* Busca */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar usuários..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Lista de usuários disponíveis */}
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Usuário</TableHead>
                    <TableHead>Cargo Atual</TableHead>
                    <TableHead className="w-12">Ação</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoadingAvailable ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-10">
                        <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                        <p className="text-sm text-muted-foreground mt-2">Carregando usuários...</p>
                      </TableCell>
                    </TableRow>
                  ) : paginatedAvailableUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-10">
                        <div className="text-muted-foreground">
                          {searchTerm ? "Nenhum usuário encontrado" : "Todos os usuários já possuem este cargo"}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    paginatedAvailableUsers.map((user, index) => (
                      <motion.tr
                        key={user.id}
                        variants={rowVariants}
                        initial="hidden"
                        animate="visible"
                        transition={{ delay: index * 0.05 }}
                      >
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={user.avatar_url} />
                              <AvatarFallback className="bg-blue-100 text-blue-600">
                                {user.full_name.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{user.full_name}</p>
                              <p className="text-sm text-muted-foreground">{user.email}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {user.job_title_name ? (
                            <Badge variant="outline">{user.job_title_name}</Badge>
                          ) : (
                            <span className="text-muted-foreground text-sm">Sem cargo</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            onClick={() => handleAssignUser(user.id)}
                            disabled={assignJobTitleMutation.isPending}
                            className="bg-green-500 hover:bg-green-600"
                          >
                            {assignJobTitleMutation.isPending ? (
                              <Loader2 className="h-3 w-3 animate-spin" />
                            ) : (
                              <UserPlus className="h-3 w-3" />
                            )}
                          </Button>
                        </TableCell>
                      </motion.tr>
                    ))
                  )}
                </TableBody>
              </Table>

              {/* Paginação para usuários disponíveis */}
              {filteredAvailableUsers.length > 0 && (
                <div className="border-t p-4">
                  <DataTablePagination
                    currentPage={pagination.page}
                    pageSize={pagination.pageSize}
                    totalItems={filteredAvailableUsers.length}
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                  />
                </div>
              )}
            </div>
          </div>
        </DialogContent>
        </DialogPortal>
      </Dialog>
    </div>
  );
} 