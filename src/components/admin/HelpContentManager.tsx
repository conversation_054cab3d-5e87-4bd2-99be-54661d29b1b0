/**
 * Interface administrativa para gestão de conteúdo de ajuda
 * Permite criar, editar e organizar todos os dados da Central de Ajuda
 * <AUTHOR> Internet 2025
 */
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  HelpCircle,
  Save,
  X,
  MessageSquare,
  BookOpen,
  Video,
  FileText,
  Users,
  Settings,
  Shield,
  Database,
  Lightbulb
} from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  HelpSectionsTab,
  HelpGuidesTab,
  HelpFeaturesTab,
  HelpTipsTab,
  HelpResourcesTab
} from '@/components/admin/help';
import {
  useHelpSections,
  useHelpResources,
  useHelpGuides,
  useCreateHelpSection,
  useUpdateHelpSection,
  useDeleteHelpSection,
  useCreateHelpFeature,
  useUpdateHelpFeature,
  useDeleteHelpFeature,
  useCreateHelpTip,
  useUpdateHelpTip,
  useDeleteHelpTip,
  useCreateHelpResource,
  useCreateHelpGuide,
  useUpdateHelpGuide,
  useDeleteHelpGuide
} from '@/lib/query/hooks/useHelpContent';
import {
  HelpSection,
  HelpFeature,
  HelpTip,
  HelpGuide,
  HelpResource,
  HelpSectionCreateData,
  HelpFeatureCreateData,
  HelpTipCreateData,
  HelpGuideCreateData,
  HelpResourceCreateData
} from '@/types/help';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { HeroSection } from '@/components/common/HeroSection';
import { ArrowLeft } from 'lucide-react';

// Configurações de páginas disponíveis
const PAGE_OPTIONS = [
  { value: 'feed', label: 'Feed & Publicações', emoji: '📢', icon: MessageSquare },
  { value: 'knowledge', label: 'Knowledge Hub', emoji: '📚', icon: BookOpen },
  { value: 'chat', label: 'Chat & Comunicação', emoji: '💬', icon: MessageSquare },
  { value: 'tasks', label: 'Gerenciador de Tasks', emoji: '✅', icon: FileText },
  { value: 'biblioteca', label: 'Biblioteca de Arquivos', emoji: '📁', icon: Database },
  { value: 'equipe', label: 'Gestão de Equipe', emoji: '👥', icon: Users },
  { value: 'admin', label: 'Administração', emoji: '⚙️', icon: Settings },
  { value: 'security', label: 'Segurança & Privacidade', emoji: '🔒', icon: Shield },
];

const RESOURCE_TYPES = [
  { value: 'video', label: 'Vídeo Tutorial', emoji: '📹', icon: Video, color: 'text-blue-500' },
  { value: 'article', label: 'Artigo/Documentação', emoji: '📖', icon: FileText, color: 'text-green-500' },
  { value: 'live_support', label: 'Suporte ao Vivo', emoji: '💬', icon: MessageSquare, color: 'text-purple-500' },
  { value: 'emergency', label: 'Suporte Emergencial', emoji: '🆘', icon: HelpCircle, color: 'text-red-500' },
];

const TIP_TYPES = [
  { value: 'keyboard_shortcut', label: 'Atalho de Teclado', emoji: '⌨️' },
  { value: 'best_practice', label: 'Melhores Práticas', emoji: '✨' },
  { value: 'warning', label: 'Aviso Importante', emoji: '⚠️' },
  { value: 'info', label: 'Informação Geral', emoji: 'ℹ️' },
];

export function HelpContentManager() {
  const [activeTab, setActiveTab] = useState('sections');
  const [editingSection, setEditingSection] = useState<HelpSection | null>(null);
  const [editingFeature, setEditingFeature] = useState<HelpFeature | null>(null);
  const [editingTip, setEditingTip] = useState<HelpTip | null>(null);
  const [editingResource, setEditingResource] = useState<HelpResource | null>(null);
  const [editingGuide, setEditingGuide] = useState<HelpGuide | null>(null);
  const [showSectionDialog, setShowSectionDialog] = useState(false);
  const [showResourceDialog, setShowResourceDialog] = useState(false);
  const [showFeatureDialog, setShowFeatureDialog] = useState(false);
  const [showTipDialog, setShowTipDialog] = useState(false);
  const [showGuideDialog, setShowGuideDialog] = useState(false);
  const [selectedSectionForFeature, setSelectedSectionForFeature] = useState<string>('');

  // Queries
  const { data: sections = [], isLoading: sectionsLoading, refetch: refetchSections } = useHelpSections();
  const { data: resources = [], isLoading: resourcesLoading, refetch: refetchResources } = useHelpResources();
  const { data: guides = [], isLoading: guidesLoading, refetch: refetchGuides } = useHelpGuides();

  // Mutations
  const createSectionMutation = useCreateHelpSection();
  const updateSectionMutation = useUpdateHelpSection();
  const deleteSectionMutation = useDeleteHelpSection();
  const createFeatureMutation = useCreateHelpFeature();
  const updateFeatureMutation = useUpdateHelpFeature();
  const deleteFeatureMutation = useDeleteHelpFeature();
  const createTipMutation = useCreateHelpTip();
  const updateTipMutation = useUpdateHelpTip();
  const deleteTipMutation = useDeleteHelpTip();
  const createResourceMutation = useCreateHelpResource();
  const createGuideMutation = useCreateHelpGuide();
  const updateGuideMutation = useUpdateHelpGuide();
  const deleteGuideMutation = useDeleteHelpGuide();

  // Form states
  const [sectionForm, setSectionForm] = useState<HelpSectionCreateData>({
    page_key: '',
    title: '',
    emoji: '',
    icon_name: '',
    description: '',
    display_order: 0,
    is_active: true,
  });

  const [featureForm, setFeatureForm] = useState<HelpFeatureCreateData>({
    help_section_id: '',
    feature_text: '',
    display_order: 0,
    is_active: true,
  });

  const [tipForm, setTipForm] = useState<HelpTipCreateData>({
    help_section_id: '',
    tip_text: '',
    tip_type: 'keyboard_shortcut',
    is_active: true,
  });

  const [resourceForm, setResourceForm] = useState<HelpResourceCreateData>({
    resource_type: 'video',
    title: '',
    description: '',
    icon_name: '',
    url_or_content: '',
    display_order: 0,
    is_active: true,
  });

  const [guideForm, setGuideForm] = useState<HelpGuideCreateData>({
    category_key: '',
    title: '',
    description: '',
    icon_name: '',
    topics: [],
    content: '',
    display_order: 0,
    is_active: true,
  });

  // Reset forms
  const resetSectionForm = () => {
    setSectionForm({
      page_key: '',
      title: '',
      emoji: '',
      icon_name: '',
      description: '',
      display_order: 0,
      is_active: true,
    });
    setEditingSection(null);
  };

  const resetFeatureForm = () => {
    setFeatureForm({
      help_section_id: '',
      feature_text: '',
      display_order: 0,
      is_active: true,
    });
    setEditingFeature(null);
  };

  const resetTipForm = () => {
    setTipForm({
      help_section_id: '',
      tip_type: 'info',
      tip_text: '',
      is_active: true,
    });
    setEditingTip(null);
  };

  const resetResourceForm = () => {
    setResourceForm({
      resource_type: 'video',
      title: '',
      description: '',
      icon_name: '',
      url_or_content: '',
      display_order: 0,
      is_active: true,
    });
    setEditingResource(null);
  };

  const resetGuideForm = () => {
    setGuideForm({
      category_key: '',
      title: '',
      description: '',
      icon_name: '',
      topics: [],
      content: '',
      display_order: 0,
      is_active: true,
    });
    setEditingGuide(null);
  };

  // Handlers para seções
  const handleSaveSection = async () => {
    try {
      if (editingSection) {
        await updateSectionMutation.mutateAsync({
          id: editingSection.id,
          data: sectionForm
        });
        toast.success('Seção atualizada com sucesso!');
      } else {
        await createSectionMutation.mutateAsync(sectionForm);
        toast.success('Seção criada com sucesso!');
      }
      
      resetSectionForm();
      setShowSectionDialog(false);
      refetchSections();
    } catch (error) {
      toast.error('Erro ao salvar seção');
      console.error('Erro:', error);
    }
  };

  const handleEditSection = (section: HelpSection) => {
    setEditingSection(section);
    setSectionForm({
      page_key: section.page_key,
      title: section.title,
      emoji: section.emoji || '',
      icon_name: section.icon_name || '',
      description: section.description,
      display_order: section.display_order,
      is_active: section.is_active,
    });
    setShowSectionDialog(true);
  };

  const handleDeleteSection = async (id: string) => {
    if (confirm('Tem certeza que deseja excluir esta seção? Todas as funcionalidades e dicas relacionadas também serão excluídas.')) {
      try {
        await deleteSectionMutation.mutateAsync(id);
        toast.success('Seção excluída com sucesso!');
        refetchSections();
      } catch (error) {
        toast.error('Erro ao excluir seção');
        console.error('Erro:', error);
      }
    }
  };

  // Handlers para funcionalidades
  const handleSaveFeature = async () => {
    try {
      if (editingFeature) {
        await updateFeatureMutation.mutateAsync({
          id: editingFeature.id,
          data: featureForm
        });
        toast.success('Funcionalidade atualizada com sucesso!');
      } else {
        await createFeatureMutation.mutateAsync(featureForm);
        toast.success('Funcionalidade criada com sucesso!');
      }
      
      resetFeatureForm();
      setShowFeatureDialog(false);
      refetchSections();
    } catch (error) {
      toast.error('Erro ao salvar funcionalidade');
      console.error('Erro:', error);
    }
  };

  const handleEditFeature = (feature: HelpFeature) => {
    setEditingFeature(feature);
    setFeatureForm({
      help_section_id: feature.help_section_id,
      feature_text: feature.feature_text,
      display_order: feature.display_order,
      is_active: feature.is_active,
    });
    setShowFeatureDialog(true);
  };

  const handleDeleteFeature = async (id: string) => {
    if (confirm('Tem certeza que deseja excluir esta funcionalidade?')) {
      try {
        await deleteFeatureMutation.mutateAsync(id);
        toast.success('Funcionalidade excluída com sucesso!');
        refetchSections();
      } catch (error) {
        toast.error('Erro ao excluir funcionalidade');
        console.error('Erro:', error);
      }
    }
  };

  // Handlers para dicas
  const handleSaveTip = async () => {
    try {
      if (editingTip) {
        await updateTipMutation.mutateAsync({
          id: editingTip.id,
          data: tipForm
        });
        toast.success('Dica atualizada com sucesso!');
      } else {
        await createTipMutation.mutateAsync(tipForm);
        toast.success('Dica criada com sucesso!');
      }
      
      resetTipForm();
      setShowTipDialog(false);
      refetchSections();
    } catch (error) {
      toast.error('Erro ao salvar dica');
      console.error('Erro:', error);
    }
  };

  const handleEditTip = (tip: HelpTip) => {
    setEditingTip(tip);
    setTipForm({
      help_section_id: tip.help_section_id,
      tip_type: tip.tip_type,
      tip_text: tip.tip_text,
      is_active: tip.is_active,
    });
    setShowTipDialog(true);
  };

  const handleDeleteTip = async (id: string) => {
    if (confirm('Tem certeza que deseja excluir esta dica?')) {
      try {
        await deleteTipMutation.mutateAsync(id);
        toast.success('Dica excluída com sucesso!');
        refetchSections();
      } catch (error) {
        toast.error('Erro ao excluir dica');
        console.error('Erro:', error);
      }
    }
  };

  // Handlers para recursos
  const handleSaveResource = async () => {
    try {
      await createResourceMutation.mutateAsync(resourceForm);
      toast.success('Recurso criado com sucesso!');
      resetResourceForm();
      setShowResourceDialog(false);
      refetchResources();
    } catch (error) {
      toast.error('Erro ao salvar recurso');
      console.error('Erro:', error);
    }
  };

  // Handlers para guides
  const handleSaveGuide = async () => {
    try {
      if (editingGuide) {
        await updateGuideMutation.mutateAsync({
          id: editingGuide.id,
          data: guideForm
        });
        toast.success('Guia atualizado com sucesso!');
      } else {
        await createGuideMutation.mutateAsync(guideForm);
        toast.success('Guia criado com sucesso!');
      }
      
      resetGuideForm();
      setShowGuideDialog(false);
      refetchGuides();
    } catch (error) {
      toast.error('Erro ao salvar guia');
      console.error('Erro:', error);
    }
  };

  const handleEditGuide = (guide: HelpGuide) => {
    setEditingGuide(guide);
    setGuideForm({
      category_key: guide.category_key,
      title: guide.title,
      description: guide.description,
      icon_name: guide.icon_name || '',
      topics: guide.topics || [],
      content: guide.content || '',
      display_order: guide.display_order,
      is_active: guide.is_active,
    });
    setShowGuideDialog(true);
  };

  const handleDeleteGuide = async (id: string) => {
    if (confirm('Tem certeza que deseja excluir este guia?')) {
      try {
        await deleteGuideMutation.mutateAsync(id);
        toast.success('Guia excluído com sucesso!');
        refetchGuides();
      } catch (error) {
        toast.error('Erro ao excluir guia');
        console.error('Erro:', error);
      }
    }
  };

  // Auto-fill emoji e icon baseado na página selecionada
  useEffect(() => {
    if (sectionForm.page_key) {
      const pageOption = PAGE_OPTIONS.find(p => p.value === sectionForm.page_key);
      if (pageOption) {
        setSectionForm(prev => ({
          ...prev,
          emoji: pageOption.emoji,
          icon_name: pageOption.icon.name
        }));
      }
    }
  }, [sectionForm.page_key]);

  // Auto-fill icon baseado no tipo de recurso
  useEffect(() => {
    if (resourceForm.resource_type) {
      const resourceType = RESOURCE_TYPES.find(r => r.value === resourceForm.resource_type);
      if (resourceType) {
        setResourceForm(prev => ({
          ...prev,
          icon_name: resourceType.icon.name
        }));
      }
    }
  }, [resourceForm.resource_type]);

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0 }
  };

  return (
    <div className="container py-1 px-4 space-y-4">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-6"
      >
        {/* Hero Section usando o componente reutilizável */}
        <HeroSection
          title="Central de Ajuda"
          description="Configure todo o conteúdo da Central de Ajuda dinamicamente para todos os clientes"
          icon={HelpCircle}
          gradientColors="from-blue-600 via-cyan-600 to-teal-600"
          iconAnimation={true}
          actions={
            <div className="flex gap-2">
              <Button
                onClick={() => window.history.back()}
                className="bg-white/15 hover:bg-white/25 text-white border-white/30 backdrop-blur-sm"
                size="lg"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Voltar
              </Button>
            </div>
          }
        />
        </motion.div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5 h-auto bg-background/80 backdrop-blur-sm rounded-xl border shadow-lg p-2">
            <TabsTrigger 
              value="sections" 
              className="flex items-center gap-2 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-indigo-500 data-[state=active]:text-white py-3 px-4"
            >
              <BookOpen className="h-4 w-4" />
              <span className="font-medium text-sm">Seções</span>
            </TabsTrigger>
            <TabsTrigger 
              value="guides" 
              className="flex items-center gap-2 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-purple-500 data-[state=active]:text-white py-3 px-4"
            >
              <FileText className="h-4 w-4" />
              <span className="font-medium text-sm">Guias</span>
            </TabsTrigger>
            <TabsTrigger 
              value="features" 
              className="flex items-center gap-2 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-500 data-[state=active]:to-green-500 data-[state=active]:text-white py-3 px-4"
            >
              <Settings className="h-4 w-4" />
              <span className="font-medium text-sm">Features</span>
            </TabsTrigger>
            <TabsTrigger 
              value="tips" 
              className="flex items-center gap-2 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-500 data-[state=active]:text-white py-3 px-4"
            >
              <Lightbulb className="h-4 w-4" />
              <span className="font-medium text-sm">Dicas</span>
            </TabsTrigger>
            <TabsTrigger 
              value="resources" 
              className="flex items-center gap-2 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white py-3 px-4"
            >
              <Shield className="h-4 w-4" />
              <span className="font-medium text-sm">Recursos</span>
            </TabsTrigger>
          </TabsList>

        {/* ABA SEÇÕES */}
        <TabsContent value="sections" className="space-y-6">
          <HelpSectionsTab
            sections={sections}
            sectionsLoading={sectionsLoading}
            onNewSection={() => setShowSectionDialog(true)}
            onEditSection={handleEditSection}
            onDeleteSection={handleDeleteSection}
          />
        </TabsContent>

        {/* ABA GUIDES */}
        <TabsContent value="guides" className="space-y-6">
          <HelpGuidesTab
            guides={guides}
            guidesLoading={guidesLoading}
            onNewGuide={() => setShowGuideDialog(true)}
            onEditGuide={handleEditGuide}
            onDeleteGuide={handleDeleteGuide}
          />
        </TabsContent>

        {/* ABA FUNCIONALIDADES */}
        <TabsContent value="features" className="space-y-6">
          <HelpFeaturesTab
            sections={sections}
            onNewFeature={() => setShowFeatureDialog(true)}
            onEditFeature={handleEditFeature}
            onDeleteFeature={handleDeleteFeature}
          />
        </TabsContent>

        {/* ABA DICAS */}
        <TabsContent value="tips" className="space-y-6">
          <HelpTipsTab
            sections={sections}
            onNewTip={() => setShowTipDialog(true)}
            onEditTip={handleEditTip}
            onDeleteTip={handleDeleteTip}
          />
        </TabsContent>

        {/* ABA RECURSOS */}
        <TabsContent value="resources" className="space-y-6">
          <HelpResourcesTab
            resources={resources}
            resourcesLoading={resourcesLoading}
            onNewResource={() => setShowResourceDialog(true)}
            onEditResource={(resource) => {
              setEditingResource(resource);
              setShowResourceDialog(true);
            }}
          />
        </TabsContent>
      </Tabs>

      {/* DIALOG SEÇÃO */}
      <Dialog open={showSectionDialog} onOpenChange={setShowSectionDialog}>
        <DialogContent className="max-w-3xl bg-white/95 backdrop-blur-sm border border-gray-200 shadow-2xl">
          <DialogHeader className="pb-6 border-b border-gray-100">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-indigo-500 text-white">
                <BookOpen className="h-6 w-6" />
              </div>
              <div>
                <DialogTitle className="text-2xl font-bold text-gray-900">
                  {editingSection ? 'Editar Seção' : 'Nova Seção de Página'}
                </DialogTitle>
                <DialogDescription className="text-gray-600 mt-1">
                  Configure informações específicas para uma página do sistema
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          
          <div className="grid gap-6 py-6">
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="page_key" className="text-sm font-semibold text-gray-700">Página</Label>
                <Select 
                  value={sectionForm.page_key} 
                  onValueChange={(value) => setSectionForm(prev => ({ ...prev, page_key: value }))}
                >
                  <SelectTrigger className="bg-white border-gray-200 hover:border-blue-300 focus:border-blue-500 transition-colors">
                    <SelectValue placeholder="Selecione uma página" />
                  </SelectTrigger>
                  <SelectContent className="bg-white border-gray-200 shadow-lg">
                    {PAGE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value} className="hover:bg-blue-50">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{option.emoji}</span>
                          <span>{option.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-3">
                <Label htmlFor="title" className="text-sm font-semibold text-gray-700">Título</Label>
                <Input
                  id="title"
                  value={sectionForm.title}
                  onChange={(e) => setSectionForm(prev => ({ ...prev, title: e.target.value }))}
                  className="bg-white border-gray-200 hover:border-blue-300 focus:border-blue-500 transition-colors"
                  placeholder="Ex: Feed & Publicações"
                />
              </div>
            </div>
            
            <div className="space-y-3">
              <Label htmlFor="description" className="text-sm font-semibold text-gray-700">Descrição</Label>
              <Textarea
                id="description"
                value={sectionForm.description}
                onChange={(e) => setSectionForm(prev => ({ ...prev, description: e.target.value }))}
                className="bg-white border-gray-200 hover:border-blue-300 focus:border-blue-500 transition-colors min-h-[100px]"
                placeholder="Descreva o que é e para que serve esta funcionalidade..."
              />
            </div>
            
            <div className="grid grid-cols-3 gap-6">
              <div className="space-y-3">
                <Label htmlFor="emoji" className="text-sm font-semibold text-gray-700">Emoji</Label>
                <Input
                  id="emoji"
                  value={sectionForm.emoji}
                  onChange={(e) => setSectionForm(prev => ({ ...prev, emoji: e.target.value }))}
                  className="bg-white border-gray-200 hover:border-blue-300 focus:border-blue-500 transition-colors text-center text-2xl"
                  placeholder="📢"
                  maxLength={2}
                />
              </div>
              
              <div className="space-y-3">
                <Label htmlFor="display_order" className="text-sm font-semibold text-gray-700">Ordem de Exibição</Label>
                <Input
                  id="display_order"
                  type="number"
                  value={sectionForm.display_order}
                  onChange={(e) => setSectionForm(prev => ({ ...prev, display_order: parseInt(e.target.value) || 0 }))}
                  className="bg-white border-gray-200 hover:border-blue-300 focus:border-blue-500 transition-colors"
                  min="0"
                />
              </div>
              
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700">Status</Label>
                <div className="flex items-center space-x-3 pt-2">
                  <Switch
                    id="is_active"
                    checked={sectionForm.is_active}
                    onCheckedChange={(checked) => setSectionForm(prev => ({ ...prev, is_active: checked }))}
                  />
                  <Label htmlFor="is_active" className="text-sm text-gray-600">
                    {sectionForm.is_active ? 'Ativo' : 'Inativo'}
                  </Label>
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter className="pt-6 border-t border-gray-100">
            <Button 
              variant="outline" 
              onClick={() => {
                resetSectionForm();
                setShowSectionDialog(false);
              }}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <X className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
            <Button 
              onClick={handleSaveSection} 
              className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white shadow-lg"
            >
              <Save className="h-4 w-4 mr-2" />
              {editingSection ? 'Salvar Alterações' : 'Criar Seção'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* DIALOG FUNCIONALIDADE */}
      <Dialog open={showFeatureDialog} onOpenChange={setShowFeatureDialog}>
        <DialogContent className="max-w-2xl bg-white/95 backdrop-blur-sm border border-gray-200 shadow-2xl">
          <DialogHeader className="pb-6 border-b border-gray-100">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gradient-to-r from-emerald-500 to-teal-500 text-white">
                <Settings className="h-6 w-6" />
              </div>
              <div>
                <DialogTitle className="text-2xl font-bold text-gray-900">
                  {editingFeature ? 'Editar Funcionalidade' : 'Nova Funcionalidade'}
                </DialogTitle>
                <DialogDescription className="text-gray-600 mt-1">
                  Adicione uma funcionalidade específica a uma seção
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          
          <div className="grid gap-6 py-6">
            <div className="space-y-3">
              <Label htmlFor="section_select" className="text-sm font-semibold text-gray-700">Seção</Label>
              <Select 
                value={featureForm.help_section_id} 
                onValueChange={(value) => setFeatureForm(prev => ({ ...prev, help_section_id: value }))}
              >
                <SelectTrigger className="bg-white border-gray-200 hover:border-emerald-300 focus:border-emerald-500 transition-colors">
                  <SelectValue placeholder="Selecione uma seção" />
                </SelectTrigger>
                <SelectContent className="bg-white border-gray-200 shadow-lg">
                  {sections.map((section) => (
                    <SelectItem key={section.id} value={section.id} className="hover:bg-emerald-50">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{section.emoji}</span>
                        <span>{section.title}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-3">
              <Label htmlFor="feature_text" className="text-sm font-semibold text-gray-700">Texto da Funcionalidade</Label>
              <Textarea
                id="feature_text"
                value={featureForm.feature_text}
                onChange={(e) => setFeatureForm(prev => ({ ...prev, feature_text: e.target.value }))}
                className="bg-white border-gray-200 hover:border-emerald-300 focus:border-emerald-500 transition-colors min-h-[120px]"
                placeholder="Ex: Publicar posts com texto, imagens e vídeos"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="feature_order" className="text-sm font-semibold text-gray-700">Ordem de Exibição</Label>
                <Input
                  id="feature_order"
                  type="number"
                  value={featureForm.display_order}
                  onChange={(e) => setFeatureForm(prev => ({ ...prev, display_order: parseInt(e.target.value) || 0 }))}
                  className="bg-white border-gray-200 hover:border-emerald-300 focus:border-emerald-500 transition-colors"
                  min="0"
                />
              </div>
              
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700">Status</Label>
                <div className="flex items-center space-x-3 pt-2">
                  <Switch
                    id="feature_active"
                    checked={featureForm.is_active}
                    onCheckedChange={(checked) => setFeatureForm(prev => ({ ...prev, is_active: checked }))}
                  />
                  <Label htmlFor="feature_active" className="text-sm text-gray-600">
                    {featureForm.is_active ? 'Ativo' : 'Inativo'}
                  </Label>
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter className="pt-6 border-t border-gray-100">
            <Button 
              variant="outline" 
              onClick={() => {
                resetFeatureForm();
                setShowFeatureDialog(false);
              }}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <X className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
            <Button 
              onClick={handleSaveFeature} 
              className="bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white shadow-lg"
            >
              <Save className="h-4 w-4 mr-2" />
              {editingFeature ? 'Salvar Alterações' : 'Adicionar Funcionalidade'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* DIALOG DICA */}
      <Dialog open={showTipDialog} onOpenChange={setShowTipDialog}>
        <DialogContent className="max-w-2xl bg-white/95 backdrop-blur-sm border border-gray-200 shadow-2xl">
          <DialogHeader className="pb-6 border-b border-gray-100">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                <Lightbulb className="h-6 w-6" />
              </div>
              <div>
                <DialogTitle className="text-2xl font-bold text-gray-900">
                  {editingTip ? 'Editar Dica' : 'Nova Dica'}
                </DialogTitle>
                <DialogDescription className="text-gray-600 mt-1">
                  Adicione uma dica contextual a uma seção
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          
          <div className="grid gap-6 py-6">
            <div className="space-y-3">
              <Label htmlFor="tip_section_select" className="text-sm font-semibold text-gray-700">Seção</Label>
              <Select 
                value={tipForm.help_section_id} 
                onValueChange={(value) => setTipForm(prev => ({ ...prev, help_section_id: value }))}
              >
                <SelectTrigger className="bg-white border-gray-200 hover:border-purple-300 focus:border-purple-500 transition-colors">
                  <SelectValue placeholder="Selecione uma seção" />
                </SelectTrigger>
                <SelectContent className="bg-white border-gray-200 shadow-lg">
                  {sections.map((section) => (
                    <SelectItem key={section.id} value={section.id} className="hover:bg-purple-50">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{section.emoji}</span>
                        <span>{section.title}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-3">
              <Label htmlFor="tip_type" className="text-sm font-semibold text-gray-700">Tipo de Dica</Label>
              <Select 
                value={tipForm.tip_type} 
                onValueChange={(value: 'keyboard_shortcut' | 'best_practice' | 'warning' | 'info') => setTipForm(prev => ({ ...prev, tip_type: value }))}
              >
                <SelectTrigger className="bg-white border-gray-200 hover:border-purple-300 focus:border-purple-500 transition-colors">
                  <SelectValue placeholder="Selecione o tipo de dica" />
                </SelectTrigger>
                <SelectContent className="bg-white border-gray-200 shadow-lg">
                  {TIP_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value} className="hover:bg-purple-50">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{type.emoji}</span>
                        <span>{type.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-3">
              <Label htmlFor="tip_text" className="text-sm font-semibold text-gray-700">Texto da Dica</Label>
              <Textarea
                id="tip_text"
                value={tipForm.tip_text}
                onChange={(e) => setTipForm(prev => ({ ...prev, tip_text: e.target.value }))}
                className="bg-white border-gray-200 hover:border-purple-300 focus:border-purple-500 transition-colors min-h-[120px]"
                placeholder="Ex: Use Alt+P para criar uma nova publicação rapidamente"
              />
            </div>
            
            <div className="space-y-3">
              <Label className="text-sm font-semibold text-gray-700">Status</Label>
              <div className="flex items-center space-x-3">
                <Switch
                  id="tip_active"
                  checked={tipForm.is_active}
                  onCheckedChange={(checked) => setTipForm(prev => ({ ...prev, is_active: checked }))}
                />
                <Label htmlFor="tip_active" className="text-sm text-gray-600">
                  {tipForm.is_active ? 'Ativo' : 'Inativo'}
                </Label>
              </div>
            </div>
          </div>
          
          <DialogFooter className="pt-6 border-t border-gray-100">
            <Button 
              variant="outline" 
              onClick={() => {
                resetTipForm();
                setShowTipDialog(false);
              }}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <X className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
            <Button 
              onClick={handleSaveTip} 
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-lg"
            >
              <Lightbulb className="h-4 w-4 mr-2" />
              {editingTip ? 'Salvar Alterações' : 'Adicionar Dica'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* DIALOG RECURSO */}
      <Dialog open={showResourceDialog} onOpenChange={setShowResourceDialog}>
        <DialogContent className="max-w-3xl bg-white/95 backdrop-blur-sm border border-gray-200 shadow-2xl">
          <DialogHeader className="pb-6 border-b border-gray-100">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gradient-to-r from-orange-500 to-red-500 text-white">
                <HelpCircle className="h-6 w-6" />
              </div>
              <div>
                <DialogTitle className="text-2xl font-bold text-gray-900">
                  {editingResource ? 'Editar Recurso' : 'Novo Recurso de Suporte'}
                </DialogTitle>
                <DialogDescription className="text-gray-600 mt-1">
                  Configure um recurso de ajuda e suporte
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          
          <div className="grid gap-6 py-6">
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="resource_type" className="text-sm font-semibold text-gray-700">Tipo de Recurso</Label>
                <Select 
                  value={resourceForm.resource_type} 
                  onValueChange={(value: 'video' | 'article' | 'live_support' | 'emergency') => setResourceForm(prev => ({ ...prev, resource_type: value }))}
                >
                  <SelectTrigger className="bg-white border-gray-200 hover:border-orange-300 focus:border-orange-500 transition-colors">
                    <SelectValue placeholder="Selecione o tipo de recurso" />
                  </SelectTrigger>
                  <SelectContent className="bg-white border-gray-200 shadow-lg">
                    {RESOURCE_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value} className="hover:bg-orange-50">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{type.emoji}</span>
                          <span>{type.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-3">
                <Label htmlFor="resource_title" className="text-sm font-semibold text-gray-700">Título</Label>
                <Input
                  id="resource_title"
                  value={resourceForm.title}
                  onChange={(e) => setResourceForm(prev => ({ ...prev, title: e.target.value }))}
                  className="bg-white border-gray-200 hover:border-orange-300 focus:border-orange-500 transition-colors"
                  placeholder="Ex: Vídeos Tutoriais"
                />
              </div>
            </div>
            
            <div className="space-y-3">
              <Label htmlFor="resource_description" className="text-sm font-semibold text-gray-700">Descrição</Label>
              <Textarea
                id="resource_description"
                value={resourceForm.description}
                onChange={(e) => setResourceForm(prev => ({ ...prev, description: e.target.value }))}
                className="bg-white border-gray-200 hover:border-orange-300 focus:border-orange-500 transition-colors min-h-[100px]"
                placeholder="Descreva este recurso de suporte..."
              />
            </div>
            
            <div className="space-y-3">
              <Label htmlFor="resource_url" className="text-sm font-semibold text-gray-700">URL ou Conteúdo</Label>
              <Input
                id="resource_url"
                value={resourceForm.url_or_content}
                onChange={(e) => setResourceForm(prev => ({ ...prev, url_or_content: e.target.value }))}
                className="bg-white border-gray-200 hover:border-orange-300 focus:border-orange-500 transition-colors"
                placeholder="https://... ou instruções específicas"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="resource_order" className="text-sm font-semibold text-gray-700">Ordem de Exibição</Label>
                <Input
                  id="resource_order"
                  type="number"
                  value={resourceForm.display_order}
                  onChange={(e) => setResourceForm(prev => ({ ...prev, display_order: parseInt(e.target.value) || 0 }))}
                  className="bg-white border-gray-200 hover:border-orange-300 focus:border-orange-500 transition-colors"
                  min="0"
                />
              </div>
              
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700">Status</Label>
                <div className="flex items-center space-x-3 pt-2">
                  <Switch
                    id="resource_active"
                    checked={resourceForm.is_active}
                    onCheckedChange={(checked) => setResourceForm(prev => ({ ...prev, is_active: checked }))}
                  />
                  <Label htmlFor="resource_active" className="text-sm text-gray-600">
                    {resourceForm.is_active ? 'Ativo' : 'Inativo'}
                  </Label>
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter className="pt-6 border-t border-gray-100">
            <Button 
              variant="outline" 
              onClick={() => {
                resetResourceForm();
                setShowResourceDialog(false);
              }}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <X className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
            <Button 
              onClick={handleSaveResource} 
              className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg"
            >
              <Save className="h-4 w-4 mr-2" />
              {editingResource ? 'Salvar Alterações' : 'Criar Recurso'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* DIALOG GUIDE */}
      <Dialog open={showGuideDialog} onOpenChange={setShowGuideDialog}>
        <DialogContent className="max-w-5xl bg-white/95 backdrop-blur-sm border border-gray-200 shadow-2xl">
          <DialogHeader className="pb-6 border-b border-gray-100">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gradient-to-r from-indigo-500 to-purple-500 text-white">
                <FileText className="h-6 w-6" />
              </div>
              <div>
                <DialogTitle className="text-2xl font-bold text-gray-900">
                  {editingGuide ? 'Editar Guia' : 'Novo Guia Detalhado'}
                </DialogTitle>
                <DialogDescription className="text-gray-600 mt-1">
                  Configure um guia completo com conteúdo rico para a Central de Ajuda
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          
          <div className="grid gap-6 py-6 max-h-[70vh] overflow-y-auto pr-2">
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="category_key" className="text-sm font-semibold text-gray-700">Categoria</Label>
                <Select 
                  value={guideForm.category_key} 
                  onValueChange={(value) => setGuideForm(prev => ({ ...prev, category_key: value }))}
                >
                  <SelectTrigger className="bg-white border-gray-200 hover:border-indigo-300 focus:border-indigo-500 transition-colors">
                    <SelectValue placeholder="Selecione uma categoria" />
                  </SelectTrigger>
                  <SelectContent className="bg-white border-gray-200 shadow-lg">
                    {PAGE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value} className="hover:bg-indigo-50">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{option.emoji}</span>
                          <span>{option.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-3">
                <Label htmlFor="guide_title" className="text-sm font-semibold text-gray-700">Título do Guia</Label>
                <Input
                  id="guide_title"
                  value={guideForm.title}
                  onChange={(e) => setGuideForm(prev => ({ ...prev, title: e.target.value }))}
                  className="bg-white border-gray-200 hover:border-indigo-300 focus:border-indigo-500 transition-colors"
                  placeholder="Ex: Como usar o Knowledge Hub"
                />
              </div>
            </div>
            
            <div className="space-y-3">
              <Label htmlFor="guide_description" className="text-sm font-semibold text-gray-700">Descrição</Label>
              <Textarea
                id="guide_description"
                value={guideForm.description}
                onChange={(e) => setGuideForm(prev => ({ ...prev, description: e.target.value }))}
                className="bg-white border-gray-200 hover:border-indigo-300 focus:border-indigo-500 transition-colors min-h-[80px]"
                placeholder="Breve descrição do que este guia ensina..."
              />
            </div>
            
            <div className="space-y-3">
              <Label htmlFor="guide_topics" className="text-sm font-semibold text-gray-700">Tópicos (separados por vírgula)</Label>
              <Textarea
                id="guide_topics"
                value={Array.isArray(guideForm.topics) ? guideForm.topics.join(', ') : ''}
                onChange={(e) => {
                  const topics = e.target.value.split(',').map(t => t.trim()).filter(t => t.length > 0);
                  setGuideForm(prev => ({ ...prev, topics }));
                }}
                className="bg-white border-gray-200 hover:border-indigo-300 focus:border-indigo-500 transition-colors min-h-[80px]"
                placeholder="Ex: Criar documento, Organizar pastas, Compartilhar conhecimento"
              />
            </div>
            
            <div className="space-y-3">
              <Label htmlFor="guide_content" className="text-sm font-semibold text-gray-700">Conteúdo do Guia (HTML/Markdown)</Label>
              <Textarea
                id="guide_content"
                value={guideForm.content}
                onChange={(e) => setGuideForm(prev => ({ ...prev, content: e.target.value }))}
                className="bg-white border-gray-200 hover:border-indigo-300 focus:border-indigo-500 transition-colors font-mono text-sm min-h-[200px]"
                placeholder="Conteúdo rico do guia em HTML ou Markdown..."
              />
            </div>
            
            <div className="grid grid-cols-3 gap-6">
              <div className="space-y-3">
                <Label htmlFor="guide_icon" className="text-sm font-semibold text-gray-700">Ícone (nome)</Label>
                <Input
                  id="guide_icon"
                  value={guideForm.icon_name}
                  onChange={(e) => setGuideForm(prev => ({ ...prev, icon_name: e.target.value }))}
                  className="bg-white border-gray-200 hover:border-indigo-300 focus:border-indigo-500 transition-colors"
                  placeholder="BookOpen"
                />
              </div>
              
              <div className="space-y-3">
                <Label htmlFor="guide_order" className="text-sm font-semibold text-gray-700">Ordem de Exibição</Label>
                <Input
                  id="guide_order"
                  type="number"
                  value={guideForm.display_order}
                  onChange={(e) => setGuideForm(prev => ({ ...prev, display_order: parseInt(e.target.value) || 0 }))}
                  className="bg-white border-gray-200 hover:border-indigo-300 focus:border-indigo-500 transition-colors"
                  min="0"
                />
              </div>
              
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700">Status</Label>
                <div className="flex items-center space-x-3 pt-2">
                  <Switch
                    id="guide_active"
                    checked={guideForm.is_active}
                    onCheckedChange={(checked) => setGuideForm(prev => ({ ...prev, is_active: checked }))}
                  />
                  <Label htmlFor="guide_active" className="text-sm text-gray-600">
                    {guideForm.is_active ? 'Ativo' : 'Inativo'}
                  </Label>
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter className="pt-6 border-t border-gray-100">
            <Button 
              variant="outline" 
              onClick={() => {
                resetGuideForm();
                setShowGuideDialog(false);
              }}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <X className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
            <Button 
              onClick={handleSaveGuide} 
              className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white shadow-lg"
            >
              <Save className="h-4 w-4 mr-2" />
              {editingGuide ? 'Salvar Alterações' : 'Criar Guia'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}