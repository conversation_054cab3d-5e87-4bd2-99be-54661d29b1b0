/**
 * Componente para gerenciamento de dicas e truques da Central de Ajuda
 * <AUTHOR> Internet 2025
 */
import React from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Lightbulb,
  EyeOff,
  Plus,
  Eye,
  Edit,
  Trash2,
} from 'lucide-react';
import { HelpSection, HelpTip } from '@/types/help';

const TIP_TYPES = [
  { value: 'keyboard_shortcut', label: 'Atalho de Teclado', emoji: '⌨️' },
  { value: 'best_practice', label: 'Melhores Práticas', emoji: '✨' },
  { value: 'warning', label: 'Aviso Importante', emoji: '⚠️' },
  { value: 'info', label: 'Informação Geral', emoji: 'ℹ️' },
];

interface HelpTipsTabProps {
  sections: HelpSection[];
  onNewTip: () => void;
  onEditTip: (tip: HelpTip) => void;
  onDeleteTip: (tipId: string) => void;
}

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 }
};

export function HelpTipsTab({
  sections,
  onNewTip,
  onEditTip,
  onDeleteTip
}: HelpTipsTabProps) {
  return (
    <motion.div 
      variants={itemVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Header Section */}
      <motion.div variants={itemVariants} className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white">
              <Lightbulb className="h-6 w-6" />
            </div>
            Dicas e Truques
          </h2>
          <p className="text-gray-600 mt-1">
            Configure dicas contextuais para cada seção
          </p>
        </div>
        <Button 
          onClick={onNewTip}
          className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-lg"
          size="lg"
        >
          <Plus className="h-5 w-5 mr-2" />
          Nova Dica
        </Button>
      </motion.div>

      {/* Content Section */}
      <motion.div variants={itemVariants}>
        {sections.length === 0 ? (
          <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200">
            <CardContent className="text-center py-16">
              <Lightbulb className="h-16 w-16 text-purple-400 mx-auto mb-6" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Nenhuma seção disponível</h3>
              <p className="text-gray-600 mb-6">Crie seções primeiro para adicionar dicas</p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {sections.map((section, sectionIndex) => (
              <motion.div 
                key={section.id} 
                variants={itemVariants}
                transition={{ delay: sectionIndex * 0.1 }}
              >
                <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="flex items-center gap-4">
                        <span className="text-3xl">{section.emoji}</span>
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 text-lg">{section.title}</h3>
                          <p className="text-sm text-gray-500">
                            {section.help_tips?.length || 0} dicas cadastradas
                          </p>
                        </div>
                        <Badge 
                          variant="outline" 
                          className="bg-purple-50 text-purple-700 border-purple-200"
                        >
                          {section.help_tips?.length || 0} dicas
                        </Badge>
                      </div>
                      
                      {section.help_tips && section.help_tips.length > 0 ? (
                        <div className="grid gap-3 pl-4 border-l-2 border-purple-200">
                          {section.help_tips.map((tip, index) => {
                            const tipType = TIP_TYPES.find(t => t.value === tip.tip_type);
                            return (
                              <motion.div 
                                key={tip.id}
                                variants={itemVariants}
                                transition={{ delay: (sectionIndex * 0.1) + (index * 0.05) }}
                                className="flex items-start gap-3 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-100 hover:shadow-sm transition-shadow"
                              >
                                <Badge className="bg-purple-600 text-white text-xs px-2 py-1 flex items-center gap-1 shrink-0">
                                  <span>{tipType?.emoji}</span>
                                  <span>{tipType?.label}</span>
                                </Badge>
                                <span className="text-gray-800 flex-1 font-medium leading-relaxed">{tip.tip_text}</span>
                                <div className="flex items-center gap-2 shrink-0">
                                  <Badge 
                                    variant={tip.is_active ? "default" : "secondary"}
                                    className={tip.is_active ? "bg-green-100 text-green-800 border-green-200" : ""}
                                  >
                                    {tip.is_active ? (
                                      <><Eye className="h-3 w-3 mr-1" /> Ativo</>
                                    ) : (
                                      <><EyeOff className="h-3 w-3 mr-1" /> Inativo</>
                                    )}
                                  </Badge>
                                  <div className="flex items-center gap-1">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => onEditTip(tip)}
                                      className="h-8 w-8 p-0 hover:bg-purple-100 hover:text-purple-700"
                                    >
                                      <Edit className="h-3 w-3" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => onDeleteTip(tip.id)}
                                      className="h-8 w-8 p-0 hover:bg-red-100 hover:text-red-700"
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </div>
                              </motion.div>
                            );
                          })}
                        </div>
                      ) : (
                        <div className="pl-4 border-l-2 border-gray-200">
                          <p className="text-gray-500 text-sm italic">Nenhuma dica cadastrada para esta seção</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>
    </motion.div>
  );
}
