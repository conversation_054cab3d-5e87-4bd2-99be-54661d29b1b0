/**
 * Componente para gerenciamento de funcionalidades da Central de Ajuda
 * <AUTHOR> Internet 2025
 */
import React from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Plus,
  EyeOff,
  Settings,
  Eye,
  Edit,
  Trash2,
} from 'lucide-react';
import { HelpSection, HelpFeature } from '@/types/help';

interface HelpFeaturesTabProps {
  sections: HelpSection[];
  onNewFeature: () => void;
  onEditFeature: (feature: HelpFeature) => void;
  onDeleteFeature: (featureId: string) => void;
}

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 }
};

export function HelpFeaturesTab({
  sections,
  onNewFeature,
  onEditFeature,
  onDeleteFeature
}: HelpFeaturesTabProps) {
  return (
    <motion.div 
      variants={itemVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Header Section */}
      <motion.div variants={itemVariants} className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-r from-emerald-500 to-green-500 text-white">
              <Settings className="h-6 w-6" />
            </div>
            Funcionalidades por Seção
          </h2>
          <p className="text-gray-600 mt-1">
            Adicione funcionalidades específicas para cada seção/página
          </p>
        </div>
        <Button 
          onClick={onNewFeature}
          className="bg-gradient-to-r from-emerald-500 to-green-500 hover:from-emerald-600 hover:to-green-600 text-white shadow-lg"
          size="lg"
        >
          <Plus className="h-5 w-5 mr-2" />
          Nova Funcionalidade
        </Button>
      </motion.div>

      {/* Content Section */}
      <motion.div variants={itemVariants}>
        {sections.length === 0 ? (
          <Card className="bg-gradient-to-br from-emerald-50 to-green-50 border-emerald-200">
            <CardContent className="text-center py-16">
              <Settings className="h-16 w-16 text-emerald-400 mx-auto mb-6" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Nenhuma seção disponível</h3>
              <p className="text-gray-600 mb-6">Crie seções primeiro para adicionar funcionalidades</p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {sections.map((section, sectionIndex) => (
              <motion.div 
                key={section.id} 
                variants={itemVariants}
                transition={{ delay: sectionIndex * 0.1 }}
              >
                <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="flex items-center gap-4">
                        <span className="text-3xl">{section.emoji}</span>
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 text-lg">{section.title}</h3>
                          <p className="text-sm text-gray-500">
                            {section.help_features?.length || 0} funcionalidades cadastradas
                          </p>
                        </div>
                        <Badge 
                          variant="outline" 
                          className="bg-emerald-50 text-emerald-700 border-emerald-200"
                        >
                          {section.help_features?.length || 0} features
                        </Badge>
                      </div>
                      
                      {section.help_features && section.help_features.length > 0 ? (
                        <div className="grid gap-3 pl-4 border-l-2 border-emerald-200">
                          {section.help_features
                            .sort((a, b) => a.display_order - b.display_order)
                            .map((feature, index) => (
                            <motion.div 
                              key={feature.id}
                              variants={itemVariants}
                              transition={{ delay: (sectionIndex * 0.1) + (index * 0.05) }}
                              className="flex items-center gap-3 p-4 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg border border-emerald-100 hover:shadow-sm transition-shadow"
                            >
                              <Badge className="bg-emerald-600 text-white text-xs min-w-[24px] h-6 flex items-center justify-center">
                                {index + 1}
                              </Badge>
                              <span className="text-gray-800 flex-1 font-medium">{feature.feature_text}</span>
                              <Badge 
                                variant={feature.is_active ? "default" : "secondary"}
                                className={feature.is_active ? "bg-green-100 text-green-800 border-green-200" : ""}
                              >
                                {feature.is_active ? (
                                  <><Eye className="h-3 w-3 mr-1" /> Ativo</>
                                ) : (
                                  <><EyeOff className="h-3 w-3 mr-1" /> Inativo</>
                                )}
                              </Badge>
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => onEditFeature(feature)}
                                  className="h-8 w-8 p-0 hover:bg-emerald-100 hover:text-emerald-700"
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => onDeleteFeature(feature.id)}
                                  className="h-8 w-8 p-0 hover:bg-red-100 hover:text-red-700"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      ) : (
                        <div className="pl-4 border-l-2 border-gray-200">
                          <p className="text-gray-500 text-sm italic">Nenhuma funcionalidade cadastrada para esta seção</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>
    </motion.div>
  );
}
