/**
 * Componente para gerenciamento de seções de página da Central de Ajuda
 * <AUTHOR> Internet 2025
 */
import React from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Plus,
  Edit,
  Trash2,
  BookOpen,
  Eye,
  EyeOff,
} from 'lucide-react';
import { HelpSection } from '@/types/help';

// Configurações de páginas disponíveis
const PAGE_OPTIONS = [
  { value: 'feed', label: 'Feed & Publicações', emoji: '📢' },
  { value: 'knowledge', label: 'Knowledge Hub', emoji: '📚' },
  { value: 'chat', label: 'Chat & Comunicação', emoji: '💬' },
  { value: 'tasks', label: 'Gerenciador de Tasks', emoji: '✅' },
  { value: 'biblioteca', label: 'Biblioteca de Arquivos', emoji: '📁' },
  { value: 'equipe', label: 'Gestão de Equipe', emoji: '👥' },
  { value: 'admin', label: 'Administração', emoji: '⚙️' },
  { value: 'security', label: 'Segurança & Privacidade', emoji: '🔒' },
];

interface HelpSectionsTabProps {
  sections: HelpSection[];
  sectionsLoading: boolean;
  onNewSection: () => void;
  onEditSection: (section: HelpSection) => void;
  onDeleteSection: (id: string) => void;
}

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 }
};

export function HelpSectionsTab({
  sections,
  sectionsLoading,
  onNewSection,
  onEditSection,
  onDeleteSection
}: HelpSectionsTabProps) {
  return (
    <motion.div 
      variants={itemVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Header Section */}
      <motion.div variants={itemVariants} className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-indigo-500 text-white">
              <BookOpen className="h-6 w-6" />
            </div>
            Seções de Página
          </h2>
          <p className="text-gray-600 mt-1">
            Configure a descrição e conteúdo específico para cada página do sistema
          </p>
        </div>
        <Button 
          onClick={onNewSection}
          className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white shadow-lg"
          size="lg"
        >
          <Plus className="h-5 w-5 mr-2" />
          Nova Seção
        </Button>
      </motion.div>

      {/* Content Section */}
      <motion.div variants={itemVariants}>
        {sectionsLoading ? (
          <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
            <CardContent className="text-center py-16">
              <div className="animate-pulse">
                <BookOpen className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <p className="text-blue-600 font-medium">Carregando seções...</p>
              </div>
            </CardContent>
          </Card>
        ) : sections.length === 0 ? (
          <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
            <CardContent className="text-center py-16">
              <BookOpen className="h-16 w-16 text-blue-400 mx-auto mb-6" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Nenhuma seção criada</h3>
              <p className="text-gray-600 mb-6">Comece criando sua primeira seção de página</p>
              <Button 
                onClick={onNewSection}
                className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
              >
                <Plus className="h-4 w-4 mr-2" />
                Criar Primeira Seção
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {sections.map((section, index) => {
              const pageOption = PAGE_OPTIONS.find(p => p.value === section.page_key);
              return (
                <motion.div
                  key={section.id}
                  variants={itemVariants}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-3">
                            <span className="text-3xl">{section.emoji}</span>
                            <div>
                              <h3 className="font-semibold text-gray-900 text-lg">{section.title}</h3>
                              <p className="text-sm text-gray-500">
                                Página: {pageOption?.label || section.page_key}
                              </p>
                            </div>
                          </div>
                          <Badge 
                            variant={section.is_active ? "default" : "secondary"}
                            className={section.is_active ? "bg-green-100 text-green-800 border-green-200" : ""}
                          >
                            {section.is_active ? (
                              <><Eye className="h-3 w-3 mr-1" /> Ativo</>
                            ) : (
                              <><EyeOff className="h-3 w-3 mr-1" /> Inativo</>
                            )}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-4">
                          <div className="text-sm text-gray-500 text-right">
                            <p>{section.help_features?.length || 0} funcionalidades</p>
                            <p>{section.help_tips?.length || 0} dicas</p>
                          </div>
                          <div className="flex items-center gap-1">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => onEditSection(section)}
                              className="text-gray-600 hover:text-blue-600 hover:bg-blue-50"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => onDeleteSection(section.id)}
                              className="text-gray-600 hover:text-red-600 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        )}
      </motion.div>
    </motion.div>
  );
}
