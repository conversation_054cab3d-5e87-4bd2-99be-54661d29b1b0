/**
 * Componente para gerenciamento de guias detalhados da Central de Ajuda
 * <AUTHOR> Internet 2025
 */
import React from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Plus,
  Edit,
  Trash2,
  BookOpen,
  Eye,
  EyeOff,
  FileText,
} from 'lucide-react';
import { HelpGuide } from '@/types/help';

interface HelpGuidesTabProps {
  guides: HelpGuide[];
  guidesLoading: boolean;
  onNewGuide: () => void;
  onEditGuide: (guide: HelpGuide) => void;
  onDeleteGuide: (id: string) => void;
}

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 }
};

export function HelpGuidesTab({
  guides,
  guidesLoading,
  onNewGuide,
  onEditGuide,
  onDeleteGuide
}: HelpGuidesTabProps) {
  return (
    <motion.div 
      variants={itemVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Header Section */}
      <motion.div variants={itemVariants} className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-r from-indigo-500 to-purple-500 text-white">
              <FileText className="h-6 w-6" />
            </div>
            Guias Detalhados
          </h2>
          <p className="text-gray-600 mt-1">
            Crie guias passo-a-passo para ajudar os usuários em tarefas complexas
          </p>
        </div>
        <Button 
          onClick={onNewGuide}
          className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white shadow-lg"
          size="lg"
        >
          <Plus className="h-5 w-5 mr-2" />
          Novo Guia
        </Button>
      </motion.div>

      {/* Content Section */}
      <motion.div variants={itemVariants}>
        {guidesLoading ? (
          <Card className="bg-gradient-to-br from-indigo-50 to-purple-50 border-indigo-200">
            <CardContent className="text-center py-16">
              <div className="animate-pulse">
                <FileText className="h-12 w-12 text-indigo-400 mx-auto mb-4" />
                <p className="text-indigo-600 font-medium">Carregando guias...</p>
              </div>
            </CardContent>
          </Card>
        ) : guides.length === 0 ? (
          <Card className="bg-gradient-to-br from-indigo-50 to-purple-50 border-indigo-200">
            <CardContent className="text-center py-16">
              <FileText className="h-16 w-16 text-indigo-400 mx-auto mb-6" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Nenhum guia criado</h3>
              <p className="text-gray-600 mb-6">Comece criando seu primeiro guia detalhado</p>
              <Button 
                onClick={onNewGuide}
                className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600"
              >
                <Plus className="h-4 w-4 mr-2" />
                Criar Primeiro Guia
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {guides.map((guide, index) => (
              <motion.div
                key={guide.id}
                variants={itemVariants}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-3">
                          <span className="text-3xl">{guide.emoji}</span>
                          <div>
                            <h3 className="font-semibold text-gray-900 text-lg">{guide.title}</h3>
                            <p className="text-sm text-gray-500">
                              {guide.steps?.length || 0} passos • Categoria: {guide.category}
                            </p>
                          </div>
                        </div>
                        <Badge 
                          variant={guide.is_active ? "default" : "secondary"}
                          className={guide.is_active ? "bg-green-100 text-green-800 border-green-200" : ""}
                        >
                          {guide.is_active ? (
                            <><Eye className="h-3 w-3 mr-1" /> Ativo</>
                          ) : (
                            <><EyeOff className="h-3 w-3 mr-1" /> Inativo</>
                          )}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onEditGuide(guide)}
                          className="text-gray-600 hover:text-indigo-600 hover:bg-indigo-50"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onDeleteGuide(guide.id)}
                          className="text-gray-600 hover:text-red-600 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>
    </motion.div>
  );
}
