/**
 * Componente para gerenciamento de recursos de suporte da Central de Ajuda
 * <AUTHOR> Internet 2025
 */
import React from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Plus,
  Edit,
  Trash2,
  HelpCircle,
  MessageSquare,
  Video,
  FileText,
  Eye,
  EyeOff,
  Shield,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HelpResource } from '@/types/help';

const RESOURCE_TYPES = [
  { value: 'video', label: 'Vídeo Tutorial', emoji: '📹', icon: Video, color: 'text-blue-500' },
  { value: 'article', label: 'Artigo/Documentação', emoji: '📖', icon: FileText, color: 'text-green-500' },
  { value: 'live_support', label: 'Suporte ao Vivo', emoji: '💬', icon: MessageSquare, color: 'text-purple-500' },
  { value: 'emergency', label: 'Suporte Emergencial', emoji: '🆘', icon: HelpCircle, color: 'text-red-500' },
];

interface HelpResourcesTabProps {
  resources: HelpResource[];
  resourcesLoading: boolean;
  onNewResource: () => void;
  onEditResource: (resource: HelpResource) => void;
  onDeleteResource?: (id: string) => void;
}

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 }
};

export function HelpResourcesTab({
  resources,
  resourcesLoading,
  onNewResource,
  onEditResource,
  onDeleteResource
}: HelpResourcesTabProps) {
  return (
    <motion.div 
      variants={itemVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Header Section */}
      <motion.div variants={itemVariants} className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-r from-orange-500 to-red-500 text-white">
              <Shield className="h-6 w-6" />
            </div>
            Recursos de Suporte
          </h2>
          <p className="text-gray-600 mt-1">
            Configure recursos de ajuda e suporte disponíveis
          </p>
        </div>
        <Button
          onClick={onNewResource}
          className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg"
          size="lg"
        >
          <Plus className="h-5 w-5 mr-2" />
          Novo Recurso
        </Button>
      </motion.div>

      {/* Content Section */}
      <motion.div variants={itemVariants}>
        {resourcesLoading ? (
          <Card className="bg-gradient-to-br from-orange-50 to-red-50 border-orange-200">
            <CardContent className="text-center py-16">
              <div className="animate-pulse">
                <Shield className="h-12 w-12 text-orange-400 mx-auto mb-4" />
                <p className="text-orange-600 font-medium">Carregando recursos...</p>
              </div>
            </CardContent>
          </Card>
        ) : resources.length === 0 ? (
          <Card className="bg-gradient-to-br from-orange-50 to-red-50 border-orange-200">
            <CardContent className="text-center py-16">
              <Shield className="h-16 w-16 text-orange-400 mx-auto mb-6" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Nenhum recurso criado</h3>
              <p className="text-gray-600 mb-6">Comece criando seu primeiro recurso de suporte</p>
              <Button 
                onClick={onNewResource}
                className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
              >
                <Plus className="h-4 w-4 mr-2" />
                Criar Primeiro Recurso
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {resources.map((resource, index) => {
              const resourceType = RESOURCE_TYPES.find(r => r.value === resource.resource_type);
              const IconComponent = resourceType?.icon || HelpCircle;
              
              return (
                <motion.div
                  key={resource.id}
                  variants={itemVariants}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-3">
                            <div className={cn(
                              "p-3 rounded-lg",
                              resourceType?.value === 'video' && "bg-blue-100 text-blue-600",
                              resourceType?.value === 'article' && "bg-green-100 text-green-600",
                              resourceType?.value === 'live_support' && "bg-purple-100 text-purple-600",
                              resourceType?.value === 'emergency' && "bg-red-100 text-red-600",
                              !resourceType && "bg-gray-100 text-gray-600"
                            )}>
                              <IconComponent className="h-6 w-6" />
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900 text-lg flex items-center gap-2">
                                <span>{resourceType?.emoji}</span>
                                <span>{resource.title}</span>
                              </h3>
                              <p className="text-sm text-gray-500 max-w-md leading-relaxed">
                                {resource.description}
                              </p>
                              <Badge 
                                variant="outline" 
                                className={cn(
                                  "mt-2 text-xs",
                                  resourceType?.value === 'video' && "bg-blue-50 text-blue-700 border-blue-200",
                                  resourceType?.value === 'article' && "bg-green-50 text-green-700 border-green-200",
                                  resourceType?.value === 'live_support' && "bg-purple-50 text-purple-700 border-purple-200",
                                  resourceType?.value === 'emergency' && "bg-red-50 text-red-700 border-red-200",
                                  !resourceType && "bg-gray-50 text-gray-700 border-gray-200"
                                )}
                              >
                                {resourceType?.label || 'Tipo desconhecido'}
                              </Badge>
                            </div>
                          </div>
                          <Badge 
                            variant={resource.is_active ? "default" : "secondary"}
                            className={resource.is_active ? "bg-green-100 text-green-800 border-green-200" : ""}
                          >
                            {resource.is_active ? (
                              <><Eye className="h-3 w-3 mr-1" /> Ativo</>
                            ) : (
                              <><EyeOff className="h-3 w-3 mr-1" /> Inativo</>
                            )}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => onEditResource(resource)}
                            className="text-gray-600 hover:text-orange-600 hover:bg-orange-50"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          {onDeleteResource && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => onDeleteResource(resource.id)}
                              className="text-gray-600 hover:text-red-600 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        )}
      </motion.div>
    </motion.div>
  );
}
