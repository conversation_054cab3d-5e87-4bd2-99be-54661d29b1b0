/**
 * Componente dedicado para visualização e gerenciamento de Templates de Conhecimento
 * 
 * PADRÃO MOBILE NATIVO: Suporte híbrido mobile/desktop
 * - Layout responsivo com helpers condicionais
 * - Botões híbridos com CSS nativo
 * - Espaçamento otimizado para mobile
 * 
 * <AUTHOR> Internet 2025
 */
import { useState, useMemo, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { HybridButton } from "@/components/ui/HybridButton";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  FileText,
  Search,
  Plus,
  Users,
  Settings,
  Filter,
  Grid3X3,
  List,
  Calendar,
  ChevronRight,
  MoreVertical,
  Star,
  Archive,
  Share2,
  Copy,
  Edit,
  Trash2,
  SortAsc,
  SortDesc,
  Clock,
  TrendingUp,
  Globe,
  Target,
  Zap,
  Activity,
  Bookmark,
  Lock,
  Crown,
  Building2,
  Eye,
} from "lucide-react";

import { KnowledgeTemplate } from "@/types/knowledge.types";
import {
  useKnowledgeTemplates,
  useCombinedKnowledgeTemplates,
} from "@/lib/query/hooks/useKnowledgeTemplates";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
import {
  successWithNotification,
  errorWithNotification,
  infoWithNotification,
} from "@/lib/notifications/toastWithNotification";
import { useQueryClient } from "@tanstack/react-query";
import { GenericPermissionGate } from "@/components/auth/GenericPermissionGate";
import { UseTemplatePermissionDeniedButton } from "@/components/knowledge/UseTemplatePermissionDeniedButton";
import { CreateTemplateSelector } from "@/components/knowledge/CreateTemplateSelector";
import { useGenerateKnowledgeTemplate } from "@/lib/query/hooks/useGenerateKnowledgeTemplate";
import { useAuthStore } from "@/stores/authStore";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import { usePlatform } from "@/hooks/usePlatform";
import { cn } from "@/lib/utils";
import { usePageHybrid } from "@/components/layout/PageHybrid";

// Tipos para filtros e ordenação
type SortOption = "name" | "created_at" | "updated_at" | "usage_count";
type SortDirection = "asc" | "desc";
type FilterType = "all" | "company" | "vindula" | "featured";

interface KnowledgeTemplatesProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onCreateTemplate: () => void;
  compactLayout?: boolean;
}

// Variantes de animação otimizadas
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.1,
    },
  },
};

const cardVariants = {
  hidden: { opacity: 0, y: 15, scale: 0.95 },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  },
};

const headerVariants = {
  hidden: { opacity: 0, y: -20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut",
    },
  },
};

export function KnowledgeTemplates({
  searchQuery,
  onSearchChange,
  onCreateTemplate,
  compactLayout = false
}: KnowledgeTemplatesProps) {
  // Hook para detectar plataforma e helpers híbridos
  const { isNative } = usePlatform();
  const { spacing, iconSize, gap, textSize } = usePageHybrid();
  
  // Mobile sempre usa layout compacto
  const effectiveCompactLayout = compactLayout || isNative;
  
  const [viewMode, setViewMode] = useLocalStorage<"grid" | "list">(
    "knowledgeTemplates-viewMode",
    "grid"
  );
  const [sortBy, setSortBy] = useState<SortOption>("usage_count");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const [filterType, setFilterType] = useState<FilterType>("all");
  const [hasInitiallyLoaded, setHasInitiallyLoaded] = useState(false);
  const [showCreateSelector, setShowCreateSelector] = useState(false);
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { user } = useAuthStore();

  // Definir se deve mostrar opção global (sempre false para uso normal, só true na página admin)
  const showGlobalOption = false;

  // Hook para geração de template com IA
  const generateMutation = useGenerateKnowledgeTemplate();

  // Buscar nome da empresa do usuário
  const companyName = user?.profiles?.companies?.name || "Sua Empresa";

  // Hook para buscar todos os templates (simplificado)
  const {
    data: allTemplates = [],
    isLoading,
    error,
    refetch,
  } = useKnowledgeTemplates({
    search: searchQuery,
    template_source: filterType === "all" ? undefined : filterType,
  });

  // Separar templates por origem - com verificação de segurança
  const vindulaTemplates = Array.isArray(allTemplates)
    ? allTemplates.filter(t => t && t.is_vindula_template)
    : [];
  const companyTemplates = Array.isArray(allTemplates)
    ? allTemplates.filter(t => t && !t.is_vindula_template)
    : [];

  // Marcar quando carregou inicialmente
  useEffect(() => {
    if (!isLoading && Array.isArray(allTemplates)) {
      setHasInitiallyLoaded(true);
    }
  }, [isLoading, allTemplates]);

  // Função de filtro e ordenação aplicada a cada grupo
  const applyFiltersAndSort = (templates: KnowledgeTemplate[]) => {
    let filtered = templates;

    // Aplicar ordenação
    filtered = filtered.sort((a, b) => {
      let valueA = a[sortBy];
      let valueB = b[sortBy];

      if (sortBy === "name") {
        valueA = valueA?.toString().toLowerCase() || "";
        valueB = valueB?.toString().toLowerCase() || "";
      }

      if (sortDirection === "asc") {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });

    return filtered;
  };

  const filteredVindulaTemplates = applyFiltersAndSort(vindulaTemplates);
  const filteredCompanyTemplates = applyFiltersAndSort(companyTemplates);

  // Estatísticas dos templates
  const templatesStats = useMemo(() => {
    // Verificações de segurança para evitar erros
    const safeAllTemplates = Array.isArray(allTemplates) ? allTemplates : [];
    const safeVindulaTemplates = Array.isArray(vindulaTemplates) ? vindulaTemplates : [];
    const safeCompanyTemplates = Array.isArray(companyTemplates) ? companyTemplates : [];

    const total = safeAllTemplates.length;
    const vindula = safeVindulaTemplates.length;
    const company = safeCompanyTemplates.length;
    const featured = safeAllTemplates.filter(t => t && t.is_featured).length;
    const totalUsage = safeAllTemplates.reduce((sum, t) => sum + ((t && t.usage_count) || 0), 0);

    return {
      total,
      vindula,
      company,
      featured,
      totalUsage,
    };
  }, [allTemplates, vindulaTemplates, companyTemplates]);

  // Função para refresh manual
  const handleRefreshTemplates = async () => {
    try {
      await Promise.all([queryClient.invalidateQueries({ queryKey: ["knowledge-templates"] })]);

      await refetch();

      successWithNotification("Dados atualizados!", {
        description: "Templates recarregados com sucesso.",
        persist: false,
      });
    } catch (error) {
      errorWithNotification("Erro ao atualizar", {
        description: "Não foi possível recarregar os dados.",
        persist: false,
      });
    }
  };

  // Função para abrir o seletor de criação de template  
  const handleOpenCreateSelector = () => {
    setShowCreateSelector(true);
  };

  // Função para lidar com o sucesso da geração de template
  const handleGenerateSuccess = (templateId: string) => {
    logQueryEvent("KnowledgeTemplates", "Template gerado com sucesso via IA", { templateId });
    // Atualizar os dados
    refetch();
  };

  const handleTemplateAction = (templateId: string, action: string, isVindulaTemplate = false) => {
    logQueryEvent("KnowledgeTemplates", `Ação do template: ${action}`, {
      templateId,
      action,
      isVindulaTemplate,
    });

    switch (action) {
      case "use":
        navigate(
          `/knowledge/create-page?template=${templateId}&source=${isVindulaTemplate ? "vindula" : "company"}`
        );
        break;
      case "edit":
        if (isVindulaTemplate) {
          infoWithNotification("Template do Vindula", {
            description: "Templates do Vindula não podem ser editados.",
            persist: false,
          });
          return;
        }
        
        // Verificar se o template existe antes de navegar
        logQueryEvent("KnowledgeTemplates", "Navegando para edição de template", { templateId });
        
        try {
          navigate(`/knowledge/template/${templateId}/edit`);
        } catch (error) {
          logQueryEvent("KnowledgeTemplates", "Erro ao navegar para edição", { error }, "error");
          errorWithNotification("Erro de navegação", {
            description: "Não foi possível abrir o editor de template. Tente novamente.",
            persist: false,
          });
        }
        break;
      case "share":
        // Implementar compartilhamento real
        const templateUrl = `${window.location.origin}/knowledge/template/${templateId}`;
        navigator.clipboard.writeText(templateUrl).then(() => {
          successWithNotification("Link copiado!", {
            description: "Link do template copiado para área de transferência.",
            persist: false,
          });
        }).catch(() => {
          errorWithNotification("Erro ao copiar", {
            description: "Não foi possível copiar o link. Tente novamente.",
            persist: false,
          });
        });
        break;
      case "duplicate":
        // Implementar duplicação
        infoWithNotification("Em desenvolvimento", {
          description: "Funcionalidade de duplicação será implementada em breve.",
          persist: false,
        });
        break;
      case "archive":
        // Implementar arquivamento
        infoWithNotification("Em desenvolvimento", {
          description: "Funcionalidade de arquivamento será implementada em breve.",
          persist: false,
        });
        break;
    }
  };

  const handleGenerateTemplate = async (data: any) => {
    try {
      await generateMutation.mutateAsync(data);
      setShowCreateSelector(false);
    } catch (error) {
      console.error('Erro ao gerar template:', error);
    }
  };

  return (
    <GenericPermissionGate
      resourceTypeKey="knowledge_hub"
      actionKey="knowledge_template_view"
      resourceId={null}
      description="Verificar se o usuário pode visualizar templates de conhecimento"
      loadingComponent={
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-8 w-64 bg-gradient-to-r from-green-200 to-emerald-200 rounded-lg animate-pulse"></div>
              <div className="h-5 w-96 bg-muted/50 rounded animate-pulse"></div>
            </div>
            <div className="h-10 w-32 bg-gradient-to-r from-green-200 to-emerald-200 rounded-lg animate-pulse"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <div
                key={i}
                className="h-48 bg-gradient-to-br from-green-100 to-emerald-200 rounded-xl animate-pulse"
              ></div>
            ))}
          </div>
        </div>
      }
      fallbackComponent={
        <Card className="text-center py-16 border-0 bg-gradient-to-br from-red-500/5 to-pink-500/5 shadow-lg">
          <CardContent>
            <motion.div
              animate={{
                y: [0, -10, 0],
                scale: [1, 1.05, 1],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            >
              <Lock className="h-20 w-20 text-red-500 mx-auto mb-6" />
            </motion.div>

            <h3 className="text-2xl font-bold mb-4 text-red-600">Acesso Restrito aos Templates</h3>

            <p className="text-muted-foreground text-lg mb-6 max-w-md mx-auto">
              Você não tem permissão para visualizar templates de conhecimento. Entre em contato com
              um administrador para solicitar acesso.
            </p>

            <div className="p-4 bg-red-50 rounded-lg border border-red-200 max-w-md mx-auto">
              <p className="text-sm text-red-700">
                <strong>Permissão necessária:</strong> Visualizar Modelo de Página
              </p>
            </div>
          </CardContent>
        </Card>
      }
    >
      <KnowledgeTemplatesContent
        searchQuery={searchQuery}
        onSearchChange={onSearchChange}
        onCreateTemplate={handleOpenCreateSelector}
        onCreateTemplateManual={onCreateTemplate}
        templates={allTemplates}
        isLoading={isLoading}
        error={error}
        hasInitiallyLoaded={hasInitiallyLoaded}
        filteredVindulaTemplates={filteredVindulaTemplates}
        filteredCompanyTemplates={filteredCompanyTemplates}
        templatesStats={templatesStats}
        viewMode={viewMode}
        setViewMode={setViewMode}
        sortBy={sortBy}
        setSortBy={setSortBy}
        sortDirection={sortDirection}
        setSortDirection={setSortDirection}
        filterType={filterType}
        setFilterType={setFilterType}
        handleRefreshTemplates={handleRefreshTemplates}
        handleTemplateAction={handleTemplateAction}
        showCreateSelector={showCreateSelector}
        setShowCreateSelector={setShowCreateSelector}
        handleGenerateSuccess={handleGenerateSuccess}
        handleGenerateTemplate={handleGenerateTemplate}
        showGlobalOption={showGlobalOption}
        companyName={companyName}
        compactLayout={effectiveCompactLayout}
      />
    </GenericPermissionGate>
  );
}

// Componente interno com o conteúdo principal dos templates
interface KnowledgeTemplatesContentProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onCreateTemplate: () => void;
  onCreateTemplateManual: () => void;
  templates: KnowledgeTemplate[];
  isLoading: boolean;
  error: Error | null;
  hasInitiallyLoaded: boolean;
  filteredVindulaTemplates: KnowledgeTemplate[];
  filteredCompanyTemplates: KnowledgeTemplate[];
  templatesStats: {
    total: number;
    vindula: number;
    company: number;
    featured: number;
    totalUsage: number;
  };
  viewMode: "grid" | "list";
  setViewMode: (mode: "grid" | "list") => void;
  sortBy: SortOption;
  setSortBy: (sort: SortOption) => void;
  sortDirection: SortDirection;
  setSortDirection: (direction: SortDirection) => void;
  filterType: FilterType;
  setFilterType: (filter: FilterType) => void;
  handleRefreshTemplates: () => void;
  handleTemplateAction: (templateId: string, action: string, isVindulaTemplate?: boolean) => void;
  showCreateSelector: boolean;
  setShowCreateSelector: (show: boolean) => void;
  handleGenerateSuccess: (templateId: string) => void;
  handleGenerateTemplate: (data: any) => Promise<void>;
  showGlobalOption: boolean;
  companyName: string;
  compactLayout?: boolean;
}

function KnowledgeTemplatesContent({
  searchQuery,
  onSearchChange,
  onCreateTemplate,
  onCreateTemplateManual,
  templates,
  isLoading,
  error,
  hasInitiallyLoaded,
  filteredVindulaTemplates,
  filteredCompanyTemplates,
  templatesStats,
  viewMode,
  setViewMode,
  sortBy,
  setSortBy,
  sortDirection,
  setSortDirection,
  filterType,
  setFilterType,
  handleRefreshTemplates,
  handleTemplateAction,
  showCreateSelector,
  setShowCreateSelector,
  handleGenerateSuccess,
  handleGenerateTemplate,
  showGlobalOption,
  companyName,
  compactLayout = false,
}: KnowledgeTemplatesContentProps) {
  // Hook para detectar plataforma e helpers híbridos
  const { isNative } = usePlatform();
  const { spacing, iconSize, gap, textSize } = usePageHybrid();
  
  // Mobile sempre usa layout compacto
  const effectiveCompactLayout = compactLayout || isNative;
  // Loading state - apenas no carregamento inicial
  if (isLoading && !hasInitiallyLoaded) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-8 w-64 bg-gradient-to-r from-green-200 to-emerald-200 rounded-lg animate-pulse"></div>
            <div className="h-5 w-96 bg-muted/50 rounded animate-pulse"></div>
          </div>
          <div className="h-10 w-32 bg-gradient-to-r from-green-200 to-emerald-200 rounded-lg animate-pulse"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <div
              key={i}
              className="h-48 bg-gradient-to-br from-green-100 to-emerald-200 rounded-xl animate-pulse"
            ></div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Card className="text-center py-16 border-0 bg-gradient-to-br from-red-500/5 to-pink-500/5 shadow-lg">
        <CardContent>
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-2xl font-bold text-red-600 mb-2">Erro ao carregar templates</h3>
          <p className="text-muted-foreground text-lg mb-6">
            Não foi possível carregar os templates de conhecimento.
          </p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Tentar novamente
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={gap("space-y-2", "space-y-6")}
    >
      {/* Header Premium com Estatísticas */}
      <motion.div variants={headerVariants}>
        <div className={cn(
          "flex flex-col lg:flex-row lg:justify-between rounded-2xl shadow-xl",
          gap("gap-3 p-3", effectiveCompactLayout ? "gap-4 p-4 lg:items-center" : "gap-6 p-6 lg:items-start"),
          effectiveCompactLayout 
            ? 'bg-gradient-to-r from-slate-50 via-white to-green-50 border-2 border-green-100 text-gray-800' 
            : 'bg-gradient-to-br from-green-600 via-emerald-600 to-green-700 text-white'
        )}>
          <div className={gap("space-y-2", effectiveCompactLayout ? "space-y-3" : "space-y-4")}>
            {/* Header com título e ícone */}
            <div className={cn("flex items-center", gap("gap-2", "gap-4"))}>
              {!effectiveCompactLayout && (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="p-3 bg-white/10 rounded-xl backdrop-blur-sm"
                >
                  <Target className="h-8 w-8 text-green-200" />
                </motion.div>
              )}
              {effectiveCompactLayout && (
                <div className={cn("rounded-lg", isNative ? "p-1 bg-green-100" : "p-2 bg-green-100")}>
                  <Target className={iconSize("h-4 w-4", "h-5 w-5")} />
                </div>
              )}
              <div>
                <h2 className={cn(
                  "font-bold",
                  textSize("text-lg", effectiveCompactLayout ? "text-xl text-gray-800" : "text-3xl")
                )}>
                  {spacing("Templates", effectiveCompactLayout ? "Templates" : "Templates Premium")}
                </h2>
                {!effectiveCompactLayout && (
                  <p className="text-white/90 text-lg">Acelere a criação com templates padronizados</p>
                )}
                {effectiveCompactLayout && !isNative && (
                  <p className="text-gray-600 text-sm">Acelere a criação com templates padronizados</p>
                )}
              </div>
              {/* Indicador discreto de carregamento quando filtrando */}
              {isLoading && hasInitiallyLoaded && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className={cn(
                    "flex items-center gap-2 px-3 py-1 rounded-full backdrop-blur-sm",
                    effectiveCompactLayout ? "bg-green-100 border border-green-200" : "bg-white/10"
                  )}
                >
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className={cn(
                      "w-4 h-4 border-2 rounded-full",
                      effectiveCompactLayout 
                        ? "border-green-300 border-t-green-600" 
                        : "border-white/30 border-t-white"
                    )}
                  />
                  <span className={cn(
                    "text-sm",
                    effectiveCompactLayout ? "text-green-700" : "text-white/80"
                  )}>
                    Filtrando...
                  </span>
                </motion.div>
              )}
            </div>

            {/* Estatísticas Inline - Layout otimizado */}
            {effectiveCompactLayout ? (
              // Layout compacto: estatísticas mais sutis
              <div className="flex flex-wrap items-center gap-2 text-sm">
                <div className="flex items-center gap-1 bg-green-50 border border-green-200 rounded-full px-3 py-1">
                  <div className="text-sm font-semibold text-green-700">{templatesStats.total}</div>
                  <div className="text-green-600 text-xs">Templates</div>
                </div>
                <div className="flex items-center gap-1 bg-blue-50 border border-blue-200 rounded-full px-3 py-1">
                  <div className="text-sm font-semibold text-blue-700">{templatesStats.vindula}</div>
                  <div className="text-blue-600 text-xs">Vindula</div>
                </div>
                <div className="flex items-center gap-1 bg-orange-50 border border-orange-200 rounded-full px-3 py-1">
                  <div className="text-sm font-semibold text-orange-700">{templatesStats.company}</div>
                  <div className="text-orange-600 text-xs">Empresa</div>
                </div>
                <div className="flex items-center gap-1 bg-purple-50 border border-purple-200 rounded-full px-3 py-1">
                  <div className="text-sm font-semibold text-purple-700">{templatesStats.totalUsage}</div>
                  <div className="text-purple-600 text-xs">Usos</div>
                </div>
              </div>
            ) : (
              // Layout normal: estatísticas em grid
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                  <div className="text-2xl font-bold text-green-200">{templatesStats.total}</div>
                  <div className="text-white/80 text-sm">Total de Templates</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                  <div className="text-2xl font-bold text-blue-200">{templatesStats.vindula}</div>
                  <div className="text-white/80 text-sm">Vindula</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                  <div className="text-2xl font-bold text-orange-200">{templatesStats.company}</div>
                  <div className="text-white/80 text-sm">Empresa</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                  <div className="text-2xl font-bold text-purple-200">{templatesStats.featured}</div>
                  <div className="text-white/80 text-sm">Destacados</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                  <div className="text-2xl font-bold text-orange-200">
                    {templatesStats.totalUsage}
                  </div>
                  <div className="text-white/80 text-sm">Total de Usos</div>
                </div>
              </div>
            )}
          </div>
          
          <div className={cn(
            "flex flex-col sm:flex-row items-center",
            gap("gap-2", effectiveCompactLayout ? "gap-2" : "gap-3")
          )}>
            <HybridButton
              onClick={() => setShowCreateSelector(true)}
              className={cn(
                "bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white",
                isNative ? "flex-1 text-base py-3" : (effectiveCompactLayout ? "shadow-lg" : "")
              )}
              size={isNative ? "lg" : (effectiveCompactLayout ? "default" : "lg")}
            >
              <Zap className={iconSize("h-5 w-5 mr-2", effectiveCompactLayout ? "h-4 w-4 mr-1" : "h-5 w-5 mr-2")} />
              {spacing("Criar Template", effectiveCompactLayout ? "Criar Template" : "Criar Template")}
            </HybridButton>
            {!isNative && (
              <HybridButton
                onClick={handleRefreshTemplates}
                variant="outline"
                className={cn(
                  effectiveCompactLayout 
                    ? "bg-white/50 border border-gray-300 text-gray-600 hover:bg-gray-50 hover:text-gray-700 shadow-sm"
                    : "bg-white/10 hover:bg-white/20 text-white border-white/30"
                )}
                size={effectiveCompactLayout ? "default" : "lg"}
              >
                <Activity className={iconSize("h-4 w-4 mr-1", effectiveCompactLayout ? "h-4 w-4 mr-1" : "h-5 w-5 mr-2")} />
                Atualizar
              </HybridButton>
            )}
          </div>
        </div>
      </motion.div>

      {/* Controles Avançados */}
      <motion.div variants={cardVariants}>
        <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
          <CardContent className={gap("p-2", effectiveCompactLayout ? "p-4" : "p-6")}>
            <div className={gap("space-y-2", effectiveCompactLayout ? "space-y-3" : "space-y-4")}>
              {/* Linha Principal de Controles */}
              <div className={cn(
                "flex flex-col lg:flex-row",
                gap("gap-2", effectiveCompactLayout ? "gap-3" : "gap-4")
              )}>
                {/* Busca Aprimorada */}
                <div className="flex-1 relative">
                  <Search className={cn(
                    "absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground",
                    iconSize("h-4 w-4", effectiveCompactLayout ? "h-4 w-4" : "h-5 w-5")
                  )} />
                  <Input
                    placeholder={spacing(
                      "Buscar...", 
                      effectiveCompactLayout ? "Buscar templates..." : "Buscar por nome, descrição, categoria..."
                    )}
                    value={searchQuery}
                    onChange={e => onSearchChange(e.target.value)}
                    className={cn(
                      "border-2 border-muted/20 focus:border-green-500 rounded-xl bg-white shadow-sm transition-all duration-200 focus:shadow-lg",
                      gap(
                        "pl-8 pr-4 h-9 text-sm", 
                        effectiveCompactLayout ? "pl-10 pr-4 h-10 text-sm" : "pl-12 pr-4 h-12 text-base"
                      )
                    )}
                  />
                  {searchQuery && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2"
                    >
                      <Badge
                        variant="secondary"
                        className="bg-green-100 text-green-700 border-green-200"
                      >
                        {filteredVindulaTemplates.length + filteredCompanyTemplates.length}{" "}
                        resultados
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onSearchChange("")}
                        className="h-6 w-6 p-0 rounded-full hover:bg-muted"
                      >
                        ×
                      </Button>
                    </motion.div>
                  )}
                </div>

                {/* Filtros Rápidos - ocultar no mobile */}
                {!isNative && (
                  <div className={cn(
                    "flex items-center",
                    gap("gap-2", effectiveCompactLayout ? "gap-2" : "gap-3")
                  )}>
                  <Select
                    value={filterType}
                    onValueChange={(value: FilterType) => setFilterType(value)}
                  >
                    <SelectTrigger className={`${effectiveCompactLayout ? 'w-[140px] h-10' : 'w-[180px] h-12'} border-2 border-muted/20 focus:border-green-500 ${effectiveCompactLayout ? 'text-sm' : ''}`}>
                      <SelectValue placeholder="Filtrar por" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos ({templatesStats.total})</SelectItem>
                      <SelectItem value="company">Empresa ({templatesStats.company})</SelectItem>
                      <SelectItem value="vindula">Vindula ({templatesStats.vindula})</SelectItem>
                      <SelectItem value="featured">
                        Destacados ({templatesStats.featured})
                      </SelectItem>
                    </SelectContent>
                  </Select>

                  {/* Ordenação */}
                  <Select
                    value={`${sortBy}-${sortDirection}`}
                    onValueChange={value => {
                      const [sort, direction] = value.split("-") as [SortOption, SortDirection];
                      setSortBy(sort);
                      setSortDirection(direction);
                    }}
                  >
                    <SelectTrigger className={`${effectiveCompactLayout ? 'w-[160px] h-10' : 'w-[180px] h-12'} border-2 border-muted/20 focus:border-green-500 ${effectiveCompactLayout ? 'text-sm' : ''}`}>
                      <SelectValue placeholder="Ordenar por" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="updated_at-desc">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          Mais recentes
                        </div>
                      </SelectItem>
                      <SelectItem value="name-asc">
                        <div className="flex items-center gap-2">
                          <SortAsc className="h-4 w-4" />
                          Nome (A-Z)
                        </div>
                      </SelectItem>
                      <SelectItem value="name-desc">
                        <div className="flex items-center gap-2">
                          <SortDesc className="h-4 w-4" />
                          Nome (Z-A)
                        </div>
                      </SelectItem>
                      <SelectItem value="usage_count-desc">
                        <div className="flex items-center gap-2">
                          <TrendingUp className="h-4 w-4" />
                          Mais usados
                        </div>
                      </SelectItem>
                      <SelectItem value="created_at-desc">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          Mais novos
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>

                    {/* Modo de Visualização */}
                    <div className="flex rounded-lg border-2 border-muted/20 overflow-hidden bg-muted/5">
                      <HybridButton
                        variant={viewMode === "grid" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setViewMode("grid")}
                        className={cn(
                          "rounded-none transition-all",
                          gap("h-9 px-2", effectiveCompactLayout ? "h-10 px-3" : "h-12 px-4"),
                          viewMode === "grid"
                            ? "bg-green-500 text-white shadow-sm"
                            : "hover:bg-muted/20",
                          textSize("text-xs", effectiveCompactLayout ? "text-sm" : "")
                        )}
                      >
                        <Grid3X3 className={iconSize("h-3 w-3 mr-1", effectiveCompactLayout ? "h-3 w-3 mr-1" : "h-4 w-4 mr-2")} />
                        Grid
                      </HybridButton>
                      <HybridButton
                        variant={viewMode === "list" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setViewMode("list")}
                        className={cn(
                          "rounded-none transition-all",
                          gap("h-9 px-2", effectiveCompactLayout ? "h-10 px-3" : "h-12 px-4"),
                          viewMode === "list"
                            ? "bg-green-500 text-white shadow-sm"
                            : "hover:bg-muted/20",
                          textSize("text-xs", effectiveCompactLayout ? "text-sm" : "")
                        )}
                      >
                        <List className={iconSize("h-3 w-3 mr-1", effectiveCompactLayout ? "h-3 w-3 mr-1" : "h-4 w-4 mr-2")} />
                        Lista
                      </HybridButton>
                    </div>
                  </div>
                )}
              </div>

              {/* Filtros Ativos */}
              <AnimatePresence>
                {(searchQuery || filterType !== "all") && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="border-t pt-4"
                  >
                    <div className="flex flex-wrap items-center gap-2">
                      <span className="text-sm font-medium text-muted-foreground">
                        Filtros ativos:
                      </span>
                      {searchQuery && (
                        <Badge
                          variant="outline"
                          className="bg-green-50 text-green-700 border-green-200"
                        >
                          Busca: "{searchQuery}"
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onSearchChange("")}
                            className="h-4 w-4 p-0 ml-2 hover:bg-green-200"
                          >
                            ×
                          </Button>
                        </Badge>
                      )}
                      {filterType !== "all" && (
                        <Badge
                          variant="outline"
                          className="bg-emerald-50 text-emerald-700 border-emerald-200"
                        >
                          Tipo:{" "}
                          {filterType === "company"
                            ? "Empresa"
                            : filterType === "vindula"
                              ? "Vindula"
                              : "Destacados"}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setFilterType("all")}
                            className="h-4 w-4 p-0 ml-2 hover:bg-emerald-200"
                          >
                            ×
                          </Button>
                        </Badge>
                      )}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Grid/Lista de Templates */}
      <motion.div variants={cardVariants} className="relative">
        {/* Overlay sutil durante carregamento de filtros */}
        {isLoading && hasInitiallyLoaded && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-white/50 backdrop-blur-[1px] rounded-xl z-10 flex items-center justify-center"
          >
            <div className="flex items-center gap-3 px-4 py-2 bg-white/80 rounded-full shadow-lg border">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                className="w-5 h-5 border-2 border-green-300 border-t-green-600 rounded-full"
              />
              <span className="text-sm font-medium text-green-700">Atualizando dados...</span>
            </div>
          </motion.div>
        )}

        {filteredVindulaTemplates.length === 0 && filteredCompanyTemplates.length === 0 ? (
          <EmptyTemplatesState
            searchQuery={searchQuery}
            filterType={filterType}
            onCreateTemplate={() => setShowCreateSelector(true)}
            onClearFilters={() => setFilterType("all")}
          />
        ) : (
          <div className="space-y-8">
            {/* Templates do Vindula */}
            {(filterType === "all" || filterType === "vindula") &&
              filteredVindulaTemplates.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4 }}
                  className="space-y-4"
                >
                  <div className="flex items-center gap-3 pb-4 border-b border-gradient-to-r from-blue-200 to-purple-200">
                    <div className="flex items-center gap-2">
                      <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
                        <Crown className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h2 className="text-xl font-bold text-blue-900">Templates do Vindula</h2>
                        <p className="text-sm text-blue-600">
                          Templates profissionais criados pela equipe Vindula
                        </p>
                      </div>
                    </div>
                    <Badge
                      variant="secondary"
                      className="bg-blue-100 text-blue-700 border-blue-200 ml-auto"
                    >
                      {filteredVindulaTemplates.length} templates
                    </Badge>
                  </div>

                  <motion.div
                    className={
                      viewMode === "grid"
                        ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                        : "space-y-4"
                    }
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                  >
                    <AnimatePresence>
                      {filteredVindulaTemplates.map((template, index) => (
                        <TemplateCard
                          key={template.id}
                          template={template}
                          index={index}
                          viewMode={viewMode}
                          onAction={action => handleTemplateAction(template.id, action, true)}
                        />
                      ))}
                    </AnimatePresence>
                  </motion.div>
                </motion.div>
              )}

            {/* Templates da Empresa */}
            {(filterType === "all" || filterType === "company") &&
              filteredCompanyTemplates.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.2 }}
                  className="space-y-4"
                >
                  <div className="flex items-center gap-3 pb-4 border-b border-gradient-to-r from-green-200 to-emerald-200">
                    <div className="flex items-center gap-2">
                      <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
                        <Building2 className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h2 className="text-xl font-bold text-green-900">
                          Templates de {companyName}
                        </h2>
                        <p className="text-sm text-green-600">
                          Templates personalizados criados pela sua equipe
                        </p>
                      </div>
                    </div>
                    <Badge
                      variant="secondary"
                      className="bg-green-100 text-green-700 border-green-200 ml-auto"
                    >
                      {filteredCompanyTemplates.length} templates
                    </Badge>
                  </div>

                  <motion.div
                    className={
                      viewMode === "grid"
                        ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                        : "space-y-4"
                    }
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                  >
                    <AnimatePresence>
                      {filteredCompanyTemplates.map((template, index) => (
                        <TemplateCard
                          key={template.id}
                          template={template}
                          index={index}
                          viewMode={viewMode}
                          onAction={action => handleTemplateAction(template.id, action, false)}
                        />
                      ))}
                    </AnimatePresence>
                  </motion.div>
                </motion.div>
              )}

            {/* Mensagem quando não há templates na categoria selecionada */}
            {((filterType === "vindula" && filteredVindulaTemplates.length === 0) ||
              (filterType === "company" && filteredCompanyTemplates.length === 0) ||
              (filterType === "featured" &&
                filteredVindulaTemplates.filter(t => t.is_featured).length === 0 &&
                filteredCompanyTemplates.filter(t => t.is_featured).length === 0)) && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-12"
              >
                <div className="text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">Nenhum template encontrado</p>
                  <p className="text-sm">
                    {filterType === "vindula" &&
                      "Não há templates do Vindula disponíveis no momento."}
                    {filterType === "company" &&
                      `Sua empresa ainda não criou templates personalizados.`}
                    {filterType === "featured" && "Não há templates destacados no momento."}
                  </p>
                </div>
              </motion.div>
            )}
          </div>
        )}
      </motion.div>

      {/* Seletor de Criação de Template */}
      <CreateTemplateSelector
        isOpen={showCreateSelector}
        onClose={() => setShowCreateSelector(false)}
        onCreateTemplate={onCreateTemplateManual}
      />
    </motion.div>
  );
}

// Componente TemplateCard Melhorado
interface TemplateCardProps {
  template: KnowledgeTemplate;
  index: number;
  viewMode: "grid" | "list";
  onAction: (action: string) => void;
}

function TemplateCard({ template, index, viewMode, onAction }: TemplateCardProps) {
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        delay: index * 0.1,
        ease: "easeOut",
      },
    },
  };

  const isVindulaTemplate = template.is_vindula_template;
  const cardGradient = isVindulaTemplate
    ? "from-blue-50 via-purple-50 to-pink-50"
    : "from-green-50 via-emerald-50 to-teal-50";

  const borderGradient = isVindulaTemplate
    ? "border-gradient-to-r from-blue-200 to-purple-200"
    : "border-gradient-to-r from-green-200 to-emerald-200";

  if (viewMode === "list") {
    return (
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        exit="hidden"
        className={`bg-gradient-to-r ${cardGradient} border ${borderGradient} rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02]`}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-3">
              {isVindulaTemplate && (
                <Badge className="bg-blue-500 text-white border-blue-600">
                  <Crown className="h-3 w-3 mr-1" />
                  Vindula
                </Badge>
              )}
              {template.is_featured && (
                <Badge className="bg-amber-500 text-white border-amber-600">
                  <Star className="h-3 w-3 mr-1" />
                  Destaque
                </Badge>
              )}
              <Badge variant="outline" className="capitalize">
                {template.category || "Geral"}
              </Badge>
            </div>

            <h3 className="text-lg font-semibold text-gray-900 mb-2">{template.name}</h3>
            <p className="text-gray-600 text-sm mb-4 line-clamp-2">{template.description}</p>

            <div className="flex items-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <Eye className="h-4 w-4" />
                <span>{template.usage_count || 0} usos</span>
              </div>

              {template.difficulty_level && (
                <div className="flex items-center gap-1">
                  <Target className="h-4 w-4" />
                  <span className="capitalize">{template.difficulty_level}</span>
                </div>
              )}

              {template.estimated_time && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>{template.estimated_time} min</span>
                </div>
              )}

              {template.creator_name && (
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  <span>{template.creator_name}</span>
                </div>
              )}
            </div>
          </div>

          <TemplateActions template={template} onAction={onAction} />
        </div>
      </motion.div>
    );
  }

  // Grid view
  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      exit="hidden"
      className={`group bg-gradient-to-br ${cardGradient} border ${borderGradient} rounded-xl p-6 shadow-sm hover:shadow-xl transition-all duration-500 transform hover:scale-[1.05] cursor-pointer relative overflow-hidden`}
    >
      {/* Efeito de brilho para templates do Vindula */}
      {isVindulaTemplate && (
        <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 via-purple-400/10 to-pink-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      )}

      <div className="relative z-10">
        <div className="flex items-start justify-between mb-4">
          <div className="flex flex-col gap-2">
            {isVindulaTemplate && (
              <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white border-0 w-fit">
                <Crown className="h-3 w-3 mr-1" />
                Vindula
              </Badge>
            )}
            {template.is_featured && (
              <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 w-fit">
                <Star className="h-3 w-3 mr-1" />
                Destaque
              </Badge>
            )}
          </div>

          <TemplateActions template={template} onAction={onAction} />
        </div>

        <h3 className="text-lg font-bold text-gray-900 mb-2 line-clamp-1">{template.name}</h3>
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">{template.description}</p>

        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <Badge variant="outline" className="capitalize">
              {template.category || "Geral"}
            </Badge>
            <div className="flex items-center gap-1 text-gray-500">
              <Eye className="h-4 w-4" />
              <span>{template.usage_count || 0}</span>
            </div>
          </div>

          <div className="flex items-center gap-4 text-xs text-gray-500">
            {template.difficulty_level && (
              <div className="flex items-center gap-1">
                <Target className="h-3 w-3" />
                <span className="capitalize">{template.difficulty_level}</span>
              </div>
            )}

            {template.estimated_time && (
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{template.estimated_time}min</span>
              </div>
            )}
          </div>

          {template.creator_name && (
            <div className="flex items-center gap-2 pt-2 border-t border-gray-200">
              <div className="w-6 h-6 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full flex items-center justify-center">
                <Users className="h-3 w-3 text-gray-600" />
              </div>
              <span className="text-xs text-gray-600 truncate">{template.creator_name}</span>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
}

// Componente de Ações do Template
interface TemplateActionsProps {
  template: KnowledgeTemplate;
  onAction: (action: string) => void;
}

function TemplateActions({ template, onAction }: TemplateActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          onClick={e => e.stopPropagation()}
          className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <MoreVertical className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuLabel>Ações do Template</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <GenericPermissionGate
          resourceTypeKey="knowledge_hub"
          actionKey="knowledge_template_use"
          resourceId={template.id}
          description="Verificar se o usuário pode usar este template no dropdown"
          fallbackComponent={
            <UseTemplatePermissionDeniedButton isMenuItem templateName={template.name} />
          }
        >
          <DropdownMenuItem onClick={() => onAction("use")}>
            <Zap className="h-4 w-4 mr-2" />
            Usar Template
          </DropdownMenuItem>
        </GenericPermissionGate>
        <DropdownMenuItem onClick={() => onAction("edit")}>
          <Edit className="h-4 w-4 mr-2" />
          Editar
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onAction("duplicate")}>
          <Copy className="h-4 w-4 mr-2" />
          Duplicar
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onAction("share")}>
          <Share2 className="h-4 w-4 mr-2" />
          Compartilhar
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => onAction("archive")} className="text-orange-600">
          <Archive className="h-4 w-4 mr-2" />
          Arquivar
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Estado Vazio Melhorado
interface EmptyTemplatesStateProps {
  searchQuery: string;
  filterType: FilterType;
  onCreateTemplate: () => void;
  onClearFilters: () => void;
}

function EmptyTemplatesState({
  searchQuery,
  filterType,
  onCreateTemplate,
  onClearFilters,
}: EmptyTemplatesStateProps) {
  const hasFilters = searchQuery || filterType !== "all";

  return (
    <Card className="text-center py-16 border-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 shadow-lg">
      <CardContent>
        <motion.div
          animate={{
            y: [0, -20, 0],
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        >
          <Target className="h-20 w-20 text-muted-foreground mx-auto mb-6" />
        </motion.div>

        <h3 className="text-2xl font-bold mb-4 bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
          {hasFilters ? "Nenhum template encontrado" : "Nenhum template criado ainda"}
        </h3>

        <p className="text-muted-foreground text-lg mb-6 max-w-md mx-auto">
          {hasFilters
            ? `Nenhum template corresponde aos filtros aplicados.`
            : "Ainda não há templates criados. Crie modelos para agilizar a criação!"}
        </p>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {hasFilters ? (
            <>
              <Button onClick={onClearFilters} variant="outline" size="lg">
                <Filter className="h-5 w-5 mr-2" />
                Limpar Filtros
              </Button>
              <Button
                onClick={onCreateTemplate}
                className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
                size="lg"
              >
                <Zap className="h-5 w-5 mr-2" />
                Criar Template
              </Button>
            </>
          ) : (
            <Button
              onClick={onCreateTemplate}
              className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
              size="lg"
            >
              <Zap className="h-5 w-5 mr-2" />
              Criar Template
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
