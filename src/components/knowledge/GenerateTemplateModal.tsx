/**
 * Modal para gerar templates de conhecimento usando IA
 * <AUTHOR> Internet 2025
 */
import { useState } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { PostEditor } from "@/components/editor/PostEditor";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Loader2, <PERSON><PERSON>les, Clock, BarChart, Globe, X, Info, Building2, Users, Target, Lightbulb, FileText, Settings, MessageSquare, Package, CalendarDays, Search, Heart, TrendingUp, HelpCircle, Coins } from "lucide-react";
import { useGenerateKnowledgeTemplate } from "@/lib/query/hooks/useGenerateKnowledgeTemplate";
import { useAIFeatureAvailability, useAICreditBalance } from "@/lib/query/hooks/useAICredits";
import { cn } from "@/lib/utils";

interface GenerateTemplateModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: (templateId: string) => void;
  showGlobalOption?: boolean; // Controla se mostra a opção de template global
}

// Mapeamento das categorias para o frontend e enum do banco
const CATEGORIES = [
  { label: 'Reuniões e Atas', value: 'meeting' },
  { label: 'Projetos e Planejamento', value: 'project' },
  { label: 'Processos e Procedimentos', value: 'process' },
  { label: 'Decisões e Aprovações', value: 'decision' },
  { label: 'Guias e Manuais', value: 'runbook' },
  { label: 'Tutoriais e Treinamento', value: 'guide' },
  { label: 'Outros', value: 'other' }
];

const DIFFICULTY_LEVELS = [
  { label: 'Básico', value: 'beginner' },
  { label: 'Intermediário', value: 'intermediate' },
  { label: 'Avançado', value: 'advanced' }
];

const TIME_ESTIMATES = [
  { label: '5-10 minutos', value: '5-10' },
  { label: '15-30 minutos', value: '15-30' },
  { label: '30-60 minutos', value: '30-60' },
  { label: 'Mais de 1 hora', value: '60+' }
];

const COMPANY_TYPES = [
  { label: 'Startup', value: 'startup' },
  { label: 'Pequena Empresa', value: 'small' },
  { label: 'Média Empresa', value: 'medium' },
  { label: 'Grande Empresa', value: 'large' },
  { label: 'Corporação', value: 'corporation' },
  { label: 'ONG', value: 'ngo' },
  { label: 'Órgão Público', value: 'government' },
  { label: 'Consultoria', value: 'consulting' }
];

const BUSINESS_SECTORS = [
  { label: 'Tecnologia', value: 'technology' },
  { label: 'Saúde', value: 'healthcare' },
  { label: 'Educação', value: 'education' },
  { label: 'Financeiro', value: 'finance' },
  { label: 'Varejo', value: 'retail' },
  { label: 'Manufatura', value: 'manufacturing' },
  { label: 'Serviços', value: 'services' },
  { label: 'Construção', value: 'construction' },
  { label: 'Alimentos', value: 'food' },
  { label: 'Energia', value: 'energy' },
  { label: 'Transporte', value: 'transport' },
  { label: 'Mídia', value: 'media' },
  { label: 'Imobiliário', value: 'realestate' },
  { label: 'Jurídico', value: 'legal' },
  { label: 'Outros', value: 'other' }
];

const KNOWLEDGE_AREAS = [
  { label: 'Documentação Técnica', value: 'technical_documentation', icon: FileText },
  { label: 'Políticas e Procedimentos', value: 'policies_procedures', icon: Settings },
  { label: 'Treinamentos e Capacitação', value: 'training_development', icon: Target },
  { label: 'Comunicação Interna', value: 'internal_communication', icon: MessageSquare },
  { label: 'Produtos e Serviços', value: 'products_services', icon: Package },
  { label: 'Gestão de Projetos', value: 'project_management', icon: CalendarDays },
  { label: 'Inovação e Pesquisa', value: 'innovation_research', icon: Lightbulb },
  { label: 'Cultura Organizacional', value: 'organizational_culture', icon: Heart },
  { label: 'Processos e Qualidade', value: 'processes_quality', icon: TrendingUp },
  { label: 'Estratégia e Planejamento', value: 'strategy_planning', icon: Target },
  { label: 'Colaboração e Equipes', value: 'collaboration_teams', icon: Users },
  { label: 'Conhecimento Externo', value: 'external_knowledge', icon: Globe }
];

export function GenerateTemplateModal({
  open,
  onOpenChange,
  onSuccess,
  showGlobalOption = false,
}: GenerateTemplateModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '<p></p>', // Estado inicial HTML vazio para o editor
    category: '',
    difficulty: '',
    timeEstimate: '',
    companyType: '',
    businessSector: '',
    teamSize: '',
    knowledgeAreas: [] as string[],
    specificRequirements: '',
    isGlobal: false
  });

  const [isGenerating, setIsGenerating] = useState(false);

  const generateMutation = useGenerateKnowledgeTemplate();

  // Sistema de créditos de IA
  const { data: balance } = useAICreditBalance();
  const { 
    isAvailable, 
    creditsRequired, 
    insufficientCredits 
  } = useAIFeatureAvailability('knowledge_template_generation');

  // Cálculos de créditos
  const actualCreditsRequired = creditsRequired || 1;
  const actualCreditsRemaining = balance?.remaining_credits || 0;
  const totalCredits = balance?.plan_credits || 5;
  const hasEnoughCredits = actualCreditsRemaining >= actualCreditsRequired;

  // Funcionalidade disponível se: existe e está ativa, OU se temos créditos suficientes
  const isFeatureAvailable = (isAvailable !== false) && hasEnoughCredits;

  // Verificar se deve mostrar opção global (apenas quando explicitamente permitido)
  const canMarkGlobal = showGlobalOption;

  // Função helper para extrair texto do HTML
  const extractTextFromHTML = (html: string): string => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title || !formData.category || !formData.companyType || !formData.businessSector) return;

    // Verificar créditos antes de prosseguir
    if (!hasEnoughCredits) {
      console.log('💸 Créditos insuficientes - bloqueando geração');
      return;
    }

    setIsGenerating(true);
    try {
      // Extrair texto da descrição HTML para o backend
      const descriptionText = extractTextFromHTML(formData.description);
      
      const result = await generateMutation.mutateAsync({
        title: formData.title,
        description: descriptionText, // Enviar texto limpo para a IA
        descriptionHTML: formData.description, // Manter HTML para referencia
        category: formData.category,
        difficulty: formData.difficulty,
        timeEstimate: formData.timeEstimate,
        companyType: formData.companyType,
        businessSector: formData.businessSector,
        teamSize: formData.teamSize,
        knowledgeAreas: formData.knowledgeAreas,
        specificRequirements: formData.specificRequirements,
        isGlobal: canMarkGlobal && formData.isGlobal,
      });

      // Resetar form
      setFormData({
        title: '',
        description: '<p></p>', // Reset HTML vazio para o editor
        category: '',
        difficulty: '',
        timeEstimate: '',
        companyType: '',
        businessSector: '',
        teamSize: '',
        knowledgeAreas: [],
        specificRequirements: '',
        isGlobal: false
      });

      onOpenChange(false);
      onSuccess?.(result.templateId);
    } catch (error) {
      console.error("Erro ao gerar template:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!generateMutation.isPending) {
      onOpenChange(newOpen);
    }
  };

  const toggleKnowledgeArea = (area: string) => {
    setFormData(prev => ({
      ...prev,
      knowledgeAreas: prev.knowledgeAreas.includes(area)
        ? prev.knowledgeAreas.filter(a => a !== area)
        : [...prev.knowledgeAreas, area]
    }));
  };

  const selectedCategory = CATEGORIES.find(cat => cat.value === formData.category);

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent
        className="max-w-4xl w-[95vw] max-h-[85vh] overflow-hidden relative"
        style={{
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          position: "fixed",
          margin: "0",
        }}
      >
        <DialogHeader className="pb-4 border-b">
          <DialogTitle className="flex items-center justify-between text-xl">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Sparkles className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h2 className="font-semibold text-gray-900">Gerar Template com IA</h2>
                <p className="text-sm text-gray-600 font-normal">
                  Crie templates personalizados usando inteligência artificial
                </p>
              </div>
            </div>

            {/* Badge de saldo de créditos */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge 
                    variant="outline" 
                    className={cn(
                      "flex items-center gap-2 px-3 py-1.5 text-sm font-medium",
                      actualCreditsRemaining <= 2 
                        ? "border-red-200 bg-red-50 text-red-700"
                        : actualCreditsRemaining <= 5 
                          ? "border-yellow-200 bg-yellow-50 text-yellow-700"
                          : "border-green-200 bg-green-50 text-green-700"
                    )}
                  >
                    <Coins className="w-4 h-4" />
                    {actualCreditsRemaining}
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Você tem {actualCreditsRemaining} de {totalCredits} créditos restantes este mês</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </DialogTitle>
        </DialogHeader>

        <div className="overflow-y-auto max-h-[calc(85vh-140px)] pb-6">
          {/* Aviso de créditos baixos */}
          {actualCreditsRemaining < actualCreditsRequired && (
            <Alert className="mt-4 border-red-200 bg-red-50">
              <Coins className="h-4 w-4 text-red-600" />
              <AlertTitle className="text-red-800">Créditos insuficientes para geração</AlertTitle>
              <AlertDescription className="text-red-700">
                <p>A geração de templates precisa de {actualCreditsRequired} crédito, mas você tem apenas {actualCreditsRemaining}.</p>
                <p className="mt-1">Aguarde a renovação mensal ou adquira créditos adicionais.</p>
              </AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-6 pt-6">
            {/* Prompt Principal */}
            <div className="space-y-3">
              <Label htmlFor="title" className="text-base font-medium">
                Título do Template *
              </Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Ex: Template de Ata de Reunião SIPAT"
                className="h-11"
                required
              />
            </div>

            {/* Categoria */}
            <div className="space-y-3">
              <Label htmlFor="category" className="text-base font-medium">
                Categoria *
              </Label>
              <Select
                value={formData.category}
                onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                disabled={generateMutation.isPending}
                required
              >
                <SelectTrigger className="h-11">
                  <SelectValue placeholder="Selecione uma categoria" />
                </SelectTrigger>
                <SelectContent>
                  {CATEGORIES.map(cat => (
                    <SelectItem key={cat.value} value={cat.value}>
                      {cat.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Descrição com Editor Rico */}
            <div className="space-y-3">
              <Label className="text-base font-medium">
                Descrição do Template
              </Label>
              <p className="text-sm text-gray-600">
                Use o editor rico para descrever detalhadamente o propósito, contexto e como usar este template.
              </p>
              <PostEditor
                content={formData.description}
                onChange={(content) => setFormData(prev => ({ ...prev, description: content }))}
                placeholder="Descreva o propósito e uso do template. Você pode usar formatação rica, inserir imagens e gerar conteúdo com IA..."
                tempPostId={`template-${Date.now()}`}
              />
            </div>

            {/* Contexto Empresarial */}
            <div className="space-y-4">
              <h3 className="text-base font-medium text-gray-900 border-b pb-2">
                Contexto Empresarial
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Building2 className="w-4 h-4 text-gray-600" />
                    Tipo da empresa *
                  </Label>
                  <Select
                    value={formData.companyType}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, companyType: value }))}
                    disabled={generateMutation.isPending}
                    required
                  >
                    <SelectTrigger className="h-11">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {COMPANY_TYPES.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <BarChart className="w-4 h-4 text-gray-600" />
                    Setor de atuação *
                  </Label>
                  <Select
                    value={formData.businessSector}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, businessSector: value }))}
                    disabled={generateMutation.isPending}
                    required
                  >
                    <SelectTrigger className="h-11">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {BUSINESS_SECTORS.map(sector => (
                        <SelectItem key={sector.value} value={sector.value}>
                          {sector.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-3">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <Users className="w-4 h-4 text-gray-600" />
                  Tamanho da equipe
                </Label>
                <Input
                  id="teamSize"
                  value={formData.teamSize}
                  onChange={(e) => setFormData(prev => ({ ...prev, teamSize: e.target.value }))}
                  placeholder="Ex: 50"
                  className="h-11"
                  disabled={generateMutation.isPending}
                />
              </div>
            </div>

            {/* Áreas de Conhecimento */}
            <div className="space-y-4">
              <div>
                <Label className="text-base font-medium text-gray-900">
                  Áreas de conhecimento (selecione as relevantes)
                </Label>
                <p className="text-sm text-gray-500 mt-1">
                  Selecione as áreas que este template deve abordar
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {KNOWLEDGE_AREAS.map(area => {
                  const Icon = area.icon;
                  const isSelected = formData.knowledgeAreas.includes(area.value);
                  
                  return (
                    <button
                      key={area.value}
                      type="button"
                      onClick={() => toggleKnowledgeArea(area.value)}
                      className={cn(
                        "flex items-center space-x-3 p-3 rounded-lg border transition-all",
                        isSelected 
                          ? "bg-purple-50 border-purple-200 text-purple-700" 
                          : "bg-white border-gray-200 text-gray-700 hover:bg-gray-50"
                      )}
                    >
                      <Icon className="w-5 h-5" />
                      <span className="text-sm font-medium">{area.label}</span>
                    </button>
                  );
                })}
              </div>
              
              <div className="text-sm text-gray-600">
                Selecionadas: {formData.knowledgeAreas.length} área(s) de conhecimento selecionada(s)
              </div>
            </div>

            {/* Configurações Avançadas */}
            <div className="space-y-4">
              <h3 className="text-base font-medium text-gray-900 border-b pb-2">
                Configurações Avançadas
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <BarChart className="w-4 h-4 text-gray-600" />
                    Nível de Dificuldade
                  </Label>
                  <Select
                    value={formData.difficulty}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, difficulty: value }))}
                    disabled={generateMutation.isPending}
                  >
                    <SelectTrigger className="h-11">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {DIFFICULTY_LEVELS.map(level => (
                        <SelectItem key={level.value} value={level.value}>
                          {level.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Clock className="w-4 h-4 text-gray-600" />
                    Tempo Estimado de Preenchimento
                  </Label>
                  <Select
                    value={formData.timeEstimate}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, timeEstimate: value }))}
                    disabled={generateMutation.isPending}
                  >
                    <SelectTrigger className="h-11">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {TIME_ESTIMATES.map(time => (
                        <SelectItem key={time.value} value={time.value}>
                          {time.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-3">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <FileText className="w-4 h-4 text-gray-600" />
                  Requisitos específicos (opcional)
                </Label>
                <Textarea
                  id="specificRequirements"
                  value={formData.specificRequirements}
                  onChange={(e) => setFormData(prev => ({ ...prev, specificRequirements: e.target.value }))}
                  placeholder="Descreva necessidades específicas, estrutura organizacional, processos importantes, etc."
                  className="min-h-[100px]"
                  disabled={generateMutation.isPending}
                />
              </div>
            </div>

            {/* Opção Global (apenas para Vindula) */}
            {canMarkGlobal && (
              <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl">
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="isGlobal"
                    checked={formData.isGlobal}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isGlobal: checked as boolean }))}
                    disabled={generateMutation.isPending}
                    className="mt-1"
                  />
                  <div className="space-y-1">
                    <Label htmlFor="isGlobal" className="flex items-center gap-2 font-medium">
                      <Globe className="w-4 h-4 text-blue-600" />
                      Disponibilizar como template global
                    </Label>
                    <p className="text-sm text-blue-700">
                      Templates globais ficam disponíveis para todos os clientes da plataforma
                      Vindula.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Preview das configurações */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <h4 className="text-sm font-medium text-gray-900">Resumo da Configuração</h4>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary" className="text-sm">
                  {selectedCategory ? selectedCategory.label : "Categoria não selecionada"}
                </Badge>
                <Badge variant="outline">
                  {formData.difficulty ? DIFFICULTY_LEVELS.find(l => l.value === formData.difficulty)?.label : "Nível de Dificuldade não selecionado"}
                </Badge>
                <Badge variant="outline">{formData.timeEstimate ? formData.timeEstimate : "Tempo Estimado não selecionado"}</Badge>
                {canMarkGlobal && formData.isGlobal && (
                  <Badge variant="default" className="bg-blue-600">
                    🌍 Global
                  </Badge>
                )}
              </div>
            </div>

            {/* Botões */}
            <div className="flex justify-end gap-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={generateMutation.isPending}
                className="h-11 px-6"
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={
                  !formData.title || 
                  !formData.category || 
                  !formData.companyType || 
                  !formData.businessSector || 
                  generateMutation.isPending ||
                  !hasEnoughCredits
                }
                className={cn(
                  "min-w-[180px] h-11",
                  hasEnoughCredits
                    ? "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    : "bg-gray-400 cursor-not-allowed"
                )}
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Gerando...
                  </>
                ) : !hasEnoughCredits ? (
                  <>
                    <X className="w-4 h-4 mr-2" />
                    Créditos insuficientes
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4 mr-2" />
                    Gerar Template ({actualCreditsRequired} crédito)
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>

        {/* Loading Overlay - Padrão Premium */}
        {generateMutation.isPending && (
          <div className="absolute inset-0 bg-white/95 backdrop-blur-sm z-50 flex items-center justify-center">
            <div className="text-center max-w-md p-8">
              <div className="relative">
                {/* Ícone principal com animação */}
                <div className="w-24 h-24 mx-auto mb-6 relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full animate-pulse opacity-20"></div>
                  <div className="absolute inset-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center">
                    <Sparkles className="w-10 h-10 text-white animate-bounce" />
                  </div>
                </div>

                {/* Texto principal */}
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Gerando seu template...
                </h3>
                <p className="text-gray-600 mb-4">
                  Nossa IA está criando um template personalizado baseado em sua descrição.
                </p>

                {/* Barra de progresso simulada */}
                <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                  <div
                    className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full animate-pulse"
                    style={{ width: "65%" }}
                  ></div>
                </div>

                {/* Texto de aguarde */}
                <p className="text-sm text-gray-500">
                  Isso pode levar até 1 minuto. Por favor, aguarde...
                </p>
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
