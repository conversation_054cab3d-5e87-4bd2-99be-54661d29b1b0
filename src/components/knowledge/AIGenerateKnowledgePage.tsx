/**
 * AIGenerateKnowledgePage.tsx
 * Componente para geração automática de páginas de conhecimento com IA
 * <AUTHOR> Internet 2025
 */
import React, { useState, useEffect, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  BookOpen,
  Zap,
  Sparkles,
  FileText,
  Users,
  Settings,
  Lightbulb,
  Target,
  Brain,
  Rocket,
  Heart,
  TrendingUp,
  Shield,
  Coffee,
  Search,
  Clock,
  Crown,
  AlertTriangle,
  Wand2,
  CheckCircle,
  X,
  ChevronRight,
  Building2,
  GraduationCap,
  ShoppingCart,
  Briefcase,
  Stethoscope,
  Hammer,
  Car,
  Utensils,
  Home,
  Plane,
  Gamepad2,
  Palette,
  DollarSign,
  Scale,
  Truck,
  Shirt,
  Wheat,
  Factory,
  Cpu,
  Filter
} from "lucide-react";
import { 
  errorWithNotification,
  successWithNotification 
} from "@/lib/notifications/toastWithNotification";
import { useAIFeatureWithCredits, useAIFeatureAvailability } from "@/lib/query/hooks/useAICredits";
import { useUpgradeContext } from "@/hooks/useUpgradeContext";
import { useKnowledgeSpaces } from "@/lib/query/hooks/useKnowledgeSpaces";
import { useGenericPermissionCheck } from "@/lib/query/hooks/useGenericPermissionCheck";
import type { KnowledgeSpace } from "@/types/knowledge.types";

interface AIGenerateKnowledgePageProps {
  isOpen: boolean;
  onClose: () => void;
  preSelectedSpaceId?: string;
}

type KnowledgeCategory = {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  gradient: string;
  examples: string[];
  prompt: string;
  estimatedTime: string;
  fields: Array<{
    key: string;
    label: string;
    type: 'text' | 'textarea' | 'select';
    placeholder: string;
    required: boolean;
    options?: string[];
  }>;
};

type FormData = Record<string, string>;

// Componente para validar permissões de um espaço específico
interface SpaceWithPermissionProps {
  space: KnowledgeSpace;
  children: (hasPermission: boolean, isLoading: boolean) => React.ReactNode;
}

function SpaceWithPermission({ space, children }: SpaceWithPermissionProps) {
  const { hasPermission, isLoading } = useGenericPermissionCheck({
    resourceTypeKey: "knowledge_hub",
    actionKey: "knowledge_page_create",
    resourceId: space.id,
    description: `Criar página no espaço "${space.name}"`,
    enabled: true
  });

  return <>{children(hasPermission, isLoading)}</>;
}

// Hook personalizado para filtrar espaços com permissões
function useSpacesWithPermissions(spaces: KnowledgeSpace[]) {
  const [spacesWithPermissions, setSpacesWithPermissions] = useState<{
    space: KnowledgeSpace;
    hasPermission: boolean;
    isLoading: boolean;
  }[]>([]);

  useEffect(() => {
    // Inicializar com todos os espaços como carregando
    setSpacesWithPermissions(
      spaces.map(space => ({
        space,
        hasPermission: false,
        isLoading: true
      }))
    );
  }, [spaces]);

  const updateSpacePermission = (spaceId: string, hasPermission: boolean, isLoading: boolean) => {
    setSpacesWithPermissions(prev => 
      prev.map(item => 
        item.space.id === spaceId 
          ? { ...item, hasPermission, isLoading }
          : item
      )
    );
  };

  const availableSpaces = spacesWithPermissions
    .filter(item => item.hasPermission && !item.isLoading)
    .map(item => item.space);

  const isLoadingPermissions = spacesWithPermissions.some(item => item.isLoading);

  return {
    availableSpaces,
    isLoadingPermissions,
    updateSpacePermission,
    spacesWithPermissions
  };
}

const knowledgeCategories: KnowledgeCategory[] = [
  {
    id: "technical-guide",
    title: "Guia Técnico",
    description: "Documentação técnica, APIs, processos e procedimentos",
    icon: Settings,
    color: "blue",
    gradient: "from-blue-500 to-indigo-600",
    examples: ["Guia de API", "Processo de deploy", "Configuração de sistemas"],
    prompt: "Crie um guia técnico completo sobre: {topic}. Área técnica: {area}. Nível de complexidade: {complexity}. Inclua introdução, pré-requisitos, passo-a-passo detalhado, exemplos práticos, troubleshooting comum e referências. Use linguagem técnica mas clara.",
    estimatedTime: "5-7 min",
    fields: [
      {
        key: "topic",
        label: "Tópico técnico",
        type: "text",
        placeholder: "Ex: Integração com API de pagamentos",
        required: true
      },
      {
        key: "area",
        label: "Área técnica",
        type: "select",
        placeholder: "Selecione a área",
        required: true,
        options: ["Desenvolvimento", "DevOps", "Infraestrutura", "Segurança", "Banco de Dados", "Frontend", "Backend", "Mobile"]
      },
      {
        key: "complexity",
        label: "Nível de complexidade",
        type: "select",
        placeholder: "Selecione o nível",
        required: true,
        options: ["Iniciante", "Intermediário", "Avançado", "Expert"]
      }
    ]
  },
  {
    id: "process-documentation",
    title: "Documentação de Processos",
    description: "Fluxos de trabalho, procedimentos operacionais e metodologias",
    icon: Target,
    color: "green",
    gradient: "from-green-500 to-emerald-600",
    examples: ["Processo de onboarding", "Fluxo de aprovação", "Metodologia ágil"],
    prompt: "Documente o processo: {process}. Departamento: {department}. Objetivo: {objective}. Crie documentação estruturada com visão geral, responsáveis, etapas detalhadas, pontos de controle, templates necessários e métricas de sucesso.",
    estimatedTime: "4-6 min",
    fields: [
      {
        key: "process",
        label: "Nome do processo",
        type: "text",
        placeholder: "Ex: Processo de contratação de funcionários",
        required: true
      },
      {
        key: "department",
        label: "Departamento responsável",
        type: "select",
        placeholder: "Selecione o departamento",
        required: true,
        options: ["Recursos Humanos", "Vendas", "Marketing", "Financeiro", "Operações", "TI", "Jurídico", "Atendimento"]
      },
      {
        key: "objective",
        label: "Objetivo principal",
        type: "textarea",
        placeholder: "Descreva o que este processo visa alcançar",
        required: true
      }
    ]
  },
  {
    id: "training-material",
    title: "Material de Treinamento",
    description: "Conteúdo educacional, capacitação e desenvolvimento",
    icon: Brain,
    color: "purple",
    gradient: "from-purple-500 to-violet-600",
    examples: ["Treinamento de produto", "Capacitação técnica", "Soft skills"],
    prompt: "Crie material de treinamento sobre: {subject}. Público-alvo: {audience}. Duração estimada: {duration}. Inclua objetivos de aprendizagem, conteúdo estruturado em módulos, exercícios práticos, avaliações e recursos complementares.",
    estimatedTime: "6-8 min",
    fields: [
      {
        key: "subject",
        label: "Assunto do treinamento",
        type: "text",
        placeholder: "Ex: Técnicas de vendas consultivas",
        required: true
      },
      {
        key: "audience",
        label: "Público-alvo",
        type: "select",
        placeholder: "Para quem é o treinamento",
        required: true,
        options: ["Novos funcionários", "Equipe de vendas", "Gestores", "Equipe técnica", "Atendimento", "Todos os colaboradores"]
      },
      {
        key: "duration",
        label: "Duração estimada",
        type: "select",
        placeholder: "Tempo de treinamento",
        required: true,
        options: ["30 minutos", "1 hora", "2 horas", "4 horas", "1 dia", "1 semana", "1 mês"]
      }
    ]
  },
  {
    id: "policy-procedure",
    title: "Políticas e Procedimentos",
    description: "Normas internas, compliance e diretrizes corporativas",
    icon: Shield,
    color: "red",
    gradient: "from-red-500 to-rose-600",
    examples: ["Política de segurança", "Código de conduta", "Procedimento de emergência"],
    prompt: "Elabore uma política/procedimento sobre: {policy}. Área de aplicação: {scope}. Nível de criticidade: {criticality}. Estruture com objetivo, escopo, definições, responsabilidades, procedimentos detalhados, penalidades e revisões.",
    estimatedTime: "5-7 min",
    fields: [
      {
        key: "policy",
        label: "Política ou procedimento",
        type: "text",
        placeholder: "Ex: Política de uso de equipamentos corporativos",
        required: true
      },
      {
        key: "scope",
        label: "Área de aplicação",
        type: "select",
        placeholder: "Onde se aplica",
        required: true,
        options: ["Toda a empresa", "Departamento específico", "Equipe de liderança", "Área técnica", "Área comercial", "Área administrativa"]
      },
      {
        key: "criticality",
        label: "Nível de criticidade",
        type: "select",
        placeholder: "Importância da política",
        required: true,
        options: ["Baixa", "Média", "Alta", "Crítica"]
      }
    ]
  },
  {
    id: "best-practices",
    title: "Melhores Práticas",
    description: "Recomendações, padrões e lições aprendidas",
    icon: Lightbulb,
    color: "amber",
    gradient: "from-amber-500 to-orange-600",
    examples: ["Boas práticas de código", "Padrões de atendimento", "Otimização de processos"],
    prompt: "Compile as melhores práticas para: {practice}. Contexto: {context}. Baseado em: {source}. Organize com introdução, princípios fundamentais, práticas recomendadas, exemplos de implementação, armadilhas comuns e métricas de sucesso.",
    estimatedTime: "4-6 min",
    fields: [
      {
        key: "practice",
        label: "Área de prática",
        type: "text",
        placeholder: "Ex: Desenvolvimento de software seguro",
        required: true
      },
      {
        key: "context",
        label: "Contexto de aplicação",
        type: "textarea",
        placeholder: "Onde e quando aplicar essas práticas",
        required: true
      },
      {
        key: "source",
        label: "Baseado em",
        type: "select",
        placeholder: "Fonte das práticas",
        required: true,
        options: ["Experiência interna", "Padrões da indústria", "Metodologias ágeis", "Frameworks estabelecidos", "Pesquisa acadêmica", "Benchmarking"]
      }
    ]
  },
  {
    id: "troubleshooting",
    title: "Solução de Problemas",
    description: "Guias de troubleshooting, FAQs e resolução de incidentes",
    icon: Search,
    color: "cyan",
    gradient: "from-cyan-500 to-blue-600",
    examples: ["FAQ técnico", "Resolução de bugs", "Troubleshooting de sistema"],
    prompt: "Crie um guia de solução de problemas para: {problem}. Sistema/área: {system}. Urgência típica: {urgency}. Estruture com sintomas comuns, diagnóstico passo-a-passo, soluções ordenadas por probabilidade, quando escalar e prevenção.",
    estimatedTime: "4-5 min",
    fields: [
      {
        key: "problem",
        label: "Tipo de problema",
        type: "text",
        placeholder: "Ex: Falhas de conexão com banco de dados",
        required: true
      },
      {
        key: "system",
        label: "Sistema ou área",
        type: "text",
        placeholder: "Ex: Sistema de e-commerce",
        required: true
      },
      {
        key: "urgency",
        label: "Urgência típica",
        type: "select",
        placeholder: "Nível de urgência",
        required: true,
        options: ["Baixa", "Média", "Alta", "Crítica", "Emergencial"]
      }
    ]
  },
  {
    id: "product-documentation",
    title: "Documentação de Produto",
    description: "Especificações, funcionalidades e guias de usuário",
    icon: Rocket,
    color: "indigo",
    gradient: "from-indigo-500 to-purple-600",
    examples: ["Manual do usuário", "Especificação técnica", "Release notes"],
    prompt: "Documente o produto/funcionalidade: {product}. Tipo de usuário: {userType}. Versão: {version}. Crie documentação com visão geral, funcionalidades principais, instruções de uso, exemplos práticos, limitações conhecidas e roadmap.",
    estimatedTime: "5-7 min",
    fields: [
      {
        key: "product",
        label: "Produto ou funcionalidade",
        type: "text",
        placeholder: "Ex: Sistema de relatórios avançados",
        required: true
      },
      {
        key: "userType",
        label: "Tipo de usuário",
        type: "select",
        placeholder: "Quem vai usar",
        required: true,
        options: ["Usuário final", "Administrador", "Desenvolvedor", "Analista", "Gestor", "Cliente externo"]
      },
      {
        key: "version",
        label: "Versão",
        type: "text",
        placeholder: "Ex: v2.1.0 ou Atual",
        required: false
      }
    ]
  },
  {
    id: "knowledge-base",
    title: "Base de Conhecimento",
    description: "Artigos informativos, conceitos e fundamentos",
    icon: BookOpen,
    color: "emerald",
    gradient: "from-emerald-500 to-green-600",
    examples: ["Conceitos fundamentais", "Glossário técnico", "Artigos educativos"],
    prompt: "Crie um artigo de base de conhecimento sobre: {topic}. Nível de profundidade: {depth}. Audiência: {audience}. Desenvolva com introdução clara, conceitos fundamentais, explicações detalhadas, exemplos ilustrativos, casos de uso e referências para aprofundamento.",
    estimatedTime: "6-8 min",
    fields: [
      {
        key: "topic",
        label: "Tópico principal",
        type: "text",
        placeholder: "Ex: Arquitetura de microserviços",
        required: true
      },
      {
        key: "depth",
        label: "Nível de profundidade",
        type: "select",
        placeholder: "Quão detalhado",
        required: true,
        options: ["Visão geral", "Intermediário", "Detalhado", "Especialista"]
      },
      {
        key: "audience",
        label: "Audiência principal",
        type: "select",
        placeholder: "Para quem escrever",
        required: true,
        options: ["Iniciantes", "Profissionais", "Especialistas", "Tomadores de decisão", "Equipe mista"]
      }
    ]
  },
  // === CATEGORIAS ESPECÍFICAS POR INDÚSTRIA ===
  {
    id: "healthcare-protocols",
    title: "Protocolos de Saúde",
    description: "Procedimentos médicos, protocolos clínicos e guidelines",
    icon: Stethoscope,
    color: "red",
    gradient: "from-red-500 to-pink-600",
    examples: ["Protocolo de emergência", "Procedimento cirúrgico", "Guidelines de tratamento"],
    prompt: "Crie um protocolo de saúde para: {procedure}. Especialidade: {specialty}. Nível de urgência: {urgency}. Inclua indicações, contraindicações, materiais necessários, passo-a-passo detalhado, cuidados especiais, monitoramento e documentação obrigatória.",
    estimatedTime: "7-9 min",
    fields: [
      {
        key: "procedure",
        label: "Procedimento/Protocolo",
        type: "text",
        placeholder: "Ex: Protocolo de sepse",
        required: true
      },
      {
        key: "specialty",
        label: "Especialidade médica",
        type: "select",
        placeholder: "Área médica",
        required: true,
        options: ["Clínica Geral", "Emergência", "UTI", "Cirurgia", "Pediatria", "Cardiologia", "Neurologia", "Oncologia"]
      },
      {
        key: "urgency",
        label: "Nível de urgência",
        type: "select",
        placeholder: "Criticidade",
        required: true,
        options: ["Rotina", "Urgente", "Emergência", "Crítico"]
      }
    ]
  },
  {
    id: "education-curriculum",
    title: "Currículo Educacional",
    description: "Planos de aula, currículos e metodologias de ensino",
    icon: GraduationCap,
    color: "indigo",
    gradient: "from-indigo-500 to-blue-600",
    examples: ["Plano de aula", "Currículo semestral", "Metodologia ativa"],
    prompt: "Desenvolva um currículo educacional para: {subject}. Nível de ensino: {level}. Duração: {duration}. Crie objetivos de aprendizagem, competências a desenvolver, conteúdo programático, metodologias, recursos didáticos e avaliação.",
    estimatedTime: "8-10 min",
    fields: [
      {
        key: "subject",
        label: "Disciplina/Matéria",
        type: "text",
        placeholder: "Ex: Matemática Aplicada",
        required: true
      },
      {
        key: "level",
        label: "Nível de ensino",
        type: "select",
        placeholder: "Público-alvo",
        required: true,
        options: ["Fundamental I", "Fundamental II", "Ensino Médio", "Superior", "Pós-graduação", "Técnico", "EJA"]
      },
      {
        key: "duration",
        label: "Duração do curso",
        type: "select",
        placeholder: "Período",
        required: true,
        options: ["Bimestral", "Semestral", "Anual", "Módulo", "Intensivo"]
      }
    ]
  },
  {
    id: "retail-operations",
    title: "Operações de Varejo",
    description: "Procedimentos de loja, atendimento e gestão comercial",
    icon: ShoppingCart,
    color: "orange",
    gradient: "from-orange-500 to-red-600",
    examples: ["Manual do vendedor", "Procedimento de troca", "Gestão de estoque"],
    prompt: "Crie um manual de operações de varejo para: {operation}. Tipo de estabelecimento: {storeType}. Foco: {focus}. Desenvolva procedimentos detalhados, scripts de atendimento, políticas da empresa, tratamento de exceções e métricas de performance.",
    estimatedTime: "6-8 min",
    fields: [
      {
        key: "operation",
        label: "Operação/Processo",
        type: "text",
        placeholder: "Ex: Atendimento ao cliente",
        required: true
      },
      {
        key: "storeType",
        label: "Tipo de estabelecimento",
        type: "select",
        placeholder: "Formato da loja",
        required: true,
        options: ["Loja física", "E-commerce", "Marketplace", "Franquia", "Shopping", "Atacado", "Varejo especializado"]
      },
      {
        key: "focus",
        label: "Foco principal",
        type: "select",
        placeholder: "Objetivo",
        required: true,
        options: ["Vendas", "Atendimento", "Operações", "Logística", "Gestão", "Qualidade"]
      }
    ]
  },
  {
    id: "legal-procedures",
    title: "Procedimentos Jurídicos",
    description: "Contratos, procedimentos legais e compliance",
    icon: Scale,
    color: "slate",
    gradient: "from-slate-500 to-gray-600",
    examples: ["Modelo de contrato", "Procedimento legal", "Compliance LGPD"],
    prompt: "Elabore um documento jurídico sobre: {document}. Área do direito: {area}. Complexidade: {complexity}. Inclua fundamentação legal, cláusulas essenciais, procedimentos, prazos, responsabilidades e referências normativas.",
    estimatedTime: "9-12 min",
    fields: [
      {
        key: "document",
        label: "Tipo de documento",
        type: "text",
        placeholder: "Ex: Contrato de prestação de serviços",
        required: true
      },
      {
        key: "area",
        label: "Área do direito",
        type: "select",
        placeholder: "Especialização",
        required: true,
        options: ["Empresarial", "Trabalhista", "Civil", "Tributário", "Digital", "Compliance", "Contratos", "Propriedade Intelectual"]
      },
      {
        key: "complexity",
        label: "Complexidade",
        type: "select",
        placeholder: "Nível de detalhamento",
        required: true,
        options: ["Simples", "Intermediário", "Complexo", "Especializado"]
      }
    ]
  },
  {
    id: "manufacturing-sop",
    title: "SOPs de Manufatura",
    description: "Procedimentos operacionais padrão e controle de qualidade",
    icon: Factory,
    color: "gray",
    gradient: "from-gray-500 to-slate-600",
    examples: ["SOP de produção", "Controle de qualidade", "Manutenção preventiva"],
    prompt: "Crie um SOP de manufatura para: {process}. Setor: {sector}. Criticidade: {criticality}. Desenvolva objetivo, escopo, responsabilidades, procedimento detalhado, controles de qualidade, registros obrigatórios e ações corretivas.",
    estimatedTime: "7-9 min",
    fields: [
      {
        key: "process",
        label: "Processo de manufatura",
        type: "text",
        placeholder: "Ex: Montagem de componentes eletrônicos",
        required: true
      },
      {
        key: "sector",
        label: "Setor industrial",
        type: "select",
        placeholder: "Indústria",
        required: true,
        options: ["Automotiva", "Eletrônica", "Alimentícia", "Farmacêutica", "Têxtil", "Metalúrgica", "Química", "Plásticos"]
      },
      {
        key: "criticality",
        label: "Criticidade do processo",
        type: "select",
        placeholder: "Impacto",
        required: true,
        options: ["Baixa", "Média", "Alta", "Crítica"]
      }
    ]
  },
  {
    id: "financial-procedures",
    title: "Procedimentos Financeiros",
    description: "Processos contábeis, controles financeiros e compliance",
    icon: DollarSign,
    color: "green",
    gradient: "from-green-500 to-emerald-600",
    examples: ["Processo de fechamento", "Controle de caixa", "Auditoria interna"],
    prompt: "Documente o procedimento financeiro: {procedure}. Área: {area}. Periodicidade: {frequency}. Inclua objetivo, responsáveis, documentos necessários, passo-a-passo, controles internos, relatórios e conformidade regulatória.",
    estimatedTime: "6-8 min",
    fields: [
      {
        key: "procedure",
        label: "Procedimento financeiro",
        type: "text",
        placeholder: "Ex: Conciliação bancária",
        required: true
      },
      {
        key: "area",
        label: "Área financeira",
        type: "select",
        placeholder: "Departamento",
        required: true,
        options: ["Contabilidade", "Tesouraria", "Contas a Pagar", "Contas a Receber", "Controladoria", "Auditoria", "Planejamento"]
      },
      {
        key: "frequency",
        label: "Periodicidade",
        type: "select",
        placeholder: "Frequência",
        required: true,
        options: ["Diária", "Semanal", "Quinzenal", "Mensal", "Trimestral", "Anual", "Eventual"]
      }
    ]
  },
  {
    id: "construction-safety",
    title: "Segurança na Construção",
    description: "Normas de segurança, procedimentos e EPIs",
    icon: Hammer,
    color: "yellow",
    gradient: "from-yellow-500 to-orange-600",
    examples: ["Manual de segurança", "Uso de EPIs", "Procedimento de emergência"],
    prompt: "Elabore um manual de segurança para: {activity}. Tipo de obra: {workType}. Risco: {risk}. Inclua normas aplicáveis, EPIs obrigatórios, procedimentos seguros, identificação de riscos, emergências e treinamentos necessários.",
    estimatedTime: "8-10 min",
    fields: [
      {
        key: "activity",
        label: "Atividade/Processo",
        type: "text",
        placeholder: "Ex: Trabalho em altura",
        required: true
      },
      {
        key: "workType",
        label: "Tipo de obra",
        type: "select",
        placeholder: "Categoria",
        required: true,
        options: ["Residencial", "Comercial", "Industrial", "Infraestrutura", "Reforma", "Demolição", "Montagem"]
      },
      {
        key: "risk",
        label: "Nível de risco",
        type: "select",
        placeholder: "Classificação",
        required: true,
        options: ["Baixo", "Médio", "Alto", "Crítico"]
      }
    ]
  },
  {
    id: "automotive-maintenance",
    title: "Manutenção Automotiva",
    description: "Procedimentos de manutenção e reparos veiculares",
    icon: Car,
    color: "blue",
    gradient: "from-blue-500 to-cyan-600",
    examples: ["Manual de manutenção", "Diagnóstico de falhas", "Procedimento de reparo"],
    prompt: "Crie um manual de manutenção automotiva para: {procedure}. Tipo de veículo: {vehicleType}. Complexidade: {complexity}. Desenvolva ferramentas necessárias, procedimento passo-a-passo, pontos de atenção, testes de validação e especificações técnicas.",
    estimatedTime: "7-9 min",
    fields: [
      {
        key: "procedure",
        label: "Procedimento de manutenção",
        type: "text",
        placeholder: "Ex: Troca de pastilhas de freio",
        required: true
      },
      {
        key: "vehicleType",
        label: "Tipo de veículo",
        type: "select",
        placeholder: "Categoria",
        required: true,
        options: ["Carros de passeio", "Motocicletas", "Caminhões", "Ônibus", "Veículos pesados", "Híbridos/Elétricos"]
      },
      {
        key: "complexity",
        label: "Complexidade do serviço",
        type: "select",
        placeholder: "Dificuldade",
        required: true,
        options: ["Básica", "Intermediária", "Avançada", "Especializada"]
      }
    ]
  },
  {
    id: "hospitality-service",
    title: "Serviços de Hospitalidade",
    description: "Protocolos de hotelaria, restaurantes e turismo",
    icon: Utensils,
    color: "amber",
    gradient: "from-amber-500 to-yellow-600",
    examples: ["Manual de atendimento", "Protocolo de limpeza", "Procedimento de check-in"],
    prompt: "Desenvolva um manual de hospitalidade para: {service}. Tipo de estabelecimento: {establishment}. Padrão: {standard}. Inclua protocolos de atendimento, procedimentos operacionais, padrões de qualidade, tratamento de reclamações e treinamento da equipe.",
    estimatedTime: "6-8 min",
    fields: [
      {
        key: "service",
        label: "Serviço/Área",
        type: "text",
        placeholder: "Ex: Atendimento no restaurante",
        required: true
      },
      {
        key: "establishment",
        label: "Tipo de estabelecimento",
        type: "select",
        placeholder: "Categoria",
        required: true,
        options: ["Hotel", "Restaurante", "Resort", "Pousada", "Café", "Bar", "Eventos", "Turismo"]
      },
      {
        key: "standard",
        label: "Padrão de serviço",
        type: "select",
        placeholder: "Nível",
        required: true,
        options: ["Econômico", "Intermediário", "Premium", "Luxo"]
      }
    ]
  },
  {
    id: "real-estate-process",
    title: "Processos Imobiliários",
    description: "Procedimentos de vendas, locação e gestão imobiliária",
    icon: Home,
    color: "teal",
    gradient: "from-teal-500 to-green-600",
    examples: ["Processo de venda", "Gestão de locação", "Avaliação de imóvel"],
    prompt: "Crie um manual imobiliário para: {process}. Tipo de operação: {operation}. Segmento: {segment}. Desenvolva etapas do processo, documentação necessária, aspectos legais, negociação, fechamento e pós-venda.",
    estimatedTime: "7-9 min",
    fields: [
      {
        key: "process",
        label: "Processo imobiliário",
        type: "text",
        placeholder: "Ex: Captação de imóveis",
        required: true
      },
      {
        key: "operation",
        label: "Tipo de operação",
        type: "select",
        placeholder: "Modalidade",
        required: true,
        options: ["Venda", "Locação", "Administração", "Avaliação", "Consultoria", "Lançamentos"]
      },
      {
        key: "segment",
        label: "Segmento",
        type: "select",
        placeholder: "Mercado",
        required: true,
        options: ["Residencial", "Comercial", "Industrial", "Rural", "Luxo", "Popular"]
      }
    ]
  },
  {
    id: "aviation-procedures",
    title: "Procedimentos de Aviação",
    description: "Manuais de voo, segurança aérea e operações",
    icon: Plane,
    color: "sky",
    gradient: "from-sky-500 to-blue-600",
    examples: ["Checklist de voo", "Procedimento de emergência", "Manual de manutenção"],
    prompt: "Elabore um procedimento de aviação para: {procedure}. Tipo de operação: {operation}. Categoria: {category}. Inclua pré-requisitos, checklist detalhado, procedimentos normais e de emergência, comunicações e conformidade regulatória.",
    estimatedTime: "9-12 min",
    fields: [
      {
        key: "procedure",
        label: "Procedimento aéreo",
        type: "text",
        placeholder: "Ex: Decolagem em condições adversas",
        required: true
      },
      {
        key: "operation",
        label: "Tipo de operação",
        type: "select",
        placeholder: "Modalidade",
        required: true,
        options: ["Voo comercial", "Aviação executiva", "Instrução", "Carga", "Emergência médica", "Manutenção"]
      },
      {
        key: "category",
        label: "Categoria da aeronave",
        type: "select",
        placeholder: "Tipo",
        required: true,
        options: ["Monomotor", "Bimotor", "Jato comercial", "Helicóptero", "Planador", "Ultraleve"]
      }
    ]
  },
  {
    id: "gaming-development",
    title: "Desenvolvimento de Games",
    description: "Game design, programação e produção de jogos",
    icon: Gamepad2,
    color: "violet",
    gradient: "from-violet-500 to-purple-600",
    examples: ["Game design document", "Pipeline de arte", "Sistema de gameplay"],
    prompt: "Crie documentação de desenvolvimento de games para: {feature}. Gênero: {genre}. Plataforma: {platform}. Desenvolva conceito, mecânicas, implementação técnica, arte, áudio, testes e lançamento.",
    estimatedTime: "8-10 min",
    fields: [
      {
        key: "feature",
        label: "Feature/Sistema",
        type: "text",
        placeholder: "Ex: Sistema de combate",
        required: true
      },
      {
        key: "genre",
        label: "Gênero do jogo",
        type: "select",
        placeholder: "Categoria",
        required: true,
        options: ["RPG", "FPS", "Estratégia", "Puzzle", "Plataforma", "Corrida", "Simulação", "Casual"]
      },
      {
        key: "platform",
        label: "Plataforma alvo",
        type: "select",
        placeholder: "Dispositivo",
        required: true,
        options: ["PC", "Console", "Mobile", "Web", "VR", "Multiplataforma"]
      }
    ]
  },
  {
    id: "design-guidelines",
    title: "Guidelines de Design",
    description: "Padrões visuais, brand guidelines e design systems",
    icon: Palette,
    color: "pink",
    gradient: "from-pink-500 to-rose-600",
    examples: ["Manual da marca", "Design system", "Guia de estilo"],
    prompt: "Desenvolva um guideline de design para: {project}. Tipo: {type}. Aplicação: {application}. Crie identidade visual, paleta de cores, tipografia, elementos gráficos, aplicações práticas e especificações técnicas.",
    estimatedTime: "7-9 min",
    fields: [
      {
        key: "project",
        label: "Projeto/Marca",
        type: "text",
        placeholder: "Ex: Identidade visual da empresa",
        required: true
      },
      {
        key: "type",
        label: "Tipo de design",
        type: "select",
        placeholder: "Categoria",
        required: true,
        options: ["Identidade visual", "Design system", "Material gráfico", "Interface digital", "Packaging", "Sinalização"]
      },
      {
        key: "application",
        label: "Aplicação principal",
        type: "select",
        placeholder: "Uso",
        required: true,
        options: ["Digital", "Impresso", "Produtos", "Ambientes", "Multiplataforma", "Eventos"]
      }
    ]
  },
  {
    id: "logistics-operations",
    title: "Operações Logísticas",
    description: "Gestão de estoque, transporte e distribuição",
    icon: Truck,
    color: "orange",
    gradient: "from-orange-500 to-amber-600",
    examples: ["Gestão de estoque", "Roteirização", "Controle de qualidade"],
    prompt: "Crie um manual de operações logísticas para: {operation}. Modalidade: {mode}. Escopo: {scope}. Desenvolva fluxo operacional, controles, indicadores, tecnologias, custos e otimização de processos.",
    estimatedTime: "6-8 min",
    fields: [
      {
        key: "operation",
        label: "Operação logística",
        type: "text",
        placeholder: "Ex: Gestão de centro de distribuição",
        required: true
      },
      {
        key: "mode",
        label: "Modalidade de transporte",
        type: "select",
        placeholder: "Tipo",
        required: true,
        options: ["Rodoviário", "Aéreo", "Marítimo", "Ferroviário", "Multimodal", "Urbano"]
      },
      {
        key: "scope",
        label: "Escopo da operação",
        type: "select",
        placeholder: "Abrangência",
        required: true,
        options: ["Local", "Regional", "Nacional", "Internacional", "E-commerce", "B2B"]
      }
    ]
  },
  {
    id: "fashion-production",
    title: "Produção de Moda",
    description: "Desenvolvimento de coleções, produção e controle de qualidade",
    icon: Shirt,
    color: "rose",
    gradient: "from-rose-500 to-pink-600",
    examples: ["Desenvolvimento de coleção", "Controle de qualidade", "Ficha técnica"],
    prompt: "Elabore documentação de produção de moda para: {process}. Segmento: {segment}. Tipo de produto: {product}. Inclua pesquisa, desenvolvimento, modelagem, produção, qualidade e comercialização.",
    estimatedTime: "7-9 min",
    fields: [
      {
        key: "process",
        label: "Processo de produção",
        type: "text",
        placeholder: "Ex: Desenvolvimento de coleção verão",
        required: true
      },
      {
        key: "segment",
        label: "Segmento de moda",
        type: "select",
        placeholder: "Mercado",
        required: true,
        options: ["Fast fashion", "Luxo", "Casual", "Esportivo", "Infantil", "Plus size", "Sustentável"]
      },
      {
        key: "product",
        label: "Tipo de produto",
        type: "select",
        placeholder: "Categoria",
        required: true,
        options: ["Roupas", "Calçados", "Acessórios", "Bolsas", "Joias", "Lingerie", "Moda praia"]
      }
    ]
  },
  {
    id: "agriculture-management",
    title: "Gestão Agrícola",
    description: "Práticas agrícolas, manejo de culturas e sustentabilidade",
    icon: Wheat,
    color: "lime",
    gradient: "from-lime-500 to-green-600",
    examples: ["Manejo de culturas", "Controle de pragas", "Sustentabilidade"],
    prompt: "Desenvolva um manual agrícola para: {practice}. Cultura: {crop}. Sistema: {system}. Inclua planejamento, preparo, plantio, manejo, colheita, pós-colheita e sustentabilidade ambiental.",
    estimatedTime: "8-10 min",
    fields: [
      {
        key: "practice",
        label: "Prática agrícola",
        type: "text",
        placeholder: "Ex: Manejo integrado de pragas",
        required: true
      },
      {
        key: "crop",
        label: "Tipo de cultura",
        type: "select",
        placeholder: "Cultivo",
        required: true,
        options: ["Grãos", "Frutas", "Hortaliças", "Cana-de-açúcar", "Café", "Pastagem", "Flores", "Orgânicos"]
      },
      {
        key: "system",
        label: "Sistema de produção",
        type: "select",
        placeholder: "Modalidade",
        required: true,
        options: ["Convencional", "Orgânico", "Integrado", "Hidropônico", "Agroflorestal", "Precisão"]
      }
    ]
  },
  {
    id: "technology-infrastructure",
    title: "Infraestrutura de TI",
    description: "Arquitetura, redes, segurança e cloud computing",
    icon: Cpu,
    color: "cyan",
    gradient: "from-cyan-500 to-blue-600",
    examples: ["Arquitetura de rede", "Migração para cloud", "Política de segurança"],
    prompt: "Crie documentação de infraestrutura de TI para: {component}. Ambiente: {environment}. Criticidade: {criticality}. Desenvolva arquitetura, configuração, monitoramento, backup, segurança e procedimentos de contingência.",
    estimatedTime: "8-10 min",
    fields: [
      {
        key: "component",
        label: "Componente/Sistema",
        type: "text",
        placeholder: "Ex: Migração para AWS",
        required: true
      },
      {
        key: "environment",
        label: "Ambiente",
        type: "select",
        placeholder: "Tipo",
        required: true,
        options: ["On-premise", "Cloud", "Híbrido", "Edge", "Container", "Serverless"]
      },
      {
        key: "criticality",
        label: "Criticidade",
        type: "select",
        placeholder: "Impacto",
        required: true,
        options: ["Baixa", "Média", "Alta", "Missão crítica"]
      }
    ]
  },
  {
    id: "corporate-governance",
    title: "Governança Corporativa",
    description: "Políticas empresariais, compliance e gestão de riscos",
    icon: Building2,
    color: "slate",
    gradient: "from-slate-500 to-gray-600",
    examples: ["Política de compliance", "Gestão de riscos", "Código de ética"],
    prompt: "Elabore documentação de governança corporativa para: {policy}. Área: {area}. Abrangência: {scope}. Desenvolva diretrizes, responsabilidades, controles, monitoramento, auditoria e conformidade regulatória.",
    estimatedTime: "9-12 min",
    fields: [
      {
        key: "policy",
        label: "Política/Processo",
        type: "text",
        placeholder: "Ex: Política de gestão de riscos",
        required: true
      },
      {
        key: "area",
        label: "Área de governança",
        type: "select",
        placeholder: "Foco",
        required: true,
        options: ["Compliance", "Riscos", "Auditoria", "Ética", "Sustentabilidade", "Dados", "Terceiros"]
      },
      {
        key: "scope",
        label: "Abrangência",
        type: "select",
        placeholder: "Alcance",
        required: true,
        options: ["Departamental", "Corporativa", "Grupo", "Subsidiárias", "Terceiros", "Cadeia de valor"]
      }
    ]
  },
  {
    id: "customer-experience",
    title: "Experiência do Cliente",
    description: "Jornada do cliente, touchpoints e satisfação",
    icon: Heart,
    color: "red",
    gradient: "from-red-500 to-rose-600",
    examples: ["Mapeamento de jornada", "Protocolo de atendimento", "Pesquisa de satisfação"],
    prompt: "Desenvolva estratégia de experiência do cliente para: {touchpoint}. Canal: {channel}. Objetivo: {objective}. Crie mapeamento da jornada, pontos de contato, métricas, melhorias e personalização da experiência.",
    estimatedTime: "6-8 min",
    fields: [
      {
        key: "touchpoint",
        label: "Ponto de contato",
        type: "text",
        placeholder: "Ex: Processo de onboarding",
        required: true
      },
      {
        key: "channel",
        label: "Canal de atendimento",
        type: "select",
        placeholder: "Meio",
        required: true,
        options: ["Presencial", "Telefone", "Chat", "E-mail", "App", "Site", "Redes sociais", "Omnichannel"]
      },
      {
        key: "objective",
        label: "Objetivo principal",
        type: "select",
        placeholder: "Meta",
        required: true,
        options: ["Satisfação", "Retenção", "Fidelização", "Upsell", "Redução de churn", "NPS"]
      }
    ]
  }
];

export function AIGenerateKnowledgePage({ isOpen, onClose, preSelectedSpaceId }: AIGenerateKnowledgePageProps) {
  const navigate = useNavigate();
  const [showCategorySelector, setShowCategorySelector] = useState(false);
  const [showDetailsForm, setShowDetailsForm] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<KnowledgeCategory | null>(null);
  const [formData, setFormData] = useState<FormData>({});
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedSpaceId, setSelectedSpaceId] = useState<string>(preSelectedSpaceId || '');
  const [searchTerm, setSearchTerm] = useState('');

  // Integração com sistema de créditos
  const {
    executeWithCredits,
    feature,
    balance,
    isLoading: isExecuting,
    canUseFeature,
    creditsRequired,
    creditsRemaining,
  } = useAIFeatureWithCredits('knowledge_page_generation');

  const {
    isAvailable,
    feature: availabilityFeature,
    insufficientCredits,
  } = useAIFeatureAvailability('knowledge_page_generation');

  // Usar 1 crédito se a funcionalidade não existir ainda
  const actualCreditsRequired = creditsRequired || 1;
  const actualCreditsRemaining = creditsRemaining || (balance?.remaining_credits ?? 0);
  const hasEnoughCredits = actualCreditsRemaining >= actualCreditsRequired;
  
  // Funcionalidade disponível se: existe e está ativa, OU se temos créditos suficientes
  const isFeatureAvailable = (isAvailable !== false) && hasEnoughCredits;

  // Hook para upgrade contextual
  const { upgradeForAI } = useUpgradeContext();

  // Buscar espaços de conhecimento
  const { data: spaces = [], isLoading: isLoadingSpaces } = useKnowledgeSpaces();
  
  // Hook para verificar permissões dos espaços
  const { 
    availableSpaces, 
    isLoadingPermissions, 
    updateSpacePermission,
    spacesWithPermissions 
  } = useSpacesWithPermissions(spaces);

  // Dados dinâmicos do plano
  const totalCredits = balance?.plan_credits || 5;

  // Filtrar categorias baseado na busca
  const filteredCategories = knowledgeCategories.filter(category =>
    category.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.examples.some(example => 
      example.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const resetToInitialState = () => {
    setShowCategorySelector(false);
    setShowDetailsForm(false);
    setSelectedCategory(null);
    setFormData({});
    setIsGenerating(false);
    setSearchTerm('');
  };

  const handleGenerateWithAI = () => {
    console.log('🔥 BOTÃO CLICADO - handleGenerateWithAI iniciado');
    console.log('🔍 Estado dos hooks:', {
      isAvailable,
      hasEnoughCredits,
      actualCreditsRequired,
      actualCreditsRemaining,
      insufficientCredits,
      balance
    });

    if (!isFeatureAvailable) {
      console.log('❌ Bloqueado por créditos ou disponibilidade');
      if (!hasEnoughCredits) {
        console.log('💸 Créditos insuficientes - mostrando notificação');
        errorWithNotification("Créditos insuficientes", {
          description: `Você precisa de ${actualCreditsRequired} crédito para gerar uma página. Restam: ${actualCreditsRemaining}.`
        });
      }
      return;
    }

    console.log('✅ Prosseguindo para seleção de categoria');
    setShowCategorySelector(true);
  };

  const handleCategorySelect = (category: KnowledgeCategory) => {
    setSelectedCategory(category);
    setFormData({});
    setShowDetailsForm(true);
  };

  const handleFormSubmit = async () => {
    if (!selectedCategory) return;

    console.log('🚀 Iniciando geração de página com IA');
    console.log('📝 Dados do formulário:', formData);
    console.log('🏷️ Categoria selecionada:', selectedCategory.title);

    // Validar campos obrigatórios
    const missingFields = selectedCategory.fields
      .filter(field => field.required && !formData[field.key]?.trim())
      .map(field => field.label);

    if (missingFields.length > 0) {
      errorWithNotification('Campos obrigatórios', {
        description: `Por favor, preencha: ${missingFields.join(', ')}`
      });
      return;
    }

    // Validar espaços disponíveis
    if (availableSpaces.length === 0) {
      errorWithNotification('Sem espaços disponíveis', {
        description: 'Você não tem permissão para criar páginas em nenhum espaço. Entre em contato com um administrador.'
      });
      return;
    }

    // Validar espaço selecionado
    if (!selectedSpaceId) {
      errorWithNotification('Espaço obrigatório', {
        description: 'Por favor, selecione um espaço de conhecimento.'
      });
      return;
    }

    setIsGenerating(true);

    try {
      console.log('🔄 Executando geração com créditos...');
      
      // Usar o hook de créditos para executar e rastrear
      const result = await executeWithCredits(
        async () => {
          return await generateKnowledgePage(selectedCategory, formData, selectedSpaceId);
        },
        {
          provider: 'groq',
          model: 'llama-3.1-8b-instant',
          apiEndpoint: 'generate-knowledge-page',
          requestData: { 
            category: selectedCategory.title,
            formData,
            spaceId: selectedSpaceId
          },
          contextType: 'knowledge_page_generation',
          usageDescription: `Geração de página: ${selectedCategory.title} - "${formData.topic || formData.process || formData.subject || 'conteúdo personalizado'}"`
        }
      );

      console.log('✅ Página gerada com sucesso:', result);

      // Navegar para a tela de criação com conteúdo pré-populado via URL params
      const params = new URLSearchParams({
        space: selectedSpaceId,
        ai_title: encodeURIComponent(result.title || ''),
        ai_content: encodeURIComponent(result.content || ''),
        ai_excerpt: encodeURIComponent(result.excerpt || ''),
        ai_tags: encodeURIComponent(JSON.stringify(result.tags || []))
      });

      navigate(`/knowledge/create-page?${params.toString()}`);

      // Feedback de sucesso
      const remainingAfterUse = actualCreditsRemaining - actualCreditsRequired;
      successWithNotification('Página de conhecimento gerada!', {
        description: `Restam ${remainingAfterUse} de ${totalCredits} créditos este mês.`
      });

      resetToInitialState();
      onClose();
    } catch (error) {
      console.error('❌ Erro ao gerar página:', error);
      
      // Mostrar notificação de erro específica
      let errorTitle = 'Erro na geração da página';
      let errorDescription = 'Não foi possível gerar a página automaticamente.';
      
      if (error instanceof Error) {
        if (error.message.includes('404')) {
          errorTitle = 'Serviço temporariamente indisponível';
          errorDescription = 'As funções de IA estão em manutenção. Redirecionando para criação manual.';
        } else if (error.message.includes('401') || error.message.includes('403')) {
          errorTitle = 'Erro de autenticação';
          errorDescription = 'Problema de acesso às funções de IA. Tente novamente em alguns minutos.';
        } else if (error.message.includes('500')) {
          errorTitle = 'Erro interno do servidor';
          errorDescription = 'Problema temporário no servidor. Redirecionando para criação manual.';
        }
      }
      
      errorWithNotification(errorTitle, {
        description: errorDescription
      });
      
      // Em caso de erro, navega sem conteúdo
      const fallbackParams = new URLSearchParams({
        space: selectedSpaceId
      });
              navigate(`/knowledge/create-page?${fallbackParams.toString()}`);
      resetToInitialState();
      onClose();
    } finally {
      setIsGenerating(false);
    }
  };

  const generateKnowledgePage = async (category: KnowledgeCategory, formData: FormData, spaceId: string) => {
    // Substituir placeholders no prompt com dados do formulário
    let personalizedPrompt = category.prompt;
    Object.entries(formData).forEach(([key, value]) => {
      personalizedPrompt = personalizedPrompt.replace(`{${key}}`, value || '');
    });

    console.log('📡 Enviando requisição para edge function...');
    console.log('📝 Prompt personalizado:', personalizedPrompt);

    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const { supabase } = await import('@/integrations/supabase/client');
    const session = await supabase.auth.getSession();
    
    const response = await fetch(`${supabaseUrl}/functions/v1/generate-knowledge-page`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.data.session?.access_token}`
      },
      body: JSON.stringify({ 
        prompt: personalizedPrompt,
        category: category.title,
        spaceId: spaceId,
        formData: formData
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Erro na API:', response.status, errorText);
      throw new Error(`Erro na API: ${response.status} - ${response.statusText}`);
    }

    const result = await response.json();
    console.log('📦 Resposta da API:', result);
    
    if (!result || typeof result !== 'object') {
      throw new Error('Resposta inválida da API de geração de página');
    }

    return result;
  };

  const getColorClasses = (color: string) => {
    const colorMap: Record<string, string> = {
      blue: "border-blue-200 text-blue-700 bg-blue-50",
      green: "border-green-200 text-green-700 bg-green-50",
      purple: "border-purple-200 text-purple-700 bg-purple-50",
      red: "border-red-200 text-red-700 bg-red-50",
      amber: "border-amber-200 text-amber-700 bg-amber-50",
      cyan: "border-cyan-200 text-cyan-700 bg-cyan-50",
      indigo: "border-indigo-200 text-indigo-700 bg-indigo-50",
      emerald: "border-emerald-200 text-emerald-700 bg-emerald-50",
    };
    return colorMap[color] || colorMap.blue;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-4">
          <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent flex items-center gap-2">
            Como você quer criar sua página?
            {/* Badge de saldo de créditos com tooltip */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge 
                    variant="secondary" 
                    className={`cursor-help ${
                      actualCreditsRemaining <= 2 
                        ? "bg-red-100 text-red-700 border-red-200" 
                        : actualCreditsRemaining <= 5
                        ? "bg-orange-100 text-orange-700 border-orange-200"
                        : "bg-blue-100 text-blue-700 border-blue-200"
                    }`}
                  >
                    <Zap className="w-3 h-3 mr-1" />
                    {actualCreditsRemaining}
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Você tem {actualCreditsRemaining} de {totalCredits} créditos restantes este mês</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </DialogTitle>
          <DialogDescription className="text-base text-muted-foreground">
            Escolha entre criar do zero ou gerar uma página de conhecimento completa automaticamente
          </DialogDescription>
        </DialogHeader>

        {/* Componentes invisíveis para carregar permissões */}
        <div className="hidden">
          {spaces.map((space) => (
            <SpaceWithPermission key={space.id} space={space}>
              {(hasPermission, isLoading) => {
                // Atualizar o estado quando a permissão mudar
                React.useEffect(() => {
                  updateSpacePermission(space.id, hasPermission, isLoading);
                }, [hasPermission, isLoading]);
                return null;
              }}
            </SpaceWithPermission>
          ))}
        </div>

        {/* Aviso de créditos baixos para geração automática */}
        {actualCreditsRemaining < actualCreditsRequired && (
          <Alert className="border-red-200 bg-red-50">
            <Zap className="h-4 w-4 text-red-600" />
            <AlertTitle className="text-red-800">Créditos insuficientes para geração automática</AlertTitle>
            <AlertDescription className="text-red-700 space-y-3">
              <p>A geração automática precisa de {actualCreditsRequired} crédito, mas você tem apenas {actualCreditsRemaining}.</p>
              <div className="flex gap-2">
                <Button 
                  size="sm" 
                  className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
                  onClick={() => {
                    onClose();
                    upgradeForAI();
                  }}
                >
                  <Crown className="w-4 h-4 mr-1" />
                  Upgrade
                </Button>
                <Button variant="outline" size="sm" onClick={() => {
                                      navigate('/knowledge/create-page');
                  onClose();
                }}>
                  Criar do Zero (Grátis)
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        <AnimatePresence mode="wait">
          {!showCategorySelector && !showDetailsForm ? (
            <motion.div
              key="main-options"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="grid md:grid-cols-2 gap-6 p-4"
            >
              {/* Opção 1: Criar do Zero */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card 
                  className="h-full cursor-pointer border-2 transition-all duration-300 hover:border-indigo-300 hover:shadow-lg"
                  onClick={() => {
                    const createParams = new URLSearchParams();
                    if (preSelectedSpaceId) {
                      createParams.set('space', preSelectedSpaceId);
                    }
                    navigate(`/knowledge/create-page${createParams.toString() ? '?' + createParams.toString() : ''}`);
                    onClose();
                  }}
                >
                  <CardHeader className="text-center space-y-4">
                    <div className="mx-auto w-16 h-16 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center">
                      <FileText className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-xl">Criar do Zero</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center space-y-4">
                    <p className="text-muted-foreground">
                      Controle total sobre seu conteúdo. Use nosso editor premium para criar páginas de conhecimento personalizadas.
                    </p>
                    <div className="space-y-2">
                      <Badge variant="outline" className="text-green-600 border-green-200">
                        Grátis
                      </Badge>
                      <div className="text-sm text-muted-foreground">
                        • Editor completo<br/>
                        • Templates disponíveis<br/>
                        • Controle total do conteúdo<br/>
                        • Organização personalizada
                      </div>
                    </div>
                    <Button variant="outline" className="w-full">
                      Começar a Escrever
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Opção 2: Gerar Automaticamente */}
              <motion.div
                whileHover={{ scale: isFeatureAvailable ? 1.02 : 1 }}
                whileTap={{ scale: isFeatureAvailable ? 0.98 : 1 }}
              >
                <Card 
                  className={`h-full transition-all duration-300 border-2 ${
                    isFeatureAvailable 
                      ? 'cursor-pointer hover:border-indigo-300 hover:shadow-lg bg-gradient-to-br from-indigo-50 to-purple-50' 
                      : 'opacity-60 cursor-not-allowed bg-gray-50'
                  }`}
                  onClick={isFeatureAvailable ? handleGenerateWithAI : undefined}
                >
                  <CardHeader className="text-center space-y-4">
                    <div className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center ${
                      isFeatureAvailable 
                        ? 'bg-gradient-to-r from-indigo-500 to-purple-600' 
                        : 'bg-gray-400'
                    }`}>
                      <Sparkles className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-xl flex items-center gap-2 justify-center">
                      Gerar Automaticamente
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="text-center space-y-4">
                    <p className="text-muted-foreground">
                      IA cria automaticamente conteúdo estruturado baseado no tipo de conhecimento que você deseja documentar.
                    </p>
                    <div className="space-y-2">
                      <Badge variant="outline" className="text-purple-600 border-purple-200">
                        IA Premium
                      </Badge>
                      <div className="text-sm text-muted-foreground">
                        • Conteúdo estruturado<br/>
                        • 8 categorias especializadas<br/>
                        • Resultado em 5-8 minutos<br/>
                        • Totalmente editável
                      </div>
                    </div>
                    <Button 
                      className={`w-full ${
                        isFeatureAvailable 
                          ? 'bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700' 
                          : 'bg-gray-400 cursor-not-allowed'
                      }`}
                      disabled={!isFeatureAvailable}
                    >
                      {isFeatureAvailable ? (
                        <>
                          <Wand2 className="mr-2 h-4 w-4" />
                          Gerar com IA
                        </>
                      ) : (
                        <>
                          <AlertTriangle className="mr-2 h-4 w-4" />
                          Créditos insuficientes
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            </motion.div>
          ) : showCategorySelector && !showDetailsForm ? (
            <motion.div
              key="category-selector"
              initial={{ opacity: 0, x: 300 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -300 }}
              className="space-y-6"
            >
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowCategorySelector(false);
                    setSelectedCategory(null);
                  }}
                  disabled={isGenerating}
                >
                  ← Voltar
                </Button>
                <div>
                  <h3 className="text-lg font-semibold">Escolha o tipo de conteúdo</h3>
                  <p className="text-sm text-muted-foreground">Selecione a categoria que melhor se adapta à sua página de conhecimento</p>
                </div>
              </div>

              {/* Campo de busca */}
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar por tipo de conteúdo..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                  disabled={isGenerating}
                />
                {searchTerm && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSearchTerm('')}
                    className="absolute right-1 top-1 h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {/* Contador de resultados */}
              {searchTerm && (
                <div className="text-sm text-muted-foreground">
                  {filteredCategories.length} categoria{filteredCategories.length !== 1 ? 's' : ''} encontrada{filteredCategories.length !== 1 ? 's' : ''}
                  {filteredCategories.length === 0 && (
                    <span className="ml-2 text-amber-600">
                      <Filter className="inline h-3 w-3 mr-1" />
                      Tente termos mais gerais
                    </span>
                  )}
                </div>
              )}

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                {filteredCategories.length === 0 ? (
                  <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
                    <Search className="h-12 w-12 text-muted-foreground mb-4" />
                    <h4 className="text-lg font-medium text-muted-foreground mb-2">
                      Nenhuma categoria encontrada
                    </h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      Tente usar termos mais gerais como "técnico", "processo" ou "manual"
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSearchTerm('')}
                    >
                      Limpar busca
                    </Button>
                  </div>
                ) : (
                  filteredCategories.map((category) => {
                    const IconComponent = category.icon;
                    return (
                    <motion.div
                      key={category.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      layout
                    >
                      <Card 
                        className={`cursor-pointer border-2 transition-all duration-200 hover:shadow-md ${
                          selectedCategory?.id === category.id ? getColorClasses(category.color) : ''
                        }`}
                        onClick={() => !isGenerating && handleCategorySelect(category)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-start gap-3">
                            <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${category.gradient} flex items-center justify-center flex-shrink-0`}>
                              <IconComponent className="h-5 w-5 text-white" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <CardTitle className="text-sm font-semibold leading-tight">{category.title}</CardTitle>
                              <p className="text-xs text-muted-foreground mt-1">{category.description}</p>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0 space-y-3">
                          <div className="space-y-1">
                            {category.examples.slice(0, 2).map((example, index) => (
                              <Badge key={index} variant="secondary" className="text-xs mr-1">
                                {example}
                              </Badge>
                            ))}
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-muted-foreground flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {category.estimatedTime}
                            </span>
                            <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  );
                  })
                )}
              </div>
            </motion.div>
          ) : showDetailsForm && selectedCategory ? (
            <motion.div
              key="details-form"
              initial={{ opacity: 0, x: 300 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -300 }}
              className="space-y-6"
            >
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowDetailsForm(false);
                    setFormData({});
                  }}
                  disabled={isGenerating}
                >
                  ← Voltar
                </Button>
                <div>
                  <h3 className="text-lg font-semibold">Detalhes do conteúdo</h3>
                  <p className="text-sm text-muted-foreground">
                    Preencha os dados para personalizar: <strong>{selectedCategory.title}</strong>
                  </p>
                </div>
              </div>

              <div className="space-y-4 max-h-80 overflow-y-auto">
                {/* Seleção de Espaço */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Espaço de Conhecimento <span className="text-red-500">*</span>
                  </Label>
                  {availableSpaces.length > 0 ? (
                    <Select value={selectedSpaceId} onValueChange={setSelectedSpaceId} disabled={isGenerating}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione um espaço" />
                      </SelectTrigger>
                      <SelectContent>
                        {spacesWithPermissions.map(({ space, hasPermission, isLoading }) => {
                          if (isLoading) {
                            return (
                              <SelectItem key={space.id} value="" disabled>
                                <div className="flex items-center gap-2">
                                  <div className="w-3 h-3 rounded-full bg-gray-300 animate-pulse" />
                                  <span className="text-gray-500">{space.name}</span>
                                  <span className="text-xs text-blue-500 ml-auto">Verificando...</span>
                                </div>
                              </SelectItem>
                            );
                          }

                          if (!hasPermission) {
                            return (
                              <SelectItem key={space.id} value="" disabled>
                                <div className="flex items-center gap-2 opacity-50">
                                  <div className="w-3 h-3 rounded-full bg-red-300" />
                                  <span className="text-gray-500">{space.name}</span>
                                  <span className="text-xs bg-red-100 text-red-600 px-1.5 py-0.5 rounded ml-auto">
                                    Sem acesso
                                  </span>
                                </div>
                              </SelectItem>
                            );
                          }

                          return (
                            <SelectItem key={space.id} value={space.id}>
                              <div className="flex items-center gap-2">
                                <div 
                                  className="w-3 h-3 rounded-full" 
                                  style={{ backgroundColor: space.color }}
                                />
                                {space.name}
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  ) : (
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <p className="text-sm text-yellow-800">
                        Você não tem permissão para criar páginas em nenhum espaço disponível. 
                        Entre em contato com um administrador para obter acesso.
                      </p>
                    </div>
                  )}
                </div>

                {/* Campos específicos da categoria */}
                {selectedCategory.fields.map((field) => (
                  <div key={field.key} className="space-y-2">
                    <Label className="text-sm font-medium">
                      {field.label}
                      {field.required && <span className="text-red-500 ml-1">*</span>}
                    </Label>
                    
                    {field.type === 'text' && (
                      <Input
                        placeholder={field.placeholder}
                        value={formData[field.key] || ''}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          [field.key]: e.target.value
                        }))}
                        disabled={isGenerating}
                      />
                    )}
                    
                    {field.type === 'textarea' && (
                      <Textarea
                        placeholder={field.placeholder}
                        value={formData[field.key] || ''}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          [field.key]: e.target.value
                        }))}
                        disabled={isGenerating}
                        rows={3}
                      />
                    )}
                    
                    {field.type === 'select' && field.options && (
                      <Select
                        value={formData[field.key] || ''}
                        onValueChange={(value) => setFormData(prev => ({
                          ...prev,
                          [field.key]: value
                        }))}
                        disabled={isGenerating}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={field.placeholder} />
                        </SelectTrigger>
                        <SelectContent>
                          {field.options.map((option) => (
                            <SelectItem key={option} value={option}>
                              {option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  </div>
                ))}
              </div>

              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowDetailsForm(false);
                    setFormData({});
                  }}
                  disabled={isGenerating}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleFormSubmit}
                  disabled={isGenerating || availableSpaces.length === 0}
                  className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
                >
                  {isGenerating ? (
                    <>
                      <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                      Gerando...
                    </>
                  ) : (
                    <>
                      <Zap className="w-4 h-4 mr-2" />
                      Gerar Página ({actualCreditsRequired} crédito)
                    </>
                  )}
                </Button>
              </div>

              {isGenerating && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center space-y-4 py-8"
                >
                  <div className="mx-auto w-16 h-16 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center">
                    <Sparkles className="h-8 w-8 text-white animate-spin" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">Gerando sua página de conhecimento...</h3>
                    <p className="text-sm text-muted-foreground">
                      Criando conteúdo estruturado para: <strong>{selectedCategory.title}</strong>
                    </p>
                  </div>
                  <div className="max-w-md mx-auto bg-gray-100 rounded-full h-2 overflow-hidden">
                    <div className="bg-gradient-to-r from-indigo-500 to-purple-600 h-full rounded-full animate-pulse" />
                  </div>
                </motion.div>
              )}
            </motion.div>
          ) : null}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  );
} 