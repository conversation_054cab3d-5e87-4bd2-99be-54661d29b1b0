/**
 * Componente de modal para preview da página de conhecimento.
 * <AUTHOR> Internet 2025
 */
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  Eye,
  ChevronRight,
  Clock,
  Type as IconType, // Renomeado para evitar conflito
  Globe,
  FileText as IconFileText, // Renomeado para evitar conflito
  BookOpen, // Default icon
  // Importando todos os ícones diretamente
  Code,
  Megaphone,
  Users,
  Package,
  Lock,
  Star,
  Heart
} from 'lucide-react';
import type { CreateKnowledgePageData, KnowledgeSpace } from '@/types/knowledge.types';

// Mapeamento de ícones - Agora com importações diretas
const iconMap: { [key: string]: React.ElementType } = {
  BookOpen,
  Code,
  Megaphone,
  Users,
  Package,
  FileText: IconFileText, // Usando o alias importado
  Globe,
  Lock,
  Star,
  Heart,
};

interface KnowledgePagePreviewModalProps {
  showPreview: boolean;
  setShowPreview: (show: boolean) => void;
  formData: CreateKnowledgePageData;
  selectedSpace: KnowledgeSpace | undefined;
  calculateReadingTime: (text: string) => number;
  countWords: (text: string) => number;
}

export function KnowledgePagePreviewModal({
  showPreview,
  setShowPreview,
  formData,
  selectedSpace,
  calculateReadingTime,
  countWords,
}: KnowledgePagePreviewModalProps) {
  if (!showPreview) return null;

  return (
    <Dialog open={showPreview} onOpenChange={setShowPreview}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto p-0">
        <DialogHeader className="p-6 pb-4 border-b bg-slate-50 rounded-t-lg">
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Eye className="h-5 w-5 text-blue-600" />
            Preview da Página de Conhecimento
          </DialogTitle>
          <DialogDescription>
            Esta é uma visualização de como sua página aparecerá para os usuários.
          </DialogDescription>
        </DialogHeader>
        
        <div className="p-6 space-y-8">
          {/* Header da página no preview */}
          <div className="space-y-4 border-b pb-6 mb-6">
            <div className="flex items-center gap-2 text-sm text-slate-500">
              {selectedSpace && (
                <>
                  {(() => {
                    const IconComponent = iconMap[selectedSpace.icon as keyof typeof iconMap] || BookOpen;
                    return <IconComponent className="h-4 w-4" style={{ color: selectedSpace.color || '#000000' }} />;
                  })()}
                  <span style={{ color: selectedSpace.color || '#000000' }}>{selectedSpace.name}</span>
                </>
              )}
            </div>
            
            <h1 className="text-4xl font-bold text-slate-800 tracking-tight">
              {formData.title || 'Página Sem Título'}
            </h1>
            
            {formData.excerpt && (
              <p className="text-lg text-slate-600 leading-relaxed">
                {formData.excerpt}
              </p>
            )}
            
            {formData.tags && formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 pt-2">
                {formData.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200 shadow-sm">
                    #{tag}
                  </Badge>
                ))}
              </div>
            )}
            
            <div className="flex items-center flex-wrap gap-x-6 gap-y-2 text-sm text-slate-500 pt-4 mt-4 border-t">
              <div className="flex items-center gap-1.5">
                <Clock className="h-4 w-4" />
                <span>Leitura: {calculateReadingTime(formData.content || '')} min</span>
              </div>
              <div className="flex items-center gap-1.5">
                <IconType className="h-4 w-4" />
                <span>{countWords(formData.content || '')} palavras</span>
              </div>
              <div className="flex items-center gap-1.5">
                <Globe className="h-4 w-4" />
                <span>
                  Visibilidade: 
                  {formData.visibility === 'company' && ' Toda a Empresa'}
                  {formData.visibility === 'space' && ' Membros do Espaço'}
                  {formData.visibility === 'restricted' && ' Acesso Restrito'}
                </span>
              </div>
            </div>
          </div>
          
          {/* Conteúdo Principal */}
          <div className="prose prose-slate max-w-none lg:prose-lg">
            {formData.content ? (
              // Idealmente, aqui seria um renderizador de Markdown para HTML
              // Por enquanto, exibindo como texto pré-formatado
              <div 
                className="p-4 bg-slate-50 border border-slate-200 rounded-lg prose-pre:bg-slate-100 prose-pre:p-4 prose-pre:rounded-md prose-code:bg-slate-100 prose-code:px-1 prose-code:py-0.5 prose-code:rounded"
                dangerouslySetInnerHTML={{ __html: formData.content.replace(/\n/g, '<br />') }}
              />
            ) : (
              <div className="text-center py-12 text-slate-500 bg-slate-50 rounded-lg">
                <IconFileText className="h-16 w-16 mx-auto mb-4 text-slate-300" />
                <h3 className="text-xl font-semibold">Nenhum Conteúdo Adicionado</h3>
                <p className="text-sm">Comece a escrever na aba de edição para visualizar o conteúdo aqui.</p>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 