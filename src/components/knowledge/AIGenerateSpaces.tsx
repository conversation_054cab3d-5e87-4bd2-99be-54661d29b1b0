/**
 * AIGenerateSpaces - Componente para gerar espaços de conhecimento usando IA
 * Integrado com sistema de créditos (1 crédito por geração)
 * <AUTHOR> Internet 2025
 */
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Wand2,
  Sparkles,
  BookOpen,
  Zap,
  Crown,
  AlertTriangle,
  Brain,
  Target,
  Users,
  Settings,
  Code,
  Megaphone,
  Package,
  Globe,
  Calendar,
  Star,
  Heart,
  Activity,
  Lightbulb,
  Rocket
} from "lucide-react";
import { useAIFeatureWithCredits, useAIFeatureAvailability } from "@/lib/query/hooks/useAICredits";
import { useUpgradeContext } from "@/hooks/useUpgradeContext";
import { successWithNotification, errorWithNotification } from "@/lib/notifications/toastWithNotification";
import { supabase } from "@/integrations/supabase/client";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";

interface AIGenerateSpacesProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

interface GenerateSpacesRequest {
  companyType: string;
  industry: string;
  teamSize: number;
  knowledgeAreas: string[];
  customRequirements: string;
  spacesCount: number;
}

// Áreas de conhecimento pré-definidas
const knowledgeAreaOptions = [
  { value: "technical", label: "Documentação Técnica", icon: Code },
  { value: "policies", label: "Políticas e Procedimentos", icon: Settings },
  { value: "training", label: "Treinamentos e Capacitação", icon: Target },
  { value: "communication", label: "Comunicação Interna", icon: Megaphone },
  { value: "products", label: "Produtos e Serviços", icon: Package },
  { value: "projects", label: "Gestão de Projetos", icon: Calendar },
  { value: "innovation", label: "Inovação e Pesquisa", icon: Lightbulb },
  { value: "culture", label: "Cultura Organizacional", icon: Heart },
  { value: "processes", label: "Processos e Qualidade", icon: Activity },
  { value: "strategy", label: "Estratégia e Planejamento", icon: Star },
  { value: "collaboration", label: "Colaboração e Equipes", icon: Users },
  { value: "external", label: "Conhecimento Externo", icon: Globe },
];

// Tipos de empresa
const companyTypes = [
  "Startup Tecnológica",
  "Empresa de Software",
  "Consultoria",
  "Manufatura",
  "Varejo",
  "Serviços Financeiros",
  "Saúde e Medicina",
  "Educação",
  "Marketing e Publicidade",
  "Recursos Humanos",
  "Logística e Transporte",
  "Construção",
  "Agronegócio",
  "Energia",
  "Outros"
];

// Setores de indústria
const industries = [
  "Tecnologia da Informação",
  "Financeiro",
  "Saúde",
  "Educação",
  "Varejo",
  "Manufatura",
  "Serviços",
  "Agronegócio",
  "Energia",
  "Construção",
  "Logística",
  "Marketing",
  "Outros"
];

export function AIGenerateSpaces({ open, onOpenChange, onSuccess }: AIGenerateSpacesProps) {
  const [formData, setFormData] = useState<Partial<GenerateSpacesRequest>>({
    spacesCount: 5,
    knowledgeAreas: [],
    customRequirements: ""
  });
  const [isGenerating, setIsGenerating] = useState(false);

  // Hooks para IA e créditos
  const {
    executeWithCredits,
    feature,
    balance,
    isLoading: isExecuting,
    canUseFeature,
    creditsRequired,
    creditsRemaining,
  } = useAIFeatureWithCredits('knowledge_space_generation');

  const {
    isAvailable,
    feature: availabilityFeature,
    insufficientCredits,
  } = useAIFeatureAvailability('knowledge_space_generation');

  // Usar 1 crédito se a funcionalidade não existir ainda
  const actualCreditsRequired = creditsRequired || 1;
  const actualCreditsRemaining = creditsRemaining || (balance?.remaining_credits ?? 0);
  const hasEnoughCredits = actualCreditsRemaining >= actualCreditsRequired;
  
  // Funcionalidade disponível se: existe e está ativa, OU se temos créditos suficientes
  const isFeatureAvailable = isAvailable !== false && hasEnoughCredits;

  // Hook para upgrade contextual
  const { upgradeForAI } = useUpgradeContext();

  const totalCredits = balance?.plan_credits || 5;

  const handleKnowledgeAreaToggle = (area: string) => {
    setFormData(prev => ({
      ...prev,
      knowledgeAreas: prev.knowledgeAreas?.includes(area)
        ? prev.knowledgeAreas.filter(a => a !== area)
        : [...(prev.knowledgeAreas || []), area]
    }));
  };

  const handleGenerate = async () => {
    console.log('🔥 BOTÃO CLICADO - handleGenerate iniciado');
    console.log('🔍 Estado dos hooks:', {
      isAvailable,
      hasEnoughCredits,
      creditsRequired,
      creditsRemaining,
      insufficientCredits,
      balance
    });
    console.log('📝 FormData atual:', formData);

    if (!isFeatureAvailable) {
      console.log('❌ Bloqueado por créditos ou disponibilidade');
      if (insufficientCredits) {
        console.log('💸 Créditos insuficientes - mostrando notificação');
        errorWithNotification("Créditos insuficientes", {
          description: `Você precisa de ${actualCreditsRequired} crédito para gerar espaços. Restam: ${actualCreditsRemaining}.`
        });
      }
      return;
    }

    // Validar dados obrigatórios
    console.log('✅ Validando dados obrigatórios...');
    if (!formData.companyType || !formData.industry || !formData.teamSize) {
      console.log('❌ Dados obrigatórios faltando:', {
        companyType: formData.companyType,
        industry: formData.industry,
        teamSize: formData.teamSize
      });
      errorWithNotification('Dados obrigatórios', {
        description: 'Por favor, preencha tipo da empresa, setor e tamanho da equipe.'
      });
      return;
    }

    if (!formData.knowledgeAreas?.length) {
      console.log('❌ Áreas de conhecimento não selecionadas:', formData.knowledgeAreas);
      errorWithNotification('Áreas de conhecimento', {
        description: 'Selecione pelo menos uma área de conhecimento.'
      });
      return;
    }

    console.log('🚀 Todas as validações passaram - iniciando geração');
    setIsGenerating(true);
    logQueryEvent('AIGenerateSpaces', 'Iniciando geração de espaços com IA', formData);

    try {
      console.log('🤖 Executando aiFeature.executeWithCredits...');
      // Usar o hook de créditos para executar e rastrear
      const result = await executeWithCredits(
        async () => {
          console.log('📡 Fazendo requisição para edge function...');
          const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-knowledge-spaces`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
            },
            body: JSON.stringify(formData)
          });

          console.log('📡 Resposta da edge function:', response.status, response.statusText);

          if (!response.ok) {
            const errorData = await response.json();
            console.log('❌ Erro na resposta:', errorData);
            throw new Error(errorData.error || `Erro na API: ${response.status}`);
          }

          const result = await response.json();
          console.log('✅ Resultado da edge function:', result);
          return result;
        },
        {
          provider: 'groq',
          model: 'llama-3.1-8b-instant',
          apiEndpoint: 'generate-knowledge-spaces',
          requestData: formData,
          contextType: 'knowledge_space_generation',
          usageDescription: `Geração de ${formData.spacesCount} espaços para ${formData.companyType} - ${formData.industry}`
        }
      );

      console.log('🎉 Espaços gerados com sucesso:', result);
      logQueryEvent('AIGenerateSpaces', 'Espaços gerados com sucesso', { 
        spacesCreated: result.spaces_created,
        totalSpaces: result.total_spaces 
      });

      const remainingAfterUse = actualCreditsRemaining - actualCreditsRequired;
      successWithNotification('Espaços criados com sucesso!', {
        description: `${result.spaces_created} espaços foram criados. Restam ${remainingAfterUse} de ${totalCredits} créditos este mês.`,
        persist: true
      });

      // Reset form e fechar modal
      setFormData({
        spacesCount: 5,
        knowledgeAreas: [],
        customRequirements: ""
      });
      onOpenChange(false);
      onSuccess();

    } catch (error) {
      console.error('💥 Erro ao gerar espaços:', error);
      logQueryEvent('AIGenerateSpaces', 'Erro na geração de espaços', { error }, 'error');
      
      let errorTitle = 'Erro na geração de espaços';
      let errorDescription = 'Não foi possível gerar os espaços automaticamente.';
      
      if (error instanceof Error) {
        console.log('🔍 Detalhes do erro:', error.message);
        if (error.message.includes('404')) {
          errorTitle = 'Serviço temporariamente indisponível';
          errorDescription = 'As funções de IA estão em manutenção. Tente novamente em alguns minutos.';
        } else if (error.message.includes('401') || error.message.includes('403')) {
          errorTitle = 'Erro de autenticação';
          errorDescription = 'Problema de acesso às funções de IA. Tente novamente em alguns minutos.';
        }
      }
      
      errorWithNotification(errorTitle, {
        description: errorDescription,
        persist: true
      });
    } finally {
      console.log('🏁 Finalizando geração - setIsGenerating(false)');
      setIsGenerating(false);
    }
  };

  return (
    <TooltipProvider>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="space-y-4">
            <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center gap-2">
              <Brain className="h-6 w-6 text-blue-600" />
              Gerar Espaços com IA
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge 
                    variant="secondary" 
                    className={`cursor-help ${
                      actualCreditsRemaining <= 2 
                        ? "bg-red-100 text-red-700 border-red-200" 
                        : actualCreditsRemaining <= 5
                        ? "bg-orange-100 text-orange-700 border-orange-200"
                        : "bg-blue-100 text-blue-700 border-blue-200"
                    }`}
                  >
                    <Zap className="w-3 h-3 mr-1" />
                    {actualCreditsRemaining}
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Você tem {actualCreditsRemaining} de {totalCredits} créditos restantes este mês</p>
                </TooltipContent>
              </Tooltip>
            </DialogTitle>
            <DialogDescription className="text-base">
              Nossa IA criará espaços de conhecimento personalizados para sua empresa, organizados por área e prontos para uso.
            </DialogDescription>
          </DialogHeader>

          {/* Aviso de créditos insuficientes */}
          {!hasEnoughCredits && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertTitle className="text-red-800">Créditos insuficientes</AlertTitle>
              <AlertDescription className="text-red-700 space-y-3">
                <p>A geração de espaços precisa de {actualCreditsRequired} crédito, mas você tem apenas {actualCreditsRemaining}.</p>
                <Button 
                  size="sm" 
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                  onClick={() => {
                    onOpenChange(false);
                    upgradeForAI();
                  }}
                >
                  <Crown className="w-4 h-4 mr-1" />
                  Upgrade
                </Button>
              </AlertDescription>
            </Alert>
          )}

          <AnimatePresence>
            {!isGenerating ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-6"
              >
                {/* Informações da empresa */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="companyType">Tipo da empresa *</Label>
                    <Select value={formData.companyType || ""} onValueChange={(value) => setFormData(prev => ({ ...prev, companyType: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo" />
                      </SelectTrigger>
                      <SelectContent>
                        {companyTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="industry">Setor de atuação *</Label>
                    <Select value={formData.industry || ""} onValueChange={(value) => setFormData(prev => ({ ...prev, industry: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o setor" />
                      </SelectTrigger>
                      <SelectContent>
                        {industries.map((industry) => (
                          <SelectItem key={industry} value={industry}>
                            {industry}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="teamSize">Tamanho da equipe *</Label>
                    <Input
                      type="number"
                      placeholder="Ex: 50"
                      value={formData.teamSize || ""}
                      onChange={(e) => setFormData(prev => ({ ...prev, teamSize: parseInt(e.target.value) || 0 }))}
                      min={1}
                      max={10000}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="spacesCount">Número de espaços a gerar</Label>
                    <Select value={formData.spacesCount?.toString() || "5"} onValueChange={(value) => setFormData(prev => ({ ...prev, spacesCount: parseInt(value) }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="3">3 espaços</SelectItem>
                        <SelectItem value="5">5 espaços</SelectItem>
                        <SelectItem value="7">7 espaços</SelectItem>
                        <SelectItem value="10">10 espaços</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Áreas de conhecimento */}
                <div className="space-y-3">
                  <Label>Áreas de conhecimento * (selecione as relevantes)</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {knowledgeAreaOptions.map((area) => {
                      const IconComponent = area.icon;
                      const isSelected = formData.knowledgeAreas?.includes(area.value) || false;
                      
                      return (
                        <motion.div
                          key={area.value}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <Button
                            type="button"
                            variant={isSelected ? "default" : "outline"}
                            size="sm"
                            className={`w-full justify-start h-auto p-3 ${
                              isSelected 
                                ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white' 
                                : 'hover:bg-blue-50'
                            }`}
                            onClick={() => handleKnowledgeAreaToggle(area.value)}
                          >
                            <IconComponent className="h-4 w-4 mr-2 flex-shrink-0" />
                            <span className="text-xs leading-tight">{area.label}</span>
                          </Button>
                        </motion.div>
                      );
                    })}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Selecionadas: {formData.knowledgeAreas?.length || 0} áreas
                  </p>
                </div>

                {/* Requisitos customizados */}
                <div className="space-y-2">
                  <Label htmlFor="customRequirements">Requisitos específicos (opcional)</Label>
                  <Textarea
                    placeholder="Descreva necessidades específicas, estrutura organizacional, processos importantes, etc."
                    value={formData.customRequirements || ""}
                    onChange={(e) => setFormData(prev => ({ ...prev, customRequirements: e.target.value }))}
                    rows={3}
                  />
                </div>

                {/* Botões */}
                <div className="flex justify-end gap-3 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                    disabled={isGenerating}
                  >
                    Cancelar
                  </Button>
                  <Button
                    onClick={handleGenerate}
                    disabled={!hasEnoughCredits || isGenerating}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  >
                    <Wand2 className="w-4 h-4 mr-2" />
                    Gerar Espaços ({actualCreditsRequired} crédito)
                  </Button>
                </div>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center space-y-6 py-8"
              >
                <div className="mx-auto w-20 h-20 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                  <Brain className="h-10 w-10 text-white animate-pulse" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-xl font-semibold">Gerando espaços de conhecimento...</h3>
                  <p className="text-muted-foreground">
                    Nossa IA está criando espaços personalizados para <strong>{formData.companyType}</strong>
                  </p>
                  <div className="flex justify-center items-center gap-2 text-sm text-muted-foreground">
                    <Sparkles className="h-4 w-4 animate-spin" />
                    Analisando {formData.knowledgeAreas?.length} áreas de conhecimento
                  </div>
                </div>
                <div className="max-w-md mx-auto bg-gray-100 rounded-full h-3 overflow-hidden">
                  <div className="bg-gradient-to-r from-blue-500 to-purple-600 h-full rounded-full animate-pulse" />
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  );
} 