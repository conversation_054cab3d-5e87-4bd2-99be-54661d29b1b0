/**
 * AbsenceTypeDialog - Dialog para Criar/Editar Tipos de Ausência
 * 
 * Dialog completo para gerenciar tipos de ausência:
 * - Formulário para criar/editar tipos
 * - Seletor de cores e ícones
 * - Configurações avançadas
 * - Validações em tempo real
 * 
 * <AUTHOR> Internet 2025
 */
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Calendar,
  Clock,
  Sun,
  Heart,
  Baby,
  Users,
  FileText,
  Flower,
  GraduationCap,
  Home,
  Plane,
  CheckCircle2,
  AlertTriangle,
  Shield,
  Palette,
  Settings,
  Save,
  X
} from "lucide-react";
import { useCreateAbsenceType, useUpdateAbsenceType } from "@/lib/query/hooks/useAbsenceTypes";

// =====================================================
// INTERFACES E SCHEMAS
// =====================================================

interface AbsenceType {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  requires_approval: boolean;
  allows_self_registration: boolean;
  requires_justification: boolean;
  max_days_in_advance: number;
  max_duration_days: number;
  is_active: boolean;
}

interface AbsenceTypeDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  absenceType?: AbsenceType | null;
  onSuccess: () => void;
}

const absenceTypeSchema = z.object({
  name: z.string().min(3, "Nome deve ter pelo menos 3 caracteres").max(100, "Nome muito longo"),
  description: z.string().max(500, "Descrição muito longa").optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Cor deve estar no formato #RRGGBB"),
  icon: z.string().min(1, "Selecione um ícone"),
  requires_approval: z.boolean(),
  allows_self_registration: z.boolean(),
  requires_justification: z.boolean(),
  max_days_in_advance: z.number().min(0, "Deve ser pelo menos 0").max(365, "Máximo 365 dias"),
  max_duration_days: z.number().min(1, "Deve ser pelo menos 1").max(365, "Máximo 365 dias"),
  is_active: z.boolean(),
});

type AbsenceTypeFormData = z.infer<typeof absenceTypeSchema>;

// =====================================================
// CONFIGURAÇÕES
// =====================================================

const availableIcons = [
  { key: 'Calendar', label: 'Calendário', icon: Calendar },
  { key: 'Clock', label: 'Relógio', icon: Clock },
  { key: 'Sun', label: 'Sol', icon: Sun },
  { key: 'Heart', label: 'Coração', icon: Heart },
  { key: 'Baby', label: 'Bebê', icon: Baby },
  { key: 'Users', label: 'Usuários', icon: Users },
  { key: 'FileText', label: 'Documento', icon: FileText },
  { key: 'Flower', label: 'Flor', icon: Flower },
  { key: 'GraduationCap', label: 'Formatura', icon: GraduationCap },
  { key: 'Home', label: 'Casa', icon: Home },
  { key: 'Plane', label: 'Avião', icon: Plane },
  { key: 'Shield', label: 'Escudo', icon: Shield },
];

const predefinedColors = [
  '#22C55E', '#EF4444', '#EC4899', '#3B82F6',
  '#F59E0B', '#8B5CF6', '#6B7280', '#06B6D4',
  '#10B981', '#F97316', '#84CC16', '#F43F5E',
];

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export function AbsenceTypeDialog({ 
  isOpen, 
  onOpenChange, 
  absenceType, 
  onSuccess 
}: AbsenceTypeDialogProps) {
  const [activeTab, setActiveTab] = useState("basic");
  
  const isEditing = !!absenceType;
  
  // Hooks para mutations
  const createMutation = useCreateAbsenceType();
  const updateMutation = useUpdateAbsenceType();

  const form = useForm<AbsenceTypeFormData>({
    resolver: zodResolver(absenceTypeSchema),
    defaultValues: {
      name: "",
      description: "",
      color: "#6B7280",
      icon: "Calendar",
      requires_approval: false,
      allows_self_registration: true,
      requires_justification: false,
      max_days_in_advance: 30,
      max_duration_days: 30,
      is_active: true,
    },
  });

  // Resetar form quando dialog abre/fecha ou tipo muda
  useEffect(() => {
    if (isOpen) {
      if (absenceType) {
        form.reset({
          name: absenceType.name,
          description: absenceType.description || "",
          color: absenceType.color,
          icon: absenceType.icon,
          requires_approval: absenceType.requires_approval,
          allows_self_registration: absenceType.allows_self_registration,
          requires_justification: absenceType.requires_justification,
          max_days_in_advance: absenceType.max_days_in_advance,
          max_duration_days: absenceType.max_duration_days,
          is_active: absenceType.is_active,
        });
      } else {
        form.reset({
          name: "",
          description: "",
          color: "#6B7280",
          icon: "Calendar",
          requires_approval: false,
          allows_self_registration: true,
          requires_justification: false,
          max_days_in_advance: 30,
          max_duration_days: 30,
          is_active: true,
        });
      }
    }
  }, [isOpen, absenceType, form]);

  // Handler do submit
  const onSubmit = async (data: AbsenceTypeFormData) => {
    try {
      if (isEditing) {
        await updateMutation.mutateAsync({
          id: absenceType.id,
          ...data,
        });
      } else {
        await createMutation.mutateAsync(data);
      }
      onSuccess();
    } catch (error) {
      console.error('Erro ao salvar tipo de ausência:', error);
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  // Assistir mudanças nos campos para preview
  const watchedColor = form.watch("color");
  const watchedIcon = form.watch("icon");
  const watchedName = form.watch("name");

  const selectedIcon = availableIcons.find(i => i.key === watchedIcon)?.icon || Calendar;

  // =====================================================
  // COMPONENTES AUXILIARES
  // =====================================================

  const ColorPicker = () => (
    <div className="space-y-3">
      <Label>Cor</Label>
      <div className="flex flex-wrap gap-2">
        {predefinedColors.map((color) => (
          <button
            key={color}
            type="button"
            className={`w-8 h-8 rounded-lg border-2 transition-all hover:scale-110 ${
              watchedColor === color ? 'border-gray-900 scale-110' : 'border-gray-200'
            }`}
            style={{ backgroundColor: color }}
            onClick={() => form.setValue("color", color)}
          />
        ))}
      </div>
      <Input
        {...form.register("color")}
        placeholder="#RRGGBB"
        className="font-mono"
      />
      {form.formState.errors.color && (
        <p className="text-sm text-red-500">{form.formState.errors.color.message}</p>
      )}
    </div>
  );

  const IconPicker = () => (
    <div className="space-y-3">
      <Label>Ícone</Label>
      <div className="grid grid-cols-6 gap-2">
        {availableIcons.map((iconOption) => {
          const IconComponent = iconOption.icon;
          return (
            <button
              key={iconOption.key}
              type="button"
              className={`p-3 rounded-lg border-2 transition-all hover:scale-105 flex flex-col items-center gap-1 ${
                watchedIcon === iconOption.key ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => form.setValue("icon", iconOption.key)}
            >
              <IconComponent className="h-5 w-5" />
              <span className="text-xs text-center">{iconOption.label}</span>
            </button>
          );
        })}
      </div>
      {form.formState.errors.icon && (
        <p className="text-sm text-red-500">{form.formState.errors.icon.message}</p>
      )}
    </div>
  );

  const PreviewCard = () => {
    const IconComponent = selectedIcon;
    
    return (
      <Card className="border-dashed">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div 
              className="p-2 rounded-lg flex-shrink-0"
              style={{ backgroundColor: `${watchedColor}20`, border: `1px solid ${watchedColor}40` }}
            >
              <IconComponent 
                className="h-5 w-5" 
                style={{ color: watchedColor }}
              />
            </div>
            <div className="space-y-1">
              <h4 className="font-medium">{watchedName || "Nome do Tipo"}</h4>
              <p className="text-sm text-muted-foreground">Preview do tipo de ausência</p>
              <div className="flex gap-2">
                <Badge variant="outline" className="text-xs">
                  <Calendar className="h-3 w-3 mr-1" />
                  Configurado
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  // =====================================================
  // RENDER
  // =====================================================

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Editar Tipo de Ausência" : "Novo Tipo de Ausência"}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? "Modifique as configurações do tipo de ausência."
              : "Configure um novo tipo de ausência para a empresa."
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">
                <Settings className="h-4 w-4 mr-2" />
                Básico
              </TabsTrigger>
              <TabsTrigger value="appearance">
                <Palette className="h-4 w-4 mr-2" />
                Aparência
              </TabsTrigger>
              <TabsTrigger value="rules">
                <Shield className="h-4 w-4 mr-2" />
                Regras
              </TabsTrigger>
            </TabsList>

            {/* Tab: Informações Básicas */}
            <TabsContent value="basic" className="space-y-4">
              <div className="space-y-3">
                <Label htmlFor="name">Nome *</Label>
                <Input
                  id="name"
                  {...form.register("name")}
                  placeholder="Ex: Férias, Licença médica..."
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-500">{form.formState.errors.name.message}</p>
                )}
              </div>

              <div className="space-y-3">
                <Label htmlFor="description">Descrição</Label>
                <Textarea
                  id="description"
                  {...form.register("description")}
                  placeholder="Descrição opcional para o tipo de ausência"
                  rows={3}
                />
                {form.formState.errors.description && (
                  <p className="text-sm text-red-500">{form.formState.errors.description.message}</p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={form.watch("is_active")}
                  onCheckedChange={(checked) => form.setValue("is_active", checked)}
                />
                <Label htmlFor="is_active">Tipo ativo</Label>
              </div>

              <PreviewCard />
            </TabsContent>

            {/* Tab: Aparência */}
            <TabsContent value="appearance" className="space-y-6">
              <ColorPicker />
              <IconPicker />
              <PreviewCard />
            </TabsContent>

            {/* Tab: Regras */}
            <TabsContent value="rules" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-3">
                  <Label htmlFor="max_days_in_advance">Antecedência Máxima (dias)</Label>
                  <Input
                    id="max_days_in_advance"
                    type="number"
                    min="0"
                    max="365"
                    {...form.register("max_days_in_advance", { valueAsNumber: true })}
                  />
                  {form.formState.errors.max_days_in_advance && (
                    <p className="text-sm text-red-500">{form.formState.errors.max_days_in_advance.message}</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="max_duration_days">Duração Máxima (dias)</Label>
                  <Input
                    id="max_duration_days"
                    type="number"
                    min="1"
                    max="365"
                    {...form.register("max_duration_days", { valueAsNumber: true })}
                  />
                  {form.formState.errors.max_duration_days && (
                    <p className="text-sm text-red-500">{form.formState.errors.max_duration_days.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="requires_approval"
                    checked={form.watch("requires_approval")}
                    onCheckedChange={(checked) => form.setValue("requires_approval", checked)}
                  />
                  <Label htmlFor="requires_approval">Requer aprovação</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="allows_self_registration"
                    checked={form.watch("allows_self_registration")}
                    onCheckedChange={(checked) => form.setValue("allows_self_registration", checked)}
                  />
                  <Label htmlFor="allows_self_registration">Permite auto-registro pelo usuário</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="requires_justification"
                    checked={form.watch("requires_justification")}
                    onCheckedChange={(checked) => form.setValue("requires_justification", checked)}
                  />
                  <Label htmlFor="requires_justification">Requer justificativa</Label>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              <X className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
            >
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? "Salvando..." : isEditing ? "Salvar Alterações" : "Criar Tipo"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}