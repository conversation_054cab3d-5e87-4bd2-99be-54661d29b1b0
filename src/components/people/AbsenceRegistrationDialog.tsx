/**
 * Dialog para Registro de Ausências
 * 
 * Permite que usuários registrem suas próprias ausências ou que
 * administradores registrem ausências para outros usuários.
 * 
 * FUNCIONALIDADES:
 * - Formulário dinâmico baseado no tipo de ausência
 * - Validação de regras de negócio
 * - Diferenciação entre auto-registro e registro administrativo
 * - Preview das informações antes do envio
 * - Validação de conflitos de datas
 * 
 * <AUTHOR> Internet 2025
 */

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format, addDays, isBefore, differenceInDays } from "date-fns";
import { ptBR } from "date-fns/locale";
import { motion } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  CalendarIcon,
  Clock,
  AlertTriangle,
  Info,
  UserPlus,
  CheckCircle,
  Loader2,
  Calendar as CalendarLucide
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useAbsenceTypes, useCreateAbsence, useMyAbsences, type AbsenceType, type CreateAbsenceData } from "@/lib/query/hooks/useAbsences";
import { useAuthStore } from "@/stores/authStore";
import { GenericPermissionGate } from "@/components/auth/GenericPermissionGate";
import { useCompanyUsers } from "@/lib/query/hooks/useUsers";

// =====================================================
// SCHEMA E VALIDAÇÃO
// =====================================================

const createAbsenceSchema = z.object({
  user_id: z.string().min(1, "Usuário é obrigatório"),
  absence_type_id: z.string().min(1, "Tipo de ausência é obrigatório"),
  start_date: z.date({
    required_error: "Data de início é obrigatória",
  }),
  end_date: z.date({
    required_error: "Data de fim é obrigatória",
  }),
  start_time: z.string().optional(),
  end_time: z.string().optional(),
  reason: z.string().min(3, "Motivo deve ter pelo menos 3 caracteres"),
  justification: z.string().optional(),
  notes: z.string().optional(),
}).refine((data) => {
  return !isBefore(data.end_date, data.start_date);
}, {
  message: "Data de fim deve ser posterior à data de início",
  path: ["end_date"],
}).refine((data) => {
  // Se tem horários, validar
  if (data.start_time && data.end_time) {
    return data.start_time < data.end_time;
  }
  return true;
}, {
  message: "Horário de fim deve ser posterior ao horário de início",
  path: ["end_time"],
});

type FormData = z.infer<typeof createAbsenceSchema>;

// =====================================================
// PROPS E INTERFACES
// =====================================================

interface AbsenceRegistrationDialogProps {
  /** Trigger para abrir o dialog */
  trigger?: React.ReactNode;
  /** Se permite registro para outros usuários (modo admin) */
  allowAdminMode?: boolean;
  /** Usuário pré-selecionado (quando em modo admin) */
  selectedUserId?: string;
  /** Callback quando ausência é criada com sucesso */
  onSuccess?: (absenceId: string) => void;
  /** Se o dialog deve estar aberto por padrão */
  defaultOpen?: boolean;
}

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export function AbsenceRegistrationDialog({
  trigger,
  allowAdminMode = false,
  selectedUserId,
  onSuccess,
  defaultOpen = false,
}: AbsenceRegistrationDialogProps) {
  const [open, setOpen] = useState(defaultOpen);
  const [selectedType, setSelectedType] = useState<AbsenceType | null>(null);
  const [isPartialDay, setIsPartialDay] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Hooks
  const { user } = useAuthStore();
  const { data: absenceTypes = [], isLoading: loadingTypes } = useAbsenceTypes();
  const { data: myAbsences = [] } = useMyAbsences();
  const { data: companyUsers = [] } = useCompanyUsers();
  const createAbsenceMutation = useCreateAbsence();

  // Form
  const form = useForm<FormData>({
    resolver: zodResolver(createAbsenceSchema),
    defaultValues: {
      user_id: selectedUserId || user?.id || "",
      absence_type_id: "",
      start_date: new Date(),
      end_date: new Date(),
      start_time: "",
      end_time: "",
      reason: "",
      justification: "",
      notes: "",
    },
  });

  const watchedValues = form.watch();

  // =====================================================
  // EFEITOS
  // =====================================================

  // Resetar tipo selecionado quando tipo muda
  useEffect(() => {
    const typeId = watchedValues.absence_type_id;
    const type = absenceTypes.find(t => t.id === typeId);
    setSelectedType(type || null);
    
    // Limpar justificação se não for mais obrigatória
    if (type && !type.requires_justification) {
      form.setValue('justification', '');
    }
  }, [watchedValues.absence_type_id, absenceTypes, form]);

  // =====================================================
  // FUNÇÕES AUXILIARES
  // =====================================================

  const handleSubmit = async (data: FormData) => {
    try {
      setShowPreview(false);
      
      const absenceData: CreateAbsenceData = {
        user_id: data.user_id,
        absence_type_id: data.absence_type_id,
        start_date: format(data.start_date, 'yyyy-MM-dd'),
        end_date: format(data.end_date, 'yyyy-MM-dd'),
        start_time: isPartialDay ? data.start_time : undefined,
        end_time: isPartialDay ? data.end_time : undefined,
        reason: data.reason,
        justification: data.justification || undefined,
        notes: data.notes || undefined,
      };

      const absenceId = await createAbsenceMutation.mutateAsync(absenceData);
      
      // Sucesso
      setOpen(false);
      form.reset();
      setSelectedType(null);
      setIsPartialDay(false);
      onSuccess?.(absenceId);
      
    } catch (error) {
      console.error('Erro ao criar ausência:', error);
    }
  };

  const handlePreview = () => {
    setShowPreview(true);
  };

  const calculateDuration = () => {
    if (!watchedValues.start_date || !watchedValues.end_date) return 0;
    return differenceInDays(watchedValues.end_date, watchedValues.start_date) + 1;
  };

  const validateAdvanceNotice = () => {
    if (!selectedType || !watchedValues.start_date) return true;
    const daysInAdvance = differenceInDays(watchedValues.start_date, new Date());
    return daysInAdvance <= selectedType.max_days_in_advance;
  };

  const validateDuration = () => {
    if (!selectedType) return true;
    const duration = calculateDuration();
    return duration <= selectedType.max_duration_days;
  };

  const hasConflicts = () => {
    if (!watchedValues.start_date || !watchedValues.end_date) return false;
    
    return myAbsences.some(absence => {
      const absenceStart = new Date(absence.start_date);
      const absenceEnd = new Date(absence.end_date);
      const newStart = watchedValues.start_date;
      const newEnd = watchedValues.end_date;
      
      return !(newEnd < absenceStart || newStart > absenceEnd) && 
             ['pending', 'approved'].includes(absence.status);
    });
  };

  // =====================================================
  // COMPONENTES AUXILIARES
  // =====================================================

  const TypeInfoBadge = ({ type }: { type: AbsenceType }) => (
    <div className="flex flex-wrap gap-2 mt-2">
      {type.requires_approval && (
        <Badge variant="outline" className="text-orange-700 border-orange-200 bg-orange-50">
          <AlertTriangle className="h-3 w-3 mr-1" />
          Requer Aprovação
        </Badge>
      )}
      {type.requires_justification && (
        <Badge variant="outline" className="text-blue-700 border-blue-200 bg-blue-50">
          <Info className="h-3 w-3 mr-1" />
          Justificativa Obrigatória
        </Badge>
      )}
      {!type.allows_self_registration && (
        <Badge variant="outline" className="text-red-700 border-red-200 bg-red-50">
          <UserPlus className="h-3 w-3 mr-1" />
          Apenas Admin
        </Badge>
      )}
    </div>
  );

  const PreviewSection = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-4 p-4 bg-slate-50 rounded-lg border"
    >
      <div className="flex items-center gap-2">
        <CheckCircle className="h-5 w-5 text-green-500" />
        <h4 className="font-medium">Resumo da Ausência</h4>
      </div>
      
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <Label className="text-muted-foreground">Tipo</Label>
          <p className="font-medium">{selectedType?.name}</p>
        </div>
        <div>
          <Label className="text-muted-foreground">Duração</Label>
          <p className="font-medium">{calculateDuration()} dia(s)</p>
        </div>
        <div>
          <Label className="text-muted-foreground">Período</Label>
          <p className="font-medium">
            {format(watchedValues.start_date, 'dd/MM/yyyy', { locale: ptBR })} até{' '}
            {format(watchedValues.end_date, 'dd/MM/yyyy', { locale: ptBR })}
          </p>
        </div>
        {isPartialDay && (
          <div>
            <Label className="text-muted-foreground">Horário</Label>
            <p className="font-medium">
              {watchedValues.start_time} - {watchedValues.end_time}
            </p>
          </div>
        )}
      </div>
      
      <div>
        <Label className="text-muted-foreground">Motivo</Label>
        <p className="font-medium">{watchedValues.reason}</p>
      </div>
      
      {watchedValues.justification && (
        <div>
          <Label className="text-muted-foreground">Justificativa</Label>
          <p className="font-medium">{watchedValues.justification}</p>
        </div>
      )}
      
      <div className="flex gap-2 pt-2">
        <Button
          type="button"
          variant="outline"
          onClick={() => setShowPreview(false)}
          className="flex-1"
        >
          Voltar
        </Button>
        <Button
          type="submit"
          disabled={createAbsenceMutation.isPending}
          className="flex-1"
        >
          {createAbsenceMutation.isPending && (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          )}
          Confirmar Registro
        </Button>
      </div>
    </motion.div>
  );

  // =====================================================
  // RENDER
  // =====================================================

  const defaultTrigger = (
    <Button className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white">
      <CalendarLucide className="h-4 w-4 mr-2" />
      Registrar Ausência
    </Button>
  );

  return (
    <TooltipProvider>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          {trigger || defaultTrigger}
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Registrar Ausência</DialogTitle>
            <DialogDescription>
              Formulário para registrar nova ausência
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(showPreview ? handleSubmit : handlePreview)} className="space-y-6">
              
              {/* Seleção de Usuário (se admin) */}
              {allowAdminMode && (
                <GenericPermissionGate
                  resourceTypeKey="user_absence"
                  actionKey="register_user_absences"
                  fallbackComponent={null}
                >
                  <FormField
                    control={form.control}
                    name="user_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Usuário</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Selecionar usuário" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {companyUsers.map((user) => (
                              <SelectItem key={user.id} value={user.id}>
                                {user.full_name} ({user.email})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Selecione o usuário para registrar a ausência
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </GenericPermissionGate>
              )}

              {/* Tipo de Ausência */}
              <FormField
                control={form.control}
                name="absence_type_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo de Ausência</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecionar tipo de ausência" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {loadingTypes ? (
                          <SelectItem value="" disabled>
                            <div className="flex items-center gap-2">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Carregando tipos...
                            </div>
                          </SelectItem>
                        ) : (
                          absenceTypes.map((type) => (
                            <SelectItem key={type.id} value={type.id}>
                              <div className="flex items-center justify-between w-full">
                                <span>{type.name}</span>
                                {type.requires_approval && (
                                  <Badge variant="outline" className="ml-2 text-xs">
                                    Aprovação
                                  </Badge>
                                )}
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    {selectedType && <TypeInfoBadge type={selectedType} />}
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Período da Ausência */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="start_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Data de Início</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "dd/MM/yyyy", { locale: ptBR })
                              ) : (
                                <span>Selecionar data</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 z-[1000000]" align="start" style={{ zIndex: 1000000 }}>
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => isBefore(date, new Date())}
                            initialFocus
                            className="clickable-calendar"
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="end_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Data de Fim</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "dd/MM/yyyy", { locale: ptBR })
                              ) : (
                                <span>Selecionar data</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 z-[1000000]" align="start" style={{ zIndex: 1000000 }}>
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => isBefore(date, watchedValues.start_date || new Date())}
                            initialFocus
                            className="clickable-calendar"
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Ausência Parcial */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="partial-day"
                  checked={isPartialDay}
                  onChange={(e) => setIsPartialDay(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="partial-day" className="text-sm font-medium">
                  Ausência parcial (com horários específicos)
                </Label>
              </div>

              {/* Horários (se parcial) */}
              {isPartialDay && (
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="start_time"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Horário de Início</FormLabel>
                        <FormControl>
                          <Input type="time" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="end_time"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Horário de Fim</FormLabel>
                        <FormControl>
                          <Input type="time" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {/* Motivo */}
              <FormField
                control={form.control}
                name="reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Motivo da Ausência</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Descreva o motivo da sua ausência..."
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Explique brevemente o motivo da ausência
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Justificativa (se obrigatória) */}
              {selectedType?.requires_justification && (
                <FormField
                  control={form.control}
                  name="justification"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Justificativa Obrigatória</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Forneça uma justificativa detalhada..."
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Este tipo de ausência requer justificativa detalhada
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Observações */}
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Observações (Opcional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Informações adicionais..."
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Validações e alertas */}
              {(hasConflicts() || !validateAdvanceNotice() || !validateDuration()) && (
                <div className="space-y-2 text-sm">
                  {hasConflicts() && (
                    <div className="flex items-center gap-2 text-red-600">
                      <AlertTriangle className="h-4 w-4" />
                      Conflito: Você já possui ausência neste período
                    </div>
                  )}
                  {!validateAdvanceNotice() && (
                    <div className="flex items-center gap-2 text-red-600">
                      <AlertTriangle className="h-4 w-4" />
                      Antecedência máxima excedida ({selectedType?.max_days_in_advance} dias)
                    </div>
                  )}
                  {!validateDuration() && (
                    <div className="flex items-center gap-2 text-red-600">
                      <AlertTriangle className="h-4 w-4" />
                      Duração máxima excedida ({selectedType?.max_duration_days} dias)
                    </div>
                  )}
                </div>
              )}

              {/* Preview ou botões */}
              {showPreview ? (
                <PreviewSection />
              ) : (
                <div className="flex gap-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setOpen(false)}
                    className="flex-1"
                  >
                    Cancelar
                  </Button>
                  <Button
                    type="submit"
                    disabled={
                      !form.formState.isValid || 
                      !validateAdvanceNotice() || 
                      !validateDuration() ||
                      hasConflicts()
                    }
                    className="flex-1"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Revisar e Confirmar
                  </Button>
                </div>
              )}
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  );
} 