/**
 * AbsenceTypesManager - Gerenciamento de Tipos de Ausência
 * 
 * Componente para gerenciar tipos de ausência da empresa:
 * - Visualizar tipos existentes
 * - Criar novos tipos
 * - Editar tipos existentes
 * - Ativar/desativar tipos
 * - Configurar regras específicas
 * 
 * <AUTHOR> Internet 2025
 */
import { useState } from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Plus,
  Settings,
  Eye,
  EyeOff,
  Edit3,
  Calendar,
  Clock,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  Palette,
  Shield,
  Users,
  FileText,
  Home,
  Plane,
  GraduationCap,
  Heart,
  Baby,
  Sun,
  Flower
} from "lucide-react";
import { AbsenceTypeDialog } from "@/components/people/AbsenceTypeDialog";
import { useAbsenceTypes } from "@/lib/query/hooks/useAbsenceTypes";
import { RefreshButton } from "@/components/ui/RefreshButton";

// =====================================================
// INTERFACES
// =====================================================

interface AbsenceType {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  requires_approval: boolean;
  allows_self_registration: boolean;
  requires_justification: boolean;
  max_days_in_advance: number;
  max_duration_days: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export function AbsenceTypesManager() {
  const [selectedType, setSelectedType] = useState<AbsenceType | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Hooks para dados
  const { data: absenceTypes = [], isLoading, refetch } = useAbsenceTypes();

  // Mapear ícones
  const iconMap: Record<string, React.ElementType> = {
    Calendar,
    Clock,
    Sun,
    Heart,
    Baby,
    Users,
    FileText,
    Flower,
    GraduationCap,
    Home,
    Plane,
    CheckCircle2,
    AlertTriangle,
    Shield,
    Palette,
    Settings,
    Edit3,
    Plus,
  };

  // Estatísticas
  const stats = {
    total: absenceTypes.length,
    active: absenceTypes.filter(t => t.is_active).length,
    inactive: absenceTypes.filter(t => !t.is_active).length,
    requiresApproval: absenceTypes.filter(t => t.requires_approval).length,
  };

  // Variantes de animação
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  // =====================================================
  // COMPONENTES AUXILIARES
  // =====================================================

  const StatCard = ({ 
    title, 
    value, 
    icon: Icon, 
    color, 
    description 
  }: { 
    title: string; 
    value: number; 
    icon: React.ElementType; 
    color: string; 
    description: string;
  }) => (
    <motion.div variants={cardVariants}>
      <Card className="border-0 bg-gradient-to-br from-white to-slate-50 shadow-md hover:shadow-lg transition-all">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">{title}</p>
              <p className="text-2xl font-bold">{value}</p>
              <p className="text-xs text-muted-foreground mt-1">{description}</p>
            </div>
            <div className={`p-2 rounded-full bg-gradient-to-r ${color}`}>
              <Icon className="h-5 w-5 text-white" />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );

  const AbsenceTypeCard = ({ type }: { type: AbsenceType }) => {
    const IconComponent = iconMap[type.icon] || Calendar;
    
    return (
      <motion.div variants={cardVariants}>
        <Card className="hover:shadow-md transition-all">
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3 flex-1">
                {/* Ícone e cor */}
                <div 
                  className="p-2 rounded-lg flex-shrink-0"
                  style={{ backgroundColor: `${type.color}20`, border: `1px solid ${type.color}40` }}
                >
                  <IconComponent 
                    className="h-5 w-5" 
                    style={{ color: type.color }}
                  />
                </div>

                {/* Informações principais */}
                <div className="space-y-2 flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold">{type.name}</h3>
                    <Badge 
                      variant={type.is_active ? "default" : "secondary"}
                      className={type.is_active ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-600"}
                    >
                      {type.is_active ? "Ativo" : "Inativo"}
                    </Badge>
                  </div>
                  
                  <p className="text-sm text-muted-foreground">{type.description}</p>
                  
                  {/* Configurações */}
                  <div className="flex flex-wrap gap-2">
                    {type.requires_approval && (
                      <Badge variant="outline" className="text-xs">
                        <CheckCircle2 className="h-3 w-3 mr-1" />
                        Requer Aprovação
                      </Badge>
                    )}
                    {!type.allows_self_registration && (
                      <Badge variant="outline" className="text-xs">
                        <Shield className="h-3 w-3 mr-1" />
                        Apenas Admin
                      </Badge>
                    )}
                    {type.requires_justification && (
                      <Badge variant="outline" className="text-xs">
                        <FileText className="h-3 w-3 mr-1" />
                        Requer Justificativa
                      </Badge>
                    )}
                  </div>

                  {/* Limites */}
                  <div className="text-xs text-muted-foreground space-y-1">
                    <div>Máximo: {type.max_duration_days} dias</div>
                    <div>Antecedência: {type.max_days_in_advance} dias</div>
                  </div>
                </div>
              </div>

              {/* Botões de ação */}
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setSelectedType(type);
                    setIsEditDialogOpen(true);
                  }}
                >
                  <Edit3 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  // =====================================================
  // RENDER
  // =====================================================

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="h-24 bg-gray-200 rounded-lg animate-pulse" />
        ))}
      </div>
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Header com estatísticas */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Tipos de Ausência</h2>
          <p className="text-sm text-muted-foreground">
            Configure os tipos de ausência disponíveis na empresa
          </p>
        </div>
        <div className="flex items-center gap-3">
          <RefreshButton
            variant="outline"
            size="sm"
            isLoading={isLoading}
            onRefresh={refetch}
            successMessage="Tipos atualizados!"
          />
          <Button
            onClick={() => setIsCreateDialogOpen(true)}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
          >
            <Plus className="h-4 w-4 mr-2" />
            Novo Tipo
          </Button>
        </div>
      </div>

      {/* Estatísticas rápidas */}
      <motion.div variants={containerVariants} className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total"
          value={stats.total}
          icon={Calendar}
          color="from-blue-500 to-indigo-500"
          description="Tipos cadastrados"
        />
        <StatCard
          title="Ativos"
          value={stats.active}
          icon={CheckCircle2}
          color="from-green-500 to-emerald-500"
          description="Disponíveis para uso"
        />
        <StatCard
          title="Inativos"
          value={stats.inactive}
          icon={XCircle}
          color="from-gray-500 to-slate-500"
          description="Desabilitados"
        />
        <StatCard
          title="Com Aprovação"
          value={stats.requiresApproval}
          icon={Shield}
          color="from-amber-500 to-yellow-500"
          description="Requerem aprovação"
        />
      </motion.div>

      {/* Lista de tipos */}
      <div className="space-y-4">
        {absenceTypes.length === 0 ? (
          <Card className="p-8 text-center">
            <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium mb-2">Nenhum tipo cadastrado</h3>
            <p className="text-muted-foreground mb-4">
              Configure os tipos de ausência que estarão disponíveis na empresa.
            </p>
            <Button
              onClick={() => setIsCreateDialogOpen(true)}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
            >
              <Plus className="h-4 w-4 mr-2" />
              Criar Primeiro Tipo
            </Button>
          </Card>
        ) : (
          <div className="space-y-3">
            {absenceTypes.map((type) => (
              <AbsenceTypeCard key={type.id} type={type} />
            ))}
          </div>
        )}
      </div>

      {/* Dialogs */}
      <AbsenceTypeDialog
        isOpen={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSuccess={() => {
          refetch();
          setIsCreateDialogOpen(false);
        }}
      />

      <AbsenceTypeDialog
        isOpen={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        absenceType={selectedType}
        onSuccess={() => {
          refetch();
          setIsEditDialogOpen(false);
          setSelectedType(null);
        }}
      />
    </motion.div>
  );
}