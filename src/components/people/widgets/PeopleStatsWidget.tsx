/**
 * PeopleStatsWidget - Widget de estatísticas e analytics de pessoas
 * <AUTHOR> Internet 2025
 */
import { motion } from "framer-motion";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AdvancedRefreshButton } from "@/components/ui/advanced-refresh-button";
import { Progress } from "@/components/ui/progress";
import {
  Users,
  TrendingUp,
  Calendar,
  MapPin,
  Award,
  Clock,
  UserPlus,
  Activity,
  Brain,
  Building2,
  Cake,
  Sparkles,

  BarChart3,
  Target,
  Zap,
  Crown
} from "lucide-react";

interface PeopleAnalytics {
  totalEmployees: number;
  activeToday: number;
  newHires: number;
  departments: number;
  avgTenure: number;
  birthdaysThisWeek: number;
  topSkills: { skill: string; count: number }[];
  locations: { location: string; count: number }[];
}

interface PeopleStatsWidgetProps {
  analytics: PeopleAnalytics;
  isLoading: boolean;
  onRefresh: () => void;
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  }
};

const statCardVariants = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

export function PeopleStatsWidget({ analytics, isLoading, onRefresh }: PeopleStatsWidgetProps) {
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  const activityRate = Math.round((analytics.activeToday / analytics.totalEmployees) * 100);
  const maxSkillCount = Math.max(...analytics.topSkills.map(s => s.count));
  const maxLocationCount = Math.max(...analytics.locations.map(l => l.count));

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <Card className="border-0 bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                >
                  <BarChart3 className="h-5 w-5 text-blue-500" />
                </motion.div>
                Analytics de Pessoas
                <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                  Atualizado hoje
                </Badge>
                <Badge variant="outline" className="bg-white/50">
                  Demo
                </Badge>
              </div>
              
              <AdvancedRefreshButton
                onRefresh={onRefresh}
                variant="outline"
                size="sm"
                className="hover:bg-blue-50 border-blue-200"
                operationName="Analytics de Pessoas"
                successMessage="Dados atualizados!"
                errorMessage="Erro ao atualizar dados"
                enableSound={true}
              >
                Atualizar
              </Button>
            </CardTitle>
          </CardHeader>
        </Card>
      </motion.div>

      {/* KPIs Principais */}
      <motion.div variants={itemVariants}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total de Funcionários */}
          <motion.div variants={statCardVariants} whileHover={{ scale: 1.02 }}>
            <Card className="border-0 bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg overflow-hidden">
              <CardContent className="p-6 relative">
                <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-4 translate-x-4" />
                <div className="relative">
                  <div className="flex items-center gap-2 mb-2">
                    <Users className="h-5 w-5" />
                    <span className="text-sm font-medium opacity-90">Total de Pessoas</span>
                  </div>
                  <div className="text-3xl font-bold">{analytics.totalEmployees}</div>
                  <div className="text-sm opacity-75">funcionários ativos</div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Atividade Hoje */}
          <motion.div variants={statCardVariants} whileHover={{ scale: 1.02 }}>
            <Card className="border-0 bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg overflow-hidden">
              <CardContent className="p-6 relative">
                <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-4 translate-x-4" />
                <div className="relative">
                  <div className="flex items-center gap-2 mb-2">
                    <Activity className="h-5 w-5" />
                    <span className="text-sm font-medium opacity-90">Online Hoje</span>
                  </div>
                  <div className="text-3xl font-bold">{analytics.activeToday}</div>
                  <div className="text-sm opacity-75">{activityRate}% de atividade</div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Novas Contratações */}
          <motion.div variants={statCardVariants} whileHover={{ scale: 1.02 }}>
            <Card className="border-0 bg-gradient-to-br from-purple-500 to-pink-600 text-white shadow-lg overflow-hidden">
              <CardContent className="p-6 relative">
                <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-4 translate-x-4" />
                <div className="relative">
                  <div className="flex items-center gap-2 mb-2">
                    <UserPlus className="h-5 w-5" />
                    <span className="text-sm font-medium opacity-90">Novos Contratados</span>
                  </div>
                  <div className="text-3xl font-bold">{analytics.newHires}</div>
                  <div className="text-sm opacity-75">este mês</div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Aniversários */}
          <motion.div variants={statCardVariants} whileHover={{ scale: 1.02 }}>
            <Card className="border-0 bg-gradient-to-br from-orange-500 to-red-600 text-white shadow-lg overflow-hidden">
              <CardContent className="p-6 relative">
                <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-4 translate-x-4" />
                <div className="relative">
                  <div className="flex items-center gap-2 mb-2">
                    <Cake className="h-5 w-5" />
                    <span className="text-sm font-medium opacity-90">Aniversários</span>
                  </div>
                  <div className="text-3xl font-bold">{analytics.birthdaysThisWeek}</div>
                  <div className="text-sm opacity-75">esta semana</div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </motion.div>

      {/* Informações Detalhadas */}
      <motion.div variants={itemVariants}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Skills */}
          <Card className="border-0 bg-gradient-to-br from-white to-indigo-50 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-indigo-500" />
                Habilidades Mais Comuns
                <Badge variant="outline" className="bg-indigo-100 text-indigo-700">
                  Top {analytics.topSkills.length}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {analytics.topSkills.map((skill, index) => (
                <motion.div
                  key={skill.skill}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="space-y-2"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${
                        index === 0 ? 'bg-yellow-500' :
                        index === 1 ? 'bg-gray-400' :
                        index === 2 ? 'bg-amber-600' : 'bg-blue-500'
                      }`} />
                      <span className="font-medium text-foreground">{skill.skill}</span>
                      {index < 3 && (
                        <div className="flex items-center">
                          {index === 0 && <Crown className="h-3 w-3 text-yellow-500" />}
                          {index === 1 && <Award className="h-3 w-3 text-gray-400" />}
                          {index === 2 && <Sparkles className="h-3 w-3 text-amber-600" />}
                        </div>
                      )}
                    </div>
                    <span className="text-sm font-bold text-foreground">{skill.count} pessoas</span>
                  </div>
                  <Progress 
                    value={(skill.count / maxSkillCount) * 100} 
                    className="h-2"
                  />
                </motion.div>
              ))}
            </CardContent>
          </Card>

          {/* Distribuição por Localização */}
          <Card className="border-0 bg-gradient-to-br from-white to-green-50 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-green-500" />
                Distribuição Geográfica
                <Badge variant="outline" className="bg-green-100 text-green-700">
                  {analytics.locations.length} locais
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {analytics.locations.map((location, index) => (
                <motion.div
                  key={location.location}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="space-y-2"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-green-500" />
                      <span className="font-medium text-foreground">{location.location}</span>
                    </div>
                    <span className="text-sm font-bold text-foreground">{location.count} pessoas</span>
                  </div>
                  <Progress 
                    value={(location.count / maxLocationCount) * 100} 
                    className="h-2"
                  />
                  <div className="text-xs text-muted-foreground">
                    {Math.round((location.count / analytics.totalEmployees) * 100)}% do total
                  </div>
                </motion.div>
              ))}
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* Resumo Executivo */}
      <motion.div variants={itemVariants}>
        <Card className="border-0 bg-gradient-to-r from-gray-50 to-slate-50 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <Target className="h-5 w-5 text-slate-500" />
              <h3 className="font-semibold text-foreground">Resumo Executivo</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">{analytics.departments}</div>
                <div className="text-sm text-muted-foreground">Departamentos</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">{analytics.avgTenure} anos</div>
                <div className="text-sm text-muted-foreground">Tenure Médio</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{activityRate}%</div>
                <div className="text-sm text-muted-foreground">Taxa de Atividade</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {Math.round((analytics.newHires / analytics.totalEmployees) * 100)}%
                </div>
                <div className="text-sm text-muted-foreground">Growth Rate</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
} 