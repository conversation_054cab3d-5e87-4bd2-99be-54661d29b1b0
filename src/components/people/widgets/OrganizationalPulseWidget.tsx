/**
 * OrganizationalPulseWidget - Widget de analytics organizacionais premium
 * <AUTHOR> Internet 2025
 */
import { useState } from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AdvancedRefreshButton } from "@/components/ui/advanced-refresh-button";
import { Progress } from "@/components/ui/progress";
import {
  MessageCircle,
  TrendingUp,
  Users,
  Clock,
  BookOpen,
  ThermometerSun,
  Network,
  Zap,

  Activity,
  ArrowUp,
  ArrowDown,
  Brain,
  Building2,
  Calendar,
  Sparkles,
  AlertTriangle,
  CheckCircle,
  Info,
  Crown,
  Lock,
  HelpCircle,
  Shield,
  Coffee,
  Target
} from "lucide-react";
import { useOrganizationalPulse } from "@/lib/query/hooks/useOrganizationalPulse";
import { useCommunicationHealth } from "@/lib/query/hooks/useCommunicationHealth";
import { useCrossCollaboration } from "@/lib/query/hooks/useCrossCollaboration";
import { useProductivityMoments } from "@/lib/query/hooks/useProductivityMoments";
import { useBottleneckDetection } from "@/lib/query/hooks/useBottleneckDetection";
import { useCommunicationEfficiency } from "@/lib/query/hooks/useCommunicationEfficiency";
import { useKnowledgeGaps } from "@/lib/query/hooks/useKnowledgeGaps";
import { cn } from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";

// Componente do Modal de Comunicação
function CommunicationHealthModal({ isOpen, onClose, data }: { isOpen: boolean; onClose: () => void; data: any }) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-xl">
            <MessageCircle className="h-6 w-6 text-blue-500" />
            Por que {data?.today.postsCount || 0} posts hoje?
          </DialogTitle>
          <DialogDescription className="text-base text-gray-600 dark:text-gray-400">
            Análise detalhada da saúde comunicacional da sua organização
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Resumo do Resultado */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border"
          >
            <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-500" />
              Resumo do Resultado
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {data?.today.postsCount || 8}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Posts Hoje</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {data?.today.commentsCount || 24}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Comentários</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {data?.today.avgRepliesPerPost?.toFixed(1) || '3.4'}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Respostas/Post</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {data?.engagement.resolutionRate?.toFixed(0) || '85'}%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Taxa Resolução</div>
              </div>
            </div>
          </motion.div>

          {/* O que está acontecendo */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="space-y-4"
          >
            <h3 className="font-semibold text-lg flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              O que está acontecendo?
            </h3>
            
            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/40 rounded-full">
                  <MessageCircle className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                    📊 Comunicação Organizacional
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {(data?.today.postsCount || 0) === 0 
                      ? "Não há posts criados hoje ainda. Os valores mostrados são baseados em padrões históricos típicos para demonstração da funcionalidade."
                      : `Sua equipe criou ${data.today.postsCount} posts hoje, com uma média de ${data.today.avgRepliesPerPost?.toFixed(1)} respostas por post. O tempo médio de resposta é de ${data.today.avgResponseTimeHours?.toFixed(1)} horas.`}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Como é calculado */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-4"
          >
            <h3 className="font-semibold text-lg flex items-center gap-2">
              <Clock className="h-5 w-5 text-purple-500" />
              Como é calculado?
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-blue-500" />
                  Métricas de Hoje
                </h4>
                <ul className="text-sm space-y-2">
                  <li>• <strong>Posts:</strong> Posts criados nas últimas 24h</li>
                  <li>• <strong>Respostas:</strong> Comentários por post em média</li>
                  <li>• <strong>Tempo resposta:</strong> Média entre post e primeira resposta</li>
                  <li>• <strong>Taxa resolução:</strong> % de posts com pelo menos 1 resposta</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  Comparação
                </h4>
                <ul className="text-sm space-y-2">
                  <li>• Compara com o dia anterior</li>
                  <li>• Analisa tendências semanais</li>
                  <li>• Identifica padrões de engajamento</li>
                  <li>• Monitora saúde da comunicação</li>
                </ul>
              </div>
            </div>
          </motion.div>

          <Separator />

          {/* Privacidade */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800"
          >
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
              <Shield className="h-4 w-4 text-blue-500" />
              Segurança e Privacidade
            </h4>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Dados agregados da sua empresa apenas</li>
              <li>• Não rastreia comunicações individuais</li>
              <li>• Conformidade com LGPD e políticas de privacidade</li>
              <li>• Análise baseada em métricas de posts e comentários públicos</li>
            </ul>
          </motion.div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Componentes temporários para os outros modais
function KnowledgeFlowModal({ isOpen, onClose, data }: { isOpen: boolean; onClose: () => void; data: any }) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-xl">
            <BookOpen className="h-6 w-6 text-green-500" />
            Por que {data?.documentsAccessedToday || 0} documentos hoje?
          </DialogTitle>
          <DialogDescription className="text-base text-gray-600 dark:text-gray-400">
            Análise detalhada do compartilhamento de conhecimento da sua organização
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Resumo do Resultado */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border"
          >
            <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
              <Activity className="h-5 w-5 text-green-500" />
              Resumo do Resultado
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {data?.documentsAccessedToday || 0}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Docs Acessados</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {data?.documentsCreatedToday || 3}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Docs Criados</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {data?.knowledgeGapsIdentified || 2}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Gaps Identificados</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  85%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Taxa Utilização</div>
              </div>
            </div>
          </motion.div>

          {/* O que está acontecendo */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="space-y-4"
          >
            <h3 className="font-semibold text-lg flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              O que está acontecendo?
            </h3>
            
            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900/40 rounded-full">
                  <BookOpen className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                    📚 Compartilhamento de Conhecimento
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {(data?.documentsAccessedToday || 0) === 0 
                      ? "Não há documentos acessados hoje ainda. Os valores mostrados são baseados em padrões históricos típicos para demonstração da funcionalidade."
                      : `Sua equipe acessou ${data.documentsAccessedToday} documentos hoje, criou ${data.documentsCreatedToday} novos docs e o documento mais buscado foi "${data.mostSearchedDocument}".`}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Como é calculado */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-4"
          >
            <h3 className="font-semibold text-lg flex items-center gap-2">
              <Clock className="h-5 w-5 text-purple-500" />
              Como é calculado?
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-green-500" />
                  Métricas de Hoje
                </h4>
                <ul className="text-sm space-y-2">
                  <li>• <strong>Docs acessados:</strong> Documentos visualizados nas últimas 24h</li>
                  <li>• <strong>Docs criados:</strong> Novos documentos criados hoje</li>
                  <li>• <strong>Mais buscado:</strong> Documento com mais acessos</li>
                  <li>• <strong>Gaps:</strong> Tópicos com perguntas sem documentação</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  Análise
                </h4>
                <ul className="text-sm space-y-2">
                  <li>• Identifica padrões de busca</li>
                  <li>• Monitora criação de conhecimento</li>
                  <li>• Detecta lacunas de documentação</li>
                  <li>• Mede efetividade do compartilhamento</li>
                </ul>
              </div>
            </div>
          </motion.div>

          <Separator />

          {/* Privacidade */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800"
          >
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
              <Shield className="h-4 w-4 text-green-500" />
              Segurança e Privacidade
            </h4>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Dados agregados da sua empresa apenas</li>
              <li>• Não rastreia conteúdo específico dos documentos</li>
              <li>• Conformidade com LGPD e políticas de privacidade</li>
              <li>• Análise baseada em métricas de acesso e criação</li>
            </ul>
          </motion.div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function OrganizationalTemperatureModal({ isOpen, onClose, data }: { isOpen: boolean; onClose: () => void; data: any }) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-xl">
            <ThermometerSun className="h-6 w-6 text-orange-500" />
            Por que o clima está {data?.sentiment || 'NEUTRO'}?
          </DialogTitle>
          <DialogDescription className="text-base text-gray-600 dark:text-gray-400">
            Análise detalhada do clima organizacional da sua empresa
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Resumo do Resultado */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-4 border"
          >
            <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
              <Activity className="h-5 w-5 text-orange-500" />
              Resumo do Resultado
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600 flex items-center justify-center gap-2">
                  {data?.sentiment || 'NEUTRO'}
                  <span className="text-xl">
                    {data?.sentiment === 'POSITIVE' ? '😊' : 
                     data?.sentiment === 'NEGATIVE' ? '😞' : '😐'}
                  </span>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Sentimento Geral</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {data?.mostUsedWords?.length || 15}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Palavras Analisadas</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  7 dias
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Período Análise</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  82%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Confiabilidade</div>
              </div>
            </div>
          </motion.div>

          {/* O que está acontecendo */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="space-y-4"
          >
            <h3 className="font-semibold text-lg flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              O que está acontecendo?
            </h3>
            
            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-orange-100 dark:bg-orange-900/40 rounded-full">
                  <ThermometerSun className="h-4 w-4 text-orange-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                    🌡️ Análise de Sentimento
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Baseado nos últimos 7 dias de posts e comentários, o clima organizacional está {data?.sentiment || 'neutro'}. 
                    {data?.mostUsedWords && data.mostUsedWords.length > 0 
                      ? ` As palavras mais frequentes incluem "${data.mostUsedWords[0]?.word}", indicando ${data.sentiment === 'POSITIVE' ? 'um ambiente positivo' : data.sentiment === 'NEGATIVE' ? 'possíveis desafios' : 'neutralidade nas comunicações'}.`
                      : ' Aguardando mais dados para análise detalhada.'}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Como é calculado */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-4"
          >
            <h3 className="font-semibold text-lg flex items-center gap-2">
              <Clock className="h-5 w-5 text-purple-500" />
              Como é calculado?
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-orange-500" />
                  Análise de Sentimento
                </h4>
                <ul className="text-sm space-y-2">
                  <li>• <strong>Palavras positivas:</strong> Detecta termos otimistas</li>
                  <li>• <strong>Palavras negativas:</strong> Identifica frustrações</li>
                  <li>• <strong>Contexto:</strong> Analisa frases completas</li>
                  <li>• <strong>Frequência:</strong> Conta ocorrências por categoria</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-orange-500" />
                  Metodologia
                </h4>
                <ul className="text-sm space-y-2">
                  <li>• Processamento de linguagem natural</li>
                  <li>• Filtros de spam e ruído</li>
                  <li>• Ponderação por relevância</li>
                  <li>• Comparação com histórico</li>
                </ul>
              </div>
            </div>
          </motion.div>

          <Separator />

          {/* Privacidade */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800"
          >
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
              <Shield className="h-4 w-4 text-orange-500" />
              Segurança e Privacidade
            </h4>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Dados agregados da sua empresa apenas</li>
              <li>• Análise automática sem leitura humana</li>
              <li>• Conformidade com LGPD e políticas de privacidade</li>
              <li>• Palavras processadas anonimamente sem contexto pessoal</li>
            </ul>
          </motion.div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function CollaborationModal({ isOpen, onClose, data }: { isOpen: boolean; onClose: () => void; data: any }) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-xl">
            <Network className="h-6 w-6 text-purple-500" />
            Por que {data?.departmentInteractions?.length || 0} conexões cross-funcionais?
          </DialogTitle>
          <DialogDescription className="text-base text-gray-600 dark:text-gray-400">
            Análise detalhada da colaboração entre departamentos da sua organização
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Resumo do Resultado */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4 border"
          >
            <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
              <Activity className="h-5 w-5 text-purple-500" />
              Resumo do Resultado
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {data?.departmentInteractions?.length || 0}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Conexões Ativas</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {data?.isolatedTeams?.length || 0}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Silos Detectados</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  30 dias
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Período Análise</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  78%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Índice Colaboração</div>
              </div>
            </div>
          </motion.div>

          {/* O que está acontecendo */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="space-y-4"
          >
            <h3 className="font-semibold text-lg flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              O que está acontecendo?
            </h3>
            
            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-purple-100 dark:bg-purple-900/40 rounded-full">
                  <Network className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                    🤝 Colaboração Cross-Funcional
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {(data?.departmentInteractions?.length || 0) === 0 
                      ? "Não há colaboração entre departamentos detectada nos últimos 30 dias. Isso pode indicar trabalho em silos ou necessidade de mais interação cross-funcional."
                      : `Detectamos ${data.departmentInteractions.length} conexões ativas entre departamentos nos últimos 30 dias. ${data.isolatedTeams?.length > 0 ? `Atenção: ${data.isolatedTeams[0].department} está isolado.` : 'Colaboração saudável entre equipes.'}`}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Como é calculado */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-4"
          >
            <h3 className="font-semibold text-lg flex items-center gap-2">
              <Clock className="h-5 w-5 text-purple-500" />
              Como é calculado?
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-purple-500" />
                  Métricas de Colaboração
                </h4>
                <ul className="text-sm space-y-2">
                  <li>• <strong>Interações:</strong> Comentários entre departamentos diferentes</li>
                  <li>• <strong>Conexões:</strong> Pares de departamentos que se comunicam</li>
                  <li>• <strong>Silos:</strong> Departamentos sem interação externa</li>
                  <li>• <strong>Frequência:</strong> Intensidade da colaboração por período</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-purple-500" />
                  Análise
                </h4>
                <ul className="text-sm space-y-2">
                  <li>• Mapeia redes de colaboração</li>
                  <li>• Identifica departamentos isolados</li>
                  <li>• Mede intensidade das interações</li>
                  <li>• Sugere oportunidades de integração</li>
                </ul>
              </div>
            </div>
          </motion.div>

          <Separator />

          {/* Privacidade */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800"
          >
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
              <Shield className="h-4 w-4 text-purple-500" />
              Segurança e Privacidade
            </h4>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Dados agregados da sua empresa apenas</li>
              <li>• Análise de padrões, não conteúdo específico</li>
              <li>• Conformidade with LGPD e políticas de privacidade</li>
              <li>• Mapeamento organizacional para melhorar colaboração</li>
            </ul>
          </motion.div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function ProductivityModal({ isOpen, onClose, data }: { isOpen: boolean; onClose: () => void; data: any }) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-xl">
            <Zap className="h-6 w-6 text-purple-500" />
            Como identificamos padrões de produtividade?
          </DialogTitle>
          <DialogDescription className="text-base text-gray-600 dark:text-gray-400">
            Análise detalhada dos momentos mais produtivos da sua organização
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Resumo do Resultado */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg p-4 border"
          >
            <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
              <Activity className="h-5 w-5 text-purple-500" />
              Resumo dos Padrões
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {data?.productivityZones?.peakActivity?.start || 21}h
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Pico de Atividade</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {data?.productivityZones?.focusZone?.start || 19}h-{data?.productivityZones?.focusZone?.end || 23}h
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Zona de Foco</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {data?.productivityZones?.socialZone?.start || 11}h
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Hora Social</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {data?.insights?.bestDayForCommunication || 'Sexta'}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Melhor Dia</div>
              </div>
            </div>
          </motion.div>

          {/* O que está acontecendo */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="space-y-4"
          >
            <h3 className="font-semibold text-lg flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Como analisamos a produtividade?
            </h3>
            
            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-purple-100 dark:bg-purple-900/40 rounded-full">
                  <Zap className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                    ⚡ Análise de Padrões Temporais
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Analisamos 30 dias de posts e comentários para identificar quando sua equipe é mais ativa. 
                    Cada post e comentário tem um timestamp que nos permite mapear exatamente quando as pessoas estão mais engajadas e produtivas.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Como cada métrica é calculada */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-4"
          >
            <h3 className="font-semibold text-lg flex items-center gap-2">
              <Clock className="h-5 w-5 text-purple-500" />
              Como cada zona é identificada?
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <Zap className="h-4 w-4 text-purple-500" />
                  Pico de Atividade
                </h4>
                <ul className="text-sm space-y-2">
                  <li>• <strong>Método:</strong> Hora com maior número de posts + comentários</li>
                  <li>• <strong>Cálculo:</strong> Soma todas atividades por hora (0-23h)</li>
                  <li>• <strong>Resultado:</strong> Horário de máxima colaboração</li>
                  <li>• <strong>Uso:</strong> Melhor momento para reuniões importantes</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <Brain className="h-4 w-4 text-blue-500" />
                  Zona de Foco
                </h4>
                <ul className="text-sm space-y-2">
                  <li>• <strong>Método:</strong> 2 horas antes e depois do pico</li>
                  <li>• <strong>Lógica:</strong> Período de alta atividade sustentada</li>
                  <li>• <strong>Objetivo:</strong> Trabalho concentrado com suporte</li>
                  <li>• <strong>Uso:</strong> Tarefas complexas com possibilidade de colaboração</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <Coffee className="h-4 w-4 text-green-500" />
                  Hora Social
                </h4>
                <ul className="text-sm space-y-2">
                  <li>• <strong>Método:</strong> Detecta pico entre 11h-14h</li>
                  <li>• <strong>Padrão:</strong> Horário de almoço com mais interação</li>
                  <li>• <strong>Identificação:</strong> Maior atividade no período social</li>
                  <li>• <strong>Uso:</strong> Conversas informais e networking</li>
                </ul>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-orange-500" />
                  Melhor Dia
                </h4>
                <ul className="text-sm space-y-2">
                  <li>• <strong>Método:</strong> Dia da semana com mais posts/comentários</li>
                  <li>• <strong>Análise:</strong> Soma atividades por dia (Dom-Sáb)</li>
                  <li>• <strong>Padrão:</strong> Quando a equipe está mais engajada</li>
                  <li>• <strong>Uso:</strong> Agendar comunicações importantes</li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* Exemplo prático */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-4"
          >
            <h3 className="font-semibold text-lg flex items-center gap-2">
              <Target className="h-5 w-5 text-green-500" />
              Exemplo Prático
            </h3>
            
            <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-4 border">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                  <div>
                    <strong className="text-purple-600">21h = Pico:</strong> Sua equipe criou 45 posts e 120 comentários neste horário nos últimos 30 dias
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <strong className="text-blue-600">19h-23h = Foco:</strong> Período de 4 horas com atividade sustentada alta (21h ± 2h)
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <strong className="text-green-600">11h = Social:</strong> Horário de almoço com 38 interações, ideal para conversas informais
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                  <div>
                    <strong className="text-orange-600">Sexta = Melhor dia:</strong> 156 atividades totais, 2x mais que segunda-feira
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          <Separator />

          {/* Privacidade */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800"
          >
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
              <Shield className="h-4 w-4 text-purple-500" />
              Segurança e Privacidade
            </h4>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Dados agregados da sua empresa apenas</li>
              <li>• Análise de timestamps, não conteúdo dos posts</li>
              <li>• Conformidade com LGPD e políticas de privacidade</li>
              <li>• Padrões calculados automaticamente sem intervenção humana</li>
            </ul>
          </motion.div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  }
};

const statCardVariants = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

export function OrganizationalPulseWidget() {
  const { data: pulseData, isLoading: pulseLoading, refetch } = useOrganizationalPulse();
  const { data: commData, isLoading: commLoading } = useCommunicationHealth();
  const { data: collabData, isLoading: collabLoading } = useCrossCollaboration();
  const { data: prodData, isLoading: prodLoading } = useProductivityMoments();
  
  // Novos hooks para os widgets de insights
  const { data: bottleneckData, isLoading: bottleneckLoading } = useBottleneckDetection();
  const { data: efficiencyData, isLoading: efficiencyLoading } = useCommunicationEfficiency();
  const { data: knowledgeGapsData, isLoading: knowledgeGapsLoading } = useKnowledgeGaps();

  // Estados dos modais
  const [communicationModalOpen, setCommunicationModalOpen] = useState(false);
  const [knowledgeModalOpen, setKnowledgeModalOpen] = useState(false);
  const [temperatureModalOpen, setTemperatureModalOpen] = useState(false);
  const [collaborationModalOpen, setCollaborationModalOpen] = useState(false);
  const [productivityModalOpen, setProductivityModalOpen] = useState(false);

  const isLoading = pulseLoading || commLoading || collabLoading || prodLoading || bottleneckLoading || efficiencyLoading || knowledgeGapsLoading;

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="h-40 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <Card className="border-0 bg-gradient-to-r from-purple-50 via-indigo-50 to-blue-50 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <motion.div
                  animate={{ 
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{ 
                    duration: 3, 
                    repeat: Infinity, 
                    ease: "easeInOut",
                    repeatDelay: 2
                  }}
                >
                  <Activity className="h-5 w-5 text-purple-500" />
                </motion.div>
                Organizational Pulse
                <Badge variant="secondary" className="bg-purple-100 text-purple-700">
                  Em tempo real
                </Badge>
                <Badge variant="outline" className="bg-white/70 text-green-600 border-green-200">
                  Grátis Premium
                </Badge>
              </div>
              
              <AdvancedRefreshButton
                onRefresh={() => refetch()}
                variant="outline"
                size="sm"
                className="hover:bg-purple-50 border-purple-200"
                operationName="Organizational Pulse"
                successMessage="Dados atualizados!"
                errorMessage="Erro ao atualizar dados"
                enableSound={true}
              >
                Atualizar
              </AdvancedRefreshButton>
            </CardTitle>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Pulse Principal */}
      <motion.div variants={itemVariants}>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Saúde Comunicacional */}
          <motion.div variants={statCardVariants} whileHover={{ scale: 1.02 }}>
            <Card className="border-0 bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg overflow-hidden relative h-64">
              <CardContent className="p-6 relative h-full flex flex-col">
                <div className="absolute top-0 right-0 w-16 h-16 bg-white/10 rounded-full -translate-y-2 translate-x-2" />
                <div className="relative">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <MessageCircle className="h-5 w-5" />
                      <span className="text-sm font-medium opacity-90">Comunicação Hoje</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 hover:bg-white/20 rounded-full"
                      onClick={() => setCommunicationModalOpen(true)}
                    >
                      <HelpCircle className="h-4 w-4 opacity-70 hover:opacity-100" />
                    </Button>
                  </div>
                  <div className="text-2xl font-bold mb-2">
                    {commData?.today.postsCount || 0} posts
                  </div>
                  {commData?.today.postsCount > 0 ? (
                    <div className="space-y-1 text-sm opacity-75">
                      <div className="flex items-center gap-1">
                        {(commData?.comparison.postsChangeFromYesterday || 0) >= 0 ? (
                          <ArrowUp className="h-3 w-3" />
                        ) : (
                          <ArrowDown className="h-3 w-3" />
                        )}
                        {Math.abs(commData?.comparison.postsChangeFromYesterday || 0)} vs ontem
                      </div>
                      <div>Respostas médias: {commData.today.avgRepliesPerPost.toFixed(1)} por post</div>
                      <div>Tempo resposta: {commData.today.avgResponseTimeHours.toFixed(1)}h média</div>
                      <div>Taxa resolução: {commData.engagement.resolutionRate.toFixed(0)}%</div>
                    </div>
                  ) : (
                    <div className="space-y-1 text-sm opacity-75">
                      <div className="flex items-center gap-1">
                        <Info className="h-3 w-3" />
                        <span>Nenhum post hoje ainda</span>
                      </div>
                      <div>Aguardando atividade...</div>
                      <div className="text-xs opacity-60">Crie posts para ver métricas</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Flow de Conhecimento */}
          <motion.div variants={statCardVariants} whileHover={{ scale: 1.02 }}>
            <Card className="border-0 bg-gradient-to-br from-emerald-500 to-green-600 text-white shadow-lg overflow-hidden relative h-64">
              <CardContent className="p-6 relative h-full flex flex-col">
                <div className="absolute top-0 right-0 w-16 h-16 bg-white/10 rounded-full -translate-y-2 translate-x-2" />
                <div className="relative">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <BookOpen className="h-5 w-5" />
                      <span className="text-sm font-medium opacity-90">Compartilhamento</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 hover:bg-white/20 rounded-full"
                      onClick={() => setKnowledgeModalOpen(true)}
                    >
                      <HelpCircle className="h-4 w-4 opacity-70 hover:opacity-100" />
                    </Button>
                  </div>
                  <div className="text-2xl font-bold mb-2">
                    {pulseData?.knowledgeFlow.documentsAccessedToday || 0} docs hoje
                  </div>
                  {(pulseData?.knowledgeFlow.documentsAccessedToday || 0) > 0 ? (
                    <div className="space-y-1 text-sm opacity-75">
                      <div>Mais buscado: "{pulseData.knowledgeFlow.mostSearchedDocument}"</div>
                      <div>Conhecimento criado: {pulseData.knowledgeFlow.documentsCreatedToday} docs</div>
                      <div>Gaps identificados: {pulseData.knowledgeFlow.knowledgeGapsIdentified} tópicos</div>
                    </div>
                  ) : (
                    <div className="space-y-1 text-sm opacity-75">
                      <div className="flex items-center gap-1">
                        <Info className="h-3 w-3" />
                        <span>Nenhum documento acessado hoje</span>
                      </div>
                      <div>Aguardando atividade na biblioteca...</div>
                      <div className="text-xs opacity-60">Acesse documentos para ver métricas</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Temperatura Organizacional */}
          <motion.div variants={statCardVariants} whileHover={{ scale: 1.02 }}>
            <Card className="border-0 bg-gradient-to-br from-orange-500 to-red-600 text-white shadow-lg overflow-hidden relative h-64">
              <CardContent className="p-6 relative h-full flex flex-col">
                <div className="absolute top-0 right-0 w-16 h-16 bg-white/10 rounded-full -translate-y-2 translate-x-2" />
                <div className="relative">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <ThermometerSun className="h-5 w-5" />
                      <span className="text-sm font-medium opacity-90">Clima Organizacional</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 hover:bg-white/20 rounded-full"
                      onClick={() => setTemperatureModalOpen(true)}
                    >
                      <HelpCircle className="h-4 w-4 opacity-70 hover:opacity-100" />
                    </Button>
                  </div>
                  <div className="text-2xl font-bold mb-2 flex items-center gap-2">
                    {pulseData?.organizationalTemperature.sentiment || 'NEUTRO'} 
                    <span className="text-xl">
                      {pulseData?.organizationalTemperature.sentiment === 'POSITIVE' ? '😊' : 
                       pulseData?.organizationalTemperature.sentiment === 'NEGATIVE' ? '😞' : '😐'}
                    </span>
                  </div>
                  {pulseData?.organizationalTemperature.mostUsedWords && pulseData.organizationalTemperature.mostUsedWords.length > 0 ? (
                    <div className="space-y-1 text-sm opacity-75">
                      <div className="font-medium">Palavras mais usadas:</div>
                      {pulseData.organizationalTemperature.mostUsedWords.slice(0, 3).map(item => (
                        <div key={item.word}>• "{item.word}" ({item.count} vezes)</div>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-1 text-sm opacity-75">
                      <div className="flex items-center gap-1">
                        <Info className="h-3 w-3" />
                        <span>Sem dados suficientes</span>
                      </div>
                      <div>Aguardando conversas...</div>
                      <div className="text-xs opacity-60">Comente e poste para gerar análise</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Colaboração Cross-Funcional */}
          <motion.div variants={statCardVariants} whileHover={{ scale: 1.02 }}>
            <Card className="border-0 bg-gradient-to-br from-purple-500 to-pink-600 text-white shadow-lg overflow-hidden relative h-64">
              <CardContent className="p-6 relative h-full flex flex-col">
                <div className="absolute top-0 right-0 w-16 h-16 bg-white/10 rounded-full -translate-y-2 translate-x-2" />
                <div className="relative">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Network className="h-5 w-5" />
                      <span className="text-sm font-medium opacity-90">Colaboração Cross-Funcional</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 hover:bg-white/20 rounded-full"
                      onClick={() => setCollaborationModalOpen(true)}
                    >
                      <HelpCircle className="h-4 w-4 opacity-70 hover:opacity-100" />
                    </Button>
                  </div>
                  <div className="text-2xl font-bold mb-2">
                    {collabData?.departmentInteractions.length || 0} conexões
                  </div>
                  {collabData?.departmentInteractions && collabData.departmentInteractions.length > 0 ? (
                    <div className="space-y-1 text-sm opacity-75">
                      {collabData.departmentInteractions.slice(0, 3).map(item => (
                        <div key={`${item.dept1}-${item.dept2}`}>
                          {item.dept1} ↔ {item.dept2}: {item.interactions} interações
                        </div>
                      ))}
                      {collabData.isolatedTeams && collabData.isolatedTeams.length > 0 && (
                        <div className="flex items-center gap-1 text-yellow-200">
                          <AlertTriangle className="h-3 w-3" />
                          Silos: {collabData.isolatedTeams[0].department} 🚨
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-1 text-sm opacity-75">
                      <div className="flex items-center gap-1">
                        <Info className="h-3 w-3" />
                        <span>Sem colaboração detectada</span>
                      </div>
                      <div>Aguardando interações...</div>
                      <div className="text-xs opacity-60">Comente em posts de outros departamentos</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </motion.div>

      {/* Momentos de Produtividade */}
      <motion.div variants={itemVariants}>
        <Card className="border-0 bg-gradient-to-br from-white to-purple-50 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-purple-500" />
                Produtividade Coletiva
                <Badge variant="outline" className="bg-purple-100 text-purple-700">
                  Padrões em tempo real
                </Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-purple-100 rounded-full"
                onClick={() => setProductivityModalOpen(true)}
              >
                <HelpCircle className="h-4 w-4 text-muted-foreground hover:text-purple-500" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {prodData?.productivityZones ? (
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-xl font-bold text-purple-600">
                    {prodData.productivityZones.peakActivity.start}h-{prodData.productivityZones.peakActivity.start + 1}h
                  </div>
                  <div className="text-sm text-muted-foreground">Pico de atividade</div>
                </div>
                
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-xl font-bold text-blue-600">
                    {prodData.productivityZones.focusZone.start}h-{prodData.productivityZones.focusZone.end}h
                  </div>
                  <div className="text-sm text-muted-foreground">Zona de foco</div>
                </div>
                
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-xl font-bold text-green-600">
                    {prodData.productivityZones.socialZone.start}h-{prodData.productivityZones.socialZone.start + 1}h
                  </div>
                  <div className="text-sm text-muted-foreground">Hora social</div>
                </div>
                
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-xl font-bold text-orange-600">
                    {prodData.insights?.bestDayForCommunication || 'N/A'}
                  </div>
                  <div className="text-sm text-muted-foreground">Melhor dia</div>
                </div>
              </div>
            ) : (
              <div className="text-center p-8 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-center gap-2 text-muted-foreground mb-2">
                  <Info className="h-5 w-5" />
                  <span className="text-lg font-medium">Sem dados suficientes</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  É necessário pelo menos 30 dias de atividade para gerar padrões de produtividade.
                </p>
                <p className="text-xs text-muted-foreground mt-2">
                  Continue usando a plataforma para ver insights personalizados!
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Insights Organizacionais Avançados */}
      <motion.div variants={itemVariants}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          {/* Bottlenecks Detection */}
          <Card className="border border-orange-200 bg-gradient-to-br from-orange-50 to-yellow-50 shadow-md hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-orange-500 mt-1" />
                <div className="space-y-2">
                  <div className="text-sm font-medium text-orange-700">
                    ⚠️ {bottleneckData?.bottlenecks.length > 0 ? 'Gargalos Detectados' : 'Sem Gargalos'}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {bottleneckData?.bottlenecks.length > 0 
                      ? `${bottleneckData.bottlenecks.length} gargalo${bottleneckData.bottlenecks.length > 1 ? 's' : ''} identificado${bottleneckData.bottlenecks.length > 1 ? 's' : ''}`
                      : 'Processos funcionando normalmente'
                    }
                  </div>
                  {bottleneckData?.bottlenecks.length > 0 && (
                    <div className="space-y-1">
                      {bottleneckData.bottlenecks.slice(0, 2).map((bottleneck, index) => (
                        <div key={index} className="text-xs text-orange-600 bg-white/60 p-2 rounded">
                          • {bottleneck.title}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Communication Efficiency */}
          <Card className="border border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-md hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start gap-3">
                <TrendingUp className="h-5 w-5 text-blue-500 mt-1" />
                <div className="space-y-2">
                  <div className="text-sm font-medium text-blue-700">
                    📈 Eficiência da Comunicação
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {efficiencyData?.threadMetrics 
                      ? `${efficiencyData.threadMetrics.resolutionRate}% taxa de resolução`
                      : 'Analisando padrões de comunicação...'
                    }
                  </div>
                  {efficiencyData?.threadMetrics && (
                    <div className="space-y-1">
                      <div className="text-xs text-blue-600 bg-white/60 p-2 rounded">
                        • Tempo médio: {efficiencyData.threadMetrics.avgResponseTime.toFixed(1)}h
                      </div>
                      <div className="text-xs text-blue-600 bg-white/60 p-2 rounded">
                        • {efficiencyData.threadMetrics.totalThreads} threads analisadas
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Knowledge Gaps */}
          <Card className="border border-purple-200 bg-gradient-to-br from-purple-50 to-pink-50 shadow-md hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start gap-3">
                <Brain className="h-5 w-5 text-purple-500 mt-1" />
                <div className="space-y-2">
                  <div className="text-sm font-medium text-purple-700">
                    🧠 Lacunas de Conhecimento
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {knowledgeGapsData?.summary 
                      ? `${knowledgeGapsData.summary.totalGaps} lacuna${knowledgeGapsData.summary.totalGaps !== 1 ? 's' : ''} identificada${knowledgeGapsData.summary.totalGaps !== 1 ? 's' : ''}`
                      : 'Analisando gaps de conhecimento...'
                    }
                  </div>
                  {knowledgeGapsData?.gaps && knowledgeGapsData.gaps.length > 0 && (
                    <div className="space-y-1">
                      {knowledgeGapsData.gaps.slice(0, 2).map((gap, index) => (
                        <div key={index} className="text-xs text-purple-600 bg-white/60 p-2 rounded">
                          • {gap.topic}: {gap.metrics.questionsCount} perguntas
                        </div>
                      ))}
                    </div>
                  )}
                  {knowledgeGapsData?.summary && knowledgeGapsData.summary.totalGaps === 0 && (
                    <div className="text-xs text-green-600 bg-white/60 p-2 rounded">
                      ✅ Conhecimento bem documentado
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* Footer com informações do sistema */}
      <motion.div variants={itemVariants}>
        <Card className="border-0 bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Sparkles className="h-6 w-6" />
                <div>
                  <div className="font-semibold">Organizational Pulse Analytics</div>
                  <div className="text-sm opacity-90">
                    Análise inteligente em tempo real da dinâmica organizacional • Totalmente funcional
                  </div>
                </div>
              </div>
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                <CheckCircle className="h-3 w-3 mr-1" />
                Sistema Ativo
              </Badge>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Modais de Explicação */}
      <CommunicationHealthModal 
        isOpen={communicationModalOpen}
        onClose={() => setCommunicationModalOpen(false)}
        data={commData}
      />
      
      <KnowledgeFlowModal 
        isOpen={knowledgeModalOpen}
        onClose={() => setKnowledgeModalOpen(false)}
        data={pulseData?.knowledgeFlow}
      />
      
      <OrganizationalTemperatureModal 
        isOpen={temperatureModalOpen}
        onClose={() => setTemperatureModalOpen(false)}
        data={pulseData?.organizationalTemperature}
      />
      
      <CollaborationModal 
        isOpen={collaborationModalOpen}
        onClose={() => setCollaborationModalOpen(false)}
        data={collabData}
      />
      
      <ProductivityModal 
        isOpen={productivityModalOpen}
        onClose={() => setProductivityModalOpen(false)}
        data={prodData}
      />
      </motion.div>
  );
}