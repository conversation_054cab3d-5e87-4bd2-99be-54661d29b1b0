/**
 * Componente reutilizável da Tabela de Comparação de Funcionalidades
 * Baseado na auditoria real das funcionalidades do Vindula Cosmos
 * <AUTHOR> Internet 2025
 */

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Check, 
  X,
  Crown,
  Infinity,
  Users, 
  Database, 
  Sparkles, 
  Shield,
  MessageSquare,
  FileText,
  BarChart3,
  Settings,
  Palette,
  Bot,
  Activity,
  Network,
  Lock,
  Bell,
  Target,
  Layers,
  Code,
  Globe,
  Gauge,
  Brain,
  Award,
  Heart,
  Calendar,
  Search,
  Filter,
  Share2,
  Download,
  Upload,
  Eye,
  TrendingUp,
  Workflow,
  BookOpen,
  Video,
  Image,
  HardDrive,
  Wifi,
  RefreshCw,
  CheckCircle2,
  Star,
  Zap,
  Headphones,
  Clock,
  Smartphone,
  MapPin,
  Edit,
  BarChart2,
  CalendarDays,
  Hash
} from "lucide-react";

// Dados das funcionalidades (extraídos da landing original)
const comparisonFeatures = [
  {
    category: "💬 Sistema de Comunicação Real-Time",
    features: [
      {
        name: "Chat privado entre usuários",
        icon: MessageSquare,
        free: "check",
        pro: "check",
        max: "check",
        description: "Conversas privadas instantâneas entre colaboradores"
      },
      {
        name: "Canais temáticos da empresa",
        icon: Network,
        free: "check",
        pro: "check",
        max: "check",
        description: "Canais públicos e privados por empresa"
      },
      {
        name: "Threads de discussão",
        icon: MessageSquare,
        free: "check",
        pro: "check", 
        max: "check",
        description: "Respostas aninhadas em conversas"
      },
      {
        name: "Indicadores de digitação",
        icon: Activity,
        free: "check",
        pro: "check",
        max: "check",
        description: "Veja quando alguém está digitando em tempo real"
      },
      {
        name: "Histórico de mensagens",
        icon: Clock,
        free: "30 dias",
        pro: "6 meses",
        max: "Ilimitado",
        description: "Acesse conversas antigas quando precisar"
      },
      {
        name: "Sugestões Inteligentes de Resposta",
        icon: Brain,
        free: "check - tempo limitado",
        pro: "check",
        max: "check",
        description: "IA gera sugestões de resposta naturais para chat - responda mais rápido e com naturalidade brasileira"
      },
      {
        name: "Resumo Automático de Conversas",
        icon: FileText,
        free: "check - tempo limitado",
        pro: "check",
        max: "check",
        description: "IA cria resumos inteligentes de conversas por período temporal - chegue por último e entenda tudo"
      },
      {
        name: "Tradução em Tempo Real",
        icon: Globe,
        free: "check - tempo limitado",
        pro: "check",
        max: "check",
        description: "Detecta idiomas automaticamente e oferece tradução instantânea das mensagens"
      }
    ]
  },
  {
    category: "🚀 Feed Interativo & Posts",
    features: [
      {
        name: "Feed de posts da empresa",
        icon: Activity,
        free: "check",
        pro: "check",
        max: "check",
        description: "Posts com likes, comentários e compartilhamentos"
      },
      {
        name: "Timeline com eventos personalizados",
        icon: Calendar,
        free: "check",
        pro: "check",
        max: "check",
        description: "Timeline personalizada com eventos da empresa e feed global"
      },
      {
        name: "Editor rico",
        icon: FileText,
        free: "check",
        pro: "check",
        max: "check",
        description: "Crie posts com texto rico, enquetes, imagens, gravação/upload de áudio e vídeo"
      },
      {
        name: "Imagens por IA",
        icon: Brain,
        free: "check - tempo limitado",
        pro: "check",
        max: "check",
        description: "Crie imagens únicas geradas por inteligência artificial para suas publicações no Feed Interativo"
      },
      {
        name: "Textos por IA",
        icon: Brain,
        free: "check - tempo limitado",
        pro: "check",
        max: "check",
        description: "Gere textos inteligentes e personalizados automaticamente para suas publicações no Feed Interativo."
      },
      {
        name: "Aprimoramento por IA",
        icon: Brain,
        free: "check - tempo limitado",
        pro: "check",
        max: "check",
        description: "Transforme seus textos automaticamente para publicações mais claras, envolventes e impactantes no Feed Interativo."
      },
      {
        name: "Enquetes Interativas",
        icon: BarChart2,
        free: "check",
        pro: "check",
        max: "check",
        description: "Crie enquetes dinâmicas para aumentar o engajamento e ouvir a opinião do seu público em tempo real."
      },
      {
        name: "Hashtags Automáticas",
        icon: Hash,
        free: "check",
        pro: "check",
        max: "check",
        description: "Gere hashtags relevantes automaticamente com base no conteúdo da publicação e aumente o alcance das suas postagens."
      },
      {
        name: "Galeria de Fotos",
        icon: Image,
        free: "check",
        pro: "check",
        max: "check",
        description: "Adicione múltiplas imagens em formato de galeria para tornar suas publicações mais visuais, atrativas e organizadas."
      },
      {
        name: "Publicação Segmentada",
        icon: Target,
        free: "check",
        pro: "check",
        max: "check",
        description: "Direcione suas publicações para públicos específicos e aumente a relevância da sua comunicação. Alcance as pessoas certas no momento certo."
      },
      {
        name: "Sistema de likes e reações",
        icon: Heart,
        free: "check",
        pro: "check",
        max: "check",
        description: "Engajamento em tempo real com curtidas e reações animadas que dão vida às suas publicações."
      },
      {
        name: "Comentários aninhados",
        icon: MessageSquare,
        free: "check",
        pro: "check",
        max: "check",
        description: "Sistema completo de comentários em posts"
      },
      {
        name: "Anexos de arquivos",
        icon: Upload,
        free: "Básico",
        pro: "Completo",
        max: "Ilimitado",
        description: "Upload de imagens, aúdios, documentos e vídeos"
      },
      {
        name: "Análise de Sentimento",
        icon: Brain,
        free: "check - tempo limitado",
        pro: "check",
        max: "check",
        description: "Identifique automaticamente o tom emocional das suas publicações antes de postar e comunique-se com mais intenção e estratégia."
      },
      {
        name: "Análise de Engajamento com IA",
        icon: Brain,
        free: "check - tempo limitado",
        pro: "check",
        max: "check",
        description: "Análise contextualizada de engajamento para publicações considerando a empresa, audiência e timing"
      },
      {
        name: "Estatísticas do Feed",
        icon: BarChart3,
        free: "24 horas",
        pro: "30 dias",
        max: "90 dias",
        description: "Acompanhe métricas detalhadas de engajamento, visualizações e interações para entender o desempenho das suas publicações e otimizar sua estratégia."
      },
      {
        name: "Agendamento Inteligente",
        icon: Clock,
        free: "2 agendamentos",
        pro: "10 agendamentos",
        max: "Ilimitado",
        description: "Programe suas postagens para os melhores dias e horários. Mantenha sua presença ativa mesmo quando estiver offline."
      }
    ]
  },
  {
    category: "🏆 Gamificação Completa (Sistema Stardust)",
    features: [
      {
        name: "Sistema Stardust",
        icon: Sparkles,
        free: "check",
        pro: "check",
        max: "check",
        description: "Moeda virtual que recompensa participação e engajamento"
      },
      {
        name: "Níveis de usuário (XP)",
        icon: TrendingUp,
        free: "check",
        pro: "check",
        max: "check",
        description: "Sistema de progressão gamificado - colaboradores evoluem, ganham XP e desbloqueiam conquistas automaticamente"
      },
      {
        name: "Medalhas e conquistas",
        icon: Award,
        free: "Visualização apenas",
        pro: "Editar existentes",
        max: "Criação ilimitada",
        description: "Reconhecimento visual das conquistas dos colaboradores"
      },
      {
        name: "Configuração de níveis",
        icon: Target,
        free: "Visualização apenas",
        pro: "Editar existentes",
        max: "Criação ilimitada",
        description: "Configure níveis personalizados, requisitos XP e desbloqueios para gamificação empresarial"
      },
      {
        name: "Loja virtual de recompensas",
        icon: Crown,
        free: "x",
        pro: "check",
        max: "Premium",
        description: "Colaboradores trocam pontos por benefícios e prêmios"
      },
      {
        name: "Missões e desafios",
        icon: Target,
        free: "x",
        pro: "check",
        max: "Ilimitado",
        description: "Tarefas gamificadas que aumentam motivação e participação"
      },
      {
        name: "Hearts flutuantes",
        icon: Heart,
        free: "check",
        pro: "check",
        max: "check",
        description: "Feedback visual divertido para ações dos colaboradores"
      },
      {
        name: "Missões diárias/semanais",
        icon: Calendar,
        free: "x",
        pro: "check",
        max: "Ilimitado",
        description: "Desafios recorrentes que mantêm o engajamento constante"
      },
      {
        name: "Banco de missões",
        icon: Database,
        free: "x",
        pro: "check",
        max: "Ilimitado",
        description: "Biblioteca de desafios prontos para usar na empresa"
      },
      {
        name: "Geração de missões com IA",
        icon: Brain,
        free: "x",
        pro: "x",
        max: "check",
        description: "Inteligência artificial cria desafios personalizados automaticamente"
      },
      {
        name: "Analytics de missões",
        icon: BarChart3,
        free: "x",
        pro: "check",
        max: "Avançado",
        description: "Relatórios completos de participação e resultados"
      },
      {
        name: "Marketplace estratégico",
        icon: Globe,
        free: "x",
        pro: "check",
        max: "Premium",
        description: "Catálogo de benefícios e vantagens exclusivas para colaboradores"
      },
      {
        name: "Ofertas especiais limitadas",
        icon: Zap,
        free: "x",
        pro: "check",
        max: "Ilimitado",
        description: "Promoções por tempo limitado que criam senso de urgência"
      },
      {
        name: "Ranking e competições",
        icon: TrendingUp,
        free: "x",
        pro: "check",
        max: "Avançado",
        description: "Competições saudáveis que motivam equipes e indivíduos"
      }
    ]
  },
  {
    category: "📚 Gestão de Conhecimento",
    features: [
      {
        name: "Knowledge Hub",
        icon: BookOpen,
        free: "Básico",
        pro: "Completo",
        max: "Ilimitado",
        description: "Biblioteca centralizada de documentos"
      },
      {
        name: "Leituras obrigatórias",
        icon: BookOpen,
        free: "x",
        pro: "check",
        max: "check",
        description: "Garante que documentos importantes sejam lidos e compreendidos"
      },
      {
        name: "Sistema Post-it",
        icon: FileText,
        free: "check",
        pro: "check",
        max: "check",
        description: "Notas rápidas e lembretes compartilhados entre colaboradores"
      },
      {
        name: "Controle de versões",
        icon: RefreshCw,
        free: "x",
        pro: "check",
        max: "check",
        description: "Histórico completo de alterações em documentos"
      },
      {
        name: "Busca avançada",
        icon: Search,
        free: "Simples",
        pro: "Avançada",
        max: "IA-Powered",
        description: "Encontre qualquer documento ou informação rapidamente"
      },
      {
        name: "Templates inteligentes",
        icon: Workflow,
        free: "x",
        pro: "check",
        max: "Ilimitado",
        description: "Modelos prontos que agilizam a criação de documentos"
      },
      {
        name: "Espaços de conhecimento",
        icon: Layers,
        free: "x",
        pro: "check",
        max: "Ilimitado",
        description: "Organize documentos por departamento ou projeto"
      }
    ]
  },
  {
    category: "🧑‍🤝‍🧑 People Hub",
    features: [
      {
        name: "Diretório de Pessoas",
        icon: Users,
        free: "check",
        pro: "check",
        max: "check",
        description: "Conheça e conecte-se com pessoas incríveis da sua empresa"
      },
      {
        name: "Equipes",
        icon: Users,
        free: "4 equipes",
        pro: "10 equipes",
        max: "Ilimitado",
        description: "Organize pessoas em equipes e gerencie conteúdos direcionados"
      },
      {
        name: "Organograma",
        icon: Network,
        free: "check",
        pro: "check",
        max: "check",
        description: "Visualize a estrutura hierárquica da sua empresa de forma clara e organizada"
      },
      {
        name: "Eventos",
        icon: CalendarDays,
        free: "check",
        pro: "check",
        max: "check",
        description: "Gerencie aniversários, promoções, ausências e novidades da sua equipe"
      }, 
      {
        name: "Pulse",
        icon: Activity,
        free: "check",
        pro: "check",
        max: "check",
        description: "Acompanhe o clima da empresa com relatórios rápidos e insights valiosos"
      }     
    ]
  },
  {
    category: "🧠 Inteligência Artificial Nativa",
    features: [
      {
        name: "Créditos de IA por mês",
        icon: Bot,
        free: "10 créditos",
        pro: "50 créditos",
        max: "250 créditos",
        description: "Use inteligência artificial para automatizar tarefas"
      },
      {
        name: "Recursos de IA disponíveis",
        icon: Brain,
        free: "3 recursos",
        pro: "10 recursos",
        max: "15 recursos",
        description: "Ferramentas inteligentes para otimizar produtividade"
      },
      {
        name: "IA para resumos automáticos",
        icon: FileText,
        free: "x",
        pro: "check",
        max: "check",
        description: "Economize tempo com resumos inteligentes de documentos"
      },
      {
        name: "Sugestões inteligentes",
        icon: Zap,
        free: "x",
        pro: "check",
        max: "Avançado",
        description: "Recomendações personalizadas de conteúdo e pessoas"
      },
      {
        name: "Análise de sentimento",
        icon: Activity,
        free: "x",
        pro: "x",
        max: "check",
        description: "Monitore o clima organizacional automaticamente"
      },
      {
        name: "Geração de conteúdo",
        icon: Brain,
        free: "x",
        pro: "check",
        max: "Avançado",
        description: "Crie conteúdo profissional com assistente inteligente"
      },
      {
        name: "Análise preditiva",
        icon: TrendingUp,
        free: "x",
        pro: "x",
        max: "check",
        description: "Previsões e insights estratégicos para tomada de decisão"
      },
    ]
  },
  {
    category: "📊 Analytics & Relatórios",
    features: [
      {
        name: "Analytics de engajamento",
        icon: BarChart3,
        free: "x",
        pro: "check",
        max: "Avançado",
        description: "Acompanhe o engajamento da equipe em tempo real"
      },
      {
        name: "Relatórios de uso",
        icon: TrendingUp,
        free: "x",
        pro: "Básicos",
        max: "Completos",
        description: "Relatórios detalhados de uso e performance"
      },
      {
        name: "Dashboards personalizados",
        icon: Gauge,
        free: "x",
        pro: "x",
        max: "check",
        description: "Painéis personalizados para líderes e gestores"
      },
      {
        name: "Relatórios de ROI",
        icon: TrendingUp,
        free: "x",
        pro: "x",
        max: "check",
        description: "Comprove o valor e ROI da plataforma"
      },
      {
        name: "Exportação de dados",
        icon: Download,
        free: "x",
        pro: "Básica",
        max: "Completa",
        description: "Exporte dados para análises externas"
      },
      {
        name: "Relatórios de cultura",
        icon: Users,
        free: "x",
        pro: "x",
        max: "check",
        description: "Relatórios de cultura e satisfação dos colaboradores"
      },
      {
        name: "Learning Analytics",
        icon: BookOpen,
        free: "x",
        pro: "check",
        max: "Avançado",
        description: "Acompanhe o progresso de capacitação da equipe"
      }
    ]
  },
  {
    category: "🏢 Gestão de RH & Organização",
    features: [
      {
        name: "Sistema de promoções",
        icon: TrendingUp,
        free: "x",
        pro: "check",
        max: "check",
        description: "Processo automatizado de promoções com celebrações"
      },
      {
        name: "Gestão de ausências",
        icon: Calendar,
        free: "x",
        pro: "check",
        max: "check",
        description: "Gerencie ausências e licenças facilmente"
      },
      {
        name: "Organograma interativo",
        icon: Network,
        free: "x",
        pro: "check",
        max: "check",
        description: "Organograma visual e interativo da empresa"
      },
      {
        name: "Gestão de departamentos",
        icon: Users,
        free: "3 departamentos",
        pro: "10 departamentos",
        max: "Ilimitado",
        description: "Organize a empresa por departamentos"
      },
      {
        name: "Controle de unidades",
        icon: Globe,
        free: "3 unidades",
        pro: "10 unidades",
        max: "Ilimitado",
        description: "Gerencie múltiplas filiais e localidades"
      },
      {
        name: "Gestão de localidades",
        icon: MapPin,
        free: "3 localidades",
        pro: "10 localidades",
        max: "Ilimitado",
        description: "Cadastre endereços e locais de trabalho"
      },
      {
        name: "Gestão de cargos",
        icon: Award,
        free: "3 cargos",
        pro: "10 cargos",
        max: "Ilimitado",
        description: "Defina cargos e hierarquias organizacionais"
      },
      {
        name: "Diretório inteligente",
        icon: Search,
        free: "Básico",
        pro: "Avançado",
        max: "Completo",
        description: "Encontre colaboradores rapidamente"
      },
      {
        name: "Networking corporativo",
        icon: Network,
        free: "x",
        pro: "check",
        max: "check",
        description: "Conecte colaboradores com interesses em comum"
      },
      {
        name: "Pulse organizacional",
        icon: Activity,
        free: "x",
        pro: "check",
        max: "Avançado",
        description: "Monitore o engajamento por departamento"
      },
      {
        name: "Sistema de equipes",
        icon: Users,
        free: "1 equipe",
        pro: "5 equipes",
        max: "Ilimitado",
        description: "Crie equipes para projetos com chat privado e colaboração"
      }
    ]
  },
  {
    category: "🎂 Eventos & Celebrações",
    features: [
      {
        name: "Cartões de aniversário",
        icon: Heart,
        free: "x",
        pro: "check",
        max: "check",
        description: "Celebre aniversários com cartões personalizados"
      },
      {
        name: "Eventos corporativos",
        icon: Calendar,
        free: "Básico",
        pro: "Avançado",
        max: "Completo",
        description: "Organize eventos corporativos completos"
      },
      {
        name: "Calendário inteligente",
        icon: Calendar,
        free: "x",
        pro: "check",
        max: "check",
        description: "Calendário integrado com lembretes inteligentes"
      },
      {
        name: "Celebrações automáticas",
        icon: Star,
        free: "x",
        pro: "check",
        max: "check",
        description: "Celebre automaticamente conquistas e marcos"
      },
      {
        name: "Notificações inteligentes",
        icon: Bell,
        free: "x",
        pro: "check",
        max: "check",
        description: "Receba resumos automáticos de eventos importantes"
      }
    ]
  },
  {
    category: "🎨 Personalização & Interface",
    features: [
      {
        name: "Personalização de tema",
        icon: Palette,
        free: "x",
        pro: "Básica",
        max: "Total",
        description: "Personalize cores e visual da plataforma"
      },
      {
        name: "Logo da empresa",
        icon: Image,
        free: "x",
        pro: "check",
        max: "check",
        description: "Adicione a marca da sua empresa"
      },
      {
        name: "App mobile nativo",
        icon: Smartphone,
        free: "PWA",
        pro: "PWA+",
        max: "Nativo",
        description: "Acesse de qualquer dispositivo móvel"
      },
      {
        name: "Interface customizável",
        icon: Settings,
        free: "x",
        pro: "Limitada",
        max: "Completa",
        description: "Customize a interface completamente"
      },
      {
        name: "Visual Assets",
        icon: Image,
        free: "x",
        pro: "check",
        max: "Ilimitado",
        description: "Elementos visuais exclusivos para sua empresa"
      },
      {
        name: "Reaction Packs",
        icon: Heart,
        free: "x",
        pro: "check",
        max: "Ilimitado",
        description: "Reações e emojis personalizados"
      }
    ]
  },
  {
    category: "🔒 Segurança & Conformidade",
    features: [
      {
        name: "Usuários por empresa",
        icon: Users,
        free: "5 usuários",
        pro: "10 usuários", 
        max: "10 usuários",
        description: "Quantidade de colaboradores por plano"
      },
      {
        name: "Row Level Security (RLS)",
        icon: Shield,
        free: "check",
        pro: "check",
        max: "check",
        description: "Segurança avançada com isolamento total de dados"
      },
      {
        name: "Isolamento de dados por empresa",
        icon: Lock,
        free: "check",
        pro: "check",
        max: "check",
        description: "Cada empresa tem seus dados 100% isolados"
      },
      {
        name: "Controle de permissões granular",
        icon: Settings,
        free: "Básico",
        pro: "Avançado",
        max: "Completo",
        description: "Controle detalhado de quem acessa o quê"
      },
      {
        name: "Portal de privacidade LGPD",
        icon: Shield,
        free: "x",
        pro: "check",
        max: "check",
        description: "Conformidade total com LGPD e privacidade"
      },
      {
        name: "Gestão de consentimentos",
        icon: Lock,
        free: "x",
        pro: "check",
        max: "check",
        description: "Gerencie consentimentos e privacidade"
      },
      {
        name: "Auditoria de conformidade",
        icon: CheckCircle2,
        free: "x",
        pro: "x",
        max: "check",
        description: "Auditoria completa para conformidade"
      }
    ]
  },
  {
    category: "🔧 Integrações & API",
    features: [
      {
        name: "API REST completa",
        icon: Code,
        free: "x",
        pro: "Básica",
        max: "Completa",
        description: "Integre com sistemas existentes da empresa"
      },
      {
        name: "Webhooks",
        icon: Workflow,
        free: "x",
        pro: "check",
        max: "check",
        description: "Automações e integrações em tempo real"
      },
      {
        name: "SSO (Single Sign-On)",
        icon: Lock,
        free: "x",
        pro: "x",
        max: "check",
        description: "Login único com sistemas corporativos"
      },
      {
        name: "Integrações nativas",
        icon: Globe,
        free: "x",
        pro: "5 apps",
        max: "Ilimitado",
        description: "Conecte com ferramentas que já usa"
      }
    ]
  },
  {
    category: "💾 Armazenamento & Performance",
    features: [
      {
        name: "Armazenamento de arquivos",
        icon: Database,
        free: "1GB",
        pro: "10GB",
        max: "50GB",
        description: "Armazenamento seguro com acesso rápido mundial"
      },
      {
        name: "Backup automático",
        icon: HardDrive,
        free: "x",
        pro: "check",
        max: "check",
        description: "Seus dados sempre seguros com backup automático"
      },
      {
        name: "CDN Global",
        icon: Globe,
        free: "x",
        pro: "check",
        max: "check",
        description: "Acesso ultrarrápido de qualquer lugar do mundo"
      },
      {
        name: "WebSocket consolidado",
        icon: Wifi,
        free: "Básico",
        pro: "Otimizado",
        max: "Premium",
        description: "Tecnologia avançada para máxima performance"
      }
    ]
  },
  {
    category: "🛠️ Suporte & SLA",
    features: [
      {
        name: "Suporte técnico",
        icon: Headphones,
        free: "Email",
        pro: "Prioritário",
        max: "24/7 Dedicado",
        description: "Suporte especializado quando precisar"
      },
      {
        name: "SLA de uptime",
        icon: CheckCircle2,
        free: "x",
        pro: "99%",
        max: "99.9%",
        description: "Plataforma sempre disponível quando precisar"
      },
      {
        name: "Onboarding personalizado",
        icon: Users,
        free: "x",
        pro: "x",
        max: "check",
        description: "Implementação guiada por especialistas"
      },
      {
        name: "Treinamento da equipe",
        icon: BookOpen,
        free: "x",
        pro: "x",
        max: "check",
        description: "Treinamento completo para sua equipe"
      }
    ]
  }
];

// Classes Tailwind para substituir CSS dinâmico
const featureHighlightClass = "bg-gradient-to-r from-emerald-500/10 to-emerald-500/5 border border-emerald-500/20 rounded-lg p-3 m-1 pointer-events-auto";
const featurePremiumClass = "bg-gradient-to-r from-violet-500/15 to-purple-600/10 border border-violet-500/40 rounded-lg p-3 m-1 shadow-lg shadow-violet-500/20 pointer-events-auto";
const featureLimitedClass = "bg-gradient-to-r from-amber-500/10 to-amber-500/5 border border-amber-500/20 rounded-lg p-3 m-1 pointer-events-auto";

interface ComparisonTableProps {
  onPlanSelect: (planId: string) => void;
  showCTAs?: boolean;
  className?: string;
}

// Função para renderizar o conteúdo das células
const renderCellContent = (value: string, planType: 'free' | 'pro' | 'max', feature: any) => {
  if (value === "check") {
    return (
      <div className={featureHighlightClass}>
        <Check className="h-5 w-5 text-emerald-400 mx-auto drop-shadow-sm" />
        <div className="text-xs text-emerald-300 mt-1">Incluído</div>
      </div>
    );
  }
  if (value === "check - tempo limitado") {
    return (
      <div className={featureHighlightClass}>
        <Check className="h-5 w-5 text-emerald-400 mx-auto drop-shadow-sm" />
        <div className="text-xs text-emerald-200 mt-1 opacity-75 flex items-center justify-center gap-1">
          <Star className="h-3 w-3" />
          Por tempo limitado
        </div>
      </div>
    );
  }
  if (value === "x") {
    return (
      <div className="text-center pointer-events-auto">
        <X className="h-5 w-5 text-gray-500 mx-auto" />
        <div className="text-xs text-gray-500 mt-1">Não incluído</div>
      </div>
    );
  }
  
  // Plano Max - SEMPRE mostrar infinity + texto original
  if (planType === 'max') {
    return (
      <div className={featurePremiumClass}>
        <div className="flex items-center justify-center gap-2">
          <Infinity className="h-4 w-4 text-purple-400 drop-shadow-md" />
          <span className="text-purple-200 font-semibold text-sm">{value}</span>
        </div>
        {feature.description && (
          <div className="text-xs text-purple-200 mt-1 opacity-75">
            {feature.description}
          </div>
        )}
      </div>
    );
  }
  
  // Plano Pro - SEMPRE mostrar coroa + texto original
  if (planType === 'pro') {
    return (
      <div className={featureLimitedClass}>
        <div className="flex items-center justify-center gap-2">
          <Crown className="h-4 w-4 text-amber-400 drop-shadow-md" />
          <span className="text-amber-300 font-medium text-sm">{value}</span>
        </div>
        {feature.description && (
          <div className="text-xs text-amber-200 mt-1 opacity-75">
            {feature.description}
          </div>
        )}
      </div>
    );
  }
  
  return (
    <div className="text-center pointer-events-auto">
      <span className="text-gray-200 font-medium">{value}</span>
      {feature.description && (
        <div className="text-xs text-gray-400 mt-1 opacity-75">
          {feature.description}
        </div>
      )}
    </div>
  );
};

export default function ComparisonTable({ 
  onPlanSelect, 
  showCTAs = true, 
  className = "" 
}: ComparisonTableProps) {
  
  // CSS dinâmico removido - usando apenas Tailwind classes

  return (
    <div className={`max-w-7xl mx-auto overflow-x-auto ${className}`}>
      <table className="w-full border-collapse bg-gradient-to-br from-[#1a1e2a] to-[#0f1419] rounded-xl border border-[#4D3428] overflow-hidden shadow-2xl">
        {/* Header */}
        <thead>
          <tr className="bg-gradient-to-r from-[#2a2e3a] to-[#1a1e2a] border-b border-[#4D3428]">
            <th className="text-left py-6 px-6 text-white font-bold text-lg">
              Tudo que sua empresa precisa para engajar
            </th>
            <th className="text-center py-6 px-4 text-white font-bold">
              <div>Grátis</div>
              <div className="text-gray-400 text-sm font-normal">R$ 0/mês</div>
            </th>
            <th className="text-center py-6 px-4 text-white font-bold relative">
              <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#C85C2D] to-[#E8A95B]"></div>
              <div>Pro</div>
              <div className="text-gray-400 text-sm font-normal">R$ 299,99/mês</div>
              <div className="text-[#E8A95B] text-xs font-medium">Mais Popular</div>
            </th>
            <th className="text-center py-6 px-4 text-white font-bold relative">
              <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-600 to-pink-600"></div>
              <div>Max</div>
              <div className="text-gray-400 text-sm font-normal">R$ 599,99/mês</div>
              <div className="text-purple-400 text-xs font-medium">5x Mais Poder</div>
            </th>
          </tr>
        </thead>

        <tbody>
          {comparisonFeatures.map((category, categoryIndex) => (
            <React.Fragment key={categoryIndex}>
              {/* Categoria Header */}
              <tr className="bg-[#1a1e2a] border-b border-[#4D3428]">
                <td colSpan={4} className="py-4 px-6">
                  <h3 className="text-[#E8A95B] font-semibold text-lg">
                    {category.category}
                  </h3>
                </td>
              </tr>

              {/* Features */}
              {category.features.map((feature, featureIndex) => (
                <tr 
                  key={featureIndex} 
                  className="border-b border-[#4D3428]/30 hover:bg-[#1a1e2a]/50 transition-colors"
                >
                  <td className="py-4 px-6">
                    <div className="flex items-start text-gray-200">
                      <feature.icon className="h-5 w-5 text-[#E8A95B] mr-3 mt-0.5 flex-shrink-0" />
                      <div>
                        <div className="font-medium">{feature.name}</div>
                        {feature.description && (
                          <div className="text-sm text-gray-400 mt-1">
                            {feature.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4 text-center">
                    {renderCellContent(feature.free, 'free', feature)}
                  </td>
                  <td className="py-4 px-4 text-center">
                    {renderCellContent(feature.pro, 'pro', feature)}
                  </td>
                  <td className="py-4 px-4 text-center">
                    {renderCellContent(feature.max, 'max', feature)}
                  </td>
                </tr>
              ))}
            </React.Fragment>
          ))}

          {/* Footer com CTAs */}
          {showCTAs && (
            <tr className="bg-gradient-to-r from-[#2a2e3a] to-[#1a1e2a] border-t border-[#4D3428]">
              <td className="py-6 px-6 text-white font-semibold">
                Escolha o plano ideal para sua empresa
              </td>
              <td className="py-6 px-4 text-center">
                <Button
                  onClick={() => onPlanSelect('free')}
                  className="bg-[#1a1e2a] hover:bg-[#2a2e3a] border border-[#4D3428] text-white px-6 py-2 pointer-events-auto cursor-pointer relative z-10"
                >
                  Começar Grátis
                </Button>
              </td>
              <td className="py-6 px-4 text-center">
                <Button
                  onClick={() => onPlanSelect('pro')}
                  className="bg-gradient-to-r from-[#C85C2D] to-[#E8A95B] hover:from-[#E8A95B] hover:to-[#C85C2D] text-white px-6 py-2 pointer-events-auto cursor-pointer relative z-10"
                >
                  Testar Pro
                </Button>
              </td>
              <td className="py-6 px-4 text-center">
                <Button
                  onClick={() => onPlanSelect('max')}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 py-2 pointer-events-auto cursor-pointer relative z-10"
                >
                  Testar Max
                </Button>
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
}