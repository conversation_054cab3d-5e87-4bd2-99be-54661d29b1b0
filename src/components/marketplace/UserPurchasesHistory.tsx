/**
 * Histórico Unificado de Compras do Usuário
 * Mostra ofertas especiais e itens estratégicos comprados
 * <AUTHOR> Internet 2025
 */
import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ShoppingBag,
  Calendar,
  Star,
  Package,
  Clock,
  CheckCircle,
  Truck,
  XCircle,
  Eye,
  Sparkles,
  Gift,
  Filter,
  ArrowRight,
  Trophy,
  Target
} from 'lucide-react';
import { useUnifiedUserPurchases, type UnifiedPurchase } from '@/lib/query/hooks/useUnifiedPurchases';
import { RefreshButton } from '@/components/ui/RefreshButton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { HeroSection } from '@/components/common/HeroSection';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';


// Função para formatar Stardust com novo padrão oficial
const formatStardust = (amount: number) => {
  return `✨ ${amount.toLocaleString('pt-BR')}`;
};

// Função para formatar data
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Componente para status da compra
const PurchaseStatusBadge = ({ status }: { status: string }) => {
  const statusConfig = {
    pending: { label: 'Pendente', variant: 'secondary' as const, icon: Clock },
    confirmed: { label: 'Confirmada', variant: 'default' as const, icon: CheckCircle },
    delivered: { label: 'Entregue', variant: 'default' as const, icon: Truck },
    completed: { label: 'Concluída', variant: 'default' as const, icon: CheckCircle },
    cancelled: { label: 'Cancelada', variant: 'destructive' as const, icon: XCircle },
  };

  const config = statusConfig[status as keyof typeof statusConfig];
  
  if (!config) {
    // Log para debug de status não mapeados
    logQueryEvent('PurchaseStatusBadge', `Status não mapeado: ${status}. Usando fallback 'pending'.`, { status }, 'warn');
    return (
      <Badge variant="secondary" className="flex items-center gap-1">
        <Clock className="h-3 w-3" />
        Pendente
      </Badge>
    );
  }

  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  );
};

// Modal para ver detalhes da compra
const PurchaseDetailsModal = ({ purchase }: { purchase: UnifiedPurchase }) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="hover:bg-accent transition-colors">
          <Eye className="h-3 w-3 mr-2" />
          Ver Detalhes
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <ShoppingBag className="h-6 w-6 text-blue-600" />
            Detalhes da Compra - {purchase.title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Informações da compra */}
          <Card className="border-blue-200 bg-blue-50/50">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Package className="h-5 w-5 text-blue-600" />
                Resumo da Compra
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Data da Compra</p>
                  <p className="text-sm font-semibold">{formatDate(purchase.purchased_at)}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Status</p>
                  <PurchaseStatusBadge status={purchase.status} />
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Quantidade</p>
                  <p className="text-sm font-semibold">{purchase.quantity} item(s)</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Tipo</p>
                  <p className="text-sm font-bold text-blue-600">
                    {purchase.type === 'strategic_item' ? '🎯 Estratégico' : '🔥 Oferta Especial'}
                  </p>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-3 gap-4">
                {purchase.original_price && purchase.original_price !== purchase.total_cost && (
                  <div className="text-center p-4 bg-white rounded-lg border">
                    <p className="text-sm font-medium text-muted-foreground mb-1">Preço Original</p>
                    <p className="text-xl font-bold line-through text-muted-foreground">
                      {formatStardust(purchase.original_price)}
                    </p>
                  </div>
                )}
                <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <p className="text-sm font-medium text-blue-700 mb-1">Preço Pago</p>
                  <p className="text-xl font-bold text-blue-600">
                    {formatStardust(purchase.total_cost)}
                  </p>
                </div>
                {purchase.original_price && purchase.original_price !== purchase.total_cost && (
                  <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                    <p className="text-sm font-medium text-green-700 mb-1">Economia</p>
                    <p className="text-xl font-bold text-green-600">
                      {formatStardust(purchase.original_price - purchase.total_cost)}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Itens comprados - DESTAQUE PRINCIPAL */}
          <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-pink-50 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-t-lg">
              <CardTitle className="text-xl flex items-center gap-3">
                <div className="p-2 bg-white/20 rounded-lg">
                  <Sparkles className="h-6 w-6 text-white" />
                </div>
                <div>
                  <span className="text-white font-bold">Produtos Resgatados</span>
                  <p className="text-purple-100 text-sm font-normal">
                    {purchase.quantity} item(s) - {purchase.type === 'strategic_item' ? 'Item Estratégico' : 'Oferta Especial'}
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4 }}
                  className="relative p-6 border-2 border-purple-200 rounded-xl bg-white hover:shadow-xl transition-all duration-300 hover:border-purple-300"
                >
                  {/* Badge de destaque */}
                  <div className="absolute -top-3 left-6">
                    <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white border-0 px-3 py-1">
                      <Gift className="h-3 w-3 mr-1" />
                      {purchase.type === 'strategic_item' ? '🎯 Estratégico' : '🔥 Oferta'}
                    </Badge>
                  </div>
                  
                  <div className="flex items-start justify-between mt-2">
                    <div className="flex-1 space-y-3">
                      {/* Nome do produto com destaque */}
                      <div className="space-y-2">
                        <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                          <Package className="h-5 w-5 text-purple-600" />
                          {purchase.title}
                        </h3>
                        <div className="flex items-center gap-2">
                          {purchase.category && (
                            <Badge variant="secondary" className="bg-purple-100 text-purple-700 border-purple-200">
                              {purchase.category}
                            </Badge>
                          )}
                          <Badge variant="outline" className="text-xs border-purple-200">
                            {purchase.type === 'strategic_item' ? 'Item Estratégico' : 'Oferta Especial'}
                          </Badge>
                        </div>
                      </div>
                      
                      {/* Descrição com destaque */}
                      {purchase.description && (
                        <div className="p-3 bg-purple-50 rounded-lg border border-purple-100">
                          <p className="text-sm text-gray-700 leading-relaxed">{purchase.description}</p>
                        </div>
                      )}
                      
                      {/* Nível requerido */}
                      {purchase.metadata?.level_required && (
                        <div className="flex items-center gap-2 p-2 bg-yellow-50 rounded-lg border border-yellow-200">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span className="text-sm text-yellow-700 font-medium">
                            Nível {purchase.metadata.level_required} necessário para uso
                          </span>
                        </div>
                      )}
                    </div>
                    
                    {/* Preços com destaque visual */}
                    <div className="ml-6 text-right space-y-3">
                      {purchase.original_price && purchase.original_price !== purchase.total_cost && (
                        <div className="p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border">
                          <p className="text-xs text-gray-500 mb-1">Preço Original</p>
                          <p className="text-lg font-bold line-through text-gray-400">
                            {formatStardust(purchase.original_price)}
                          </p>
                        </div>
                      )}
                      
                      <div className="p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg border border-green-200">
                        <p className="text-xs text-green-600 mb-1">Preço Pago</p>
                        <p className="text-xl font-bold text-green-700">
                          {formatStardust(purchase.total_cost)}
                        </p>
                      </div>
                      
                      {purchase.original_price && purchase.original_price !== purchase.total_cost && (
                        <div className="p-3 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-200 flex items-center gap-1">
                          <Sparkles className="h-3 w-3 text-blue-900 drop-shadow-md" fill="currentColor" strokeWidth={2} />
                          <p className="text-xs text-blue-600 font-medium">
                            Economizou {formatStardust(purchase.original_price - purchase.total_cost)}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
                
                {/* Mostrar items detalhados para ofertas especiais */}
                {purchase.type === 'special_offer' && purchase.metadata?.items_purchased?.map((item: any, index: number) => (
                  <motion.div
                    key={item.item_id || index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: (index + 1) * 0.1 }}
                    className="ml-8 p-4 border border-purple-100 rounded-lg bg-purple-50/30"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-purple-900">{item.name}</h4>
                        <p className="text-sm text-purple-700">{item.category}</p>
                        {item.description && (
                          <p className="text-xs text-purple-600 mt-1">{item.description}</p>
                        )}
                      </div>
                      <div className="text-right">
                        {item.original_price && (
                          <p className="text-sm line-through text-purple-400">
                            {formatStardust(item.original_price)}
                          </p>
                        )}
                        <p className="font-bold text-purple-700">
                          {formatStardust(item.discounted_price || item.original_price)}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>


          {/* Notas do admin */}
          {purchase.admin_notes && (
            <Card className="border-purple-200 bg-purple-50/50">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Target className="h-5 w-5 text-purple-600" />
                  Observações da Empresa
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-700">{purchase.admin_notes}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export function UserPurchasesHistory() {
  const { data: unifiedData, isLoading, error, refetch } = useUnifiedUserPurchases();
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Filtrar compras por status
  const purchasesArray = unifiedData?.purchases || [];
  const filteredPurchases = purchasesArray.filter(purchase => 
    statusFilter === 'all' || purchase.status === statusFilter
  );

  // Estatísticas
  const stats = unifiedData?.stats;

  const handleRefresh = async () => {
    await refetch();
  };

  // Loading otimista - renderizar conteúdo imediatamente
  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Skeleton das estatísticas */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-24 bg-muted animate-pulse rounded-lg"></div>
          ))}
        </div>
        
        {/* Skeleton das compras */}
        <div className="grid gap-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-48 bg-muted animate-pulse rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Alert className="border-red-200 bg-red-50">
          <XCircle className="h-4 w-4 text-red-500" />
          <AlertDescription className="text-red-700">
            Erro ao carregar suas compras. Tente novamente.
            <RefreshButton 
              isLoading={false} 
              onRefresh={handleRefresh}
              className="ml-4 bg-red-100 hover:bg-red-200 text-red-800"
              size="sm"
            />
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Actions Header Compacto */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Package className="h-5 w-5 text-blue-600" />
          <span className="text-lg font-semibold text-gray-900">
            {purchasesArray.length > 0 ? `${purchasesArray.length} compra(s)` : 'Histórico'}
          </span>
        </div>
        <RefreshButton 
          isLoading={isLoading} 
          onRefresh={handleRefresh}
          className="bg-blue-50 hover:bg-blue-100 text-blue-700"
          size="sm"
        />
      </div>

      {/* Estatísticas */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <Card className="border-blue-200 bg-blue-50/50">
            <CardContent className="p-4 text-center">
              <ShoppingBag className="h-6 w-6 mx-auto mb-2 text-blue-600" />
              <p className="text-2xl font-bold text-blue-700">{stats.total}</p>
              <p className="text-sm text-blue-600">Total de Compras</p>
            </CardContent>
          </Card>
          
          <Card className="border-yellow-200 bg-yellow-50/50">
            <CardContent className="p-4 text-center">
              <Clock className="h-6 w-6 mx-auto mb-2 text-yellow-600" />
              <p className="text-2xl font-bold text-yellow-700">{stats.pending}</p>
              <p className="text-sm text-yellow-600">Pendentes</p>
            </CardContent>
          </Card>
          
          <Card className="border-green-200 bg-green-50/50">
            <CardContent className="p-4 text-center">
              <Truck className="h-6 w-6 mx-auto mb-2 text-green-600" />
              <p className="text-2xl font-bold text-green-700">{stats.delivered}</p>
              <p className="text-sm text-green-600">Entregues</p>
            </CardContent>
          </Card>
          
          <Card className="border-purple-200 bg-purple-50/50">
            <CardContent className="p-4 text-center">
              <Sparkles className="h-6 w-6 mx-auto mb-2 text-purple-900 drop-shadow-md" fill="currentColor" strokeWidth={2} />
              <p className="text-sm font-bold text-purple-700">
                {formatStardust(stats.totalRevenue)}
              </p>
               <p className="text-sm text-purple-600">Total Gasto</p>
             </CardContent>
           </Card>
           
          <Card className="border-emerald-200 bg-emerald-50/50">
            <CardContent className="p-4 text-center">
              <Sparkles className="h-6 w-6 mx-auto mb-2 text-emerald-900 drop-shadow-md" fill="currentColor" strokeWidth={2} />
              <p className="text-sm font-bold text-emerald-700">
                {formatStardust((stats?.byType?.special_offer || 0) + (stats?.byType?.strategic_item || 0))}
              </p>
              <p className="text-sm text-emerald-600">Itens Comprados</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filtros */}
      {purchasesArray.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">Filtrar por status:</span>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os Status</SelectItem>
                  <SelectItem value="pending">Pendentes</SelectItem>
                  <SelectItem value="confirmed">Confirmadas</SelectItem>
                  <SelectItem value="delivered">Entregues</SelectItem>
                  <SelectItem value="cancelled">Canceladas</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Lista de compras */}
      {!purchasesArray.length ? (
        <Card className="border-dashed border-2 border-blue-200">
          <CardContent className="p-12 text-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="space-y-4"
            >
              <div className="relative">
                <ShoppingBag className="h-20 w-20 mx-auto text-blue-300" />
                <Sparkles className="h-8 w-8 absolute -top-2 -right-2 text-yellow-400 animate-pulse" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Nenhuma compra ainda</h3>
              <p className="text-muted-foreground max-w-md mx-auto">
                Você ainda não aproveitou nenhuma oferta especial. Explore nosso marketplace e encontre produtos incríveis com descontos exclusivos!
              </p>
              <Button 
                onClick={() => window.location.href = '/marketplace'}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Explorar Ofertas
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </motion.div>
          </CardContent>
        </Card>
      ) : !filteredPurchases.length ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Filter className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">Nenhuma compra com este status</h3>
            <p className="text-muted-foreground">
              Não há compras com status "{statusFilter}". Tente selecionar outro filtro.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {filteredPurchases.map((purchase, index) => (
            <motion.div
              key={purchase.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="hover:shadow-xl transition-all duration-300 border-l-4 border-l-blue-500">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <ShoppingBag className="h-5 w-5 text-green-500" />
                        {purchase.title}
                        <Badge variant="outline" className="text-xs">
                          {purchase.type === 'strategic_item' ? '🎯 Estratégico' : '🔥 Oferta'}
                        </Badge>
                      </CardTitle>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {formatDate(purchase.purchased_at)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Package className="h-4 w-4" />
                          {purchase.quantity} item(s)
                        </div>
                        {purchase.discount_percent && (
                          <div className="flex items-center gap-1">
                            <Gift className="h-4 w-4" />
                            {purchase.discount_percent}% OFF
                          </div>
                        )}
                        {purchase.category && (
                          <div className="flex items-center gap-1">
                            <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                              {purchase.category}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    <PurchaseStatusBadge status={purchase.status} />
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Info do produto */}
                  <div className="mb-4 p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-100">
                    <div className="flex items-center gap-2 mb-2">
                      <Package className="h-4 w-4 text-purple-600" />
                      <span className="text-sm font-medium text-purple-700">
                        Produto comprado:
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <Badge 
                        variant="secondary" 
                        className="bg-white/70 text-purple-700 border-purple-200 text-xs"
                      >
                        <Sparkles className="h-3 w-3 mr-1 text-purple-900 drop-shadow-md" fill="currentColor" strokeWidth={2} />
                        {purchase.title}
                      </Badge>
                      {purchase.category && (
                        <Badge 
                          variant="outline" 
                          className="text-xs text-purple-600 border-purple-300"
                        >
                          {purchase.category}
                        </Badge>
                      )}
                    </div>
                    
                    {/* Mostrar items das ofertas especiais */}
                    {purchase.type === 'special_offer' && purchase.metadata?.items_purchased && (
                      <div className="mt-3 pt-3 border-t border-purple-200">
                        <p className="text-xs text-purple-600 mb-2">Itens inclusos na oferta:</p>
                        <div className="flex flex-wrap gap-1">
                          {purchase.metadata.items_purchased.slice(0, 4).map((item: any, idx: number) => (
                            <Badge 
                              key={item.item_id || idx}
                              variant="outline" 
                              className="text-xs text-purple-600 border-purple-300 bg-white/50"
                            >
                              {item.name}
                            </Badge>
                          ))}
                          {purchase.metadata.items_purchased.length > 4 && (
                            <Badge 
                              variant="outline" 
                              className="text-xs text-purple-600 border-purple-300"
                            >
                              +{purchase.metadata.items_purchased.length - 4} mais
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Resumo financeiro */}
                  <div className="grid grid-cols-3 gap-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border">
                    {purchase.original_price && purchase.original_price !== purchase.total_cost && (
                      <div className="text-center">
                        <p className="text-xs text-muted-foreground mb-1">Preço Original</p>
                        <p className="font-bold line-through text-muted-foreground flex items-center justify-center gap-1">
                          <Sparkles className="h-3 w-3 text-gray-500 drop-shadow-md" fill="currentColor" strokeWidth={2} />
                          {purchase.original_price.toLocaleString('pt-BR')}
                        </p>
                      </div>
                    )}
                    <div className="text-center">
                      <p className="text-xs text-muted-foreground mb-1">Preço Pago</p>
                      <p className="text-lg font-bold text-blue-600 flex items-center justify-center gap-1">
                        <Sparkles className="h-4 w-4 text-blue-900 drop-shadow-md" fill="currentColor" strokeWidth={2} />
                        {purchase.total_cost.toLocaleString('pt-BR')}
                      </p>
                    </div>
                    {purchase.original_price && purchase.original_price !== purchase.total_cost && (
                      <div className="text-center">
                        <p className="text-xs text-muted-foreground mb-1">Economia</p>
                        <p className="font-bold text-green-600 flex items-center justify-center gap-1">
                          <Sparkles className="h-3 w-3 text-green-800 drop-shadow-md" fill="currentColor" strokeWidth={2} />
                          {(purchase.original_price - purchase.total_cost).toLocaleString('pt-BR')}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Ações */}
                  <div className="flex justify-end pt-2">
                    <PurchaseDetailsModal purchase={purchase} />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
} 