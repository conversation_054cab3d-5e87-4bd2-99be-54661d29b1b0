/**
 * ItemForm - Formulário para criar/editar itens estratégicos
 * <AUTHOR> Internet 2025
 */
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import {
  Gift,
  Star,
  Flame,
  Plus,
  Coins,
  TrendingUp,
  Package,
  Users,
  Eye,
  EyeOff
} from "lucide-react";
import { 
  useStrategicCategories, 
  useCreateStrategicItem, 
  useUpdateStrategicItem 
} from "@/lib/query/hooks/useStrategicMarketplace";
import { successWithNotification, errorWithNotification } from "@/lib/notifications/toastWithNotification";

// Schema de validação
const itemFormSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório").max(100, "Nome muito longo"),
  description: z.string().min(1, "Descrição é obrigatória").max(500, "Descrição muito longa"),
  category_id: z.string().min(1, "Categoria é obrigatória"),
  price: z.number().min(1, "Preço deve ser maior que 0"),
  level_required: z.number().min(1, "Nível deve ser maior que 0"),
  active: z.boolean().default(true),
  is_popular: z.boolean().default(false),
  is_hot: z.boolean().default(false),
  max_quantity_per_user: z.number().optional(),
  total_stock: z.number().optional(),
  current_stock: z.number().optional(),
  metadata: z.record(z.any()).default({}),
});

type ItemFormValues = z.infer<typeof itemFormSchema>;

interface ItemFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  item?: any; // Para edição
  preselectedCategoryId?: string | null; // Categoria pré-selecionada
  onSuccess?: () => void;
}

export function ItemForm({ open, onOpenChange, item, preselectedCategoryId, onSuccess }: ItemFormProps) {
  const [hasStock, setHasStock] = useState(!!item?.total_stock);
  const [hasUserLimit, setHasUserLimit] = useState(!!item?.max_quantity_per_user);

  const { data: categories = [] } = useStrategicCategories();
  const createMutation = useCreateStrategicItem();
  const updateMutation = useUpdateStrategicItem();

  const form = useForm<ItemFormValues>({
    resolver: zodResolver(itemFormSchema),
    defaultValues: {
      name: item?.name || "",
      description: item?.description || "",
      category_id: item?.category_id || "",
      price: item?.price || 100,
      level_required: item?.level_required || 1,
      active: item?.active ?? true,
      is_popular: item?.is_popular || false,
      is_hot: item?.is_hot || false,
      max_quantity_per_user: item?.max_quantity_per_user || undefined,
      total_stock: item?.total_stock || undefined,
      current_stock: item?.current_stock || undefined,
      metadata: item?.metadata || {},
    },
  });

  const isEditing = !!item;
  const isLoading = createMutation.isPending || updateMutation.isPending;

  // Reset do formulário quando o item muda ou o modal abre/fecha
  useEffect(() => {
    if (open) {
      // Reset com os valores do item ou valores padrão
      // Se tem categoria pré-selecionada e não está editando, usar ela
      const categoryId = item?.category_id || preselectedCategoryId || "";
      
      form.reset({
        name: item?.name || "",
        description: item?.description || "",
        category_id: categoryId,
        price: item?.price || 100,
        level_required: item?.level_required || 1,
        active: item?.active ?? true,
        is_popular: item?.is_popular || false,
        is_hot: item?.is_hot || false,
        max_quantity_per_user: item?.max_quantity_per_user || undefined,
        total_stock: item?.total_stock || undefined,
        current_stock: item?.current_stock || undefined,
        metadata: item?.metadata || {},
      });
      
      // Resetar estados dos switches
      setHasStock(!!item?.total_stock);
      setHasUserLimit(!!item?.max_quantity_per_user);
    }
  }, [open, item, preselectedCategoryId, form]);

  const onSubmit = async (values: ItemFormValues) => {
    try {
      // Remover campos de estoque se não estiver habilitado
      const finalValues = {
        ...values,
        max_quantity_per_user: hasUserLimit ? values.max_quantity_per_user : null,
        total_stock: hasStock ? values.total_stock : null,
        current_stock: hasStock ? (values.current_stock ?? values.total_stock) : null,
      };

      if (isEditing) {
        await updateMutation.mutateAsync({
          id: item.id,
          ...finalValues,
        });
        successWithNotification("Item atualizado com sucesso!");
      } else {
        await createMutation.mutateAsync(finalValues);
        successWithNotification("Item criado com sucesso!");
      }
      
      onSuccess?.();
      onOpenChange(false);
      form.reset();
    } catch (error) {
      console.error("Erro ao salvar item:", error);
      errorWithNotification(
        isEditing ? "Erro ao atualizar item" : "Erro ao criar item"
      );
    }
  };

  const selectedCategory = categories.find(cat => cat.id === form.watch("category_id"));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className={`p-2 rounded-lg bg-gradient-to-r ${selectedCategory?.gradient || 'from-purple-500 to-pink-600'}`}>
              <Gift className="h-5 w-5 text-white" />
            </div>
            {isEditing ? "Editar Item" : "Novo Item Estratégico"}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? "Atualize as informações do item estratégico"
              : "Crie um novo benefício para o marketplace estratégico"
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Informações Básicas */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Gift className="h-5 w-5" />
                Informações Básicas
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Nome */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Nome do Item</FormLabel>
                      <FormControl>
                        <Input placeholder="Ex: Day Off Extra" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Categoria */}
                <FormField
                  control={form.control}
                  name="category_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Categoria</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                        disabled={!!preselectedCategoryId && !isEditing}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione uma categoria" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              <div className="flex items-center gap-2">
                                <div className={`w-3 h-3 rounded bg-gradient-to-r ${category.gradient}`} />
                                {category.name}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {!!preselectedCategoryId && !isEditing && (
                        <FormDescription className="text-blue-600">
                          Categoria selecionada automaticamente
                        </FormDescription>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Preço */}
                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preço (Stardust)</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Coins className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-yellow-500" />
                          <Input 
                            type="number" 
                            min="1"
                            placeholder="100"
                            className="pl-10"
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Descrição */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descrição</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Descreva os benefícios e detalhes deste item..."
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            {/* Configurações de Acesso */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Users className="h-5 w-5" />
                Configurações de Acesso
              </h3>

              <FormField
                control={form.control}
                name="level_required"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nível Mínimo Necessário</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <TrendingUp className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-blue-500" />
                        <Input 
                          type="number" 
                          min="1"
                          placeholder="1"
                          className="pl-10"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      Nível mínimo de gamificação necessário para comprar este item
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            {/* Controle de Estoque */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Package className="h-5 w-5" />
                Controle de Estoque
              </h3>

              {/* Limite por usuário */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Limite por usuário</h4>
                    <p className="text-sm text-muted-foreground">
                      Restringir quantas vezes cada usuário pode comprar este item
                    </p>
                  </div>
                  <Switch
                    checked={hasUserLimit}
                    onCheckedChange={setHasUserLimit}
                  />
                </div>

                {hasUserLimit && (
                  <FormField
                    control={form.control}
                    name="max_quantity_per_user"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Quantidade máxima por usuário</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="1"
                            placeholder="1"
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>

              {/* Estoque total */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Estoque limitado</h4>
                    <p className="text-sm text-muted-foreground">
                      Definir quantidade total disponível deste item
                    </p>
                  </div>
                  <Switch
                    checked={hasStock}
                    onCheckedChange={setHasStock}
                  />
                </div>

                {hasStock && (
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="total_stock"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Estoque total</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0"
                              placeholder="100"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="current_stock"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Estoque atual</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="0"
                              placeholder="100"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                            />
                          </FormControl>
                          <FormDescription>
                            Deixe vazio para usar o estoque total
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {/* Configurações de Destaque */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Star className="h-5 w-5" />
                Configurações de Destaque
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Item Ativo */}
                <FormField
                  control={form.control}
                  name="active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">
                          {field.value ? <Eye className="h-4 w-4 inline mr-1" /> : <EyeOff className="h-4 w-4 inline mr-1" />}
                          Ativo
                        </FormLabel>
                        <FormDescription className="text-xs">
                          Visível no marketplace
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* Item Popular */}
                <FormField
                  control={form.control}
                  name="is_popular"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">
                          <Star className="h-4 w-4 inline mr-1 text-orange-500" />
                          Popular
                        </FormLabel>
                        <FormDescription className="text-xs">
                          Badge "Popular"
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* Item Hot */}
                <FormField
                  control={form.control}
                  name="is_hot"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">
                          <Flame className="h-4 w-4 inline mr-1 text-red-500" />
                          Hot
                        </FormLabel>
                        <FormDescription className="text-xs">
                          Badge "Hot"
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Preview do Item */}
            {form.watch("name") && selectedCategory && (
              <>
                <Separator />
                <div className="space-y-2">
                  <FormLabel>Preview</FormLabel>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="p-4 rounded-xl border bg-gradient-to-br from-slate-50 to-white"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3">
                        <div className={`p-3 rounded-xl bg-gradient-to-r ${selectedCategory.gradient}`}>
                          <Gift className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-semibold">{form.watch("name")}</h4>
                            {form.watch("is_popular") && (
                              <Badge className="bg-orange-100 text-orange-700 text-xs">
                                <Star className="h-3 w-3 mr-1" />
                                Popular
                              </Badge>
                            )}
                            {form.watch("is_hot") && (
                              <Badge className="bg-red-100 text-red-700 text-xs">
                                <Flame className="h-3 w-3 mr-1" />
                                Hot
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mb-2">
                            {form.watch("description")}
                          </p>
                          <div className="flex items-center gap-3 text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                              <Coins className="h-3 w-3 text-yellow-500" />
                              {form.watch("price")} Stardust
                            </span>
                            <span className="flex items-center gap-1">
                              <TrendingUp className="h-3 w-3 text-blue-500" />
                              Nível {form.watch("level_required")}
                            </span>
                            {hasStock && form.watch("total_stock") && (
                              <span className="flex items-center gap-1">
                                <Package className="h-3 w-3 text-purple-500" />
                                {form.watch("current_stock") || form.watch("total_stock")} disponíveis
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <Badge variant={form.watch("active") ? "default" : "secondary"}>
                        {form.watch("active") ? "Ativo" : "Inativo"}
                      </Badge>
                    </div>
                  </motion.div>
                </div>
              </>
            )}

            {/* Botões de Ação */}
            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    {isEditing ? "Atualizando..." : "Criando..."}
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    {isEditing ? "Atualizar Item" : "Criar Item"}
                  </div>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}