/**
 * Siste<PERSON> de botões laterais separados: Post-its e Quick Actions.
 * Dois botões independentes com painéis focados.
 * <AUTHOR> Internet 2025
 */
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  StickyNote, 
  Plus, 
  Eye, 
  EyeOff, 
  Calendar,
  Search,
  MessageCircle,
  Home,
  CheckSquare,
  BookOpen,
  Zap
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { usePostIt } from "@/contexts/PostItContext";
import { useCompany } from "@/hooks/useCompany";
import { useProfile } from "@/hooks/useProfile";
import { toast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import { AbsenceRegistrationDialog } from "@/components/people/AbsenceRegistrationDialog";

// Componente Post-it Button
function PostItButton() {
  const [isOpen, setIsOpen] = useState(false);
  
  const { 
    postIts, 
    createPostIt, 
    isVisible, 
    toggleVisibility,
    togglePostItVisibility,
    hiddenPostIts,
    isLoading: isLoadingPostIts
  } = usePostIt();
  
  const { company, isLoading: isLoadingCompany } = useCompany();
  const { profile, loading: isLoadingProfile } = useProfile();

  const colors = ['#ffeb3b', '#4caf50', '#2196f3', '#e91e63', '#9c27b0'];
  let lastColorIndex = 0;

  const createNewPostIt = async () => {
    try {
      if (!company?.id || !profile?.id) {
        toast({
          title: "Erro",
          description: "Não foi possível criar o post-it: dados necessários não encontrados",
          variant: "destructive"
        });
        return;
      }

      const colorIndex = lastColorIndex;
      lastColorIndex = (colorIndex + 1) % colors.length;

      const x = Math.max(0, (window.innerWidth / 2) - 100);
      const y = Math.max(0, (window.innerHeight / 2) - 100);

      await createPostIt({
        company_id: company.id,
        author_id: profile.id,
        content: '',
        color: colors[colorIndex],
        x,
        y,
        width: 260,
        height: 280,
        rotation: 0,
        is_fixed: false,
        is_public: false,
      });

      setIsOpen(false);
    } catch (error) {
      console.error('Erro ao criar post-it:', error);
      toast({
        title: "Erro",
        description: "Não foi possível criar o post-it",
        variant: "destructive"
      });
    }
  };

  const isLoading = isLoadingPostIts || isLoadingCompany || isLoadingProfile;

  return (
    <div className="relative">
      {/* Painel do Post-it - Posicionado absolutamente */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 280, opacity: 1 }}
            exit={{ width: 0, opacity: 0 }}
            transition={{ duration: 0.2, ease: "easeInOut" }}
            className="absolute right-16 top-1/2 transform -translate-y-1/2 bg-background/95 backdrop-blur-sm border-t border-b border-l rounded-l-lg shadow-xl overflow-hidden"
            style={{ minHeight: '300px', maxHeight: '450px' }}
          >
            <div className="p-4 h-full flex flex-col">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <StickyNote className="h-5 w-5 text-yellow-600" />
                  <h3 className="font-semibold">Post-its</h3>
                </div>
                <Badge variant="secondary">{postIts.length}</Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-3 mb-4">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        onClick={createNewPostIt} 
                        className="w-full"
                        disabled={isLoading}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Novo
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Criar novo post-it (Ctrl+Shift+P)</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        onClick={() => toggleVisibility()}
                        className="w-full"
                        disabled={isLoading && postIts.length === 0}
                      >
                        {isVisible ? <Eye className="h-4 w-4 mr-2" /> : <EyeOff className="h-4 w-4 mr-2" />}
                        {isVisible ? 'Ocultar' : 'Mostrar'}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{isVisible ? 'Ocultar' : 'Mostrar'} post-its (Alt+H)</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              {postIts.length > 0 && (
                <div className="flex-1 flex flex-col space-y-2 min-h-0">
                  <div className="text-sm text-muted-foreground border-b pb-2">
                    Post-its ativos
                  </div>
                  <div className="flex-1 overflow-y-auto space-y-2 min-h-0 max-h-48">
                    {postIts.map((postIt) => (
                      <motion.div
                        key={postIt.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="p-3 border rounded-md cursor-pointer hover:bg-accent transition-colors"
                        style={{ borderLeftColor: postIt.color, borderLeftWidth: 4 }}
                        onClick={async () => {
                          await togglePostItVisibility(postIt.id);
                        }}
                      >
                        <div className="text-sm truncate font-medium">
                          {postIt.content || 'Post-it em branco'}
                        </div>
                        <div className="text-xs text-muted-foreground mt-1 flex items-center justify-between">
                          <span>{postIt.is_public ? 'Público' : 'Privado'}</span>
                          <span>{hiddenPostIts.has(postIt.id) ? 'Oculto' : 'Visível'}</span>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {postIts.length === 0 && !isLoading && (
                <div className="flex-1 flex flex-col items-center justify-center text-center text-muted-foreground">
                  <StickyNote className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p className="text-sm">Nenhum post-it criado</p>
                  <p className="text-xs mt-1">Clique em "Novo" para começar</p>
                </div>
              )}

              {isLoading && postIts.length === 0 && (
                <div className="flex-1 flex flex-col items-center justify-center text-center text-muted-foreground">
                  <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full mx-auto mb-3"></div>
                  <p className="text-sm">Carregando post-its...</p>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Botão Post-it - Visual Realista - POSIÇÃO FIXA */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                "w-12 h-12 p-0 border-none shadow-xl transition-all duration-300",
                "bg-gradient-to-br from-yellow-200 via-yellow-300 to-yellow-400",
                "rounded-tl-sm rounded-tr-sm rounded-bl-3xl rounded-br-sm",
                "transform -rotate-2 hover:rotate-0 hover:scale-105",
                "shadow-[0_2px_4px_rgba(0,0,0,0.1),0_4px_8px_rgba(0,0,0,0.1),inset_0_1px_0_rgba(255,255,255,0.3)]",
                "hover:shadow-[0_6px_12px_rgba(0,0,0,0.15),0_8px_16px_rgba(0,0,0,0.1)]",
                "relative"
              )}
              onClick={() => setIsOpen(!isOpen)}
            >
              <div className="absolute top-0 right-0 w-3 h-3 bg-gradient-to-bl from-transparent via-yellow-600 to-transparent opacity-30 rounded-tr-sm" />
              
              <div className="flex items-center justify-center text-yellow-700">
                <StickyNote className="h-4 w-4" />
              </div>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="left">
            <p>Post-its & Notas</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}

// Componente Quick Actions Button
function QuickActionsButton() {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();

  const quickActions = [
    {
      id: 'absence',
      label: 'Registrar Ausência',
      icon: Calendar,
      component: (
        <AbsenceRegistrationDialog
          trigger={
            <Button
              variant="ghost"
              className="w-full justify-start h-auto p-3 hover:bg-accent"
            >
              <Calendar className="h-4 w-4 mr-3 text-green-600" />
              <div className="text-left">
                <div className="text-sm font-medium">Registrar Ausência</div>
              </div>
            </Button>
          }
          onSuccess={() => {
            setIsOpen(false);
            toast({
              title: "Ausência registrada",
              description: "Sua ausência foi registrada com sucesso",
            });
          }}
        />
      ),
      description: 'Registre férias, licenças ou faltas'
    }
  ];


  return (
    <>
      <div className="relative">
        {/* Painel do Quick Actions - Posicionado absolutamente */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 320, opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              transition={{ duration: 0.2, ease: "easeInOut" }}
              className="absolute right-16 top-1/2 transform -translate-y-1/2 bg-background/95 backdrop-blur-sm border-t border-b border-l rounded-l-lg shadow-xl overflow-hidden"
              style={{ minHeight: '450px', maxHeight: '600px' }}
            >
              <div className="p-4 h-full flex flex-col overflow-y-auto">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-blue-600" />
                    <h3 className="font-semibold">Quick Actions</h3>
                  </div>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    {quickActions.length}
                  </Badge>
                </div>

                {/* Ações Rápidas */}
                <div className="mb-6">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <h4 className="text-sm font-medium text-muted-foreground">Ações Rápidas</h4>
                  </div>
                  <div className="space-y-2">
                    {quickActions.map((action) => (
                      <TooltipProvider key={action.id}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            {action.component}
                          </TooltipTrigger>
                          <TooltipContent side="left">
                            <p>{action.description}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Botão Quick Actions - Visual Moderno - POSIÇÃO FIXA */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                className={cn(
                  "w-12 h-12 p-0 border-none shadow-xl transition-all duration-300",
                  "bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600",
                  "rounded-lg",
                  "hover:scale-105 hover:shadow-2xl",
                  "shadow-[0_4px_12px_rgba(59,130,246,0.3)]",
                  "hover:shadow-[0_8px_24px_rgba(59,130,246,0.4)]"
                )}
                onClick={() => setIsOpen(!isOpen)}
              >
                <div className="flex items-center justify-center text-white">
                  <Zap className="h-4 w-4" />
                </div>
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Ações Rápidas</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </>
  );
}

// Componente principal que renderiza os 2 botões verticalmente
export function SidebarTools() {
  return (
    <div className="fixed right-4 z-[99999]" style={{ top: '30%' }}>
      <div className="flex flex-col items-center gap-3">
        {/* Post-it Button - Posição Superior */}
        <PostItButton />
        
        {/* Quick Actions Button - Posição Inferior */}
        <QuickActionsButton />
      </div>
    </div>
  );
} 