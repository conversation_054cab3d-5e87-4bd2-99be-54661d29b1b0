/**
 * Componente avançado para botões de atualização com funcionalidades completas
 * Inclui feedback sonoro, timeout, tempo mínimo de display e estados avançados
 * <AUTHOR> Internet 2025
 */
import { useState, forwardRef } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { cn } from "@/lib/utils";
import { playSound, SoundEffects } from '@/lib/sound-effects';
import { successWithNotification, errorWithNotification } from "@/lib/notifications/toastWithNotification";

interface AdvancedRefreshButtonProps {
  /**
   * Função chamada ao clicar no botão
   */
  onRefresh: () => void | Promise<void>;
  
  /**
   * Texto do botão quando não está atualizando
   * @default "Atualizar"
   */
  children?: React.ReactNode;
  
  /**
   * Texto do botão quando está atualizando
   * @default "Atualizando..."
   */
  refreshingText?: React.ReactNode;
  
  /**
   * Variante do botão
   * @default "outline"
   */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  
  /**
   * Tamanho do botão
   * @default "default"
   */
  size?: "default" | "sm" | "lg" | "icon";
  
  /**
   * Classes CSS adicionais
   */
  className?: string;
  
  /**
   * Tempo mínimo de exibição do estado "Atualizando..." em ms
   * @default 2000
   */
  minimumDisplayTime?: number;
  
  /**
   * Tempo limite para timeout em ms
   * @default 8000
   */
  timeout?: number;
  
  /**
   * Se deve reproduzir som de feedback
   * @default true
   */
  enableSound?: boolean;
  
  /**
   * Se deve mostrar notificação de sucesso
   * @default true
   */
  showSuccessNotification?: boolean;
  
  /**
   * Mensagem personalizada de sucesso
   * @default "Dados atualizados!"
   */
  successMessage?: string;
  
  /**
   * Descrição da notificação de sucesso
   * @default "Os dados foram atualizados com sucesso"
   */
  successDescription?: string;
  
  /**
   * Mensagem personalizada de erro
   * @default "Erro ao atualizar"
   */
  errorMessage?: string;
  
  /**
   * Descrição da notificação de erro
   * @default "Ocorreu um erro durante a atualização"
   */
  errorDescription?: string;
  
  /**
   * Se o botão está desabilitado externamente
   */
  disabled?: boolean;
  
  /**
   * Tooltip personalizado
   */
  title?: string;
  
  /**
   * Nome da operação para logs
   * @default "Refresh"
   */
  operationName?: string;
}

export const AdvancedRefreshButton = forwardRef<HTMLButtonElement, AdvancedRefreshButtonProps>(
  ({
    onRefresh,
    children = "Atualizar",
    refreshingText = "Atualizando...",
    variant = "outline",
    size = "default",
    className,
    minimumDisplayTime = 2000,
    timeout = 8000,
    enableSound = true,
    showSuccessNotification = true,
    successMessage = "Dados atualizados!",
    successDescription = "Os dados foram atualizados com sucesso",
    errorMessage = "Erro ao atualizar",
    errorDescription = "Ocorreu um erro durante a atualização",
    disabled = false,
    title = "Clique para atualizar os dados",
    operationName = "Refresh",
    ...props
  }, ref) => {
    
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [showRefreshingText, setShowRefreshingText] = useState(false);
    
    const handleAdvancedRefresh = async (): Promise<void> => {
      const startTime = Date.now();
      
      try {
        setIsRefreshing(true);
        setShowRefreshingText(true);
        
        console.log(`${operationName}: Iniciando atualização manual dos dados`);
        
        // Reproduzir feedback sonoro
        if (enableSound) {
          playSound(SoundEffects.REFRESH);
        }
        
        // Configurar tempo mínimo para o texto "Atualizando..."
        const minimumDisplayPromise = new Promise(resolve => 
          setTimeout(resolve, minimumDisplayTime)
        );
        
        // Configurar timeout para evitar espera infinita
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error(`Timeout ao atualizar ${operationName.toLowerCase()}`)), timeout)
        );
        
        // Executar o refresh
        const refreshPromise = Promise.race([
          onRefresh(),
          timeoutPromise,
        ]);
        
        // Aguardar tanto o refresh quanto o tempo mínimo
        await Promise.all([refreshPromise, minimumDisplayPromise]);
        
        console.log(`${operationName}: Dados atualizados com sucesso`);
        
        // Mostrar notificação de sucesso
        if (showSuccessNotification) {
          successWithNotification(successMessage, {
            description: successDescription,
            persist: false
          });
        }
        
      } catch (error) {
        console.error(`${operationName}: Erro ao atualizar dados`, error);
        
        // Mesmo em erro, aguardar o tempo mínimo se ainda não passou
        const timeElapsed = Date.now() - startTime;
        if (timeElapsed < minimumDisplayTime) {
          await new Promise(resolve => 
            setTimeout(resolve, minimumDisplayTime - timeElapsed)
          );
        }
        
        // Mostrar notificação de erro
        errorWithNotification(errorMessage, {
          description: errorDescription,
          persist: false
        });
        
      } finally {
        setIsRefreshing(false);
        setShowRefreshingText(false);
      }
    };

    return (
      <motion.div
        whileHover={!isRefreshing ? { scale: 1.02 } : {}}
        whileTap={!isRefreshing ? { scale: 0.98 } : {}}
        transition={{ duration: 0.1, ease: "easeInOut" }}
      >
        <Button
          ref={ref}
          variant={variant}
          size={size}
          onClick={handleAdvancedRefresh}
          disabled={disabled || isRefreshing}
          className={cn("transition-all duration-200", className)}
          title={title}
          {...props}
        >
          <motion.div
            animate={isRefreshing ? { rotate: 360 } : { rotate: 0 }}
            transition={
              isRefreshing
                ? { duration: 2, repeat: Infinity, ease: "linear" }
                : { duration: 0.2 }
            }
            className="mr-2"
          >
            <RefreshCw className={cn(
              "h-4 w-4",
              size === "sm" && "h-3 w-3",
              size === "lg" && "h-5 w-5",
              size === "icon" && "h-4 w-4"
            )} />
          </motion.div>
          {showRefreshingText ? refreshingText : children}
        </Button>
      </motion.div>
    );
  }
);

AdvancedRefreshButton.displayName = "AdvancedRefreshButton";

// Variantes pré-configuradas para casos específicos
export const AdvancedRefreshButtonIcon = forwardRef<HTMLButtonElement, Omit<AdvancedRefreshButtonProps, 'children' | 'size'>>(
  (props, ref) => (
    <AdvancedRefreshButton 
      ref={ref} 
      size="icon" 
      title="Atualizar dados"
      {...props}
    >
      <span className="sr-only">Atualizar</span>
    </AdvancedRefreshButton>
  )
);

AdvancedRefreshButtonIcon.displayName = "AdvancedRefreshButtonIcon";

export const AdvancedRefreshButtonLarge = forwardRef<HTMLButtonElement, AdvancedRefreshButtonProps>(
  (props, ref) => (
    <AdvancedRefreshButton 
      ref={ref} 
      size="lg" 
      variant="default"
      {...props}
    />
  )
);

AdvancedRefreshButtonLarge.displayName = "AdvancedRefreshButtonLarge";

export const AdvancedRefreshButtonGhost = forwardRef<HTMLButtonElement, AdvancedRefreshButtonProps>(
  (props, ref) => (
    <AdvancedRefreshButton 
      ref={ref} 
      variant="ghost"
      {...props}
    />
  )
);

AdvancedRefreshButtonGhost.displayName = "AdvancedRefreshButtonGhost";

// Hook personalizado para facilitar o uso
export const useAdvancedRefresh = (
  refreshFunction: () => Promise<void>,
  options?: {
    minimumDisplayTime?: number;
    timeout?: number;
    enableSound?: boolean;
    operationName?: string;
  }
) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showRefreshingText, setShowRefreshingText] = useState(false);

  const {
    minimumDisplayTime = 2000,
    timeout = 8000,
    enableSound = true,
    operationName = "Operation"
  } = options || {};

  const executeRefresh = async (): Promise<void> => {
    const startTime = Date.now();
    
    try {
      setIsRefreshing(true);
      setShowRefreshingText(true);
      
      console.log(`${operationName}: Iniciando atualização manual`);
      
      if (enableSound) {
        playSound(SoundEffects.REFRESH);
      }
      
      const minimumDisplayPromise = new Promise(resolve => 
        setTimeout(resolve, minimumDisplayTime)
      );
      
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error(`Timeout na operação ${operationName}`)), timeout)
      );
      
      const refreshPromise = Promise.race([
        refreshFunction(),
        timeoutPromise,
      ]);
      
      await Promise.all([refreshPromise, minimumDisplayPromise]);
      
      console.log(`${operationName}: Operação concluída com sucesso`);
      
    } catch (error) {
      console.error(`${operationName}: Erro na operação`, error);
      
      const timeElapsed = Date.now() - startTime;
      if (timeElapsed < minimumDisplayTime) {
        await new Promise(resolve => 
          setTimeout(resolve, minimumDisplayTime - timeElapsed)
        );
      }
      
      throw error;
      
    } finally {
      setIsRefreshing(false);
      setShowRefreshingText(false);
    }
  };

  return { 
    executeRefresh, 
    isRefreshing, 
    showRefreshingText 
  };
};