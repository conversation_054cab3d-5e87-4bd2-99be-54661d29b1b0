/**
 * Componente de Debug do Sistema de Cache Centralizado
 * <AUTHOR> Internet 2025
 * @description Interface de debug para testar o novo sistema em desenvolvimento
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Database, 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  Bug,
  CheckCircle,
  XCircle,
  Clock,
  Info
} from 'lucide-react';
import { useCacheSystemDebug } from '@/lib/query/hooks/useCacheSystemTest';

/**
 * Componente principal de debug
 * Só renderiza em desenvolvimento
 */
export function CacheSystemDebug() {
  const [isVisible, setIsVisible] = useState(false);
  const debug = useCacheSystemDebug();

  // Não renderizar em produção
  if (import.meta.env.PROD) {
    return null;
  }

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-blue-500 text-white hover:bg-blue-600"
        >
          <Bug className="h-4 w-4 mr-2" />
          Cache Debug
        </Button>
      </div>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'loading':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-[600px] overflow-hidden">
      <Card className="shadow-lg border-blue-200">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Database className="h-4 w-4" />
              Cache System Debug
            </CardTitle>
            <div className="flex gap-2">
              <Badge variant={debug.isOnline ? "default" : "destructive"} className="text-xs">
                {debug.isOnline ? (
                  <><Wifi className="h-3 w-3 mr-1" /> Online</>
                ) : (
                  <><WifiOff className="h-3 w-3 mr-1" /> Offline</>
                )}
              </Badge>
              <Button
                onClick={() => setIsVisible(false)}
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
              >
                ×
              </Button>
            </div>
          </div>
          <div className="text-xs text-muted-foreground">
            {debug.getStatusMessage()}
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <Tabs defaultValue="status" className="w-full">
            <TabsList className="grid w-full grid-cols-3 text-xs h-8">
              <TabsTrigger value="status" className="text-xs">Status</TabsTrigger>
              <TabsTrigger value="queries" className="text-xs">Queries</TabsTrigger>
              <TabsTrigger value="actions" className="text-xs">Actions</TabsTrigger>
            </TabsList>

            <TabsContent value="status" className="space-y-3 mt-3">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span>Cache Service</span>
                  {debug.cacheService ? (
                    <Badge variant="default" className="text-xs">Ativo</Badge>
                  ) : (
                    <Badge variant="destructive" className="text-xs">Inativo</Badge>
                  )}
                </div>

                <div className="flex items-center justify-between text-xs">
                  <span>System Ready</span>
                  {debug.isSystemReady ? (
                    <Badge variant="default" className="text-xs">Ready</Badge>
                  ) : (
                    <Badge variant="secondary" className="text-xs">Loading</Badge>
                  )}
                </div>

                <div className="flex items-center justify-between text-xs">
                  <span>Network</span>
                  <Badge variant={debug.isOnline ? "default" : "destructive"} className="text-xs">
                    {debug.isOnline ? 'Online' : 'Offline'}
                  </Badge>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="queries" className="space-y-3 mt-3">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span>Basic Test</span>
                  <div className="flex items-center gap-1">
                    {getStatusIcon(debug.testQuery.status)}
                    <span className="text-xs">{debug.testQuery.status}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between text-xs">
                  <span>User Test</span>
                  <div className="flex items-center gap-1">
                    {getStatusIcon(debug.userTestQuery.status)}
                    <span className="text-xs">{debug.userTestQuery.status}</span>
                  </div>
                </div>

                {debug.testQuery.data && (
                  <div className="text-xs bg-gray-50 dark:bg-gray-800 p-2 rounded">
                    <div className="font-mono text-xs">
                      {debug.testQuery.data.message}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="actions" className="space-y-3 mt-3">
              <div className="grid grid-cols-2 gap-2">
                <Button
                  onClick={debug.debug}
                  variant="outline"
                  size="sm"
                  className="text-xs"
                >
                  <Bug className="h-3 w-3 mr-1" />
                  Console Log
                </Button>

                <Button
                  onClick={debug.refetchTests}
                  variant="outline"
                  size="sm"
                  className="text-xs"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Refetch
                </Button>

                <Button
                  onClick={debug.testOffline}
                  variant="outline"
                  size="sm"
                  className="text-xs col-span-2"
                >
                  <WifiOff className="h-3 w-3 mr-1" />
                  Test Offline Mode
                </Button>

                <Button
                  onClick={debug.testCacheOperations}
                  variant="outline"
                  size="sm"
                  className="text-xs col-span-2"
                >
                  <Activity className="h-3 w-3 mr-1" />
                  Test Cache Ops
                </Button>
              </div>

              <div className="text-xs text-muted-foreground mt-2">
                Abra o console para ver logs detalhados
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Hook para adicionar o componente de debug
 * Usar em desenvolvimento para testar o sistema
 */
export function useCacheSystemDebugUI() {
  return {
    DebugComponent: CacheSystemDebug,
    isEnabled: import.meta.env.DEV,
  };
}