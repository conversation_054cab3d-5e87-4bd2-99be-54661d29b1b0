/**
 * Widget Lista de Tenants para o painel administrativo
 * <AUTHOR> Internet 2025
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Search,
  Filter,
  Download,
  MoreHorizontal,
  CheckCircle2,
  XCircle,
  AlarmClock
} from 'lucide-react';
import { useTenantList } from '@/lib/query/hooks/useTenantList';
import { useUpdateCompany } from '@/lib/query/hooks/useUpdateCompany';
import { Skeleton } from '@/components/ui/skeleton';
import { TenantEditSheet } from './TenantEditSheet';
import { TenantPlanSheet } from './TenantPlanSheet';
import { TenantDetailSheet } from './TenantDetailSheet';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card } from '@/components/ui/card';



const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  }
};

export function TenantListWidget() {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [planFilter, setPlanFilter] = useState('all');
  const [selectedTenant, setSelectedTenant] = useState<any>(null);
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);
  const [isPlanSheetOpen, setIsPlanSheetOpen] = useState(false);
  const [isDetailSheetOpen, setIsDetailSheetOpen] = useState(false);
  
  const { data: tenants, isLoading, isError } = useTenantList();
  const updateCompanyMutation = useUpdateCompany();

  const handleEditTenant = (tenant: any) => {
    setSelectedTenant(tenant);
    setIsEditSheetOpen(true);
  };

  const handleManagePlan = (tenant: any) => {
    setSelectedTenant(tenant);
    setIsPlanSheetOpen(true);
  };

  const handleViewDetails = (tenant: any) => {
    setSelectedTenant(tenant);
    setIsDetailSheetOpen(true);
  };

  // **CORREÇÃO CRÍTICA**: Reset pointer-events para evitar travamento da tela
  const resetPointerEvents = () => {
    setTimeout(() => {
      document.body.style.pointerEvents = 'auto';
    }, 100);
  };

  const handleCloseEditSheet = () => {
    setIsEditSheetOpen(false);
    setSelectedTenant(null);
    resetPointerEvents();
  };

  const handleClosePlanSheet = () => {
    setIsPlanSheetOpen(false);
    setSelectedTenant(null);
    resetPointerEvents();
  };

  const handleCloseDetailSheet = () => {
    setIsDetailSheetOpen(false);
    setSelectedTenant(null);
    resetPointerEvents();
  };

  const handleSaveTenant = (data: any) => {
    if (!selectedTenant?.id) return;

    updateCompanyMutation.mutate({
      id: selectedTenant.id,
      ...data,
    }, {
      onSuccess: () => {
        setIsEditSheetOpen(false);
        setSelectedTenant(null);
        resetPointerEvents();
      },
      onError: () => {
        // Não fecha o dialog em caso de erro para permitir correções
        resetPointerEvents();
      }
    });
  };

  // Filtrar tenants usando dados reais
  const filteredTenants = (tenants || []).filter(tenant => {
    const matchesSearch = 
      tenant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (tenant.cnpj && tenant.cnpj.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesStatus = statusFilter === 'all' || tenant.status === statusFilter;
    const matchesPlan = planFilter === 'all' || tenant.plan === planFilter;
    
    return matchesSearch && matchesStatus && matchesPlan;
  });

  const getStatusBadge = (status: string, tenant?: any) => {
    // CORREÇÃO: Verificar se há trial ativo via courtesy_period (igual ao CommercialLeads)
    if (status === 'trial' && tenant?.courtesy_period_start) {
      const now = new Date();
      const endDate = new Date(tenant.courtesy_period_end);
      
      if (now <= endDate) {
        const daysLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        return (
          <Badge variant="secondary" className="flex items-center gap-1 bg-blue-50 text-blue-700 border-blue-200">
            <AlarmClock className="h-3 w-3" /> 
            Trial ({daysLeft}d)
          </Badge>
        );
      }
    }
    
    switch (status) {
      case 'active':
        return (
          <Badge variant="default" className="flex items-center gap-1 bg-green-500 hover:bg-green-600">
            <CheckCircle2 className="h-3 w-3" /> Ativo
          </Badge>
        );
      case 'trial':
        return (
          <Badge variant="secondary" className="flex items-center gap-1 bg-yellow-500 text-white hover:bg-yellow-600">
            <AlarmClock className="h-3 w-3" /> Trial
          </Badge>
        );
      case 'suspended':
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <XCircle className="h-3 w-3" /> Suspenso
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <XCircle className="h-3 w-3" /> Cancelado
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPlanBadge = (plan: string, tenant?: any) => {
    // Se está em trial e tem plano específico selecionado, mostrar diferentemente
    if (tenant?.status === 'trial' && tenant?.selected_plan_info) {
      return (
        <Badge variant="outline" className="border-purple-500 text-purple-500 bg-purple-50">
          {plan} (Trial)
        </Badge>
      );
    }
    
    const className = 
      plan === 'Grátis' ? 'border-blue-500 text-blue-500' : 
      plan === 'Pro' ? 'border-purple-500 text-purple-500' : 
      plan === 'Max' ? 'border-green-500 text-green-500' :
      'border-gray-500 text-gray-500'; // Para N/A ou outros

    return (
      <Badge variant="outline" className={className}>
        {plan}
      </Badge>
    );
  };

  return (
    <motion.div variants={cardVariants} className="space-y-4">
      {/* Filtros */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative w-full md:w-80">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar por nome, CNPJ ou domínio..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos os...</SelectItem>
              <SelectItem value="active">Ativos</SelectItem>
              <SelectItem value="trial">Trial</SelectItem>
              <SelectItem value="suspended">Suspensos</SelectItem>
              <SelectItem value="cancelled">Cancelados</SelectItem>
            </SelectContent>
          </Select>
                     <Select value={planFilter} onValueChange={setPlanFilter}>
             <SelectTrigger className="w-[150px]">
               <SelectValue placeholder="Plano" />
             </SelectTrigger>
             <SelectContent>
               <SelectItem value="all">Todos os planos</SelectItem>
               <SelectItem value="Grátis">Grátis</SelectItem>
               <SelectItem value="Pro">Pro</SelectItem>
               <SelectItem value="Max">Max</SelectItem>
             </SelectContent>
           </Select>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>
        <Button variant="outline" className="flex items-center gap-2">
          <Download className="h-4 w-4" /> Exportar
        </Button>
      </div>

      {/* Tabela */}
      <Card className="border shadow-sm">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Empresa</TableHead>
              <TableHead>CNPJ</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Plano</TableHead>
              <TableHead>Usuários</TableHead>
              <TableHead>Armazenamento</TableHead>
              <TableHead>Criado em</TableHead>
              <TableHead>Último acesso</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              // Skeleton loading
              [...Array(5)].map((_, index) => (
                <TableRow key={index}>
                  <TableCell><Skeleton className="h-4 w-40" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-8" /></TableCell>
                </TableRow>
              ))
            ) : isError ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-10 text-muted-foreground">
                  Erro ao carregar dados dos tenants. Tente novamente.
                </TableCell>
              </TableRow>
            ) : filteredTenants.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-10 text-muted-foreground">
                  Nenhum tenant encontrado com os filtros atuais.
                </TableCell>
              </TableRow>
            ) : (
              filteredTenants.map((tenant) => (
                <TableRow key={tenant.id}>
                  <TableCell className="font-medium">{tenant.name}</TableCell>
                  <TableCell className="text-muted-foreground">
                    {tenant.cnpj || 'N/A'}
                  </TableCell>
                  <TableCell>{getStatusBadge(tenant.status, tenant)}</TableCell>
                  <TableCell>{getPlanBadge(tenant.plan, tenant)}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <div className="w-full max-w-[80px]">
                        <div className="flex justify-between text-xs">
                          <span>{tenant.users_count}</span>
                          <span>{tenant.users_limit || 'N/A'}</span>
                        </div>
                        {tenant.users_limit > 0 && (
                          <Progress 
                            value={(tenant.users_count / tenant.users_limit) * 100} 
                            className="h-2"
                          />
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <div className="w-full max-w-[80px]">
                        <div className="flex justify-between text-xs">
                          <span>{tenant.storage_used} GB</span>
                          <span>{tenant.storage_limit || 'N/A'} GB</span>
                        </div>
                        {tenant.storage_limit > 0 && (
                          <Progress 
                            value={(tenant.storage_used / tenant.storage_limit) * 100} 
                            className="h-2"
                          />
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {tenant.created_at ? new Date(tenant.created_at).toLocaleDateString('pt-BR') : 'N/A'}
                  </TableCell>
                  <TableCell className={!tenant.last_access ? 'text-muted-foreground' : ''}>
                    {tenant.last_access ? new Date(tenant.last_access).toLocaleDateString('pt-BR') : 'N/A'}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Ações</DropdownMenuLabel>
                        <DropdownMenuItem onSelect={(e) => {
                          e.preventDefault();
                          handleViewDetails(tenant);
                        }}>
                          Ver detalhes
                        </DropdownMenuItem>
                        <DropdownMenuItem onSelect={(e) => {
                          e.preventDefault();
                          handleEditTenant(tenant);
                        }}>
                          Editar informações
                        </DropdownMenuItem>
                        <DropdownMenuItem onSelect={(e) => {
                          e.preventDefault();
                          handleManagePlan(tenant);
                        }}>
                          Gerenciar plano
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>Acessar como admin</DropdownMenuItem>
                        <DropdownMenuItem>Enviar notificação</DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {tenant.status === 'active' ? (
                          <DropdownMenuItem className="text-destructive">
                            Suspender acesso
                          </DropdownMenuItem>
                        ) : tenant.status === 'suspended' ? (
                          <DropdownMenuItem className="text-green-600">
                            Reativar acesso
                          </DropdownMenuItem>
                        ) : null}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>

      {/* Sheets */}
      <TenantEditSheet
        isOpen={isEditSheetOpen}
        onClose={handleCloseEditSheet}
        tenant={selectedTenant}
        onSave={handleSaveTenant}
        isLoading={updateCompanyMutation.isPending}
      />

      <TenantPlanSheet
        isOpen={isPlanSheetOpen}
        onClose={handleClosePlanSheet}
        tenant={isPlanSheetOpen ? selectedTenant : null}
      />

      <TenantDetailSheet
        isOpen={isDetailSheetOpen}
        onClose={handleCloseDetailSheet}
        tenant={isDetailSheetOpen ? selectedTenant : null}
      />
    </motion.div>
  );
} 