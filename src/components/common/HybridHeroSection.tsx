/**
 * HybridHeroSection - Versão híbrida do HeroSection para mobile nativo
 * Usa sistema CSS nativo no mobile e shadcn/ui no desktop
 * <AUTHOR> Internet 2025
 */

import { motion } from "framer-motion";
import { ReactNode } from "react";
import { LucideIcon } from "lucide-react";
import { usePlatform } from "@/hooks/usePlatform";
import { cn } from "@/lib/utils";

// Sistema CSS nativo - sem dependências externas

interface HybridHeroSectionProps {
  // Conteúdo principal
  title: string;
  description: string;
  icon: LucideIcon;
  
  // Customização visual
  gradientColors?: string;
  iconAnimation?: boolean;
  
  // Ações opcionais no lado direito
  actions?: ReactNode;
  
  // Classes adicionais
  className?: string;
}

// Variantes de animação
const heroVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export function HybridHeroSection({
  title,
  description,
  icon: IconComponent,
  gradientColors = "from-indigo-600 via-purple-600 to-pink-600",
  iconAnimation = true,
  actions,
  className = ""
}: HybridHeroSectionProps) {
  const { isNative, isIOS } = usePlatform();
  
  // Detectar mobile via media query (Chrome mobile não é nativo)
  const isMobile = window?.innerWidth <= 768;

  if (isNative || isMobile) {
    // Versão mobile nativa - muito mais compacta
    return (
      <motion.div 
        variants={heroVariants} 
        initial="hidden"
        animate="visible"
        className={cn("mb-0", className)} // ZERO margin
      >
        {/* Hero Section compacto para mobile */}
        <div className={cn(
          "flex flex-col gap-2 p-3 bg-gradient-to-r text-white sticky top-0 z-[100] shadow-lg transform translate-z-0", // Forçar novo stacking context
          isNative ? "native-card" : "", // Usar native-card apenas no nativo
          gradientColors,
          // Estilos específicos para mobile - mais compacto
          (isIOS && isNative) && "rounded-[var(--native-radius-large)]", // Bordas menores apenas no iOS nativo
          isNative ? "border-0 shadow-[var(--native-shadow-small)] mx-1" : (isMobile ? "rounded-xl shadow-lg mx-0" : "rounded-xl shadow-lg mx-0") // Mobile Chrome: manter bordas arredondadas
        )}>
          <div className="flex items-center gap-2"> {/* Reduzir gap */}
            <motion.div
              animate={iconAnimation ? { rotate: 360 } : {}}
              transition={iconAnimation ? { duration: 20, repeat: Infinity, ease: "linear" } : {}}
              className={cn(
                "p-1.5 bg-white/20 rounded-md", // Reduzir padding e bordas
                (isIOS && isNative) && "rounded-lg" // iOS nativo: bordas menores
              )}
            >
              {IconComponent && <IconComponent className="h-4 w-4 text-white" />} {/* Ícone menor */}
            </motion.div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h1 className={cn(
                  "font-bold text-white drop-shadow-sm", // Volta para bold
                  (isIOS && isNative) ? "text-lg" : "text-base" // Ainda menor para economizar espaço
                )}>
                  {title}
                </h1>
                {/* Ações na mesma linha do título */}
                {actions && (
                  <div className="flex gap-2 ml-2">
                    {actions}
                  </div>
                )}
              </div>
              {/* Mostrar descrição apenas se tiver conteúdo */}
              {description && (
                <p className="text-white/80 text-xs font-normal truncate"> {/* Texto menor e mais sutil */}
                  {description}
                </p>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    );
  }

  // Versão desktop/web padrão
  return (
    <motion.div 
      variants={heroVariants} 
      initial="hidden"
      animate="visible"
      className={`space-y-4 mb-6 ${className}`}
    >
      {/* Header Principal com Design Moderno */}
      <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 p-6 bg-gradient-to-r ${gradientColors} rounded-2xl text-white shadow-xl sticky top-0 z-[100] transform translate-z-0`}>
        <div className="flex items-center gap-4">
          <motion.div
            animate={iconAnimation ? { rotate: 360 } : {}}
            transition={iconAnimation ? { duration: 20, repeat: Infinity, ease: "linear" } : {}}
            className="p-3 bg-white/10 rounded-xl backdrop-blur-sm"
          >
            {IconComponent ? <IconComponent className="h-8 w-8 text-white" /> : <p>Icon Undefined</p>}
          </motion.div>
          <div>
            <h1 className="text-3xl lg:text-4xl font-bold text-white drop-shadow-lg">
              {title}
            </h1>
            <p className="text-white/90 text-lg font-medium">
              {description}
            </p>
          </div>
        </div>
        
        {/* Ações opcionais */}
        {actions && (
          <div className="flex flex-col sm:flex-row items-center gap-4">
            {actions}
          </div>
        )}
      </div>
    </motion.div>
  );
}