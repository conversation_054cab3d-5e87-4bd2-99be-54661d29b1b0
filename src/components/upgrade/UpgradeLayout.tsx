/**
 * Layout para o fluxo de upgrade unificado - Design Premium
 * Interface moderna com HeroSection, gradientes e animações premium
 * <AUTHOR> Internet 2025
 */
import React, { ReactNode, useEffect, useCallback, useMemo, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { HeroSection } from '@/components/common/HeroSection';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useUpgradeStore, useUpgradeContext, useUpgradeProgress } from '@/stores/upgradeStore';
import { useAuthStore } from '@/stores/authStore';
import { 
  UpgradeSource, 
  UpgradeContext, 
  ContextConfig,
  ContextGradients 
} from '@/types/upgrade';
import type { AddOn } from '@/components/upgrade/AddOnSelector';
import {
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  AlertTriangle,
  Loader2,
  CreditCard,
  Users,
  HardDrive,
  Brain,
  Crown,
  Sparkles,
  Clock,
  Shield,
  Phone,
  Star,
  Zap,
  Gift,
  BarChart3,
  Medal,
  TrendingUp
} from 'lucide-react';

// Configurações de contexto por fonte de upgrade
const CONTEXT_CONFIGS: Record<UpgradeSource, ContextConfig> = {
  [UpgradeSource.AI_CREDITS]: {
    title: 'Upgrade de Créditos IA',
    subtitle: 'Desbloqueie o poder completo da inteligência artificial',
    description: 'Seus créditos de IA estão limitados. Faça upgrade para acesso ilimitado às funcionalidades de IA.',
    icon: 'brain',
    urgencyMessage: 'Créditos insuficientes para continuar usando as funcionalidades de IA',
    ctaText: 'Upgrade Agora',
    gradients: {
      from: 'from-purple-600',
      to: 'to-pink-600',
      accent: 'bg-gradient-to-r from-purple-600 to-pink-600'
    }
  },
  [UpgradeSource.STORAGE_FULL]: {
    title: 'Upgrade de Armazenamento',
    subtitle: 'Expanda seu espaço de armazenamento',
    description: 'Seu armazenamento está quase cheio. Adicione mais espaço para continuar subindo arquivos.',
    icon: 'hard-drive',
    urgencyMessage: 'Espaço de armazenamento quase esgotado',
    ctaText: 'Expandir Agora',
    gradients: {
      from: 'from-green-600',
      to: 'to-emerald-600',
      accent: 'bg-gradient-to-r from-green-600 to-emerald-600'
    }
  },
  [UpgradeSource.USERS_LIMIT]: {
    title: 'Upgrade de Usuários',
    subtitle: 'Adicione mais membros à sua equipe',
    description: 'Expanda sua equipe adicionando mais usuários ao seu plano.',
    icon: 'users',
    urgencyMessage: '',
    ctaText: 'Adicionar Usuários',
    gradients: {
      from: 'from-blue-600',
      to: 'to-indigo-600',
      accent: 'bg-gradient-to-r from-blue-600 to-indigo-600'
    }
  },
  [UpgradeSource.PLAN_MANAGEMENT]: {
    title: 'Escolha Seu Plano Ideal',
    subtitle: 'Desbloqueie todo o potencial da sua empresa',
    description: 'Explore nossos planos premium e encontre a solução perfeita para suas necessidades.',
    icon: 'crown',
    urgencyMessage: '',
    ctaText: 'Explorar Planos',
    gradients: {
      from: 'from-purple-600',
      to: 'to-indigo-600',
      accent: 'bg-gradient-to-r from-purple-600 to-indigo-600'
    }
  },
  [UpgradeSource.FEED]: {
    title: 'Upgrade de Plano',
    subtitle: 'Desbloqueie recursos premium para seu feed',
    description: 'Acesse estatísticas avançadas, filtros premium e histórico estendido do feed com nossos planos pagos.',
    icon: 'crown',
    urgencyMessage: 'Recursos premium bloqueados no plano atual',
    ctaText: 'Escolher Plano',
    gradients: {
      from: 'from-blue-600',
      to: 'to-purple-600',
      accent: 'bg-gradient-to-r from-blue-600 to-purple-600'
    }
  },
  [UpgradeSource.MOBILE_NAVIGATION]: {
    title: 'Upgrade de Plano',
    subtitle: 'Desbloqueie recursos premium para navegação mobile',
    description: 'Acesse funcionalidades avançadas de personalização e navegação mobile com nossos planos pagos.',
    icon: 'crown',
    urgencyMessage: 'Recursos premium bloqueados no plano atual',
    ctaText: 'Escolher Plano',
    gradients: {
      from: 'from-blue-600',
      to: 'to-cyan-600',
      accent: 'bg-gradient-to-r from-blue-600 to-cyan-600'
    }
  },
  [UpgradeSource.KNOWLEDGE_HUB]: {
    title: 'Upgrade Knowledge Hub',
    subtitle: 'Desbloqueie analytics avançadas e recursos premium',
    description: 'Acesse períodos estendidos de analytics, export de relatórios, colaboração avançada e muito mais.',
    icon: 'bar-chart',
    urgencyMessage: 'Recursos premium do Knowledge Hub bloqueados no plano atual',
    ctaText: 'Fazer Upgrade',
    gradients: {
      from: 'from-indigo-600',
      to: 'to-purple-600',
      accent: 'bg-gradient-to-r from-indigo-600 to-purple-600'
    }
  },
  [UpgradeSource.PEOPLE_DIRECTORY]: {
    title: 'Upgrade People Directory',
    subtitle: 'Desbloqueie recursos avançados do diretório',
    description: 'Acesse funcionalidades premium do diretório de pessoas.',
    icon: 'users',
    urgencyMessage: 'Recursos premium do diretório bloqueados no plano atual',
    ctaText: 'Fazer Upgrade',
    gradients: {
      from: 'from-green-600',
      to: 'to-blue-600',
      accent: 'bg-gradient-to-r from-green-600 to-blue-600'
    }
  },
  [UpgradeSource.CHAT_HISTORY]: {
    title: 'Upgrade Histórico de Chat',
    subtitle: 'Acesse todo o histórico de conversas',
    description: 'Desbloqueie histórico ilimitado de mensagens e tenha acesso completo a todas as suas conversas.',
    icon: 'clock',
    urgencyMessage: 'Histórico de chat limitado no plano atual',
    ctaText: 'Expandir Histórico',
    gradients: {
      from: 'from-orange-600',
      to: 'to-yellow-600',
      accent: 'bg-gradient-to-r from-orange-600 to-yellow-600'
    }
  },
  [UpgradeSource.TRANSCRIPTION]: {
    title: 'Transcrição de Áudio',
    subtitle: 'Converta áudios em texto automaticamente',
    description: 'Desbloqueie a transcrição automática de áudios usando tecnologia de IA avançada para melhor acessibilidade.',
    icon: 'sparkles',
    urgencyMessage: 'Transcrição de áudio bloqueada no plano atual',
    ctaText: 'Ativar Transcrição',
    gradients: {
      from: 'from-blue-600',
      to: 'to-purple-600',
      accent: 'bg-gradient-to-r from-blue-600 to-purple-600'
    }
  },
  [UpgradeSource.MEDALS]: {
    title: 'Sistema de Medalhas Premium',
    subtitle: 'Crie e gerencie medalhas personalizadas',
    description: 'Desbloqueie recursos avançados de gamificação e crie medalhas ilimitadas para engajar sua equipe.',
    icon: 'medal',
    urgencyMessage: 'Recursos de medalhas limitados no plano atual',
    ctaText: 'Upgrade Medalhas',
    gradients: {
      from: 'from-amber-600',
      to: 'to-yellow-600',
      accent: 'bg-gradient-to-r from-amber-600 to-yellow-600'
    }
  },
  [UpgradeSource.LEVELS]: {
    title: 'Sistema de Níveis Premium',
    subtitle: 'Configure níveis de progressão XP ilimitados',
    description: 'Crie sistemas de progressão personalizados com níveis XP ilimitados e recursos desbloqueados.',
    icon: 'trending-up',
    urgencyMessage: 'Recursos de níveis limitados no plano atual',
    ctaText: 'Upgrade Níveis',
    gradients: {
      from: 'from-blue-600',
      to: 'to-purple-600',
      accent: 'bg-gradient-to-r from-blue-600 to-purple-600'
    }
  },
  [UpgradeSource.ACTIONS]: {
    title: 'Sistema de Ações XP Premium',
    subtitle: 'Edite valores e configure ações personalizadas',
    description: 'Desbloqueie controle total sobre ações de XP: edite valores, ative/desative e configure recompensas personalizadas.',
    icon: 'zap',
    urgencyMessage: 'Edição de ações XP limitada no plano atual',
    ctaText: 'Upgrade Ações',
    gradients: {
      from: 'from-orange-600',
      to: 'to-red-600',
      accent: 'bg-gradient-to-r from-orange-600 to-red-600'
    }
  },
  [UpgradeSource.MARKETPLACE]: {
    title: 'Marketplace Estratégico Premium',
    subtitle: 'Desbloqueie recursos avançados do marketplace',
    description: 'Crie categorias ilimitadas, itens personalizados e ofertas especiais para engajar sua equipe com benefícios exclusivos.',
    icon: 'crown',
    urgencyMessage: 'Recursos do marketplace limitados no plano atual',
    ctaText: 'Upgrade Marketplace',
    gradients: {
      from: 'from-purple-600',
      to: 'to-pink-600',
      accent: 'bg-gradient-to-r from-purple-600 to-pink-600'
    }
  }
};

// Mapear ícones
const ICONS = {
  brain: Brain,
  'hard-drive': HardDrive,
  users: Users,
  crown: Crown,
  'bar-chart': BarChart3,
  clock: Clock,
  sparkles: Sparkles,
  medal: Medal,
  'trending-up': TrendingUp
};

// Etapas do fluxo de upgrade
const UPGRADE_STEPS = [
  { id: 'plan-selection', label: 'Plano', order: 1 },
  { id: 'add-ons', label: 'Add-ons', order: 2 },
  { id: 'contact-info', label: 'Dados', order: 3 },
  { id: 'confirmation', label: 'Confirmação', order: 4 },
  { id: 'success', label: 'Concluído', order: 5 }
];

interface UpgradeLayoutProps {
  children: ReactNode;
  showBackButton?: boolean;
  showProgress?: boolean;
  showContextAlert?: boolean;
}

// Mock data para desenvolvimento - será implementado na tarefa 8
interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  monthlyPrice: number;
  annualPrice: number;
  features: string[];
  userLimit: number;
  storageLimit: number;
  aiCredits: number | 'unlimited';
  badge?: string;
}

const MOCK_SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: 'basic',
    name: 'Básico',
    description: 'Ideal para pequenas equipes',
    monthlyPrice: 49.90,
    annualPrice: 499.90,
    features: ['Feature 1', 'Feature 2', 'Feature 3'],
    userLimit: 25,
    storageLimit: 100,
    aiCredits: 500,
    badge: 'popular'
  },
  {
    id: 'premium',
    name: 'Premium',
    description: 'Para empresas em crescimento',
    monthlyPrice: 99.90,
    annualPrice: 999.90,
    features: ['Todas as features', 'Suporte prioritário'],
    userLimit: 100,
    storageLimit: 500,
    aiCredits: 'unlimited',
    badge: 'best-value'
  }
];

const MOCK_ADDONS: AddOn[] = [
  {
    id: 'storage-addon',
    name: 'Armazenamento Extra',
    description: '+100GB de armazenamento',
    type: 'storage_pack',
    monthlyPrice: 19.90,
    annualPrice: 199.90,
    value: 100
  },
  {
    id: 'users-addon',
    name: 'Usuários Extra',
    description: '+10 usuários',
    type: 'user_pack',
    monthlyPrice: 29.90,
    annualPrice: 299.90,
    value: 10
  }
];

export const UpgradeLayout: React.FC<UpgradeLayoutProps> = ({
  children,
  showBackButton = true,
  showProgress = true,
  showContextAlert = true
}) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const user = useAuthStore((state) => state.user);
  
  // Hooks do store
  const store = useUpgradeStore();
  const { context, setContext } = useUpgradeContext();
  const { currentStep, steps, progress } = useUpgradeProgress();

  // Estado para controlar se já foi inicializado
  const [isInitialized, setIsInitialized] = useState(false);
  
  // Simular período de cortesia (será implementado na tarefa 8)
  const courtesyPeriod = useMemo(() => ({
    isActive: false,
    endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 dias
  }), []);
  
  // Simular steps completados baseado no step atual
  const completedSteps = useMemo(() => {
    const currentStepIndex = UPGRADE_STEPS.findIndex(step => step.id === currentStep);
    const completed: string[] = [];
    
    // Marcar todos os steps anteriores como completados
    for (let i = 0; i < currentStepIndex; i++) {
      completed.push(UPGRADE_STEPS[i].id);
    }
    
    return completed;
  }, [currentStep]);
  
  // Estados temporários
  const error: string | null = null;
  const isLoading = false;

  // Inicializar contexto baseado nos parâmetros da URL
  useEffect(() => {
    const source = searchParams.get('source') as any;
    const feature = searchParams.get('feature'); // Ex: 'analytics'
    const target = searchParams.get('target'); // Ex: 'pro', 'max'
    
    if (!source) {
      navigate('/plan-management');
      return;
    }

    // Evitar re-inicialização desnecessária
    if (isInitialized && context && context.source === source) {
      return;
    }

    // Criar contexto específico para Knowledge Hub
    const getKnowledgeHubContext = () => {
      if (source === 'knowledge-hub') {
        return {
          industry: 'knowledge-management',
          teamSize: 'medium',
          useCases: ['analytics', 'collaboration', 'content-management'],
          budget: 'medium',
          features: feature ? [feature, 'export', 'advanced-search'] : ['analytics', 'export', 'advanced-search'],
          targetPlan: target // Adicionar plano alvo específico
        };
      }
      
      // Contexto padrão para outras fontes
      return {
        industry: 'technology',
        teamSize: 'medium',
        useCases: ['collaboration', 'document-management'],
        budget: 'medium',
        features: ['ai', 'storage', 'analytics']
      };
    };

    // Criar contexto baseado na fonte
    const newContext: UpgradeContext = {
      timestamp: new Date(),
      source: source,
      userPreferences: getKnowledgeHubContext(),
      currentLimitations: {
        storage: { current: 8, limit: 10, percentage: 80 },
        users: { current: 25, limit: 50, percentage: 50 },
        aiCredits: { current: 5, limit: 10, percentage: 50 }
      },
      usagePatterns: {
        storageGrowthRate: 0.15,
        userGrowthRate: 0.08,
        aiUsageFrequency: 'high'
      }
    };

    // Definir contexto e marcar como inicializado
    setContext(newContext);
    setIsInitialized(true);
    
    // Log para debug
    console.log('Upgrade context initialized:', {
      source,
      feature,
      target,
      context: newContext
    });
    
  }, [searchParams, isInitialized, context?.source, setContext, navigate]);

  // Obter configuração do contexto atual
  const contextConfig = useMemo(() => {
    return context ? CONTEXT_CONFIGS[context.source] : null;
  }, [context]);
  
  const IconComponent = useMemo(() => {
    return contextConfig ? ICONS[contextConfig.icon as keyof typeof ICONS] : Crown;
  }, [contextConfig]);

  // Calcular progresso
  const currentStepInfo = useMemo(() => {
    const currentStepIndex = UPGRADE_STEPS.findIndex(step => step.id === currentStep);
    const progress = currentStepIndex >= 0 ? ((currentStepIndex + 1) / UPGRADE_STEPS.length) * 100 : 0;
    return { currentStepIndex, progress };
  }, [currentStep]);

  // Handlers
  const handleBack = useCallback(() => {
    if (currentStep === 'plan-selection') {
      navigate('/plan-management');
    } else {
      // Lógica para voltar ao passo anterior
      const prevStepIndex = currentStepInfo.currentStepIndex - 1;
      if (prevStepIndex >= 0) {
        // setCurrentStep(UPGRADE_STEPS[prevStepIndex].id);
      }
    }
  }, [currentStep, currentStepInfo.currentStepIndex, navigate]);

  // Animações
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  if (!context || !contextConfig) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="p-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mb-6 mx-auto w-fit">
            <Loader2 className="h-8 w-8 animate-spin text-white" />
          </div>
          <h3 className="text-lg font-semibold text-slate-800 mb-2">Carregando Upgrade</h3>
          <p className="text-slate-600">Preparando sua experiência de upgrade...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-8 pb-8"
      >
          {/* HeroSection Premium */}
          <HeroSection
            title={contextConfig.title}
            description={contextConfig.subtitle}
            icon={IconComponent}
            gradientColors={`${contextConfig.gradients.from} via-purple-600 ${contextConfig.gradients.to}`}
            actions={
              <div className="flex items-center justify-between w-full">

                
                {/* Barra de Progresso PREMIUM - Design Espetacular */}
                {showProgress && (
                  <div className="bg-white/15 backdrop-blur-md rounded-3xl px-8 py-4 min-w-[720px] border border-white/20 shadow-2xl">
                    {/* Header da Barra de Progresso */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="p-1.5 bg-white/20 rounded-lg">
                          <Zap className="h-4 w-4 text-white" />
                        </div>
                        <span className="text-base text-white font-semibold">Progresso do Upgrade</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="px-3 py-1 bg-white/20 rounded-full">
                          <span className="text-sm text-white font-bold">{Math.round(currentStepInfo.progress)}%</span>
                        </div>
                        <div className="p-1.5 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg">
                          <Star className="h-3 w-3 text-white" />
                        </div>
                      </div>
                    </div>
                    
                    {/* Barra de Progresso Principal - MUITO MAIS ROBUSTA */}
                    <div className="relative mb-4">
                      <div className="h-4 bg-white/10 rounded-full overflow-hidden border border-white/20">
                        <motion.div 
                          className="h-full bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 relative overflow-hidden"
                          initial={{ width: 0 }}
                          animate={{ width: `${currentStepInfo.progress}%` }}
                          transition={{ duration: 1, ease: "easeInOut" }}
                        >
                          {/* Animação de brilho na barra */}
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse" />
                          
                          {/* Efeito de ondulação */}
                          <motion.div 
                            className="absolute right-0 top-0 h-full w-8 bg-gradient-to-l from-white/40 to-transparent"
                            animate={{ 
                              x: [0, 20, 0],
                              opacity: [0.8, 0.4, 0.8] 
                            }}
                            transition={{ 
                              duration: 2, 
                              repeat: Infinity,
                              ease: "easeInOut" 
                            }}
                          />
                        </motion.div>
                      </div>
                      
                      {/* Indicadores de marcos na barra */}
                      <div className="absolute top-0 left-0 w-full h-4 flex justify-between items-center px-1">
                        {UPGRADE_STEPS.map((_, index) => {
                          const position = ((index + 1) / UPGRADE_STEPS.length) * 100;
                          const isPassed = currentStepInfo.progress >= position;
                          
                          return (
                            <div 
                              key={index}
                              className={`w-1 h-6 rounded-full transition-all duration-300 ${
                                isPassed 
                                  ? 'bg-white shadow-lg' 
                                  : 'bg-white/30'
                              }`}
                              style={{ marginLeft: `${position}%`, position: 'absolute', left: 0 }}
                            />
                          );
                        })}
                      </div>
                    </div>
                    
                    {/* Steps Detalhados - Design Premium */}
                    <div className="flex justify-between items-center">
                      {UPGRADE_STEPS.map((step, index) => {
                        const isPast = index < currentStepInfo.currentStepIndex;
                        const isCurrent = step.id === currentStep;
                        const isFuture = index > currentStepInfo.currentStepIndex;
                        
                        return (
                          <motion.div 
                            key={step.id} 
                            className="flex flex-col items-center relative"
                            whileHover={{ scale: 1.05 }}
                            transition={{ type: "spring", stiffness: 300 }}
                          >
                            {/* Indicador do Step */}
                            <div className={`
                              w-10 h-10 rounded-2xl flex items-center justify-center text-sm font-bold transition-all duration-500 relative overflow-hidden
                              ${isPast 
                                ? 'bg-gradient-to-r from-green-400 to-emerald-500 text-white shadow-lg shadow-green-500/30' 
                                : isCurrent 
                                  ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg shadow-orange-500/30 ring-2 ring-white/60' 
                                  : 'bg-white/20 text-white/60 border border-white/30'
                              }
                            `}>
                              {isPast ? (
                                <motion.div
                                  initial={{ scale: 0, rotate: -180 }}
                                  animate={{ scale: 1, rotate: 0 }}
                                  transition={{ type: "spring", stiffness: 300, delay: 0.1 }}
                                >
                                  <CheckCircle className="h-5 w-5" />
                                </motion.div>
                              ) : isCurrent ? (
                                <motion.div
                                  animate={{ 
                                    scale: [1, 1.1, 1],
                                    rotate: [0, 5, -5, 0]
                                  }}
                                  transition={{ 
                                    duration: 2, 
                                    repeat: Infinity,
                                    ease: "easeInOut" 
                                  }}
                                >
                                  <Sparkles className="h-5 w-5" />
                                </motion.div>
                              ) : (
                                <span className="font-bold">{step.order}</span>
                              )}
                              
                              {/* Efeito de brilho para step atual */}
                              {isCurrent && (
                                <motion.div 
                                  className="absolute inset-0 bg-white/20 rounded-2xl"
                                  animate={{ opacity: [0, 0.5, 0] }}
                                  transition={{ 
                                    duration: 1.5, 
                                    repeat: Infinity,
                                    ease: "easeInOut" 
                                  }}
                                />
                              )}
                            </div>
                            
                            {/* Label do Step */}
                            <span className={`
                              text-xs mt-1.5 text-center max-w-[70px] font-semibold transition-all duration-300
                              ${isPast 
                                ? 'text-white' 
                                : isCurrent 
                                  ? 'text-white drop-shadow-sm' 
                                  : 'text-white/70'
                              }
                            `}>
                              {step.label}
                            </span>
                            
                            {/* Status Badge */}
                            {isPast && (
                              <motion.div 
                                className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center"
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ type: "spring", stiffness: 400, delay: 0.2 }}
                              >
                                <CheckCircle className="h-2.5 w-2.5 text-white" />
                              </motion.div>
                            )}
                            
                            {isCurrent && (
                              <motion.div 
                                className="absolute -top-1 -right-1 w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center"
                                animate={{ 
                                  scale: [1, 1.2, 1],
                                  rotate: [0, 360] 
                                }}
                                transition={{ 
                                  duration: 3, 
                                  repeat: Infinity,
                                  ease: "easeInOut" 
                                }}
                              >
                                <Clock className="h-2.5 w-2.5 text-white" />
                              </motion.div>
                            )}
                            
                            {/* Linha conectora entre steps */}
                            {index < UPGRADE_STEPS.length - 1 && (
                              <div className="absolute top-5 left-12 w-16 h-0.5 bg-white/20">
                                <motion.div 
                                  className="h-full bg-gradient-to-r from-yellow-400 to-orange-500"
                                  initial={{ width: "0%" }}
                                  animate={{ width: isPast ? "100%" : "0%" }}
                                  transition={{ duration: 0.5, delay: 0.3 }}
                                />
                              </div>
                            )}
                          </motion.div>
                        );
                      })}
                    </div>
                    
                    {/* Mensagem motivacional */}
                    <motion.div 
                      className="mt-3 text-center"
                      animate={{ opacity: [0.7, 1, 0.7] }}
                      transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                    >
                      <p className="text-xs text-white/80 font-medium">
                        {currentStepInfo.currentStepIndex === 0 && "🚀 Iniciando sua jornada de upgrade!"}
                        {currentStepInfo.currentStepIndex === 1 && "⚡ Personalizando sua experiência..."}
                        {currentStepInfo.currentStepIndex === 2 && "📋 Quase lá! Coletando informações..."}
                        {currentStepInfo.currentStepIndex === 3 && "✨ Finalizando os detalhes..."}
                        {currentStepInfo.currentStepIndex === 4 && "🎉 Upgrade concluído com sucesso!"}
                      </p>
                    </motion.div>
                                      </div>
                  )}

              </div>
            }
          />



          {/* Alert de Erro */}
          {error && (
            <motion.div variants={cardVariants}>
              <Alert className="border-0 bg-gradient-to-r from-red-50 via-rose-50 to-pink-50 shadow-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-red-500 to-rose-500 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-white" />
                  </div>
                  <AlertDescription className="text-red-800 font-medium text-base">
                    <strong>Erro:</strong> {error}
                  </AlertDescription>
                </div>
              </Alert>
            </motion.div>
          )}

                     {/* Conteúdo Principal com Animações */}
          <motion.div variants={cardVariants}>
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                {children}
              </motion.div>
            </AnimatePresence>
          </motion.div>

          {/* Footer Premium com Garantias */}
          <motion.div variants={cardVariants} className="px-6">
            <Card className="border-0 bg-gradient-to-br from-slate-50 to-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                  <div className="flex flex-col items-center">
                    <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl mb-3">
                      <Shield className="h-6 w-6 text-white" />
                    </div>
                    <h4 className="font-semibold text-slate-800 mb-2">100% Seguro</h4>
                    <p className="text-sm text-slate-600">
                      Transações protegidas com criptografia SSL
                    </p>
                  </div>
                  
                  <div className="flex flex-col items-center">
                    <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl mb-3">
                      <Phone className="h-6 w-6 text-white" />
                    </div>
                    <h4 className="font-semibold text-slate-800 mb-2">Suporte Especializado</h4>
                    <p className="text-sm text-slate-600">
                      Atendimento personalizado para sua empresa
                    </p>
                  </div>
                  
                  <div className="flex flex-col items-center">
                    <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mb-3">
                      <Clock className="h-6 w-6 text-white" />
                    </div>
                    <h4 className="font-semibold text-slate-800 mb-2">7 Dias de Cortesia</h4>
                    <p className="text-sm text-slate-600">
                      Teste sem compromisso por uma semana
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Overlay de Loading Premium */}
          {isLoading && (
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
              <Card className="p-8 text-center border-0 shadow-2xl bg-white/95 backdrop-blur-md">
                <div className="p-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mb-6 mx-auto w-fit">
                  <Loader2 className="h-8 w-8 animate-spin text-white" />
                </div>
                <h3 className="text-lg font-semibold text-slate-800 mb-2">Processando Upgrade</h3>
                <p className="text-sm text-slate-600">
                  Estamos preparando tudo para você. Aguarde um momento...
                </p>
              </Card>
            </div>
          )}
        </motion.div>
    </div>
  );
};

export default UpgradeLayout;