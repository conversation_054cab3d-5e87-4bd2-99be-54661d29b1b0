/**
 * Componente de seleção de add-ons para upgrade
 * Permite adicionar recursos extras como storage, usuários e créditos de IA
 * <AUTHOR> Internet 2025
 */
import React, { useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useUpgradeStore, useUpgradeContext, useUpgradeProgress, useAddOnSelection, useUpgradePricing } from '@/stores/upgradeStore';
import { AddOn } from '@/types/upgrade';
import { Plus, Minus, HardDrive, Users, ArrowRight, Sparkles, Check, Loader2, Package, AlertCircle } from 'lucide-react';
import { useAvailableAddons } from '@/lib/query/hooks/useSubscriptions';

export const AddonsStep: React.FC = () => {
  const store = useUpgradeStore();
  const { context, billingCycle } = useUpgradeContext();
  const isAiContext = context?.source === 'ai';
  const { goToStep, completeStep } = useUpgradeProgress();
  const { selectedAddOns, handleToggle } = useAddOnSelection();
  const { pricingSummary, formatPriceWithCycle, addOnsPrice, calculatePricing } = useUpgradePricing();
  
  const selectedPlan = store.selectedPlan;

  // Buscar addons reais do backend
  const { data: backendAddons = [], isLoading, error } = useAvailableAddons();
  
  // Mapear os addons do backend para o formato esperado pelo componente
  const availableAddons: AddOn[] = useMemo(() => {
    if (isLoading || error) return [];
    
    return backendAddons.map(addon => ({
      id: addon.id,
      name: addon.name,
      description: addon.description || '',
      type: addon.type as 'storage_pack' | 'user_pack' | 'ai_credits',
      value: addon.capacity,
      monthlyPrice: addon.price,
      annualPrice: Math.round(addon.price * 10 * 0.9 * 100) / 100, // Aproximadamente 10% de desconto no plano anual
      relevanceScore: addon.type === 'ai_credits' ? 10 : 
                     addon.type === 'storage_pack' ? 8 : 6,
      isSelected: false
    }));
  }, [backendAddons, isLoading, error]);
  
  // Se estiver carregando, mostrar indicador de carregamento
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p>Carregando opções de add-ons...</p>
      </div>
    );
  }
  
  // Se houver erro, mostrar mensagem de erro
  if (error) {
    return (
      <div className="space-y-4">
        <div className="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-lg relative" role="alert">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 flex-shrink-0" />
            <div>
              <h3 className="font-bold">Erro ao carregar add-ons</h3>
              <p className="text-sm">
                {error instanceof Error ? error.message : 'Ocorreu um erro inesperado ao carregar os add-ons disponíveis.'}
              </p>
              <Button 
                variant="outline" 
                size="sm" 
                className="mt-2"
                onClick={() => window.location.reload()}
              >
                Tentar novamente
              </Button>
            </div>
          </div>
        </div>
        <Button
          variant="outline"
          onClick={() => goToStep('contact-info')}
          className="w-full md:w-auto"
        >
          Continuar sem add-ons
        </Button>
      </div>
    );
  }

  // Se não houver add-ons disponíveis
  if (!isLoading && availableAddons.length === 0) {
    return (
      <div className="space-y-6 text-center">
        <div className="bg-blue-50 border border-blue-200 text-blue-800 px-6 py-8 rounded-lg">
          <Package className="h-12 w-12 mx-auto mb-4 text-blue-400" />
          <h3 className="text-lg font-medium mb-2">Nenhum add-on disponível no momento</h3>
          <p className="text-sm text-blue-700 mb-4">
            No momento, não há add-ons disponíveis para adição ao seu plano.
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            variant="outline"
            onClick={() => goToStep('plan-selection')}
            className="w-full sm:w-auto"
          >
            Voltar aos Planos
          </Button>
          <Button
            onClick={() => goToStep('contact-info')}
            className="w-full sm:w-auto"
          >
            Continuar sem add-ons
          </Button>
        </div>
      </div>
    );
  }

  // Verifica se o addon é recomendado com base no contexto
  const isRecommendedAddon = (addon: AddOn) => {
    if (isAiContext && addon.type === 'ai_credits') {
      return true;
    }
    if (context?.source === 'storage' && addon.type === 'storage_pack') {
      return true;
    }
    if (context?.source === 'users' && addon.type === 'user_pack') {
      return true;
    }
    return false;
  };

  // Calcular pricing quando o componente for montado ou quando add-ons/plano mudar
  useEffect(() => {
    if (selectedPlan) {
      calculatePricing();
    }
  }, [selectedPlan, selectedAddOns, billingCycle, calculatePricing]);

  if (!selectedPlan) return null;

  const handleToggleAddon = (addon: AddOn) => {
    handleToggle(addon);
  };

  const handleContinue = () => {
    // Verificar se temos a estrutura correta de steps
    const hasCorrectSteps = store.steps.some(s => s.id === 'contact-info');
    if (!hasCorrectSteps) {
      // Preservar o billingCycle antes do reset
      const currentBillingCycle = billingCycle;
      
      store.reset();
      // Depois do reset, precisamos selecionar o plano novamente
      if (selectedPlan) {
        store.setPlan(selectedPlan);
        // E restaurar o billingCycle
        store.setBillingCycle(currentBillingCycle);
        // E avançar para add-ons
        store.goToStep('add-ons');
      }
    }
    
    // Primeiro completar o step atual, que automaticamente habilita o próximo
    completeStep('add-ons');
    // Então navegar para o próximo step
    goToStep('contact-info');
  };

  const getIcon = (addonType: string) => {
    switch (addonType) {
      case 'storage_pack':
        return HardDrive;
      case 'user_pack':
        return Users;
      case 'ai_credits':
        return Sparkles;
      default:
        return Sparkles;
    }
  };

  const isAddonSelected = (addon: AddOn) => {
    return selectedAddOns.some(a => a.id === addon.id);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3
      }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Header */}
      <motion.div variants={cardVariants}>
        <Card className="border-0 bg-gradient-to-r from-white to-slate-50">
          <CardHeader>
            <CardTitle className="text-center">
              Adicione Recursos Extras (Opcional)
            </CardTitle>
            <p className="text-center text-muted-foreground">
              Personalize seu plano {selectedPlan.name} com add-ons específicos para suas necessidades
            </p>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Lista de Add-ons */}
      <motion.div variants={cardVariants}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {availableAddons.map((addon) => {
            const IconComponent = getIcon(addon.type);
            const isSelected = isAddonSelected(addon);
            const price = billingCycle === 'annual' ? addon.annualPrice : addon.monthlyPrice;
            const priceLabel = billingCycle === 'annual' ? '/ano' : '/mês';

            return (
              <motion.div
                key={addon.id}
                variants={cardVariants}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card className={`
                  border-2 transition-all cursor-pointer
                  ${isSelected 
                    ? 'border-primary shadow-lg bg-gradient-to-br from-primary/5 to-primary/10' 
                    : 'border-slate-200 hover:border-primary/50 hover:shadow-md'
                  }
                `}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div 
                          className={`
                            p-3 rounded-lg relative
                            ${isSelected 
                              ? 'bg-gradient-to-r from-primary to-primary/80 text-white' 
                              : isRecommendedAddon(addon)
                                ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-lg'
                                : 'bg-slate-100 text-slate-600'
                            }`}
                        >
                          {isRecommendedAddon(addon) && !isSelected && (
                            <div className="absolute -top-2 -right-2">
                              <div className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-md">
                                Recomendado
                              </div>
                            </div>
                          )}
                          <IconComponent className="h-6 w-6" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{addon.name}</CardTitle>
                          <p className="text-sm text-muted-foreground">
                            {addon.description}
                          </p>
                        </div>
                      </div>
                      {isSelected && (
                        <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white">
                          <Check className="h-3 w-3 mr-1" />
                          Selecionado
                        </Badge>
                      )}
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Preço */}
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        R$ {price.toFixed(2).replace('.', ',')}
                        <span className="text-sm text-muted-foreground font-normal">
                          {priceLabel}
                        </span>
                      </div>
                      {billingCycle === 'annual' && (
                        <p className="text-xs text-green-600 font-medium">
                          Economize com cobrança anual
                        </p>
                      )}
                    </div>

                    {/* Valor do add-on */}
                    <div className={`text-center rounded-lg p-3 ${
                      isRecommendedAddon(addon) 
                        ? 'bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-100' 
                        : 'bg-slate-50'
                    }`}>
                      <p className={`text-sm font-medium ${
                        isRecommendedAddon(addon) ? 'text-blue-700' : ''
                      }`}>
                        {addon.type === 'storage_pack' 
                          ? `+${addon.value}GB de armazenamento`
                          : addon.type === 'user_pack'
                            ? `+${addon.value} usuários extras`
                            : `+${addon.value} créditos de IA`
                        }
                      </p>
                    </div>

                    {/* Botão de seleção */}
                    <Button
                      onClick={() => handleToggleAddon(addon)}
                      variant={isSelected ? "default" : isRecommendedAddon(addon) ? "default" : "outline"}
                      className={`
                        w-full gap-2 transition-all
                        ${isSelected 
                          ? 'bg-gradient-to-r from-primary to-primary/80' 
                          : isRecommendedAddon(addon)
                            ? 'bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white shadow-md hover:shadow-lg'
                            : 'hover:bg-primary hover:text-white'
                        }
                      `}
                    >
                      {isSelected ? (
                        <>
                          <Minus className="h-4 w-4" />
                          Remover
                        </>
                      ) : (
                        <>
                          <Plus className="h-4 w-4" />
                          Adicionar
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </motion.div>

      {/* Resumo de Pricing */}
      {(selectedAddOns.length > 0 || pricingSummary) && (
        <motion.div variants={cardVariants}>
          <Card className="border border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
            <CardHeader>
              <CardTitle className="text-lg">Resumo do Pedido</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {/* Plano selecionado */}
                {selectedPlan && (
                  <div className="flex items-center justify-between bg-white rounded-lg p-3">
                    <div>
                      <p className="font-medium">Plano {selectedPlan.name}</p>
                      <p className="text-sm text-muted-foreground">Plano base selecionado</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {formatPriceWithCycle(pricingSummary?.planPrice || selectedPlan.price)}
                      </p>
                    </div>
                  </div>
                )}

                {/* Add-ons selecionados */}
                {selectedAddOns.map((addon) => (
                  <div key={addon.id} className="flex items-center justify-between bg-white rounded-lg p-3">
                    <div>
                      <p className="font-medium">{addon.name}</p>
                      <p className="text-sm text-muted-foreground">{addon.description}</p>
                      {addon.quantity && addon.quantity > 1 && (
                        <p className="text-xs text-primary">Quantidade: {addon.quantity}</p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {formatPriceWithCycle(addon.totalPrice)}
                      </p>
                    </div>
                  </div>
                ))}
                
                {/* Subtotal */}
                {pricingSummary && (
                  <div className="border-t pt-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <span>Subtotal:</span>
                      <span>{formatPriceWithCycle(pricingSummary.subtotal)}</span>
                    </div>
                    
                    {/* Descontos */}
                    {pricingSummary.discounts.totalDiscount > 0 && (
                      <div className="flex items-center justify-between text-green-600">
                        <span>Desconto:</span>
                        <span>-{formatPriceWithCycle(pricingSummary.discounts.totalDiscount)}</span>
                      </div>
                    )}
                    
                    {/* Impostos - Comentado por enquanto */}
                    {/* <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span>Impostos ({(pricingSummary.taxes.rate * 100).toFixed(0)}%):</span>
                      <span>{formatPriceWithCycle(pricingSummary.taxes.amount)}</span>
                    </div> */}
                    
                    {/* Total final */}
                    <div className="border-t pt-2">
                      <div className="flex items-center justify-between font-bold text-lg">
                        <span>Total Final:</span>
                        <span className="text-primary">
                          {formatPriceWithCycle(pricingSummary.total)}
                        </span>
                      </div>
                    </div>

                    {/* Economia anual */}
                    {pricingSummary.savings && (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-2">
                        <p className="text-sm text-green-700 font-medium text-center">
                          💰 Você economiza {formatPriceWithCycle(pricingSummary.savings.annualSavings)} 
                          ({pricingSummary.savings.percentageSaved.toFixed(0)}%) com o plano anual!
                        </p>
                      </div>
                    )}
                  </div>
                )}
                
                {/* Total simples se não há pricingSummary */}
                {!pricingSummary && selectedAddOns.length > 0 && (
                  <div className="border-t pt-3">
                    <div className="flex items-center justify-between font-bold">
                      <span>Total dos Add-ons:</span>
                      <span className="text-primary">
                        {formatPriceWithCycle(addOnsPrice)}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Navegação */}
      <motion.div variants={cardVariants}>
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={() => goToStep('plan-selection')}
          >
            Voltar aos Planos
          </Button>
          
          <Button
            onClick={handleContinue}
            className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 gap-2"
          >
            {selectedAddOns.length > 0 ? 'Continuar para Informações' : 'Pular para Informações'}
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default AddonsStep; 