/**
 * Componente de confirmação final do upgrade
 * Exibe resumo completo e processa o envio da solicitação
 * <AUTHOR> Internet 2025
 */
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { successWithNotification, errorWithNotification } from '@/lib/notifications/toastWithNotification';
import { AddonPackage } from '@/config/addonFlowConfig';
import { useAuthStore } from '@/stores/authStore';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { useCurrentSubscription } from '@/lib/query/hooks/useSubscriptions';
import { useUpgradeStore, useUpgradeContext, useUpgradeProgress } from '@/stores/upgradeStore';
import {
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  CreditCard,
  Clock,
  Shield,
  Users,
  HardDrive,
  Brain,
  Send,
  Crown
} from 'lucide-react';
import { useConsolidateOrCreateLead } from '@/lib/query/hooks/useCommercialAddonApproval';
import { useActivatePlanWithCourtesy } from '@/lib/query/hooks/useCommercialAddonApproval';

interface ConfirmationStepProps {
  // Props para fluxo de addons (opcionais)
  primaryAddon?: AddonPackage | null;
  relatedAddons?: AddonPackage[];
  totalPrice?: number;
  source?: string;
  onNext?: () => void;
  onBack?: () => void;
}

export const ConfirmationStep: React.FC<ConfirmationStepProps> = ({ 
  primaryAddon, 
  relatedAddons = [], 
  totalPrice, 
  source, 
  onNext, 
  onBack 
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Hooks
  const { user } = useAuthStore();
  const company_id = useAuthStore((state) => state.company_id);
  const { data: currentSubscription } = useCurrentSubscription();
  const consolidateOrCreateLead = useConsolidateOrCreateLead();
  const activatePlanWithCourtesy = useActivatePlanWithCourtesy();
  
  // Hooks do upgrade store (para fluxo de planos)
  const store = useUpgradeStore();
  const { context, billingCycle } = useUpgradeContext();
  const { goToStep } = useUpgradeProgress();
  
  // Detectar se é fluxo de addons ou planos
  const isAddonFlow = !!primaryAddon;
  const isPlanFlow = !isAddonFlow && !!store.selectedPlan;
  
  // Usar dados do fluxo apropriado
  const effectiveSource = source || context?.source || 'plan-management';
  const effectiveTotalPrice = totalPrice || store.pricingSummary?.total || 0;
  
  // Função para obter o ícone baseado no tipo de addon
  const getAddonIcon = (addonId: string) => {
    if (addonId.includes('users')) return Users;
    if (addonId.includes('storage')) return HardDrive;
    if (addonId.includes('ai')) return Brain;
    return CreditCard;
  };

  // Função para obter a descrição do tipo de addon
  const getAddonTypeDescription = (source: string) => {
    switch (source) {
      case 'users-limit': return 'Expansão de Usuários';
      case 'storage-limit': 
      case 'storage-full': return 'Expansão de Armazenamento';
      case 'ai-credits': return 'Créditos de IA';
      default: return 'Upgrade';
    }
  };

  const handleConfirmUpgrade = async () => {
    if (!user) {
      errorWithNotification('Erro de validação', {
        description: 'Usuário não encontrado.'
      });
      return;
    }

    // Debug: verificar company_id
    logQueryEvent('ConfirmationStep', 'Debug company_id', {
      user_id: user.id,
      company_id: company_id,
      user_company_id: (user as any).company_id,
      user_metadata: user.user_metadata
    });

    // Validar se há algo para processar
    if (!primaryAddon && !store.selectedPlan) {
      errorWithNotification('Erro de validação', {
        description: 'Nenhum plano ou addon selecionado.'
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Preparar dados baseado no fluxo
      let submitData;
      
      if (isAddonFlow && primaryAddon) {
        // Fluxo de addons
        submitData = {
          user_id: user?.id,
          requested_plan_id: currentSubscription?.plan_id || null, // Mantém plano atual para addon-only
          selected_addons: [
            ...(primaryAddon ? [primaryAddon] : []),
            ...relatedAddons
          ],
          lead_source: `addon-flow-${effectiveSource}`,
          contact_preferences: {
            name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Usuário',
            email: user.email || '',
            phone: user.user_metadata?.phone || '',
            urgency: 'normal',
            preferredContactMethod: 'email'
          },
          upgrade_context: {
            source: effectiveSource,
            flow_type: 'addon-focused',
            primary_addon: primaryAddon?.name,
            related_addons_count: relatedAddons.length,
            total_monthly_value: effectiveTotalPrice,
            current_plan_id: currentSubscription?.plan_id || null,
            timestamp: new Date().toISOString()
          },
          metadata: {
            user_agent: navigator.userAgent,
            referrer: document.referrer || '',
            page_url: window.location.href,
            addon_flow_version: '1.0',
            primary_addon_details: primaryAddon,
            related_addons_details: relatedAddons
          }
        };
      } else if (isPlanFlow && store.selectedPlan) {
        // Fluxo de planos - usar dados do contactInfo se disponível
        const contactInfo = store.contactInfo || {
          name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Usuário',
          email: user.email || '',
          phone: user.user_metadata?.phone || '',
          company: '',
          role: '',
          urgency: 'normal',
          message: '',
          preferredContactMethod: 'email'
        };
        
        submitData = {
          user_id: user?.id,
          requested_plan_id: store.selectedPlan.id,
          selected_addons: store.selectedAddOns.map(addon => ({
            id: addon.id,
            name: addon.name,
            description: addon.description || '',
            price: addon.totalPrice,
            type: addon.type || 'addon'
          })),
          lead_source: `plan-flow-${effectiveSource}`,
          contact_preferences: contactInfo,
          upgrade_context: {
            source: effectiveSource,
            flow_type: 'plan-focused',
            selected_plan: store.selectedPlan.name,
            selected_addons_count: store.selectedAddOns.length,
            total_monthly_value: effectiveTotalPrice,
            current_plan_id: currentSubscription?.plan_id || null,
            billing_cycle: store.billingCycle,
            timestamp: new Date().toISOString()
          },
          metadata: {
            user_agent: navigator.userAgent,
            referrer: document.referrer || '',
            page_url: window.location.href,
            plan_flow_version: '1.0',
            selected_plan_details: store.selectedPlan,
            selected_addons_details: store.selectedAddOns,
            contact_info: contactInfo
          }
        };
      } else {
        throw new Error('Dados insuficientes para processar a solicitação');
      }

      // Submeter usando hook de consolidação inteligente
      const result = await consolidateOrCreateLead.mutateAsync(submitData);
      
      // Se for fluxo de planos, ativar automaticamente com período de cortesia
      if (isPlanFlow && store.selectedPlan && result.lead_id) {
        try {
          const activationResult = await activatePlanWithCourtesy.mutateAsync({
            leadId: result.lead_id,
            userId: user.id,
            selectedPlan: {
              id: store.selectedPlan.id,
              name: store.selectedPlan.name,
              price: store.selectedPlan.price
            },
            selectedAddons: store.selectedAddOns.map(addon => ({
              id: addon.id,
              name: addon.name,
              price: addon.totalPrice,
              type: addon.type
            }))
          });
          
          // Salvar resultado da ativação no store
          store.completeStep('confirmation', { 
            submittedAt: new Date(),
            upgradeProcessed: true,
            leadId: result.lead_id,
            actionTaken: result.action_taken,
            planActivated: true,
            activationId: activationResult.activation_id,
            courtesyEndDate: activationResult.courtesy_end_date,
            previousPlan: activationResult.previous_plan_id
          });
          
          logQueryEvent('ConfirmationStep', 'Plano ativado automaticamente', {
            leadId: result.lead_id,
            activationId: activationResult.activation_id,
            courtesyEndDate: activationResult.courtesy_end_date
          });
        } catch (activationError) {
          console.error('Erro na ativação automática:', activationError);
          // Mesmo com erro na ativação, continuar o fluxo
          // O lead foi criado com sucesso, a ativação pode ser feita manualmente
          store.completeStep('confirmation', { 
            submittedAt: new Date(),
            upgradeProcessed: true,
            leadId: result.lead_id,
            actionTaken: result.action_taken,
            planActivated: false,
            activationError: (activationError as Error).message
          });
        }
      } else {
        // Fluxo de addons - apenas salvar resultado do lead
        store.completeStep('confirmation', { 
          submittedAt: new Date(),
          upgradeProcessed: true,
          leadId: result.lead_id,
          actionTaken: result.action_taken,
          planActivated: false
        });
      }
      
      // Sucesso - avançar para próxima etapa SEM mostrar toast aqui
      // O toast será mostrado na tela de sucesso
      handleNext();
    } catch (error) {
      console.error('Erro ao processar upgrade:', error);
      // O erro já é tratado pelo hook
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNext = () => {
    // Completar o step atual e habilitar o próximo
    store.completeStep('confirmation', { 
      submittedAt: new Date(),
      upgradeProcessed: true 
    });
    
    if (onNext) {
      onNext();
    } else {
      goToStep('success');
    }
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      goToStep('contact-info');
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3
      }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Header */}
      <motion.div variants={cardVariants}>
        <Card className="border-0 bg-gradient-to-r from-white to-slate-50">
          <CardHeader>
            <CardTitle className="text-center">
              Confirmação do Upgrade
            </CardTitle>
            <p className="text-center text-muted-foreground">
              Revise todas as informações antes de confirmar sua solicitação
            </p>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Resumo do Addon Selecionado */}
      <motion.div variants={cardVariants}>
        <Card>
          <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
              Resumo do Upgrade
              </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {primaryAddon ? (
              <>
                {/* Addon Principal */}
                <div className="text-center p-8 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-3">
                    {React.createElement(getAddonIcon(primaryAddon.id), { 
                      className: "h-8 w-8 text-blue-600" 
                    })}
                    <Badge variant="outline" className="text-blue-700 border-blue-300">
                      {getAddonTypeDescription(effectiveSource)}
                    </Badge>
                  </div>
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    R$ {primaryAddon.price.toFixed(2).replace('.', ',')}{billingCycle === 'annual' ? '/ano' : '/mês'}
                  </div>
                  <h3 className="text-xl font-semibold text-slate-800 mb-1">
                    {primaryAddon.name}
                  </h3>
                  <p className="text-slate-600">
                    {primaryAddon.description}
                  </p>
                </div>

                {/* Addons Relacionados */}
                {relatedAddons.length > 0 && (
              <div className="space-y-3">
                    <h4 className="font-semibold text-slate-700">Addons Adicionais:</h4>
                    {relatedAddons.map((addon) => (
                  <div key={addon.id} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          {React.createElement(getAddonIcon(addon.id), { 
                            className: "h-5 w-5 text-slate-600" 
                          })}
                    <div>
                      <p className="font-medium">{addon.name}</p>
                            <p className="text-sm text-slate-600">{addon.description}</p>
                          </div>
                    </div>
                        <p className="font-medium text-slate-800">
                          R$ {addon.price.toFixed(2).replace('.', ',')}{billingCycle === 'annual' ? '/ano' : '/mês'}
                    </p>
                  </div>
                ))}
                  </div>
                )}

                {/* Total */}
                {relatedAddons.length > 0 && (
                  <>
                    <Separator />
                    <div className="flex items-center justify-between text-lg font-bold">
                      <span>Total {billingCycle === 'annual' ? 'Anual' : 'Mensal'}:</span>
                      <span className="text-blue-600">
                        R$ {effectiveTotalPrice.toFixed(2).replace('.', ',')}{billingCycle === 'annual' ? '/ano' : '/mês'}
                      </span>
                  </div>
                  </>
                )}
              </>
            ) : store.selectedPlan ? (
              <>
                {/* Plano Selecionado */}
                <div className="text-center p-8 bg-gradient-to-br from-purple-50 to-indigo-50 rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-3">
                    <Crown className="h-8 w-8 text-purple-600" />
                    <Badge variant="outline" className="text-purple-700 border-purple-300">
                      Upgrade de Plano
                    </Badge>
                  </div>
                  <div className="text-3xl font-bold text-purple-600 mb-2">
                    R$ {store.selectedPlan.price.toFixed(2).replace('.', ',')}{billingCycle === 'annual' ? '/ano' : '/mês'}
                  </div>
                  <h3 className="text-xl font-semibold text-slate-800 mb-1">
                    {store.selectedPlan.name}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <div className="text-center">
                      <Users className="h-5 w-5 mx-auto mb-1 text-purple-600" />
                      <p className="text-sm font-medium">{store.selectedPlan.limits.users} usuários</p>
                    </div>
                    <div className="text-center">
                      <HardDrive className="h-5 w-5 mx-auto mb-1 text-purple-600" />
                      <p className="text-sm font-medium">{store.selectedPlan.limits.storage}GB storage</p>
                    </div>
                    <div className="text-center">
                      <Brain className="h-5 w-5 mx-auto mb-1 text-purple-600" />
                      <p className="text-sm font-medium">
                        {store.selectedPlan.limits.aiCredits === 'unlimited' ? '∞' : store.selectedPlan.limits.aiCredits} créditos IA
                      </p>
                    </div>
                  </div>
                </div>

                {/* Add-ons Selecionados (se houver) */}
                {store.selectedAddOns.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="font-semibold text-slate-700">Add-ons Incluídos:</h4>
                    {store.selectedAddOns.map((addon) => (
                      <div key={addon.id} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <HardDrive className="h-5 w-5 text-slate-600" />
                          <div>
                            <p className="font-medium">{addon.name}</p>
                            <p className="text-sm text-slate-600">{addon.description}</p>
                          </div>
                        </div>
                        <p className="font-medium text-slate-800">
                          R$ {addon.totalPrice.toFixed(2).replace('.', ',')}{billingCycle === 'annual' ? '/ano' : '/mês'}
                        </p>
                      </div>
                    ))}
                  </div>
                )}

                {/* Total do Plano */}
                {store.pricingSummary && (
                  <>
                    <Separator />
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>Plano {store.selectedPlan.name}:</span>
                        <span>R$ {store.selectedPlan.price.toFixed(2).replace('.', ',')}{billingCycle === 'annual' ? '/ano' : '/mês'}</span>
                      </div>
                      {store.selectedAddOns.length > 0 && (
                        <div className="flex items-center justify-between">
                          <span>Add-ons:</span>
                          <span>R$ {store.pricingSummary.addOnsPrice.toFixed(2).replace('.', ',')}{billingCycle === 'annual' ? '/ano' : '/mês'}</span>
                        </div>
                      )}
                      <Separator />
                      <div className="flex items-center justify-between text-lg font-bold">
                        <span>Total {billingCycle === 'annual' ? 'Anual' : 'Mensal'}:</span>
                        <span className="text-purple-600">
                          R$ {store.pricingSummary.total.toFixed(2).replace('.', ',')}{billingCycle === 'annual' ? '/ano' : '/mês'}
                        </span>
                      </div>
                    </div>
                  </>
                )}
              </>
            ) : (
              <div className="text-center p-8 text-slate-500">
                <p>Nenhum plano ou addon selecionado</p>
              </div>
            )}
            
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-green-800">Acesso imediato após confirmação</span>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                <Clock className="h-5 w-5 text-orange-600" />
                <span className="text-orange-800">7 dias de cortesia para formalização</span>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                <Shield className="h-5 w-5 text-blue-600" />
                <span className="text-blue-800">Contato comercial personalizado</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Navegação */}
      <motion.div variants={cardVariants}>
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={handleBack}
            disabled={isSubmitting}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar
          </Button>
          
          <Button
            onClick={handleConfirmUpgrade}
            disabled={isSubmitting}
            className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                Processando...
              </>
            ) : (
              <>
                <Send className="h-4 w-4" />
                Confirmar Upgrade
              </>
            )}
          </Button>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default ConfirmationStep;