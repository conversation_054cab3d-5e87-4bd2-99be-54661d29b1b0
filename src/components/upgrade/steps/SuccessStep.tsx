/**
 * Componente de sucesso após envio da solicitação de upgrade
 * Adaptado para fluxo de addons e planos com período de cortesia
 * <AUTHOR> Internet 2025
 */
import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AddonPackage } from '@/config/addonFlowConfig';
import { useNavigate } from 'react-router-dom';
import { useUpgradeStore } from '@/stores/upgradeStore';
import { successWithNotification } from '@/lib/notifications/toastWithNotification';
import {
  CheckCircle,
  Calendar,
  Phone,
  Mail,
  Clock,
  Shield,
  Star,
  ArrowRight,
  Home,
  MessageSquare,
  Users,
  FileText,
  Award,
  Sparkles,
  Crown,
  HardDrive,
  Brain,
  Plus,
  Gift
} from 'lucide-react';

interface SuccessStepProps {
  primaryAddon?: AddonPackage | null;
  relatedAddons?: AddonPackage[];
  totalPrice?: number;
  source?: string;
  contactInfo?: {
    name: string;
    email: string;
    phone: string;
    urgency: string;
    preferredContactMethod: string;
  };
}

export const SuccessStep: React.FC<SuccessStepProps> = ({ 
  primaryAddon, 
  relatedAddons = [], 
  totalPrice = 0,
  source = 'addon',
  contactInfo 
}) => {
  const navigate = useNavigate();
  const store = useUpgradeStore();

  // Detectar se é fluxo de addon ou plano
  const isAddonFlow = !!primaryAddon;
  const isPlanFlow = !isAddonFlow && !!store.selectedPlan;

  useEffect(() => {
    console.log('Success page viewed for upgrade', { isAddonFlow, isPlanFlow });
    
    // Mostrar notificação de sucesso específica para o fluxo
    if (isPlanFlow) {
      successWithNotification('Upgrade ativado com sucesso!', {
        description: `Seu plano ${store.selectedPlan?.name} foi ativado! Você tem 7 dias de cortesia para testar todos os recursos.`
      });
    }
  }, [isAddonFlow, isPlanFlow, store.selectedPlan]);

  // Calcular data de fim do período de cortesia (7 dias)
  const courtesyEndDate = new Date();
  courtesyEndDate.setDate(courtesyEndDate.getDate() + 7);

  // Gerar protocolo baseado no timestamp
  const protocol = `#${Date.now().toString().substring(0, 8).toUpperCase()}`;

  const handleGoToDashboard = () => {
    navigate('/dashboard');
  };

  const handleGoToPlans = () => {
    navigate('/plan-management');
  };

  const handleNewUpgrade = () => {
    // Recarregar a página para iniciar novo fluxo
    window.location.reload();
  };

  // Função para obter ícone do addon
  const getAddonIcon = (addonId: string) => {
    if (addonId.includes('user')) return Users;
    if (addonId.includes('storage')) return HardDrive;
    if (addonId.includes('ai')) return Brain;
    return Plus;
  };

  // Função para obter cor do addon
  const getAddonColor = (addonId: string) => {
    if (addonId.includes('user')) return {
      border: 'border-blue-200',
      bg: 'bg-blue-50',
      text: 'text-blue-800',
      textAccent: 'text-blue-600'
    };
    if (addonId.includes('storage')) return {
      border: 'border-green-200',
      bg: 'bg-green-50',
      text: 'text-green-800',
      textAccent: 'text-green-600'
    };
    if (addonId.includes('ai')) return {
      border: 'border-purple-200',
      bg: 'bg-purple-50',
      text: 'text-purple-800',
      textAccent: 'text-purple-600'
    };
    return {
      border: 'border-gray-200',
      bg: 'bg-gray-50',
      text: 'text-gray-800',
      textAccent: 'text-gray-600'
    };
  };

  // Se não há addon principal nem plano selecionado, mostrar loading
  if (!primaryAddon && !store.selectedPlan) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Carregando informações...</h2>
          <p className="text-muted-foreground">Processando dados do upgrade</p>
        </div>
      </div>
    );
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  const iconVariants = {
    hidden: { scale: 0 },
    visible: {
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 200,
        delay: 0.3
      }
    }
  };

  // Só calcular se for fluxo de addons
  const totalAddons = primaryAddon ? relatedAddons.length + 1 : 0; // +1 para o addon principal
  const addonColors = primaryAddon ? getAddonColor(primaryAddon.id) : null;

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Header de Sucesso */}
      <motion.div variants={cardVariants}>
        <Card className="border-0 bg-gradient-to-r from-green-50 to-emerald-50">
          <CardContent className="p-8 text-center">
            <motion.div
              variants={iconVariants}
              className="mb-6"
            >
              <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="h-10 w-10 text-white" />
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              <h1 className="text-3xl font-bold text-green-800 mb-2">
                {isPlanFlow ? 'Upgrade Concluído com Sucesso!' : 'Solicitação Enviada com Sucesso!'}
              </h1>
              <p className="text-lg text-green-600 mb-4">
                {isPlanFlow ? (
                  <>
                    Seu upgrade para o plano <strong>{store.selectedPlan?.name}</strong> foi ativado
                    {store.selectedAddOns && store.selectedAddOns.length > 0 && (
                      <span> + {store.selectedAddOns.length} addon{store.selectedAddOns.length > 1 ? 's' : ''} adicional{store.selectedAddOns.length > 1 ? 'is' : ''}</span>
                    )}
                  </>
                ) : (
                  <>
                    Seu upgrade com <strong>{primaryAddon?.name}</strong> foi solicitado
                    {relatedAddons.length > 0 && (
                      <span> + {relatedAddons.length} addon{relatedAddons.length > 1 ? 's' : ''} adicional{relatedAddons.length > 1 ? 'is' : ''}</span>
                    )}
                  </>
                )}
              </p>
              
              <div className="flex flex-wrap justify-center gap-2 mb-4">
                <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2">
                  <FileText className="h-4 w-4 mr-2" />
                  Protocolo: {protocol}
                </Badge>
                
                <Badge className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-4 py-2">
                  <Crown className="h-4 w-4 mr-2" />
                  {isPlanFlow ? 'Plano Ativado' : 'Ativação Pendente'}
                </Badge>
              </div>
              
              <div className="mt-4 text-sm text-green-700">
                <div className="space-y-2">
                  <div className="flex items-center justify-center gap-4">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{isPlanFlow ? 'Ativo agora' : 'Processamento em até 2h'}</span>
                    </div>
                    {isPlanFlow && (
                      <div className="flex items-center gap-1">
                        <Gift className="h-4 w-4" />
                        <span>7 dias de cortesia até {courtesyEndDate.toLocaleDateString('pt-BR')}</span>
                      </div>
                    )}
                  </div>
                  <div className="text-center">
                    <strong>
                      {isPlanFlow 
                        ? 'Você já pode usar todos os recursos do novo plano!' 
                        : 'Nossa equipe fará a ativação dos addons'
                      }
                    </strong>
                  </div>
                </div>
              </div>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Resumo dos Addons ou Plano */}
      <motion.div variants={cardVariants}>
        {isPlanFlow ? (
          // Fluxo de Planos - Mostrar plano selecionado
          <Card className="border-purple-200 bg-purple-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-purple-800">
                <Crown className="h-5 w-5" />
                Plano Ativado
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Plano Principal */}
                <div className="p-6 bg-white rounded-lg border border-purple-200">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-3">
                      <Crown className="h-8 w-8 text-purple-600" />
                      <Badge variant="outline" className="text-purple-700 border-purple-300">
                        Plano Ativo
                      </Badge>
                    </div>
                    <div className="text-3xl font-bold text-purple-600 mb-2">
                      R$ {(store.selectedPlan?.price || 0).toFixed(2).replace('.', ',')}/mês
                    </div>
                    <h3 className="text-xl font-semibold text-slate-800 mb-4">
                      {store.selectedPlan?.name}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <Users className="h-5 w-5 mx-auto mb-1 text-purple-600" />
                        <p className="text-sm font-medium">{store.selectedPlan?.limits?.users || 0} usuários</p>
                      </div>
                      <div className="text-center">
                        <HardDrive className="h-5 w-5 mx-auto mb-1 text-purple-600" />
                        <p className="text-sm font-medium">{store.selectedPlan?.limits?.storage || 0}GB storage</p>
                      </div>
                      <div className="text-center">
                        <Brain className="h-5 w-5 mx-auto mb-1 text-purple-600" />
                        <p className="text-sm font-medium">
                          {store.selectedPlan?.limits?.aiCredits === 'unlimited' ? '∞' : store.selectedPlan?.limits?.aiCredits || 0} créditos IA
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Add-ons Selecionados (se houver) */}
                {store.selectedAddOns && store.selectedAddOns.length > 0 && (
                  <>
                    <h4 className="font-semibold text-slate-700">Add-ons Incluídos:</h4>
                    {store.selectedAddOns.map((addon) => (
                      <div key={addon.id} className="p-4 bg-white rounded-lg border border-gray-200">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <HardDrive className="h-5 w-5 text-slate-600" />
                            <div>
                              <p className="font-medium">{addon.name}</p>
                              <p className="text-sm text-slate-600">{addon.description}</p>
                            </div>
                          </div>
                          <p className="font-medium text-slate-800">
                            R$ {addon.totalPrice.toFixed(2).replace('.', ',')}/mês
                          </p>
                        </div>
                      </div>
                    ))}
                  </>
                )}

                {/* Total */}
                <div className="border-t pt-4">
                  <div className="flex items-center justify-between text-lg font-bold">
                    <span>Total Mensal:</span>
                    <span className="text-purple-600">
                      R$ {((store.selectedPlan?.price || 0) + (store.selectedAddOns || []).reduce((sum, addon) => sum + addon.totalPrice, 0)).toFixed(2).replace('.', ',')}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          // Fluxo de Addons - Mostrar addons selecionados (código original)
          primaryAddon && (
            <Card className={`border ${getAddonColor(primaryAddon.id).border} ${getAddonColor(primaryAddon.id).bg}`}>
              <CardHeader>
                <CardTitle className={`flex items-center gap-2 ${getAddonColor(primaryAddon.id).text}`}>
                  <Award className="h-5 w-5" />
                  Addons Selecionados
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Addon Principal */}
                  <div className={`p-4 bg-white rounded-lg border ${getAddonColor(primaryAddon.id).border}`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {React.createElement(getAddonIcon(primaryAddon.id), { 
                          className: `h-6 w-6 ${getAddonColor(primaryAddon.id).textAccent}` 
                        })}
                        <div>
                          <h3 className="font-semibold">{primaryAddon.name}</h3>
                          <p className="text-sm text-muted-foreground">{primaryAddon.description}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-lg">R$ {primaryAddon.price.toFixed(2)}</p>
                        <p className="text-sm text-muted-foreground">+{primaryAddon.value} {primaryAddon.id.includes('user') ? 'usuários' : primaryAddon.id.includes('storage') ? 'GB' : 'créditos'}</p>
                      </div>
                    </div>
                  </div>

                  {/* Addons Relacionados */}
                  {relatedAddons.map((addon, index) => (
                    <div key={addon.id} className="p-4 bg-white rounded-lg border border-gray-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {React.createElement(getAddonIcon(addon.id), { 
                            className: "h-5 w-5 text-gray-600" 
                          })}
                          <div>
                            <h4 className="font-medium">{addon.name}</h4>
                            <p className="text-sm text-muted-foreground">{addon.description}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">R$ {addon.price.toFixed(2)}</p>
                          <p className="text-sm text-muted-foreground">+{addon.value} {addon.id.includes('user') ? 'usuários' : addon.id.includes('storage') ? 'GB' : 'créditos'}</p>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Total */}
                  <div className="border-t pt-4">
                    <div className="flex items-center justify-between text-lg font-bold">
                      <span>Total Mensal:</span>
                      <span className={getAddonColor(primaryAddon.id).textAccent}>R$ {totalPrice.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        )}
      </motion.div>

      {/* Timeline de Próximos Passos */}
      <motion.div variants={cardVariants}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {isPlanFlow ? 'Período de Cortesia' : 'Próximos Passos'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {isPlanFlow ? (
                // Fluxo de Planos - Período de Cortesia
                <>
                  {/* Passo 1 - Plano Ativado */}
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-green-800">Plano Ativado Automaticamente</h3>
                      <p className="text-sm text-muted-foreground mb-2">
                        Seu plano {store.selectedPlan?.name} já está ativo e você pode usar todos os recursos imediatamente
                      </p>
                      <Badge variant="outline" className="border-green-200 text-green-700">
                        ✅ Ativo agora
                      </Badge>
                    </div>
                  </div>

                  {/* Passo 2 - Período de Cortesia */}
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center">
                        <Gift className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-amber-800">7 Dias de Cortesia</h3>
                      <p className="text-sm text-muted-foreground mb-2">
                        Use todos os recursos gratuitamente até <strong>{courtesyEndDate.toLocaleDateString('pt-BR')}</strong>. 
                        Nossa equipe comercial entrará em contato para formalizar o upgrade.
                      </p>
                      <Badge variant="outline" className="border-amber-200 text-amber-700">
                        🎁 Gratuito até {courtesyEndDate.toLocaleDateString('pt-BR')}
                      </Badge>
                    </div>
                  </div>

                  {/* Passo 3 - Contato Comercial */}
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                        <Phone className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-blue-800">Contato Comercial</h3>
                      <p className="text-sm text-muted-foreground mb-2">
                        Nossa equipe entrará em contato em até 24h para alinhar detalhes de pagamento e formalização
                      </p>
                      <Badge variant="outline" className="border-blue-200 text-blue-700">
                        📞 Em até 24 horas
                      </Badge>
                    </div>
                  </div>
                </>
              ) : (
                // Fluxo de Addons - Processo Original
                <>
                  {/* Passo 1 - Imediato */}
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-green-800">Solicitação Registrada</h3>
                      <p className="text-sm text-muted-foreground mb-2">
                        Sua solicitação de addon foi processada e registrada em nosso sistema
                      </p>
                      <Badge variant="outline" className="border-green-200 text-green-700">
                        ✅ Concluído agora
                      </Badge>
                    </div>
                  </div>

                  {/* Passo 2 - Contato */}
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                        <Phone className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-blue-800">Contato da Equipe Comercial</h3>
                      <p className="text-sm text-muted-foreground mb-2">
                        Nossa equipe entrará em contato para confirmar os detalhes do addon
                        {contactInfo?.email && ` via ${contactInfo.email}`}
                        {contactInfo?.phone && ` ou ${contactInfo.phone}`}
                      </p>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-blue-500" />
                        <Badge variant="outline" className="border-blue-200 text-blue-700">
                          📞 Em até 4 horas úteis
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Passo 3 - Ativação */}
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <Star className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-purple-800">Ativação dos Addons</h3>
                      <p className="text-sm text-muted-foreground mb-2">
                        Após confirmação, seus addons serão ativados e você poderá usar os recursos adicionais
                      </p>
                      <Badge variant="outline" className="border-purple-200 text-purple-700">
                        ⚡ Após confirmação comercial
                      </Badge>
                    </div>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Informações de Contato (se disponível) - apenas para fluxo de addons */}
      {!isPlanFlow && contactInfo && (
      <motion.div variants={cardVariants}>
        <Card className="border border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <MessageSquare className="h-5 w-5" />
              Suas Informações de Contato
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-blue-600" />
                <span className="text-sm">{contactInfo.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-blue-600" />
                <span className="text-sm">{contactInfo.phone}</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-blue-600" />
                <span className="text-sm">{contactInfo.name}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-600" />
                <span className="text-sm">
                  Urgência: {contactInfo.urgency === 'urgent' ? 'Urgente' :
                             contactInfo.urgency === 'high' ? 'Alta' :
                             contactInfo.urgency === 'normal' ? 'Normal' : 'Baixa'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
      )}

      {/* Ações */}
      <motion.div variants={cardVariants}>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={handleGoToDashboard}
            className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 gap-2"
          >
            <Home className="h-4 w-4" />
            {isPlanFlow ? 'Explorar Novos Recursos' : 'Ir para Dashboard'}
          </Button>
          
          <Button
            onClick={handleGoToPlans}
            variant="outline"
            className="gap-2"
          >
            <FileText className="h-4 w-4" />
            {isPlanFlow ? 'Gerenciar Planos' : 'Ver Planos Atuais'}
          </Button>
          
          {!isPlanFlow && (
            <Button
              onClick={handleNewUpgrade}
              variant="ghost"
              className="gap-2"
            >
              <ArrowRight className="h-4 w-4" />
              Fazer Novo Upgrade
            </Button>
          )}
        </div>
      </motion.div>

      {/* Mensagem Final */}
      <motion.div variants={cardVariants}>
        <Card className="border-0 bg-gradient-to-r from-slate-50 to-gray-50">
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">
              <strong>
                {isPlanFlow 
                  ? 'Bem-vindo ao seu novo plano!' 
                  : 'Obrigado por expandir seu plano!'
                }
              </strong><br />
              {isPlanFlow 
                ? 'Aproveite os 7 dias de cortesia para explorar todos os recursos. Nossa equipe comercial entrará em contato em breve.'
                : 'Nossa equipe está ansiosa para ajudá-lo a aproveitar ao máximo seus novos recursos.'
              }
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
};

export default SuccessStep; 