/**
 * Componente de coleta de informações de contato para upgrade
 * Captura dados necessários para o processo de upgrade e follow-up
 * <AUTHOR> Internet 2025
 */
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PhoneInput } from '@/components/ui/phone-input';
import { useUpgradeStore, useUpgradeContext, useUpgradeProgress, ContactInfo } from '@/stores/upgradeStore';
import { useAuthStore } from '@/stores/authStore';
import { useUserProfile } from '@/lib/query/hooks/useUserProfile';
import { UpgradeSource } from '@/types/upgrade';
import {
  ArrowRight,
  ArrowLeft,
  User,
  Mail,
  Phone,
  Building,
  MessageSquare,
  Shield,
  AlertCircle,
  Clock,
  UserCheck,
  Gift
} from 'lucide-react';

interface ContactInfoStepProps {
  onNext: () => void;
  onBack: () => void;
  source?: UpgradeSource;
}

// Função para obter conteúdo personalizado baseado no tipo de upgrade
const getUpgradeContent = (source?: UpgradeSource) => {
  switch (source) {
    case 'users-limit':
    case 'users':
      return {
        title: '👥 Como funciona seu upgrade de usuários',
        items: [
          {
            icon: Clock,
            title: '🚀 Ativação Imediata',
            description: 'Suas **novas licenças de usuário** serão ativadas instantaneamente após confirmar.',
            color: 'green'
          },
          {
            icon: Gift,
            title: '🎁 7 Dias de Cortesia',
            description: 'Use as **licenças extras gratuitamente por 7 dias** enquanto formalizamos.',
            color: 'orange'
          },
          {
            icon: UserCheck,
            title: '📞 Suporte Personalizado',
            description: 'Nossa equipe entrará em contato para **otimizar a gestão da sua equipe**.',
            color: 'blue'
          },
          {
            icon: Shield,
            title: '✨ Flexibilidade Total',
            description: 'Adicione ou remova usuários conforme sua **necessidade, sem compromisso**.',
            color: 'purple'
          }
        ],
        summary: {
          title: 'Resumo: Expanda sua equipe hoje!',
          description: 'Após confirmar, você terá **acesso imediato às novas licenças** e nossa equipe cuidará de todo o processo para você. **Mais colaboradores, mais resultados!**'
        }
      };

    case 'storage-full':
    case 'storage':
      return {
        title: '💾 Como funciona seu upgrade de armazenamento',
        items: [
          {
            icon: Clock,
            title: '🚀 Espaço Liberado na Hora',
            description: 'Seu **novo limite de armazenamento** será aplicado instantaneamente após confirmar.',
            color: 'green'
          },
          {
            icon: Gift,
            title: '🎁 7 Dias de Cortesia',
            description: 'Use o **espaço extra gratuitamente por 7 dias** enquanto formalizamos.',
            color: 'orange'
          },
          {
            icon: UserCheck,
            title: '📞 Otimização Personalizada',
            description: 'Nossa equipe entrará em contato para **otimizar o uso do seu armazenamento**.',
            color: 'blue'
          },
          {
            icon: Shield,
            title: '✨ Escalabilidade Garantida',
            description: 'Ajuste seu armazenamento conforme **crescer, sem limites ou burocracias**.',
            color: 'purple'
          }
        ],
        summary: {
          title: 'Resumo: Mais espaço disponível agora!',
          description: 'Após confirmar, você terá **acesso imediato ao novo limite** e nossa equipe cuidará de todo o processo. **Sem preocupações com espaço!**'
        }
      };

    case 'ai-credits':
      return {
        title: '🤖 Como funciona seu upgrade de IA',
        items: [
          {
            icon: Clock,
            title: '🚀 Créditos Liberados na Hora',
            description: 'Seus **novos créditos de IA** serão ativados instantaneamente após confirmar.',
            color: 'green'
          },
          {
            icon: Gift,
            title: '🎁 7 Dias de Cortesia',
            description: 'Use os **créditos extras gratuitamente por 7 dias** enquanto formalizamos.',
            color: 'orange'
          },
          {
            icon: UserCheck,
            title: '📞 Consultoria Especializada',
            description: 'Nossa equipe entrará em contato para **maximizar o uso da IA na sua empresa**.',
            color: 'blue'
          },
          {
            icon: Shield,
            title: '✨ Inteligência Sem Limites',
            description: 'Explore todo o **potencial da IA sem se preocupar** com restrições de uso.',
            color: 'purple'
          }
        ],
        summary: {
          title: 'Resumo: Potencialize sua IA hoje!',
          description: 'Após confirmar, você terá **acesso total aos novos recursos de IA** e nossa equipe cuidará de todo o processo. **Mais inteligência, melhores resultados!**'
        }
      };

    default:
      return {
        title: '🎁 Como funciona seu upgrade',
        items: [
          {
            icon: Clock,
            title: '🚀 Acesso Imediato',
            description: 'Seu plano será **ativado instantaneamente** após confirmar suas informações.',
            color: 'green'
          },
          {
            icon: Gift,
            title: '🎁 7 Dias de Cortesia',
            description: 'Use todas as funcionalidades **gratuitamente por 7 dias** enquanto formalizamos.',
            color: 'orange'
          },
          {
            icon: UserCheck,
            title: '📞 Contato Personalizado',
            description: '**Nosso comercial entrará em contato** para alinhar detalhes e formalizar o upgrade.',
            color: 'blue'
          },
          {
            icon: Shield,
            title: '✨ Sem Compromisso',
            description: 'Experimente primeiro, **formalize depois**. Sem pressão, sem pressa.',
            color: 'purple'
          }
        ],
        summary: {
          title: 'Resumo: Comece a usar hoje mesmo!',
          description: 'Após preencher os dados, você terá **acesso total ao seu novo plano** e nosso time comercial cuidará de todos os detalhes para você. **Zero stress, máximo resultado!**'
        }
      };
  }
};

export const ContactInfoStep: React.FC<ContactInfoStepProps> = ({ onNext, onBack, source }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [errors, setErrors] = useState<Partial<ContactInfo>>({});
  const [isInitialized, setIsInitialized] = useState(false);
  
  // Hooks do upgrade store
  const store = useUpgradeStore();
  const { user } = useAuthStore();
  const { goToStep } = useUpgradeProgress();
  
  // Hook para buscar dados completos do usuário
  const { data: userProfile, isLoading: isLoadingProfile } = useUserProfile();
  
  // Obter conteúdo personalizado baseado no tipo de upgrade
  const upgradeContent = getUpgradeContent(source);
  
  // Estado do formulário - inicializar vazio primeiro
  const [formData, setFormData] = useState<ContactInfo>({
    name: '',
    email: '',
    phone: '',
    company: '',
    role: '',
    urgency: 'normal',
    message: '',
    preferredContactMethod: 'email'
  });

  // Inicializar dados do formulário com prioridade: store > userProfile > user
  useEffect(() => {
    if (isInitialized) return;
    
    // Se já temos dados no store, usar eles
    if (store.contactInfo) {
      setFormData(store.contactInfo);
      setIsInitialized(true);
      return;
    }
    
    // Se temos dados do perfil do usuário, usar eles
    if (userProfile && !isLoadingProfile) {
      setFormData({
        name: userProfile.full_name || user?.full_name || '',
        email: userProfile.email || user?.email || '',
        phone: userProfile.phone || '',
        company: userProfile.company_name || '',
        role: '',
        urgency: 'normal',
        message: '',
        preferredContactMethod: 'email'
      });
      setIsInitialized(true);
      return;
    }
    
    // Fallback para dados básicos do user se o perfil não estiver carregado
    if (user && !userProfile && !isLoadingProfile) {
      setFormData({
        name: user.full_name || '',
        email: user.email || '',
        phone: '',
        company: '',
        role: '',
        urgency: 'normal',
        message: '',
        preferredContactMethod: 'email'
      });
      setIsInitialized(true);
    }
  }, [store.contactInfo, userProfile, user, isLoadingProfile, isInitialized]);

  // Atualizar formData quando o store mudar (caso o usuário volte a este step)
  useEffect(() => {
    if (store.contactInfo && isInitialized) {
      setFormData(prev => ({
        ...prev,
        ...store.contactInfo
      }));
    }
  }, [store.contactInfo, isInitialized]);

  const handleInputChange = (field: keyof ContactInfo, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Limpar erro quando o campo for preenchido
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePhoneChange = (value: string, country?: any) => {
    setFormData(prev => ({ ...prev, phone: value }));
    
    // Limpar erro quando o campo for preenchido
    if (errors.phone) {
      setErrors(prev => ({ ...prev, phone: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nome é obrigatório';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email é obrigatório';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email inválido';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Telefone é obrigatório';
    } else if (formData.phone.length < 8) {
      newErrors.phone = 'Telefone deve ter pelo menos 8 dígitos';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleContinue = async () => {
    if (!validateForm()) {
      return;
    }

    setIsProcessing(true);
    
    try {
      // Salvar informações de contato no store
      store.setContactInfo(formData);
      
      // Completar o step atual e habilitar o próximo
      store.completeStep('contact-info', { contactInfo: formData });
      
      // Aguardar um pouco para garantir que o store foi atualizado
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Navegar para o próximo step
      if (onNext) {
        onNext();
      } else {
        // Fallback: usar goToStep diretamente
        goToStep('confirmation');
      }
      
      console.log('Contact info submitted and step completed:', formData);
    } catch (error) {
      console.error('Erro ao processar:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBack = () => {
    onBack();
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3
      }
    }
  };

  // Mostrar loading enquanto os dados do perfil estão sendo carregados
  if (isLoadingProfile || !isInitialized) {
    return (
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-8"
      >
        <motion.div variants={cardVariants}>
          <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
            <CardHeader>
                          <CardTitle className="flex items-center gap-2 text-blue-800">
              <Gift className="h-5 w-5" />
              {getUpgradeContent(source).title}
            </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full" />
                <span className="ml-3 text-blue-700">Carregando seus dados...</span>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Informação sobre o Processo Comercial */}
      <motion.div variants={cardVariants}>
        <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <Gift className="h-5 w-5" />
              {upgradeContent.title}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {upgradeContent.items.map((item, index) => {
                const colorClasses = {
                  green: { bg: 'bg-green-500', text: 'text-green-800', description: 'text-green-700' },
                  orange: { bg: 'bg-orange-500', text: 'text-orange-800', description: 'text-orange-700' },
                  blue: { bg: 'bg-blue-500', text: 'text-blue-800', description: 'text-blue-700' },
                  purple: { bg: 'bg-purple-500', text: 'text-purple-800', description: 'text-purple-700' }
                }[item.color];

                return (
                  <div key={index} className="flex items-start gap-3">
                    <div className={`${colorClasses.bg} rounded-full p-2 mt-1`}>
                      <item.icon className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h4 className={`font-semibold ${colorClasses.text} mb-1`}>
                        {item.title}
                      </h4>
                      <p className={`text-sm ${colorClasses.description}`}>
                        <span dangerouslySetInnerHTML={{ __html: item.description.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') }} />
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Destaque Principal */}
            <div className="bg-white border border-blue-300 rounded-lg p-4 mt-4">
              <div className="flex items-center gap-3">
                <div className="bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full p-2">
                  <Gift className="h-5 w-5 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="font-bold text-blue-900 text-lg">
                    {upgradeContent.summary.title}
                  </h3>
                  <p className="text-blue-800 text-sm mt-1">
                    <span dangerouslySetInnerHTML={{ __html: upgradeContent.summary.description.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') }} />
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Formulário */}
      <motion.div variants={cardVariants}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Dados Pessoais
            </CardTitle>
            {userProfile && (formData.name || formData.email || formData.company) && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3 mt-2">
                <div className="flex items-center gap-2 text-green-800">
                  <UserCheck className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    Dados preenchidos automaticamente do seu perfil
                  </span>
                </div>
                <p className="text-xs text-green-700 mt-1">
                  Você pode editar qualquer informação conforme necessário.
                </p>
              </div>
            )}
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Nome */}
            <div className="space-y-2">
              <Label htmlFor="name">
                Nome Completo *
              </Label>
              <Input
                id="name"
                type="text"
                placeholder="Seu nome completo"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.name}
                </p>
              )}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">
                Email *
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.email}
                </p>
              )}
            </div>

            {/* Telefone */}
            <div className="space-y-2">
              <Label htmlFor="phone">
                Telefone *
              </Label>
              <PhoneInput
                value={formData.phone}
                onChange={handlePhoneChange}
                placeholder="Digite seu telefone"
                className={errors.phone ? 'border-red-500' : ''}
              />
              {errors.phone && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.phone}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Informações Profissionais */}
      <motion.div variants={cardVariants}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Informações Profissionais
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Empresa */}
            <div className="space-y-2">
              <Label htmlFor="company">
                Empresa (Opcional)
              </Label>
              <Input
                id="company"
                type="text"
                placeholder="Nome da sua empresa"
                value={formData.company}
                onChange={(e) => handleInputChange('company', e.target.value)}
              />
            </div>

            {/* Cargo */}
            <div className="space-y-2">
              <Label htmlFor="role">
                Cargo (Opcional)
              </Label>
              <Input
                id="role"
                type="text"
                placeholder="Seu cargo na empresa"
                value={formData.role}
                onChange={(e) => handleInputChange('role', e.target.value)}
              />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Preferências de Contato */}
      <motion.div variants={cardVariants}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Preferências de Contato
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Método de contato preferido */}
            <div className="space-y-2">
              <Label>
                Como prefere ser contatado?
              </Label>
              <Select
                value={formData.preferredContactMethod}
                onValueChange={(value) => handleInputChange('preferredContactMethod', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="email">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      Email
                    </div>
                  </SelectItem>
                  <SelectItem value="phone">
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Telefone
                    </div>
                  </SelectItem>
                  <SelectItem value="both">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      <Phone className="h-4 w-4" />
                      Ambos
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Urgência */}
            <div className="space-y-2">
              <Label>
                Urgência do upgrade
              </Label>
              <Select
                value={formData.urgency}
                onValueChange={(value) => handleInputChange('urgency', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Baixa - Posso aguardar alguns dias</SelectItem>
                  <SelectItem value="normal">Normal - Alguns dias úteis</SelectItem>
                  <SelectItem value="high">Alta - Preciso em 24h</SelectItem>
                  <SelectItem value="urgent">Urgente - Preciso hoje</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Mensagem adicional */}
            <div className="space-y-2">
              <Label htmlFor="message">
                Mensagem adicional (Opcional)
              </Label>
              <Textarea
                id="message"
                placeholder="Conte-nos mais sobre suas necessidades ou dúvidas específicas..."
                value={formData.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                rows={4}
              />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Aviso de Privacidade */}
      <motion.div variants={cardVariants}>
        <Card className="border border-slate-200 bg-slate-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <Shield className="h-5 w-5 text-green-500 mt-0.5" />
              <div className="text-sm text-muted-foreground">
                <p className="font-medium mb-1">Privacidade e Segurança</p>
                <p>
                  Suas informações são protegidas e utilizadas apenas para processar 
                  seu upgrade e manter contato sobre seu plano. Não compartilhamos 
                  seus dados com terceiros.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Navegação */}
      <motion.div variants={cardVariants}>
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={handleBack}
          >
            Voltar
          </Button>
          
          <Button
            onClick={handleContinue}
            disabled={isProcessing}
            className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 gap-2"
          >
            {isProcessing ? (
              <>
                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                Processando...
              </>
            ) : (
              <>
                Continuar
                <ArrowRight className="h-4 w-4" />
              </>
            )}
          </Button>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default ContactInfoStep; 