/**
 * Confirmation Screen - Tela de confirmação transparente do upgrade
 * <AUTHOR> Internet 2025
 */
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useUpgradeContext, useUpgradePricing, useAddOnSelection } from '@/stores/upgradeStore';
import { CheckCircle, X, AlertCircle, Calendar, Package, Users, CreditCard, Clock, Phone, FileText, Zap } from 'lucide-react';
import { format, addDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface ConfirmationScreenProps {
  /** Callback chamado quando o usuário confirma o upgrade */
  onConfirm?: () => void;
  /** Callback chamado quando o usuário cancela o upgrade */
  onCancel?: () => void;
  /** Se true, mostra estado de carregamento */
  isLoading?: boolean;
  /** Classes CSS adicionais */
  className?: string;
}

export function ConfirmationScreen({
  onConfirm,
  onCancel,
  isLoading = false,
  className = ''
}: ConfirmationScreenProps) {
  const { context, billingCycle } = useUpgradeContext();
  const { pricingSummary, formatPrice, formatPriceWithCycle } = useUpgradePricing();
  const { selectedAddOns } = useAddOnSelection();
  
  // Debug logs
  console.log('[ConfirmationScreen] billingCycle:', billingCycle);
  console.log('[ConfirmationScreen] pricingSummary:', pricingSummary);
  
  // Calcular datas do período de cortesia
  const courtesyStartDate = new Date();
  const courtesyEndDate = addDays(courtesyStartDate, 7);
  
  // Obter plano selecionado do store
  const selectedPlan = useUpgradeContext().context?.currentPlan;
  
  // Calcular dias restantes do período de cortesia
  const daysRemaining = Math.ceil((courtesyEndDate.getTime() - courtesyStartDate.getTime()) / (1000 * 60 * 60 * 24));
  
  // Estado interno para checkbox de confirmação
  const [isAgreed, setIsAgreed] = React.useState(false);
  const [submissionError, setSubmissionError] = React.useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  
  // Handler para confirmação com validação
  const handleConfirm = async () => {
    if (!isAgreed) {
      setSubmissionError('É necessário concordar com os termos para continuar');
      return;
    }
    
    if (!pricingSummary || pricingSummary.total <= 0) {
      setSubmissionError('Não há itens selecionados para o upgrade');
      return;
    }
    
    if (!selectedPlan && selectedAddOns.length === 0) {
      setSubmissionError('É necessário selecionar pelo menos um plano ou add-on');
      return;
    }
    
    setSubmissionError(null);
    setIsSubmitting(true);
    
    try {
      if (onConfirm) {
        await onConfirm();
      }
    } catch (error) {
      setSubmissionError(error instanceof Error ? error.message : 'Erro ao processar upgrade');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handler para cancelamento com confirmação
  const handleCancel = () => {
    if (onCancel) {
      const confirmed = window.confirm(
        'Tem certeza que deseja cancelar o upgrade? Todas as configurações serão perdidas.'
      );
      if (confirmed) {
        onCancel();
      }
    }
  };

  return (
    <TooltipProvider>
      <div className={`max-w-4xl mx-auto p-6 space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-foreground">
          Confirme Seu Upgrade
        </h1>
        <p className="text-muted-foreground text-lg">
          Revise todos os detalhes antes de finalizar sua atualização
        </p>
      </div>

      {/* Layout principal - 2 colunas em desktop, 1 coluna em mobile */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        
        {/* Coluna principal - Revisão do upgrade */}
        <div className="lg:col-span-2 space-y-6">
          
          {/* Resumo do Upgrade */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                Resumo do Upgrade
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Resumo do plano selecionado */}
              <div className="space-y-2">
                <h4 className="font-medium flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Plano Selecionado
                </h4>
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200">
                  {selectedPlan ? (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h5 className="font-semibold text-blue-900">{selectedPlan.name || 'Plano Profissional'}</h5>
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          {billingCycle === 'annual' ? 'Anual' : 'Mensal'}
                        </Badge>
                      </div>
                      <p className="text-sm text-blue-700">
                        {formatPrice(pricingSummary?.planPrice || 0)}
                        <span className="ml-1 text-xs">
                          {billingCycle === 'annual' ? '/ano' : '/mês'}
                        </span>
                      </p>
                      <div className="flex items-center gap-4 text-xs text-blue-600">
                        <span className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          Inicia com 10 usuários, evolui com add-ons
                        </span>
                        <span className="flex items-center gap-1">
                          <Package className="h-3 w-3" />
                          200GB storage
                        </span>
                      </div>
                    </div>
                  ) : (
                    <p className="text-muted-foreground text-sm">
                      Nenhum plano selecionado
                    </p>
                  )}
                </div>
              </div>

              {/* Add-ons selecionados */}
              <div className="space-y-2">
                <h4 className="font-medium flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  Add-ons Selecionados
                </h4>
                <div className="space-y-2">
                  {selectedAddOns.length > 0 ? (
                    selectedAddOns.map((addon) => (
                      <div key={addon.id} className="bg-green-50 p-3 rounded-lg border border-green-200">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-green-900">{addon.name}</p>
                            <p className="text-sm text-green-700">{addon.description}</p>
                            {addon.quantity && addon.quantity > 1 && (
                              <p className="text-xs text-green-600">Quantidade: {addon.quantity}</p>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="font-semibold text-green-900">
                              {formatPrice(addon.totalPrice)}
                            </p>
                            <p className="text-xs text-green-600">
                              {billingCycle === 'annual' ? '/ano' : '/mês'}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="bg-muted p-3 rounded-lg">
                      <p className="text-muted-foreground text-sm">
                        Nenhum add-on selecionado
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* Total com breakdown */}
              <div className="space-y-2">
                {pricingSummary && (
                  <>
                    <div className="flex justify-between text-sm">
                      <span>Plano base:</span>
                      <span>{formatPrice(pricingSummary.planPrice)}</span>
                    </div>
                    {pricingSummary.addOnsPrice > 0 && (
                      <div className="flex justify-between text-sm">
                        <span>Add-ons:</span>
                        <span>{formatPrice(pricingSummary.addOnsPrice)}</span>
                      </div>
                    )}
                    {pricingSummary.discounts.totalDiscount > 0 && (
                      <div className="flex justify-between text-sm text-green-600">
                        <span>Desconto:</span>
                        <span>-{formatPrice(pricingSummary.discounts.totalDiscount)}</span>
                      </div>
                    )}
                    {/* Impostos - Comentado por enquanto */}
                    {/* <div className="flex justify-between text-sm">
                      <span>Impostos:</span>
                      <span>{formatPrice(pricingSummary.taxes.amount)}</span>
                    </div> */}
                    <Separator />
                  </>
                )}
                <div className="flex justify-between items-center font-semibold text-lg">
                  <span>Total {billingCycle === 'annual' ? 'Anual' : 'Mensal'}</span>
                  <span className="text-blue-600">
                    {formatPrice(pricingSummary?.total || 0)}
                  </span>
                </div>
                {billingCycle === 'annual' && pricingSummary?.savings && (
                  <p className="text-sm text-green-600 text-right">
                    Economia de {formatPrice(pricingSummary.savings.annualSavings)} 
                    ({pricingSummary.savings.percentageSaved.toFixed(0)}% de desconto)
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Período de Cortesia */}
          <Card className="border-blue-200 bg-blue-50/50">
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-blue-700">
                <div className="flex items-center gap-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <AlertCircle className="h-5 w-5 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Durante estes 7 dias você terá acesso completo ao novo plano. 
                        Nossa equipe entrará em contato para finalizar os detalhes contratuais.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                  Período de Cortesia - 7 Dias
                </div>
                <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300">
                  {daysRemaining} dias de acesso
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-white p-4 rounded-lg border border-blue-200">
                <h4 className="font-medium text-blue-900 mb-2">
                  O que isso significa?
                </h4>
                <p className="text-blue-800 text-sm leading-relaxed">
                  Você terá acesso total às funcionalidades do novo plano por 7 dias. 
                  Durante este período, nossa equipe comercial entrará em contato para 
                  finalizar os detalhes contratuais.
                </p>
              </div>

              {/* Datas específicas do período de cortesia */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-blue-900">Início do Período</p>
                    <p className="text-sm text-blue-700 font-semibold">
                      {format(courtesyStartDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
                    </p>
                    <p className="text-xs text-blue-600">
                      {format(courtesyStartDate, "EEEE", { locale: ptBR })} às {format(courtesyStartDate, "HH:mm")}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-blue-900">Final do Período</p>
                    <p className="text-sm text-blue-700 font-semibold">
                      {format(courtesyEndDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
                    </p>
                    <p className="text-xs text-blue-600">
                      {format(courtesyEndDate, "EEEE", { locale: ptBR })} às {format(courtesyEndDate, "HH:mm")}
                    </p>
                  </div>
                </div>
              </div>

              {/* Informações adicionais sobre o período */}
              <div className="bg-blue-100 p-3 rounded-lg">
                <h5 className="font-medium text-blue-900 mb-2">Durante este período:</h5>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>✅ Acesso completo a todas as funcionalidades do novo plano</li>
                  <li>✅ Suporte técnico prioritário</li>
                  <li>✅ Migração de dados automática</li>
                  <li>📞 Contato comercial para finalização do contrato</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Timeline do Processo */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Próximos Passos
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Timeline visual */}
              <div className="relative">
                {/* Linha vertical da timeline */}
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gradient-to-b from-green-500 via-blue-500 to-gray-300"></div>
                
                {/* Passos da timeline */}
                <div className="space-y-6">
                  {/* Passo 1 - Ativação Imediata */}
                  <div className="flex items-start gap-4">
                    <div className="relative z-10 flex-shrink-0">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <Zap className="h-4 w-4 text-white" />
                      </div>
                    </div>
                    <div className="flex-1 pb-4">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-semibold text-green-700">Ativação Imediata</h4>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          Agora
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        Seu novo plano será ativado instantaneamente após confirmação
                      </p>
                      <div className="text-xs text-green-600">
                        ✅ Acesso liberado às novas funcionalidades
                      </div>
                    </div>
                  </div>

                  {/* Passo 2 - Contato Comercial */}
                  <div className="flex items-start gap-4">
                    <div className="relative z-10 flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <Phone className="h-4 w-4 text-white" />
                      </div>
                    </div>
                    <div className="flex-1 pb-4">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-semibold text-blue-700">Contato Comercial</h4>
                        <Badge variant="outline" className="bg-blue-50 text-blue-700">
                          1-2 dias úteis
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        Nossa equipe entrará em contato para alinhar detalhes contratuais
                      </p>
                      <div className="text-xs text-blue-600">
                        📞 Agendamento de reunião para finalização
                      </div>
                    </div>
                  </div>

                  {/* Passo 3 - Formalização */}
                  <div className="flex items-start gap-4">
                    <div className="relative z-10 flex-shrink-0">
                      <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center">
                        <FileText className="h-4 w-4 text-white" />
                      </div>
                    </div>
                    <div className="flex-1 pb-4">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-semibold text-indigo-700">Formalização do Contrato</h4>
                        <Badge variant="outline" className="bg-indigo-50 text-indigo-700">
                          Até 7 dias
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        Assinatura de contrato e início da cobrança oficial
                      </p>
                      <div className="text-xs text-indigo-600">
                        📄 Documentação e termos finalizados
                      </div>
                    </div>
                  </div>

                  {/* Passo 4 - Suporte Contínuo */}
                  <div className="flex items-start gap-4">
                    <div className="relative z-10 flex-shrink-0">
                      <div className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center">
                        <Clock className="h-4 w-4 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-semibold text-gray-700">Suporte Contínuo</h4>
                        <Badge variant="outline" className="bg-gray-50 text-gray-700">
                          Sempre
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        Acompanhamento dedicado durante todo o período
                      </p>
                      <div className="text-xs text-gray-600">
                        🎯 Otimização e suporte técnico prioritário
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar - Controles de Confirmação */}
        <div className="space-y-6">
          
          {/* Resumo Rápido */}
          <Card className="sticky top-6">
            <CardHeader>
              <CardTitle className="text-lg">Confirmação</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              
              {/* Badge de contexto */}
              {context?.source && (
                <Badge variant="outline" className="w-full justify-center">
                  Upgrade via {context.source}
                </Badge>
              )}

              {/* Resumo rápido dos valores */}
              {pricingSummary && (
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-lg border border-blue-200">
                  <div className="text-center">
                    <p className="text-sm text-blue-700 mb-1">Total do upgrade</p>
                    <p className="text-2xl font-bold text-blue-900">
                      {formatPrice(pricingSummary.total)}
                    </p>
                    <p className="text-xs text-blue-600">
                      {console.log('[ConfirmationScreen] Sidebar billingCycle:', billingCycle)}
                      {billingCycle === 'annual' ? 'por ano' : 'por mês'}
                    </p>
                    {billingCycle === 'annual' && pricingSummary.savings && (
                      <p className="text-xs text-green-600 mt-1">
                        Economize {formatPrice(pricingSummary.savings.annualSavings)}
                      </p>
                    )}
                  </div>
                </div>
              )}

              {/* Checkbox de confirmação obrigatório */}
              <div className="space-y-3">
                <label className="flex items-start gap-3 cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="mt-1 rounded border-gray-300 focus:ring-2 focus:ring-blue-500"
                    checked={isAgreed}
                    onChange={(e) => setIsAgreed(e.target.checked)}
                    disabled={isLoading}
                  />
                  <span className="text-sm leading-relaxed">
                    Li e compreendo os termos do período de cortesia de 7 dias e 
                    autorizo o contato da equipe comercial.
                  </span>
                </label>
                
                {!isAgreed && (
                  <p className="text-xs text-amber-600 ml-6">
                    É necessário concordar com os termos para continuar
                  </p>
                )}
              </div>

              <Separator />

              {/* Exibição de erros */}
              {submissionError && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-red-600 flex-shrink-0" />
                    <p className="text-sm text-red-700">{submissionError}</p>
                  </div>
                </div>
              )}

              {/* Botões de ação */}
              <div className="space-y-3">
                <Button 
                  onClick={handleConfirm}
                  className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:opacity-50"
                  disabled={isLoading || isSubmitting || !isAgreed}
                >
                  {(isLoading || isSubmitting) ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      {isSubmitting ? 'Confirmando upgrade...' : 'Processando...'}
                    </div>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Confirmar Upgrade
                    </>
                  )}
                </Button>
              </div>

              {/* Informações de suporte */}
              <div className="text-center pt-4 border-t">
                <p className="text-xs text-muted-foreground">
                  Dúvidas? Entre em contato:<br />
                  📧 <EMAIL><br />
                  📞 (11) 99999-9999
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Botão de cancelar sempre visível - fixed no mobile */}
      <div className="fixed bottom-6 right-6 lg:hidden">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              onClick={handleCancel}
              className="h-12 w-12 rounded-full shadow-lg bg-white border-red-200 hover:bg-red-50"
              disabled={isLoading || isSubmitting}
            >
              <X className="h-5 w-5 text-red-600" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="left">
            <p>Cancelar upgrade</p>
          </TooltipContent>
        </Tooltip>
      </div>

      {/* Botão de cancelar para desktop */}
      <div className="hidden lg:flex justify-center">
        <Button
          variant="outline"
          onClick={handleCancel}
          className="border-red-200 text-red-700 hover:bg-red-50"
          disabled={isLoading || isSubmitting}
        >
          <X className="h-4 w-4 mr-2" />
          Cancelar Upgrade
        </Button>
      </div>
    </div>
    </TooltipProvider>
  );
}

export default ConfirmationScreen; 