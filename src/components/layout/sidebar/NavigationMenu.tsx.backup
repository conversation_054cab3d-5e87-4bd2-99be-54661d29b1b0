/**
 * Menu de navegação principal do Vindula Cosmos - Design Elegante e Moderno
 * <AUTHOR> Internet 2025
 */
import React, { useMemo, useEffect, useRef, useState } from "react";
import {
  Home,
  Users,
  MessageSquare,
  Bell,
  Settings,
  Sparkles,
  Building,
  BarChart,
  Trophy,
  Calendar,
  Crown,
  BookOpen,
  LucideIcon,
  Plus,
  Zap,
  TrendingUp,
  Shield,
  Workflow,
  FileText,
  CheckSquare,
  Library,
  Keyboard,
  Rocket,
  ShoppingBag
} from "lucide-react";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuBadge,
  SidebarMenuSkeleton,
} from "@/components/ui/sidebar";
import { useNavigate, useLocation } from "react-router-dom";
import { useUserRoles } from "@/lib/query/hooks/useUserRoles";
import { useFeatureAvailability } from "@/lib/query/hooks/useFeatureFlags";
import { useUnreadNotificationsCount } from "@/lib/query/hooks/useNotifications";
import { useUnreadChatCount } from "@/lib/query/hooks/useUnreadChatCount";
import { useGlobalHotkeys } from "@/lib/hooks/useGlobalHotkeys";
import { usePendingObligations } from "@/hooks/mandatory-reading/use-pending-obligations";
import { useObligationUrgency } from "@/hooks/mandatory-reading/useObligationsWithUrgency";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { PostCreationSelector } from "@/components/feed/PostCreationSelector";
import "./NavigationMenu.css";

// Interface para os itens do menu
interface MenuItem {
  title: string;
  icon: LucideIcon;
  url: string;
  badge?: number;
  description?: string;
  gradient?: string;
  isNew?: boolean;
  isPro?: boolean;
  shortcut?: string;
  disabled?: boolean;
  devBadge?: boolean;
  featureKey?: string; // Chave da feature flag para verificação automática
}

// Interface para grupos de menu
interface MenuGroup {
  title: string;
  items: MenuItem[];
  icon?: LucideIcon;
  color?: string;
}

// Variantes de animação para o menu
const menuVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      staggerChildren: 0.05,
      delayChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, x: -20, scale: 0.95 },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  }
};

const groupVariants = {
  hidden: { opacity: 0, height: 0 },
  visible: {
    opacity: 1,
    height: "auto",
    transition: {
      duration: 0.4,
      ease: "easeOut",
      staggerChildren: 0.05
    }
  }
};

// Componente para badge de obrigações com urgência
interface ObligationBadgeProps {
  critical: number;
  urgent: number;
  normal: number;
  total: number;
  isLoading: boolean;
}

const ObligationBadge = React.memo(({ critical, urgent, normal, total, isLoading }: ObligationBadgeProps) => {
  if (isLoading || total === 0) {
    return total === 0 ? null : (
      <SidebarMenuBadge className="bg-gray-400">
        {total}
      </SidebarMenuBadge>
    );
  }

  // Determinar cor e animação baseado na urgência
  let badgeClass = "";
  let shouldPulse = false;

  if (critical > 0) {
    badgeClass = "bg-red-500 text-white border-red-300";
    shouldPulse = true;
  } else if (urgent > 0) {
    badgeClass = "bg-orange-500 text-white border-orange-300";
    shouldPulse = false;
  } else {
    badgeClass = "bg-blue-500 text-white border-blue-300";
    shouldPulse = false;
  }

  // Texto do tooltip
  const tooltipText = [
    critical > 0 && `${critical} crítica${critical > 1 ? 's' : ''} (vencendo hoje/amanhã)`,
    urgent > 0 && `${urgent} urgente${urgent > 1 ? 's' : ''} (2-3 dias)`,
    normal > 0 && `${normal} normal${normal > 1 ? 'is' : ''} (>3 dias)`
  ].filter(Boolean).join('\n');

  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ 
        scale: 1, 
        opacity: 1,
        ...(shouldPulse && {
          scale: [1, 1.1, 1],
          transition: {
            scale: {
              repeat: Infinity,
              duration: 2,
              ease: "easeInOut"
            }
          }
        })
      }}
      whileHover={critical > 0 ? {
        scale: [1, 1.3, 1.2, 1.3, 1.2],
        rotateZ: [0, -5, 5, -5, 0],
        transition: {
          duration: 0.6,
          ease: "easeInOut",
          times: [0, 0.2, 0.4, 0.6, 1]
        }
      } : { scale: 1.1 }}
      className="relative cursor-pointer"
      title={tooltipText}
    >
      <SidebarMenuBadge 
        className={cn(
          "min-w-6 h-6 rounded-full font-bold text-xs flex items-center justify-center border-2 border-white shadow-lg transition-all duration-200",
          badgeClass,
          shouldPulse && "animate-pulse",
          critical > 0 && "hover:shadow-lg hover:shadow-red-500/50"
        )}
      >
        {total > 99 ? '99+' : total}
        {critical > 0 && (
          <motion.div
            className="absolute -top-1 -right-1 w-2 h-2 bg-red-600 rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [1, 0.7, 1]
            }}
            transition={{
              repeat: Infinity,
              duration: 1.5,
              ease: "easeInOut"
            }}
          />
        )}
      </SidebarMenuBadge>

      {/* Efeito de ondas no hover para casos críticos */}
      {critical > 0 && (
        <motion.div
          className="absolute inset-0 rounded-full border-2 border-red-400"
          initial={{ scale: 1, opacity: 0 }}
          whileHover={{
            scale: [1, 1.8, 2.2],
            opacity: [0, 0.6, 0],
            transition: {
              duration: 1,
              repeat: Infinity,
              ease: "easeOut"
            }
          }}
        />
      )}
    </motion.div>
  );
});

// Componente para botão de nova publicação com seletor premium
const CreatePostButton = React.memo(({ onClick }: { onClick: () => void }) => {
  const [showSelector, setShowSelector] = useState(false);

  // Escutar evento customizado para abrir o seletor via atalho global
  useEffect(() => {
    const handleOpenPostSelector = () => {
      setShowSelector(true);
    };

    window.addEventListener('openPostSelector', handleOpenPostSelector);
    return () => window.removeEventListener('openPostSelector', handleOpenPostSelector);
  }, []);

  const handleClick = () => {
    setShowSelector(true);
  };

  return (
    <>
      <motion.div
        variants={itemVariants}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className="mb-6"
      >
        <Button
          onClick={handleClick}
          className="w-full h-14 bg-gradient-to-r from-orange-500 via-orange-600 to-red-500 hover:from-orange-600 hover:via-orange-700 hover:to-red-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl font-semibold create-button relative overflow-hidden group"
          title="Nova Publicação (Alt+P)"
        >
          {/* Efeito de brilho no hover */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 group-hover:animate-shimmer opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          
          <div className="flex items-center justify-between w-full relative z-10 px-1">
            <div className="flex items-center gap-3">
              <motion.div
                whileHover={{ rotate: 180, scale: 1.1 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="flex-shrink-0"
              >
                <Plus className="h-5 w-5" />
              </motion.div>
              
              <div className="flex flex-col items-start">
                <span className="text-base font-semibold">Nova Publicação</span>
                <div className="flex items-center gap-1 text-xs text-orange-100/80">
                  <Keyboard className="h-3 w-3" />
                  <span className="font-mono tracking-wider">Alt + P</span>
                </div>
              </div>
            </div>
            
            {/* Indicador de IA Premium */}
            <motion.div
              className="text-yellow-300 flex items-center gap-1"
              animate={{ 
                scale: [0.9, 1.1, 0.9],
                opacity: [0.7, 1, 0.7]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <Sparkles className="w-4 h-4" />
              <span className="text-xs font-bold">IA</span>
            </motion.div>
          </div>
        </Button>
      </motion.div>

      <PostCreationSelector
        isOpen={showSelector}
        onClose={() => setShowSelector(false)}
      />
    </>
  );
});

CreatePostButton.displayName = "CreatePostButton";

// Hook para verificar se um item deve ser exibido baseado em feature flags
const useItemAvailability = (item: MenuItem) => {
  const { data: featureAvailability, isLoading } = useFeatureAvailability(item.featureKey || '');
  
  // Se não tem featureKey, o item está sempre disponível (comportamento padrão)
  if (!item.featureKey) {
    return { 
      isAvailable: !item.disabled, 
      isLoading: false,
      isDevelopmentOnly: false
    };
  }
  
  // Se tem featureKey, verificar se está habilitada
  const isFeatureEnabled = featureAvailability?.isFeatureEnabled ?? false;
  const isDevelopmentOnly = featureAvailability?.featureFlag?.development_only ?? false;
  
  
  return { 
    isAvailable: isFeatureEnabled && !item.disabled, 
    isLoading,
    isDevelopmentOnly
  };
};

// Componente para cada item do menu, memoizado para evitar re-renderizações
const MenuItem = React.memo(({ 
  item, 
  navigate,
  isActive,
  obligationUrgency
}: { 
  item: MenuItem; 
  navigate: (url: string) => void;
  isActive: boolean;
  obligationUrgency?: {
    critical: number;
    urgent: number;
    normal: number;
    total: number;
    isLoading: boolean;
  };
}) => {
  const Icon = item.icon;
  const { isAvailable, isDevelopmentOnly } = useItemAvailability(item);
  
  // Se o item não está disponível (feature flag), não renderizar
  if (!isAvailable) {
    return null;
  }
  
  const handleClick = () => {
    if (!item.disabled) {
      navigate(item.url);
    }
  };
  
  return (
    <motion.div
      variants={itemVariants}
      whileHover={!item.disabled ? { x: 2, scale: 1.01 } : {}}
      whileTap={!item.disabled ? { scale: 0.98 } : {}}
      transition={{ duration: 0.2, ease: "easeOut" }}
      className="group"
    >
      <SidebarMenuItem className="relative">
        <SidebarMenuButton
          onClick={handleClick}
          className={cn(
            "w-full h-11 px-3 py-2 rounded-xl relative overflow-hidden menu-item-transition",
            "transition-colors duration-200 ease-in-out",
            item.disabled 
              ? "opacity-50 cursor-not-allowed bg-slate-700/30 text-slate-500"
              : "hover:bg-slate-600/40",
            !item.disabled && isActive 
              ? "!bg-gray-800 !text-white border-l-4 !border-orange-400 font-medium shadow-sm" 
              : !item.disabled 
                ? "text-slate-300 hover:text-white"
                : "text-slate-500"
          )}
          isActive={!item.disabled && isActive}
          title={
            item.disabled 
              ? `${item.title} - Em desenvolvimento` 
              : isDevelopmentOnly
                ? `${item.title} - Visível apenas em desenvolvimento${item.shortcut ? ` (${item.shortcut})` : ''}`
                : item.shortcut 
                  ? `${item.title} (${item.shortcut})` 
                  : item.title
          }
          disabled={item.disabled}
        >
          <div className="flex items-center gap-3 w-full">
            <motion.div
              whileHover={!item.disabled ? { scale: 1.05 } : {}}
              transition={{ duration: 0.15 }}
              className={cn(
                "flex-shrink-0 transition-colors duration-200",
                item.disabled
                  ? "text-slate-600"
                  : isActive 
                    ? "!text-white" 
                    : "text-slate-400 hover:text-white"
              )}
            >
              <Icon className="h-5 w-5" />
            </motion.div>
            
            <div className="flex flex-col flex-1 min-w-0">
              <div className="flex items-center w-full">
                <span className={cn(
                  "font-medium truncate",
                  item.disabled && "line-through",
                  // Reservar espaço para badge quando presente
                  !item.disabled && item.badge !== undefined && item.badge > 0 && "pr-8"
                )}>{item.title}</span>
              </div>
              {item.description && (
                <span className={cn(
                  "text-xs truncate transition-colors duration-200",
                  item.disabled
                    ? "text-slate-600"
                    : isActive 
                      ? "!text-gray-300" 
                      : "text-slate-500 hover:text-slate-300",
                  // Reservar espaço para badge quando presente
                  !item.disabled && item.badge !== undefined && item.badge > 0 && "pr-8"
                )}>
                  {item.disabled 
                    ? "Em desenvolvimento" 
                    : isDevelopmentOnly 
                      ? "Apenas em desenvolvimento" 
                      : item.description}
                </span>
              )}
            </div>
            
            <div className="flex items-center gap-1">
              {(item.devBadge || isDevelopmentOnly) && (
                <Badge className={cn(
                  "text-xs px-1.5 py-0.5 bg-amber-100 text-amber-700 border-amber-200",
                  "animate-pulse"
                )}>
                  {isDevelopmentOnly ? "Dev" : "Em Dev"}
                </Badge>
              )}
              {!item.disabled && item.isNew && !isDevelopmentOnly && (
                <Badge className={cn(
                  "text-xs px-1.5 py-0.5 bg-green-100 text-green-700 border-green-200",
                  "badge-shimmer sparkle-effect"
                )}>
                  Novo
                </Badge>
              )}
              {!item.disabled && item.isPro && (
                <Badge className={cn(
                  "text-xs px-1.5 py-0.5 bg-purple-100 text-purple-700 border-purple-200",
                  "badge-pro-shimmer"
                )}>
                  Pro
                </Badge>
              )}
            </div>
          </div>
          
          {/* Efeito de brilho removido para evitar piscar */}
        </SidebarMenuButton>
        
        {!item.disabled && item.badge !== undefined && item.badge > 0 && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            whileHover={{ scale: 1.1 }}
            className="absolute top-2 right-2"
          >
            {item.title === "Obrigações" && obligationUrgency ? (
              <ObligationBadge 
                critical={obligationUrgency.critical}
                urgent={obligationUrgency.urgent}
                normal={obligationUrgency.normal}
                total={obligationUrgency.total}
                isLoading={obligationUrgency.isLoading}
              />
            ) : (
              <SidebarMenuBadge className={cn(
                "bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg min-w-6 h-6 rounded-full font-bold text-xs flex items-center justify-center border-2 border-white",
                item.title === "Notificações" && "notification-badge"
              )}>
                {item.badge > 99 ? '99+' : item.badge}
              </SidebarMenuBadge>
            )}
          </motion.div>
        )}
      </SidebarMenuItem>
    </motion.div>
  );
});

MenuItem.displayName = "MenuItem";

// Componente para cabeçalho de grupo
const GroupHeader = React.memo(({ 
  title, 
  icon: Icon, 
  color = "text-slate-400" 
}: { 
  title: string; 
  icon?: LucideIcon; 
  color?: string; 
}) => (
  <motion.div
    variants={itemVariants}
    className={cn(
      "flex items-center gap-2 px-3 py-2 mb-2 rounded-lg",
      "group-header-gradient"
    )}
  >
    {Icon && (
      <motion.div
        whileHover={{ scale: 1.1 }}
        transition={{ duration: 0.2 }}
      >
        <Icon className={cn("h-4 w-4", color)} />
      </motion.div>
    )}
    <span className={cn("text-sm font-semibold uppercase tracking-wide", color)}>
      {title}
    </span>
    <div className="flex-1 h-px bg-gradient-to-r from-slate-600 to-transparent ml-2" />
  </motion.div>
));

GroupHeader.displayName = "GroupHeader";

function NavigationMenu() {
  const navigate = useNavigate();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [scrollState, setScrollState] = useState({
    canScrollUp: false,
    canScrollDown: false,
    isScrolling: false
  });
  
  // Usar o hook centralizado de atalhos
  const { navigateWithoutFeedback } = useGlobalHotkeys();
  
  // Função para criar nova publicação
  const handleCreatePost = () => {
    navigateWithoutFeedback('/post/create');
  };
  
  const location = useLocation();
  const { data: unreadNotificationsCount = 0 } = useUnreadNotificationsCount();
  const { data: unreadChatCount = 0, isLoading: isChatCountLoading, error: chatCountError } = useUnreadChatCount();
  const { pendingObligations } = usePendingObligations();
  const obligationUrgency = useObligationUrgency();
  
  // Debug logs removidos para performance
  
  // Usar useUserRoles para verificar permissões (já otimizado)
  const { isAdmin, isLoading } = useUserRoles();
  
  // Log para debug das permissões admin
  useEffect(() => {
    // Log removido para reduzir verbosidade
  }, [isAdmin, isLoading]);
  
  // Hook para detectar scroll e controlar efeitos de fade
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;
    
    let scrollTimeout: NodeJS.Timeout;
    
    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      
      setScrollState({
        canScrollUp: scrollTop > 10,
        canScrollDown: scrollTop < scrollHeight - clientHeight - 10,
        isScrolling: true
      });
      
      // Limpar timeout anterior
      clearTimeout(scrollTimeout);
      
      // Definir que parou de scrollar após 800ms para dar tempo de ver a scrollbar
      scrollTimeout = setTimeout(() => {
        setScrollState(prev => ({ ...prev, isScrolling: false }));
      }, 800);
    };
    
    container.addEventListener('scroll', handleScroll);
    
    // Verificar estado inicial
    handleScroll();
    
    return () => {
      container.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeout);
    };
  }, []);
  
  // Hooks para verificar as features
  const { data: reportsAvailability } = useFeatureAvailability('feature_reports');
  const { data: tenantManagementAvailability } = useFeatureAvailability('feature_tenant_management');
  
  const isReportsEnabled = reportsAvailability?.isFeatureEnabled ?? false;
  const isTenantManagementEnabled = tenantManagementAvailability?.isFeatureEnabled ?? false;
  
  // Função para verificar se a rota está ativa
  const isRouteActive = (url: string): boolean => {
    if (url === location.pathname) return true;
    
    // Para /admin, só ativar se for exatamente /admin ou /admin/ (não sub-rotas)
    if (url === '/admin' && (location.pathname === '/admin' || location.pathname === '/admin/')) return true;
    
    if (url === '/chat' && location.pathname.startsWith('/chat')) return true;
    if (url === '/marketplace' && location.pathname.startsWith('/marketplace')) return true;
    if (url === '/knowledge' && location.pathname.startsWith('/knowledge')) return true;
    if (url === '/library' && location.pathname.startsWith('/library')) return true;
    if (url === '/tasks' && location.pathname.startsWith('/tasks')) return true;
    if (url === '/tasks-new' && location.pathname.startsWith('/tasks-new')) return true;
    if (url === '/obligations' && location.pathname.startsWith('/obligations')) return true;
    if (url === '/people' && location.pathname.startsWith('/people')) return true;
    if (url === '/events' && location.pathname.startsWith('/events')) return true;
    if (url === '/notifications' && location.pathname.startsWith('/notifications')) return true;
    if (url === '/missions' && location.pathname.startsWith('/missions')) return true;
    if (url === '/ranking' && location.pathname.startsWith('/ranking')) return true;
    if (url === '/reports' && location.pathname.startsWith('/reports')) return true;
    return false;
  };
  
  // Construir os grupos de menu organizados
  const menuGroups = useMemo((): MenuGroup[] => {
    const groups: MenuGroup[] = [
      {
        title: "Principal",
        icon: Home,
        color: "text-blue-400",
        items: [
          { 
            title: "Feed", 
            icon: Home, 
            url: "/feed",
            description: "Timeline principal",
            badge: unreadNotificationsCount,
            shortcut: "Alt+1"
          },
          { 
            title: "Knowledge Hub", 
            icon: BookOpen, 
            url: "/knowledge",
            description: "Base de conhecimento",
            shortcut: "Alt+2"
          },
          { 
            title: "Chat", 
            icon: MessageSquare, 
            url: "/chat",
            description: "Mensagens e conversas",
            badge: unreadChatCount,
            shortcut: "Alt+3"
          }
        ]
      },
      {
        title: "Produtividade",
        icon: Zap,
        color: "text-emerald-400",
        items: [
          { 
            title: "Biblioteca", 
            icon: Library, 
            url: "/library",
            description: "Documentos e recursos",
            shortcut: "Alt+4"
          },
          { 
            title: "Tasks", 
            icon: CheckSquare, 
            url: "/tasks-new",
            description: "Tarefas e projetos",
            shortcut: "Alt+5",
            featureKey: "tasks_feature"
          },
          { 
            title: "Obrigações", 
            icon: FileText, 
            url: "/obligations",
            description: "Leituras obrigatórias",
            badge: obligationUrgency.total || 0,
            shortcut: "Alt+6"
          }
        ]
      },
      {
        title: "Colaboração",
        icon: Users,
        color: "text-green-400",
        items: [
          { 
            title: "People Hub", 
            icon: Users, 
            url: "/people",
            description: "Diretório e networking",
            shortcut: "Alt+7",
            isNew: false
          },
          { 
            title: "Eventos", 
            icon: Calendar, 
            url: "/events",
            description: "Calendário e reuniões",
            shortcut: "Alt+8"
          },
        ]
      },
      {
        title: "Gamificação",
        icon: Trophy,
        color: "text-purple-400",
        items: [
          { 
            title: "Missões", 
            icon: Rocket, 
            url: "/missions",
            description: "Desafios e conquistas",
            shortcut: "Alt+M",
            isNew: true,
            featureKey: "missions_feature"
          },
          { 
            title: "Ranking", 
            icon: Trophy, 
            url: "/ranking",
            description: "Classificação e conquistas",
            shortcut: "Alt+0"
          },
          { 
            title: "Marketplace", 
            icon: Crown, 
            url: "/marketplace",
            description: "Benefícios estratégicos",
            isPro: false,
            isNew: false
          },
          { 
            title: "Minhas Compras", 
            icon: ShoppingBag, 
            url: "/offer-purchases",
            description: "Histórico de compras",
            isPro: false,
            isNew: false
          }
        ]
      }
    ];


    // Adicionar seção administrativa
    if (isAdmin) {
      const adminItems: MenuItem[] = [
        {
          title: "Painel de Controle",
          icon: Settings,
          url: "/admin",
          description: "Configurações gerais",
          shortcut: "Ctrl+Alt+1"
        }
      ];

      if (isReportsEnabled) {
        adminItems.push({
          title: "Relatórios",
          icon: BarChart,
          url: "/reports",
          description: "Analytics e métricas",
          shortcut: "Ctrl+Alt+2"
        });
      }

      if (isTenantManagementEnabled) {
        adminItems.push({
          title: "Gestão de Tenants",
          icon: Building,
          url: "/admin/tenants",
          description: "Organizações e licenças"
        });
      }

      // Adicionar Portal LGPD apenas para company_owner
      adminItems.push({
        title: "Portal LGPD",
        icon: Shield,
        url: "/admin/privacy",
        description: "Gestão de privacidade",
        isNew: true
      });

      groups.push({
        title: "Administração",
        icon: Shield,
        color: "text-red-400",
        items: adminItems
      });
    }

    return groups;
  }, [isAdmin, isReportsEnabled, isTenantManagementEnabled, unreadNotificationsCount, unreadChatCount, pendingObligations]);

  // Loading skeleton
  if (isLoading) {
    return (
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="space-y-4 p-3"
      >
        {/* Skeleton do botão de criar */}
        <motion.div
          animate={{ 
            opacity: [0.4, 0.8, 0.4],
            scale: [1, 1.02, 1]
          }}
          transition={{ 
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="h-12 bg-gradient-to-r from-slate-200 via-slate-300 to-slate-200 rounded-xl animate-pulse"
        />
        
        {/* Skeleton dos itens do menu */}
        {Array.from({ length: 8 }).map((_, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ 
              opacity: [0.3, 0.6, 0.3],
              x: 0
            }}
            transition={{ 
              delay: index * 0.1,
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="h-11 bg-slate-100 rounded-xl animate-pulse"
          />
        ))}
        
        {/* Indicador de carregamento */}
        <motion.div
          className="flex items-center justify-center py-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <div className="flex items-center gap-2 text-sm text-slate-500">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ 
                duration: 1,
                repeat: Infinity,
                ease: "linear"
              }}
              className="w-4 h-4 border-2 border-blue-200 border-t-blue-500 rounded-full"
            />
            Carregando menu...
          </div>
        </motion.div>
      </motion.div>
    );
  }

  return (
    <motion.div
      ref={scrollContainerRef}
      variants={menuVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        "p-3 space-y-6 menu-container relative",
        scrollState.isScrolling && "scrolling",
        scrollState.canScrollUp && "can-scroll-up",
        scrollState.canScrollDown && "can-scroll-down"
      )}
      style={{
        "--scroll-fade-top": scrollState.canScrollUp ? "1" : "0",
        "--scroll-fade-bottom": scrollState.canScrollDown ? "1" : "0"
      } as React.CSSProperties}
    >
      {/* Botão de Nova Publicação */}
      <CreatePostButton onClick={handleCreatePost} />

      {/* Grupos de Menu */}
      <AnimatePresence>
        {menuGroups.map((group, groupIndex) => (
          <motion.div
            key={group.title}
            variants={groupVariants}
            className="space-y-1"
          >
            <GroupHeader 
              title={group.title} 
              icon={group.icon} 
              color={group.color} 
            />
            
            <SidebarMenu>
              {group.items.map((item) => (
                <MenuItem 
                  key={`${item.title}-${item.url}`}
                  item={item} 
                  navigate={navigate}
                  isActive={isRouteActive(item.url)}
                  obligationUrgency={obligationUrgency}
                />
              ))}
            </SidebarMenu>
          </motion.div>
        ))}
      </AnimatePresence>
    </motion.div>
  );
}

// Exportar o componente memoizado como default
export default NavigationMenu;
