/**
 * Componente de layout principal da aplicação, responsável por renderizar a estrutura básica
 * incluindo TopBar, Sidebar e conteúdo principal com suporte a safe areas em dispositivos móveis
 * <AUTHOR> Internet 2025
 */

import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "./AppSidebar";
import { TopBar } from "./TopBar";
import { ChatNotifications } from '../chat/ChatNotifications';
import { useEffect } from 'react';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { useNavigationTracking } from '@/hooks/tracking/useNavigationTracking'; 
import { notificationService } from '@/services/notifications';
import { useUserLevel } from "@/hooks/useUserLevel";
import { LevelUpAnimation } from "@/components/gamification/LevelUpAnimation";
import { useUnifiedRealtime } from "@/contexts/UnifiedRealtimeProvider";
import { MedalUnlockedAnimation } from "@/components/gamification/MedalUnlockedAnimation";
import { BirthdayCardDialog } from "@/components/birthday/BirthdayCardDialog";
import { PromotionCelebrationModal } from "@/components/admin/PromotionCelebrationModal";
import { useSafeArea } from "@/hooks/useSafeArea";
import { usePlatform } from "@/hooks/usePlatform";
import { PlatformWrapper } from "@/components/platform/PlatformWrapper";
import { GlobalPermissionDebug } from "@/components/debug/GlobalPermissionDebug";
import { cn } from "@/lib/utils";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { FloatingTabBar } from "@/components/layout/mobile/FloatingTabBar";

/**
 * Interface para props do MainLayout
 */
interface MainLayoutProps {
  /**
   * Conteúdo principal do layout
   */
  children: React.ReactNode;
}

/**
 * Componente de layout principal da aplicação
 * @param {MainLayoutProps} props - Props do componente
 */
export function MainLayout({ children }: MainLayoutProps) {
  const { level, showLevelUp, setShowLevelUp, unlockedAssets } = useUserLevel();
  
  // Hook centralizado para tracking de navegação
  useNavigationTracking();
  const { isNative, isIOS } = usePlatform();
  
  // Detectar mobile para mostrar floating tab bar
  const isMobileQuery = useMediaQuery("(max-width: 768px)");
  const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;
  
  const { achievedMedal, clearAchievedMedal, birthdayCardData, clearBirthdayCard, promotionCelebrationData, clearPromotionCelebration } = useUnifiedRealtime();
  
  
  
  const { cssVariables, isLoaded } = useSafeArea();

  /**
   * Efeito para registrar visualização de página (Task 2.3)
   */
  useEffect(() => {
    // Inicializa o OneSignal quando o MainLayout é montado
    notificationService.initialize().catch(error => {
      logQueryEvent('MainLayout', 'Erro ao inicializar OneSignal:', error, 'error'); 
    });
  }, []);

 

  /**
   * Efeito para aplicar as variáveis CSS de safe area ao elemento raiz
   */
  useEffect(() => {
    if (isLoaded && isNative) {
      // Verificar se os valores realmente mudaram antes de aplicar
      const currentValues = {
        top: document.documentElement.style.getPropertyValue('--safe-area-top'),
        bottom: document.documentElement.style.getPropertyValue('--safe-area-bottom'),
        left: document.documentElement.style.getPropertyValue('--safe-area-left'),
        right: document.documentElement.style.getPropertyValue('--safe-area-right')
      };
      
      const newValues = {
        top: cssVariables['--safe-area-top'],
        bottom: cssVariables['--safe-area-bottom'],
        left: cssVariables['--safe-area-left'],
        right: cssVariables['--safe-area-right']
      };
      
      // Verificar se algum valor mudou
      const valuesChanged = 
        currentValues.top !== newValues.top ||
        currentValues.bottom !== newValues.bottom ||
        currentValues.left !== newValues.left ||
        currentValues.right !== newValues.right;
      
      if (valuesChanged) {
        logQueryEvent('MainLayout', 'Aplicando safe area insets', cssVariables);
        
        // Aplicar variáveis CSS ao elemento :root
        Object.entries(cssVariables).forEach(([key, value]) => {
          document.documentElement.style.setProperty(key, value);
        });
      }
    }
  }, [isLoaded, cssVariables]);

  return (
    <SidebarProvider>
      <div 
        className="min-h-screen bg-background w-full grid grid-rows-[auto_1fr]"
        style={isNative ? {
          // REMOVIDO: paddingTop duplicado - TopBar já gerencia safe-area-top
          // Manter apenas bottom, left, right para o container principal
          paddingBottom: 'var(--safe-area-bottom)',
          paddingLeft: 'var(--safe-area-left)',
          paddingRight: 'var(--safe-area-right)'
        } : undefined}
      >
        <TopBar />
        <div className="flex overflow-hidden min-h-0">
          <AppSidebar />
          <main className={cn(
            "flex-1 overflow-auto",
            isNative ? "p-0" : "p-4" // ZERO padding no mobile
          )}>
            {/* Animação de Level Up */}
            <LevelUpAnimation 
              show={showLevelUp}
              onClose={() => {
                setShowLevelUp(false, 'fechamento pelo usuário via MainLayout');
              }}
              level={level?.current_level || 0}
              title={level?.current_title || ''}
              unlockedAssets={unlockedAssets}
            />
            {/* Animação de medalha conquistada */}
            {achievedMedal && (
              <MedalUnlockedAnimation
                medal={achievedMedal.medal}
                onClose={clearAchievedMedal}
                autoOpen={achievedMedal.autoOpen}
              />
            )}
            {/* Modal de cartão de aniversário (seguindo padrão das medalhas) */}
            {birthdayCardData && (
              <BirthdayCardDialog
                show={true}
                onClose={clearBirthdayCard}
                cardData={birthdayCardData}
              />
            )}
            {/* Modal de celebração de promoção */}
            {promotionCelebrationData && (
              <PromotionCelebrationModal
                isOpen={true}
                onClose={clearPromotionCelebration}
                promotionData={promotionCelebrationData}
              />
            )}
            {/* Componente global de debug de permissões */}
            {/* <GlobalPermissionDebug /> */}
            {children}
          </main>
        </div>
        <ChatNotifications />
        
        {/* Floating Tab Bar para mobile - sempre visível */}
        {(isNative || isMobile || isMobileQuery) && (
          <FloatingTabBar />
        )}
      </div>
    </SidebarProvider>
  );
}