/**
 * Layout administrativo simplificado para integração com design moderno
 * <AUTHOR> Internet 2025
 */
import { AdminSidebar, MobileAdminNav } from "@/components/admin/AdminSidebar";
import { motion } from "framer-motion";
import { Settings, Menu } from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import React from "react";
import { SessionReconnector } from "@/components/auth/SessionReconnector";
import { useAuthStore } from "@/stores/authStore";
import { 
  SidebarProvider, 
  SidebarTrigger,
  useSidebar
} from "@/components/ui/sidebar";

const SIDEBAR_STORAGE_KEY = "admin-sidebar-state";

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  icon?: React.ReactNode;
}

export const AdminLayout = React.memo(function AdminLayout({
  children,
  title = "Painel de Controle",
  description = "Gerencie usuários, permissões e configurações do sistema",
  icon = <Settings className="h-6 w-6 sm:h-8 sm:w-8 text-orange-500" />,
}: AdminLayoutProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState<boolean>(() => {
    // Verifica se é um dispositivo móvel (largura < 768px)
    const isMobile = window.innerWidth < 768;
    
    // Em dispositivos móveis, sempre inicia fechado
    if (isMobile) {
      return false;
    }
    
    // Em desktop, inicializa o estado da sidebar a partir do localStorage
    const savedState = localStorage.getItem(SIDEBAR_STORAGE_KEY);
    // MUDANÇA: Padrão agora é fechado (false) ao invés de aberto (true)
    return savedState ? savedState === "true" : false;
  });
  
  // Memoizar handler de resize
  const handleResize = React.useCallback(() => {
    const isMobile = window.innerWidth < 768;
    if (isMobile && sidebarOpen) {
      setSidebarOpen(false);
    }
  }, [sidebarOpen]);

  // Atualiza o estado da sidebar quando a largura da janela muda
  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  // Salvar mudanças de estado no localStorage (apenas para desktop)
  useEffect(() => {
    const isMobile = window.innerWidth < 768;
    if (!isMobile) {
      localStorage.setItem(SIDEBAR_STORAGE_KEY, String(sidebarOpen));
    }
  }, [sidebarOpen]);

  const { state } = useSidebar();

  // Memoizar handler de mudança de estado da sidebar
  const handleSidebarChange = React.useCallback((open: boolean) => {
    setSidebarOpen(open);
  }, []);
  
  return (
    <div className="w-full max-w-[100vw] overflow-x-hidden relative min-h-screen">
      {/* Navegação mobile no topo */}
      <MobileAdminNav />
      
      {/* Conteúdo principal com sidebar secundário retrátil */}
              <SidebarProvider 
          defaultOpen={sidebarOpen} 
          open={sidebarOpen}
          onOpenChange={handleSidebarChange}
        >
        <div className="flex w-full overflow-x-hidden min-h-screen">
          {/* Sidebar vertical oculta em telas menores */}
          <AdminSidebar className="hidden lg:block" />
          {/* Conteúdo principal sem padding extra para dar espaço ao design moderno */}
          <div className="flex-1 max-w-full">
            <div className="w-full">
              {children}
            </div>
          </div>
          
          {/* Botão de toggle melhorado - posicionado corretamente acima da TopBar */}
          <motion.div
            className="fixed z-30 hidden md:block"
            style={{
              top: "5.5rem", // 88px - Abaixo da TopBar (64px) + margem
              left: state === "collapsed" ? "1.75rem" : "17.25rem"
            }}
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ 
              duration: 0.3,
              type: "spring",
              stiffness: 200,
              damping: 15
            }}
          >
            <SidebarTrigger 
              className="group bg-white/95 backdrop-blur-md shadow-xl border-2 border-orange-200/60 h-12 w-12 text-gray-600 hover:text-orange-600 hover:border-orange-400 hover:shadow-2xl rounded-xl transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:ring-offset-2 focus:ring-offset-white/10" 
            >
              <Menu className="h-5 w-5 transition-transform duration-300 group-hover:rotate-180" />
            </SidebarTrigger>
          </motion.div>
        </div>
      </SidebarProvider>
    </div>
  );
});
