import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Send, Mic, Upload, X, Play, Pause, Video, ImagePlus } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";
import { PostAudioService } from "@/services/post-audio";
import { PhotoGalleryUploader } from "./PhotoGalleryUploader";

interface PhotoData {
  id: string;
  url: string;
  file: File;
  uploading?: boolean;
}

interface CreatePostDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  newPost: string;
  onNewPostChange: (value: string) => void;
  onCreatePost: (audioData?: { url: string; duration: number }, videoData?: { url: string; duration: number }, photosData?: PhotoData[]) => void;
  isCreating: boolean;
}

// Componente para gravação/upload de áudio
const AudioRecorder = ({ 
  onAudioRecorded, 
  onRemoveAudio, 
  audioData 
}: { 
  onAudioRecorded: (data: { url: string; duration: number }) => void;
  onRemoveAudio: () => void;
  audioData?: { url: string; duration: number } | null;
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const chunksRef = useRef<Blob[]>([]);

  // Limpar timer ao desmontar
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(chunksRef.current, { type: 'audio/webm' });
        
        // Calcular duração do áudio
        const audioUrl = URL.createObjectURL(audioBlob);
        const audio = new Audio(audioUrl);
        
        audio.onloadedmetadata = async () => {
          const duration = Math.round(audio.duration);
          
          try {
            // Upload do áudio
            const uploadedUrl = await PostAudioService.uploadAudio(audioBlob, `recording-${Date.now()}.webm`);
            onAudioRecorded({ url: uploadedUrl, duration });
          } catch (error) {
            console.error('Erro ao fazer upload do áudio:', error);
          }
          
          URL.revokeObjectURL(audioUrl);
        };
      };

      mediaRecorder.start();
      setIsRecording(true);
      setRecordingTime(0);

      // Timer para mostrar tempo de gravação
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

    } catch (error) {
      console.error('Erro ao acessar microfone:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
      
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validar tipo de arquivo
    if (!file.type.startsWith('audio/')) {
      alert('Por favor, selecione um arquivo de áudio válido.');
      return;
    }

    // Validar tamanho (máx 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('Arquivo muito grande. Máximo 10MB.');
      return;
    }

    try {
      // Calcular duração
      const audioUrl = URL.createObjectURL(file);
      const audio = new Audio(audioUrl);
      
      audio.onloadedmetadata = async () => {
        const duration = Math.round(audio.duration);
        
        try {
          // Upload do arquivo
          const uploadedUrl = await PostAudioService.uploadAudio(file, file.name);
          onAudioRecorded({ url: uploadedUrl, duration });
        } catch (error) {
          console.error('Erro ao fazer upload do áudio:', error);
        }
        
        URL.revokeObjectURL(audioUrl);
      };
    } catch (error) {
      console.error('Erro ao processar arquivo de áudio:', error);
    }
  };

  const togglePlayback = () => {
    if (!audioData?.url || !audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (audioData) {
    return (
      <div className="border border-orange-200 bg-orange-50/30 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={togglePlayback}
              className="h-8 w-8 p-0 hover:bg-orange-100"
            >
              {isPlaying ? (
                <Pause className="h-4 w-4 text-orange-600" />
              ) : (
                <Play className="h-4 w-4 text-orange-600" />
              )}
            </Button>
            
            <div className="flex flex-col">
              <span className="text-sm font-medium text-orange-700">
                Áudio anexado
              </span>
              <span className="text-xs text-orange-600">
                Duração: {formatTime(audioData.duration)}
              </span>
            </div>
          </div>
          
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onRemoveAudio}
            className="h-8 w-8 p-0 hover:bg-red-100"
          >
            <X className="h-4 w-4 text-red-600" />
          </Button>
        </div>
        
        <audio
          ref={audioRef}
          src={audioData.url}
          onEnded={() => setIsPlaying(false)}
          preload="metadata"
        />
      </div>
    );
  }

  return (
    <div className="border border-dashed border-gray-300 rounded-lg p-4">
      <div className="flex items-center justify-center gap-4">
        {isRecording ? (
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium text-red-600">
                Gravando: {formatTime(recordingTime)}
              </span>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={stopRecording}
              className="border-red-200 text-red-600 hover:bg-red-50"
            >
              Parar Gravação
            </Button>
          </div>
        ) : (
          <>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={startRecording}
              className="flex items-center gap-2"
            >
              <Mic className="h-4 w-4" />
              Gravar Áudio
            </Button>
            
            <span className="text-sm text-gray-500">ou</span>
            
            <div className="relative">
              <input
                type="file"
                accept="audio/*"
                onChange={handleFileUpload}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Upload className="h-4 w-4" />
                Enviar Arquivo
              </Button>
            </div>
          </>
        )}
      </div>
      
      <p className="text-xs text-gray-500 text-center mt-2">
        Grave um áudio ou envie um arquivo (máx 10MB)
      </p>
    </div>
  );
};

// Componente para upload de vídeo
const VideoRecorder = ({ 
  onVideoRecorded, 
  onRemoveVideo, 
  videoData 
}: { 
  onVideoRecorded: (data: { url: string; duration: number }) => void;
  onRemoveVideo: () => void;
  videoData?: { url: string; duration: number } | null;
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validar tipo de arquivo
    if (!file.type.startsWith('video/')) {
      alert('Por favor, selecione um arquivo de vídeo válido.');
      return;
    }

    // Validar tamanho (máx 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('Arquivo muito grande. Máximo 10MB.');
      return;
    }

    try {
      // Calcular duração
      const videoUrl = URL.createObjectURL(file);
      const video = document.createElement('video');
      
      video.onloadedmetadata = async () => {
        const duration = Math.round(video.duration);
        
        try {
          // Upload do vídeo usando o mesmo serviço de áudio (mesmo bucket)
          const uploadedUrl = await PostAudioService.uploadAudio(file, file.name, 'mp4');
          onVideoRecorded({ url: uploadedUrl, duration });
        } catch (error) {
          console.error('Erro ao fazer upload do vídeo:', error);
          alert('Erro ao fazer upload do vídeo. Tente novamente.');
        }
        
        URL.revokeObjectURL(videoUrl);
      };
      
      video.src = videoUrl;
    } catch (error) {
      console.error('Erro ao processar arquivo de vídeo:', error);
      alert('Erro ao processar vídeo. Tente novamente.');
    }
  };

  const togglePlayback = () => {
    if (!videoData?.url || !videoRef.current) return;

    if (isPlaying) {
      videoRef.current.pause();
      setIsPlaying(false);
    } else {
      videoRef.current.play();
      setIsPlaying(true);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (videoData) {
    return (
      <div className="border border-blue-200 bg-blue-50/30 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={togglePlayback}
              className="h-8 w-8 p-0 hover:bg-blue-100"
            >
              {isPlaying ? (
                <Pause className="h-4 w-4 text-blue-600" />
              ) : (
                <Play className="h-4 w-4 text-blue-600" />
              )}
            </Button>
            
            <div className="flex flex-col">
              <span className="text-sm font-medium text-blue-700">
                Vídeo anexado
              </span>
              <span className="text-xs text-blue-600">
                Duração: {formatTime(videoData.duration)}
              </span>
            </div>
          </div>
          
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onRemoveVideo}
            className="h-8 w-8 p-0 hover:bg-red-100"
          >
            <X className="h-4 w-4 text-red-600" />
          </Button>
        </div>
        
        <video
          ref={videoRef}
          src={videoData.url}
          onEnded={() => setIsPlaying(false)}
          preload="metadata"
          className="w-full mt-3 rounded-lg max-h-48"
          controls={false}
        />
      </div>
    );
  }

  return (
    <div className="border border-dashed border-gray-300 rounded-lg p-4">
      <div className="flex items-center justify-center gap-4">
        <div className="relative">
          <input
            type="file"
            accept="video/*"
            onChange={handleFileUpload}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          />
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Video className="h-4 w-4" />
            Adicionar Vídeo
          </Button>
        </div>
      </div>
      
      <p className="text-xs text-gray-500 text-center mt-2">
        Selecione um arquivo de vídeo (máx 10MB)
      </p>
    </div>
  );
};

export const CreatePostDialog = ({
  isOpen,
  onOpenChange,
  newPost,
  onNewPostChange,
  onCreatePost,
  isCreating
}: CreatePostDialogProps) => {
  const [audioData, setAudioData] = useState<{ url: string; duration: number } | null>(null);
  const [videoData, setVideoData] = useState<{ url: string; duration: number } | null>(null);
  const [photosData, setPhotosData] = useState<PhotoData[]>([]);

  const handleCreatePost = () => {
    onCreatePost(audioData || undefined, videoData || undefined, photosData.length > 0 ? photosData : undefined);
  };

  const handleRemoveAudio = () => {
    setAudioData(null);
  };

  const handleRemoveVideo = () => {
    setVideoData(null);
  };

  // Resetar estado quando dialog fechar
  useEffect(() => {
    if (!isOpen) {
      setAudioData(null);
      setVideoData(null);
      setPhotosData([]);
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button>Nova Publicação</Button>
      </DialogTrigger>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Criar nova publicação</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <Textarea
            placeholder="O que você quer compartilhar?"
            value={newPost}
            onChange={(e) => onNewPostChange(e.target.value)}
            className="min-h-[100px]"
          />
          
          {/* Componente de áudio */}
          <AudioRecorder
            onAudioRecorded={setAudioData}
            onRemoveAudio={handleRemoveAudio}
            audioData={audioData}
          />

          {/* Componente de vídeo */}
          <VideoRecorder
            onVideoRecorded={setVideoData}
            onRemoveVideo={handleRemoveVideo}
            videoData={videoData}
          />

          {/* Componente de galeria de fotos */}
          <PhotoGalleryUploader
            onPhotosChange={setPhotosData}
            photos={photosData}
          />
          
          <div className="flex justify-end">
            <Button 
              onClick={handleCreatePost}
              disabled={isCreating || (!newPost.trim() && !audioData && !videoData && photosData.length === 0)}
            >
              {isCreating ? (
                "Publicando..."
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Publicar
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};