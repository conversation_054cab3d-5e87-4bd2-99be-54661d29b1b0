import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { PostActions } from "./PostActions";
import { CommentList } from "./CommentList";
import { cn } from "@/lib/utils";
import DOMPurify from "dompurify";
import { EnhancedOptimizedAvatar } from "@/components/common/EnhancedOptimizedAvatar";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  Globe,
  Building2,
  Users,
  UserSquare2,
  MessageCircle,
  Heart,
  Share2,
  MoreVertical,
  Edit3,
  History,
  Trash2,
} from "lucide-react";
import { format, isAfter } from "date-fns";
import { ptBR } from "date-fns/locale";
import { PollVoting } from "@/components/poll/PollVoting";
import { PostPhotoGallery } from "./PostPhotoGallery";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Post } from "@/types/post.types";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { memo, useMemo, useState } from "react";
import { Link } from "react-router-dom";
import { useCanEditPost } from "@/lib/query/hooks/usePosts";

interface PostCardProps {
  post: Post;
  currentUserId: string;
  formatDate: (date: string) => string;
  onEdit?: (postId: string) => void;
  onDelete?: (postId: string) => void;
  onViewHistory?: (postId: string) => void;
}

function PostCardComponent({
  post,
  currentUserId,
  formatDate,
  onEdit,
  onDelete,
  onViewHistory,
}: PostCardProps) {
  // Processamento de audiência memoizado para evitar recálculos desnecessários
  const { audience, audienceType, audienceTargets, audienceTargetCount } =
    useMemo(() => {
      // @ts-expect-error - A propriedade post_audience não está definida no tipo Post,
      // mas pode existir nos dados recebidos da API
      const postAudience = post.post_audience;
      let processedAudience = post.audience;

      if (
        !processedAudience &&
        postAudience &&
        Array.isArray(postAudience) &&
        postAudience.length > 0
      ) {
        const firstAudience = postAudience[0];
        const audienceType = firstAudience.target_type as
          | "all"
          | "department"
          | "team"
          | "user";

        if (audienceType === "all") {
          processedAudience = { type: "all" };
        } else {
          const targets = postAudience
            .map((a) => a.target_id)
            .filter((id): id is string => id !== null);

          processedAudience = {
            type: audienceType,
            targets,
            targetCount: targets.length,
          };
        }
      }

      return {
        audience: processedAudience,
        audienceType: processedAudience?.type,
        audienceTargets: processedAudience?.targets,
        audienceTargetCount: processedAudience?.targetCount,
      };
      // @ts-expect-error - post_audience não está no tipo, mas é usado para processamento
    }, [post.audience, post.post_audience]);

  // Hook para verificar se usuário pode editar o post
  const { data: canEditData } = useCanEditPost(post.author.id === currentUserId ? post.id : undefined);
  
  // Verificar se usuário é o autor do post
  const isAuthor = post.author.id === currentUserId;
  
  // Sanitizar e renderizar o HTML de forma segura
  const sanitizedContent = useMemo(() => {
    return DOMPurify.sanitize(post.content);
  }, [post.content]);

  // Verificar se o post foi agendado e já foi publicado
  const isScheduledAndPublished =
    post.status === "scheduled" &&
    post.scheduled_at &&
    new Date(post.scheduled_at) <= new Date();

  // Renderizar o ícone de audiência baseado no tipo
  const renderAudienceIcon = () => {
    switch (audienceType) {
      case "all":
        return <Globe className="h-3 w-3" />;
      case "department":
        return <Building2 className="h-3 w-3" />;
      case "team":
        return <Users className="h-3 w-3" />;
      case "user":
        return <UserSquare2 className="h-3 w-3" />;
      default:
        return <Globe className="h-3 w-3" />;
    }
  };

  // Buscar nomes dos usuários quando a audiência for específica
  const { data: targetUsers } = useQuery({
    queryKey: ["target-users", post.id],
    queryFn: async () => {
      if (!audience || audience.type !== "user" || !audience.targets) {
        return [];
      }

      const { data } = await supabase
        .from("profiles")
        .select("id, full_name")
        .in("id", audience.targets)
        .order("full_name");

      return data || [];
    },
    enabled:
      !!audience && audience.type === "user" && !!audience.targets?.length,
  });

  // Obter texto da audiência
  const getAudienceText = () => {
    if (!audience || audienceType === "all") {
      return "Todos os funcionários";
    }

    const count = audienceTargetCount || audienceTargets?.length || 0;
    let userCount;

    switch (audienceType) {
      case "department":
        return `${count} departamento${count !== 1 ? "s" : ""}`;
      case "team":
        return `${count} equipe${count !== 1 ? "s" : ""}`;
      case "user":
        userCount = targetUsers?.length || count;
        return `${userCount} pessoa${userCount !== 1 ? "s" : ""}`;
      default:
        return "Todos os funcionários";
    }
  };

  // Obter texto detalhado da audiência para o tooltip
  const getAudienceTooltipText = () => {
    if (!audience || audienceType === "all") {
      return "Visível para todos os funcionários da empresa";
    }

    let targetNames = "";

    switch (audienceType) {
      case "department":
        return `Visível apenas para os departamentos: ${
          audienceTargets?.join(", ") || ""
        }`;
      case "team":
        return `Visível apenas para as equipes: ${
          audienceTargets?.join(", ") || ""
        }`;
      case "user":
        targetNames = targetUsers
          ? targetUsers.map((user) => user.full_name).join(", ")
          : audienceTargets?.join(", ") || "";
        return `Visível apenas para: ${targetNames}`;
      default:
        return "Visível para todos os funcionários da empresa";
    }
  };

  return (
    <Card className="overflow-hidden border-gray-200 dark:border-gray-800 shadow-md hover:shadow-lg transition-shadow duration-200 bg-white dark:bg-gray-900">
      {/* Cabeçalho do post com informações do autor */}
      <CardHeader className="px-5 py-4 space-y-0 bg-gradient-to-r from-gray-50/80 to-white dark:from-gray-900 dark:to-gray-800/90 border-b border-gray-200 dark:border-gray-800">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Link to={`/user/${post.author.id}`} className="hover:opacity-80 transition-opacity">
              <EnhancedOptimizedAvatar
                src={post.author.avatar_url}
                fallback={post.author.full_name?.charAt(0).toUpperCase() || "U"}
                alt={post.author.full_name || "User avatar"}
                className="h-11 w-11 border-2 border-primary/20 dark:border-primary/40 shadow-sm"
                userId={post.author.id}
              />
            </Link>
            <div className="flex-1 min-w-0">
              <Link 
                to={`/user/${post.author.id}`}
                className="font-semibold text-gray-900 dark:text-gray-100 truncate hover:underline hover:text-primary transition-colors"
              >
                {post.author.full_name}
              </Link>
              <div className="text-xs text-gray-500 dark:text-gray-400 flex flex-wrap items-center gap-2">
                <span className="font-medium">
                  {formatDate(post.created_at)}
                </span>

                {/* Badge para posts agendados */}
                {post.status === "scheduled" && post.scheduled_at && (
                  <Badge
                    variant="outline"
                    className="ml-1 flex items-center gap-1 text-xs bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-400 border-amber-200/50 dark:border-amber-700/30"
                  >
                    <Calendar className="h-3 w-3" />
                    {isScheduledAndPublished ? (
                      <span>Publicado automaticamente</span>
                    ) : (
                      <span>
                        Agendado para{" "}
                        {format(
                          new Date(post.scheduled_at),
                          "d MMM 'às' HH:mm",
                          {
                            locale: ptBR,
                          }
                        )}
                      </span>
                    )}
                  </Badge>
                )}

                {/* Badge de audiência com tooltip */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge
                        variant="outline"
                        className="flex items-center gap-1 text-xs bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary/80 border-primary/30"
                      >
                        {renderAudienceIcon()}
                        <span>{getAudienceText()}</span>
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{getAudienceTooltipText()}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
          
          {/* Menu de opções do post - apenas para o autor */}
          {isAuthor && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="h-8 w-8 p-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {/* Opção de editar - apenas se permitido */}
                {canEditData?.can_edit && onEdit && (
                  <DropdownMenuItem 
                    onClick={() => onEdit(post.id)}
                    className="flex items-center gap-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                  >
                    <Edit3 className="h-4 w-4" />
                    Editar publicação
                  </DropdownMenuItem>
                )}
                
                {/* Mostrar restrição se não pode editar */}
                {canEditData && !canEditData.can_edit && (
                  <DropdownMenuItem disabled className="flex items-center gap-2 text-gray-400">
                    <Edit3 className="h-4 w-4" />
                    <div className="flex flex-col">
                      <span>Editar publicação</span>
                      <span className="text-xs text-gray-500">{canEditData.message}</span>
                    </div>
                  </DropdownMenuItem>
                )}
                
                {/* Opção de ver histórico - apenas se post foi editado e há callback */}
                {post.is_edited && onViewHistory && (
                  <DropdownMenuItem 
                    onClick={() => onViewHistory(post.id)}
                    className="flex items-center gap-2"
                  >
                    <History className="h-4 w-4" />
                    Ver histórico de edições
                  </DropdownMenuItem>
                )}
                
                {(canEditData?.can_edit || post.is_edited) && <DropdownMenuSeparator />}
                
                {/* Opção de excluir */}
                {onDelete && (
                  <DropdownMenuItem 
                    onClick={() => onDelete(post.id)}
                    className="flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                    Excluir publicação
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {/* Conteúdo do post */}
        <div className="px-5 py-4 bg-white dark:bg-gray-900">
          <div
            className={cn(
              "prose prose-gray dark:prose-invert max-w-none break-words overflow-wrap-anywhere",
              "prose-headings:font-heading",
              "prose-h1:text-3xl prose-h1:font-bold prose-h1:text-gray-900 dark:prose-h1:text-gray-100 prose-h1:mb-5 prose-h1:tracking-tight",
              "prose-h2:text-2xl prose-h2:font-semibold prose-h2:text-gray-800 dark:prose-h2:text-gray-200 prose-h2:mb-4",
              "prose-h3:text-xl prose-h3:font-medium prose-h3:text-gray-700 dark:prose-h3:text-gray-300 prose-h3:mb-3",
              "prose-p:text-gray-600 dark:prose-p:text-gray-300 prose-p:leading-relaxed",
              "prose-ul:list-disc prose-ul:pl-6 prose-ul:my-4 prose-ol:list-decimal prose-ol:pl-6 prose-ol:my-4 prose-li:text-gray-600 dark:prose-li:text-gray-300 prose-li:mb-0 prose-li:mt-0 prose-li:leading-normal",
              // Estilos específicos para blocos de código (igualando ao PostEditor)
              "prose-pre:bg-muted prose-pre:rounded-md prose-pre:p-4 prose-pre:my-4 prose-pre:text-sm prose-pre:font-mono prose-pre:overflow-x-auto prose-pre:whitespace-pre-wrap prose-pre:break-words prose-pre:max-w-full",
              "prose-code:text-sm prose-code:font-mono prose-code:break-words prose-code:overflow-wrap-anywhere",
              // Garantir que inline code também tenha estilo adequado
              "prose-code:bg-muted prose-code:px-1.5 prose-code:py-0.5 prose-code:rounded prose-code:text-sm prose-code:font-mono"
            )}
            dangerouslySetInnerHTML={{ __html: sanitizedContent }}
          />
          
          {/* Indicador de edição */}
          {post.is_edited && post.last_edited_at && (
            <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="cursor-pointer hover:text-gray-700 dark:hover:text-gray-300">
                      • Editado
                    </span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="flex flex-col gap-1">
                      <p>Última edição: {formatDate(post.last_edited_at)}</p>
                      {post.edit_count && post.edit_count > 1 && (
                        <p>{post.edit_count} edições realizadas</p>
                      )}
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}
        </div>

        {/* Galeria de fotos (se existir) */}
        {post.images && post.images.length > 0 && (
          <div className="px-5 py-4 border-t border-gray-200/80 dark:border-gray-800/80 bg-white/50 dark:bg-gray-900/50">
            <PostPhotoGallery images={post.images} />
          </div>
        )}

        {/* Enquete (se existir) */}
        {post.has_poll === true && (
          <div className="px-5 py-4 border-t border-gray-200/80 dark:border-gray-800/80 bg-white/50 dark:bg-gray-900/50">
            <PollVoting
              postId={post.id}
              onVoteSuccess={() => {
                // Opcional: atualizar o feed após o voto
              }}
            />
          </div>
        )}

        {/* Ações do post (curtir, comentar, compartilhar) */}
        <div className="px-5 py-3 bg-gray-50 dark:bg-gray-800/30 border-t border-gray-200/80 dark:border-gray-800/80">
          <PostActions
            post={post}
            currentUserId={currentUserId}
          />
        </div>

        {/* Seção de comentários */}
        <div className="px-5 py-3 bg-gray-100/50 dark:bg-gray-800/20 border-t border-gray-200/80 dark:border-gray-800/80 rounded-b-lg">
          <CommentList postId={post.id} currentUserId={currentUserId} />
        </div>
      </CardContent>
    </Card>
  );
}

// Memoizar o componente para evitar renderizações desnecessárias
export const PostCard = memo(PostCardComponent);
