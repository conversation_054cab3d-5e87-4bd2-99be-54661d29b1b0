/**
 * Componente para mostrar limites de filtros do Feed e incentivar upgrades
 * <AUTHOR> Internet 2025
 */
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useFeedFilterLimits, useFilterDateLimit, useStatsDateLimit } from '@/hooks/feed/useFeedFilterLimits';
import { Calendar, Clock, Filter, TrendingUp, Zap, Crown, Lock, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Link } from 'react-router-dom';
import { useState } from 'react';

interface FilterLimitIndicatorProps {
  showUpgradePrompt?: boolean;
  compact?: boolean;
  className?: string;
}

/**
 * Indicador principal dos limites de filtros
 */
export const FilterLimitIndicator = ({ 
  showUpgradePrompt = true, 
  compact = false,
  className 
}: FilterLimitIndicatorProps) => {
  const { limits, isLoading } = useFeedFilterLimits();
  const filterDateLimit = useFilterDateLimit();
  const statsDateLimit = useStatsDateLimit();
  const [showDetails, setShowDetails] = useState(false);
  
  if (isLoading || !limits) {
    return (
      <div className={cn("animate-pulse", className)}>
        <div className="h-4 bg-gray-200 rounded w-32"></div>
      </div>
    );
  }
  
  if (compact) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <PlanBadge plan={limits.currentPlan} />
        <span className="text-xs text-muted-foreground">
          {filterDateLimit.getDateLimitMessage()}
        </span>
      </div>
    );
  }
  
  return (
    <Card className={cn("border-l-4", getPlanBorderColor(limits.currentPlan), className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <PlanBadge plan={limits.currentPlan} />
            <CardTitle className="text-sm">Limites de Filtros</CardTitle>
          </div>
          
          {showUpgradePrompt && !limits.isMaxPlan && (
            <Button size="xs" variant="outline" asChild>
              <Link to="/upgrade">
                <Crown className="h-3 w-3 mr-1" />
                Upgrade
              </Link>
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* Limitações atuais */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <LimitItem
            icon={<Filter className="h-3 w-3" />}
            label="Filtros"
            value={filterDateLimit.getDateLimitMessage()}
            isLimited={limits.isHistoryLimited}
            showLockIcon={false}
          />
          
          <LimitItem
            icon={<TrendingUp className="h-3 w-3" />}
            label="Estatísticas"  
            value={statsDateLimit.getDateLimitMessage()}
            isLimited={limits.isStatsLimited}
            showLockIcon={false}
          />
        </div>
        
        {/* Botão para mostrar mais detalhes */}
        {!showDetails && (
          <Button
            variant="ghost"
            size="xs"
            onClick={() => setShowDetails(true)}
            className="w-full text-xs"
          >
            Ver todos os recursos
          </Button>
        )}
        
        {/* Detalhes expandidos */}
        {showDetails && (
          <div className="space-y-2 pt-2 border-t">
            <div className="grid grid-cols-1 gap-2 text-xs">
              <LimitItem
                icon={<Calendar className="h-3 w-3" />}
                label="Filtros salvos"
                value={limits.canSaveFilters ? `${limits.maxSavedFilters}` : 'Não disponível'}
                isLimited={!limits.canSaveFilters}
                showLockIcon={false}
              />
              
              <LimitItem
                icon={<Sparkles className="h-3 w-3" />}
                label="IA e Automações"
                value={limits.hasAI ? 'Disponível' : 'Não disponível'}
                isLimited={!limits.hasAI}
                showLockIcon={true}
              />
              
              <LimitItem
                icon={<TrendingUp className="h-3 w-3" />}
                label="Analytics avançados"
                value={limits.hasAnalytics ? 'Disponível' : 'Não disponível'}
                isLimited={!limits.hasAnalytics}
                showLockIcon={true}
              />
            </div>
            
            <Button
              variant="ghost"
              size="xs"
              onClick={() => setShowDetails(false)}
              className="w-full text-xs"
            >
              Ocultar detalhes
            </Button>
          </div>
        )}
        
        {/* Prompt de upgrade para planos inferiores */}
        {showUpgradePrompt && !limits.isMaxPlan && (
          <Alert className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
            <Crown className="h-4 w-4 text-purple-600" />
            <AlertDescription className="text-xs">
              {limits.isFreePlan && (
                <>
                  <strong>Upgrade para Pro:</strong> Histórico de 30 dias + Analytics básicos + 1 filtro salvo
                </>
              )}
              {limits.isProPlan && (
                <>
                  <strong>Upgrade para Max:</strong> Histórico ilimitado + IA + Automações + Filtros ilimitados
                </>
              )}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

/**
 * Badge do plano atual
 */
const PlanBadge = ({ plan }: { plan: string }) => {
  const getVariant = () => {
    switch (plan) {
      case 'Grátis': return 'secondary';
      case 'Pro': return 'default';
      case 'Max': return 'destructive';
      default: return 'outline';
    }
  };
  
  const getIcon = () => {
    switch (plan) {
      case 'Grátis': return null;
      case 'Pro': return <Zap className="h-3 w-3 mr-1" />;
      case 'Max': return <Crown className="h-3 w-3 mr-1" />;
      default: return null;
    }
  };
  
  return (
    <Badge variant={getVariant()} className="text-xs">
      {getIcon()}
      {plan}
    </Badge>
  );
};

/**
 * Item individual de limitação
 */
const LimitItem = ({ 
  icon, 
  label, 
  value, 
  isLimited,
  showLockIcon = false
}: { 
  icon: React.ReactNode;
  label: string;
  value: string;
  isLimited: boolean;
  showLockIcon?: boolean;
}) => (
  <div className="flex items-center gap-2">
    <div className={cn(
      "p-1 rounded",
      isLimited ? "text-orange-600 bg-orange-100" : "text-green-600 bg-green-100"
    )}>
      {showLockIcon && isLimited ? <Lock className="h-3 w-3" /> : icon}
    </div>
    <div className="flex-1 min-w-0">
      <div className="font-medium truncate">{label}</div>
      <div className={cn(
        "truncate",
        isLimited ? "text-orange-600" : "text-green-600"
      )}>
        {value}
      </div>
    </div>
  </div>
);

/**
 * Utilitário para cores da borda por plano
 */
const getPlanBorderColor = (plan: string) => {
  switch (plan) {
    case 'Grátis': return 'border-l-gray-400';
    case 'Pro': return 'border-l-blue-500';
    case 'Max': return 'border-l-purple-500';
    default: return 'border-l-gray-300';
  }
};

/**
 * Componente compacto para usar em filtros
 */
export const FilterLimitBadge = ({ className }: { className?: string }) => {
  const { limits } = useFeedFilterLimits();
  const filterDateLimit = useFilterDateLimit();
  
  if (!limits) return null;
  
  return (
    <div className={cn("flex items-center gap-1 text-xs text-muted-foreground", className)}>
      <Clock className="h-3 w-3" />
      <span>{filterDateLimit.getDateLimitMessage()}</span>
      {limits.isHistoryLimited && (
        <Link to="/upgrade" className="text-purple-600 hover:text-purple-700 font-medium">
          Expandir
        </Link>
      )}
    </div>
  );
}; 