/**
 * Componente para upload de múltiplas fotos em posts
 * <AUTHOR> Internet 2025
 */
import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { ImagePlus, X, Upload } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { PostImageService } from '@/services/post-images';
import { usePhotoGalleryValidation } from '@/hooks/usePhotoGalleryLimits';
import { authManager } from '@/lib/auth/AuthManager';

interface PhotoData {
  id: string;
  url: string;
  file: File;
  uploading?: boolean;
}

interface PhotoGalleryUploaderProps {
  onPhotosChange: (photos: PhotoData[]) => void;
  photos: PhotoData[];
  tempPostId?: string;
}

export const PhotoGalleryUploader = ({
  onPhotosChange,
  photos,
  tempPostId
}: PhotoGalleryUploaderProps) => {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { validatePhotoCount, getMaxPhotosAllowed, maxLimit, subscriptionPlan } = usePhotoGalleryValidation();

  const handleFileSelect = async (files: FileList) => {
    const fileArray = Array.from(files);
    const imageFiles = fileArray.filter(file => file.type.startsWith('image/'));
    
    if (imageFiles.length === 0) {
      toast.error('Por favor, selecione apenas arquivos de imagem.');
      return;
    }

    const newTotalCount = photos.length + imageFiles.length;
    const validation = validatePhotoCount(newTotalCount);
    
    if (!validation.isValid) {
      toast.error(validation.message);
      return;
    }


    // Criar objetos PhotoData temporários
    const newPhotos: PhotoData[] = imageFiles.map(file => ({
      id: crypto.randomUUID(),
      url: URL.createObjectURL(file),
      file,
      uploading: true
    }));

    // Adicionar fotos ao estado imediatamente para preview
    const updatedPhotos = [...photos, ...newPhotos];
    onPhotosChange(updatedPhotos);

    // Upload das imagens uma por uma
    for (const photo of newPhotos) {
      try {
        const { url, path } = await PostImageService.uploadImage(photo.file);
        
        // Se temos um tempPostId, associar a imagem ao post temporário
        if (tempPostId) {
          try {
            const companyId = authManager.getCompanyId();
            
            if (companyId) {
              await PostImageService.associateImageWithPost(
                tempPostId,
                companyId, 
                url,
                path,
                photo.file.size
              );
              console.log(`✅ Imagem da galeria associada ao tempPostId: ${tempPostId}`);
            } else {
              console.warn('⚠️ Company ID não disponível no AuthManager');
            }
          } catch (associationError) {
            console.error('❌ Erro ao associar imagem da galeria ao tempPostId:', associationError);
          }
        }
        
        // Atualizar a foto com a URL real e remover status de upload
        const photoIndex = updatedPhotos.findIndex(p => p.id === photo.id);
        if (photoIndex !== -1) {
          updatedPhotos[photoIndex] = {
            ...photo,
            url,
            uploading: false
          };
          onPhotosChange([...updatedPhotos]);
        }
        
        // Limpar URL temporária
        URL.revokeObjectURL(photo.url);
        
      } catch (error) {
        console.error('Erro ao fazer upload da imagem:', error);
        
        // Remover foto com erro
        const photoIndex = updatedPhotos.findIndex(p => p.id === photo.id);
        if (photoIndex !== -1) {
          URL.revokeObjectURL(photo.url);
          updatedPhotos.splice(photoIndex, 1);
          onPhotosChange([...updatedPhotos]);
        }
        
        toast.error(`Erro ao enviar ${photo.file.name}`);
      }
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (e.dataTransfer.files) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    // Só remove isDragging se sair completamente do container
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;
    
    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setIsDragging(false);
    }
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const removePhoto = (photoId: string) => {
    const photo = photos.find(p => p.id === photoId);
    if (photo && photo.url.startsWith('blob:')) {
      URL.revokeObjectURL(photo.url);
    }
    
    const updatedPhotos = photos.filter(p => p.id !== photoId);
    onPhotosChange(updatedPhotos);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const getMaxAllowed = getMaxPhotosAllowed();
  const remainingSlots = getMaxAllowed === -1 ? Infinity : Math.max(0, getMaxAllowed - photos.length);

  if (photos.length > 0) {
    return (
      <div className="border border-green-200 bg-green-50/30 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <ImagePlus className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-700">
              Galeria de Fotos ({photos.length}{getMaxAllowed !== -1 ? `/${getMaxAllowed}` : ''})
            </span>
          </div>
          
          {remainingSlots > 0 && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={openFileDialog}
              className="text-green-600 hover:bg-green-100"
            >
              <ImagePlus className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Grid de fotos */}
        <div className="grid grid-cols-3 gap-2 mb-3">
          {photos.map((photo) => (
            <div key={photo.id} className="relative group aspect-square">
              <img
                src={photo.url}
                alt="Preview"
                className={cn(
                  "w-full h-full object-cover rounded-lg border",
                  photo.uploading && "opacity-50"
                )}
              />
              
              {photo.uploading && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
              )}
              
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removePhoto(photo.id)}
                className="absolute top-1 right-1 h-6 w-6 p-0 bg-red-500 hover:bg-red-600 text-white opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>

        {/* Info sobre limites */}
        <p className="text-xs text-green-600">
          {getMaxAllowed === -1 
            ? `Plano ${subscriptionPlan}: Fotos ilimitadas`
            : `Plano ${subscriptionPlan}: ${remainingSlots} foto(s) restante(s)`
          }
        </p>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple
          onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
          className="hidden"
        />
      </div>
    );
  }

  return (
    <div
      className={cn(
        "border border-dashed rounded-lg p-6 transition-all duration-200 cursor-pointer relative",
        isDragging 
          ? "border-green-400 bg-green-50 border-2 scale-[1.02]" 
          : "border-gray-300 hover:border-green-300 hover:bg-green-50/30"
      )}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDragEnter={handleDragEnter}
      onClick={openFileDialog}
    >
      <div className="flex flex-col items-center gap-3 text-center">
        {isDragging ? (
          <Upload className="h-8 w-8 text-green-500 animate-bounce" />
        ) : (
          <ImagePlus className="h-8 w-8 text-gray-400" />
        )}
        
        <div>
          <p className={cn(
            "text-sm font-medium mb-1",
            isDragging ? "text-green-700" : "text-gray-700"
          )}>
            {isDragging ? "Solte as imagens aqui!" : "Adicionar Galeria de Fotos"}
          </p>
          <p className={cn(
            "text-xs",
            isDragging ? "text-green-600" : "text-gray-500"
          )}>
            {isDragging 
              ? "Solte para adicionar as imagens à galeria"
              : "Arraste e solte ou clique para selecionar múltiplas imagens"
            }
          </p>
        </div>

        {!isDragging && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              openFileDialog();
            }}
            className="flex items-center gap-2"
          >
            <Upload className="h-4 w-4" />
            Selecionar Fotos
          </Button>
        )}

        <p className="text-xs text-gray-500">
          {getMaxAllowed === -1 
            ? `Plano ${subscriptionPlan}: Fotos ilimitadas`
            : `Plano ${subscriptionPlan}: Até ${getMaxAllowed} fotos por post`
          }
        </p>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
        className="hidden"
      />
    </div>
  );
};