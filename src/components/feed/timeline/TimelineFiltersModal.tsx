/**
 * TimelineFiltersModal - Modal wrapper para TimelineFilters existente
 * <AUTHOR> Internet 2025
 */

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { TimelineFilters } from './TimelineFilters';
import type { TimelineItemType, TimelinePriority } from '@/types/timeline';
import { Filter } from 'lucide-react';

interface TimelineFiltersModalProps {
  isOpen: boolean;
  onClose: () => void;
  filters: {
    types: TimelineItemType[];
    priorities: TimelinePriority[];
    search: string;
    showRead?: boolean;
  };
  onFilterChange: (filters: {
    types: TimelineItemType[];
    priorities: TimelinePriority[];
    search: string;
    showRead?: boolean;
  }) => void;
  limits?: {
    hasAdvancedFilters: boolean;
  };
}

export function TimelineFiltersModal({
  isOpen,
  onClose,
  filters,
  onFilterChange,
  limits
}: TimelineFiltersModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-blue-500" />
            Filtros da Timeline
          </DialogTitle>
          <DialogDescription>
            Configure os filtros para personalizar sua timeline
          </DialogDescription>
        </DialogHeader>

        <div className="overflow-y-auto flex-1">
          <TimelineFilters
            filters={filters}
            onFilterChange={onFilterChange}
            limits={limits}
            className="border-0" // Remove border do card interno pois já está no modal
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}