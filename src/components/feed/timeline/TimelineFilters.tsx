/**
 * TimelineFilters - Componente de filtros da Timeline
 * <AUTHOR> Internet 2025
 */

import { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import type { TimelineItemType, TimelinePriority } from '@/types/timeline';
import { cn } from '@/lib/utils';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useTimelineUnreadCount, useMarkAllTimelineItemsAsRead } from '@/lib/query/hooks/useTimelineNotifications';
import { 
  Search, 
  X, 
  MessageSquare,
  Bell,
  Cake,
  TrendingUp,
  FileText,
  Calendar,
  Target,
  CheckSquare,
  Bot,
  AlertTriangle,
  Clock,
  CheckCircle,
  Zap,
  Award,
  Rocket,
  Eye,
  EyeOff,
  CheckCircle2
} from 'lucide-react';

// Configuração dos tipos de atividade
const ACTIVITY_TYPES: {
  type: TimelineItemType;
  label: string;
  icon: any;
  color: string;
}[] = [
  { type: 'post', label: 'Posts', icon: MessageSquare, color: 'blue' },
  { type: 'notification', label: 'Notificações', icon: Bell, color: 'purple' },
  { type: 'birthday', label: 'Aniversários', icon: Cake, color: 'pink' },
  { type: 'promotion', label: 'Promoções', icon: TrendingUp, color: 'green' },
  { type: 'obligation', label: 'Obrigações', icon: FileText, color: 'orange' },
  { type: 'event', label: 'Eventos', icon: Calendar, color: 'indigo' },
  { type: 'mission', label: 'Missões', icon: Target, color: 'red' },
  { type: 'task', label: 'Tarefas', icon: CheckSquare, color: 'gray' },
  { type: 'ai_insight', label: 'AI Insights', icon: Bot, color: 'cyan' },
  { type: 'medal_earned', label: 'Medalhas', icon: Award, color: 'yellow' },
  { type: 'level_up', label: 'Level Up', icon: Rocket, color: 'indigo' },
];

// Configuração das prioridades
const PRIORITIES: {
  priority: TimelinePriority;
  label: string;
  icon: any;
  color: string;
}[] = [
  { priority: 'urgent', label: 'Urgente', icon: AlertTriangle, color: 'red' },
  { priority: 'high', label: 'Alta', icon: Zap, color: 'orange' },
  { priority: 'medium', label: 'Média', icon: Clock, color: 'yellow' },
  { priority: 'low', label: 'Baixa', icon: CheckCircle, color: 'green' },
];

interface TimelineFiltersProps {
  filters: {
    types: TimelineItemType[];
    priorities: TimelinePriority[];
    search: string;
    showRead?: boolean;
  };
  onFilterChange: (filters: {
    types: TimelineItemType[];
    priorities: TimelinePriority[];
    search: string;
    showRead?: boolean;
  }) => void;
  limits?: {
    hasAdvancedFilters: boolean;
  };
  className?: string;
}

// Função para obter cor do badge
function getBadgeColor(color: string) {
  const colors: Record<string, string> = {
    blue: 'bg-blue-100 text-blue-700 border-blue-200 hover:bg-blue-200',
    purple: 'bg-purple-100 text-purple-700 border-purple-200 hover:bg-purple-200',
    pink: 'bg-pink-100 text-pink-700 border-pink-200 hover:bg-pink-200',
    green: 'bg-green-100 text-green-700 border-green-200 hover:bg-green-200',
    orange: 'bg-orange-100 text-orange-700 border-orange-200 hover:bg-orange-200',
    indigo: 'bg-indigo-100 text-indigo-700 border-indigo-200 hover:bg-indigo-200',
    red: 'bg-red-100 text-red-700 border-red-200 hover:bg-red-200',
    gray: 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200',
    cyan: 'bg-cyan-100 text-cyan-700 border-cyan-200 hover:bg-cyan-200',
    yellow: 'bg-yellow-100 text-yellow-700 border-yellow-200 hover:bg-yellow-200',
  };
  return colors[color] || colors.gray;
}

export function TimelineFilters({ 
  filters, 
  onFilterChange, 
  limits,
  className 
}: TimelineFiltersProps) {
  const [searchValue, setSearchValue] = useState(filters.search);

  // Handler para mudança de busca
  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value);
    onFilterChange({
      ...filters,
      search: value,
    });
  }, [filters, onFilterChange]);

  // Handler para toggle de tipo
  const handleTypeToggle = useCallback((type: TimelineItemType) => {
    const newTypes = filters.types.includes(type)
      ? filters.types.filter(t => t !== type)
      : [...filters.types, type];
    
    onFilterChange({
      ...filters,
      types: newTypes,
    });
  }, [filters, onFilterChange]);

  // Handler para toggle de prioridade
  const handlePriorityToggle = useCallback((priority: TimelinePriority) => {
    const newPriorities = filters.priorities.includes(priority)
      ? filters.priorities.filter(p => p !== priority)
      : [...filters.priorities, priority];
    
    onFilterChange({
      ...filters,
      priorities: newPriorities,
    });
  }, [filters, onFilterChange]);

  // Handler para limpar filtros
  const handleClearFilters = useCallback(() => {
    setSearchValue('');
    onFilterChange({
      types: [],
      priorities: [],
      search: '',
      showRead: undefined,
    });
  }, [onFilterChange]);

  // Handler para toggle do filtro de leitura
  const handleReadFilterToggle = useCallback(() => {
    const newShowRead = filters.showRead === false ? undefined : false;
    onFilterChange({
      ...filters,
      showRead: newShowRead,
    });
  }, [filters, onFilterChange]);

  // Verificar se há filtros ativos
  const hasActiveFilters = filters.types.length > 0 || 
                          filters.priorities.length > 0 || 
                          filters.search.length > 0 ||
                          filters.showRead !== undefined;

  // Hooks para notificações não lidas
  const { data: unreadCount = 0 } = useTimelineUnreadCount();
  const markAllAsReadMutation = useMarkAllTimelineItemsAsRead();

  return (
    <motion.div
      className={className}
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="border-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 backdrop-blur-sm">
        <div className="p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Search className="h-5 w-5 text-blue-500" />
              <h3 className="text-lg font-semibold">Filtros da Timeline</h3>
            </div>
            
            {hasActiveFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearFilters}
                className="text-xs"
              >
                <X className="h-3 w-3 mr-1" />
                Limpar Filtros
              </Button>
            )}
          </div>

          {/* Busca */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Buscar</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar por título, descrição ou conteúdo..."
                value={searchValue}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <Separator />

          {/* Tipos de Atividade */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Tipos de Atividade</label>
              {!limits?.hasAdvancedFilters && (
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
                  Básico
                </Badge>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
              {ACTIVITY_TYPES.map(({ type, label, icon: Icon, color }) => {
                const isSelected = filters.types.includes(type);
                
                // Para plano gratuito, limitar a tipos básicos
                const isBasicType = ['post', 'notification', 'birthday', 'event'].includes(type);
                const isAvailable = limits?.hasAdvancedFilters || isBasicType;
                
                return (
                  <motion.div
                    key={type}
                    whileHover={isAvailable ? { scale: 1.05 } : {}}
                    whileTap={isAvailable ? { scale: 0.95 } : {}}
                  >
                    <Badge
                      className={cn(
                        "transition-all duration-200 border",
                        isAvailable 
                          ? "cursor-pointer" 
                          : "cursor-not-allowed opacity-50",
                        isSelected && isAvailable
                          ? getBadgeColor(color)
                          : isAvailable
                          ? "bg-white hover:bg-gray-50 text-gray-600 border-gray-200"
                          : "bg-gray-100 text-gray-400 border-gray-200"
                      )}
                      onClick={isAvailable ? () => handleTypeToggle(type) : undefined}
                    >
                      <Icon className="h-3 w-3 mr-1" />
                      {label}
                      {!isAvailable && <span className="ml-1 text-xs">🔒</span>}
                    </Badge>
                  </motion.div>
                );
              })}
            </div>
            
            {!limits?.hasAdvancedFilters && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="text-xs text-blue-700">
                  <span className="font-medium">Plano Gratuito:</span> Filtros básicos disponíveis (Posts, Notificações, Aniversários, Eventos).
                  <br />
                  <span className="font-medium">Upgrade para Pro:</span> Desbloqueie filtros avançados (Promoções, Obrigações, Missões, Tarefas, IA, Medalhas).
                </div>
              </div>
            )}
            
            {filters.types.length > 0 && (
              <div className="text-xs text-muted-foreground">
                {filters.types.length} tipo{filters.types.length !== 1 ? 's' : ''} selecionado{filters.types.length !== 1 ? 's' : ''}
              </div>
            )}
          </div>

          <Separator />

          {/* Filtro de Leitura */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Status de Leitura</label>
            <div className="flex flex-wrap gap-2">
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant={filters.showRead === false ? "default" : "outline"}
                  size="sm"
                  onClick={handleReadFilterToggle}
                  className="flex items-center gap-2"
                >
                  <EyeOff className="h-4 w-4" />
                  Apenas não lidas
                  {unreadCount > 0 && (
                    <Badge variant="secondary" className="ml-1 px-1 py-0 text-xs">
                      {unreadCount}
                    </Badge>
                  )}
                </Button>
              </motion.div>
            </div>
            
            {filters.showRead === false && (
              <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 p-2 rounded">
                Mostrando apenas notificações não lidas ({unreadCount})
              </div>
            )}
          </div>

          <Separator />

          {/* Prioridades */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Prioridades</label>
              {!limits?.hasAdvancedFilters && (
                <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 text-xs">
                  🔒 Limitado
                </Badge>
              )}
            </div>
            
            {limits?.hasAdvancedFilters ? (
              <div className="flex flex-wrap gap-2">
                {PRIORITIES.map(({ priority, label, icon: Icon, color }) => {
                  const isSelected = filters.priorities.includes(priority);
                  return (
                    <motion.div
                      key={priority}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Badge
                        className={cn(
                          "cursor-pointer transition-all duration-200 border",
                          isSelected
                            ? getBadgeColor(color)
                            : "bg-white hover:bg-gray-50 text-gray-600 border-gray-200"
                        )}
                        onClick={() => handlePriorityToggle(priority)}
                      >
                        <Icon className="h-3 w-3 mr-1" />
                        {label}
                      </Badge>
                    </motion.div>
                  );
                })}
              </div>
            ) : (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="text-sm text-orange-800 mb-2 font-medium">
                  🚀 Filtros de Prioridade - Recurso Premium
                </div>
                <div className="text-xs text-orange-700 mb-3">
                  Filtre por urgência, alta, média ou baixa prioridade para organizar melhor sua timeline.
                </div>
                <div className="flex gap-2">
                  <button 
                    onClick={() => window.location.href = '/upgrade?source=timeline-filters'}
                    className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded-md text-xs font-medium transition-colors"
                  >
                    Upgrade para Pro
                  </button>
                </div>
              </div>
            )}
            
            {limits?.hasAdvancedFilters && filters.priorities.length > 0 && (
              <div className="text-xs text-muted-foreground">
                {filters.priorities.length} prioridade{filters.priorities.length !== 1 ? 's' : ''} selecionada{filters.priorities.length !== 1 ? 's' : ''}
              </div>
            )}
          </div>

          {/* Legenda de Prioridades */}
          <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              🎨 Legenda de Prioridades
            </div>
            <div className="grid grid-cols-2 gap-2">
              {PRIORITIES.map(({ priority, label, color }) => (
                <div key={priority} className="flex items-center gap-2 text-xs">
                  <div className={cn(
                    "w-3 h-3 rounded-full border-2",
                    color === 'red' && "bg-red-500 border-red-600",
                    color === 'orange' && "bg-orange-500 border-orange-600", 
                    color === 'yellow' && "bg-yellow-500 border-yellow-600",
                    color === 'green' && "bg-green-500 border-green-600"
                  )} />
                  <span className="text-gray-600 dark:text-gray-400">
                    {label}
                    {priority === 'urgent' && ' (⚡ badge)'}
                  </span>
                </div>
              ))}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              📝 Faixa colorida na lateral + badge apenas para urgente
            </div>
          </div>

          {/* Resumo dos filtros ativos */}
          {hasActiveFilters && (
            <>
              <Separator />
              <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                <div className="text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  Filtros Ativos
                </div>
                <div className="space-y-1 text-xs text-blue-600 dark:text-blue-400">
                  {filters.search && (
                    <div>Busca: "{filters.search}"</div>
                  )}
                  {filters.types.length > 0 && (
                    <div>
                      Tipos: {filters.types.map(type => 
                        ACTIVITY_TYPES.find(t => t.type === type)?.label
                      ).join(', ')}
                    </div>
                  )}
                  {filters.priorities.length > 0 && (
                    <div>
                      Prioridades: {filters.priorities.map(priority => 
                        PRIORITIES.find(p => p.priority === priority)?.label
                      ).join(', ')}
                    </div>
                  )}
                  {filters.showRead === false && (
                    <div>Mostrando apenas não lidas</div>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </Card>
    </motion.div>
  );
}