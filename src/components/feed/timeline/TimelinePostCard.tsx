/**
 * Componente para renderizar posts completos na timeline
 * Reutiliza completamente o EnhancedPostCard do feed
 * <AUTHOR> Internet 2025
 */
import { useMemo, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { motion } from 'framer-motion';
import { EnhancedPostCard } from '@/components/enhanced/feed/EnhancedPostCard';
import { useSafePostDetails } from '@/hooks/usePostExistsQuery';
import { useCurrentUser } from '@/lib/query/hooks/useUsers';
import { useLatestPostEdit } from '@/lib/query/hooks/usePostEditHistory';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Loader2 } from 'lucide-react';
import { useManualTracking } from '@/hooks/tracking/useManualTracking';
import * as Diff from 'diff';

interface TimelinePostCardProps {
  notification: {
    id: string;
    type?: string;
    reference_id?: string;
    metadata?: {
      post_id?: string;
      post_content?: string;
      action_type?: string;
      [key: string]: any;
    };
  };
}

export interface TimelinePostCardRef {
  toggleDiff: () => void;
  isDiffVisible: boolean;
}

// Componente customizado de post que pode mostrar diff
function PostWithDiff({ 
  post, 
  currentUserId, 
  formatDate, 
  showDiff, 
  originalContent 
}: {
  post: any;
  currentUserId: string;
  formatDate: (date: string) => string;
  showDiff: boolean;
  originalContent?: string;
}) {
  // Se não está mostrando diff ou não tem conteúdo original, mostra post normal
  if (!showDiff || !originalContent) {
    return (
      <EnhancedPostCard
        post={post}
        currentUserId={currentUserId}
        formatDate={formatDate}
      />
    );
  }

  // Criar diff usando a biblioteca
  const diffResult = Diff.diffWords(
    originalContent.replace(/<[^>]*>/g, ''), // Remove HTML
    post.content.replace(/<[^>]*>/g, '')      // Remove HTML
  );

  // Criar HTML string com o diff
  const diffHtml = diffResult.map((part, index) => {
    if (!part.added && !part.removed) {
      return part.value;
    } else if (part.removed) {
      return `<span class="bg-red-100 text-red-800 line-through decoration-red-500 decoration-2 px-1 rounded mr-1" title="Texto removido">${part.value}</span>`;
    } else if (part.added) {
      return `<span class="bg-yellow-100 text-yellow-800 underline decoration-yellow-600 decoration-2 px-1 rounded mr-1" title="Texto adicionado">${part.value}</span>`;
    }
    return '';
  }).join('');

  // Criar uma versão do post com o conteúdo diff como HTML
  const postWithDiff = {
    ...post,
    content: `<div class="prose prose-sm max-w-none">${diffHtml}</div>`
  };

  return (
    <EnhancedPostCard
      post={postWithDiff}
      currentUserId={currentUserId}
      formatDate={formatDate}
    />
  );
}

export const TimelinePostCard = forwardRef<TimelinePostCardRef, TimelinePostCardProps>(
  ({ notification }, ref) => {
    // Estado para controlar o diff
    const [showDiff, setShowDiff] = useState(false);
    
    // Extrair post ID da notificação
    const postId = notification.metadata?.post_id || notification.reference_id;
    
    // Buscar dados completos do post usando hook seguro que verifica existência
    const { data: post, isLoading, error } = useSafePostDetails(postId);
    
    // Debug: verificar se as imagens estão chegando no TimelinePostCard
    useEffect(() => {
      if (post) {
        console.log("🔍 TimelinePostCard - Post recebido:", {
          postId: post.id,
          hasImages: !!post.images,
          imagesCount: post.images?.length || 0,
          images: post.images
        });
      }
    }, [post]);
    
    // Buscar usuário atual para passar ao PostCard
    const { data: currentUser } = useCurrentUser();
    
    // Buscar última edição se for um post editado
    const isEditedPost = notification.type === 'post_edited';
    const { data: latestEdit } = useLatestPostEdit(isEditedPost ? postId : null);
    
    // O conteúdo original vem do histórico de edição (antes da mudança)
    // Enquanto o post.content é o conteúdo atual (depois da mudança)
    const originalContent = latestEdit?.content; // Conteúdo anterior
    const currentContent = post?.content;        // Conteúdo atual
    
    // Hook para tracking manual
    const { trackTimelineAction } = useManualTracking();

    // Expor métodos via ref
    useImperativeHandle(ref, () => ({
      toggleDiff: () => setShowDiff(prev => !prev),
      isDiffVisible: showDiff,
    }), [showDiff]);
  
  // Função de formatação de data
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "d 'de' MMMM 'às' HH:mm", { locale: ptBR });
  };

  // Registrar visualização quando post é carregado na timeline
  useEffect(() => {
    if (post && postId && notification.id) {
      trackTimelineAction('view_post', postId, notification.id, {
        author_id: post.author?.id,
        author_name: post.author?.full_name,
        post_date: post.created_at,
        notification_type: notification.type || 'post_notification'
      });
    }
  }, [post, postId, notification.id, trackTimelineAction]);

  // Estados de loading e erro
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin text-orange-500" />
        <span className="ml-2 text-sm text-gray-600">Carregando post...</span>
      </div>
    );
  }

  if (error || !post || !currentUser) {
    // Verificar se é erro 406 (post deletado) ou erro específico de post não encontrado
    const isPostDeleted = error && (
      error.message?.includes('406') || 
      error.code === 'PGRST116' ||
      error.message?.includes('Not Acceptable')
    );

    if (isPostDeleted) {
      return (
        <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
          <p className="text-sm text-gray-600">
            📝 Este post foi removido ou não está mais disponível
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Notificação de {formatDate(notification.created_at)}
          </p>
        </div>
      );
    }

    return (
      <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
        <p className="text-sm text-gray-600">
          ⚠️ Não foi possível carregar o post
        </p>
        {postId && (
          <p className="text-xs text-gray-500 mt-1">Post ID: {postId}</p>
        )}
      </div>
    );
  }

    // Renderizar o post com ou sem diff
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <PostWithDiff
          post={post}
          currentUserId={currentUser.id}
          formatDate={formatDate}
          showDiff={showDiff}
          originalContent={originalContent}
        />
      </motion.div>
    );
  }
);