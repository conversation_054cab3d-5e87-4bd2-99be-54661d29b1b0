/**
 * TimelineView - Container principal da Timeline Unificada
 * <AUTHOR> Internet 2025
 */

import { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { TimelineList } from './TimelineList';
import { ContextPanel } from './ContextPanel';
import { TimelineFiltersModal } from './TimelineFiltersModal';
import { QuickFilters } from './QuickFilters';
import type { TimelineItem, TimelineItemType, TimelinePriority, TimelineNotification } from '@/types/timeline';
import { useTimelineNotifications, useMarkTimelineItemAsRead, useMarkAllTimelineItemsAsRead, useTimelineUnreadCount, type TimelineFilters as TimelineFiltersType } from '@/lib/query/hooks/useTimelineNotifications';
import { useQueryClient } from '@tanstack/react-query';
import { QueryKeys } from '@/lib/query/queryKeys';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';
import { useTimelineLimits } from '@/hooks/timeline/useTimelineLimits';
import { useQuickFilters } from '@/hooks/timeline/useQuickFilters';
import { cn } from '@/lib/utils';
import { usePlatform } from '@/hooks/usePlatform';
import { Card } from '@/components/ui/card';
import { Filter, Grid3X3, Loader2, AlertCircle, Lock, Crown, Eye, EyeOff, RefreshCw, CheckCircle2 } from 'lucide-react';
import { playSound, SoundEffects } from '@/lib/sound-effects';
import { Button } from '@/components/ui/button';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { createPortal } from "react-dom";
import { FeatureNotAvailable } from '@/components/common/FeatureNotAvailable';
import { useContextualHotkeys } from '@/lib/hooks/useContextualHotkeys';

// Variantes de animação otimizadas
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  },
};

const panelVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
};

interface TimelineViewProps {
  className?: string;
}

// Helper para renderizar tooltip com portal
const TooltipWithPortal = ({ children, content }: { children: React.ReactNode; content: React.ReactNode }) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>
        {children}
      </TooltipTrigger>
      {createPortal(
        <TooltipContent side="bottom" className="!z-[**********] !fixed">
          {content}
        </TooltipContent>,
        document.body
      )}
    </Tooltip>
  </TooltipProvider>
);

export function TimelineView({ className }: TimelineViewProps) {
  // ========= TODOS OS HOOKS DEVEM VIR PRIMEIRO - REGRA OBRIGATÓRIA =========
  
  // QueryClient para verificar cache
  const queryClient = useQueryClient();
  
  // Hook para verificar limites do feature flag com otimização de cache
  const { limits, isLoading: isLoadingLimits, isEnabled } = useTimelineLimits();
  
  // Verificar se as dependências já estão em cache para otimizar loading
  const featureAvailabilityKey = QueryKeys.features.availability('feed_timeline_feature');
  const subscriptionKey = QueryKeys.subscription.current();
  const cachedFeature = queryClient.getQueryData(featureAvailabilityKey);
  const cachedSubscription = queryClient.getQueryData(subscriptionKey);
  
  // Se as dependências estão em cache, o loading deve ser muito mais rápido
  const hasCachedDependencies = cachedFeature && cachedSubscription;
  const optimizedIsLoading = hasCachedDependencies ? false : isLoadingLimits;
  
  // Debug removido para produção
  
  // Hooks para responsividade
  const { isNative } = usePlatform();
  const isMobile = window?.innerWidth <= 768;

  // Estado dos filtros
  const [showFilters, setShowFilters] = useState(false);
  const [activeFilters, setActiveFilters] = useState<{
    types: TimelineItemType[];
    priorities: TimelinePriority[];
    search: string;
    showRead?: boolean;
  }>({
    types: [],
    priorities: [],
    search: '',
    showRead: undefined,
  });

  // Estado da seleção
  const [selectedItem, setSelectedItem] = useState<TimelineNotification | null>(null);
  const [contextPanelOpen, setContextPanelOpen] = useState(true); // Sempre aberto para demonstrar
  const [showInbox, setShowInbox] = useState(true); // Controle da visibilidade da inbox
  
  // Estado para controlar se já verificamos o cache (evitar loading flash)
  const [hasCheckedCache, setHasCheckedCache] = useState(false);
  
  // Detectar se estamos em uma sessão recuperada para reset do estado
  const [isRecoveredMount, setIsRecoveredMount] = useState(false);

  // Hook para filtros rápidos
  const { trackFilterUsage } = useQuickFilters();

  // Hook para buscar notificações da timeline
  const timelineFilters: TimelineFiltersType = {
    types: activeFilters.types.length > 0 ? activeFilters.types : undefined,
    priorities: activeFilters.priorities.length > 0 ? activeFilters.priorities : undefined,
    search: activeFilters.search || undefined,
    read: activeFilters.showRead,
  };
  

  
  // Verificar se há dados em cache para evitar loading desnecessário
  const timelineQueryKey = QueryKeys.timeline.notifications(timelineFilters);
  const cachedTimelineData = queryClient.getQueryData(timelineQueryKey);
  
  // Também verificar cache sem filtros (caso tenha sido pré-carregado assim no Loading.tsx)
  const baseTimelineQueryKey = QueryKeys.timeline.notifications();
  const baseCachedTimelineData = queryClient.getQueryData(baseTimelineQueryKey);
  
  // Usar qualquer cache disponível
  const availableCacheData = cachedTimelineData || baseCachedTimelineData;
  
  // Debug de cache removido para produção
  
  // Verificar cache na primeira renderização e detectar recovery
  useEffect(() => {
    if (!hasCheckedCache) {
      setHasCheckedCache(true);
      
      // Verificar se viemos de um recovery baseado na URL ou sessionStorage
      const urlParams = new URLSearchParams(window.location.search);
      const hasRecoveredParam = urlParams.has('recovered');
      const hasRecoveryMarker = sessionStorage.getItem('app_error_recovery');
      
      if (hasRecoveredParam || hasRecoveryMarker) {
        setIsRecoveredMount(true);
        
        // Limpar marcadores de recovery
        if (hasRecoveryMarker) {
          sessionStorage.removeItem('app_error_recovery');
        }
        
        // Reset completo do estado da timeline em sessões recuperadas
        setSelectedItem(null);
        setContextPanelOpen(true);
        setShowInbox(true);
        setActiveFilters({
          types: [],
          priorities: [],
          search: '',
        });
        
      }
    }
  }, [hasCheckedCache]);
  
  const { data: timelineNotifications = [], isLoading: isLoadingQuery, error } = useTimelineNotifications(timelineFilters, {
    // Se há dados em cache, começar com eles e não mostrar loading
    initialData: availableCacheData || [],
    placeholderData: availableCacheData || [], // Manter dados antigos durante revalidações
    staleTime: availableCacheData ? 15 * 60 * 1000 : 30 * 1000, // Cache mais longo se veio do cache
    enabled: hasCheckedCache, // Só habilitar query após verificar cache
    notifyOnChangeProps: ['data', 'error'], // Apenas notificar mudanças relevantes
    refetchOnWindowFocus: false, // Evitar refetch desnecessário
  });
  const markAsReadMutation = useMarkTimelineItemAsRead();
  const markAllAsReadMutation = useMarkAllTimelineItemsAsRead();
  const { data: unreadCount = 0, refetch: refetchUnreadCount } = useTimelineUnreadCount();
  
  // ✨ NOVO: Hook para atualizações em tempo real
  // ✅ CORREÇÃO: Desabilitar subscription duplicada - UnifiedRealtimeProvider já gerencia todas as notificações
  // const { forceRefresh } = useTimelineNotificationsRealtime();

  // Handler para refresh com som
  const handleRefresh = useCallback(() => {
    // Reproduzir feedback sonoro
    playSound(SoundEffects.REFRESH);
    
    // ✅ CORREÇÃO: Invalidar cache manualmente em vez de usar forceRefresh
    queryClient.invalidateQueries({ queryKey: ['timeline', 'notifications'] });
  }, [queryClient]);

  // Handler para marcar todas como lidas
  const handleMarkAllAsRead = useCallback(() => {
    markAllAsReadMutation.mutate();
  }, [markAllAsReadMutation]);

  // ✨ Sistema de controle de qualidade para detectar timeline vazia incorretamente
  useEffect(() => {
    // Se usuário tem notificações (indicado por ter mais de 20 na imagem) mas timeline está vazia
    // e não está carregando, pode ser um bug de cache
    if (hasCheckedCache && !isLoadingQuery && !error && timelineNotifications.length === 0) {
      // Verificar se há pelo menos um contexto que indica que deveria ter dados
      try {
        const notificationElement = document.querySelector('[data-notification-count]');
        const hasUrlNotification = window.location.search.includes('notification');
        const hadNotificationsBefore = sessionStorage.getItem('had_notifications');
        
        const hasNotificationContext = notificationElement || hasUrlNotification || hadNotificationsBefore;
        
        if (hasNotificationContext) {
          logQueryEvent('TimelineView', 'Timeline vazia detectada mas contexto indica que deveria ter dados', {
            hasCheckedCache,
            isLoadingQuery,
            notificationsLength: timelineNotifications.length,
            hasNotificationContext: !!hasNotificationContext
          }, 'warn');
          
          // Forçar refresh após 2 segundos se ainda estiver vazia
          const qualityCheckTimeout = setTimeout(() => {
            // Verificar se o componente ainda está montado antes de fazer refresh
            try {
              if (timelineNotifications.length === 0) {
                logQueryEvent('TimelineView', 'Forçando refresh da timeline por suspeita de cache inconsistente');
                // ✅ CORREÇÃO: Invalidar cache manualmente em vez de usar forceRefresh
                queryClient.invalidateQueries({ queryKey: ['timeline', 'notifications'] });
              }
            } catch (error) {
              // Componente foi desmontado, ignorar
              logQueryEvent('TimelineView', 'Componente desmontado durante quality check', {}, 'debug');
            }
          }, 2000);
          
          return () => {
            clearTimeout(qualityCheckTimeout);
          };
        }
      } catch (error) {
        logQueryEvent('TimelineView', 'Erro durante quality check', { error: error.message }, 'error');
      }
    }
    
    // Salvar estado quando há notificações para controle de qualidade futuro
    try {
      if (timelineNotifications.length > 0) {
        sessionStorage.setItem('had_notifications', 'true');
      }
    } catch (error) {
      // Ignorar erro de sessionStorage
      logQueryEvent('TimelineView', 'Erro ao salvar estado de notificações', { error: error.message }, 'debug');
    }
  }, [hasCheckedCache, isLoadingQuery, error, timelineNotifications.length, queryClient]);

  // Lógica inteligente de loading: NUNCA mostrar se há qualquer dado disponível
  const isLoading = useMemo(() => {
    // REGRA 1: Se ainda não verificamos o cache, não mostrar loading
    if (!hasCheckedCache) {
      return false;
    }
    
    // REGRA 2: Se há QUALQUER dado disponível (cache ou query), NUNCA mostrar loading
    const hasAnyData = (availableCacheData && availableCacheData.length > 0) || timelineNotifications.length > 0;
    if (hasAnyData) {
      return false;
    }
    
    // REGRA 3: Só mostrar loading se realmente está carregando E não há dados
    const shouldShowLoading = isLoadingQuery && hasCheckedCache;
    
    // Debug de decisão de loading removido para produção
    
    return shouldShowLoading;
  }, [hasCheckedCache, availableCacheData, timelineNotifications.length, isLoadingQuery]);

  // Aplicar limitações de plano aos dados
  const filteredItems = useMemo(() => {
    let items = timelineNotifications;
    
    // Aplicar limite de itens se configurado
    if (limits && limits.isItemsLimited && limits.maxItems > 0) {
      items = items.slice(0, limits.maxItems);
    }
    
    return items;
  }, [timelineNotifications, limits]);

  // Selecionar automaticamente o primeiro item para demonstração
  useEffect(() => {
    if (filteredItems.length > 0 && !selectedItem) {
      setSelectedItem(filteredItems[0]);
    }
  }, [filteredItems, selectedItem]);

  // Handler para marcar item como lido quando clicado
  const handleMarkAsRead = useCallback(async (notification: TimelineNotification) => {
    if (!notification.read) {
      try {
        await markAsReadMutation.mutateAsync(notification.id);
      } catch (error) {
        // Erro ao marcar como lida
      }
    }
  }, [markAsReadMutation]);

  // Handlers
  const handleItemSelect = useCallback((item: TimelineNotification) => {
    setSelectedItem(item);
    
    // Marcar como lido quando selecionado
    handleMarkAsRead(item);
    
    // No mobile, abrir context panel quando selecionar item
    if (isMobile) {
      setContextPanelOpen(true);
    }
  }, [isMobile, handleMarkAsRead]);

  const handleFilterChange = useCallback((filters: typeof activeFilters) => {
    setActiveFilters(filters);
    
    // Rastrear uso dos filtros para os filtros rápidos
    if (filters.types.length > 0 || filters.priorities.length > 0) {
      trackFilterUsage(filters.types, filters.priorities);
    }
  }, [trackFilterUsage]);

  const handleToggleFilters = useCallback(() => {
    setShowFilters(true);
  }, []);

  // Handler para toggle de filtros rápidos de tipos
  const handleQuickTypeToggle = useCallback((type: TimelineItemType) => {
    setActiveFilters(prev => {
      const isSelected = prev.types.includes(type);
      const newTypes = isSelected 
        ? prev.types.filter(t => t !== type)
        : [...prev.types, type];
      
      const newFilters = {
        ...prev,
        types: newTypes
      };

      // Rastrear uso dos filtros rápidos
      if (!isSelected) {
        trackFilterUsage([type]);
      }

      return newFilters;
    });
  }, [trackFilterUsage]);

  // Handler para toggle de filtros rápidos de prioridades
  const handleQuickPriorityToggle = useCallback((priority: TimelinePriority) => {
    setActiveFilters(prev => {
      const isSelected = prev.priorities.includes(priority);
      const newPriorities = isSelected 
        ? prev.priorities.filter(p => p !== priority)
        : [...prev.priorities, priority];
      
      const newFilters = {
        ...prev,
        priorities: newPriorities
      };

      // Rastrear uso dos filtros rápidos
      if (!isSelected) {
        trackFilterUsage([], [priority]);
      }

      return newFilters;
    });
  }, [trackFilterUsage]);

  // Handler para limpar todos os filtros
  const handleClearAllFilters = useCallback(() => {
    setActiveFilters({
      types: [],
      priorities: [],
      search: '',
    });
  }, []);

  const handleCloseContextPanel = useCallback(() => {
    setContextPanelOpen(false);
    if (isMobile) {
      setSelectedItem(null);
    }
  }, [isMobile]);

  // Função utilitária para encontrar elemento de timeline de forma segura
  const findTimelineElement = useCallback((itemId: string) => {
    if (!itemId) {
      return null;
    }

    try {
      // Primeiro tentar por atributo data com escape de CSS
      let element: HTMLElement | null = null;
      
      try {
        const escapedId = CSS.escape(itemId);
        const selector = `[data-timeline-item="${escapedId}"]`;
        element = document.querySelector(selector) as HTMLElement;
      } catch (cssError) {
        // Erro ao escapar CSS para ID
      }
      
      // Fallback: tentar sem escape (para IDs simples)
      if (!element) {
        try {
          const fallbackSelector = `[data-timeline-item="${itemId}"]`;
          element = document.querySelector(fallbackSelector) as HTMLElement;
        } catch (fallbackError) {
          // Erro no seletor fallback
        }
      }
      
      // Fallback: tentar como ID direto
      if (!element) {
        try {
          element = document.getElementById(itemId);
        } catch (idError) {
          // Erro ao buscar por ID direto
        }
      }
      
      // Verificar se o elemento foi encontrado
      if (element) {
        return element; // Retornar elemento encontrado
      }
      
      return null;
    } catch (error) {
      return null;
    }
  }, []);

  // Handlers de navegação J/K para Timeline
  const handleNextItem = useCallback(() => {
    if (filteredItems.length === 0) {
      return;
    }
    
    const currentIndex = selectedItem 
      ? filteredItems.findIndex(item => item.id === selectedItem.id)
      : -1;
    
    // J = Próximo item (para baixo na lista)
    let nextIndex;
    if (currentIndex === -1) {
      nextIndex = 0; // Primeiro item se nenhum selecionado
    } else if (currentIndex >= filteredItems.length - 1) {
      nextIndex = 0; // Wrap para o primeiro
    } else {
      nextIndex = currentIndex + 1; // Próximo item
    }
    
    const nextItem = filteredItems[nextIndex];
    
    if (nextItem) {
      handleItemSelect(nextItem);
      
      // Scroll para garantir visibilidade do item
      setTimeout(() => {
        const element = findTimelineElement(nextItem.id);
        if (element) {
          element.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center',
            inline: 'nearest'
          });
        }
      }, 100);
    }
  }, [filteredItems, selectedItem, handleItemSelect, findTimelineElement]);

  const handlePreviousItem = useCallback(() => {
    if (filteredItems.length === 0) {
      return;
    }
    
    const currentIndex = selectedItem 
      ? filteredItems.findIndex(item => item.id === selectedItem.id)
      : -1;
    
    // K = Item anterior (para cima na lista)
    let prevIndex;
    if (currentIndex === -1) {
      prevIndex = filteredItems.length - 1; // Último item se nenhum selecionado
    } else if (currentIndex <= 0) {
      prevIndex = filteredItems.length - 1; // Wrap para o último
    } else {
      prevIndex = currentIndex - 1; // Item anterior
    }
    
    const prevItem = filteredItems[prevIndex];
    
    if (prevItem) {
      handleItemSelect(prevItem);
      
      // Scroll para garantir visibilidade do item
      setTimeout(() => {
        const element = findTimelineElement(prevItem.id);
        if (element) {
          element.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center',
            inline: 'nearest'
          });
        }
      }, 100);
    }
  }, [filteredItems, selectedItem, handleItemSelect, findTimelineElement]);

  // Configurar atalhos contextuais para navegação J/K
  const timelineActions = useMemo(() => {
    // Garantir que as funções existem antes de criar o objeto
    if (!handleNextItem || !handlePreviousItem) {
      return undefined;
    }

    const actions = {
      onNextItem: handleNextItem,
      onPreviousItem: handlePreviousItem,
    };
    return actions;
  }, [handleNextItem, handlePreviousItem]);
  
  // Efeito para timelineActions
  
  const { contextualHotkeys } = useContextualHotkeys({
    timelineActions
  });

  // Efeito para atalhos contextuais

  // ========= RENDERIZAÇÃO CONDICIONAL - APÓS TODOS OS HOOKS =========

  // Estado de carregamento - usar loading otimizado
  if (optimizedIsLoading) {
    return (
      <motion.div
        className={cn("flex flex-col h-full", className)}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <Card className="p-8 text-center">
          <Loader2 className="h-12 w-12 mx-auto mb-4 animate-spin text-blue-500" />
          <h3 className="text-lg font-medium mb-2">Verificando disponibilidade...</h3>
          <p className="text-sm text-muted-foreground">
            Carregando configurações da timeline.
          </p>
        </Card>
      </motion.div>
    );
  }

  // Feature não habilitada
  if (!isEnabled || !limits) {
    return <FeatureNotAvailable featureName="Timeline Unificada" />;
  }

  // ========= RENDERIZAÇÃO PRINCIPAL =========
  return (
    <motion.div
      className={cn(
        "flex flex-col h-screen",
        className
      )}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header com controles */}
      <motion.div 
        className="mb-4"
        initial={{ opacity: 0, y: -20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -20, scale: 0.95 }}
        transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
      >
        <Card className={cn(
          "p-3 bg-white/70 dark:bg-gray-900/70 backdrop-blur-sm border border-white/20 shadow-lg",
          (isNative || isMobile) && "border-0 rounded-none mx-0"
        )}>
          <div className={cn(
            "flex items-center justify-between",
            (isNative || isMobile) && "gap-2"
          )}>
            <div className="flex items-center gap-2 flex-wrap">
              <span className="text-sm text-muted-foreground">
                {filteredItems.length} atividades
              </span>
              
              {/* Badge do plano atual */}
              <div className={cn(
                "px-2 py-1 rounded-full text-xs font-medium border",
                limits.isFreePlan && "bg-gray-100 text-gray-700 border-gray-300",
                limits.isProPlan && "bg-blue-50 text-blue-700 border-blue-300", 
                limits.isMaxPlan && "bg-purple-50 text-purple-700 border-purple-300"
              )}>
                {limits.isFreePlan && "🆓"}
                {limits.isProPlan && "💼"}
                {limits.isMaxPlan && "🚀"}
                {limits.currentPlan}
              </div>

              {/* Indicador de limitações */}
              {limits.isHistoryLimited && (
                <span className="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-full border border-orange-200">
                  {limits.maxHistoryDays} dias
                </span>
              )}

              {/* Filtros rápidos baseados no histórico do usuário */}
              <QuickFilters
                selectedTypes={activeFilters.types}
                selectedPriorities={activeFilters.priorities}
                onTypeToggle={handleQuickTypeToggle}
                onPriorityToggle={handleQuickPriorityToggle}
                onClearAllFilters={handleClearAllFilters}
                hasActiveFilters={activeFilters.types.length > 0 || activeFilters.priorities.length > 0 || activeFilters.search.length > 0}
                className="ml-2"
              />
            </div>

            <div className="flex items-center gap-2">
              {/* Botão de refresh */}
              <TooltipWithPortal content={<p>Atualizar timeline</p>}>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  transition={{ duration: 0.1, ease: "easeInOut" }}
                >
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefresh}
                    disabled={isLoadingQuery}
                    className="border-green-200 transition-all duration-300 px-2 hover:bg-green-50"
                  >
                    <motion.div
                      animate={isLoadingQuery ? { rotate: 360 } : { rotate: 0 }}
                      transition={
                        isLoadingQuery
                          ? { duration: 1, repeat: Infinity, ease: "linear" }
                          : { duration: 0.2 }
                      }
                    >
                      <RefreshCw className="h-4 w-4" />
                    </motion.div>
                  </Button>
                </motion.div>
              </TooltipWithPortal>

              {/* Botão de marcar todas como lidas */}
              <TooltipWithPortal content={<p>Marcar todas como lidas ({unreadCount})</p>}>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  transition={{ duration: 0.1, ease: "easeInOut" }}
                >
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    disabled={markAllAsReadMutation.isPending || unreadCount === 0}
                    className="border-green-200 transition-all duration-300 px-2 hover:bg-green-50"
                  >
                    <CheckCircle2 className="h-4 w-4" />
                    {markAllAsReadMutation.isPending && (
                      <Loader2 className="h-3 w-3 ml-1 animate-spin" />
                    )}
                  </Button>
                </motion.div>
              </TooltipWithPortal>

              {/* Botão de filtros - Apenas ícone */}
              <TooltipWithPortal content={<p>Filtros da Timeline</p>}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleToggleFilters}
                  className={cn(
                    "border-blue-200 transition-all duration-300 px-2",
                    showFilters
                      ? "bg-blue-100 text-blue-700 border-blue-300"
                      : "hover:bg-blue-50"
                  )}
                >
                  <Filter className="h-4 w-4" />
                </Button>
              </TooltipWithPortal>

              {/* Botão para ocultar/mostrar inbox */}
              <TooltipWithPortal content={<p>{showInbox ? "Ocultar" : "Mostrar"} Inbox</p>}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowInbox(prev => !prev)}
                  className={cn(
                    "border-orange-200 transition-all duration-300 px-2",
                    !showInbox
                      ? "bg-orange-100 text-orange-700 border-orange-300"
                      : "hover:bg-orange-50"
                  )}
                >
                  {showInbox ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </TooltipWithPortal>

              {/* Botão para abrir/fechar context panel no desktop - Apenas ícone */}
              {!isMobile && (
                <TooltipWithPortal content={<p>{contextPanelOpen ? "Ocultar" : "Mostrar"} Painel</p>}>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setContextPanelOpen(prev => !prev)}
                    className={cn(
                      "border-purple-200 transition-all duration-300 px-2",
                      contextPanelOpen
                        ? "bg-purple-100 text-purple-700 border-purple-300"
                        : "hover:bg-purple-50"
                    )}
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                </TooltipWithPortal>
              )}
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Modal de filtros */}
      <TimelineFiltersModal
        isOpen={showFilters}
        onClose={() => setShowFilters(false)}
        filters={activeFilters}
        onFilterChange={handleFilterChange}
        limits={limits}
      />

      {/* Loading State - Apenas mostrar se realmente está carregando e não há dados disponíveis */}
      {isLoading && (
        <motion.div 
          className="flex-1 flex items-center justify-center"
          variants={panelVariants}
        >
          <Card className="p-8 text-center">
            <Loader2 className="h-12 w-12 mx-auto mb-4 animate-spin text-blue-500" />
            <h3 className="text-lg font-medium mb-2">Carregando timeline...</h3>
            <p className="text-sm text-muted-foreground">
              Buscando suas notificações mais recentes.
            </p>
          </Card>
        </motion.div>
      )}

      {/* Error State */}
      {error && (
        <motion.div 
          className="flex-1 flex items-center justify-center"
          variants={panelVariants}
        >
          <Card className="p-8 text-center">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
            <h3 className="text-lg font-medium mb-2">Erro ao carregar timeline</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Não foi possível carregar suas notificações.
            </p>
            <Button 
              variant="outline" 
              onClick={() => window.location.reload()}
            >
              Tentar novamente
            </Button>
          </Card>
        </motion.div>
      )}

      {/* Layout principal com grid responsivo - Mostrar quando não está em loading ou há dados em cache */}
      {(!isLoading || availableCacheData) && !error && (
        <motion.div 
          className="flex-1 min-h-0 overflow-hidden"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 20 }}
          transition={{ duration: 0.5 }}
        >
          <div className={cn(
            "h-full grid gap-4",
            showInbox && contextPanelOpen && !isMobile 
              ? "grid-cols-12"
              : showInbox && !contextPanelOpen
              ? "grid-cols-1" 
              : !showInbox && contextPanelOpen && !isMobile
              ? "grid-cols-1"
              : "grid-cols-1"
          )}>
            {/* Lista da timeline - Coluna menor (estilo inbox) - Com scroll independente */}
            {showInbox && (
              <div className={cn(
                "flex flex-col min-h-0 overflow-hidden",
                contextPanelOpen && !isMobile ? "col-span-4" : "col-span-12"
              )}>
                <div className="flex-1 min-h-0 overflow-y-auto border rounded-lg bg-white dark:bg-gray-800">
                  <TimelineList
                    items={filteredItems}
                    selectedItem={selectedItem}
                    onItemSelect={handleItemSelect}
                    className=""
                  />
                </div>
              </div>
            )}

            {/* Context Panel - Coluna maior (estilo visualização de mensagem) - Com scroll independente */}
            {contextPanelOpen && (
              <motion.div
                className={cn(
                  "flex flex-col min-h-0 overflow-hidden",
                  isMobile 
                    ? "fixed inset-0 z-50 bg-white dark:bg-gray-900" 
                    : showInbox
                    ? "col-span-8" // Quando inbox visível: 8 colunas
                    : "col-span-12" // Quando inbox oculta: largura completa
                )}
                variants={!isMobile ? panelVariants : undefined}
                initial={isMobile ? { x: "100%" } : "hidden"}
                animate={isMobile ? { x: 0, transition: { duration: 0.2 } } : "visible"}
                exit={isMobile ? { x: "100%", transition: { duration: 0.15 } } : "hidden"}
              >
                <div className="flex-1 min-h-0 overflow-y-auto border rounded-lg bg-white dark:bg-gray-800">
                  <ContextPanel
                    selectedItem={selectedItem}
                    onClose={handleCloseContextPanel}
                    isMobile={isMobile}
                  />
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}