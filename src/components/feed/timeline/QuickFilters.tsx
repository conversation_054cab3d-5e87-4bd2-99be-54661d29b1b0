/**
 * QuickFilters - Filtros rápidos baseados no histórico de uso do usuário
 * <AUTHOR> Internet 2025
 */

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  TooltipPortal,
} from "@/components/ui/tooltip";
import { useQuickFilters } from '@/hooks/timeline/useQuickFilters';
import type { TimelineItemType, TimelinePriority } from '@/types/timeline';
import { cn } from '@/lib/utils';
import { X, Sparkles, RotateCcw } from 'lucide-react';

interface QuickFiltersProps {
  selectedTypes: TimelineItemType[];
  selectedPriorities: TimelinePriority[];
  onTypeToggle: (type: TimelineItemType) => void;
  onPriorityToggle: (priority: TimelinePriority) => void;
  onClearAllFilters?: () => void;
  hasActiveFilters?: boolean;
  className?: string;
}

export function QuickFilters({
  selectedTypes,
  selectedPriorities,
  onTypeToggle,
  onPriorityToggle,
  onClearAllFilters,
  hasActiveFilters = false,
  className
}: QuickFiltersProps) {
  const { quickFilters } = useQuickFilters();

  return (
    <TooltipPortal 
      side="bottom"
      content={
        <div className="w-80 p-2">
          <div className="text-sm font-medium mb-1">
            ✨ Filtros baseados no seu histórico de uso
          </div>
          <div className="text-xs opacity-80">
            O Vindula aprende seus padrões e mostra os filtros mais relevantes
          </div>
        </div>
      }
    >
      <div className={cn("flex items-center gap-2", className)}>
        {/* Indicador de filtros inteligentes */}
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Sparkles className="h-3 w-3 text-blue-500" />
          <span className="hidden sm:inline">Filtros inteligentes:</span>
        </div>
            
            {quickFilters.map((filter) => {
              const isSelected = filter.category === 'type' 
                ? selectedTypes.includes(filter.type as TimelineItemType)
                : selectedPriorities.includes(filter.type as TimelinePriority);
              
              const handleClick = () => {
                if (filter.category === 'type') {
                  onTypeToggle(filter.type as TimelineItemType);
                } else {
                  onPriorityToggle(filter.type as TimelinePriority);
                }
              };
              
              return (
                <Button
                  key={filter.id}
                  variant="outline"
                  size="sm"
                  onClick={handleClick}
                  className={cn(
                    "h-8 px-3 text-xs font-medium transition-all duration-200",
                    "border border-gray-200 hover:border-gray-300",
                    "bg-white hover:bg-gray-50",
                    isSelected && [
                      filter.colors.bg,
                      filter.colors.text,
                      filter.colors.border,
                      "hover:opacity-90"
                    ]
                  )}
                >
                  {/* Ícone do filtro */}
                  <span className="mr-1.5">{filter.icon}</span>
                  
                  {/* Label do filtro */}
                  <span className="truncate max-w-20">
                    {filter.label}
                  </span>
                  
                  {/* Indicador de seleção */}
                  {isSelected && (
                    <X className="ml-1 h-3 w-3 opacity-70" />
                  )}
                </Button>
              );
            })}

        {/* Botão discreto para limpar todos os filtros */}
        {hasActiveFilters && onClearAllFilters && (
          <TooltipPortal side="bottom" content={<div className="text-sm">Limpar todos os filtros</div>}>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearAllFilters}
              className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600 hover:bg-gray-100"
            >
              <RotateCcw className="h-3 w-3" />
            </Button>
          </TooltipPortal>
        )}
      </div>
    </TooltipPortal>
  );
}

export default QuickFilters;