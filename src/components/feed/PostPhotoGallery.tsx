/**
 * Componente para exibir galeria de fotos em posts
 * <AUTHOR> Internet 2025
 */
import { useState } from 'react';
import { PostImage } from '@/types/post.types';
import { cn } from '@/lib/utils';
import { Dialog, DialogContent, DialogTrigger, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface PostPhotoGalleryProps {
  images: PostImage[];
  className?: string;
}

export function PostPhotoGallery({ images, className }: PostPhotoGalleryProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);

  if (!images || images.length === 0) {
    return null;
  }

  const openModal = (index: number) => {
    setSelectedImageIndex(index);
    setIsModalOpen(true);
  };

  const nextImage = () => {
    setSelectedImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setSelectedImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const getGridLayout = (count: number) => {
    if (count === 1) {
      return 'grid-cols-1';
    }
    if (count === 2) {
      return 'grid-cols-2';
    }
    if (count === 3) {
      return 'grid-cols-3';
    }
    if (count === 4) {
      return 'grid-cols-2 grid-rows-2';
    }
    // Para 5+ fotos, usar layout 2x2 + overlay com contador
    return 'grid-cols-2 grid-rows-2';
  };

  const renderGallery = () => {
    const imageCount = images.length;
    
    if (imageCount === 1) {
      return (
        <div 
          onClick={() => openModal(0)}
          className="cursor-pointer group relative overflow-hidden rounded-lg"
        >
          <img
            src={images[0].image_url}
            alt="Foto do post"
            className="w-full h-80 object-cover transition-transform duration-200 group-hover:scale-105"
          />
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors" />
        </div>
      );
    }

    if (imageCount <= 4) {
      return (
        <div className={cn("grid gap-2 h-80", getGridLayout(imageCount))}>
          {images.map((image, index) => (
            <div
              key={image.id}
              onClick={() => openModal(index)}
              className="cursor-pointer group relative overflow-hidden rounded-lg"
            >
              <img
                src={image.image_url}
                alt={`Foto ${index + 1} do post`}
                className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors" />
            </div>
          ))}
        </div>
      );
    }

    // Para 5+ fotos
    return (
      <div className="grid grid-cols-2 grid-rows-2 gap-2 h-80">
        {images.slice(0, 3).map((image, index) => (
          <div
            key={image.id}
            onClick={() => openModal(index)}
            className={cn(
              "cursor-pointer group relative overflow-hidden rounded-lg",
              index === 0 ? "row-span-2" : ""
            )}
          >
            <img
              src={image.image_url}
              alt={`Foto ${index + 1} do post`}
              className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors" />
          </div>
        ))}
        <div
          onClick={() => openModal(3)}
          className="cursor-pointer group relative overflow-hidden rounded-lg"
        >
          <img
            src={images[3].image_url}
            alt={`Foto 4 do post`}
            className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
          />
          {imageCount > 4 && (
            <div className="absolute inset-0 bg-black/70 flex items-center justify-center">
              <span className="text-white text-2xl font-semibold">
                +{imageCount - 4}
              </span>
            </div>
          )}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors" />
        </div>
      </div>
    );
  };

  return (
    <div className={cn("w-full", className)}>
      {renderGallery()}

      {/* Modal do carrossel */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-4xl p-0 bg-black/95">
          <VisuallyHidden>
            <DialogTitle>Galeria de Fotos</DialogTitle>
            <DialogDescription>
              Visualização da galeria de fotos. Use as setas para navegar entre as imagens ou clique nas miniaturas.
            </DialogDescription>
          </VisuallyHidden>
          <div className="relative">
            {/* Botão fechar */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsModalOpen(false)}
              className="absolute top-4 right-4 z-10 text-white hover:bg-white/20"
            >
              <X className="h-5 w-5" />
            </Button>

            {/* Contador de fotos */}
            <div className="absolute top-4 left-4 z-10 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
              {selectedImageIndex + 1} / {images.length}
            </div>

            {/* Imagem principal */}
            <div className="flex items-center justify-center min-h-[70vh]">
              <img
                src={images[selectedImageIndex].image_url}
                alt={`Foto ${selectedImageIndex + 1}`}
                className="max-w-full max-h-[70vh] object-contain"
              />
            </div>

            {/* Navegação */}
            {images.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                >
                  <ChevronLeft className="h-6 w-6" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                >
                  <ChevronRight className="h-6 w-6" />
                </Button>
              </>
            )}

            {/* Thumbnails */}
            {images.length > 1 && (
              <div className="flex gap-2 p-4 justify-center bg-black/20">
                {images.map((image, index) => (
                  <button
                    key={image.id}
                    onClick={() => setSelectedImageIndex(index)}
                    className={cn(
                      "w-16 h-16 rounded-lg overflow-hidden border-2 transition-all",
                      index === selectedImageIndex 
                        ? "border-white" 
                        : "border-transparent opacity-70 hover:opacity-100"
                    )}
                  >
                    <img
                      src={image.image_url}
                      alt={`Thumbnail ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}