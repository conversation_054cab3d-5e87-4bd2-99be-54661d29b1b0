/**
 * Card Premium para Histórico de Cobrança
 * <AUTHOR> Internet 2025
 */
import { useState } from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { 
  CreditCard, 
  Calendar, 
  DollarSign, 
  Package, 
  Eye, 
  Download,
  CheckCircle,
  Clock,
  AlertTriangle,
  TrendingUp,
  Receipt,
  History
} from "lucide-react";
import { usePlanFinancialDetails, useBillingHistory, useBillingAddonDetails } from "@/lib/query/hooks/useBillingHistory";
import { BillingDetailsSheet } from "./BillingDetailsSheet";
import { cn } from "@/lib/utils";

interface BillingHistoryCardProps {
  className?: string;
}

export function BillingHistoryCard({ className }: BillingHistoryCardProps) {
  const [showDetailsSheet, setShowDetailsSheet] = useState(false);
  const [selectedBilling, setSelectedBilling] = useState<any>(null);
  const [selectedBillingId, setSelectedBillingId] = useState<string | null>(null);

  const { data: currentBilling, isLoading: isLoadingCurrent } = usePlanFinancialDetails();
  const { data: billingHistory, isLoading: isLoadingHistory } = useBillingHistory(5, 0);
  const { data: addonDetails, isLoading: isLoadingAddons } = useBillingAddonDetails(selectedBillingId);

  // Fallback: usar dados do histórico se currentBilling estiver vazio
  const effectiveCurrentBilling = currentBilling || (billingHistory?.length > 0 ? {
    // Para exibição na interface
    planName: billingHistory[0].plan_name,
    planPrice: billingHistory[0].plan_monthly_price,
    addonsTotal: billingHistory[0].total_addons_amount,
    monthlyTotal: billingHistory[0].total_amount,
    addonsCount: billingHistory[0].addons_count,
    billingStatus: billingHistory[0].billing_status,
    formattedNextBilling: 'Próxima cobrança em breve',
    // Para o modal de detalhes (estrutura original)
    plan_name: billingHistory[0].plan_name,
    plan_monthly_price: billingHistory[0].plan_monthly_price,
    total_addons_amount: billingHistory[0].total_addons_amount,
    total_amount: billingHistory[0].total_amount,
    billing_status: billingHistory[0].billing_status,
    billing_period_start: billingHistory[0].billing_period_start,
    billing_period_end: billingHistory[0].billing_period_end,
    billing_id: billingHistory[0].billing_id,
    due_date: billingHistory[0].due_date,
    created_at: billingHistory[0].created_at,
    paid_at: billingHistory[0].paid_at
  } : null);

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getBillingStatusInfo = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          label: 'Pendente',
          color: 'bg-amber-500',
          textColor: 'text-amber-700',
          bgColor: 'bg-amber-50',
          icon: Clock
        };
      case 'paid':
        return {
          label: 'Pago',
          color: 'bg-green-500',
          textColor: 'text-green-700',
          bgColor: 'bg-green-50',
          icon: CheckCircle
        };
      case 'overdue':
        return {
          label: 'Vencido',
          color: 'bg-red-500',
          textColor: 'text-red-700',
          bgColor: 'bg-red-50',
          icon: AlertTriangle
        };
      default:
        return {
          label: 'Processando',
          color: 'bg-blue-500',
          textColor: 'text-blue-700',
          bgColor: 'bg-blue-50',
          icon: Clock
        };
    }
  };

  const handleViewDetails = (billing: any) => {
    // Verificar se billing não é null antes de acessar propriedades
    if (!billing) {
      console.warn('[BillingHistoryCard] Tentativa de ver detalhes de billing null');
      return;
    }
    
    // Garantir que temos o billing_id correto
    const billingId = billing.billing_id || billing.id;
    setSelectedBillingId(billingId);
    setSelectedBilling(billing);
    setShowDetailsSheet(true);
  };

  if (isLoadingCurrent || isLoadingHistory) {
    return (
      <Card className={cn("border-0 bg-gradient-to-br from-white to-slate-50 shadow-xl animate-pulse", className)}>
        <CardHeader className="space-y-4">
          <div className="h-6 bg-slate-200 rounded w-1/3"></div>
          <div className="h-4 bg-slate-200 rounded w-2/3"></div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="h-32 bg-slate-200 rounded"></div>
          <div className="space-y-2">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-16 bg-slate-200 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <motion.div variants={cardVariants}>
        <Card className={cn("border-0 bg-gradient-to-br from-white to-slate-50 shadow-xl", className)}>
          <CardHeader className="pb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-4 rounded-2xl shadow-lg bg-gradient-to-r from-emerald-500 to-teal-500">
                  <Receipt className="h-8 w-8 text-white" />
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold text-slate-800">
                    
                  </CardTitle>
                  <CardDescription className="text-slate-600 mt-1 text-base">
                    Detalhes financeiros do período vigente
                  </CardDescription>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleViewDetails(effectiveCurrentBilling)}
                disabled={!effectiveCurrentBilling}
                className="gap-2"
              >
                <Eye className="h-4 w-4" />
                Ver Detalhes
              </Button>
            </div>
          </CardHeader>

          <CardContent className="space-y-8">
            {/* Resumo Financeiro Atual */}
            {effectiveCurrentBilling && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Valor Total */}
                <div className="relative overflow-hidden rounded-2xl p-6 bg-gradient-to-br from-emerald-50 to-teal-50 border border-emerald-100">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-3 mb-2">
                        <div className="p-2 rounded-lg bg-gradient-to-r from-emerald-500 to-teal-500">
                          <DollarSign className="h-5 w-5 text-white" />
                        </div>
                        <span className="text-sm font-medium text-slate-600">Total Mensal</span>
                      </div>
                      <div className="text-3xl font-bold text-slate-800">
                        {formatCurrency(effectiveCurrentBilling.monthlyTotal || 0)}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Plano Base */}
                <div className="relative overflow-hidden rounded-2xl p-6 bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-100">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-3 mb-2">
                        <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-indigo-500">
                          <Package className="h-5 w-5 text-white" />
                        </div>
                        <span className="text-sm font-medium text-slate-600">Plano Base</span>
                      </div>
                      <div className="text-3xl font-bold text-slate-800">
                        {formatCurrency(effectiveCurrentBilling.planPrice || 0)}
                      </div>
                      <div className="text-xs text-slate-500 mt-1">
                        {effectiveCurrentBilling.planName}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Add-ons */}
                <div className="relative overflow-hidden rounded-2xl p-6 bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-100">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-3 mb-2">
                        <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500">
                          <TrendingUp className="h-5 w-5 text-white" />
                        </div>
                        <span className="text-sm font-medium text-slate-600">Add-ons</span>
                      </div>
                      <div className="text-3xl font-bold text-slate-800">
                        {formatCurrency(effectiveCurrentBilling.addonsTotal || 0)}
                      </div>
                      <div className="text-xs text-slate-500 mt-1">
                        {effectiveCurrentBilling.addonsCount || 0} itens
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Status e Período */}
            {effectiveCurrentBilling && (
              <div className="rounded-2xl bg-gradient-to-r from-slate-50 to-slate-100 p-6 border border-slate-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    {(() => {
                      const status = getBillingStatusInfo(effectiveCurrentBilling.billingStatus || 'pending');
                      const StatusIcon = status.icon;
                      return (
                        <>
                          <div className={`p-3 rounded-xl ${status.bgColor}`}>
                            <StatusIcon className={`h-6 w-6 ${status.textColor}`} />
                          </div>
                          <div>
                            <div className="flex items-center gap-3">
                              <h3 className="text-lg font-semibold text-slate-800">
                                Status: {status.label}
                              </h3>
                              <Badge className={status.color}>
                                {status.label}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-2 text-slate-600 mt-1">
                              <Calendar className="h-4 w-4" />
                              <span>
                                {effectiveCurrentBilling.formattedNextBilling ? `Próxima cobrança: ${effectiveCurrentBilling.formattedNextBilling}` : 'Período atual'}
                              </span>
                            </div>
                          </div>
                        </>
                      );
                    })()}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Baixar Fatura
                  </Button>
                </div>
              </div>
            )}

            {/* Mensagem quando não há dados financeiros */}
            {!effectiveCurrentBilling && (
              <div className="text-center py-12 space-y-4">
                <div className="p-4 rounded-full bg-blue-50 w-fit mx-auto">
                  <Clock className="h-8 w-8 text-blue-500" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-slate-700">
                    Período de Cortesia Ativo
                  </h3>
                  <p className="text-sm text-slate-500 max-w-md mx-auto">
                    Você está no período de cortesia de 7 dias do seu novo plano. 
                    Os detalhes de cobrança aparecerão após a ativação oficial.
                  </p>
                </div>
              </div>
            )}

            <Separator />

            {/* Histórico Recente */}
            <div>
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-r from-amber-500 to-orange-500">
                    <History className="h-5 w-5 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-slate-800">Histórico Recente</h3>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowDetailsSheet(true)}
                  className="gap-2"
                >
                  Ver Tudo
                  <Eye className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                {billingHistory?.length > 0 ? (
                  billingHistory.slice(0, 3).map((billing: any) => {
                    const status = getBillingStatusInfo(billing.billing_status);
                    const StatusIcon = status.icon;
                    
                    return (
                      <motion.div
                        key={billing.billing_id}
                        variants={cardVariants}
                        className="flex items-center justify-between p-4 rounded-xl bg-white shadow-sm border border-slate-100 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center gap-4">
                          <div className={`p-2 rounded-lg ${status.bgColor}`}>
                            <StatusIcon className={`h-4 w-4 ${status.textColor}`} />
                          </div>
                          <div>
                            <div className="flex items-center gap-3">
                              <span className="font-medium text-slate-800">
                                {formatDate(billing.billing_period_start)} - {formatDate(billing.billing_period_end)}
                              </span>
                              <Badge variant="outline" className={status.textColor}>
                                {status.label}
                              </Badge>
                            </div>
                            <div className="text-sm text-slate-500">
                              {billing.plan_name} + {billing.addons_count || 0} add-ons
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-semibold text-slate-800">
                            {formatCurrency(billing.total_amount)}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewDetails(billing)}
                            className="text-xs gap-1 h-8"
                          >
                            <Eye className="h-3 w-3" />
                            Detalhes
                          </Button>
                        </div>
                      </motion.div>
                    );
                  })
                ) : (
                  <div className="text-center py-8 text-slate-500">
                    <Receipt className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <div className="space-y-2">
                      <p className="font-medium">Nenhum histórico de cobrança encontrado</p>
                      <p className="text-sm text-slate-400">
                        Se você acabou de fazer um upgrade, os registros de cobrança<br />
                        aparecerão após o período de cortesia de 7 dias.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Sheet de Detalhes */}
      <BillingDetailsSheet
        open={showDetailsSheet}
        onOpenChange={setShowDetailsSheet}
        billing={selectedBilling}
        addonDetails={addonDetails}
        isLoadingAddons={isLoadingAddons}
      />
    </>
  );
} 