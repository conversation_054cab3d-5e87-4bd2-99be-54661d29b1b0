/**
 * Componente para exibir um item de notificação com formatação específica por tipo.
 * <AUTHOR> Internet 2025
 */

import { Check, ExternalLink, Medal, MessageSquare, Heart, Bell, FileText, User, ArrowUp, CheckCircle, Send, Calendar, CalendarCheck, CalendarX, Gift, Rocket, Trophy, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Notification } from '@/lib/query/hooks/useNotifications';
import { Link } from 'react-router-dom';
import { EventNotificationRenderer } from './EventNotificationRenderer';
import { MissionNotificationRenderer } from './MissionNotificationRenderer';
import { useUnifiedRealtime } from "@/contexts/UnifiedRealtimeProvider";

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
}

export function NotificationItem({ notification, onMarkAsRead }: NotificationItemProps) {
  const { handlers } = useUnifiedRealtime();

  const getNotificationLink = () => {
    // Tratamento especial para cartões de aniversário
    if (notification.type === 'mention' && notification.metadata?.card_type === 'birthday_card') {
      return null; // Cartões de aniversário não têm link, apenas ação de clique
    }
    
    if (!notification.reference_id && notification.type !== 'post_published') return null;

    switch (notification.type) {
      case 'medal_earned':
        return `/profile?tab=medals`;
      case 'level_up':
        return `/profile?tab=medals`;
      case 'mission_progress':
      case 'mission_completed':
        return `/missions`;
      case 'mention':
      case 'comment':
      case 'like':
      case 'post_published':
        return `/post/${notification.reference_id}`;
      case 'document_share':
        return `/library/document/${notification.reference_id}`;
      case 'obligation_assigned':
        return `/obligations/read/${notification.reference_id}`;
      case 'event_participation_update':
      case 'event_invitation':
      case 'event_reminder':
      case 'event_cancelled':
      case 'event_updated':
        return `/events?eventId=${notification.reference_id}`;
      case 'privacy_request':
        return `/privacy`;
      case 'system':
        // Para notificações do sistema, verificar se há um link nos metadados
        return notification.metadata?.link || null;
      default:
        return null;
    }
  };

  const handleBirthdayCardClick = () => {
    const metadata = notification.metadata;
    if (metadata && metadata.card_type === 'birthday_card') {
      const cardData = {
        id: metadata.card_id as string,
        sender_id: metadata.sender_id as string,
        sender_name: metadata.sender_name as string,
        sender_avatar: metadata.sender_avatar as string,
        message: metadata.message as string,
        background_type: (metadata.background_type as 'gradient' | 'color' | 'image') || 'gradient',
        background_config: metadata.background_config as Record<string, any> || {},
        media_type: metadata.media_type as 'video' | 'audio' | undefined,
        media_url: metadata.media_url as string | undefined,
        media_duration: metadata.media_duration as number | undefined,
        created_at: notification.created_at
      };
      
      handlers.gamification?.showBirthdayCard?.(cardData);
      
      // Marcar como lida se ainda não foi
      if (!notification.read) {
        onMarkAsRead(notification.id);
      }
    }
  };

  const handleMedalClick = async () => {
    const medalId = notification.metadata?.medal_id;
    if (medalId) {
      await handlers.gamification?.showMedalModal?.(medalId);
      
      // Marcar como lida se ainda não foi
      if (!notification.read) {
        onMarkAsRead(notification.id);
      }
    }
  };

  const handleLevelUpClick = () => {
    const level = notification.metadata?.level;
    if (level) {
      handlers.gamification?.showLevelUpAnimation?.({ level, title: notification.title || `Nível ${level}` });
      
      // Marcar como lida se ainda não foi
      if (!notification.read) {
        onMarkAsRead(notification.id);
      }
    }
  };

  // Se for notificação de evento, usar renderizador especializado
  const isEventNotification = [
    'event_participation_update',
    'event_invitation',
    'event_reminder',
    'event_cancelled',
    'event_updated'
  ].includes(notification.type);

  // Se for notificação de missão, usar renderizador especializado
  const isMissionNotification = [
    'mission_progress',
    'mission_completed'
  ].includes(notification.type);

  if (isMissionNotification) {
    return <MissionNotificationRenderer notification={notification} onMarkAsRead={onMarkAsRead} />;
  }

  if (isEventNotification) {
    return (
      <div className="relative">
        <EventNotificationRenderer notification={notification} />
        
        {/* Botões de ação sobrepostos */}
        <div className="absolute top-2 right-2 flex items-center gap-1">
          {getNotificationLink() && (
            <Button
              variant="ghost"
              size="icon"
              asChild
              title="Ver detalhes"
              className="h-8 w-8"
            >
              <Link to={getNotificationLink()!}>
                <ExternalLink className="w-4 h-4" />
              </Link>
            </Button>
          )}
          {!notification.read && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onMarkAsRead(notification.id)}
              title="Marcar como lida"
              className="h-8 w-8"
            >
              <Check className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>
    );
  }

  const getNotificationIcon = () => {
    // Ícone especial para cartões de aniversário
    if (notification.type === 'mention' && notification.metadata?.card_type === 'birthday_card') {
      return <Gift className="w-5 h-5 text-pink-500" />;
    }

    switch (notification.type) {
      case 'medal_earned':
        return <Medal className="w-5 h-5 text-yellow-500" />;
      case 'level_up':
        return <ArrowUp className="w-5 h-5 text-green-500" />;
      case 'mission_progress':
        return <Rocket className="w-5 h-5 text-blue-500" />;
      case 'mission_completed':
        return <Trophy className="w-5 h-5 text-green-500" />;
      case 'mention':
        return <User className="w-5 h-5 text-blue-500" />;
      case 'comment':
        return <MessageSquare className="w-5 h-5 text-violet-500" />;
      case 'like':
        return <Heart className="w-5 h-5 text-red-500" />;
      case 'document_share':
        return <FileText className="w-5 h-5 text-indigo-500" />;
      case 'obligation_assigned':
        return <FileText className="w-5 h-5 text-orange-500" />;
      case 'post_published':
        return <Send className="w-5 h-5 text-emerald-500" />;
      case 'event_participation_update':
        return <Calendar className="w-5 h-5 text-purple-500" />;
      case 'event_invitation':
        return <CalendarCheck className="w-5 h-5 text-blue-500" />;
      case 'event_reminder':
        return <Calendar className="w-5 h-5 text-amber-500" />;
      case 'event_cancelled':
        return <CalendarX className="w-5 h-5 text-red-500" />;
      case 'event_updated':
        return <Calendar className="w-5 h-5 text-orange-500" />;
      case 'privacy_request':
        return <Shield className="w-5 h-5 text-red-600" />;
      case 'system':
        return <CheckCircle className="w-5 h-5 text-sky-500" />;
      default:
        return <Bell className="w-5 h-5 text-gray-500" />;
    }
  };

  const getActionButton = () => {
    // Se a notificação já foi lida, não mostrar botão de ação
    if (notification.read) {
      return null;
    }

    return (
      <Button
        variant="ghost"
        size="icon"
        onClick={() => onMarkAsRead(notification.id)}
        title="Marcar como lida"
      >
        <Check className="w-4 h-4" />
      </Button>
    );
  };

  const getSpecialActionButton = () => {
    // Botão especial para cartões de aniversário
    if (notification.type === 'mention' && notification.metadata?.card_type === 'birthday_card') {
      return (
        <Button
          variant="ghost"
          size="icon"
          onClick={handleBirthdayCardClick}
          title="Ver cartão"
          className="text-pink-500 hover:text-pink-600"
        >
          <Gift className="w-4 h-4" />
        </Button>
      );
    }

    // Botão especial para medalhas
    if (notification.type === 'medal_earned' && notification.metadata?.medal_id) {
      return (
        <Button
          variant="ghost"
          size="icon"
          onClick={handleMedalClick}
          title="Ver medalha"
          className="text-yellow-500 hover:text-yellow-600"
        >
          <Medal className="w-4 h-4" />
        </Button>
      );
    }

    // Botão especial para level up
    if (notification.type === 'level_up' && notification.metadata?.level) {
      return (
        <Button
          variant="ghost"
          size="icon"
          onClick={handleLevelUpClick}
          title="Ver level up"
          className="text-green-500 hover:text-green-600"
        >
          <ArrowUp className="w-4 h-4" />
        </Button>
      );
    }

    // Link padrão para outras notificações
    const notificationLink = getNotificationLink();
    if (notificationLink) {
      return (
        <Button
          variant="ghost"
          size="icon"
          asChild
          title="Ver detalhes"
        >
          <Link to={notificationLink}>
            <ExternalLink className="w-4 h-4" />
          </Link>
        </Button>
      );
    }

    return null;
  };

  return (
    <Card 
      className={`p-4 ${notification.read ? 'bg-muted' : 'border-l-4 border-l-primary'}`}
    >
      <div className="flex items-start gap-3">
        <div className="mt-1 flex-shrink-0">
          {getNotificationIcon()}
        </div>
        <div className="flex-grow min-w-0">
          <div className="flex flex-col">
            {notification.title && (
              <p className="font-medium truncate">{notification.title}</p>
            )}
            <p className={`${notification.title ? 'text-sm text-muted-foreground' : 'font-medium'}`}>
              {notification.type === 'privacy_request' && notification.content.length > 150 
                ? `${notification.content.substring(0, 150)}...` 
                : notification.content
              }
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              {formatDistanceToNow(new Date(notification.created_at), {
                addSuffix: true,
                locale: ptBR,
              })}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-1 flex-shrink-0">
          {getSpecialActionButton()}
          {getActionButton()}
        </div>
      </div>
    </Card>
  );
}
