/**
 * Upgrade Store - Gerenciamento de estado para o sistema de upgrade
 * Centraliza todas as operações de upgrade: planos, add-ons, contexto e progressão
 * <AUTHOR> Internet 2025
 */
import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { AddOn } from '@/components/upgrade/AddOnSelector';
import type { RelevanceContext } from '@/utils/addOnRelevanceEngine';

// Interfaces do estado
export interface SelectedPlan {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  billingCycle: 'monthly' | 'annual';
  features: string[];
  limits: {
    users: number;
    storage: number; // em GB
    aiCredits?: number | 'unlimited';
  };
}

export interface SelectedAddOn extends AddOn {
  quantity?: number;
  totalPrice: number;
  selectedDate: Date;
}

export interface UpgradeContext extends RelevanceContext {
  timestamp: Date;
  source: 'ai' | 'storage' | 'users' | 'plan-management' | 'medals' | 'levels' | 'actions';
  currentPlan?: {
    id: string;
    name: string;
    limitations: Record<string, unknown>;
  };
  triggerData?: {
    reason: string;
    urgency: 'low' | 'medium' | 'high';
    specificNeed?: string;
  };
}

export interface PricingSummary {
  planPrice: number;
  addOnsPrice: number;
  subtotal: number;
  discounts: {
    planDiscount: number;
    addOnDiscounts: number;
    totalDiscount: number;
  };
  taxes: {
    rate: number;
    amount: number;
  };
  total: number;
  currency: string;
  billingCycle: 'monthly' | 'annual';
  nextBillingDate?: Date;
  savings?: {
    annualSavings: number;
    percentageSaved: number;
  };
}

export interface UpgradeStep {
  id: string;
  name: string;
  completed: boolean;
  current: boolean;
  canAccess: boolean;
  data?: Record<string, unknown>;
}

export interface ContactInfo {
  name: string;
  email: string;
  phone: string;
  company: string;
  role: string;
  urgency: string;
  message: string;
  preferredContactMethod: string;
}

export interface UpgradeState {
  // Estado principal
  selectedPlan: SelectedPlan | null;
  selectedAddOns: SelectedAddOn[];
  context: UpgradeContext | null;
  billingCycle: 'monthly' | 'annual';
  contactInfo: ContactInfo | null;
  
  // Progressão
  currentStep: string;
  steps: UpgradeStep[];
  isProcessing: boolean;
  
  // Pricing
  pricingSummary: PricingSummary | null;
  
  // Configurações
  showDebugInfo: boolean;
  autoSave: boolean;
  
  // Metadata
  sessionId: string;
  startedAt: Date | null;
  lastUpdated: Date | null;
  
  // Actions
  setPlan: (plan: SelectedPlan) => void;
  addAddOn: (addOn: AddOn, quantity?: number) => void;
  removeAddOn: (addOnId: string) => void;
  updateAddOnQuantity: (addOnId: string, quantity: number) => void;
  clearAddOns: () => void;
  
  setBillingCycle: (cycle: 'monthly' | 'annual') => void;
  setContext: (context: UpgradeContext) => void;
  updateContext: (updates: Partial<UpgradeContext>) => void;
  setContactInfo: (contactInfo: ContactInfo) => void;
  
  // Step management
  setCurrentStep: (stepId: string) => void;
  completeStep: (stepId: string, data?: Record<string, unknown>) => void;
  goToStep: (stepId: string) => void;
  resetSteps: () => void;
  
  // Pricing
  calculatePricing: () => void;
  updatePricingSummary: (summary: Partial<PricingSummary>) => void;
  
  // Utilities
  reset: () => void;
  clearSession: () => void;
  exportState: () => string;
  importState: (state: string) => boolean;
  
  // Validation
  validateSelection: () => { isValid: boolean; errors: string[] };
  canProceedToNext: () => boolean;
  getStepProgress: () => { current: number; total: number; percentage: number };
  
  // Add-ons helpers
  getAddOnTotal: () => number;
  hasAddOn: (addOnId: string) => boolean;
  getAddOnQuantity: (addOnId: string) => number;
  getSelectedAddOnIds: () => string[];
  getTotalSelectedAddOns: () => number;
  canAddMoreAddOns: (maxAddOns: number) => boolean;
}

// Estado inicial padrão
const initialState = {
  selectedPlan: null,
  selectedAddOns: [],
  context: null,
  billingCycle: 'monthly' as const,
  contactInfo: null,
  currentStep: 'plan-selection',
  steps: [
    { id: 'plan-selection', name: 'Escolha do Plano', completed: false, current: true, canAccess: true },
    { id: 'add-ons', name: 'Add-ons', completed: false, current: false, canAccess: false },
    { id: 'contact-info', name: 'Informações', completed: false, current: false, canAccess: false },
    { id: 'confirmation', name: 'Confirmação', completed: false, current: false, canAccess: false },
    { id: 'success', name: 'Sucesso', completed: false, current: false, canAccess: false }
  ],
  isProcessing: false,
  pricingSummary: null,
  showDebugInfo: process.env.NODE_ENV === 'development',
  autoSave: true,
  sessionId: '',
  startedAt: null,
  lastUpdated: null
};

// Função para gerar session ID único
const generateSessionId = () => {
  return `upgrade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Função para calcular preços de add-ons baseado no ciclo de cobrança
const calculateAddOnPrice = (addOn: AddOn, billingCycle: 'monthly' | 'annual', quantity: number = 1) => {
  const basePrice = billingCycle === 'monthly' ? addOn.monthlyPrice : addOn.annualPrice;
  return basePrice * quantity;
};

// Função para validar compatibilidade de add-ons
const validateAddOnCompatibility = (newAddOn: AddOn, existingAddOns: SelectedAddOn[]): string[] => {
  const errors: string[] = [];
  
  // Verificar conflitos
  if (newAddOn.limitations?.conflictsWith) {
    const conflicts = newAddOn.limitations.conflictsWith.filter(conflictId =>
      existingAddOns.some(addon => addon.id === conflictId)
    );
    if (conflicts.length > 0) {
      errors.push(`Conflita com add-ons já selecionados: ${conflicts.join(', ')}`);
    }
  }
  
  // Verificar se já está selecionado
  if (existingAddOns.some(addon => addon.id === newAddOn.id)) {
    errors.push('Add-on já selecionado');
  }
  
  return errors;
};

export const useUpgradeStore = create<UpgradeState>()(
  persist(
    devtools(
      immer((set, get) => ({
        ...initialState,
        sessionId: generateSessionId(),
        
        // Plan actions
        setPlan: (plan) => set((state) => {
          state.selectedPlan = plan;
          state.lastUpdated = new Date();
          if (!state.startedAt) {
            state.startedAt = new Date();
          }
          
          // Auto-avançar para próximo step se não estiver lá
          if (state.currentStep === 'plan-selection') {
            const addOnsStep = state.steps.find(s => s.id === 'add-ons');
            if (addOnsStep) {
              addOnsStep.canAccess = true;
            }
          }
          
          // Recalcular pricing automaticamente
          get().calculatePricing();
        }, false, 'setPlan'),
        
        // Add-on actions
        addAddOn: (addOn, quantity = 1) => set((state) => {
          const validation = validateAddOnCompatibility(addOn, state.selectedAddOns);
          if (validation.length > 0) {
            console.warn('Cannot add add-on:', validation);
            return;
          }
          
          const totalPrice = calculateAddOnPrice(addOn, state.billingCycle, quantity);
          const selectedAddOn: SelectedAddOn = {
            ...addOn,
            quantity,
            totalPrice,
            selectedDate: new Date()
          };
          
          state.selectedAddOns.push(selectedAddOn);
          state.lastUpdated = new Date();
          
          // Recalcular pricing automaticamente
          get().calculatePricing();
        }, false, 'addAddOn'),
        
        removeAddOn: (addOnId) => set((state) => {
          const index = state.selectedAddOns.findIndex(addon => addon.id === addOnId);
          if (index !== -1) {
            state.selectedAddOns.splice(index, 1);
            state.lastUpdated = new Date();
            
            // Recalcular pricing automaticamente
            get().calculatePricing();
          }
        }, false, 'removeAddOn'),
        
        updateAddOnQuantity: (addOnId, quantity) => set((state) => {
          const addOn = state.selectedAddOns.find(addon => addon.id === addOnId);
          if (addOn && quantity > 0) {
            addOn.quantity = quantity;
            addOn.totalPrice = calculateAddOnPrice(addOn, state.billingCycle, quantity);
            state.lastUpdated = new Date();
            
            // Recalcular pricing automaticamente
            get().calculatePricing();
          }
        }, false, 'updateAddOnQuantity'),
        
        clearAddOns: () => set((state) => {
          state.selectedAddOns = [];
          state.lastUpdated = new Date();
          
          // Recalcular pricing automaticamente
          get().calculatePricing();
        }, false, 'clearAddOns'),
        
        // Billing cycle
        setBillingCycle: (cycle) => set((state) => {
          state.billingCycle = cycle;
          
          // Recalcular preços dos add-ons
          state.selectedAddOns.forEach(addOn => {
            addOn.totalPrice = calculateAddOnPrice(addOn, cycle, addOn.quantity || 1);
          });
          
          state.lastUpdated = new Date();
          
          // Recalcular pricing automaticamente
          get().calculatePricing();
        }, false, 'setBillingCycle'),
        
        // Context management
        setContext: (context) => set((state) => {
          state.context = context;
          state.lastUpdated = new Date();
        }, false, 'setContext'),
        
        updateContext: (updates) => set((state) => {
          if (state.context) {
            Object.assign(state.context, updates);
            state.lastUpdated = new Date();
          }
        }, false, 'updateContext'),
        
        // Step management
        setCurrentStep: (stepId) => set((state) => {
          state.steps.forEach(step => {
            step.current = step.id === stepId;
          });
          state.currentStep = stepId;
        }, false, 'setCurrentStep'),
        
        completeStep: (stepId, data) => set((state) => {
          const step = state.steps.find(s => s.id === stepId);
          if (step) {
            step.completed = true;
            if (data) {
              step.data = data;
            }
            
            // Habilitar próximo step
            const currentIndex = state.steps.findIndex(s => s.id === stepId);
            if (currentIndex < state.steps.length - 1) {
              state.steps[currentIndex + 1].canAccess = true;
            }
            
            state.lastUpdated = new Date();
          }
        }, false, 'completeStep'),
        
        goToStep: (stepId) => set((state) => {
          const step = state.steps.find(s => s.id === stepId);
          if (step && step.canAccess) {
            state.steps.forEach(s => {
              s.current = s.id === stepId;
            });
            state.currentStep = stepId;
          }
        }, false, 'goToStep'),
        
        resetSteps: () => set((state) => {
          state.steps = [...initialState.steps];
          state.currentStep = 'plan-selection';
        }, false, 'resetSteps'),
        
        // Pricing
        calculatePricing: () => set((state) => {
          if (!state.selectedPlan) return;
          
          const planPrice = state.selectedPlan.price;
          const addOnsPrice = state.selectedAddOns.reduce((sum, addon) => sum + addon.totalPrice, 0);
          const subtotal = planPrice + addOnsPrice;
          
          // CORREÇÃO: Cálculo correto de desconto para ciclo anual
          let planDiscount = 0;
          let savings;
          
          if (state.billingCycle === 'annual' && state.selectedPlan.originalPrice) {
            // Para plano anual: originalPrice é o preço mensal * 12
            // planPrice já é o preço anual com desconto
            planDiscount = state.selectedPlan.originalPrice - planPrice;
            
            // Cálculo de economia anual
            const annualSavings = planDiscount;
            const percentageSaved = (annualSavings / state.selectedPlan.originalPrice) * 100;
            savings = { annualSavings, percentageSaved };
          }
          
          const addOnDiscounts = 0; // Pode ser implementado baseado em regras específicas
          const totalDiscount = planDiscount + addOnDiscounts;
          
          // Cálculo de impostos (comentado - não será usado por enquanto)
          const taxRate = 0;
          const taxAmount = 0;
          
          const total = subtotal; // + taxAmount;
          
          state.pricingSummary = {
            planPrice,
            addOnsPrice,
            subtotal,
            discounts: {
              planDiscount,
              addOnDiscounts,
              totalDiscount
            },
            taxes: {
              rate: taxRate,
              amount: taxAmount
            },
            total,
            currency: 'BRL',
            billingCycle: state.billingCycle,
            nextBillingDate: new Date(Date.now() + (state.billingCycle === 'monthly' ? 30 : 365) * 24 * 60 * 60 * 1000),
            savings
          };
        }, false, 'calculatePricing'),
        
        updatePricingSummary: (summary) => set((state) => {
          if (state.pricingSummary) {
            Object.assign(state.pricingSummary, summary);
          }
        }, false, 'updatePricingSummary'),
        
        // Utilities
        reset: () => set((state) => {
          Object.assign(state, {
            ...initialState,
            sessionId: generateSessionId(),
            startedAt: null,
            lastUpdated: null
          });
        }, false, 'reset'),
        
        clearSession: () => set((state) => {
          state.sessionId = generateSessionId();
          state.startedAt = null;
          state.lastUpdated = null;
        }, false, 'clearSession'),
        
        exportState: () => {
          const state = get();
          return JSON.stringify({
            selectedPlan: state.selectedPlan,
            selectedAddOns: state.selectedAddOns,
            context: state.context,
            billingCycle: state.billingCycle,
            currentStep: state.currentStep,
            steps: state.steps,
            pricingSummary: state.pricingSummary,
            sessionId: state.sessionId,
            startedAt: state.startedAt,
            lastUpdated: state.lastUpdated
          });
        },
        
        importState: (stateJson) => {
          try {
            const importedState = JSON.parse(stateJson);
            set((state) => {
              Object.assign(state, importedState);
              state.lastUpdated = new Date();
            }, false, 'importState');
            return true;
          } catch (error) {
            console.error('Failed to import state:', error);
            return false;
          }
        },
        
        // Validation
        validateSelection: () => {
          const state = get();
          const errors: string[] = [];
          
          if (!state.selectedPlan) {
            errors.push('Nenhum plano selecionado');
          }
          
          // Validar limitações de add-ons
          state.selectedAddOns.forEach(addOn => {
            if (addOn.limitations?.maxQuantity && (addOn.quantity || 1) > addOn.limitations.maxQuantity) {
              errors.push(`Quantidade máxima excedida para ${addOn.name}`);
            }
            
            if (addOn.limitations?.requiresPlan && state.selectedPlan) {
              if (!addOn.limitations.requiresPlan.includes(state.selectedPlan.id)) {
                errors.push(`${addOn.name} não é compatível com o plano selecionado`);
              }
            }
          });
          
          return {
            isValid: errors.length === 0,
            errors
          };
        },
        
        canProceedToNext: () => {
          const state = get();
          const validation = get().validateSelection();
          
          // Verificar se pode prosseguir baseado no step atual
          switch (state.currentStep) {
            case 'plan-selection':
              return !!state.selectedPlan;
            case 'add-ons':
              return validation.isValid;
            case 'review':
              return validation.isValid && !!state.pricingSummary;
            default:
              return validation.isValid;
          }
        },
        
        getStepProgress: () => {
          const state = get();
          const completedSteps = state.steps.filter(s => s.completed).length;
          const totalSteps = state.steps.length;
          const currentIndex = state.steps.findIndex(s => s.current);
          
          return {
            current: currentIndex + 1,
            total: totalSteps,
            percentage: Math.round(((completedSteps + (currentIndex >= 0 ? 0.5 : 0)) / totalSteps) * 100)
          };
        },
        
        // Add-on helpers
        getAddOnTotal: () => {
          const state = get();
          return state.selectedAddOns.reduce((sum, addon) => sum + addon.totalPrice, 0);
        },
        
        hasAddOn: (addOnId) => {
          const state = get();
          return state.selectedAddOns.some(addon => addon.id === addOnId);
        },
        
        getAddOnQuantity: (addOnId) => {
          const state = get();
          const addOn = state.selectedAddOns.find(addon => addon.id === addOnId);
          return addOn?.quantity || 0;
        },
        
        getSelectedAddOnIds: () => {
          const state = get();
          return state.selectedAddOns.map(addon => addon.id);
        },
        
        getTotalSelectedAddOns: () => {
          const state = get();
          return state.selectedAddOns.length;
        },
        
        canAddMoreAddOns: (maxAddOns) => {
          const state = get();
          return state.selectedAddOns.length < maxAddOns;
        },
        
        setContactInfo: (contactInfo) => set((state) => {
          state.contactInfo = contactInfo;
          state.lastUpdated = new Date();
        }, false, 'setContactInfo')
      })),
      {
        name: 'upgrade-store',
        version: 2
      }
    ),
    {
      name: 'vindula-upgrade-state',
      version: 2,
      partialize: (state) => ({
        selectedPlan: state.selectedPlan,
        selectedAddOns: state.selectedAddOns,
        context: state.context,
        billingCycle: state.billingCycle,
        currentStep: state.currentStep,
        steps: state.steps,
        pricingSummary: state.pricingSummary,
        sessionId: state.sessionId,
        startedAt: state.startedAt,
        lastUpdated: state.lastUpdated
      })
    }
  )
);

// Hook utilitário para seleção de add-ons com integração ao store
export const useAddOnSelection = () => {
  const store = useUpgradeStore();
  
  return {
    selectedAddOns: store.selectedAddOns,
    selectedIds: store.getSelectedAddOnIds(),
    totalSelected: store.getTotalSelectedAddOns(),
    totalPrice: store.getAddOnTotal(),
    
    addAddOn: store.addAddOn,
    removeAddOn: store.removeAddOn,
    updateQuantity: store.updateAddOnQuantity,
    clearAll: store.clearAddOns,
    
    hasAddOn: store.hasAddOn,
    getQuantity: store.getAddOnQuantity,
    canAddMore: (maxAddOns: number) => store.canAddMoreAddOns(maxAddOns),
    
    handleToggle: (addOn: AddOn) => {
      if (store.hasAddOn(addOn.id)) {
        store.removeAddOn(addOn.id);
      } else {
        store.addAddOn(addOn);
      }
    }
  };
};

// Hook para contexto de upgrade
export const useUpgradeContext = () => {
  const store = useUpgradeStore();
  
  // Debug log
  console.log('[useUpgradeContext] billingCycle:', store.billingCycle);
  
  return {
    context: store.context,
    setContext: store.setContext,
    updateContext: store.updateContext,
    billingCycle: store.billingCycle,
    setBillingCycle: (cycle: 'monthly' | 'annual') => {
      console.log('[useUpgradeContext] setBillingCycle called with:', cycle);
      store.setBillingCycle(cycle);
    }
  };
};

// Hook para progresso e navegação
export const useUpgradeProgress = () => {
  const store = useUpgradeStore();
  
  return {
    currentStep: store.currentStep,
    steps: store.steps,
    progress: store.getStepProgress(),
    isProcessing: store.isProcessing,
    
    goToStep: store.goToStep,
    completeStep: store.completeStep,
    setCurrentStep: store.setCurrentStep,
    resetSteps: store.resetSteps,
    
    canProceed: store.canProceedToNext(),
    validation: store.validateSelection()
  };
};

// Hook para pricing summary
export const useUpgradePricing = () => {
  const store = useUpgradeStore();
  
  return {
    pricingSummary: store.pricingSummary,
    calculatePricing: store.calculatePricing,
    updatePricingSummary: store.updatePricingSummary,
    
    // Shortcuts para valores comuns
    total: store.pricingSummary?.total || 0,
    subtotal: store.pricingSummary?.subtotal || 0,
    planPrice: store.pricingSummary?.planPrice || 0,
    addOnsPrice: store.pricingSummary?.addOnsPrice || 0,
    billingCycle: store.billingCycle,
    
    // Formatação de preços
    formatPrice: (price: number) => {
      return `R$ ${price.toFixed(2).replace('.', ',')}`;
    },
    
    formatPriceWithCycle: (price: number) => {
      const formattedPrice = `R$ ${price.toFixed(2).replace('.', ',')}`;
      const cycle = store.billingCycle === 'annual' ? '/ano' : '/mês';
      console.log('[formatPriceWithCycle] billingCycle:', store.billingCycle, 'cycle:', cycle);
      return `${formattedPrice}${cycle}`;
    }
  };
};

export default useUpgradeStore; 