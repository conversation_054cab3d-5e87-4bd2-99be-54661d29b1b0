/**
 * Página principal de upgrade - rota unificada /upgrade
 * Gerencia todo o fluxo de upgrade baseado no contexto detectado
 * <AUTHOR> Internet 2025
 */
import React, { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { UpgradeLayout } from '@/components/upgrade/UpgradeLayout';
import { PlanSelectionStep } from '@/components/upgrade/steps/PlanSelectionStep';
import { AddonsStep } from '@/components/upgrade/steps/AddonsStep';
import { ContactInfoStep } from '@/components/upgrade/steps/ContactInfoStep';
import { ConfirmationStep } from '@/components/upgrade/steps/ConfirmationStep';
import { SuccessStep } from '@/components/upgrade/steps/SuccessStep';
import { AddonFocusedFlow } from '@/components/upgrade/flows/AddonFocusedFlow';
import { useUpgradeContext, useUpgradeProgress } from '@/stores/upgradeStore';
import { UpgradeSource } from '@/types/upgrade';
import { GenericPermissionGate } from '@/components/auth/GenericPermissionGate';
import { Card, CardContent } from '@/components/ui/card';
import { AlertTriangle } from 'lucide-react';

// Tipos de fluxo
type FlowType = 'addon-focused' | 'plan-focused' | 'generic';

/**
 * PADRÃO CRÍTICO: Mapeamento de UpgradeSource para tipos de fluxo
 * 
 * REGRA OBRIGATÓRIA: Todo novo UpgradeSource DEVE ser adicionado a uma das listas abaixo!
 * 
 * QUANDO IMPLEMENTAR NOVA FEATURE COM LIMITAÇÕES:
 * 1. Adicionar source ao enum UpgradeSource em /src/types/upgrade.ts
 * 2. Adicionar o source a uma das listas abaixo:
 *    - addonFocusedSources: Para limitações de recursos (storage, users, ai-credits)
 *    - planFocusedSources: Para limitações de funcionalidades (features, capabilities)
 * 3. Configurar contexto em UpgradeLayout.tsx (CONTEXT_CONFIGS)
 * 4. Configurar hero em HeroSection.tsx (CONTEXT_CONFIG)
 * 
 * CATEGORIZAÇÃO:
 * - addon-focused: Usuário precisa de mais recursos (GB, usuários, créditos)
 * - plan-focused: Usuário precisa de novas funcionalidades/capacidades
 * 
 * VALIDAÇÃO: Todos os sources do enum UpgradeSource devem estar em uma das listas!
 */
// Determinar tipo de fluxo baseado na origem
const determineFlowType = (source: UpgradeSource | null): FlowType => {
  if (!source) return 'generic';
  
  const addonFocusedSources: UpgradeSource[] = [
    'storage-full',
    'users-limit', 
    'ai-credits'
  ];
  
  const planFocusedSources: UpgradeSource[] = [
    'plan-management',
    'feed',
    'knowledge-hub',
    'people-directory',
    'chat-history',
    'transcription',
    'medals',
    'levels',
    'actions',
    'marketplace',
    'navegacaoaplicativomobile'
  ];
  
  if (addonFocusedSources.includes(source)) {
    return 'addon-focused';
  }
  
  if (planFocusedSources.includes(source)) {
    return 'plan-focused';
  }
  
  return 'generic';
};

// Componente de fluxo de planos (original)
const PlanFocusedFlow: React.FC = () => {
  const { currentStep, goToStep } = useUpgradeProgress();

  // Handlers para navegação
  const handleNext = () => {
    const stepOrder = ['plan-selection', 'add-ons', 'contact-info', 'confirmation', 'success'];
    const currentIndex = stepOrder.indexOf(currentStep);
    if (currentIndex < stepOrder.length - 1) {
      goToStep(stepOrder[currentIndex + 1]);
    }
  };

  const handleBack = () => {
    const stepOrder = ['plan-selection', 'add-ons', 'contact-info', 'confirmation', 'success'];
    const currentIndex = stepOrder.indexOf(currentStep);
    if (currentIndex > 0) {
      goToStep(stepOrder[currentIndex - 1]);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'plan-selection':
        return <PlanSelectionStep />;
      case 'add-ons':
        return <AddonsStep />;
      case 'contact-info':
        return <ContactInfoStep onNext={handleNext} onBack={handleBack} />;
      case 'confirmation':
        return <ConfirmationStep />;
      case 'success':
        return <SuccessStep />;
      default:
        return <PlanSelectionStep />;
    }
  };

  return (
    <UpgradeLayout>
      {renderCurrentStep()}
    </UpgradeLayout>
  );
};

// Componente principal
const UpgradePage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const { context } = useUpgradeContext();
  const source = searchParams.get('source') as UpgradeSource;
  const targetPlan = searchParams.get('plan') as 'pro' | 'max' | null;
  
  // Determinar tipo de fluxo
  const flowType = determineFlowType(source);

  // Track page view
  useEffect(() => {
    if (source) {
      console.log('Upgrade page view:', {
        source,
        targetPlan,
        flowType,
        timestamp: new Date()
      });
    }
  }, [source, targetPlan, flowType]);

  // Renderizar fluxo baseado no tipo
  const renderFlow = () => {
    switch (flowType) {
      case 'addon-focused':
        return <AddonFocusedFlow source={source} />;
      case 'plan-focused':
        return <PlanFocusedFlow />;
      default:
        return <PlanFocusedFlow />; // Fallback para fluxo original
    }
  };

  return renderFlow();
};

export default UpgradePage; 