/**
 * AdminMarketplace - Administração do marketplace estratégico
 * Página para gerenciar categorias e itens do marketplace
 * <AUTHOR> Internet 2025
 */
import { useState } from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Crown,
  Plus,
  Sparkles,
  Settings,
  TrendingUp,
  Users,
  Award,
  ShoppingBag,
  Gift,
  Edit3,
  Trash2,
  Eye,
  Calendar,
  Timer,
  Target,
  BarChart3,
  Bot,
  Wand2,
  RefreshCw
} from "lucide-react";
import { useAllStrategicCategories, useStrategicItems } from '@/lib/query/hooks/useStrategicMarketplace';
import { useSpecialOffers, useDeleteSpecialOffer, useToggleSpecialOffer } from '@/lib/query/hooks/useSpecialOffers';
import { useMarketplaceLimits } from '@/hooks/marketplace/useMarketplaceLimits';
import { HeroSection } from "@/components/common/HeroSection";
import { CategoryForm } from "@/components/marketplace/CategoryForm";
import { ItemForm } from "@/components/marketplace/ItemForm";
import { SpecialOfferForm } from "@/components/marketplace/SpecialOfferForm";
import { AIGenerateCategories } from "@/components/marketplace/AIGenerateCategories";
import { AIGenerateItems } from "@/components/marketplace/AIGenerateItems";
import { AIGenerateOffer } from "@/components/marketplace/AIGenerateOffer";
import { ItemViewModal } from "@/components/marketplace/ItemViewModal";
import { CategoryViewModal } from "@/components/marketplace/CategoryViewModal";
import { MainLayout } from "@/components/layout/MainLayout";
import { AdminLayout } from "@/components/layout/AdminLayout";
import { IconMapper } from "@/components/marketplace/IconMapper";
import { formatCurrency } from "@/lib/utils";
import { SmartUpgradeButton } from "@/components/ui/PlanUpgradeButton";
import { errorWithNotification } from "@/lib/notifications/toastWithNotification";

// Variantes de animação
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

export default function AdminMarketplace() {
  const [activeTab, setActiveTab] = useState("overview");
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [showItemForm, setShowItemForm] = useState(false);
  const [showOfferForm, setShowOfferForm] = useState(false);
  const [showAIGenerate, setShowAIGenerate] = useState(false);
  const [editingCategory, setEditingCategory] = useState<any>(null);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [editingOffer, setEditingOffer] = useState<any>(null);
  const [preselectedCategoryId, setPreselectedCategoryId] = useState<string | null>(null);
  const [viewingCategory, setViewingCategory] = useState<any>(null);
  const [viewingItem, setViewingItem] = useState<any>(null);
  const [showCategoryView, setShowCategoryView] = useState(false);
  const [showItemView, setShowItemView] = useState(false);
  
  // Buscar dados do marketplace (incluindo categorias inativas para admin)
  const { data: categories = [], isLoading: isLoadingCategories, refetch: refetchCategories, error: categoriesError } = useAllStrategicCategories();
  const { data: allItems = [], isLoading: isLoadingItems, refetch: refetchItems, error: itemsError } = useStrategicItems();
  const { data: specialOffers = [], isLoading: isLoadingOffers, refetch: refetchOffers } = useSpecialOffers();
  const deleteOfferMutation = useDeleteSpecialOffer();
  const toggleOfferMutation = useToggleSpecialOffer();
  
  // Hook de limitações por plano
  const { 
    limits, 
    isLoading: isLoadingLimits,
    canCreateCategory,
    canCreateSpecialOffer,
    getCategoriesLimitMessage,
    getOffersLimitMessage,
    getItemsPerCategoryLimitMessage,
    getCategoryItemsInfo
  } = useMarketplaceLimits();

  // Estatísticas calculadas
  const stats = {
    totalCategories: categories?.length || 0,
    totalItems: allItems?.length || 0,
    activeItems: allItems?.filter(item => item?.active)?.length || 0,
    totalOffers: specialOffers?.length || 0,
    activeOffers: specialOffers?.filter(offer => offer?.active)?.length || 0,
    totalPurchases: 0 // TODO: Implementar quando tivermos hook de purchases
  };

  // Função para refresh manual dos dados
  const handleRefreshData = () => {
    refetchCategories();
    refetchItems();
    refetchOffers();
  };

  // Handlers para formulários
  const handleCreateCategory = () => {
    if (!canCreateCategory()) {
      // Se não pode criar, não abre o formulário
      // O botão deve mostrar upgrade quando limite atingido
      return;
    }
    setEditingCategory(null);
    setShowCategoryForm(true);
  };

  const handleEditCategory = (category: any) => {
    setEditingCategory(category);
    setShowCategoryForm(true);
  };

  const handleCreateItem = async (categoryId?: string) => {
    // Se foi especificada uma categoria, validar limite de itens para essa categoria
    if (categoryId && limits?.canCreateItem) {
      const safeCategoryId = String(categoryId); // Garantir que é string
      const canCreate = await limits.canCreateItem(safeCategoryId);
      if (!canCreate) {
        // Se não pode criar, não abre o formulário
        return;
      }
    } else if (!categoryId) {
      // Se não foi especificada categoria, verificar se há categorias disponíveis
      if (categories.length === 0) {
        errorWithNotification("Nenhuma categoria disponível", {
          description: "Você precisa criar pelo menos uma categoria antes de adicionar itens.",
          persist: true
        });
        return;
      }
      
      // Verificar se pelo menos uma categoria pode receber itens
      if (limits?.canCreateItem && !limits.isUnlimited) {
        const categoriesWithSpace = await Promise.all(
          categories.map(async (category) => {
            const categoryId = String(category.id); // Garantir que é string
            const canCreate = await limits.canCreateItem(categoryId);
            return canCreate;
          })
        );
        
        const hasAvailableCategory = categoriesWithSpace.some(canCreate => canCreate);
        
        if (!hasAvailableCategory) {
          errorWithNotification("Limite de itens atingido", {
            description: `Todas as categorias atingiram o limite de ${limits.maxItemsPerCategory} itens. Upgrade seu plano para adicionar mais itens.`,
            persist: true
          });
          return;
        }
      }
    }
    setEditingItem(null);
    setPreselectedCategoryId(categoryId || null);
    setShowItemForm(true);
  };

  const handleEditItem = (item: any) => {
    setEditingItem(item);
    setShowItemForm(true);
  };

  const handleViewCategory = (category: any) => {
    setViewingCategory(category);
    setShowCategoryView(true);
  };

  const handleViewItem = (item: any) => {
    setViewingItem(item);
    setShowItemView(true);
  };

  // Handlers para ofertas especiais
  const handleCreateOffer = () => {
    if (!canCreateSpecialOffer()) {
      // Se não pode criar, não abre o formulário
      return;
    }
    setEditingOffer(null);
    setShowOfferForm(true);
  };

  const handleEditOffer = (offer: any) => {
    setEditingOffer(offer);
    setShowOfferForm(true);
  };

  const handleDeleteOffer = async (offerId: string) => {
    if (confirm('Tem certeza que deseja excluir esta oferta especial?')) {
      await deleteOfferMutation.mutateAsync(offerId);
    }
  };

  const handleToggleOffer = async (offerId: string, active: boolean) => {
    await toggleOfferMutation.mutateAsync({ id: offerId, active });
  };

  const handleFormSuccess = () => {
    // Os formulários já invalidam as queries automaticamente, mas vamos forçar refresh
    handleRefreshData();
    setShowCategoryForm(false);
    setShowItemForm(false);
    setShowOfferForm(false);
    setShowAIGenerate(false);
    setEditingCategory(null);
    setEditingItem(null);
    setEditingOffer(null);
    setPreselectedCategoryId(null);
  };

  return (
    <MainLayout>
    <AdminLayout>
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      <div className="container py-1 px-4 space-y-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Hero Section */}
          <HeroSection
            title="Marketplace Estratégico"
            description="Gerencie categorias e itens do sistema de benefícios corporativos"
            icon={Crown}
            gradientColors="from-purple-600 via-pink-600 to-indigo-600"
            iconAnimation={true}
            actions={
              <Button 
                onClick={handleRefreshData}
                className="bg-white/15 hover:bg-white/25 text-white border-white/30 backdrop-blur-sm"
                size="lg"
                disabled={isLoadingCategories || isLoadingItems}
              >
                <RefreshCw className={`h-5 w-5 mr-2 ${(isLoadingCategories || isLoadingItems) ? 'animate-spin' : ''}`} />
                Atualizar
              </Button>
            }
          />

          {/* Estatísticas em Cards */}
          <motion.div variants={cardVariants} className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <Card className="border-0 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 text-white">
                    <Target className="h-6 w-6" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-600">
                      {stats.totalCategories}
                      {limits && !limits.isUnlimited && (
                        <span className="text-sm text-muted-foreground">/{limits.maxCategories}</span>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Categorias
                      {limits && !limits.isUnlimited && (
                        <span className="ml-1">
                          ({limits.remainingCategorySlots || 0} restantes)
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                    <Gift className="h-6 w-6" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-600">{stats.totalItems}</div>
                    <div className="text-sm text-muted-foreground">
                      Itens totais
                      {limits && limits.maxItemsPerCategory !== -1 && (
                        <span className="block text-xs mt-1">
                          Máx. {limits.maxItemsPerCategory}/categoria
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 text-white">
                    <Award className="h-6 w-6" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">{stats.activeItems}</div>
                    <div className="text-sm text-muted-foreground">Itens ativos</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 bg-gradient-to-br from-orange-500/10 to-red-500/10 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-xl bg-gradient-to-r from-orange-500 to-red-500 text-white">
                    <Timer className="h-6 w-6" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-orange-600">
                      {stats.activeOffers}
                      {limits && !limits.isUnlimited && (
                        <span className="text-sm text-muted-foreground">/{limits.maxSpecialOffers}</span>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Ofertas ativas
                      {limits && !limits.isUnlimited && (
                        <span className="ml-1">
                          ({limits.remainingOfferSlots || 0} restantes)
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Tabs Principal */}
          <motion.div variants={cardVariants}>
            <Card className="border-0 bg-white/80 backdrop-blur-sm">
              <CardContent className="p-6">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="overview" className="flex items-center gap-2">
                      <BarChart3 className="h-4 w-4" />
                      Visão Geral
                    </TabsTrigger>
                    <TabsTrigger value="categories" className="flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      Categorias
                    </TabsTrigger>
                    <TabsTrigger value="items" className="flex items-center gap-2">
                      <Gift className="h-4 w-4" />
                      Itens
                    </TabsTrigger>
                    <TabsTrigger value="offers" className="flex items-center gap-2">
                      <Timer className="h-4 w-4" />
                      Ofertas Especiais
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="space-y-6">
                    {/* Card de informações do plano */}
                    {limits && (
                      <Card className="border-0 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 backdrop-blur-sm">
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <div className="p-3 rounded-xl bg-gradient-to-r from-indigo-500 to-purple-500 text-white">
                                <Crown className="h-6 w-6" />
                              </div>
                              <div>
                                <div className="text-lg font-semibold">Plano {limits.currentPlan}</div>
                                <div className="text-sm text-muted-foreground">
                                  {limits.isUnlimited ? (
                                    "Recursos ilimitados do marketplace"
                                  ) : (
                                    "Limitações ativas para marketplace"
                                  )}
                                </div>
                              </div>
                            </div>
                            <div className="text-right space-y-1">
                              {!limits.isUnlimited && (
                                <>
                                  <div className="text-xs text-muted-foreground">
                                    {getCategoriesLimitMessage()}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {getItemsPerCategoryLimitMessage()}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {getOffersLimitMessage()}
                                  </div>
                                </>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Resumo Executivo */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                      {/* Status Geral */}
                      <Card className="lg:col-span-2">
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <BarChart3 className="h-5 w-5 text-blue-600" />
                            Status do Marketplace
                          </CardTitle>
                          <CardDescription>
                            Visão geral do desempenho e configuração atual
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">Categorias Ativas</span>
                                <span className="text-sm text-muted-foreground">{stats.totalCategories}</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div 
                                  className="bg-blue-600 h-2 rounded-full transition-all duration-500" 
                                  style={{ width: `${Math.min((stats.totalCategories / 10) * 100, 100)}%` }}
                                ></div>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">Taxa de Ativação</span>
                                <span className="text-sm text-muted-foreground">
                                  {stats.totalItems > 0 ? Math.round((stats.activeItems / stats.totalItems) * 100) : 0}%
                                </span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div 
                                  className="bg-green-600 h-2 rounded-full transition-all duration-500" 
                                  style={{ 
                                    width: `${stats.totalItems > 0 ? (stats.activeItems / stats.totalItems) * 100 : 0}%` 
                                  }}
                                ></div>
                              </div>
                            </div>
                          </div>
                          
                          <div className="pt-4 border-t">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium">Ofertas Especiais</span>
                              <Badge variant={stats.activeOffers > 0 ? "default" : "secondary"}>
                                {stats.activeOffers > 0 ? `${stats.activeOffers} ativa${stats.activeOffers > 1 ? 's' : ''}` : 'Nenhuma ativa'}
                              </Badge>
                            </div>
                            {stats.activeOffers > 0 && (
                              <p className="text-xs text-muted-foreground">
                                Você tem ofertas especiais ativas que podem impulsionar o engajamento
                              </p>
                            )}
                          </div>
                        </CardContent>
                      </Card>

                      {/* Ações Rápidas */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Settings className="h-5 w-5 text-purple-600" />
                            Ações Rápidas
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <Button 
                            onClick={() => setActiveTab("categories")}
                            variant="outline" 
                            className="w-full justify-start"
                          >
                            <Target className="h-4 w-4 mr-2" />
                            Gerenciar Categorias
                          </Button>
                          <Button 
                            onClick={() => setActiveTab("items")}
                            variant="outline" 
                            className="w-full justify-start"
                          >
                            <Gift className="h-4 w-4 mr-2" />
                            Gerenciar Itens
                          </Button>
                          <Button 
                            onClick={() => setActiveTab("offers")}
                            variant="outline" 
                            className="w-full justify-start"
                          >
                            <Timer className="h-4 w-4 mr-2" />
                            Ofertas Especiais
                          </Button>
                          <div className="pt-2 border-t">
                            {limits?.isPremiumFeaturesEnabled ? (
                              <Button 
                                onClick={() => setShowAIGenerate(true)}
                                className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white"
                              >
                                <Wand2 className="h-4 w-4 mr-2" />
                                Gerar com IA
                              </Button>
                            ) : (
                              <SmartUpgradeButton
                                currentPlan={limits?.currentPlan as 'Grátis' | 'Pro' | 'Max'}
                                source="marketplace"
                                className="w-full"
                              >
                                <Bot className="h-4 w-4 mr-2" />
                                Upgrade para IA
                              </SmartUpgradeButton>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Categorias Recentes */}
                    <Card>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="flex items-center gap-2">
                              <Target className="h-5 w-5 text-purple-600" />
                              Categorias do Marketplace
                            </CardTitle>
                            <CardDescription>
                              {categories.length > 0 ? `${categories.length} categoria${categories.length > 1 ? 's' : ''} configurada${categories.length > 1 ? 's' : ''}` : 'Nenhuma categoria configurada'}
                            </CardDescription>
                          </div>
                          <Button 
                            onClick={() => setActiveTab("categories")}
                            variant="outline"
                            size="sm"
                          >
                            Ver todas
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {categories.length === 0 ? (
                          <div className="text-center py-8">
                            <Target className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                            <p className="text-muted-foreground mb-4">
                              Nenhuma categoria criada ainda
                            </p>
                            {canCreateCategory() ? (
                              <Button 
                                onClick={handleCreateCategory}
                                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
                              >
                                <Plus className="h-4 w-4 mr-2" />
                                Criar primeira categoria
                              </Button>
                            ) : (
                              <SmartUpgradeButton
                                currentPlan={limits?.currentPlan as 'Grátis' | 'Pro' | 'Max'}
                                source="marketplace"
                              >
                                <Crown className="h-4 w-4 mr-2" />
                                Upgrade para criar categorias
                              </SmartUpgradeButton>
                            )}
                          </div>
                        ) : (
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {categories.slice(0, 6).map((category) => (
                              <div 
                                key={category.id} 
                                className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer"
                                onClick={() => handleViewCategory(category)}
                              >
                                <div className="flex items-center gap-3 mb-2">
                                  <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500/10 to-pink-500/10">
                                    <IconMapper iconName={category.icon} className="h-5 w-5 text-purple-600" />
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <h4 className="font-medium truncate">{category.name}</h4>
                                    <p className="text-xs text-muted-foreground">
                                      {allItems.filter(item => item.category_id === category.id).length} itens
                                    </p>
                                  </div>
                                </div>
                                <Badge 
                                  variant={category.active ? "default" : "secondary"}
                                  className="text-xs"
                                >
                                  {category.active ? 'Ativa' : 'Inativa'}
                                </Badge>
                              </div>
                            ))}
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Itens em Destaque */}
                    <Card>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="flex items-center gap-2">
                              <Gift className="h-5 w-5 text-green-600" />
                              Itens em Destaque
                            </CardTitle>
                            <CardDescription>
                              {allItems.length > 0 ? `${allItems.length} item${allItems.length > 1 ? 's' : ''} no marketplace` : 'Nenhum item cadastrado'}
                            </CardDescription>
                          </div>
                          <Button 
                            onClick={() => setActiveTab("items")}
                            variant="outline"
                            size="sm"
                          >
                            Ver todos
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {allItems.length === 0 ? (
                          <div className="text-center py-8">
                            <Gift className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                            <p className="text-muted-foreground mb-4">
                              Nenhum item cadastrado ainda
                            </p>
                            <Button 
                              onClick={handleCreateItem}
                              className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Criar primeiro item
                            </Button>
                          </div>
                        ) : (
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            {allItems.slice(0, 8).map((item) => {
                              const category = categories.find(cat => cat.id === item.category_id);
                              return (
                                <div 
                                  key={item.id} 
                                  className="p-3 border rounded-lg hover:shadow-md transition-shadow cursor-pointer"
                                  onClick={() => handleViewItem(item)}
                                >
                                  <div className="flex items-start justify-between mb-2">
                                    <div className="flex-1 min-w-0">
                                      <h4 className="font-medium text-sm truncate">{item.name}</h4>
                                      <p className="text-xs text-muted-foreground">
                                        {category?.name || 'Sem categoria'}
                                      </p>
                                    </div>
                                    <Badge 
                                      variant={item.active ? "default" : "secondary"}
                                      className="text-xs ml-2"
                                    >
                                      {item.active ? 'Ativo' : 'Inativo'}
                                    </Badge>
                                  </div>
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm font-semibold text-green-600">
                                      {formatCurrency(item.price)}
                                    </span>
                                    <span className="text-xs text-muted-foreground">
                                      Nível {item.level_required}
                                    </span>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="categories" className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold">Categorias Estratégicas</h3>
                        <p className="text-sm text-muted-foreground">
                          Configure as categorias que organizam os benefícios do marketplace
                        </p>
                        {categoriesError && (
                          <p className="text-sm text-red-600 mt-1">
                            Erro ao carregar categorias: {categoriesError.message}
                          </p>
                        )}
                      </div>
                      <div className="flex gap-2">
                        {limits?.isPremiumFeaturesEnabled ? (
                          <Button 
                            onClick={() => setShowAIGenerate(true)}
                            className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white"
                          >
                            <Wand2 className="h-4 w-4 mr-2" />
                            IA
                          </Button>
                        ) : (
                          <SmartUpgradeButton
                            currentPlan={limits?.currentPlan as 'Grátis' | 'Pro' | 'Max'}
                            source="marketplace"
                            size="sm"
                          >
                            <Bot className="h-4 w-4 mr-2" />
                            Upgrade para IA
                          </SmartUpgradeButton>
                        )}
                        {canCreateCategory() ? (
                          <Button 
                            onClick={handleCreateCategory}
                            className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Nova Categoria
                          </Button>
                        ) : (
                          <SmartUpgradeButton
                            currentPlan={limits?.currentPlan as 'Grátis' | 'Pro' | 'Max'}
                            source="marketplace"
                            size="sm"
                          >
                            <Crown className="h-4 w-4 mr-2" />
                            Upgrade para criar categorias
                          </SmartUpgradeButton>
                        )}
                      </div>
                    </div>

                    {isLoadingCategories ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {[...Array(6)].map((_, i) => (
                          <div key={i} className="h-32 bg-muted animate-pulse rounded-lg"></div>
                        ))}
                      </div>
                    ) : categories.length === 0 ? (
                      <div className="text-center py-16">
                        <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <h4 className="text-lg font-semibold text-gray-800 mb-2">
                          Nenhuma categoria criada
                        </h4>
                        <p className="text-muted-foreground mb-4">
                          Crie sua primeira categoria para começar a organizar o marketplace
                        </p>
                        <div className="flex gap-3">
                          {limits?.isPremiumFeaturesEnabled ? (
                            <Button 
                              onClick={() => setShowAIGenerate(true)}
                              className="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 hover:from-yellow-500 hover:via-orange-600 hover:to-red-600 text-white"
                            >
                              <Wand2 className="h-4 w-4 mr-2" />
                              Gerar com IA
                              <Sparkles className="h-4 w-4 ml-2" />
                            </Button>
                          ) : (
                            <SmartUpgradeButton
                              currentPlan={limits?.currentPlan as 'Grátis' | 'Pro' | 'Max'}
                              source="marketplace"
                            >
                              <Bot className="h-4 w-4 mr-2" />
                              Upgrade para IA
                              <Sparkles className="h-4 w-4 ml-2" />
                            </SmartUpgradeButton>
                          )}
                          {canCreateCategory() ? (
                            <Button 
                              onClick={handleCreateCategory}
                              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Criar manualmente
                            </Button>
                          ) : (
                            <SmartUpgradeButton
                              currentPlan={limits?.currentPlan as 'Grátis' | 'Pro' | 'Max'}
                              source="marketplace"
                            >
                              <Crown className="h-4 w-4 mr-2" />
                              Upgrade para criar categorias
                            </SmartUpgradeButton>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {categories.map((category) => (
                          <Card key={category.id} className="hover:shadow-md transition-shadow">
                            <CardHeader className="pb-3">
                              <div className="flex items-center justify-between">
                                <div className={`p-2 rounded-lg bg-gradient-to-r ${category.gradient}`}>
                                  <IconMapper 
                                    iconName={category.icon} 
                                    className="h-5 w-5 text-white" 
                                  />
                                </div>
                                <div className="flex gap-1">
                                  <Button 
                                    variant="ghost" 
                                    size="sm" 
                                    onClick={() => handleViewCategory(category)}
                                    title="Visualizar categoria"
                                  >
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                  <Button 
                                    variant="ghost" 
                                    size="sm"
                                    onClick={() => handleEditCategory(category)}
                                    title="Editar categoria"
                                  >
                                    <Edit3 className="h-4 w-4" />
                                  </Button>
                                  <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700" title="Excluir categoria">
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                              <CardTitle className="text-lg">{category.name}</CardTitle>
                              <CardDescription>{category.description}</CardDescription>
                            </CardHeader>
                            <CardContent>
                              <div className="flex items-center justify-between">
                                <Badge variant="outline">
                                  {category.items_count || 0} itens
                                </Badge>
                                <Badge variant={category.active ? "default" : "secondary"}>
                                  {category.active ? "Ativo" : "Inativo"}
                                </Badge>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="items" className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold">Itens do Marketplace</h3>
                        <p className="text-sm text-muted-foreground">
                          Gerencie os benefícios disponíveis organizados por categoria
                        </p>
                      </div>
                      <div className="flex gap-2">
                        {limits?.isPremiumFeaturesEnabled && (
                          <AIGenerateItems onItemsGenerated={refetchItems} />
                        )}
                        <Button 
                          onClick={() => window.location.reload()}
                          variant="outline"
                          className="gap-2"
                        >
                          <RefreshCw className="h-4 w-4" />
                          Atualizar
                        </Button>
                      </div>
                    </div>

                    {isLoadingItems || isLoadingCategories ? (
                      <div className="space-y-4">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="h-32 bg-muted animate-pulse rounded-lg"></div>
                        ))}
                      </div>
                    ) : categories.length === 0 ? (
                      <div className="text-center py-16">
                        <ShoppingBag className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <h4 className="text-lg font-semibold text-gray-800 mb-2">
                          Nenhuma categoria criada
                        </h4>
                        <p className="text-muted-foreground mb-4">
                          Crie primeiro uma categoria antes de adicionar itens
                        </p>
                        <Button 
                          onClick={() => setActiveTab("categories")}
                          className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Criar primeira categoria
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-6">
                        {categories.map((category) => {
                          const categoryItems = allItems.filter(item => item.category_id === category.id);
                          const currentItemsCount = categoryItems.length;
                          const maxItemsPerCategory = limits?.maxItemsPerCategory || 5;
                          const isUnlimited = maxItemsPerCategory === -1;
                          const canCreateInCategory = isUnlimited || currentItemsCount < maxItemsPerCategory;
                          const remainingSlots = isUnlimited ? null : Math.max(0, maxItemsPerCategory - currentItemsCount);
                          
                          return (
                            <Card key={category.id} className="overflow-hidden">
                              <CardHeader className={`p-4 bg-gradient-to-r ${category.gradient}`}>
                                <div className="flex items-center justify-between text-white">
                                  <div className="flex items-center gap-3">
                                    <div className="p-2 rounded-lg bg-white/20">
                                      <IconMapper 
                                        iconName={category.icon}
                                        className="h-5 w-5"
                                      />
                                    </div>
                                    <div>
                                      <h4 className="font-semibold">{category.name}</h4>
                                      <div className="flex items-center gap-2">
                                        <p className="text-sm text-white/80">
                                          {isUnlimited 
                                            ? `${currentItemsCount} itens` 
                                            : `${currentItemsCount}/${maxItemsPerCategory} itens`
                                          }
                                        </p>
                                        {!isUnlimited && (
                                          <Badge 
                                            variant={canCreateInCategory ? "secondary" : "destructive"}
                                            className={`text-xs ${canCreateInCategory 
                                              ? "bg-green-500/20 text-green-100 border-green-400/30" 
                                              : "bg-red-500/20 text-red-100 border-red-400/30"
                                            }`}
                                          >
                                            {remainingSlots === 0 ? "Limite atingido" : `${remainingSlots} restantes`}
                                          </Badge>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                  {canCreateInCategory ? (
                                    <Button
                                      onClick={() => handleCreateItem(category.id)}
                                      size="sm"
                                      className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                                    >
                                      <Plus className="h-4 w-4 mr-2" />
                                      Novo Item
                                    </Button>
                                  ) : (
                                    <div className="flex gap-2">
                                      <Button
                                        disabled
                                        size="sm"
                                        className="bg-white/10 text-white/50 border-white/20 cursor-not-allowed"
                                      >
                                        <Plus className="h-4 w-4 mr-2" />
                                        Limite atingido
                                      </Button>
                                      <SmartUpgradeButton
                                        currentPlan={limits?.currentPlan as 'Grátis' | 'Pro' | 'Max'}
                                        source="marketplace"
                                        size="sm"
                                        className="bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-100 border-yellow-400/30"
                                      >
                                        <Crown className="h-3 w-3 mr-1" />
                                        Upgrade
                                      </SmartUpgradeButton>
                                    </div>
                                  )}
                                </div>
                              </CardHeader>
                              <CardContent className="p-0">
                                {categoryItems.length === 0 ? (
                                  <div className="p-8 text-center text-muted-foreground">
                                    <Gift className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                    <p className="text-sm">Nenhum item nesta categoria</p>
                                    {canCreateInCategory ? (
                                      <p className="text-xs mt-1">Clique em "Novo Item" para adicionar o primeiro</p>
                                    ) : (
                                      <div className="mt-2">
                                        <p className="text-xs text-orange-600">Limite de {maxItemsPerCategory} itens por categoria atingido</p>
                                        <p className="text-xs mt-1">Faça upgrade para adicionar mais itens</p>
                                      </div>
                                    )}
                                  </div>
                                ) : (
                                  <div className="space-y-0">
                                    {categoryItems.map((item, index) => (
                                      <div 
                                        key={item.id} 
                                        className={`p-4 ${index !== categoryItems.length - 1 ? 'border-b border-border' : ''} hover:bg-muted/50 transition-colors`}
                                      >
                                        <div className="flex items-center justify-between">
                                          <div className="flex items-center gap-4">
                                            <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500">
                                              <Gift className="h-4 w-4 text-white" />
                                            </div>
                                            <div>
                                              <h5 className="font-medium">{item.name}</h5>
                                              <p className="text-sm text-muted-foreground">{item.description}</p>
                                              <div className="flex items-center gap-2 mt-1">
                                                <Badge variant="outline">{item.price} Stardust</Badge>
                                                <Badge variant="outline">Nível {item.level_required}</Badge>
                                                {item.is_popular && (
                                                  <Badge className="bg-orange-100 text-orange-700">Popular</Badge>
                                                )}
                                                {item.is_hot && (
                                                  <Badge className="bg-red-100 text-red-700">Hot</Badge>
                                                )}
                                              </div>
                                            </div>
                                          </div>
                                          <div className="flex items-center gap-2">
                                            <Badge variant={item.active ? "default" : "secondary"}>
                                              {item.active ? "Ativo" : "Inativo"}
                                            </Badge>
                                            <div className="flex gap-1">
                                              <Button 
                                                variant="ghost" 
                                                size="sm" 
                                                onClick={() => handleViewItem(item)}
                                                title="Visualizar item"
                                              >
                                                <Eye className="h-4 w-4" />
                                              </Button>
                                              <Button 
                                                variant="ghost" 
                                                size="sm"
                                                onClick={() => handleEditItem(item)}
                                                title="Editar item"
                                              >
                                                <Edit3 className="h-4 w-4" />
                                              </Button>
                                              <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700" title="Excluir item">
                                                <Trash2 className="h-4 w-4" />
                                              </Button>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </CardContent>
                            </Card>
                          );
                        })}
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="offers" className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold">Ofertas Especiais</h3>
                        <p className="text-sm text-muted-foreground">
                          Crie ofertas limitadas no tempo para impulsionar o engajamento
                        </p>
                      </div>
                      <div className="flex gap-3">
                        {limits?.isPremiumFeaturesEnabled ? (
                          <AIGenerateOffer onOfferGenerated={handleFormSuccess} />
                        ) : (
                          <SmartUpgradeButton
                            currentPlan={limits?.currentPlan as 'Grátis' | 'Pro' | 'Max'}
                            source="marketplace"
                            size="sm"
                          >
                            <Bot className="h-4 w-4 mr-2" />
                            Upgrade para IA
                          </SmartUpgradeButton>
                        )}
                        {canCreateSpecialOffer() ? (
                          <Button 
                            onClick={handleCreateOffer}
                            className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Nova Oferta
                          </Button>
                        ) : (
                          <SmartUpgradeButton
                            currentPlan={limits?.currentPlan as 'Grátis' | 'Pro' | 'Max'}
                            source="marketplace"
                            size="sm"
                          >
                            <Crown className="h-4 w-4 mr-2" />
                            Upgrade para criar ofertas
                          </SmartUpgradeButton>
                        )}
                      </div>
                    </div>

                    {isLoadingOffers ? (
                      <div className="space-y-4">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="h-32 bg-muted animate-pulse rounded-lg"></div>
                        ))}
                      </div>
                    ) : specialOffers.length === 0 ? (
                      <div className="text-center py-16">
                        <Timer className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <h4 className="text-lg font-semibold text-gray-800 mb-2">
                          Nenhuma oferta especial criada
                        </h4>
                        <p className="text-muted-foreground mb-4">
                          Crie ofertas limitadas no tempo para impulsionar o engajamento no marketplace
                        </p>
                        <div className="flex justify-center gap-3">
                          {limits?.isPremiumFeaturesEnabled ? (
                            <AIGenerateOffer onOfferGenerated={handleFormSuccess} />
                          ) : (
                            <SmartUpgradeButton
                              currentPlan={limits?.currentPlan as 'Grátis' | 'Pro' | 'Max'}
                              source="marketplace"
                            >
                              <Bot className="h-4 w-4 mr-2" />
                              Upgrade para IA
                              <Sparkles className="h-4 w-4 ml-2" />
                            </SmartUpgradeButton>
                          )}
                          {canCreateSpecialOffer() ? (
                            <Button 
                              onClick={handleCreateOffer}
                              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Criar primeira oferta
                            </Button>
                          ) : (
                            <SmartUpgradeButton
                              currentPlan={limits?.currentPlan as 'Grátis' | 'Pro' | 'Max'}
                              source="marketplace"
                            >
                              <Crown className="h-4 w-4 mr-2" />
                              Upgrade para criar ofertas
                            </SmartUpgradeButton>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {specialOffers.map((offer) => {
                          const isActive = offer.active && new Date(offer.end_date) > new Date();
                          const isExpired = new Date(offer.end_date) < new Date();
                          const daysLeft = Math.ceil((new Date(offer.end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
                          
                          return (
                            <Card key={offer.id} className="hover:shadow-md transition-shadow">
                              <CardContent className="p-6">
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-3 mb-3">
                                      <div className={`p-2 rounded-lg bg-gradient-to-r ${offer.gradient || 'from-yellow-400 via-orange-500 to-red-600'}`}>
                                        <Timer className="h-5 w-5 text-white" />
                                      </div>
                                      <div>
                                        <h4 className="text-lg font-semibold">{offer.title}</h4>
                                        <p className="text-sm text-muted-foreground">{offer.description}</p>
                                      </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                                      <div>
                                        <Label className="text-xs font-medium text-muted-foreground">DESCONTO</Label>
                                        <p className="text-2xl font-bold text-green-600">{offer.discount_percent}%</p>
                                      </div>
                                      <div>
                                        <Label className="text-xs font-medium text-muted-foreground">ITENS</Label>
                                        <p className="text-lg font-semibold">{offer.items_count || 0}</p>
                                      </div>
                                      <div>
                                        <Label className="text-xs font-medium text-muted-foreground">ECONOMIA TOTAL</Label>
                                        <p className="text-lg font-semibold text-blue-600">
                                          {formatCurrency(offer.total_savings || 0)}
                                        </p>
                                      </div>
                                      <div>
                                        <Label className="text-xs font-medium text-muted-foreground">STATUS</Label>
                                        <div className="flex items-center gap-2">
                                          {isExpired ? (
                                            <Badge variant="secondary" className="bg-gray-100 text-gray-600">
                                              Expirada
                                            </Badge>
                                          ) : isActive ? (
                                            <Badge className="bg-green-100 text-green-700">
                                              Ativa ({daysLeft}d)
                                            </Badge>
                                          ) : (
                                            <Badge variant="secondary">
                                              Inativa
                                            </Badge>
                                          )}
                                        </div>
                                      </div>
                                    </div>

                                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                      <div className="flex items-center gap-1">
                                        <Calendar className="h-4 w-4" />
                                        {new Date(offer.start_date).toLocaleDateString('pt-BR')} - {new Date(offer.end_date).toLocaleDateString('pt-BR')}
                                      </div>
                                      {offer.max_uses && (
                                        <div className="flex items-center gap-1">
                                          <Users className="h-4 w-4" />
                                          {offer.current_uses || 0}/{offer.max_uses} usos
                                        </div>
                                      )}
                                    </div>
                                  </div>

                                  <div className="flex items-center gap-2">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleToggleOffer(offer.id, !offer.active)}
                                      disabled={toggleOfferMutation.isPending}
                                    >
                                      {offer.active ? 'Desativar' : 'Ativar'}
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleEditOffer(offer)}
                                      title="Editar oferta"
                                    >
                                      <Edit3 className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleDeleteOffer(offer.id)}
                                      disabled={deleteOfferMutation.isPending}
                                      className="text-red-600 hover:text-red-700"
                                      title="Excluir oferta"
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          );
                        })}
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </div>

      {/* Formulários */}
      <CategoryForm
        open={showCategoryForm}
        onOpenChange={setShowCategoryForm}
        category={editingCategory}
        onSuccess={handleFormSuccess}
      />

      <ItemForm
        open={showItemForm}
        onOpenChange={setShowItemForm}
        item={editingItem}
        preselectedCategoryId={preselectedCategoryId}
        onSuccess={handleFormSuccess}
      />

      <SpecialOfferForm
        open={showOfferForm}
        onOpenChange={setShowOfferForm}
        offer={editingOffer}
        onSuccess={handleFormSuccess}
      />

      <AIGenerateCategories
        open={showAIGenerate}
        onOpenChange={setShowAIGenerate}
        onSuccess={handleFormSuccess}
      />

      <CategoryViewModal
        open={showCategoryView}
        onOpenChange={setShowCategoryView}
        category={viewingCategory}
        onEdit={(category) => {
          setShowCategoryView(false);
          handleEditCategory(category);
        }}
      />

      <ItemViewModal
        open={showItemView}
        onOpenChange={setShowItemView}
        item={viewingItem}
        category={categories.find(cat => cat.id === viewingItem?.category_id)}
        onEdit={(item) => {
          setShowItemView(false);
          handleEditItem(item);
        }}
      />
    </div>
    </AdminLayout>
    </MainLayout>
  );
}