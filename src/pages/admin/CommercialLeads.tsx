/**
 * CRM de Leads Comerciais - Sistema de Upgrade Unificado
 * Dashboard para equipe comercial gerenciar leads que solicitaram upgrade
 * <AUTHOR> Internet 2025
 */
import React, { useState, useMemo, useCallback } from "react";
import { useQueryClient } from '@tanstack/react-query';
import { QueryKeys } from '@/lib/query/queryKeys';
import { useApproveAddonRequest, useGetAddonApprovalDetails, useActivatePlanSubscription } from "@/lib/query/hooks/useCommercialAddonApproval";
import { ApprovalStatusBadge } from "@/components/commercial/ApprovalStatusBadge";
import { AdminLayout } from "@/components/layout/AdminLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";
import { HeroSection } from "@/components/common/HeroSection";
import { GenericPermissionGate } from "@/components/auth/GenericPermissionGate";
import { RefreshButton } from "@/components/ui/RefreshButton";
import { 
  useCommercialLeads,
  useUpdateCommercialLead,
  useCommercialLeadStats,
  useCommercialLeadHistory
} from "@/lib/query/hooks/useCommercialLeads";
import { useResendCommercialEmail } from "@/lib/query/hooks/useResendCommercialEmail";
import { successWithNotification, errorWithNotification } from "@/lib/notifications/toastWithNotification";
import type { CommercialLead, CommercialLeadFilters } from "@/types/commercial-leads.types";
import { 
  Users, 
  Crown, 
  TrendingUp, 
  Calendar, 
  DollarSign,
  Phone,
  Mail,
  MessageSquare,
  CheckCircle,
  AlertTriangle,
  Clock,
  Eye,
  Edit,
  Filter,
  Search,
  Download,
  BarChart3,
  FileText,
  Settings,
  Zap,
  Brain,
  HardDrive,
  User,
  Briefcase,
  Gift,
  Target,
  ChevronRight,
  ExternalLink,
  ArrowRight,
  ArrowDown,
  Plus,
  HelpCircle,
  VisuallyHidden,
  Loader2,
  Star,
  XCircle,
  AlertCircle,
  Timer,
  Activity,
  Calculator,
  PiggyBank,
  Info,
  Trash2,
  Check,
  X,
  Send,
  Shield,
  ShieldCheck
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { MainLayout } from "@/components/layout/MainLayout";
import { useIsVindulaCompany } from "@/lib/query/hooks/useIsVindulaCompany";
import { useAuthStore } from "@/stores/authStore";

// Interface local para filtros simplificados da UI
interface LeadFilters {
  search: string;
  status: string;
  source: string;
  courtesyStatus: string;
  dateRange: string;
  requestType: string;
}

export default function CommercialLeads() {
  // 🔒 PROTEÇÃO DUPLA: Frontend + Backend RLS
  const { isVindulaCompany, isCheckingCompany } = useIsVindulaCompany();
  const queryClient = useQueryClient();
  
  // Proteção adicional: verificar company_id diretamente
  const company_id = useAuthStore((state) => state.company_id);
  
  // Validação em múltiplas camadas (mais difícil de burlar)
  const hasVindulaAccess = isVindulaCompany && company_id;
  
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedLead, setSelectedLead] = useState<CommercialLead | null>(null);
  const [showLeadDetail, setShowLeadDetail] = useState(false);
  const [showUpdateDialog, setShowUpdateDialog] = useState(false);
  
  // Estados para aprovação de add-ons
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);
  const [approvalAction, setApprovalAction] = useState<'approve' | 'reject'>('approve');
  const [approvalNotes, setApprovalNotes] = useState('');
  
  // Estados para modal de ativação de plano
  const [showActivationDialog, setShowActivationDialog] = useState(false);
  const [activationNotes, setActivationNotes] = useState('');
  
  // Estado para rastrear qual lead está com email sendo reenviado
  const [resendingEmailLeadId, setResendingEmailLeadId] = useState<string | null>(null);
  
  // Estados para filtros
  const [filters, setFilters] = useState<LeadFilters>({
    search: "",
    status: "all",
    source: "all",
    courtesyStatus: "all",
    dateRange: "30",
    requestType: "all"
  });

  // Estados para atualização de lead
  const [updateForm, setUpdateForm] = useState({
    status: "",
    commercial_method: "",
    commercial_notes: "",
    notes: ""
  });

  // Mapeamento dos filtros da UI para o formato dos hooks - MEMOIZADO para evitar loops
  const mappedFilters = useMemo<Partial<CommercialLeadFilters>>(() => {
    const result: Partial<CommercialLeadFilters> = {};
    
    if (filters.search) {
      result.search = filters.search;
    }
    
    if (filters.status !== 'all') {
      result.status = filters.status as any;
    }
    
    if (filters.source !== 'all') {
      result.source = filters.source as any;
    }
    
    if (filters.courtesyStatus !== 'all') {
      result.courtesy_status = filters.courtesyStatus as any;
    }
    
    if (filters.requestType !== 'all') {
      result.request_type = filters.requestType as any;
    }
    
    if (filters.dateRange !== 'all') {
      const daysAgo = parseInt(filters.dateRange);
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - daysAgo);
      
      result.date_range = {
        start: startDate.toISOString(),
        end: new Date().toISOString()
      };
    }
    
    return result;
  }, [filters.search, filters.status, filters.source, filters.courtesyStatus, filters.dateRange, filters.requestType]);

  // 🔒 VERIFICAÇÃO DE ACESSO: Retorna loading ou acesso negado
  if (isCheckingCompany) {
    return (
      <MainLayout>
        <AdminLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Verificando permissões...</span>
            </div>
          </div>
        </AdminLayout>
      </MainLayout>
    );
  }

  if (!hasVindulaAccess) {
    return (
      <MainLayout>
        <AdminLayout>
          <div className="flex items-center justify-center min-h-[400px]">
            <Card className="max-w-md">
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <Shield className="h-12 w-12 text-red-500 mx-auto" />
                  <div>
                    <h3 className="text-lg font-semibold">Acesso Restrito</h3>
                    <p className="text-muted-foreground">
                      Esta área é exclusiva para a equipe comercial da Vindula.
                    </p>
                  </div>
                  <Button variant="outline" onClick={() => window.history.back()}>
                    Voltar
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </AdminLayout>
      </MainLayout>
    );
  }

  // Hooks de dados reais - PROTEGIDOS: só executam se tiver acesso
  const { data: leads, isLoading: isLoadingLeads, refetch: refetchLeads } = useCommercialLeads(
    hasVindulaAccess ? mappedFilters : { enabled: false }
  );
  const { data: stats, isLoading: isLoadingStats, refetch: refetchStats } = useCommercialLeadStats();
  const updateLead = useUpdateCommercialLead();

  // 🎯 Hooks para aprovação de add-ons
  const approveAddonRequest = useApproveAddonRequest();
  const activatePlanSubscription = useActivatePlanSubscription();
  
  // 📧 Hook para reenviar email comercial
  const resendCommercialEmail = useResendCommercialEmail();

  // 🔍 Hook para buscar histórico do lead selecionado
  const { data: leadHistory, isLoading: isLoadingHistory, refetch: refetchHistory } = useCommercialLeadHistory(
    selectedLead?.id || ""
  );

  const isLoading = isLoadingLeads || isLoadingStats;

  // Animações
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  // Funções memoizadas para evitar re-renders
  const handleRefresh = useCallback(async () => {
    // LIMPAR CACHE COMPLETO e fazer refetch
    queryClient.removeQueries({ queryKey: QueryKeys.commercialLeads.all() });
    queryClient.removeQueries({ queryKey: QueryKeys.commercialLeads.stats() });
    queryClient.removeQueries({ queryKey: QueryKeys.trials.all() });
    queryClient.removeQueries({ queryKey: QueryKeys.trials.dashboard() });
    queryClient.removeQueries({ queryKey: QueryKeys.subscription.current() });
    queryClient.removeQueries({ queryKey: QueryKeys.subscription.plans() });
    // Remover todas as queries de subscription usando predicate
    queryClient.removeQueries({ 
      predicate: (query) => query.queryKey[0] === 'subscription'
    });
    
    await Promise.all([
      refetchLeads(),
      refetchStats(),
      refetchHistory() // ✅ Incluir atualização do histórico
    ]);
  }, [refetchLeads, refetchStats, refetchHistory, queryClient]);

  const handleLeadClick = useCallback((lead: CommercialLead) => {
    setSelectedLead(lead);
    setShowLeadDetail(true);
  }, []);

  const handleUpdateLead = useCallback((lead: CommercialLead) => {
    setSelectedLead(lead);
    setUpdateForm({
      status: lead.status || "",
      commercial_method: lead.commercial_method || "",
      commercial_notes: lead.commercial_notes || "",
      notes: lead.commercial_notes || ""
    });
    setShowUpdateDialog(true);
  }, []);

  const handleSubmitUpdate = useCallback(async () => {
    if (!selectedLead) return;

    try {
      await updateLead.mutateAsync({
        leadId: selectedLead.id,
        updates: {
          status: updateForm.status as any,
          commercial_notes: updateForm.notes,
          commercial_method: updateForm.commercial_method,
          // ✅ CORRIGIDO: Remover intervention_details problemático
          // Usar apenas campos que existem na tabela
        }
      });
      
      setShowUpdateDialog(false);
      handleRefresh();
    } catch (error) {
      console.error('Erro ao atualizar lead:', error);
    }
  }, [selectedLead, updateForm, updateLead, handleRefresh]);

  // Função para reenviar email comercial
  const handleResendEmail = useCallback(async (leadId: string) => {
    console.log('🔄 [handleResendEmail] Reenviando email para lead:', leadId);
    
    // Prevenir múltiplas chamadas para o mesmo lead
    if (resendingEmailLeadId === leadId) {
      console.log('⚠️ [handleResendEmail] Já está reenviando email para este lead');
      return;
    }
    
    setResendingEmailLeadId(leadId);
    
    try {
      await resendCommercialEmail.mutateAsync({ leadId });
      console.log('✅ [handleResendEmail] Email reenviado com sucesso para:', leadId);
    } catch (error) {
      console.error('❌ [handleResendEmail] Erro ao reenviar email para', leadId, ':', error);
    } finally {
      setResendingEmailLeadId(null);
    }
  }, [resendCommercialEmail, resendingEmailLeadId]);

  // Funções auxiliares memoizadas
  const getStatusBadge = useCallback((status: string, lead?: CommercialLead) => {
    // CORREÇÃO: Verificar se há trial ativo independente do status principal
    if (lead && lead.activation_status === 'activated' && lead.courtesy_period_start) {
      const now = new Date();
      const endDate = new Date(lead.courtesy_period_end!);
      
      // NOVA LÓGICA: Se tem billing_id no activation_details, é cliente pagante convertido
      const hasBeenConverted = lead.activation_details && 
                              typeof lead.activation_details === 'object' && 
                              'billing_id' in lead.activation_details;
      
      if (now <= endDate) {
        const daysLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        
        if (hasBeenConverted) {
          // Cliente convertido - mostrar como PAGO com dias de cortesia restantes
          return (
            <Badge variant="default" className="flex items-center gap-1 bg-green-50 text-green-700 border-green-200">
              <CheckCircle className="h-3 w-3" />
              ATIVO (Pago - {daysLeft}d cortesia)
            </Badge>
          );
        } else {
          // Cliente em trial genuíno
          return (
            <Badge variant="default" className="flex items-center gap-1 bg-blue-50 text-blue-700 border-blue-200">
              <CheckCircle className="h-3 w-3" />
              ATIVO (Trial - {daysLeft}d)
            </Badge>
          );
        }
      }
    }

    const statusMap = {
      'pending': { variant: 'secondary', label: 'Pendente', icon: Clock },
      'contacted': { variant: 'default', label: 'Contatado', icon: Phone },
      'negotiating': { variant: 'default', label: 'Negociação', icon: MessageSquare },
      'converted': { variant: 'default', label: 'Convertido', icon: CheckCircle },
      'lost': { variant: 'destructive', label: 'Perdido', icon: AlertTriangle },
      'courtesy_expired': { variant: 'destructive', label: 'Cortesia Expirada', icon: AlertTriangle },
      'cancelled': { variant: 'secondary', label: 'Cancelado', icon: XCircle }
    } as const;

    const config = statusMap[status as keyof typeof statusMap] || statusMap.pending;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  }, []);

  const getSourceIcon = useCallback((source: string) => {
    const sourceMap = {
      'ai-credits': Brain,
      'storage-full': HardDrive,
      'users-limit': Users,
      'plan-management': Crown
    } as const;

    return sourceMap[source as keyof typeof sourceMap] || Target;
  }, []);

  const getCourtesyStatus = useCallback((lead: CommercialLead) => {
    if (!lead.courtesy_period_start) return null;
    
    const now = new Date();
    const endDate = new Date(lead.courtesy_period_end!);
    
    if (now > endDate) {
      return { status: 'expired', label: 'Cortesia Expirada', variant: 'destructive' as const };
    }
    
    const daysLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysLeft <= 1) {
      return { status: 'urgent', label: `${daysLeft} dia restante`, variant: 'destructive' as const };
    } else if (daysLeft <= 3) {
      return { status: 'warning', label: `${daysLeft} dias restantes`, variant: 'secondary' as const };
    }
    
    return { status: 'active', label: `${daysLeft} dias restantes`, variant: 'default' as const };
  }, []);

  const formatCurrency = useCallback((value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  }, []);

  // Função para detectar se é um lead de add-on
  const isAddonLead = useCallback((lead: CommercialLead) => {
    const requestType = detectRequestType(lead);
    return requestType === 'addon_only' || requestType === 'upgrade_complete';
  }, []);

  // Função para verificar se pode aprovar
  const canApprove = useCallback((lead: CommercialLead) => {
    const isAddon = isAddonLead(lead);
    const approvalStatus = lead.approval_status;
    
    // Se não tem approval_status definido, considerar como 'pending'
    return isAddon && (approvalStatus === 'pending' || !approvalStatus);
  }, [isAddonLead]);

  // Função para verificar se pode ativar plano
  const canActivatePlan = useCallback((lead: CommercialLead) => {
    const isPlanChange = lead.request_type === 'plan_change';
    const isInTrial = lead.activation_status === 'activated' && lead.courtesy_period_end;
    const isNotConverted = lead.status !== 'converted';
    
    return isPlanChange && isInTrial && isNotConverted;
  }, []);



  // Função para detectar tipo de request
  const detectRequestType = useCallback((lead: CommercialLead) => {
    const hasNewPlan = lead.selected_plan && lead.selected_plan.id !== lead.current_plan?.id;
    const hasAddons = lead.selected_addons && lead.selected_addons.length > 0;
    
    if (!hasNewPlan && hasAddons) {
      return 'addon_only';
    } else if (hasNewPlan && hasAddons) {
      return 'upgrade_complete';
    } else if (hasNewPlan && !hasAddons) {
      return 'plan_change';
    } else {
      return 'unknown';
    }
  }, []);

  // Handlers memoizados para filtros
  const handleFilterChange = useCallback((key: keyof LeadFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const handleUpdateFormChange = useCallback((key: string, value: string) => {
    setUpdateForm(prev => ({ ...prev, [key]: value }));
  }, []);

  // ⚡ HANDLERS ESPECÍFICOS MEMOIZADOS PARA EVITAR RE-RENDERS
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFilterChange('search', e.target.value);
  }, [handleFilterChange]);

  const handleStatusChange = useCallback((value: string) => {
    handleFilterChange('status', value);
  }, [handleFilterChange]);

  const handleSourceChange = useCallback((value: string) => {
    handleFilterChange('source', value);
  }, [handleFilterChange]);

  const handleRequestTypeChange = useCallback((value: string) => {
    handleFilterChange('requestType', value);
  }, [handleFilterChange]);

  const handleCourtesyChange = useCallback((value: string) => {
    handleFilterChange('courtesyStatus', value);
  }, [handleFilterChange]);

  const handleDateRangeChange = useCallback((value: string) => {
    handleFilterChange('dateRange', value);
  }, [handleFilterChange]);

  // ⚡ HANDLERS DO FORMULÁRIO DE ATUALIZAÇÃO MEMOIZADOS
  const handleUpdateStatusChange = useCallback((value: string) => {
    handleUpdateFormChange('status', value);
  }, [handleUpdateFormChange]);

  const handleUpdateMethodChange = useCallback((value: string) => {
    handleUpdateFormChange('commercial_method', value);
  }, [handleUpdateFormChange]);

  const handleUpdateNotesChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleUpdateFormChange('commercial_notes', e.target.value);
  }, [handleUpdateFormChange]);

  const handleUpdateCommercialNotesChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleUpdateFormChange('notes', e.target.value);
  }, [handleUpdateFormChange]);

  // ⚡ HANDLERS PARA APROVAÇÃO DE ADD-ONS MEMOIZADOS
  const handleApproveAddon = useCallback(async (leadId: string, approvalNotes?: string) => {
    try {
      await approveAddonRequest.mutateAsync({
        leadId,
        action: 'approve',
        approvalNotes
      });
    } catch (error) {
      console.error('Erro ao aprovar add-on:', error);
    }
  }, [approveAddonRequest]);

  const handleRejectAddon = useCallback(async (leadId: string, approvalNotes?: string) => {
    try {
      await approveAddonRequest.mutateAsync({
        leadId,
        action: 'reject',
        approvalNotes
      });
    } catch (error) {
      console.error('Erro ao rejeitar add-on:', error);
    }
  }, [approveAddonRequest]);

  // Handlers para abrir modais de aprovação
  const handleOpenApprovalDialog = useCallback((lead: CommercialLead, action: 'approve' | 'reject') => {
    setSelectedLead(lead);
    setApprovalAction(action);
    setApprovalNotes('');
    setShowApprovalDialog(true);
  }, []);

  const handleSubmitApproval = useCallback(async () => {
    if (!selectedLead) return;

    if (approvalAction === 'approve') {
      await handleApproveAddon(selectedLead.id, approvalNotes);
    } else {
      await handleRejectAddon(selectedLead.id, approvalNotes);
    }

    setShowApprovalDialog(false);
    setApprovalNotes('');
  }, [selectedLead, approvalAction, approvalNotes, handleApproveAddon, handleRejectAddon]);

  // ⚡ HANDLERS PARA ATIVAÇÃO DE PLANO
  const handleActivatePlan = useCallback(async (leadId: string, activationNotes?: string) => {
    try {
      await activatePlanSubscription.mutateAsync({
        leadId,
        activationNotes
      });
    } catch (error) {
      console.error('Erro ao ativar plano:', error);
    }
  }, [activatePlanSubscription]);

  const handleOpenActivationDialog = useCallback((lead: CommercialLead) => {
    setSelectedLead(lead);
    setActivationNotes('');
    setShowActivationDialog(true);
  }, []);

  const handleSubmitActivation = useCallback(async () => {
    if (!selectedLead) return;

    await handleActivatePlan(selectedLead.id, activationNotes);
    setShowActivationDialog(false);
    setActivationNotes('');
  }, [selectedLead, activationNotes, handleActivatePlan]);

  // Componente do Dialog explicativo dos Leads
  function LeadsExplanationDialog() {
    return (
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Users className="h-6 w-6 text-blue-500" />
            O que são "Leads Comerciais"?
          </DialogTitle>
          <DialogDescription className="text-base text-gray-600 dark:text-gray-400">
            Entenda como classificamos e contamos as solicitações comerciais no sistema
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4 overflow-y-auto">
          {/* Seção: Definição */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Briefcase className="h-5 w-5 text-blue-500" />
              Definição
            </h3>
            
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                🎯 Lead = Solicitação de upgrade ou mudança comercial
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Cada vez que um cliente expressa interesse em <strong>mudar de plano</strong>, 
                <strong>adicionar add-ons</strong> ou fazer <strong>upgrades</strong>, criamos um lead para acompanhar essa oportunidade.
              </p>
            </div>
          </div>

          {/* Seção: Tipos de Leads */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Filter className="h-5 w-5 text-purple-500" />
              Tipos de Leads (Detecção Inteligente)
            </h3>
            
            <div className="grid gap-4">
              <div className="flex items-start gap-3 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                <div className="p-2 bg-purple-100 dark:bg-purple-900/40 rounded-full">
                  <ArrowRight className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">🔄 Mudanças de Plano</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Cliente quer trocar do plano atual para outro (ex: Gratuito → Padrão, Padrão → Premium)
                  </p>
                  <div className="mt-2 text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 p-2 rounded">
                    <strong>Detecção:</strong> selected_plan.id ≠ current_plan.id && sem add-ons
                  </div>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/40 rounded-full">
                  <Plus className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">🔧 Apenas Add-ons</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Cliente mantém o plano atual mas quer adicionar recursos (+5 usuários, +storage, IA, etc.)
                  </p>
                  <div className="mt-2 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 p-2 rounded">
                    <strong>Detecção:</strong> plano mantém && selected_addons.length &gt; 0
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-3 p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                <div className="p-2 bg-orange-100 dark:bg-orange-900/40 rounded-full">
                  <Zap className="h-4 w-4 text-orange-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">🚀 Upgrade Completo</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Cliente quer mudar de plano E adicionar add-ons ao mesmo tempo (upgrade híbrido)
                  </p>
                  <div className="mt-2 text-xs bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 p-2 rounded">
                    <strong>Detecção:</strong> plano novo && add-ons adicionais
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Seção: Como são Contados */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-green-500" />
              Como são Contados
            </h3>
            
            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3">
              <div className="text-sm">
                <div className="font-medium text-gray-900 dark:text-white mb-2">
                  📊 Sistema de contagem em tempo real:
                </div>
                <ul className="space-y-2 text-gray-600 dark:text-gray-400 ml-4">
                  <li>• <strong>Total:</strong> Soma de todos os registros na tabela commercial</li>
                  <li>• <strong>Por Tipo:</strong> Detecção dinâmica baseada nos dados reais</li>
                  <li>• <strong>Atualização:</strong> Instantânea quando novos leads chegam</li>
                  <li>• <strong>Filtros:</strong> Considera todos os status (pendente, negociação, fechado, etc.)</li>
                </ul>
              </div>
            </div>
            
            <div className="text-xs text-gray-500 dark:text-gray-400 bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200 dark:border-green-800">
              💡 <strong>Diferença do Pipeline:</strong> Total de Leads conta TODOS os registros, Pipeline conta apenas leads ativos!
            </div>
          </div>

          {/* Seção: Exemplo Prático */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Briefcase className="h-5 w-5 text-indigo-500" />
              Exemplo Prático
            </h3>
            
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 space-y-3">
              <div className="text-sm">
                <div className="font-medium text-gray-900 dark:text-white mb-2">
                  🏢 Cenário: Empresa com 10 leads hoje
                </div>
                <div className="grid gap-2 text-gray-600 dark:text-gray-400">
                  <div className="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded">
                    <span>• 7 empresas: Gratuito → Padrão</span>
                    <Badge variant="outline" className="text-xs bg-purple-50 text-purple-700">Mudança de Plano</Badge>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded">
                    <span>• 3 empresas: +1 add-on (mantém plano)</span>
                    <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">Apenas Add-ons</Badge>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded">
                    <span>• 0 empresas: Plano + Add-ons</span>
                    <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700">Upgrade Completo</Badge>
                  </div>
                </div>
                <div className="mt-3 p-3 bg-blue-100 dark:bg-blue-900/30 rounded text-blue-700 dark:text-blue-300">
                  <strong>Total: 7 + 3 + 0 = 10 leads</strong>
                </div>
              </div>
            </div>
          </div>

          {/* Seção: Diferenças Importantes */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              Diferenças Importantes
            </h3>
            
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded border-l-4 border-blue-400">
                <Users className="h-4 w-4 text-blue-600" />
                <span className="text-blue-700 dark:text-blue-300"><strong>Total de Leads:</strong> TODOS os registros (ativos + fechados + perdidos)</span>
              </div>
              <div className="flex items-center gap-2 p-3 bg-amber-50 dark:bg-amber-900/20 rounded border-l-4 border-amber-400">
                <DollarSign className="h-4 w-4 text-amber-600" />
                <span className="text-amber-700 dark:text-amber-300"><strong>Pipeline:</strong> Apenas leads ativos (pendente, negociação, proposta)</span>
              </div>
              <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded border-l-4 border-green-400">
                <TrendingUp className="h-4 w-4 text-green-600" />
                <span className="text-green-700 dark:text-green-300"><strong>Conversão:</strong> Leads fechados com sucesso</span>
              </div>
            </div>
          </div>

          {/* Seção: Tecnologia */}
          <div className="bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-900/20 dark:to-slate-900/20 p-4 rounded-lg border border-gray-200 dark:border-gray-800">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
              <Settings className="h-4 w-4 text-gray-500" />
              Detecção Inteligente
            </h4>
            <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <p>• <strong>Fonte de dados:</strong> Tabela commercial + análise dos campos selected_plan vs current_plan</p>
              <p>• <strong>Atualização:</strong> Cálculo dinâmico em tempo real (não cache)</p>
              <p>• <strong>Precisão:</strong> Mesma lógica usada na tabela e pipeline para consistência</p>
              <p>• <strong>Performance:</strong> Filtros otimizados para não sobrecarregar a interface</p>
            </div>
          </div>
        </div>
      </DialogContent>
    );
  }

  // Componente do Dialog explicativo do Pipeline
  function PipelineExplanationDialog() {
    return (
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Calculator className="h-6 w-6 text-amber-500" />
            O que é "Pipeline de Vendas"?
          </DialogTitle>
          <DialogDescription className="text-base text-gray-600 dark:text-gray-400">
            Entenda como calculamos o valor potencial das oportunidades comerciais em andamento
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4 overflow-y-auto">
          {/* Seção: Definição */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Target className="h-5 w-5 text-amber-500" />
              Definição
            </h3>
            
            <div className="bg-amber-50 dark:bg-amber-900/20 rounded-lg p-4 border border-amber-200 dark:border-amber-800">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                💰 Pipeline = Valor mensal potencial de todos os leads ativos
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Representa o <strong>valor mensal recorrente</strong> que seria gerado se 
                todas as solicitações comerciais pendentes fossem convertidas.
              </p>
            </div>
          </div>

          {/* Seção: Como é Calculado */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Calculator className="h-5 w-5 text-blue-500" />
              Como é Calculado
            </h3>
            
            <div className="grid gap-4">
              <div className="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/40 rounded-full">
                  <Settings className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">🔧 Add-ons</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Soma dos preços de todos os add-ons solicitados pelos leads ativos
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                <div className="p-2 bg-green-100 dark:bg-green-900/40 rounded-full">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">📈 Mudanças de Plano</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Diferença de valor entre plano atual e plano solicitado (upgrade/downgrade)
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                <div className="p-2 bg-purple-100 dark:bg-purple-900/40 rounded-full">
                  <Zap className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">🚀 Upgrades Híbridos</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Combinação de mudança de plano + novos add-ons
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Seção: Fórmula de Cálculo */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Activity className="h-5 w-5 text-green-500" />
              Fórmula de Cálculo
            </h3>
            
            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3">
              <div className="text-sm font-mono">
                <div className="font-medium text-gray-900 dark:text-white mb-3">
                  📊 Para cada lead ativo:
                </div>
                
                <div className="space-y-2 text-gray-600 dark:text-gray-400 ml-4">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded text-blue-700 dark:text-blue-300">
                    <strong>Apenas Add-ons:</strong> Σ(preço dos add-ons solicitados)
                  </div>
                  <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded text-green-700 dark:text-green-300">
                    <strong>Mudança de Plano:</strong> preço_plano_novo - preço_plano_atual
                  </div>
                  <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded text-purple-700 dark:text-purple-300">
                    <strong>Híbrido:</strong> (plano_novo - plano_atual) + Σ(add-ons)
                  </div>
                </div>
                
                <div className="mt-3 p-3 bg-amber-100 dark:bg-amber-900/30 rounded text-amber-700 dark:text-amber-300">
                  <strong>Pipeline Total = Σ(valores de todos os leads ativos)</strong>
                </div>
              </div>
            </div>
          </div>

          {/* Seção: Exemplo Prático */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <PiggyBank className="h-5 w-5 text-green-500" />
              Exemplo Prático
            </h3>
            
            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3">
              <div className="text-sm">
                <div className="font-medium text-gray-900 dark:text-white mb-2">
                  🏢 Leads ativos hoje:
                </div>
                <ul className="space-y-2 text-gray-600 dark:text-gray-400 ml-4">
                  <li className="flex justify-between items-center">
                    <span>• Empresa A: +5 usuários + Storage 50GB</span>
                    <Badge variant="outline" className="text-xs">R$ 249,00</Badge>
                  </li>
                  <li className="flex justify-between items-center">
                    <span>• Empresa B: Pro → Premium</span>
                    <Badge variant="outline" className="text-xs">R$ 300,00</Badge>
                  </li>
                  <li className="flex justify-between items-center">
                    <span>• Empresa C: Business + IA Credits</span>
                    <Badge variant="outline" className="text-xs">R$ 150,00</Badge>
                  </li>
                  <li className="flex justify-between items-center">
                    <span>• Empresa D: +10 usuários</span>
                    <Badge variant="outline" className="text-xs">R$ 99,20</Badge>
                  </li>
                </ul>
                <div className="mt-3 p-3 bg-amber-100 dark:bg-amber-900/30 rounded text-amber-700 dark:text-amber-300">
                  <strong>Pipeline: R$ 249 + R$ 300 + R$ 150 + R$ 99,20 = R$ 798,20</strong>
                </div>
              </div>
            </div>
            
            <div className="text-xs text-gray-500 dark:text-gray-400 bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg border border-yellow-200 dark:border-yellow-800">
              💡 <strong>Pipeline saudável:</strong> 3-5x o faturamento mensal atual indica crescimento consistente!
            </div>
          </div>

          {/* Seção: Status dos Leads */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Timer className="h-5 w-5 text-indigo-500" />
              Status Incluídos no Pipeline
            </h3>
            
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2 p-2 bg-green-50 dark:bg-green-900/20 rounded border-l-4 border-green-400">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-green-700 dark:text-green-300"><strong>Incluídos:</strong> Pending, Contacted, Negotiating, Proposal Sent</span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-red-50 dark:bg-red-900/20 rounded border-l-4 border-red-400">
                <XCircle className="h-4 w-4 text-red-600" />
                <span className="text-red-700 dark:text-red-300"><strong>Excluídos:</strong> Converted, Closed Won, Closed Lost, Cancelled</span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded border-l-4 border-blue-400">
                <Info className="h-4 w-4 text-blue-600" />
                <span className="text-blue-700 dark:text-blue-300"><strong>Lógica:</strong> Apenas oportunidades ainda em andamento contam para o pipeline</span>
              </div>
            </div>
          </div>

          {/* Seção: Limitações Atuais */}
          <div className="bg-gradient-to-r from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
              ⚠️ Limitação Técnica Atual
            </h4>
            <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
              <p><strong>Problema identificado:</strong> O campo <code>estimated_monthly_value</code> está sendo salvo como 0 na função SQL.</p>
              <p><strong>Workaround atual:</strong> O cálculo é feito dinamicamente na interface baseado nos preços reais dos planos e add-ons.</p>
              <p><strong>Solução implementada:</strong> Corrigido na função <code>consolidate_or_create_lead_v3</code> com cálculo correto do valor.</p>
            </div>
          </div>
        </div>
      </DialogContent>
    );
  }

  return (
    <MainLayout>
    <AdminLayout>
      <GenericPermissionGate
        resourceTypeKey="commercial_lead"
        actionKey="view_commercial_leads"
        loadingComponent={<div>Carregando permissões...</div>}
      >
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-6"
        >
          {/* Hero Section */}
          <HeroSection
            title="CRM Comercial"
            description="Gerencie leads, acompanhe períodos de cortesia e monitore conversões"
            icon={Briefcase}
            gradientColors="from-purple-600 via-blue-600 to-indigo-600"
            actions={
              <div className="flex items-center gap-3">
                <RefreshButton
                  variant="ghost"
                  className="text-white hover:bg-white/10 border border-white/20"
                  isLoading={isLoading}
                  onRefresh={handleRefresh}
                  successMessage="Dados atualizados!"
                />
                <Button variant="ghost" className="text-white hover:bg-white/10 border border-white/20">
                  <Download className="h-4 w-4 mr-2" />
                  Exportar
                </Button>
              </div>
            }
          />

          {/* Cards de Estatísticas */}
          <motion.div variants={cardVariants}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="border-0 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <p className="text-sm font-medium text-slate-600">Total de Leads</p>
                        
                        {/* Ícone de Ajuda com Dialog */}
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-full"
                            >
                              <motion.div
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.95 }}
                                transition={{ type: "spring", stiffness: 400 }}
                              >
                                <HelpCircle className="h-4 w-4 text-blue-500 hover:text-blue-600" />
                              </motion.div>
                            </Button>
                          </DialogTrigger>
                          
                          <LeadsExplanationDialog />
                        </Dialog>
                      </div>
                      
                      <p className="text-3xl font-bold text-slate-800">{leads?.length || 0}</p>
                      
                      {/* Breakdown por tipo - Cálculo dinâmico correto */}
                      {(() => {
                        if (!leads || leads.length === 0) {
                          return (
                            <div className="mt-2 text-xs text-slate-500 space-y-1">
                              <div className="flex justify-between">
                                <span>🔄 Mudanças de Plano:</span>
                                <span className="font-medium">0</span>
                              </div>
                              <div className="flex justify-between">
                                <span>🔧 Apenas Add-ons:</span>
                                <span className="font-medium">0</span>
                              </div>
                              <div className="flex justify-between">
                                <span>🚀 Upgrade Completo:</span>
                                <span className="font-medium">0</span>
                              </div>
                            </div>
                          );
                        }

                        // Usar a mesma função de detecção do pipeline
                        const detectRequestType = (lead: any) => {
                          const hasNewPlan = lead.selected_plan && lead.selected_plan.id !== lead.current_plan?.id;
                          const hasAddons = lead.selected_addons && lead.selected_addons.length > 0;
                          
                          if (hasNewPlan && hasAddons) {
                            return "hybrid_upgrade";
                          } else if (hasNewPlan && !hasAddons) {
                            return "plan_change";
                          } else if (!hasNewPlan && hasAddons) {
                            return "addon_request";
                          } else {
                            return "unknown";
                          }
                        };

                        const planChangeCount = leads.filter(l => detectRequestType(l) === 'plan_change').length;
                        const addonRequestCount = leads.filter(l => detectRequestType(l) === 'addon_request').length;
                        const hybridUpgradeCount = leads.filter(l => detectRequestType(l) === 'hybrid_upgrade').length;

                        return (
                          <div className="mt-2 text-xs text-slate-500 space-y-1">
                            <div className="flex justify-between">
                              <span>🔄 Mudanças de Plano:</span>
                              <span className="font-medium">{planChangeCount}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>🔧 Apenas Add-ons:</span>
                              <span className="font-medium">{addonRequestCount}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>🚀 Upgrade Completo:</span>
                              <span className="font-medium">{hybridUpgradeCount}</span>
                            </div>
                          </div>
                        );
                      })()}
                    </div>
                    <div className="p-3 bg-blue-500 rounded-xl">
                      <Users className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 bg-gradient-to-br from-green-50 to-emerald-50 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-slate-600">Em Cortesia</p>
                      <p className="text-3xl font-bold text-slate-800">{stats?.active_courtesy || 0}</p>
                      {/* Breakdown por urgência */}
                      <div className="mt-2 text-xs text-slate-500 space-y-1">
                        <div className="flex justify-between">
                          <span>🔴 Alta Prioridade:</span>
                          <span className="font-medium text-red-600">{stats?.urgency_analysis?.high_priority_count || 0}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>🟡 Média Prioridade:</span>
                          <span className="font-medium text-yellow-600">{stats?.urgency_analysis?.medium_priority_count || 0}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>🟢 Baixa Prioridade:</span>
                          <span className="font-medium text-green-600">{stats?.urgency_analysis?.low_priority_count || 0}</span>
                        </div>
                      </div>
                    </div>
                    <div className="p-3 bg-green-500 rounded-xl">
                      <Gift className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 bg-gradient-to-br from-purple-50 to-pink-50 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-slate-600">Taxa de Conversão</p>
                      <p className="text-3xl font-bold text-slate-800">{stats?.conversion_rate?.toFixed(1) || 0}%</p>
                      {/* Breakdown por tipo de conversão */}
                      <div className="mt-2 text-xs text-slate-500 space-y-1">
                        {(() => {
                          const totalConverted = leads?.filter(l => l.status === 'converted').length || 0;
                                                      const addonConverted = leads?.filter(l => 
                              l.status === 'converted' && 
                            l.request_type === 'addon_request'
                          ).length || 0;
                                                      const planConverted = leads?.filter(l => 
                              l.status === 'converted' && 
                            (l.request_type === 'plan_change' || l.request_type === 'full_upgrade')
                          ).length || 0;
                          
                          return (
                            <>
                              <div className="flex justify-between">
                                <span>✅ Total Convertidos:</span>
                                <span className="font-medium text-green-600">{totalConverted}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>🔧 Add-ons:</span>
                                <span className="font-medium">{addonConverted}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>📈 Planos:</span>
                                <span className="font-medium">{planConverted}</span>
                              </div>
                            </>
                          );
                        })()}
                      </div>
                    </div>
                    <div className="p-3 bg-purple-500 rounded-xl">
                      <TrendingUp className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 bg-gradient-to-br from-amber-50 to-orange-50 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <p className="text-sm font-medium text-slate-600">Pipeline</p>
                        
                        {/* Ícone de Ajuda com Dialog */}
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 hover:bg-amber-100 dark:hover:bg-amber-900/20 rounded-full"
                            >
                              <motion.div
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.95 }}
                                transition={{ type: "spring", stiffness: 400 }}
                              >
                                <HelpCircle className="h-4 w-4 text-amber-500 hover:text-amber-600" />
                              </motion.div>
                            </Button>
                          </DialogTrigger>
                          
                          <PipelineExplanationDialog />
                        </Dialog>
                      </div>
                      
                      {/* Cálculo dinâmico correto do pipeline */}
                      {(() => {
                        // Filtrar apenas leads ativos (que contam para o pipeline)
                        const activeLeads = leads?.filter(l => 
                          ['pending', 'contacted', 'negotiating', 'proposal_sent'].includes(l.status)
                        ) || [];

                        // Função para detectar o tipo real de solicitação (igual à tabela)
                        const detectRequestType = (lead: any) => {
                          const hasNewPlan = lead.selected_plan && lead.selected_plan.id !== lead.current_plan?.id;
                          const hasAddons = lead.selected_addons && lead.selected_addons.length > 0;
                          
                          if (hasNewPlan && hasAddons) {
                            return "hybrid_upgrade";
                          } else if (hasNewPlan && !hasAddons) {
                            return "plan_change";
                          } else if (!hasNewPlan && hasAddons) {
                            return "addon_request";
                          } else {
                            return "unknown";
                          }
                        };

                        // Simular cálculo real baseado em preços de planos e add-ons
                        // TODO: Implementar busca real dos preços quando os dados estiverem disponíveis
                        const calculateLeadValue = (lead: any) => {
                          let value = 0;
                          
                          // Preços simulados para demonstração (baseado no sistema real)
                          const addonPrices: Record<string, number> = {
                            'users_5': 99.20,    // +5 usuários
                            'users_10': 198.40,  // +10 usuários  
                            'users_25': 496.00,  // +25 usuários
                            'storage_50': 149.90, // Storage 50GB
                            'storage_100': 249.90, // Storage 100GB
                            'ai_credits': 99.90,   // IA Credits
                          };
                          
                          const planPrices: Record<string, number> = {
                            'free': 0,
                            'pro': 99.90,        // Corrigido baseado na imagem
                            'premium': 199.90,
                            'business': 399.90,
                            'padrao': 99.90,     // Adicionado baseado na imagem
                          };

                          // Calcular valor dos add-ons
                          if (lead.selected_addons && Array.isArray(lead.selected_addons)) {
                            lead.selected_addons.forEach((addon: any) => {
                              const addonId = addon.id || addon.addon_id;
                              value += addonPrices[addonId] || 99.90; // fallback genérico
                            });
                          }

                          // Calcular diferença de plano (se houver mudança)
                          const hasNewPlan = lead.selected_plan && lead.selected_plan.id !== lead.current_plan?.id;
                          if (hasNewPlan) {
                            const currentPlanId = lead.current_plan?.id || lead.current_plan_id || 'free';
                            const newPlanId = lead.selected_plan.id;
                            
                            const currentPlanPrice = planPrices[currentPlanId] || 0;
                            const newPlanPrice = planPrices[newPlanId] || 99.90;
                            const planDiff = newPlanPrice - currentPlanPrice;
                            if (planDiff > 0) value += planDiff; // Apenas upgrades contam positivamente
                          }

                          return value;
                        };

                        // Usar detecção real ao invés do campo request_type
                        const addonLeads = activeLeads.filter(l => detectRequestType(l) === 'addon_request');
                        const planLeads = activeLeads.filter(l => detectRequestType(l) === 'plan_change');
                        const hybridLeads = activeLeads.filter(l => detectRequestType(l) === 'hybrid_upgrade');

                        const addonValue = addonLeads.reduce((sum, l) => sum + calculateLeadValue(l), 0);
                        const planValue = planLeads.reduce((sum, l) => sum + calculateLeadValue(l), 0);
                        const hybridValue = hybridLeads.reduce((sum, l) => sum + calculateLeadValue(l), 0);
                        
                        const totalPipeline = addonValue + planValue + hybridValue;

                        return (
                          <>
                            <p className="text-3xl font-bold text-slate-800">{formatCurrency(totalPipeline)}</p>
                            
                            {/* Breakdown por tipo de valor */}
                            <div className="mt-2 text-xs text-slate-500 space-y-1">
                              <div className="flex justify-between">
                                <span>🔧 Add-ons:</span>
                                <span className="font-medium text-blue-600">{formatCurrency(addonValue)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>📈 Planos:</span>
                                <span className="font-medium text-green-600">{formatCurrency(planValue)}</span>
                              </div>
                              {hybridValue > 0 && (
                                <div className="flex justify-between">
                                  <span>🚀 Híbrido:</span>
                                  <span className="font-medium text-purple-600">{formatCurrency(hybridValue)}</span>
                                </div>
                              )}
                            </div>
                          </>
                        );
                      })()}
                    </div>
                    <div className="p-3 bg-amber-500 rounded-xl">
                      <DollarSign className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </motion.div>

          {/* Métricas Avançadas por Tipo de Solicitação */}
          <motion.div variants={cardVariants}>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Análise Detalhada por Tipo de Solicitação
                </CardTitle>
                <CardDescription>
                  Insights específicos sobre diferentes tipos de requests comerciais
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Análise de Add-ons */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Settings className="h-4 w-4 text-blue-600" />
                      </div>
                      <h4 className="font-semibold text-slate-700">Solicitações de Add-ons</h4>
                    </div>
                    
                    {(() => {
                      const addonLeads = leads?.filter(l => l.request_type === 'addon_request') || [];
                      const addonConverted = addonLeads.filter(l => l.status === 'converted').length;
                      const addonConversionRate = addonLeads.length > 0 ? (addonConverted / addonLeads.length) * 100 : 0;
                      const avgAddonValue = addonLeads.length > 0 ? 
                        addonLeads.reduce((sum, l) => sum + (l.estimated_monthly_value || 0), 0) / addonLeads.length : 0;
                      
                      return (
                        <div className="space-y-3 text-sm">
                          <div className="flex justify-between">
                            <span className="text-slate-600">Total de Solicitações:</span>
                            <span className="font-semibold">{addonLeads.length}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-600">Taxa de Conversão:</span>
                            <span className="font-semibold text-blue-600">{addonConversionRate.toFixed(1)}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-600">Valor Médio:</span>
                            <span className="font-semibold">{formatCurrency(avgAddonValue)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-600">Convertidos:</span>
                            <span className="font-semibold text-green-600">{addonConverted}</span>
                          </div>
                          
                          {/* Top Add-ons Solicitados */}
                          <div className="pt-2 border-t">
                            <p className="text-xs text-slate-500 mb-2">Add-ons Mais Solicitados:</p>
                            {(() => {
                              const addonCounts = {};
                              addonLeads.forEach(lead => {
                                if (lead.selected_addons && Array.isArray(lead.selected_addons)) {
                                  lead.selected_addons.forEach(addon => {
                                    const name = addon.name || addon.id || 'Add-on não identificado';
                                    addonCounts[name] = (addonCounts[name] || 0) + 1;
                                  });
                                }
                              });
                              
                              const topAddons = Object.entries(addonCounts)
                                .sort(([,a], [,b]) => b - a)
                                .slice(0, 3);
                              
                              return topAddons.length > 0 ? (
                                <div className="space-y-1">
                                  {topAddons.map(([name, count], index) => (
                                    <div key={name} className="flex justify-between text-xs">
                                      <span className="text-slate-600 truncate">{name}</span>
                                      <span className="font-medium">{count}x</span>
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <p className="text-xs text-slate-400">Nenhum add-on identificado</p>
                              );
                            })()}
                          </div>
                        </div>
                      );
                    })()}
                  </div>

                  {/* Análise de Mudanças de Plano */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <TrendingUp className="h-4 w-4 text-green-600" />
                      </div>
                      <h4 className="font-semibold text-slate-700">Mudanças de Plano</h4>
                    </div>
                    
                    {(() => {
                      const planLeads = leads?.filter(l => 
                        l.request_type === 'plan_change' || l.request_type === 'full_upgrade'
                      ) || [];
                      const planConverted = planLeads.filter(l => l.status === 'converted').length;
                      const planConversionRate = planLeads.length > 0 ? (planConverted / planLeads.length) * 100 : 0;
                      const avgPlanValue = planLeads.length > 0 ? 
                        planLeads.reduce((sum, l) => sum + (l.estimated_monthly_value || 0), 0) / planLeads.length : 0;
                      
                      return (
                        <div className="space-y-3 text-sm">
                          <div className="flex justify-between">
                            <span className="text-slate-600">Total de Solicitações:</span>
                            <span className="font-semibold">{planLeads.length}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-600">Taxa de Conversão:</span>
                            <span className="font-semibold text-green-600">{planConversionRate.toFixed(1)}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-600">Valor Médio:</span>
                            <span className="font-semibold">{formatCurrency(avgPlanValue)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-600">Convertidos:</span>
                            <span className="font-semibold text-green-600">{planConverted}</span>
                          </div>
                          
                          {/* Planos Mais Solicitados */}
                          <div className="pt-2 border-t">
                            <p className="text-xs text-slate-500 mb-2">Planos Mais Solicitados:</p>
                            {(() => {
                              const planCounts = {};
                              planLeads.forEach(lead => {
                                if (lead.selected_plan) {
                                  const name = lead.selected_plan.name || lead.selected_plan.id || 'Plano não identificado';
                                  planCounts[name] = (planCounts[name] || 0) + 1;
                                }
                              });
                              
                              const topPlans = Object.entries(planCounts)
                                .sort(([,a], [,b]) => b - a)
                                .slice(0, 3);
                              
                              return topPlans.length > 0 ? (
                                <div className="space-y-1">
                                  {topPlans.map(([name, count], index) => (
                                    <div key={name} className="flex justify-between text-xs">
                                      <span className="text-slate-600 truncate">{name}</span>
                                      <span className="font-medium">{count}x</span>
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <p className="text-xs text-slate-400">Nenhum plano identificado</p>
                              );
                            })()}
                          </div>
                        </div>
                      );
                    })()}
                  </div>

                  {/* Análise de Urgência e Prioridade */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <div className="p-2 bg-red-100 rounded-lg">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                      </div>
                      <h4 className="font-semibold text-slate-700">Análise de Urgência</h4>
                    </div>
                    
                    <div className="space-y-3 text-sm">
                      {/* Distribuição por Prioridade */}
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-slate-600">🔴 Alta (1-3):</span>
                          <div className="flex items-center gap-2">
                            <span className="font-semibold text-red-600">{stats?.urgency_analysis?.high_priority_count || 0}</span>
                            <div className="w-12 h-2 bg-gray-200 rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-red-500 transition-all duration-300"
                                style={{ 
                                  width: `${stats?.total_leads ? 
                                    ((stats?.urgency_analysis?.high_priority_count || 0) / stats.total_leads) * 100 : 0}%` 
                                }}
                              />
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <span className="text-slate-600">🟡 Média (4-6):</span>
                          <div className="flex items-center gap-2">
                            <span className="font-semibold text-yellow-600">{stats?.urgency_analysis?.medium_priority_count || 0}</span>
                            <div className="w-12 h-2 bg-gray-200 rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-yellow-500 transition-all duration-300"
                                style={{ 
                                  width: `${stats?.total_leads ? 
                                    ((stats?.urgency_analysis?.medium_priority_count || 0) / stats.total_leads) * 100 : 0}%` 
                                }}
                              />
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <span className="text-slate-600">🟢 Baixa (7-10):</span>
                          <div className="flex items-center gap-2">
                            <span className="font-semibold text-green-600">{stats?.urgency_analysis?.low_priority_count || 0}</span>
                            <div className="w-12 h-2 bg-gray-200 rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-green-500 transition-all duration-300"
                                style={{ 
                                  width: `${stats?.total_leads ? 
                                    ((stats?.urgency_analysis?.low_priority_count || 0) / stats.total_leads) * 100 : 0}%` 
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      {/* Consolidação */}
                      <div className="pt-2 border-t">
                        <p className="text-xs text-slate-500 mb-2">Consolidação de Leads:</p>
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span className="text-slate-600">Consolidados:</span>
                            <span className="font-medium">{stats?.consolidation_stats?.consolidated_leads || 0}</span>
                          </div>
                          <div className="flex justify-between text-xs">
                            <span className="text-slate-600">Individuais:</span>
                            <span className="font-medium">{stats?.consolidation_stats?.single_requests || 0}</span>
                          </div>
                          <div className="flex justify-between text-xs">
                            <span className="text-slate-600">Média por Grupo:</span>
                            <span className="font-medium">{stats?.consolidation_stats?.average_consolidation_count?.toFixed(1) || 0}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Filtros */}
          <motion.div variants={cardVariants}>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Filtros
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="search">Buscar</Label>
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="search"
                        placeholder="Nome, email ou empresa..."
                        value={filters.search}
                        onChange={handleSearchChange}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select value={filters.status} onValueChange={handleStatusChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Todos os status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todos</SelectItem>
                        <SelectItem value="pending">Pendente</SelectItem>
                        <SelectItem value="contacted">Contatado</SelectItem>
                        <SelectItem value="negotiating">Negociação</SelectItem>
                        <SelectItem value="converted">Convertido</SelectItem>
                        <SelectItem value="lost">Perdido</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="source">Origem</Label>
                    <Select value={filters.source} onValueChange={handleSourceChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Todas as origens" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todas</SelectItem>
                        <SelectItem value="ai-credits">IA - Créditos</SelectItem>
                        <SelectItem value="storage-full">Storage</SelectItem>
                        <SelectItem value="users-limit">Usuários</SelectItem>
                        <SelectItem value="plan-management">Gestão</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="requestType">Tipo de Solicitação</Label>
                    <Select value={filters.requestType} onValueChange={handleRequestTypeChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Todos os tipos" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todos</SelectItem>
                        <SelectItem value="addon_request">🔧 Apenas Add-ons</SelectItem>
                        <SelectItem value="plan_change">🔄 Mudança de Plano</SelectItem>
                        <SelectItem value="hybrid_upgrade">⚡ Plano + Add-ons</SelectItem>
                        <SelectItem value="full_upgrade">🚀 Upgrade Completo</SelectItem>
                        <SelectItem value="unknown">❓ Não Identificado</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="courtesy">Cortesia</Label>
                    <Select value={filters.courtesyStatus} onValueChange={handleCourtesyChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Todos" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todos</SelectItem>
                        <SelectItem value="active">Ativa</SelectItem>
                        <SelectItem value="expiring">Expirando</SelectItem>
                        <SelectItem value="expired">Expirada</SelectItem>
                        <SelectItem value="none">Sem cortesia</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="period">Período</Label>
                    <Select value={filters.dateRange} onValueChange={handleDateRangeChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="30 dias" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="7">7 dias</SelectItem>
                        <SelectItem value="30">30 dias</SelectItem>
                        <SelectItem value="90">90 dias</SelectItem>
                        <SelectItem value="all">Todos</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Tabela de Leads */}
          <motion.div variants={cardVariants}>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Leads Comerciais
                </CardTitle>
                <CardDescription>
                  {leads?.length || 0} leads encontrados
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Cliente</TableHead>
                          <TableHead>Tipo de Solicitação</TableHead>
                          <TableHead>Contexto Atual → Solicitado</TableHead>
                          <TableHead>Valor & Prioridade</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Criado</TableHead>
                          <TableHead className="text-right">Ações</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {leads?.map((lead) => {
                          // Função para obter badge do tipo de request
                          const getRequestTypeBadge = (requestType?: string, lead?: CommercialLead) => {
                            // CORREÇÃO: Priorizar request_type do banco, só detectar se não existir
                            let detectedType = requestType;
                            
                            // Só detectar manualmente se request_type for nulo/vazio
                            if (!detectedType && lead) {
                              const hasNewPlan = lead.selected_plan && lead.selected_plan.id !== lead.current_plan?.id;
                              const hasAddons = lead.selected_addons && lead.selected_addons.length > 0;
                              
                              if (hasNewPlan && hasAddons) {
                                detectedType = "hybrid_upgrade";
                              } else if (hasNewPlan && !hasAddons) {
                                detectedType = "plan_change";
                              } else if (!hasNewPlan && hasAddons) {
                                detectedType = "addon_request";
                              } else {
                                detectedType = "unknown";
                              }
                            }

                            const configs = {
                              addon_request: {
                                icon: Plus,
                                label: "Apenas Add-ons",
                                className: "bg-blue-50 text-blue-700 border-blue-200"
                              },
                              plan_change: {
                                icon: ArrowRight,
                                label: "Mudança de Plano",
                                className: "bg-purple-50 text-purple-700 border-purple-200"
                              },
                              hybrid_upgrade: {
                                icon: Zap,
                                label: "Plano + Add-ons",
                                className: "bg-orange-50 text-orange-700 border-orange-200"
                              },
                              full_upgrade: {
                                icon: Crown,
                                label: "Upgrade Completo",
                                className: "bg-green-50 text-green-700 border-green-200"
                              },
                              unknown: {
                                icon: HelpCircle,
                                label: "Analisando...",
                                className: "bg-gray-50 text-gray-600 border-gray-200"
                              }
                            };

                            const config = configs[detectedType as keyof typeof configs] || configs.unknown;
                            const Icon = config.icon;
                            
                            return (
                              <Badge variant="outline" className={config.className}>
                                <Icon className="h-3 w-3 mr-1" />
                                {config.label}
                              </Badge>
                            );
                          };

                          // Função para obter badge de prioridade
                          const getPriorityBadge = (priority?: string) => {
                            if (priority === 'urgent') {
                              return (
                                <Badge variant="destructive" className="animate-pulse">
                                  <AlertTriangle className="h-3 w-3 mr-1" />
                                  Urgente
                                </Badge>
                              );
                            }
                            if (priority === 'high') {
                              return (
                                <Badge variant="secondary" className="bg-yellow-50 text-yellow-700">
                                  <Clock className="h-3 w-3 mr-1" />
                                  Alta
                                </Badge>
                              );
                            }
                            return null;
                          };
                          
                          return (
                            <TableRow 
                              key={lead.id} 
                              className="cursor-pointer hover:bg-muted/50"
                              onClick={() => handleLeadClick(lead)}
                            >
                              {/* Cliente */}
                              <TableCell>
                                <div className="min-w-[200px]">
                                  <div className="font-medium">
                                    {lead.contact_info?.name || 
                                     lead.profiles?.full_name || 
                                     lead.user?.full_name ||
                                     'Cliente não identificado'}
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    {lead.contact_info?.email || 
                                     lead.profiles?.email || 
                                     lead.user?.email ||
                                     'Email não informado'}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {lead.contact_info?.company || 
                                     lead.companies?.name || 
                                     lead.company?.name ||
                                     'Empresa não informada'}
                                  </div>
                                </div>
                              </TableCell>

                              {/* Tipo de Solicitação */}
                              <TableCell>
                                <div className="space-y-1 min-w-[140px]">
                                  {getRequestTypeBadge(lead.request_type, lead)}
                                                                     {lead.source_context && (
                                     <div className="text-xs text-muted-foreground flex items-center gap-1">
                                       {(() => {
                                         const IconComponent = getSourceIcon(lead.source_context);
                                         return <IconComponent className="h-3 w-3" />;
                                       })()}
                                       {lead.source_context}
                                     </div>
                                   )}
                                </div>
                              </TableCell>

                              {/* Contexto Atual → Solicitado */}
                              <TableCell>
                                {(() => {
                                  // CORREÇÃO: Usar request_type em vez de comparar IDs manualmente
                                  const isOnlyAddons = lead.request_type === 'addon_request';
                                  const hasAddons = lead.selected_addons && lead.selected_addons.length > 0;

                                  if (isOnlyAddons) {
                                    // Apenas Add-ons: mostrar plano atual + add-ons sem "Meta"
                                    return (
                                      <div className="space-y-2 min-w-[180px]">
                                        {/* Plano Atual (mantém) */}
                                        <div className="flex items-center gap-2">
                                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                            Plano:
                                          </div>
                                          <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                            {lead.selected_plan?.name || 'Plano Atual'} (mantém)
                                          </Badge>
                                        </div>
                                        
                                        {/* Add-ons solicitados */}
                                        <div className="flex items-center gap-2">
                                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                                            Adicionar:
                                          </div>
                                          <Badge variant="secondary" className="text-xs bg-orange-50 text-orange-700 border-orange-200">
                                            <Plus className="h-2 w-2 mr-1" />
                                            {lead.selected_addons.length} add-on{lead.selected_addons.length > 1 ? 's' : ''}
                                          </Badge>
                                        </div>
                                      </div>
                                    );
                                  } else {
                                    // Mudança de Plano: mostrar atual → meta
                                    return (
                                      <div className="space-y-2 min-w-[180px]">
                                        {/* Plano Atual */}
                                        <div className="flex items-center gap-2">
                                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                            Atual:
                                          </div>
                                          <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                            {lead.current_plan?.name || 'Plano Gratuito'}
                                          </Badge>
                                        </div>
                                        
                                        {/* Seta */}
                                        <div className="flex justify-center">
                                          <ArrowDown className="h-3 w-3 text-muted-foreground" />
                                        </div>
                                        
                                        {/* Plano Solicitado */}
                                        <div className="flex items-center gap-2">
                                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                            Meta:
                                          </div>
                                          <Badge variant="default" className="text-xs bg-green-50 text-green-700 border-green-200">
                                            {lead.selected_plan?.name || 'Não especificado'}
                                          </Badge>
                                        </div>

                                        {/* Add-ons se existirem */}
                                        {hasAddons && (
                                          <div className="flex items-center gap-2">
                                            <div className="text-xs text-muted-foreground">+</div>
                                            <Badge variant="secondary" className="text-xs bg-orange-50 text-orange-700 border-orange-200">
                                              <Plus className="h-2 w-2 mr-1" />
                                              {lead.selected_addons.length} add-on{lead.selected_addons.length > 1 ? 's' : ''}
                                            </Badge>
                                          </div>
                                        )}
                                      </div>
                                    );
                                  }
                                })()}
                              </TableCell>

                              {/* Valor & Prioridade */}
                              <TableCell>
                                <div className="space-y-2 min-w-[120px]">
                                  {/* Valor */}
                                  <div className="space-y-1">
                                    <div className="font-medium text-green-600">
                                      {(() => {
                                        // Calcular valor total: plano + add-ons
                                        const planValue = lead.selected_plan?.price || 0;
                                        const addonsValue = lead.selected_addons?.reduce((total: number, addon: any) => 
                                          total + (addon.price || 0), 0) || 0;
                                        const totalValue = planValue + addonsValue;
                                        
                                        if (totalValue > 0) {
                                          return formatCurrency(totalValue);
                                        } else if (lead.estimated_monthly_value > 0) {
                                          return formatCurrency(lead.estimated_monthly_value);
                                        } else {
                                          return 'A calcular';
                                        }
                                      })()}
                                    </div>
                                    
                                    {/* Breakdown do valor */}
                                    {lead.selected_addons && lead.selected_addons.length > 0 && (
                                      <div className="text-xs text-muted-foreground">
                                        {lead.selected_plan?.price ? `Plano: ${formatCurrency(lead.selected_plan.price)}` : ''}
                                        {lead.selected_addons && lead.selected_addons.length > 0 && (
                                          <div>Add-ons: {formatCurrency(lead.selected_addons.reduce((total: number, addon: any) => total + (addon.price || 0), 0))}</div>
                                        )}
                                      </div>
                                    )}
                                  </div>

                                  {/* Prioridade */}
                                  <div className="space-y-1">
                                    {getPriorityBadge(lead.request_priority)}
                                    
                                    {lead.commercial_urgency_notes && (
                                      <div className="text-xs text-muted-foreground line-clamp-2" title={lead.commercial_urgency_notes}>
                                        {lead.commercial_urgency_notes}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </TableCell>

                              {/* Status */}
                              <TableCell>
                                {getStatusBadge(lead.status, lead)}
                              </TableCell>

                              {/* Criado */}
                              <TableCell>
                                <div className="text-sm">
                                  {formatDistanceToNow(new Date(lead.created_at), {
                                    addSuffix: true,
                                    locale: ptBR
                                  })}
                                </div>
                              </TableCell>

                              {/* Ações */}
                              <TableCell className="text-right">
                                <div className="flex items-center justify-end gap-2">
                                  {/* Botões de Aprovação para Add-ons */}
                                  {canApprove(lead) && (
                                    <>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="text-green-600 border-green-200 hover:bg-green-50"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleOpenApprovalDialog(lead, 'approve');
                                        }}
                                        disabled={approveAddonRequest.isPending}
                                      >
                                        <CheckCircle className="h-4 w-4" />
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="text-red-600 border-red-200 hover:bg-red-50"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleOpenApprovalDialog(lead, 'reject');
                                        }}
                                        disabled={approveAddonRequest.isPending}
                                      >
                                        <XCircle className="h-4 w-4" />
                                      </Button>
                                    </>
                                  )}
                                  
                                  {/* Botão de Ativação de Plano */}
                                  {canActivatePlan(lead) && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-blue-600 border-blue-200 hover:bg-blue-50"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleOpenActivationDialog(lead);
                                      }}
                                      disabled={activatePlanSubscription.isPending}
                                    >
                                      <Zap className="h-4 w-4 mr-1" />
                                      Ativar
                                    </Button>
                                  )}
                                  
                                  {/* Badge de Status de Aprovação */}
                                  {isAddonLead(lead) && (
                                    <ApprovalStatusBadge 
                                      approvalStatus={lead.approval_status}
                                      activationStatus={lead.activation_status}
                                      className="mr-2"
                                    />
                                  )}
                                  
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleLeadClick(lead);
                                    }}
                                  >
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleUpdateLead(lead);
                                    }}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-blue-600 hover:bg-blue-50"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      e.preventDefault();
                                      if (resendingEmailLeadId !== lead.id) {
                                        handleResendEmail(lead.id);
                                      }
                                    }}
                                    disabled={resendingEmailLeadId === lead.id}
                                    title="Reenviar Email Comercial"
                                  >
                                    {resendingEmailLeadId === lead.id ? (
                                      <Loader2 className="h-4 w-4 animate-spin" />
                                    ) : (
                                      <Send className="h-4 w-4" />
                                    )}
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                    
                    {leads?.length === 0 && (
                      <div className="text-center py-8">
                        <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium">Nenhum lead encontrado</h3>
                        <p className="text-muted-foreground">
                          Tente ajustar os filtros ou aguarde novos leads de upgrade.
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Dialog de Detalhes do Lead */}
          <Dialog open={showLeadDetail} onOpenChange={setShowLeadDetail}>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Detalhes do Lead
                </DialogTitle>
                <DialogDescription>
                  Informações completas do lead comercial
                </DialogDescription>
              </DialogHeader>
              
              {selectedLead && (
                <div className="space-y-6">
                  {/* DEBUG: Dados Brutos para Diagnóstico */}
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h4 className="font-semibold text-yellow-800 mb-2">🔍 Debug - Dados Disponíveis</h4>
                    <div className="text-xs space-y-1 text-yellow-700">
                      <p><strong>Lead ID:</strong> {selectedLead.id}</p>
                      <p><strong>User ID:</strong> {selectedLead.user_id}</p>
                      <p><strong>Company ID:</strong> {selectedLead.company_id}</p>
                      <p><strong>Contact Info (JSONB):</strong> {selectedLead.contact_info ? JSON.stringify(selectedLead.contact_info) : 'null'}</p>
                      <p><strong>Profiles Join:</strong> {selectedLead.profiles ? JSON.stringify(selectedLead.profiles) : 'null'}</p>
                      <p><strong>User Join:</strong> {selectedLead.user ? JSON.stringify(selectedLead.user) : 'null'}</p>
                      <p><strong>Companies Join:</strong> {selectedLead.companies ? JSON.stringify(selectedLead.companies) : 'null'}</p>
                      <p><strong>Company Join:</strong> {selectedLead.company ? JSON.stringify(selectedLead.company) : 'null'}</p>
                      <p><strong>Selected Plan:</strong> {selectedLead.selected_plan ? JSON.stringify(selectedLead.selected_plan) : 'null'}</p>
                      <p><strong>Selected Addons:</strong> {selectedLead.selected_addons ? JSON.stringify(selectedLead.selected_addons) : 'null'}</p>
                    </div>
                  </div>

                  {/* Informações Básicas - Versão Melhorada */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-semibold flex items-center gap-2">
                        <User className="h-4 w-4" />
                        Informações do Cliente
                      </h4>
                      <div className="space-y-2">
                        <p><strong>Nome:</strong> <span className="text-blue-600">{
                          selectedLead.contact_info?.name || 
                          selectedLead.user?.full_name || 
                          selectedLead.profiles?.full_name ||
                          'Não informado'
                        }</span></p>
                        <p><strong>Email:</strong> <span className="text-blue-600">{
                          selectedLead.contact_info?.email || 
                          selectedLead.user?.email || 
                          selectedLead.profiles?.email ||
                          'Não informado'
                        }</span></p>
                        <p><strong>Empresa:</strong> <span className="text-blue-600">{
                          selectedLead.contact_info?.company || 
                          selectedLead.company?.name || 
                          selectedLead.companies?.name ||
                          'Não informado'
                        }</span></p>
                        <p><strong>Telefone:</strong> <span className="text-blue-600">{
                          selectedLead.contact_info?.phone ||
                          'Não informado'
                        }</span></p>
                        <div><strong>Status:</strong> {getStatusBadge(selectedLead.status, selectedLead)}</div>
                        {selectedLead.request_type && (
                          <div><strong>Tipo de Request:</strong> <Badge variant="outline">{selectedLead.request_type}</Badge></div>
                        )}
                        {selectedLead.request_priority && (
                          <div><strong>Prioridade:</strong> <Badge variant={selectedLead.request_priority <= 3 ? 'destructive' : selectedLead.request_priority <= 6 ? 'default' : 'secondary'}>
                            {selectedLead.request_priority}/10
                          </Badge></div>
                        )}
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-semibold flex items-center gap-2">
                        <Crown className="h-4 w-4" />
                        Plano Selecionado
                      </h4>
                      <div className="space-y-2">
                        <p><strong>Plano Solicitado:</strong> <span className="text-green-600">{
                          selectedLead.selected_plan?.name || 
                          selectedLead.selected_plan?.id ||
                          'Não especificado'
                        }</span></p>
                        {selectedLead.current_plan_id && (
                          <p><strong>Plano Atual:</strong> <span className="text-gray-600">{
                            selectedLead.current_plan?.name || selectedLead.current_plan_id
                          }</span></p>
                        )}
                        <p><strong>Valor Mensal:</strong> <span className="text-green-600 font-semibold">
                          {formatCurrency(selectedLead.estimated_monthly_value || 0)}
                        </span></p>
                        {selectedLead.selected_addons && selectedLead.selected_addons.length > 0 && (
                          <div>
                            <strong>Add-ons Selecionados:</strong>
                            <div className="mt-2 space-y-2">
                              {selectedLead.selected_addons.map((addon: any, index: number) => (
                                <div key={index} className="bg-green-50 border border-green-200 rounded p-2 text-sm">
                                  <div className="flex justify-between items-center">
                                    <span className="font-medium">{addon.name || `Add-on ${index + 1}`}</span>
                                    <span className="text-green-600 font-semibold">
                                      {addon.price ? formatCurrency(addon.price) : 'Valor não informado'}
                                    </span>
                                  </div>
                                  {addon.description && (
                                    <p className="text-gray-600 mt-1">{addon.description}</p>
                                  )}
                                  {addon.id && (
                                    <p className="text-xs text-gray-500">ID: {addon.id}</p>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Contexto do Upgrade */}
                  <div className="border rounded-lg p-4 bg-slate-50">
                    <h4 className="font-semibold flex items-center gap-2 mb-3">
                      <Settings className="h-4 w-4" />
                      Contexto do Upgrade
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Origem</p>
                        <p className="font-medium">{selectedLead.source_context || 'Não informado'}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Feature Específica</p>
                        <p className="font-medium">{selectedLead.source_feature || 'Não informado'}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Criado em</p>
                        <p className="font-medium">{new Date(selectedLead.created_at).toLocaleDateString('pt-BR')}</p>
                      </div>
                      {selectedLead.commercial_urgency_notes && (
                        <div className="md:col-span-3">
                          <p className="text-muted-foreground">Notas de Urgência</p>
                          <p className="font-medium text-orange-600">{selectedLead.commercial_urgency_notes}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Período de Cortesia */}
                  {selectedLead.courtesy_period_start && (
                    <div className="border rounded-lg p-4 bg-muted/50">
                      <h4 className="font-semibold flex items-center gap-2 mb-3">
                        <Gift className="h-4 w-4" />
                        Período de Cortesia
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <p className="text-sm text-muted-foreground">Início</p>
                          <p>{new Date(selectedLead.courtesy_period_start).toLocaleDateString('pt-BR')}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Fim</p>
                          <p>{new Date(selectedLead.courtesy_period_end!).toLocaleDateString('pt-BR')}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Status</p>
                          {getCourtesyStatus(selectedLead) && (
                            <Badge variant={getCourtesyStatus(selectedLead)!.variant as any}>
                              {getCourtesyStatus(selectedLead)!.label}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Dados de Consolidação */}
                  {(selectedLead.is_consolidated || selectedLead.consolidation_group_id || selectedLead.previous_request_id) && (
                    <div className="border rounded-lg p-4 bg-purple-50 border-purple-200">
                      <h4 className="font-semibold flex items-center gap-2 mb-3 text-purple-800">
                        <Target className="h-4 w-4" />
                        Informações de Consolidação
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-purple-600">Lead Consolidado</p>
                          <p className="font-medium">{selectedLead.is_consolidated ? 'Sim' : 'Não'}</p>
                        </div>
                        {selectedLead.consolidation_group_id && (
                          <div>
                            <p className="text-purple-600">Grupo de Consolidação</p>
                            <p className="font-medium font-mono text-xs">{selectedLead.consolidation_group_id}</p>
                          </div>
                        )}
                        {selectedLead.previous_request_id && (
                          <div>
                            <p className="text-purple-600">Request Anterior</p>
                            <p className="font-medium font-mono text-xs">{selectedLead.previous_request_id}</p>
                          </div>
                        )}
                        {selectedLead.decision_timeline_days && (
                          <div>
                            <p className="text-purple-600">Timeline de Decisão</p>
                            <p className="font-medium">{selectedLead.decision_timeline_days} dias</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Informações de Aprovação de Add-ons */}
                  {isAddonLead(selectedLead) && (
                    <div className="border rounded-lg p-4 bg-blue-50 border-blue-200">
                      <h4 className="font-semibold flex items-center gap-2 mb-3 text-blue-800">
                        <CheckCircle className="h-4 w-4" />
                        Status de Aprovação de Add-ons
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-blue-600">Status de Aprovação</p>
                          <div className="mt-1">
                            <ApprovalStatusBadge 
                              approvalStatus={selectedLead.approval_status}
                              activationStatus={selectedLead.activation_status}
                            />
                          </div>
                        </div>
                        
                        {selectedLead.approved_by && (
                          <div>
                            <p className="text-blue-600">Aprovado por</p>
                            <p className="font-medium">{selectedLead.approved_by}</p>
                          </div>
                        )}
                        
                        {selectedLead.approved_at && (
                          <div>
                            <p className="text-blue-600">Data de Aprovação</p>
                            <p className="font-medium">{new Date(selectedLead.approved_at).toLocaleDateString('pt-BR', {
                              day: '2-digit',
                              month: '2-digit', 
                              year: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}</p>
                          </div>
                        )}
                        
                        {selectedLead.activated_at && (
                          <div>
                            <p className="text-blue-600">Data de Ativação</p>
                            <p className="font-medium">{new Date(selectedLead.activated_at).toLocaleDateString('pt-BR', {
                              day: '2-digit',
                              month: '2-digit',
                              year: 'numeric', 
                              hour: '2-digit',
                              minute: '2-digit'
                            })}</p>
                          </div>
                        )}
                        
                        {selectedLead.approval_notes && (
                          <div className="md:col-span-2">
                            <p className="text-blue-600">Notas da Aprovação</p>
                            <p className="font-medium bg-white p-2 rounded border">{selectedLead.approval_notes}</p>
                          </div>
                        )}
                        
                        {selectedLead.activation_details && (
                          <div className="md:col-span-2">
                            <p className="text-blue-600">Detalhes da Ativação</p>
                            <div className="bg-white p-2 rounded border font-mono text-xs">
                              {typeof selectedLead.activation_details === 'string' 
                                ? selectedLead.activation_details 
                                : JSON.stringify(selectedLead.activation_details, null, 2)}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Dados Adicionais do Sistema */}
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <h4 className="font-semibold flex items-center gap-2 mb-3">
                      <FileText className="h-4 w-4" />
                      Dados Técnicos
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">ID do Lead</p>
                        <p className="font-mono text-xs">{selectedLead.id}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Criado em</p>
                        <p>{new Date(selectedLead.created_at).toLocaleString('pt-BR')}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Última Atualização</p>
                        <p>{new Date(selectedLead.updated_at).toLocaleString('pt-BR')}</p>
                      </div>
                      {selectedLead.activation_status && (
                        <div>
                          <p className="text-muted-foreground">Status de Ativação</p>
                          <Badge variant="outline">{selectedLead.activation_status}</Badge>
                        </div>
                      )}
                      {selectedLead.current_addons && (
                        <div className="md:col-span-2">
                          <p className="text-muted-foreground">Add-ons Atuais</p>
                          <p className="text-xs font-mono bg-white p-2 rounded border">
                            {JSON.stringify(selectedLead.current_addons, null, 2)}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Histórico Comercial */}
                  <div className="space-y-4">
                    <h4 className="font-semibold flex items-center gap-2">
                      <MessageSquare className="h-4 w-4" />
                      Histórico Comercial
                      {isLoadingHistory && (
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent" />
                      )}
                    </h4>
                    
                    {/* Status Atual do Processo */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-blue-600">Status Atual</p>
                          <div className="font-medium">{getStatusBadge(selectedLead.status)}</div>
                        </div>
                        {selectedLead.commercial_contacted_at && (
                          <div>
                            <p className="text-blue-600">Primeiro Contato</p>
                            <p className="font-medium">{new Date(selectedLead.commercial_contacted_at).toLocaleDateString('pt-BR')}</p>
                          </div>
                        )}
                        {selectedLead.commercial_method && (
                          <div>
                            <p className="text-blue-600">Método de Contato</p>
                            <Badge variant="outline">{selectedLead.commercial_method}</Badge>
                          </div>
                        )}
                      </div>
                      
                      {selectedLead.commercial_notes && (
                        <div className="mt-4">
                          <p className="text-blue-600 text-sm">Notas Comerciais</p>
                          <p className="bg-white p-3 rounded border text-sm mt-1">{selectedLead.commercial_notes}</p>
                        </div>
                      )}
                    </div>

                    {/* ✅ HISTÓRICO COMERCIAL AMIGÁVEL */}
                    {leadHistory && leadHistory.length > 0 ? (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h5 className="font-medium text-green-800 mb-4 flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          Histórico Comercial ({leadHistory.length})
                        </h5>
                        <div className="space-y-3">
                          {leadHistory.map((history, index) => {
                            // 🎯 Processar mudanças de forma amigável
                            const getChangesSummary = (prev: any, current: any) => {
                              const changes = [];
                              
                              if (prev?.status !== current?.status) {
                                const statusMap = {
                                  'pending': 'Pendente',
                                  'contacted': 'Contatado',
                                  'negotiating': 'Em Negociação',
                                  'converted': 'Convertido',
                                  'lost': 'Perdido',
                                  'courtesy_expired': 'Cortesia Expirada',
                                  'cancelled': 'Cancelado'
                                };
                                const fromStatus = prev?.status ? statusMap[prev.status] || prev.status : 'Não definido';
                                const toStatus = statusMap[current?.status] || current?.status || 'Não definido';
                                changes.push(`Status: ${fromStatus} → ${toStatus}`);
                              }
                              
                              if (prev?.commercial_method !== current?.commercial_method) {
                                const methodMap = {
                                  'phone': 'Telefone',
                                  'email': 'Email',
                                  'whatsapp': 'WhatsApp',
                                  'video_call': 'Videochamada',
                                  'meeting': 'Reunião'
                                };
                                const fromMethod = prev?.commercial_method ? methodMap[prev.commercial_method] || prev.commercial_method : 'Não definido';
                                const toMethod = methodMap[current?.commercial_method] || current?.commercial_method || 'Não definido';
                                
                                if (!prev?.commercial_method && current?.commercial_method) {
                                  changes.push(`Método de contato definido: ${toMethod}`);
                                } else {
                                  changes.push(`Método: ${fromMethod} → ${toMethod}`);
                                }
                              }
                              
                              if (prev?.commercial_notes !== current?.commercial_notes && current?.commercial_notes) {
                                const notesPreview = current.commercial_notes.length > 100 
                                  ? current.commercial_notes.substring(0, 100) + '...'
                                  : current.commercial_notes;
                                changes.push(`Notas comerciais: "${notesPreview}"`);
                              }
                              
                              if (prev?.commercial_contacted_at !== current?.commercial_contacted_at && current?.commercial_contacted_at) {
                                changes.push('Primeiro contato registrado');
                              }
                              
                              return changes;
                            };
                            
                            const changes = getChangesSummary(history.previous_state, history.current_state);
                            const actionIcon = history.action_type === 'updated' ? MessageSquare : 
                                             history.action_type === 'contacted' ? Phone :
                                             history.action_type === 'created' ? Plus : Edit;
                            
                            return (
                              <div key={history.id || index} className="bg-white p-4 rounded-lg border hover:shadow-md transition-shadow">
                                <div className="flex items-center justify-between mb-3">
                                  <div className="flex items-center gap-3">
                                    <div className="p-2 bg-blue-100 rounded-full">
                                      {React.createElement(actionIcon, { className: "h-4 w-4 text-blue-600" })}
                                    </div>
                                    <div>
                                      <p className="font-medium text-gray-900">
                                        {history.action_type === 'updated' ? 'Lead Atualizado' :
                                         history.action_type === 'contacted' ? 'Cliente Contatado' :
                                         history.action_type === 'created' ? 'Lead Criado' : 'Ação Realizada'}
                                      </p>
                                      <p className="text-sm text-gray-500">
                                        {new Date(history.created_at).toLocaleDateString('pt-BR', {
                                          day: '2-digit',
                                          month: '2-digit',
                                          year: 'numeric',
                                          hour: '2-digit',
                                          minute: '2-digit'
                                        })}
                                        {history.performed_by && ` • por ${history.performed_by}`}
                                        {history.system_automated && ' • Automático'}
                                      </p>
                                    </div>
                                  </div>
                                  
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      const detailsElement = document.getElementById(`details-${history.id}`);
                                      if (detailsElement) {
                                        detailsElement.style.display = detailsElement.style.display === 'none' ? 'block' : 'none';
                                      }
                                    }}
                                    className="text-xs"
                                  >
                                    Ver Detalhes
                                  </Button>
                                </div>
                                
                                {/* 📋 Resumo das Mudanças */}
                                {changes.length > 0 && (
                                  <div className="mb-3">
                                    <p className="text-sm font-medium text-gray-700 mb-2">Alterações realizadas:</p>
                                    <ul className="space-y-1">
                                      {changes.map((change, idx) => (
                                        <li key={idx} className="text-sm text-gray-600 flex items-center gap-2">
                                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full flex-shrink-0" />
                                          {change}
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                                
                                {/* 💬 Notas Comerciais Destacadas */}
                                {(() => {
                                  const current = history.current_state;
                                  const prev = history.previous_state;
                                  
                                  return current?.commercial_notes && (prev?.commercial_notes !== current?.commercial_notes) && (
                                    <div className="bg-blue-50 border border-blue-200 rounded p-3 mb-3">
                                      <p className="text-sm text-blue-800">
                                        <strong>📝 Nota adicionada:</strong>
                                      </p>
                                      <p className="text-sm text-blue-700 mt-1 italic">
                                        "{current.commercial_notes}"
                                      </p>
                                    </div>
                                  );
                                })()}

                                {/* 💬 Contexto de Negócio */}
                                {history.business_context && (
                                  <div className="bg-amber-50 border border-amber-200 rounded p-3 mb-3">
                                    <p className="text-sm text-amber-800">
                                      <strong>Contexto:</strong> {history.business_context}
                                    </p>
                                  </div>
                                )}
                                
                                {/* 🔍 Detalhes Técnicos (Ocultos por padrão) */}
                                <div id={`details-${history.id}`} style={{ display: 'none' }} className="border-t pt-3 mt-3">
                                  <p className="text-xs font-medium text-gray-600 mb-2">Detalhes técnicos:</p>
                                  
                                  {history.action_details && (
                                    <div className="mb-3">
                                      <p className="text-xs text-gray-500 mb-1">Ação:</p>
                                      <div className="bg-gray-50 p-2 rounded text-xs font-mono">
                                        {typeof history.action_details === 'string' 
                                          ? history.action_details 
                                          : JSON.stringify(history.action_details, null, 2)}
                                      </div>
                                    </div>
                                  )}
                                  
                                  {history.previous_state && history.current_state && (
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                      <div>
                                        <p className="text-xs text-red-700 font-medium mb-1">Estado Anterior:</p>
                                        <div className="bg-red-50 p-2 rounded border text-xs font-mono text-red-700 max-h-32 overflow-y-auto">
                                          {JSON.stringify(history.previous_state, null, 2)}
                                        </div>
                                      </div>
                                      <div>
                                        <p className="text-xs text-green-700 font-medium mb-1">Estado Atual:</p>
                                        <div className="bg-green-50 p-2 rounded border text-xs font-mono text-green-700 max-h-32 overflow-y-auto">
                                          {JSON.stringify(history.current_state, null, 2)}
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    ) : (
                      !isLoadingHistory && (
                        <Alert>
                          <AlertTriangle className="h-4 w-4" />
                          <AlertTitle>Sem Histórico Comercial</AlertTitle>
                          <AlertDescription>
                            Este lead ainda não teve nenhuma interação comercial registrada. 
                            É um lead novo que precisa de primeira abordagem.
                          </AlertDescription>
                        </Alert>
                      )
                    )}

                    {/* Alertas de Risco (mantido) */}
                    {selectedLead.risk_alerts && Object.keys(selectedLead.risk_alerts).length > 0 && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h5 className="font-medium text-red-800 mb-2 flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4" />
                          Alertas de Risco
                        </h5>
                        <div className="text-xs font-mono bg-white p-3 rounded border">
                          {JSON.stringify(selectedLead.risk_alerts, null, 2)}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Ações */}
                  <div className="flex justify-end gap-2 pt-4 border-t">
                    <Button variant="outline" onClick={() => setShowLeadDetail(false)}>
                      Fechar
                    </Button>
                    <Button onClick={() => {
                      setShowLeadDetail(false);
                      handleUpdateLead(selectedLead);
                    }}>
                      <Edit className="h-4 w-4 mr-2" />
                      Editar Lead
                    </Button>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>

          {/* Dialog de Atualização do Lead */}
          <Dialog open={showUpdateDialog} onOpenChange={setShowUpdateDialog}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Edit className="h-5 w-5" />
                  Atualizar Lead
                </DialogTitle>
                <DialogDescription>
                  Atualize o status e informações comerciais do lead
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="status-update">Status</Label>
                    <Select 
                      value={updateForm.status} 
                                                    onValueChange={handleUpdateStatusChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pending">Pendente</SelectItem>
                        <SelectItem value="contacted">Contatado</SelectItem>
                        <SelectItem value="negotiating">Em Negociação</SelectItem>
                        <SelectItem value="converted">Convertido</SelectItem>
                        <SelectItem value="lost">Perdido</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="method-update">Método de Contato</Label>
                    <Select 
                      value={updateForm.commercial_method} 
                                                    onValueChange={handleUpdateMethodChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Como foi contatado?" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="phone">Telefone</SelectItem>
                        <SelectItem value="email">Email</SelectItem>
                        <SelectItem value="whatsapp">WhatsApp</SelectItem>
                        <SelectItem value="video_call">Videochamada</SelectItem>
                        <SelectItem value="meeting">Reunião</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="commercial-notes">Notas Comerciais</Label>
                  <Textarea
                    id="commercial-notes"
                    placeholder="Detalhes do contato, objeções, próximos passos..."
                    value={updateForm.commercial_notes}
                                                  onChange={handleUpdateNotesChange}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="general-notes">Observações Gerais</Label>
                  <Textarea
                    id="general-notes"
                    placeholder="Outras observações sobre o lead..."
                    value={updateForm.notes}
                                                  onChange={handleUpdateCommercialNotesChange}
                    rows={2}
                  />
                </div>

                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={() => setShowUpdateDialog(false)}>
                    Cancelar
                  </Button>
                  <Button 
                    onClick={handleSubmitUpdate}
                    disabled={updateLead.isPending}
                  >
                    {updateLead.isPending ? "Salvando..." : "Salvar Alterações"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          {/* Dialog de Aprovação de Add-ons */}
          <Dialog open={showApprovalDialog} onOpenChange={setShowApprovalDialog}>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  {approvalAction === 'approve' ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-600" />
                  )}
                  {approvalAction === 'approve' ? 'Aprovar' : 'Rejeitar'} Add-ons
                </DialogTitle>
                <DialogDescription>
                  {approvalAction === 'approve' 
                    ? 'Confirme a aprovação dos add-ons solicitados. Eles serão ativados automaticamente.'
                    : 'Informe o motivo da rejeição dos add-ons solicitados.'
                  }
                </DialogDescription>
              </DialogHeader>
              
              {selectedLead && (
                <div className="space-y-4">
                  {/* Informações do Lead */}
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="font-medium">{selectedLead.contact_info?.name || selectedLead.profiles?.full_name || 'Cliente'}</p>
                    <p className="text-sm text-muted-foreground">{selectedLead.contact_info?.company || selectedLead.companies?.name}</p>
                  </div>

                  {/* Add-ons Solicitados */}
                  {selectedLead.selected_addons && selectedLead.selected_addons.length > 0 && (
                    <div className="space-y-2">
                      <Label>Add-ons Solicitados:</Label>
                      <div className="space-y-1">
                        {selectedLead.selected_addons.map((addon: any, index: number) => (
                          <div key={index} className="flex justify-between items-center p-2 bg-blue-50 rounded">
                            <span className="text-sm">{addon.name}</span>
                            <Badge variant="outline">{formatCurrency(addon.price || 0)}</Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Campo de Notas */}
                  <div className="space-y-2">
                    <Label htmlFor="approval-notes">
                      {approvalAction === 'approve' ? 'Notas da Aprovação (opcional)' : 'Motivo da Rejeição'}
                    </Label>
                    <Textarea
                      id="approval-notes"
                      placeholder={
                        approvalAction === 'approve' 
                          ? 'Observações sobre a aprovação...'
                          : 'Explique o motivo da rejeição...'
                      }
                      value={approvalNotes}
                      onChange={(e) => setApprovalNotes(e.target.value)}
                      rows={3}
                      required={approvalAction === 'reject'}
                    />
                  </div>

                  {/* Botões de Ação */}
                  <div className="flex justify-end gap-2 pt-4">
                    <Button variant="outline" onClick={() => setShowApprovalDialog(false)}>
                      Cancelar
                    </Button>
                    <Button 
                      onClick={handleSubmitApproval}
                      disabled={approveAddonRequest.isPending || (approvalAction === 'reject' && !approvalNotes.trim())}
                      className={approvalAction === 'approve' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
                    >
                      {approveAddonRequest.isPending ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Processando...
                        </>
                      ) : (
                        <>
                          {approvalAction === 'approve' ? (
                            <CheckCircle className="h-4 w-4 mr-2" />
                          ) : (
                            <XCircle className="h-4 w-4 mr-2" />
                          )}
                          {approvalAction === 'approve' ? 'Aprovar' : 'Rejeitar'}
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>

          {/* Modal de Ativação de Plano */}
          <Dialog open={showActivationDialog} onOpenChange={setShowActivationDialog}>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-blue-600" />
                  Ativar Assinatura
                </DialogTitle>
                <DialogDescription>
                  Ative o plano permanentemente e gere cobrança no sistema de billing
                </DialogDescription>
              </DialogHeader>

              {selectedLead && (
                <div className="space-y-4 py-4">
                  {/* Informações do Lead */}
                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-600">Cliente:</span>
                        <span className="text-sm font-semibold">{selectedLead.contact_info?.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-600">Plano:</span>
                        <span className="text-sm font-semibold">{selectedLead.selected_plan?.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-600">Valor:</span>
                        <span className="text-sm font-semibold text-green-600">
                          R$ {selectedLead.selected_plan?.price?.toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Campo de notas */}
                  <div className="space-y-2">
                    <Label htmlFor="activationNotes">
                      Notas da Ativação (opcional)
                    </Label>
                    <Textarea
                      id="activationNotes"
                      placeholder="Descreva detalhes da negociação, acordos especiais, etc..."
                      value={activationNotes}
                      onChange={(e) => setActivationNotes(e.target.value)}
                      className="min-h-[80px]"
                    />
                  </div>

                  {/* Botões de ação */}
                  <div className="flex gap-2 pt-4">
                    <Button
                      variant="outline"
                      onClick={() => setShowActivationDialog(false)}
                      className="flex-1"
                    >
                      Cancelar
                    </Button>
                    <Button
                      onClick={handleSubmitActivation}
                      disabled={activatePlanSubscription.isPending}
                      className="flex-1 bg-blue-600 hover:bg-blue-700"
                    >
                      {activatePlanSubscription.isPending ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Ativando...
                        </>
                      ) : (
                        <>
                          <Zap className="h-4 w-4 mr-2" />
                          Ativar Assinatura
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>
        </motion.div>
      </GenericPermissionGate>
    </AdminLayout>
    </MainLayout>
  );
} 