/**
 * <AUTHOR> Internet 2025
 */
import { useState } from "react";
import { AdminLayout } from "@/components/layout/AdminLayout";
import { MainLayout } from "@/components/layout";
import { HeroSection } from "@/components/common/HeroSection";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { GenericPermissionGate } from "@/components/auth/GenericPermissionGate";
import { VisualAssetWithPermissions, VisualCategory, VisualRarity } from "@/types/gamification.types";
import { useVisualAssetsAdmin } from "@/lib/query/hooks/useVisualAssetsAdmin";
import { Plus, Sparkles } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { toastWithNotification } from '@/lib/notifications/toastWithNotification';
import { motion } from "framer-motion";
import { Skeleton } from "@/components/ui/skeleton";

// Novos componentes extraídos
import { VisualAssetsTable } from "@/components/admin/VisualAssetsTable";
import { CreateAssetDialog } from "@/components/admin/CreateAssetDialog";
import { EditAssetDialog } from "@/components/admin/EditAssetDialog";
import { DeleteAssetDialog } from "@/components/admin/DeleteAssetDialog";
import { AssetFilters } from "@/components/admin/AssetFilters";

// Variantes de animação premium (mesmo padrão do AdminAll)
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.2,
    },
  },
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut",
    },
  },
};

export default function VisualAssets() {
  const [loading, setLoading] = useState(false);
  
  // React Query hooks
  const { data: assets = [], isLoading, isRefetching, refetch } = useVisualAssetsAdmin();
  
  // State para filtros e dialogs
  const [selectedCategory, setSelectedCategory] = useState<VisualCategory | 'all'>('all');
  const [selectedRarity, setSelectedRarity] = useState<VisualRarity | 'all'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [editingAsset, setEditingAsset] = useState<VisualAssetWithPermissions | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [assetToDelete, setAssetToDelete] = useState<VisualAssetWithPermissions | null>(null);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [createDialogOpen, setCreateDialogOpen] = useState(false);


  const handleRefresh = async () => {
    try {
      await refetch();
      toastWithNotification('Assets Atualizados', {
        description: `${assets.length} assets carregados com sucesso.`,
      });
    } catch (err) {
      toastWithNotification('Erro ao atualizar assets', {
        description: (err as Error).message,
      });
    }
  };

  // Loading derivado do React Query
  const actualLoading = isLoading || loading;
  const refreshing = isRefetching;

  // Filtrar assets baseado nos filtros ativos
  const filteredAssets = assets.filter(asset => {
    const matchesCategory = selectedCategory === 'all' || asset.category === selectedCategory;
    const matchesRarity = selectedRarity === 'all' || asset.rarity === selectedRarity;
    const matchesSearch = searchTerm === '' || 
      asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesCategory && matchesRarity && matchesSearch;
  });

  // Handlers para os dialogs
  const handleEdit = (asset: VisualAssetWithPermissions) => {
    setEditingAsset(asset);
  };

  const handleDelete = (asset: VisualAssetWithPermissions) => {
    setAssetToDelete(asset);
    setDeleteDialogOpen(true);
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('all');
    setSelectedRarity('all');
  };

  // Loading state - mesmo padrão do AdminLocations
  if (actualLoading && assets.length === 0) {
    return (
      <MainLayout>
        <AdminLayout>
          <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
            <div className="container py-1 px-4 space-y-4">
              {/* Hero Section skeleton */}
              <div className="w-full h-48 bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 rounded-xl p-8 flex flex-col justify-center items-center text-white">
                <Skeleton className="h-8 w-64 mb-4 bg-white/20" />
                <Skeleton className="h-4 w-96 bg-white/20" />
              </div>

              {/* Search skeleton */}
              <Skeleton className="h-12 w-full" />

              {/* Filters skeleton */}
              <div className="bg-white/80 backdrop-blur-sm p-4 rounded-xl shadow-sm border border-gray-200">
                <div className="flex flex-wrap gap-3">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <Skeleton key={i} className="h-10 w-32" />
                  ))}
                </div>
              </div>

              {/* Content skeleton */}
              <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-gray-200 p-6">
                <Skeleton className="h-8 w-48 mb-4" />
                <Skeleton className="h-64 w-full" />
              </div>
            </div>
          </div>
        </AdminLayout>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <AdminLayout>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
          <div className="container py-1 px-4 space-y-4">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {/* HeroSection Premium - mesmo padrão do AdminLocations */}
              <motion.div variants={cardVariants}>
                <HeroSection
                  title="Assets Visuais Via-Láctea"
                  description="Gerencie personalizações premium e assets visuais do sistema de gamificação"
                  icon={Sparkles}
                  gradientColors="from-purple-600 via-indigo-600 to-blue-600"
                  iconAnimation={true}
                  actions={
                    <GenericPermissionGate 
                      resourceTypeKey="visual_assets" 
                      actionKey="create"
                      fallbackComponent={null}
                    >
                      <Button
                        onClick={() => setCreateDialogOpen(true)}
                        className="bg-white text-purple-700 hover:bg-white/90 border-white"
                        size="lg"
                      >
                        <Plus className="h-5 w-5 mr-2" />
                        Novo Asset
                      </Button>
                    </GenericPermissionGate>
                  }
                />
              </motion.div>

              {/* Filtros extraídos para componente */}
              <motion.div variants={cardVariants}>
                <AssetFilters
                  searchTerm={searchTerm}
                  onSearchChange={setSearchTerm}
                  selectedCategory={selectedCategory}
                  onCategoryChange={setSelectedCategory}
                  selectedRarity={selectedRarity}
                  onRarityChange={setSelectedRarity}
                  viewMode={viewMode}
                  onViewModeChange={setViewMode}
                  onRefresh={handleRefresh}
                  isRefreshing={refreshing}
                />
              </motion.div>

              {/* Conteúdo Principal com verificação de permissões */}
              <motion.div variants={cardVariants}>
                <GenericPermissionGate 
                  resourceTypeKey="admin" 
                  actionKey="manage_visual_assets"
                  fallbackComponent={
                    <Card>
                      <CardHeader>
                        <CardTitle>Acesso Negado</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p>Você não tem permissão para gerenciar assets visuais.</p>
                        <p className="text-sm text-gray-500 mt-2">Se você acredita que deveria ter acesso, por favor, contate um administrador do sistema.</p>
                      </CardContent>
                    </Card>
                  }
                >
                  <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
                    <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-t-lg border-b border-purple-100">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <CardTitle className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                            <Sparkles className="h-6 w-6 text-purple-600" />
                            Gerenciar Assets Visuais
                          </CardTitle>
                          <CardDescription className="text-gray-600">
                            Configure personalizações premium para a experiência Via-Láctea Plus
                          </CardDescription>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-purple-700 border-purple-200">
                            {filteredAssets.length} de {assets.length} Assets
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="p-6">
                      {/* Tabela extraída para componente */}
                      <VisualAssetsTable
                        assets={assets}
                        filteredAssets={filteredAssets}
                        actualLoading={actualLoading}
                        isRefreshing={isRefetching}
                        searchTerm={searchTerm}
                        selectedCategory={selectedCategory}
                        selectedRarity={selectedRarity}
                        viewMode={viewMode}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                        onClearFilters={handleClearFilters}
                      />
                    </CardContent>
                  </Card>
                </GenericPermissionGate>
              </motion.div>
            </motion.div>

            {/* Dialogs extraídos para componentes */}
        <DeleteAssetDialog
          asset={assetToDelete}
          open={deleteDialogOpen}
          onOpenChange={(open) => {
            setDeleteDialogOpen(open);
            if (!open) setAssetToDelete(null);
          }}
        />

        <EditAssetDialog
          asset={editingAsset}
          onOpenChange={(open) => !open && setEditingAsset(null)}
        />

            <CreateAssetDialog
              open={createDialogOpen}
              onOpenChange={setCreateDialogOpen}
            />
          </div>
        </div>
      </AdminLayout>
    </MainLayout>
  );
} 