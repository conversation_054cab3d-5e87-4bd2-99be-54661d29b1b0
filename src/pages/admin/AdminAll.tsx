/**
 * <PERSON><PERSON> de Controle - Interface administrativa principal
 * Layout modernizado para manter coerência visual com o Knowledge Hub
 * <AUTHOR> Internet 2025
 */
import { AdminLayout } from "@/components/layout/AdminLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Sparkles,
  Users,
  Shield,
  Building2,
  UserCog,
  Settings,
  Palette,
  Trophy,
  ClipboardCheck,
  Search,
  Building,
  Briefcase,
  MapPin,
  LayoutGrid,
  List,
  Grid3X3,
  Filter,
  ChevronRight,
  ArrowLeft,
  TrendingUp,
  Zap,
  HardDrive,
  Mail,
  CreditCard,
  FileText,
  Crown,
  Target,
  Brain,
  Timer,
  HelpCircle,
} from "lucide-react";
import { Link } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { HeroSection } from "@/components/common/HeroSection";
import { useIsVindulaCompany } from "@/lib/query/hooks/useIsVindulaCompany";
import { useAdminPermissions } from "@/lib/query/hooks/useAdminPermissions";
import { MainLayout } from "@/components/layout";
import { Skeleton } from "@/components/ui/skeleton";
import { useFeatureAvailability } from "@/lib/query/hooks/useFeatureFlags";

type AppRole = "admin" | "user" | "recursos_humanos" | "company_owner";

interface AdminFeature {
  title: string;
  description: string;
  icon: React.ElementType;
  href: string;
  category: string;
  adminOnly?: boolean;
  hrOnly?: boolean;
  vindulaOnly?: boolean; // Flag para funcionalidades específicas da Vindula
  color: string;
  featureKey?: string; // Feature flag para verificação automática
}

// Lista completa de todas as funcionalidades administrativas
const allAdminFeatures: AdminFeature[] = [
  // Usuários e Permissões
  {
    title: "Usuários",
    description: "Gerenciar usuários e convites",
    icon: Users,
    href: "/admin/users",
    category: "Usuários e Permissões",
    adminOnly: true,
    color: "from-blue-500 to-indigo-500",
  },
  {
    title: "Permissões",
    description: "Gerenciar funções e permissões",
    icon: Shield,
    href: "/admin/roles",
    category: "Usuários e Permissões",
    adminOnly: true,
    color: "from-emerald-500 to-green-500",
  },

  // Empresa e Organização
  {
    title: "Empresa",
    description: "Configurações da empresa",
    icon: Building2,
    href: "/admin/company",
    category: "Empresa e Organização",
    adminOnly: true,
    color: "from-purple-500 to-violet-500",
  },
  {
    title: "Departamentos",
    description: "Gerenciar departamentos da empresa",
    icon: Building,
    href: "/admin/departments",
    category: "Empresa e Organização",
    hrOnly: true,
    color: "from-cyan-500 to-blue-500",
  },
  {
    title: "Cargos",
    description: "Gerenciar cargos e funções",
    icon: Briefcase,
    href: "/admin/job-titles",
    category: "Empresa e Organização",
    hrOnly: true,
    color: "from-amber-500 to-orange-500",
  },
  {
    title: "Unidades",
    description: "Gerenciar unidades da empresa",
    icon: Building,
    href: "/admin/units",
    category: "Empresa e Organização",
    hrOnly: true,
    color: "from-teal-500 to-cyan-500",
  },
  {
    title: "Localizações",
    description: "Gerenciar localizações e endereços",
    icon: MapPin,
    href: "/admin/locations",
    category: "Empresa e Organização",
    hrOnly: true,
    color: "from-red-500 to-pink-500",
  },

  // Personalização e Engajamento
  {
    title: "Assets Visuais",
    description: "Gerenciar personalizações e assets visuais",
    icon: Palette,
    href: "/admin/visual-assets",
    category: "Personalização e Engajamento",
    adminOnly: true,
    color: "from-pink-500 to-rose-500",
  },
  {
    title: "Loja Stardust",
    description: "Gerenciar itens disponíveis na loja com Stardust",
    icon: Crown,
    href: "/admin/store-items",
    category: "Personalização e Engajamento",
    adminOnly: true,
    color: "from-emerald-500 to-teal-500",
  },
  {
    title: "Gamificação",
    description: "Gerenciar níveis, XP e recompensas",
    icon: Trophy,
    href: "/admin/gamification",
    category: "Personalização e Engajamento",
    adminOnly: true,
    color: "from-yellow-500 to-amber-500",
  },
  {
    title: "Missões",
    description: "Gerenciar missões e desafios para usuários",
    icon: Target,
    href: "/admin/missions",
    category: "Personalização e Engajamento",
    adminOnly: true,
    color: "from-emerald-500 to-teal-500",
    featureKey: "missions_feature",
  },

  // Utilização
  {
    title: "Gerenciamento de Planos",
    description: "Monitorar e gerenciar planos de assinatura",
    icon: Zap,
    href: "/plan-management",
    category: "Utilização",
    adminOnly: true,
    color: "from-emerald-500 to-teal-500",
  },
  {
    title: "Dashboard de Storage (Quebrado)",
    description: "Monitorar uso de armazenamento e comprar add-ons",
    icon: HardDrive,
    href: "/storage",
    category: "Utilização",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-teal-500 to-cyan-500",
  },
  {
    title: "Logs de Auditoria",
    description: "Monitoramento de segurança e atividades do sistema",
    icon: Shield,
    href: "/admin/audit-logs",
    category: "Utilização",
    adminOnly: true,
    color: "from-red-500 to-orange-500",
  },

  // Recursos Humanos
  {
    title: "Promoções",
    description: "Gerenciar promoções e avanços na carreira",
    icon: TrendingUp,
    href: "/admin/promotions",
    category: "Recursos Humanos",
    hrOnly: true,
    color: "from-amber-500 to-orange-500",
  },
  {
    title: "Gestão de Ausências",
    description: "Gerenciar ausências e aprovações dos colaboradores",
    icon: ClipboardCheck,
    href: "/admin/absences",
    category: "Recursos Humanos",
    hrOnly: true,
    color: "from-rose-500 to-pink-500",
  },

  // Configurações dos Tenants
  {
    title: "Analytics de IA",
    description: "Monitoramento global de uso e performance das funcionalidades de IA",
    icon: Brain,
    href: "/admin/ai-analytics",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-purple-600 to-indigo-600",
  },
  {
    title: "Monitoramento de Trials",
    description: "Dashboard de automação e acompanhamento de períodos de teste",
    icon: Timer,
    href: "/admin/trial-monitoring",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-blue-600 to-indigo-600",
  },
  {
    title: "Dashboard de Emails",
    description: "Gerenciamento completo do sistema de emails contextuais",
    icon: Mail,
    href: "/admin/emails",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-blue-600 to-purple-600",
  },
  {
    title: "Monitoramento Proativo",
    description: "Sistema de detecção de riscos e intervenções automáticas",
    icon: Shield,
    href: "/admin/monitoring",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-emerald-600 to-teal-600",
  },
  {
    title: "Gerenciamento de Tenants",
    description: "Gerenciar empresas clientes e configurações de tenant",
    icon: Settings,
    href: "/admin/tenants",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-blue-600 to-purple-600",
  },
  {
    title: "Configurações de Email (Quebrado)",
    description: "Configurações gerais do sistema de email",
    icon: Mail,
    href: "/admin/email-settings",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-blue-500 to-cyan-500",
  },
  {
    title: "Contextual Email Manager",
    description: "Gerenciar templates de email contextuais",
    icon: Mail,
    href: "/admin/contextual-emails",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-blue-500 to-cyan-500",
  },
  {
    title: "Gerenciamento de Licenças",
    description: "Gerenciar licenças e assinaturas",
    icon: ClipboardCheck,
    href: "/admin/licenses",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-blue-600 to-purple-600",
  },
  {
    title: "Gerenciamento de Templates de Email",
    description: "Criar e gerenciar templates de email",
    icon: Mail,
    href: "/admin/email-templates",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-blue-500 to-cyan-500",
  },
  {
    title: "Sistema de Cobrança",
    description: "Gerenciar histórico de cobrança e faturamento",
    icon: CreditCard,
    href: "/admin/billing",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-emerald-500 to-teal-500",
  },
  {
    title: "Marketplace Estratégico",
    description: "Gerenciar categorias e itens do marketplace",
    icon: Crown,
    href: "/admin/marketplace",
    category: "Personalização e Engajamento",
    adminOnly: true,
    color: "from-purple-600 to-pink-600",
  },
  {
    title: "Templates Globais",
    description: "Gerenciar templates de conhecimento para todos os clientes",
    icon: FileText,
    href: "/admin/global-templates",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-indigo-600 to-blue-600",
  },
  {
    title: "Packs de Emojis",
    description: "Gerenciar packs de emojis premium para o sistema",
    icon: Sparkles,
    href: "/admin/reaction-packs",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-purple-600 to-pink-600",
  },
  {
    title: "Configurações Padrão de XP",
    description: "Gerenciar configurações globais de XP aplicadas a todas as empresas",
    icon: Trophy,
    href: "/admin/default-xp-actions",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-yellow-600 to-amber-600",
  },
  {
    title: "Configurações Padrão de Stardust",
    description: "Gerenciar configurações globais de Stardust aplicadas a todas as empresas",
    icon: Sparkles,
    href: "/admin/default-stardust-actions",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-purple-600 to-indigo-600",
  },
  {
    title: "Navegação Aplicativo Mobile",
    description: "Configurar navegação mobile com feature flags por tier",
    icon: Settings,
    href: "/admin/floating-tab-bar",
    category: "Personalização e Engajamento",
    adminOnly: true,
    color: "from-blue-500 to-cyan-500",
  },
  {
    title: "Domínios Públicos Bloqueados",
    description: "Gerenciar domínios públicos que não podem ser usados para OAuth corporativo",
    icon: Shield,
    href: "/admin/public-domains",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-red-500 to-orange-500",
  },
  {
    title: "Central de Ajuda",
    description: "Gerenciar conteúdo dinâmico da Central de Ajuda para todos os clientes",
    icon: HelpCircle,
    href: "/admin/help-content",
    category: "Configurações dos Tenants",
    adminOnly: true,
    vindulaOnly: true,
    color: "from-blue-500 to-cyan-500",
  },
];

// Categorias para filtrar
const categories = [
  {
    name: "Todos",
    icon: Grid3X3,
    color: "from-slate-500 to-gray-500",
  },
  {
    name: "Usuários e Permissões",
    icon: Users,
    color: "from-blue-500 to-indigo-500",
  },
  {
    name: "Empresa e Organização",
    icon: Building2,
    color: "from-purple-500 to-violet-500",
  },
  {
    name: "Personalização e Engajamento",
    icon: Palette,
    color: "from-pink-500 to-rose-500",
  },
  {
    name: "Utilização",
    icon: TrendingUp,
    color: "from-emerald-500 to-teal-500",
  },
  {
    name: "Recursos Humanos",
    icon: Users,
    color: "from-amber-500 to-orange-500",
  },
  {
    name: "Configurações dos Tenants",
    icon: Settings,
    color: "from-violet-500 to-purple-500",
  },
];

// Variantes de animação premium
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.2,
    },
  },
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut",
    },
  },
};

export default function AdminAll() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("Todos");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Verificar se é empresa Vindula para funcionalidades específicas
  const { isVindulaCompany } = useIsVindulaCompany();

  // 🔥 VIOLAÇÃO CRÍTICA ELIMINADA: useQuery inline substituído por hook centralizado
  const {
    showHRContent,
    showAdminContent,
    userRole,
    isLoading: isLoadingPermissions,
    isError: hasPermissionError,
    error: permissionError,
  } = useAdminPermissions();

  // Filtrar funcionalidades com base nas permissões, categoria e termo de busca
  const filteredFeatures = allAdminFeatures.filter(feature => {
    // Filtrar por permissões
    if ((feature.adminOnly && !showAdminContent) || (feature.hrOnly && !showHRContent)) {
      return false;
    }

    // Filtrar por empresa Vindula (apenas para funcionalidades específicas)
    if (feature.vindulaOnly && !isVindulaCompany) {
      return false;
    }

    // Filtrar por categoria
    if (selectedCategory !== "Todos" && feature.category !== selectedCategory) {
      return false;
    }

    // Filtrar por termo de busca
    if (
      searchTerm &&
      !feature.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
      !feature.description.toLowerCase().includes(searchTerm.toLowerCase())
    ) {
      return false;
    }

    return true;
  });

  // 🎯 LOADING PATTERN CORRETO - Verificar permissões antes de renderizar
  if (isLoadingPermissions) {
    return (
      <MainLayout>
        <AdminLayout>
          <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
            <div className="container py-1 px-4 space-y-4">
              {/* Hero Section skeleton */}
              <div className="w-full h-48 bg-gradient-to-r from-orange-600 via-amber-600 to-yellow-600 rounded-xl p-8 flex flex-col justify-center items-center text-white">
                <Skeleton className="h-8 w-64 mb-4 bg-white/20" />
                <Skeleton className="h-4 w-96 bg-white/20" />
              </div>

              {/* Search skeleton */}
              <Skeleton className="h-12 w-full" />

              {/* Filters skeleton */}
              <div className="bg-white/80 backdrop-blur-sm p-4 rounded-xl shadow-sm border border-gray-200">
                <div className="flex flex-wrap gap-3">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <Skeleton key={i} className="h-10 w-32" />
                  ))}
                </div>
              </div>

              {/* Grid skeleton */}
              <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {Array.from({ length: 12 }).map((_, i) => (
                  <Skeleton key={i} className="h-32 w-full rounded-xl" />
                ))}
              </div>
            </div>
          </div>
        </AdminLayout>
      </MainLayout>
    );
  }

  // 🚨 ERROR STATE - Mostrar erro se permissões falharam
  if (hasPermissionError) {
    return (
      <MainLayout>
        <AdminLayout>
          <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-red-600 mb-2">Erro ao carregar permissões</h2>
              <p className="text-gray-600 mb-4">
                {permissionError?.message ||
                  "Erro desconhecido ao verificar permissões administrativas"}
              </p>
              <Button onClick={() => window.location.reload()} variant="outline">
                Tentar novamente
              </Button>
            </div>
          </div>
        </AdminLayout>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <AdminLayout>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
          <div className="container py-1 px-4 space-y-4">
            <motion.div variants={containerVariants} initial="hidden" animate="visible">
              {/* Hero Section usando o componente reutilizável */}
              <HeroSection
                title="Painel de Controle"
                description="Gerencie usuários, permissões e configurações do sistema"
                icon={Settings}
                gradientColors="from-orange-600 via-amber-600 to-yellow-600"
                iconAnimation={true}
                actions={
                  <div className="flex gap-2">
                    <Button
                      onClick={() => navigate("/feed")}
                      className="bg-white/15 hover:bg-white/25 text-white border-white/30 backdrop-blur-sm"
                      size="lg"
                    >
                      <ArrowLeft className="h-5 w-5 mr-2" />
                      Voltar
                    </Button>
                    <Button
                      onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
                      className="bg-white text-orange-700 hover:bg-white/90 border-white"
                      size="lg"
                    >
                      {viewMode === "grid" ? (
                        <List className="h-5 w-5 mr-2" />
                      ) : (
                        <Grid3X3 className="h-5 w-5 mr-2" />
                      )}
                      {viewMode === "grid" ? "Lista" : "Grade"}
                    </Button>
                  </div>
                }
              />

              {/* Barra de Pesquisa e Filtros */}
              <motion.div variants={cardVariants} className="space-y-4 mb-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    type="search"
                    placeholder="Buscar funcionalidades..."
                    className="pl-10 bg-white/80 backdrop-blur-sm border-gray-200 focus-visible:ring-orange-500 h-12 text-base"
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                  />
                </div>

                {/* Filtros de categoria elegantes */}
                <div className="bg-white/80 backdrop-blur-sm p-4 rounded-xl shadow-sm border border-gray-200 mb-4">
                  <div className="flex flex-wrap gap-3 w-full">
                    {categories
                      .filter(category => {
                        // Se não for categoria "Configurações dos Tenants", sempre mostrar
                        if (category.name !== "Configurações dos Tenants") {
                          return true;
                        }
                        
                        // Para "Configurações dos Tenants", só mostrar se tem pelo menos um item visível
                        const hasVisibleItems = allAdminFeatures.some(feature => 
                          feature.category === "Configurações dos Tenants" &&
                          (!feature.vindulaOnly || isVindulaCompany) &&
                          (!feature.adminOnly || showAdminContent) &&
                          (!feature.hrOnly || showHRContent)
                        );
                        
                        return hasVisibleItems;
                      })
                      .map((category, index) => {
                        const IconComponent = category.icon;
                        return (
                          <motion.div
                            key={category.name}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3, delay: 0.3 + index * 0.05 }}
                          >
                            <Button
                              variant={selectedCategory === category.name ? "default" : "outline"}
                              className={`cursor-pointer ${
                                selectedCategory === category.name
                                  ? `bg-gradient-to-r ${category.color} hover:opacity-90 text-white border-0`
                                  : "hover:bg-orange-50 border-orange-200"
                              } px-4 py-2 h-auto font-medium transition-all`}
                              onClick={() => setSelectedCategory(category.name)}
                            >
                              <IconComponent className="h-4 w-4 mr-2" />
                              {category.name}
                            </Button>
                          </motion.div>
                        );
                      })
                    }
                  </div>
                </div>
              </motion.div>

              {/* Grid de Funcionalidades */}
              <motion.div
                variants={containerVariants}
                className={`grid gap-4 w-full ${
                  viewMode === "grid"
                    ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
                    : "grid-cols-1"
                }`}
              >
                {filteredFeatures.map((feature, index) => {
                  // Componente para verificar feature flag
                  const FeatureItem = () => {
                    // Hook interno para verificar feature flags
                    const { data: featureAvailability } = useFeatureAvailability(feature.featureKey || '');
                    
                    // Se tem featureKey, verificar se está habilitada
                    if (feature.featureKey) {
                      const isFeatureEnabled = featureAvailability?.isFeatureEnabled ?? false;
                      
                      // Se não está disponível, não renderizar
                      if (!isFeatureEnabled) {
                        return null;
                      }
                    }
                    
                    const IconComponent = feature.icon;
                    return (
                      <motion.div
                        key={feature.href}
                        variants={cardVariants}
                        whileHover={{ scale: 1.02, y: -2 }}
                        whileTap={{ scale: 0.98 }}
                        className="h-full"
                      >
                        <Card
                          className="hover:shadow-xl transition-all duration-300 cursor-pointer group bg-white/80 backdrop-blur-sm border border-gray-200 h-full flex flex-col overflow-hidden hover:border-orange-300"
                          onClick={() => navigate(feature.href)}
                        >
                          <CardHeader className="pb-3 flex-shrink-0">
                            <div className="flex items-start gap-3">
                              <div
                                className={`p-3 rounded-xl bg-gradient-to-r ${feature.color} text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}
                              >
                                <IconComponent className="h-5 w-5" />
                              </div>
                              <div className="flex-1">
                                <CardTitle className="text-lg break-words text-gray-800 group-hover:text-orange-600 transition-colors">
                                  {feature.title}
                                </CardTitle>
                                <CardDescription className="mt-1 break-words text-gray-600">
                                  {feature.description}
                                </CardDescription>
                              </div>
                              <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-orange-500 group-hover:translate-x-1 transition-all" />
                            </div>
                          </CardHeader>
                          <CardContent className="flex flex-col flex-grow justify-end pt-0">
                            <Badge
                              variant="secondary"
                              className={`text-sm bg-gradient-to-r ${feature.color} text-white px-3 py-1.5 rounded-lg self-start mt-auto max-w-full break-words whitespace-normal shadow-sm`}
                            >
                              {feature.category}
                            </Badge>
                          </CardContent>
                        </Card>
                      </motion.div>
                    );
                  };
                  
                  return <FeatureItem key={feature.href} />;
                })}

                {filteredFeatures.length === 0 && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    className="col-span-full flex flex-col items-center justify-center py-16 text-center"
                  >
                    <div className="p-4 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full mb-6">
                      <Search className="h-12 w-12 text-gray-400" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">
                      Nenhuma funcionalidade encontrada
                    </h3>
                    <p className="text-gray-500 max-w-md">
                      Tente ajustar os filtros ou termos de busca para encontrar o que você procura
                    </p>
                    <Button
                      onClick={() => {
                        setSearchTerm("");
                        setSelectedCategory("Todos");
                      }}
                      variant="outline"
                      className="mt-4 hover:bg-orange-50 border-orange-200"
                    >
                      Limpar Filtros
                    </Button>
                  </motion.div>
                )}
              </motion.div>
            </motion.div>
          </div>
        </div>
      </AdminLayout>
    </MainLayout>
  );
}
