/**
 * Portal de Administração LGPD - Gestão de Solicitações de Privacidade
 * <AUTHOR> Internet 2025
 */
import { useState } from 'react';
import { motion } from 'framer-motion';
import { AdminLayout } from "@/components/layout/AdminLayout";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Shield, 
  FileText, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Users,
  Eye,
  Settings,
  BarChart,
  Filter,
  Download,
  Search,
  RefreshCw,
  ArrowLeft,
  Edit,
  Trash2,
  MessageSquare,
  Calendar
} from 'lucide-react';
import { useNavigate } from "react-router-dom";
import { HeroSection } from "@/components/common/HeroSection";
import { MainLayout } from "@/components/layout";
import { AdvancedRefreshButton } from '@/components/ui/advanced-refresh-button';

// Hooks
import { useAdminPrivacyRequests, useProcessPrivacyRequest } from '@/lib/query/hooks/usePrivacyRequests';
import { useUserRoles } from '@/lib/query/hooks/useUserRoles';
import { formatBrazilianDate } from '@/lib/utils/dateUtils';

// Componentes
import { PrivacyRequestDetailModal } from '@/components/admin/privacy/PrivacyRequestDetailModal';

// Componentes (temporariamente comentados até serem criados)
// import { PrivacyRequestsManagement } from '@/components/admin/privacy/PrivacyRequestsManagement';
// import { PrivacyMetricsDashboard } from '@/components/admin/privacy/PrivacyMetricsDashboard';
// import { PrivacyConfigurationPanel } from '@/components/admin/privacy/PrivacyConfigurationPanel';

// Constantes de configuração para sincronização de timing
const REFRESH_CONFIG = {
  minimumDisplayTime: 2000, // 2 segundos mínimo (mesmo do AdvancedRefreshButton)
  timeout: 8000 // 8 segundos timeout (mesmo do AdvancedRefreshButton)
};

export default function PrivacyAdmin() {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('requests');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  // Estados para controle do loading da interface (usado independente do AdvancedRefreshButton)
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Verificação de permissões - apenas company_owner
  const { isCompanyOwner, isLoading: isLoadingRoles } = useUserRoles();
  
  // Dados reais do sistema
  const { data: allRequests = [], isLoading: requestsLoading, refetch } = useAdminPrivacyRequests();
  const { mutateAsync: processRequest } = useProcessPrivacyRequest();

  // Se não tem permissão, mostrar mensagem de acesso negado
  if (!isCompanyOwner && !isLoadingRoles) {
    return (
      <MainLayout>
        <AdminLayout>
          <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center">
            <Card className="bg-red-50 border-red-200 max-w-md">
              <CardContent className="p-6 text-center">
                <Shield className="h-12 w-12 mx-auto mb-4 text-red-500" />
                <h2 className="text-xl font-bold text-red-800 mb-2">Acesso Restrito</h2>
                <p className="text-red-700">
                  Apenas proprietários da empresa podem acessar o Portal de Administração LGPD.
                </p>
              </CardContent>
            </Card>
          </div>
        </AdminLayout>
      </MainLayout>
    );
  }

  // Estatísticas rápidas dos dados reais
  const pendingRequests = allRequests.filter(r => r.status === 'pending').length;
  const processingRequests = allRequests.filter(r => r.status === 'processing').length;
  const completedRequests = allRequests.filter(r => r.status === 'completed').length;
  const urgentRequests = allRequests.filter(r => {
    if (!r.created_at) return false;
    const daysSinceCreated = Math.floor((Date.now() - new Date(r.created_at).getTime()) / (1000 * 60 * 60 * 24));
    return r.status === 'pending' && daysSinceCreated >= 10; // LGPD: 15 dias, alertar com 10+
  }).length;

  // Filtrar solicitações
  const filteredRequests = allRequests.filter(request => {
    const matchesSearch = request.profiles?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.profiles?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || request.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'Pendente', color: 'bg-orange-100 text-orange-800 border-orange-200', icon: Clock },
      processing: { label: 'Processando', color: 'bg-blue-100 text-blue-800 border-blue-200', icon: Eye },
      completed: { label: 'Concluída', color: 'bg-green-100 text-green-800 border-green-200', icon: CheckCircle },
      rejected: { label: 'Rejeitada', color: 'bg-red-100 text-red-800 border-red-200', icon: AlertCircle }
    };
    
    const config = statusConfig[status] || statusConfig.pending;
    const Icon = config.icon;
    
    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getRequestTypeLabel = (type: string) => {
    const types = {
      access: 'Acesso aos Dados',
      rectification: 'Retificação',
      deletion: 'Exclusão',
      portability: 'Portabilidade',
      restriction: 'Limitação',
      objection: 'Oposição',
      consent_withdrawal: 'Retirada de Consentimento'
    };
    return types[type] || type;
  };

  const getDaysSinceCreated = (createdAt: string | null | undefined) => {
    if (!createdAt) return 0;
    const created = new Date(createdAt);
    const now = new Date();
    return Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24));
  };

  const handleViewDetails = (request: any) => {
    setSelectedRequest(request);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedRequest(null);
  };

  // Função para atualizar dados (sincronizada com AdvancedRefreshButton)
  const handleRefreshData = async (): Promise<void> => {
    const startTime = Date.now();
    
    try {
      setIsRefreshing(true);
      
      // Configurar tempo mínimo para o loading (sincronizado com AdvancedRefreshButton)
      const minimumDisplayPromise = new Promise(resolve => 
        setTimeout(resolve, REFRESH_CONFIG.minimumDisplayTime)
      );
      
      // Executar o refetch
      const refreshPromise = refetch();
      
      // Aguardar tanto o refetch quanto o tempo mínimo
      const [result] = await Promise.all([refreshPromise, minimumDisplayPromise]);
      
      if (result.error) {
        throw new Error(result.error.message || 'Erro ao atualizar dados');
      }
    } catch (error) {
      // Mesmo em erro, aguardar o tempo mínimo se ainda não passou
      const timeElapsed = Date.now() - startTime;
      if (timeElapsed < REFRESH_CONFIG.minimumDisplayTime) {
        await new Promise(resolve => 
          setTimeout(resolve, REFRESH_CONFIG.minimumDisplayTime - timeElapsed)
        );
      }
      throw error;
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleProcessRequest = async (requestId: string, status: string, justification?: string) => {
    try {
      await processRequest({
        request_id: requestId,
        status: status as any,
        justification,
        metadata: {}
      });
      
      // Aguardar um momento para garantir que o backend processou
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Usar a função aprimorada de refresh
      await handleRefreshData();
      
      handleCloseModal();
    } catch (error) {
      console.error('Erro ao processar solicitação:', error);
      throw error;
    }
  };

  // Animações
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };



  return (
    <MainLayout>
      <AdminLayout>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
          <div className="container py-1 px-4 space-y-4">           
              {/* Hero Section usando o componente reutilizável */}
              <HeroSection
                title="Portal de Administração LGPD"
                description="Gerencie solicitações de privacidade dos usuários e mantenha conformidade com a Lei Geral de Proteção de Dados"
                icon={Shield}
                gradientColors="from-red-600 via-orange-600 to-amber-600"
                iconAnimation={true}
                actions={
                  <div className="flex gap-2">
                    <Button
                      onClick={() => navigate("/admin")}
                      className="bg-white/15 hover:bg-white/25 text-white border-white/30 backdrop-blur-sm"
                      size="lg"
                    >
                      <ArrowLeft className="h-5 w-5 mr-2" />
                      Voltar
                    </Button>
                    <AdvancedRefreshButton
                      onRefresh={handleRefreshData}
                      className="bg-white text-red-700 hover:bg-white/90 border-white disabled:opacity-70"
                      size="lg"
                      minimumDisplayTime={REFRESH_CONFIG.minimumDisplayTime}
                      timeout={REFRESH_CONFIG.timeout}
                      operationName="Portal LGPD"
                      successMessage="Portal LGPD atualizado!"
                      successDescription="Os dados do portal foram atualizados com sucesso"
                      errorMessage="Erro ao atualizar Portal LGPD"
                      errorDescription="Ocorreu um erro durante a atualização dos dados"
                    >
                      Atualizar
                    </AdvancedRefreshButton>
                  </div>
                }
              />

              {/* Navegação por Tabs */}
              <motion.div
                variants={cardVariants}
                className="space-y-6"
              >
                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-white/80 backdrop-blur-sm border border-red-200/50">
            <TabsTrigger value="requests" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Solicitações
            </TabsTrigger>
            <TabsTrigger value="metrics" className="flex items-center gap-2">
              <BarChart className="h-4 w-4" />
              Métricas
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Configurações
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="requests" className="space-y-6">
            {/* Estatísticas Dashboard */}
            <motion.div variants={cardVariants}>
              <Card className="bg-white/80 backdrop-blur-sm border border-red-200/50 shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-800">
                    <Shield className="h-5 w-5" />
                    Status das Solicitações LGPD
                  </CardTitle>
                  <p className="text-sm text-red-600 mt-1">
                    Monitoramento em tempo real das solicitações de privacidade
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    {/* Pendentes */}
                    <div className="flex items-center gap-3 p-4 bg-orange-50 rounded-lg border border-orange-200">
                      <div className="p-2 bg-orange-100 rounded-lg">
                        <Clock className="h-5 w-5 text-orange-600" />
                      </div>
                      <div>
                        <p className="text-sm text-orange-700 font-medium">Pendentes</p>
                        <p className="text-2xl font-bold text-orange-800">{pendingRequests}</p>
                      </div>
                    </div>

                    {/* Em Processamento */}
                    <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Eye className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm text-blue-700 font-medium">Em Processamento</p>
                        <p className="text-2xl font-bold text-blue-800">{processingRequests}</p>
                      </div>
                    </div>

                    {/* Concluídas */}
                    <div className="flex items-center gap-3 p-4 bg-green-50 rounded-lg border border-green-200">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm text-green-700 font-medium">Concluídas</p>
                        <p className="text-2xl font-bold text-green-800">{completedRequests}</p>
                      </div>
                    </div>

                    {/* Urgentes */}
                    <div className="flex items-center gap-3 p-4 bg-red-50 rounded-lg border border-red-200">
                      <div className="p-2 bg-red-100 rounded-lg">
                        <AlertCircle className="h-5 w-5 text-red-600" />
                      </div>
                      <div>
                        <p className="text-sm text-red-700 font-medium">Urgentes (10+ dias)</p>
                        <p className="text-2xl font-bold text-red-800">{urgentRequests}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Filtros */}
            <motion.div variants={cardVariants} className="mb-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Buscar por nome, email ou descrição..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-white/80 backdrop-blur-sm border-gray-200 focus-visible:ring-orange-500"
                  />
                </div>
                
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-lg bg-white/80 backdrop-blur-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  <option value="all">Todos os Status</option>
                  <option value="pending">Pendentes</option>
                  <option value="processing">Em Processamento</option>
                  <option value="completed">Concluídas</option>
                  <option value="rejected">Rejeitadas</option>
                </select>
              </div>
            </motion.div>

            {/* Lista de Solicitações */}
            <motion.div variants={cardVariants}>
              <Card className="bg-white/80 backdrop-blur-sm border border-red-200/50 shadow-sm">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2 text-red-800">
                      <FileText className="h-5 w-5" />
                      Solicitações de Privacidade ({filteredRequests.length})
                    </CardTitle>
                    <Badge variant="outline" className="text-sm">
                      {requestsLoading || isRefreshing ? 'Carregando...' : `${filteredRequests.length} de ${allRequests.length}`}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  {requestsLoading || isRefreshing ? (
                    <div className="flex items-center justify-center py-8">
                      <RefreshCw className="h-6 w-6 animate-spin text-red-600" />
                      <span className="ml-2">
                        {isRefreshing ? 'Atualizando solicitações...' : 'Carregando solicitações...'}
                      </span>
                    </div>
                  ) : filteredRequests.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>Nenhuma solicitação encontrada</p>
                      <p className="text-sm">Tente ajustar os filtros ou aguarde novas solicitações</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {filteredRequests.map((request) => {
                        const daysSince = getDaysSinceCreated(request.created_at);
                        const isUrgent = daysSince >= 10 && request.created_at;
                        const isCritical = daysSince >= 13 && request.created_at;

                        return (
                          <motion.div
                            key={request.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className={`border rounded-lg p-4 hover:shadow-md transition-all ${
                              isCritical ? 'border-red-300 bg-red-50' :
                              isUrgent ? 'border-orange-300 bg-orange-50' :
                              'border-gray-200 bg-white hover:bg-gray-50'
                            }`}
                          >
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center gap-3">
                                <h4 className="font-medium text-gray-900">
                                  {request.profiles?.full_name || 'Usuário Desconhecido'}
                                </h4>
                                <Badge variant="outline" className="text-xs">
                                  {getRequestTypeLabel(request.request_type)}
                                </Badge>
                                {getStatusBadge(request.status)}
                                {isUrgent && (
                                  <Badge className={isCritical ? 'bg-red-100 text-red-800' : 'bg-orange-100 text-orange-800'}>
                                    {daysSince} dias
                                  </Badge>
                                )}
                              </div>
                              
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleViewDetails(request)}
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                Ver Detalhes
                              </Button>
                            </div>
                            
                            <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                              <div className="flex items-center gap-1">
                                <Users className="h-4 w-4" />
                                {request.profiles?.email || 'N/A'}
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-4 w-4" />
                                {formatBrazilianDate(request.created_at)}
                              </div>
                            </div>

                            {request.description && (
                              <p className="text-sm text-gray-600 line-clamp-2 bg-gray-50 p-2 rounded">
                                {request.description}
                              </p>
                            )}

                            {request.admin_response && (
                              <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded">
                                <p className="text-xs text-green-700 font-medium mb-1">Resposta do Admin:</p>
                                <p className="text-sm text-green-800">{request.admin_response}</p>
                              </div>
                            )}
                          </motion.div>
                        );
                      })}
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          {/* Métricas Tab */}
          <TabsContent value="metrics">
            <div className="space-y-6">
              {/* KPIs Principais */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <FileText className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm text-blue-700 font-medium">Total</p>
                        <p className="text-3xl font-bold text-blue-800">{allRequests.length}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-orange-50 border-orange-200">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-orange-100 rounded-lg">
                        <Clock className="h-6 w-6 text-orange-600" />
                      </div>
                      <div>
                        <p className="text-sm text-orange-700 font-medium">Pendentes</p>
                        <p className="text-3xl font-bold text-orange-800">{pendingRequests}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-green-50 border-green-200">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <CheckCircle className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm text-green-700 font-medium">Concluídas</p>
                        <p className="text-3xl font-bold text-green-800">{completedRequests}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-red-50 border-red-200">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-red-100 rounded-lg">
                        <AlertCircle className="h-6 w-6 text-red-600" />
                      </div>
                      <div>
                        <p className="text-sm text-red-700 font-medium">Urgentes</p>
                        <p className="text-3xl font-bold text-red-800">{urgentRequests}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Resumo por Tipo */}
              <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart className="h-5 w-5 text-blue-600" />
                    Solicitações por Tipo
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(
                      allRequests.reduce((acc, request) => {
                        acc[request.request_type] = (acc[request.request_type] || 0) + 1;
                        return acc;
                      }, {} as Record<string, number>)
                    ).map(([type, count]) => {
                      const percentage = allRequests.length > 0 ? (count / allRequests.length) * 100 : 0;
                      return (
                        <div key={type} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div 
                              className="w-4 h-4 rounded" 
                              style={{ 
                                backgroundColor: `hsl(${Object.keys(allRequests.reduce((acc, request) => {
                                  acc[request.request_type] = true;
                                  return acc;
                                }, {} as Record<string, boolean>)).indexOf(type) * 50}, 70%, 50%)` 
                              }}
                            />
                            <span className="text-sm font-medium">
                              {getRequestTypeLabel(type)}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{count}</Badge>
                            <span className="text-sm text-gray-500">
                              {percentage.toFixed(1)}%
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  {allRequests.length === 0 && (
                    <p className="text-center text-gray-500 py-8">
                      Nenhuma solicitação para analisar
                    </p>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Configurações Tab */}
          <TabsContent value="settings">
            <Card className="bg-white/80 backdrop-blur-sm border border-red-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-800">
                  <Settings className="h-5 w-5" />
                  Configurações do Portal LGPD
                </CardTitle>
                <p className="text-sm text-red-600 mt-1">
                  Configure as políticas e comportamentos do sistema de privacidade
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Configurações básicas */}
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-blue-900 mb-4">Configurações Básicas</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-blue-800 mb-2">
                          Prazo Padrão de Resposta (dias)
                        </label>
                        <input
                          type="number"
                          min="1"
                          max="30"
                          defaultValue="15"
                          className="w-full px-3 py-2 border border-blue-200 rounded-lg focus:ring-2 focus:ring-blue-500"
                        />
                        <p className="text-xs text-blue-600 mt-1">
                          LGPD recomenda máximo de 15 dias úteis
                        </p>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-blue-800 mb-2">
                          Email para Notificações
                        </label>
                        <input
                          type="email"
                          placeholder="<EMAIL>"
                          className="w-full px-3 py-2 border border-blue-200 rounded-lg focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Configurações Avançadas */}
                  <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-purple-900 mb-4">Configurações Avançadas</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-purple-800">Notificações Automáticas</p>
                          <p className="text-sm text-purple-600">Alertas quando prazos estão próximos do vencimento</p>
                        </div>
                        <input type="checkbox" defaultChecked className="rounded border-purple-300" />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-purple-800">Backup de Logs</p>
                          <p className="text-sm text-purple-600">Backup automático dos logs de auditoria</p>
                        </div>
                        <input type="checkbox" defaultChecked className="rounded border-purple-300" />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

                </Tabs>

                {/* Disclaimer importante */}
                <motion.div 
                  variants={cardVariants}
                  className="mt-8"
                >
                  <Card className="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="p-2 bg-amber-100 rounded-lg flex-shrink-0">
                          <AlertCircle className="h-6 w-6 text-amber-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-amber-900 mb-2">
                            🏢 Portal Administrativo Corporativo
                          </h3>
                          <p className="text-sm text-amber-800 leading-relaxed mb-3">
                            Este portal permite aos <strong>administradores da empresa</strong> gerenciar 
                            solicitações de privacidade dos usuários conforme Art. 42º da LGPD. 
                            <strong>Apenas company_owners têm acesso</strong> a estas funcionalidades.
                          </p>
                          <div className="bg-amber-100 rounded-lg p-3">
                            <p className="text-xs text-amber-700">
                              <strong>📍 Importante:</strong> Todas as ações são logadas para auditoria. 
                              O prazo legal para resposta é de <strong>15 dias úteis</strong> conforme LGPD.
                            </p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </motion.div>
          </div>
        </div>

        {/* Modal de Detalhes */}
        <PrivacyRequestDetailModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          request={selectedRequest}
          onProcessRequest={handleProcessRequest}
        />
      </AdminLayout>
    </MainLayout>
  );
}