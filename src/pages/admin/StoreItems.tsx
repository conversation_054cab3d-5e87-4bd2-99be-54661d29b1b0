/**
 * StoreItems - Interface Administrativa para Gerenciar Itens da Loja
 * Permite criar, editar e gerenciar itens disponíveis na loja Stardust
 * <AUTHOR> Internet 2025
 */

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/layout/AdminLayout";
import { MainLayout } from "@/components/layout";
import { HeroSection } from "@/components/common/HeroSection";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RefreshButton } from "@/components/ui/RefreshButton";
import { GenericPermissionGate } from "@/components/auth/GenericPermissionGate";
import { VisualAsset } from "@/types/gamification.types";
import { useVisualAssetsAdmin } from "@/lib/query/hooks/useVisualAssetsAdmin";
import { 
  useStoreItems, 
  useCreateStoreItem, 
  useUpdateStoreItem, 
  useDeleteStoreItem, 
  useToggleStoreItemStatus 
} from "@/lib/query/hooks/useStoreItemsAdmin";
import { 
  Loader2, 
  Plus, 
  ShoppingBag, 
  Package,
  Sparkles,
  Eye,
  Search,
  Star,
  Gem,
  Crown,
  Zap,
  Edit2,
  Trash2,
  ToggleLeft,
  ToggleRight
} from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { 
  successWithNotification, 
  errorWithNotification 
} from '@/lib/notifications/toastWithNotification';
import { motion } from "framer-motion";
import { Label } from "@/components/ui/label";

// Interfaces
interface StoreItem {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  item_type: string;
  resource_id: string | null;
  level_required: number;
  company_id: string | null;
  active: boolean;
  created_at: string;
  updated_at: string;
  visual_assets?: VisualAsset;
}

// Variantes de animação
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.2,
    },
  },
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut",
    },
  },
};

export default function StoreItems() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedVisualAsset, setSelectedVisualAsset] = useState<string>('');
  const [price, setPrice] = useState<number>(1000);
  const [levelRequired, setLevelRequired] = useState<number>(1);
  const [isActive, setIsActive] = useState<boolean>(true);
  const [editingItem, setEditingItem] = useState<StoreItem | null>(null);
  const [itemToDelete, setItemToDelete] = useState<StoreItem | null>(null);
  
  // React Query hooks
  const { data: visualAssets = [], isLoading: visualAssetsLoading, refetch: refetchAssets } = useVisualAssetsAdmin();
  const { data: storeItems = [], isLoading: storeItemsLoading, refetch: refetchStoreItems } = useStoreItems();
  const createMutation = useCreateStoreItem();
  const updateMutation = useUpdateStoreItem();
  const deleteMutation = useDeleteStoreItem();
  const toggleStatusMutation = useToggleStoreItemStatus();

  // Loading derivado dos hooks
  const loading = storeItemsLoading || visualAssetsLoading;

  // Obter ícone da raridade
  const getRarityIcon = (rarity: string) => {
    switch (rarity) {
      case 'common': return <Star className="h-3 w-3" />;
      case 'rare': return <Gem className="h-3 w-3" />;
      case 'epic': return <Crown className="h-3 w-3" />;
      case 'legendary': return <Sparkles className="h-3 w-3" />;
      case 'mythic': return <Zap className="h-3 w-3" />;
      default: return <Star className="h-3 w-3" />;
    }
  };

  // Função para obter preço sugerido
  const getSuggestedPrice = (rarity: string): number => {
    switch (rarity) {
      case 'common': return 500;
      case 'rare': return 1500;
      case 'epic': return 2500;
      case 'legendary': return 5000;
      case 'mythic': return 10000;
      default: return 1000;
    }
  };

  // Criar store item
  const handleCreateStoreItem = async () => {
    if (!selectedVisualAsset) {
      errorWithNotification('Erro', {
        description: 'Selecione um visual asset.'
      });
      return;
    }

    try {
      await createMutation.mutateAsync({
        visualAssetId: selectedVisualAsset,
        price,
        levelRequired,
        active: isActive
      });

      setCreateDialogOpen(false);
      clearForm();
    } catch (error) {
      // Erro já tratado no hook
    }
  };

  // Editar store item
  const handleEditStoreItem = (item: StoreItem) => {
    setEditingItem(item);
    setPrice(item.price);
    setLevelRequired(item.level_required);
    setIsActive(item.active);
    setEditDialogOpen(true);
  };

  // Salvar edição
  const handleSaveEdit = async () => {
    if (!editingItem) return;

    try {
      await updateMutation.mutateAsync({
        id: editingItem.id,
        price,
        level_required: levelRequired,
        active: isActive
      });

      setEditDialogOpen(false);
      setEditingItem(null);
      clearForm();
    } catch (error) {
      // Erro já tratado no hook
    }
  };

  // Confirmar exclusão
  const handleDeleteStoreItem = (item: StoreItem) => {
    setItemToDelete(item);
    setDeleteDialogOpen(true);
  };

  // Excluir store item
  const handleConfirmDelete = async () => {
    if (!itemToDelete) return;

    try {
      await deleteMutation.mutateAsync(itemToDelete.id);
      setDeleteDialogOpen(false);
      setItemToDelete(null);
    } catch (error) {
      // Erro já tratado no hook
    }
  };

  // Toggle status
  const handleToggleStatus = async (item: StoreItem) => {
    try {
      await toggleStatusMutation.mutateAsync({
        id: item.id,
        currentStatus: item.active
      });
    } catch (error) {
      // Erro já tratado no hook
    }
  };

  // Limpar formulário
  const clearForm = () => {
    setSelectedVisualAsset('');
    setPrice(1000);
    setLevelRequired(1);
    setIsActive(true);
  };

  // Filtrar assets que ainda não têm store item
  const availableAssets = visualAssets.filter(asset => 
    !storeItems.some(item => item.resource_id === asset.id)
  );

  // Filtrar store items
  const filteredItems = storeItems.filter(item => {
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    const matchesSearch = searchTerm === '' || 
      item.name.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <MainLayout>
      <AdminLayout>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
          <div className="container py-1 px-4 space-y-4">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {/* Hero Section */}
              <motion.div variants={cardVariants}>
                <HeroSection
                  title="Loja Stardust Via-Láctea"
                  description="Gerencie itens disponíveis para compra com Stardust na experiência premium"
                  icon={ShoppingBag}
                  gradientColors="from-emerald-600 via-teal-600 to-cyan-600"
                  iconAnimation={true}
                  actions={
                    <GenericPermissionGate 
                      resourceTypeKey="admin" 
                      actionKey="manage_visual_assets"
                      fallbackComponent={null}
                    >
                      <Button
                        onClick={() => setCreateDialogOpen(true)}
                        className="bg-white text-emerald-700 hover:bg-white/90 border-white"
                        size="lg"
                      >
                        <Plus className="h-5 w-5 mr-2" />
                        Novo Item da Loja
                      </Button>
                    </GenericPermissionGate>
                  }
                />
              </motion.div>

              {/* Breadcrumb */}
              <motion.div variants={cardVariants}>
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink href="/admin">Gamificação</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>Loja Stardust</BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </motion.div>

              {/* Filtros */}
              <motion.div variants={cardVariants} className="space-y-4 mb-6">
                <div className="flex gap-3 items-center">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      type="search"
                      placeholder="Buscar itens da loja..."
                      className="pl-10 bg-white/80 backdrop-blur-sm border-gray-200 focus-visible:ring-emerald-500 h-12 text-base"
                      value={searchTerm}
                      onChange={e => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <RefreshButton 
                    onRefresh={refetchStoreItems}
                    isRefreshing={loading}
                    className="h-12 bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-emerald-50 hover:border-emerald-300"
                  />
                </div>
              </motion.div>

              {/* Conteúdo Principal com verificação de permissões */}
              <motion.div variants={cardVariants}>
                <GenericPermissionGate 
                  resourceTypeKey="admin" 
                  actionKey="manage_visual_assets"
                  fallbackComponent={
                    <Card>
                      <CardHeader>
                        <CardTitle>Acesso Negado</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p>Você não tem permissão para gerenciar a loja.</p>
                        <p className="text-sm text-gray-500 mt-2">Se você acredita que deveria ter acesso, por favor, contate um administrador do sistema.</p>
                      </CardContent>
                    </Card>
                  }
                >
                  <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
                    <CardHeader className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-t-lg border-b border-emerald-100">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <CardTitle className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                            <Package className="h-6 w-6 text-emerald-600" />
                            Itens da Loja
                          </CardTitle>
                          <CardDescription className="text-gray-600">
                            Gerencie itens disponíveis para compra com Stardust
                          </CardDescription>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-emerald-700 border-emerald-200">
                            {filteredItems.length} de {storeItems.length} Itens
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="p-6">
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow className="bg-gray-50/50">
                              <TableHead className="w-[80px]">Preview</TableHead>
                              <TableHead>Nome do Item</TableHead>
                              <TableHead>Categoria</TableHead>
                              <TableHead>Preço</TableHead>
                              <TableHead className="text-center">Nível Mín.</TableHead>
                              <TableHead className="text-center">Status</TableHead>
                              <TableHead className="text-center">Ações</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {loading ? (
                              Array.from({ length: 5 }).map((_, i) => (
                                <TableRow key={i}>
                                  <TableCell>
                                    <div className="w-12 h-12 bg-gray-200 rounded-md animate-pulse" />
                                  </TableCell>
                                  <TableCell>
                                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                                  </TableCell>
                                  <TableCell>
                                    <div className="h-6 w-16 bg-gray-200 rounded animate-pulse" />
                                  </TableCell>
                                  <TableCell>
                                    <div className="h-6 w-20 bg-gray-200 rounded animate-pulse" />
                                  </TableCell>
                                  <TableCell>
                                    <div className="h-4 w-8 bg-gray-200 rounded animate-pulse mx-auto" />
                                  </TableCell>
                                  <TableCell>
                                    <div className="h-6 w-16 bg-gray-200 rounded animate-pulse mx-auto" />
                                  </TableCell>
                                  <TableCell>
                                    <div className="h-8 w-8 bg-gray-200 rounded animate-pulse mx-auto" />
                                  </TableCell>
                                </TableRow>
                              ))
                            ) : filteredItems.length === 0 ? (
                              <TableRow>
                                <TableCell colSpan={7} className="text-center py-16">
                                  <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.5 }}
                                    className="flex flex-col items-center gap-4"
                                  >
                                    <div className="p-4 bg-gradient-to-r from-emerald-100 to-teal-100 rounded-full">
                                      <Package className="h-12 w-12 text-emerald-400" />
                                    </div>
                                    <div className="text-center">
                                      <h3 className="text-xl font-semibold text-gray-800 mb-2">
                                        Nenhum item na loja
                                      </h3>
                                      <p className="text-gray-500 max-w-md">
                                        Comece criando seu primeiro item para a loja Stardust
                                      </p>
                                    </div>
                                  </motion.div>
                                </TableCell>
                              </TableRow>
                            ) : (
                              filteredItems.map((item) => (
                                <TableRow key={item.id} className="hover:bg-emerald-50/30 transition-colors">
                                  <TableCell className="p-4">
                                    <div className="relative">
                                      <img
                                        src={item.visual_assets?.preview_url || '/placeholder.svg'}
                                        alt={item.name}
                                        className="w-12 h-12 object-cover rounded-lg border border-gray-200 shadow-sm"
                                        onError={(e) => {
                                          (e.target as HTMLImageElement).src = '/placeholder.svg';
                                        }}
                                      />
                                      {item.visual_assets?.rarity && (
                                        <div className="absolute -top-1 -right-1">
                                          {getRarityIcon(item.visual_assets.rarity)}
                                        </div>
                                      )}
                                    </div>
                                  </TableCell>
                                  <TableCell className="font-medium">
                                    <div>
                                      <div className="font-semibold text-gray-900">{item.name}</div>
                                      <div className="text-sm text-gray-500 truncate max-w-[200px]">
                                        {item.description || 'Sem descrição'}
                                      </div>
                                    </div>
                                  </TableCell>
                                  <TableCell>
                                    <Badge variant="outline" className="border-emerald-200 text-emerald-700 bg-emerald-50">
                                      {item.category}
                                    </Badge>
                                  </TableCell>
                                  <TableCell>
                                    <div className="flex items-center gap-1">
                                      <Sparkles className="h-4 w-4 text-yellow-500" />
                                      <span className="font-semibold text-emerald-700">{item.price}</span>
                                    </div>
                                  </TableCell>
                                  <TableCell className="text-center">
                                    <Badge variant="secondary" className="bg-gray-100">
                                      Nv. {item.level_required}
                                    </Badge>
                                  </TableCell>
                                  <TableCell className="text-center">
                                    <Badge variant={item.active ? "default" : "secondary"}>
                                      {item.active ? "Ativo" : "Inativo"}
                                    </Badge>
                                  </TableCell>
                                  <TableCell>
                                    <div className="flex items-center gap-1 justify-center">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleEditStoreItem(item)}
                                        className="hover:bg-blue-50 hover:text-blue-600"
                                        title="Editar item"
                                      >
                                        <Edit2 className="h-4 w-4" />
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleToggleStatus(item)}
                                        disabled={toggleStatusMutation.isPending}
                                        className={`hover:bg-yellow-50 hover:text-yellow-600 ${
                                          item.active ? 'text-green-600' : 'text-gray-400'
                                        }`}
                                        title={item.active ? 'Inativar item' : 'Ativar item'}
                                      >
                                        {item.active ? (
                                          <ToggleRight className="h-4 w-4" />
                                        ) : (
                                          <ToggleLeft className="h-4 w-4" />
                                        )}
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleDeleteStoreItem(item)}
                                        className="hover:bg-red-50 hover:text-red-600"
                                        title="Excluir item"
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </TableCell>
                                </TableRow>
                              ))
                            )}
                          </TableBody>
                        </Table>
                      </div>
                    </CardContent>
                  </Card>
                </GenericPermissionGate>
              </motion.div>
            </motion.div>

            {/* Dialogs */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-2xl border border-emerald-200">
          <DialogHeader>
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-emerald-100 flex items-center justify-center">
                <Edit2 className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold text-gray-900">
                  Editar Item da Loja
                </DialogTitle>
                <DialogDescription className="text-gray-600">
                  Edite as configurações do item "{editingItem?.name}"
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Preço (Stardust)</Label>
                <div className="relative">
                  <Input
                    type="number"
                    min="1"
                    value={price}
                    onChange={e => setPrice(parseInt(e.target.value) || 1)}
                    placeholder="Ex: 2500"
                    className="pr-10"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Sparkles className="h-4 w-4 text-yellow-500" />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Nível Mínimo</Label>
                <Input
                  type="number"
                  min="1"
                  value={levelRequired}
                  onChange={e => setLevelRequired(parseInt(e.target.value) || 1)}
                  placeholder="Ex: 5"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Status</Label>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="active-edit"
                  checked={isActive}
                  onChange={e => setIsActive(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="active-edit" className="text-sm text-gray-600">
                  Item ativo na loja
                </Label>
              </div>
            </div>
          </div>

          <DialogFooter className="flex gap-3 mt-6">
            <Button
              variant="outline"
              onClick={() => {
                setEditDialogOpen(false);
                setEditingItem(null);
                clearForm();
              }}
              disabled={updateMutation.isPending}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSaveEdit}
              disabled={updateMutation.isPending}
              className="flex-1 bg-emerald-600 hover:bg-emerald-700"
            >
              {updateMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Edit2 className="h-4 w-4 mr-2" />
              )}
              Salvar Alterações
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog de confirmação de exclusão */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md border border-red-200">
          <DialogHeader className="text-center">
            <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
              <Trash2 className="h-6 w-6 text-red-600" />
            </div>
            <DialogTitle className="text-xl font-semibold text-gray-900">
              Confirmar Exclusão
            </DialogTitle>
            <DialogDescription className="text-gray-600 mt-2">
              Tem certeza que deseja excluir o item <span className="font-semibold text-gray-900">"{itemToDelete?.name}"</span>?
              <br />
              <span className="text-red-600 font-medium">Esta ação não pode ser desfeita.</span>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-3 mt-6">
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={deleteMutation.isPending}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirmDelete}
              disabled={deleteMutation.isPending}
              className="flex-1"
            >
              {deleteMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              Excluir Item
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog de criação */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="max-w-2xl border border-emerald-200">
          <DialogHeader>
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-emerald-100 flex items-center justify-center">
                <Plus className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold text-gray-900">
                  Criar Item da Loja
                </DialogTitle>
                <DialogDescription className="text-gray-600">
                  Adicione um visual asset à loja Stardust
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Visual Asset</Label>
              <Select
                value={selectedVisualAsset}
                onValueChange={setSelectedVisualAsset}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um visual asset" />
                </SelectTrigger>
                <SelectContent>
                  {availableAssets.map((asset) => (
                    <SelectItem key={asset.id} value={asset.id}>
                      <div className="flex items-center gap-2">
                        {getRarityIcon(asset.rarity)}
                        <span>{asset.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {asset.rarity}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Preço (Stardust)</Label>
                <div className="relative">
                  <Input
                    type="number"
                    min="1"
                    value={price}
                    onChange={e => setPrice(parseInt(e.target.value) || 1)}
                    placeholder="Ex: 2500"
                    className="pr-10"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Sparkles className="h-4 w-4 text-yellow-500" />
                  </div>
                </div>
                {selectedVisualAsset && (
                  <p className="text-xs text-gray-500">
                    Sugerido: {getSuggestedPrice(availableAssets.find(a => a.id === selectedVisualAsset)?.rarity || 'common')} Stardust
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Nível Mínimo</Label>
                <Input
                  type="number"
                  min="1"
                  value={levelRequired}
                  onChange={e => setLevelRequired(parseInt(e.target.value) || 1)}
                  placeholder="Ex: 5"
                />
              </div>
            </div>
          </div>

          <DialogFooter className="flex gap-3 mt-6">
            <Button
              variant="outline"
              onClick={() => {
                setCreateDialogOpen(false);
                clearForm();
              }}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleCreateStoreItem}
              disabled={!selectedVisualAsset || createMutation.isPending}
              className="flex-1 bg-emerald-600 hover:bg-emerald-700"
            >
              {createMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Plus className="h-4 w-4 mr-2" />
              )}
              Criar Item da Loja
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
          </div>
        </div>
      </AdminLayout>
    </MainLayout>
  );
}