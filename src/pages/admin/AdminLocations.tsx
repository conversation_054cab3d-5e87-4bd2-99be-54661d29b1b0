/**
 * Página de Gerenciamento de Localizações - Versão Premium
 * Permite aos administradores visualizar, criar, editar e gerenciar localizações da organização.
 * Utiliza o novo sistema de permissões com useGenericPermissionCheck.
 * <AUTHOR> Internet 2025
 */
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuthStore } from "@/stores/authStore";
import { AdminLayout } from "@/components/layout/AdminLayout";
import { MainLayout } from "@/components/layout";
import { HeroSection } from "@/components/common/HeroSection";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { LocationManagement } from "@/components/admin/LocationManagement";
import { SessionReconnector } from "@/components/auth/SessionReconnector";
import { AuthDebug } from "@/components/debug/AuthDebug";
import { CompanyDebug } from "@/components/debug/CompanyDebug";
import { successWithNotification, errorWithNotification } from "@/lib/notifications/toastWithNotification";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
import {
  MapPin,
  Building2,
  Map,
  Navigation,
  RefreshCw,
  Plus,
  Loader2,
  HelpCircle,
  TrendingUp
} from "lucide-react";
import { useLocations, useCreateLocation, useUpdateLocation } from "@/lib/query/hooks/useLocations";
import { useGenericPermissionCheck } from '@/lib/query/hooks/useGenericPermissionCheck';
import { QueryKeys } from "@/lib/query/queryKeys";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { SideSheet } from "@/components/ui/side-sheet";
import { LocationForm } from "@/components/admin/forms/location-form";
import { useLocationsLimits } from '@/hooks/locations/useLocationsLimits';
import { LocationsLimitIndicator } from '@/components/locations/LocationsLimitIndicator';

// Variantes de animação premium
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};



export default function AdminLocations() {
  const queryClient = useQueryClient();
  const [isReinitializing, setIsReinitializing] = useState(false);
  
  // Estados para controlar os SideSheets de ajuda
  const [helpSheets, setHelpSheets] = useState({
    total: false,
    states: false,
    cities: false,
    average: false
  });

  // Estados para controlar SideSheets de localizações
  const [isAddLocationSheetOpen, setIsAddLocationSheetOpen] = useState(false);
  const [isEditLocationSheetOpen, setIsEditLocationSheetOpen] = useState(false);
  const [editingLocation, setEditingLocation] = useState<any>(null);

  // Função para abrir/fechar help sheets
  const toggleHelpSheet = (sheet: keyof typeof helpSheets) => {
    setHelpSheets(prev => ({ ...prev, [sheet]: !prev[sheet] }));
  };

  // Auth state
  const user = useAuthStore((state) => state.user);
  const company_id = useAuthStore((state) => state.company_id);
  const refreshCompanyId = useAuthStore((state) => state.refreshCompanyId);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const isUserReady = Boolean(user?.id && company_id);

  // Permissões
  const { 
    hasPermission: canViewLocations, 
    isLoading: isLoadingViewPermission 
  } = useGenericPermissionCheck({
    resourceTypeKey: 'location',
    actionKey: 'view_locations',
    description: "Verificar se o usuário pode visualizar localizações"
  });

  const { 
    hasPermission: canCreateLocations, 
    isLoading: isLoadingCreatePermission 
  } = useGenericPermissionCheck({
    resourceTypeKey: 'location',
    actionKey: 'create_location',
    description: "Verificar se o usuário pode criar localizações"
  });

  // Sistema de limites de localidades
  const { limits, isLoading: isLoadingLimits, canCreate, refetch: refetchLimits } = useLocationsLimits();

  // Buscar dados
  const { data: locationsData, isLoading: isLoadingLocations, isError, error } = useLocations();
  
  // Mutations para CRUD de localizações
  const createLocationMutation = useCreateLocation();
  const updateLocationMutation = useUpdateLocation();

  // Estatísticas das localizações
  const { data: locationStats, isLoading: isLoadingStats, error: statsError } = useQuery({
    queryKey: ["location_stats", company_id],
    queryFn: async () => {
      logQueryEvent('AdminLocations', 'Executando query de estatísticas', { company_id });
      
      if (!company_id) {
        logQueryEvent('AdminLocations', 'Sem company_id para estatísticas');
        return { total: 0, byState: {}, byCity: {} };
      }
      
      try {
        const { data: locations, error } = await supabase
          .from("locations")
          .select("id, city, state")
          .eq("company_id", company_id);
          
        if (error) {
          logQueryEvent('AdminLocations', 'Erro na query de estatísticas', error, 'error');
          throw error;
        }
        
        logQueryEvent('AdminLocations', 'Localizações encontradas para estatísticas', { 
          count: locations?.length || 0,
          locations: locations 
        });
        
        const total = locations?.length || 0;
        const byState = locations?.reduce((acc, loc) => {
          acc[loc.state] = (acc[loc.state] || 0) + 1;
          return acc;
        }, {} as Record<string, number>) || {};
        
        const byCity = locations?.reduce((acc, loc) => {
          const key = `${loc.city} - ${loc.state}`;
          acc[key] = (acc[key] || 0) + 1;
          return acc;
        }, {} as Record<string, number>) || {};
        
        const stats = { total, byState, byCity };
        logQueryEvent('AdminLocations', 'Estatísticas calculadas', stats);
        return stats;
      } catch (error) {
        logQueryEvent('AdminLocations', 'Erro ao carregar estatísticas das localizações', error, 'error');
        return { total: 0, byState: {}, byCity: {} };
      }
    },
    enabled: isUserReady && Boolean(company_id),
    staleTime: 30 * 1000, // 30 segundos
  });

  // Handlers
  const handleRefresh = async () => {
    // Invalidar queries
    await queryClient.invalidateQueries({ queryKey: QueryKeys.locations.all });
    await queryClient.invalidateQueries({ queryKey: ["location_stats"] });
    
    // Forçar atualização dos limites
    await refetchLimits();
    
    successWithNotification("Dados atualizados!", {
      description: "A lista de localizações e limites foi atualizada",
      persist: false
    });
  };

  // Handlers para gerenciamento de localizações
  const handleCreateLocation = () => {
    // Verificar se pode criar localidades baseado nos limites
    if (!canCreate()) {
      errorWithNotification("Limite de localidades atingido", {
        description: limits?.isUnlimited 
          ? "Houve um erro ao verificar os limites" 
          : `Você atingiu o limite de ${limits?.maxLocations} localidades do seu plano ${limits?.currentPlan}. Faça upgrade para criar mais localidades.`,
        persist: true
      });
      return;
    }
    
    setIsAddLocationSheetOpen(true);
  };

  const handleEditLocation = (location: any) => {
    setEditingLocation(location);
    setIsEditLocationSheetOpen(true);
  };

  const handleCloseCreateSheet = () => {
    setIsAddLocationSheetOpen(false);
  };

  const handleCloseEditSheet = () => {
    setIsEditLocationSheetOpen(false);
    setEditingLocation(null);
  };

  const handleCreateLocationSubmit = async (values: any) => {
    try {
      await createLocationMutation.mutateAsync(values);
      setIsAddLocationSheetOpen(false);
      queryClient.invalidateQueries({ queryKey: QueryKeys.locations.all });
      queryClient.invalidateQueries({ queryKey: ["location_stats"] });
      queryClient.invalidateQueries({ queryKey: QueryKeys.locations.validation(company_id || '') });
      successWithNotification("Localização criada!", {
        description: "A nova localização foi adicionada com sucesso",
        persist: false
      });
    } catch (error) {
      console.error("Erro ao criar localização:", error);
      errorWithNotification("Erro ao criar localização", {
        description: error instanceof Error ? error.message : "Erro desconhecido",
        persist: true
      });
    }
  };

  const handleUpdateLocationSubmit = async (values: any) => {
    if (!editingLocation) return;
    
    try {
      await updateLocationMutation.mutateAsync({
        id: editingLocation.id,
        ...values
      });
      setIsEditLocationSheetOpen(false);
      setEditingLocation(null);
      queryClient.invalidateQueries({ queryKey: QueryKeys.locations.all });
      queryClient.invalidateQueries({ queryKey: ["location_stats"] });
      queryClient.invalidateQueries({ queryKey: QueryKeys.locations.validation(company_id || '') });
      successWithNotification("Localização atualizada!", {
        description: "A localização foi atualizada com sucesso",
        persist: false
      });
    } catch (error) {
      console.error("Erro ao atualizar localização:", error);
      errorWithNotification("Erro ao atualizar localização", {
        description: error instanceof Error ? error.message : "Erro desconhecido",
        persist: true
      });
    }
  };

  // Log na montagem inicial
  useEffect(() => {
    logQueryEvent('AdminLocations', 'Página AdminLocations Premium montada', { 
      userId: user?.id, 
      companyId: company_id,
      isAuthenticated,
      hasUser: !!user,
    });
  }, [user?.id, company_id, isAuthenticated]);

  // Tentar atualizar company_id se necessário
  useEffect(() => {
    if (user?.id && !company_id) {
      logQueryEvent('AdminLocations', 'Tentando atualizar company_id automaticamente');
      refreshCompanyId().then(updatedCompanyId => {
        if (updatedCompanyId) {
          logQueryEvent('AdminLocations', 'company_id atualizado automaticamente', { company_id: updatedCompanyId });
        }
      });
    }
  }, [user?.id, company_id, refreshCompanyId]);

  // Configurar listener Realtime para mudanças na tabela de localizações
  useEffect(() => {
    if (!company_id) return;

    // Configurar canal de subscriptions
    const channel = supabase
      .channel('admin-location-changes')
      .on('postgres_changes', {
        event: '*', // INSERT, UPDATE, DELETE
        schema: 'public',
        table: 'locations',
        filter: `company_id=eq.${company_id}`,
      }, () => {
        // Invalidar queries relevantes quando houver mudanças em localizações
        queryClient.invalidateQueries({ queryKey: QueryKeys.locations.all });
        queryClient.invalidateQueries({ queryKey: ["location_stats"] });
      })
      .subscribe();

    // Cleanup ao desmontar
    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient, company_id]);

  // Estados de autenticação e carregamento
  if (!isAuthenticated || !user) {
    return (
      <MainLayout>
        <AdminLayout>
          <Card>
            <CardHeader>
              <CardTitle>Gerenciamento de Localizações</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <AuthDebug />
                <div className="mt-2"></div>
                <CompanyDebug />
              </div>
              <SessionReconnector 
                onReconnectStart={() => setIsReinitializing(true)}
                onReconnectSuccess={() => setIsReinitializing(false)}
                onReconnectFailure={() => setIsReinitializing(false)}
              />
            </CardContent>
          </Card>
        </AdminLayout>
      </MainLayout>
    );
  }

  if (user && !company_id) {
    return (
      <MainLayout>
        <AdminLayout>
          <Card>
            <CardHeader>
              <CardTitle>Gerenciamento de Localizações</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <AuthDebug />
                <div className="mt-2"></div>
                <CompanyDebug />
              </div>
              <div className="p-4 border border-yellow-300 bg-yellow-50 rounded-md text-yellow-700">
                <p className="font-medium">Carregando dados da empresa...</p>
                <p className="text-sm mt-1">Aguarde enquanto obtemos as informações necessárias.</p>
              </div>
            </CardContent>
          </Card>
        </AdminLayout>
      </MainLayout>
    );
  }

  // Renderizar sempre a estrutura da página
  return (
    <MainLayout>
      <AdminLayout>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
          <div className="container py-1 px-4 space-y-4">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {/* HeroSection Premium */}
              <motion.div variants={cardVariants}>
                <HeroSection
                  title="Localizações"
                  description="Gerencie endereços e locais físicos da empresa"
                  icon={MapPin}
                  gradientColors="from-red-600 via-pink-600 to-rose-600"
                  actions={
                    <div className="flex items-center gap-3">
                      <Button
                        onClick={handleCreateLocation}
                        disabled={!canCreateLocations || isLoadingCreatePermission || !canCreate() || isLoadingLimits}
                        className="bg-red-500 hover:bg-red-600 text-white"
                        title={
                          !canCreateLocations 
                            ? "Você não tem permissão para criar localizações" 
                            : !canCreate()
                            ? `Limite de ${limits?.maxLocations} localidades atingido no plano ${limits?.currentPlan}`
                            : "Criar nova localização"
                        }
                      >
                        {isLoadingCreatePermission || isLoadingLimits ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Plus className="h-4 w-4 mr-2" />
                        )}
                        Nova Localização
                      </Button>
                      <Button
                        onClick={handleRefresh}
                        disabled={isLoadingLocations}
                        variant="outline"
                        className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                      >
                        <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingLocations ? 'animate-spin' : ''}`} />
                        {isLoadingLocations ? 'Atualizando...' : 'Atualizar'}
                      </Button>
                    </div>
                  }
                />
              </motion.div>

              {/* Estados de Loading, Erro e Permissões no Conteúdo */}
              {(isLoadingLocations || isLoadingViewPermission || isLoadingCreatePermission) ? (
                <motion.div variants={cardVariants}>
                  <Card>
                    <CardContent className="flex justify-center items-center py-20">
                      <div className="text-center space-y-4">
                        <Loader2 className="h-12 w-12 animate-spin text-red-500 mx-auto" />
                        <p className="text-muted-foreground">Carregando localizações...</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ) : !canViewLocations ? (
                <motion.div variants={cardVariants}>
                  <Card>
                    <CardHeader>
                      <CardTitle>Acesso Negado</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p>Você não tem permissão para visualizar as localizações.</p>
                      <p className="text-sm text-gray-500 mt-2">Se você acredita que deveria ter acesso, por favor, contate um administrador do sistema.</p>
                    </CardContent>
                  </Card>
                </motion.div>
              ) : (isError || !locationsData) ? (
                <motion.div variants={cardVariants}>
                  <Card className="border-red-500">
                    <CardHeader>
                      <CardTitle className="text-red-700">Erro ao Carregar Localizações</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-red-600">Não foi possível carregar a lista de localizações. Por favor, tente novamente mais tarde.</p>
                      {error && <p className="text-sm text-gray-500 mt-2">Detalhes: {error.message}</p>}
                    </CardContent>
                  </Card>
                </motion.div>
              ) : (
                <>
                  {/* Estatísticas */}
                  {locationStats && (
                    <motion.div 
                      variants={cardVariants}
                      className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6"
                    >
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Building2 className="h-4 w-4 text-red-600" />
                              <div>
                                <p className="text-sm text-muted-foreground">Total</p>
                                <p className="text-2xl font-bold">{locationStats.total}</p>
                              </div>
                            </div>
                            <button 
                              onClick={() => toggleHelpSheet('total')}
                              className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                            >
                              <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                            </button>
                          </div>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Map className="h-4 w-4 text-blue-600" />
                              <div>
                                <p className="text-sm text-muted-foreground">Estados</p>
                                <p className="text-2xl font-bold text-blue-600">{Object.keys(locationStats.byState).length}</p>
                              </div>
                            </div>
                            <button 
                              onClick={() => toggleHelpSheet('states')}
                              className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                            >
                              <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                            </button>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Navigation className="h-4 w-4 text-green-600" />
                              <div>
                                <p className="text-sm text-muted-foreground">Cidades</p>
                                <p className="text-2xl font-bold text-green-600">{Object.keys(locationStats.byCity).length}</p>
                              </div>
                            </div>
                            <button 
                              onClick={() => toggleHelpSheet('cities')}
                              className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                            >
                              <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                            </button>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <TrendingUp className="h-4 w-4 text-purple-600" />
                              <div>
                                <p className="text-sm text-muted-foreground">Média/Estado</p>
                                <p className="text-2xl font-bold text-purple-600">
                                  {Object.keys(locationStats.byState).length > 0 
                                    ? Math.round(locationStats.total / Object.keys(locationStats.byState).length * 10) / 10
                                    : 0
                                  }
                                </p>
                              </div>
                            </div>
                            <button 
                              onClick={() => toggleHelpSheet('average')}
                              className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                            >
                              <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                            </button>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )}

                  {/* Indicador de Limites de Localidades */}
                  <motion.div variants={cardVariants}>
                    <LocationsLimitIndicator />
                  </motion.div>


                  {/* Componente de Gerenciamento */}
                  <motion.div variants={cardVariants}>
                    <Card>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="flex items-center gap-2">
                              <MapPin className="h-5 w-5" />
                              Gerenciamento de Localizações
                            </CardTitle>
                            <CardDescription>
                              Cadastre e gerencie endereços e locais físicos da empresa
                            </CardDescription>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              onClick={handleCreateLocation}
                              disabled={!canCreateLocations || isLoadingCreatePermission || !canCreate() || isLoadingLimits}
                              className="bg-red-500 hover:bg-red-600 text-white"
                              title={
                                !canCreateLocations 
                                  ? "Você não tem permissão para criar localizações" 
                                  : !canCreate()
                                  ? `Limite de ${limits?.maxLocations} localidades atingido no plano ${limits?.currentPlan}`
                                  : "Criar nova localização"
                              }
                            >
                              {isLoadingCreatePermission || isLoadingLimits ? (
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              ) : (
                                <Plus className="h-4 w-4 mr-2" />
                              )}
                              Cadastrar Localidade
                            </Button>
                            <Button
                              onClick={handleRefresh}
                              disabled={isLoadingLocations}
                              variant="outline"
                            >
                              <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingLocations ? 'animate-spin' : ''}`} />
                              Atualizar
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <LocationManagement 
                          locations={locationsData}
                          onEditLocation={handleEditLocation}
                        />
                      </CardContent>
                    </Card>
                  </motion.div>
                </>
              )}
            </motion.div>
          
            {/* SideSheets de Ajuda */}
            <SideSheet
              isOpen={helpSheets.total}
              onClose={() => toggleHelpSheet('total')}
              title="Total de Localizações"
              description="Entenda a distribuição das localizações da empresa"
              size="lg"
            >
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Localizações Cadastradas
                  </h4>
                  <p className="text-gray-600">
                    Representa o número total de endereços e locais físicos cadastrados para a empresa.
                    Incluindo sede, filiais, escritórios, centros de distribuição e outros pontos operacionais.
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Utilização:</h4>
                  <ul className="list-disc list-inside text-gray-600 space-y-1">
                    <li>Cadastro de unidades organizacionais</li>
                    <li>Endereçamento de funcionários</li>
                    <li>Controle logístico e operacional</li>
                    <li>Relatórios geográficos</li>
                  </ul>
                </div>
              </div>
            </SideSheet>

            <SideSheet
              isOpen={helpSheets.states}
              onClose={() => toggleHelpSheet('states')}
              title="Distribuição por Estados"
              description="Presença geográfica da empresa por estado"
              size="lg"
            >
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
                    <Map className="h-5 w-5" />
                    Cobertura Estadual
                  </h4>
                  <p className="text-gray-600">
                    Quantidade de estados onde a empresa possui localizações cadastradas.
                    Indica a abrangência geográfica da operação.
                  </p>
                </div>
                {locationStats && Object.keys(locationStats.byState).length > 0 && (
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-2">Estados Ativos:</h4>
                    <div className="space-y-1">
                      {Object.entries(locationStats.byState).map(([state, count]) => (
                        <div key={state} className="flex justify-between text-sm">
                          <span className="text-gray-600">{state}</span>
                          <span className="font-medium">{(count as number)} {(count as number) === 1 ? 'localização' : 'localizações'}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </SideSheet>

            <SideSheet
              isOpen={helpSheets.cities}
              onClose={() => toggleHelpSheet('cities')}
              title="Distribuição por Cidades"
              description="Presença municipal da empresa"
              size="lg"
            >
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
                    <Navigation className="h-5 w-5" />
                    Cobertura Municipal
                  </h4>
                  <p className="text-gray-600">
                    Quantidade de cidades onde a empresa possui endereços cadastrados.
                    Representa a capilaridade da operação no território nacional.
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Vantagens da Distribuição:</h4>
                  <ul className="list-disc list-inside text-gray-600 space-y-1">
                    <li>Proximidade com clientes</li>
                    <li>Redução de custos logísticos</li>
                    <li>Presença local no mercado</li>
                    <li>Diversificação de riscos</li>
                  </ul>
                </div>
              </div>
            </SideSheet>

            <SideSheet
              isOpen={helpSheets.average}
              onClose={() => toggleHelpSheet('average')}
              title="Média de Localizações por Estado"
              description="Densidade operacional da empresa"
              size="lg"
            >
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Densidade Operacional
                  </h4>
                  <p className="text-gray-600">
                    Média de localizações por estado onde a empresa atua.
                    Indicador da concentração de operações por região.
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Interpretação:</h4>
                  <ul className="list-disc list-inside text-gray-600 space-y-1">
                    <li><strong>Alta concentração:</strong> Operação focada em poucos estados</li>
                    <li><strong>Distribuição equilibrada:</strong> Presença uniforme</li>
                    <li><strong>Baixa concentração:</strong> Operação pulverizada</li>
                  </ul>
                </div>
                {locationStats && (
                  <div className="bg-purple-50 p-3 rounded-lg">
                    <p className="text-purple-800 text-sm">
                      <strong>Valor atual:</strong> {
                        Object.keys(locationStats.byState).length > 0 
                          ? `${Math.round(locationStats.total / Object.keys(locationStats.byState).length * 10) / 10} localizações por estado`
                          : 'Nenhuma localização cadastrada'
                      }
                    </p>
                  </div>
                )}
              </div>
            </SideSheet>

            {/* Sheet para criar nova localização */}
            <Sheet open={isAddLocationSheetOpen} onOpenChange={setIsAddLocationSheetOpen}>
              <SheetContent 
                side="right" 
                className="w-full sm:w-[600px] sm:max-w-[600px] flex flex-col"
                style={{
                  zIndex: 999999,
                  position: 'fixed',
                  overflow: 'visible'
                }}
              >
                <SheetHeader className="space-y-4 pb-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg">
                        <MapPin className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <SheetTitle className="text-xl font-bold">Nova Localização</SheetTitle>
                        <SheetDescription>
                          Adicione uma nova localização para a empresa
                        </SheetDescription>
                      </div>
                    </div>
                  </div>
                </SheetHeader>
                
                <div className="flex-1 overflow-y-auto">
                  <LocationForm
                    onSubmit={handleCreateLocationSubmit}
                    onCancel={handleCloseCreateSheet}
                    submitText="Criar Localização"
                    isSubmitting={createLocationMutation.isPending}
                  />
                </div>
              </SheetContent>
            </Sheet>

            {/* Sheet para editar localização */}
            <Sheet open={isEditLocationSheetOpen} onOpenChange={setIsEditLocationSheetOpen}>
              <SheetContent 
                side="right" 
                className="w-full sm:w-[600px] sm:max-w-[600px] flex flex-col"
                style={{
                  zIndex: 999999,
                  position: 'fixed',
                  overflow: 'visible'
                }}
              >
                <SheetHeader className="space-y-4 pb-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg">
                        <MapPin className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <SheetTitle className="text-xl font-bold">Editar Localização</SheetTitle>
                        <SheetDescription>
                          Atualize as informações da localização
                        </SheetDescription>
                      </div>
                    </div>
                  </div>
                </SheetHeader>
                
                <div className="flex-1 overflow-y-auto">
                  {editingLocation && (
                    <LocationForm
                      initialValues={{
                        address: editingLocation.address || "",
                        city: editingLocation.city,
                        state: editingLocation.state,
                        postal_code: editingLocation.postal_code || "",
                      }}
                      onSubmit={handleUpdateLocationSubmit}
                      onCancel={handleCloseEditSheet}
                      submitText="Salvar Alterações"
                      isSubmitting={updateLocationMutation.isPending}
                    />
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </AdminLayout>
    </MainLayout>
  );
} 