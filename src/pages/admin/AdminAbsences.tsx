/**
 * AdminAbsences - Página de Gestão Administrativa de Ausências
 * 
 * Interface completa para RH e administradores gerenciarem ausências:
 * - Visualizar todas as ausências da empresa
 * - Aprovar/rejeitar ausências pendentes 
 * - Registrar ausências para outros usuários
 * - Relatórios e estatísticas
 * - Configurar tipos de ausência
 * 
 * <AUTHOR> Internet 2025
 */
import { useState } from "react";
import { motion } from "framer-motion";
import { MainLayout } from "@/components/layout/MainLayout";
import { AdminLayout } from "@/components/layout/AdminLayout";
import { HeroSection } from "@/components/common/HeroSection";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  ClipboardCheck, 
  Users, 
  Calendar, 
  CheckCircle2, 
  XCircle, 
  Clock, 
  UserPlus,
  BarChart3,
  Settings,
  AlertTriangle,
  TrendingUp,
  FileText,
  Plus
} from "lucide-react";
import { GenericPermissionGate } from "@/components/auth/GenericPermissionGate";
import { AbsencesList } from "@/components/people/AbsencesList";
import { AbsenceRegistrationDialog } from "@/components/people/AbsenceRegistrationDialog";
import { AbsenceApprovalDialog } from "@/components/people/AbsenceApprovalDialog";
import { AbsenceTypesManager } from "@/components/people/AbsenceTypesManager";
import { useUserAbsences, usePendingAbsences } from "@/lib/query/hooks/useAbsences";
import { AdvancedRefreshButton } from "@/components/ui/advanced-refresh-button";

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export default function AdminAbsences() {
  const [activeTab, setActiveTab] = useState("pending");

  // Hooks para dados
  const { data: allAbsences = [], isLoading: loadingAll, refetch: refetchAll } = useUserAbsences({
    include_team: true
  });
  const { data: pendingAbsences = [], isLoading: loadingPending, refetch: refetchPending } = usePendingAbsences();

  // Estatísticas rápidas
  const stats = {
    pending: pendingAbsences.length,
    approved: allAbsences.filter(a => a.status === 'approved').length,
    rejected: allAbsences.filter(a => a.status === 'rejected').length,
    total: allAbsences.length,
  };

  // Variantes de animação
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  // =====================================================
  // COMPONENTES AUXILIARES
  // =====================================================

  const StatCard = ({ 
    title, 
    value, 
    icon: Icon, 
    color, 
    description 
  }: { 
    title: string; 
    value: number; 
    icon: React.ElementType; 
    color: string; 
    description: string;
  }) => (
    <motion.div variants={cardVariants}>
      <Card className="border-0 bg-gradient-to-br from-white to-slate-50 shadow-lg hover:shadow-xl transition-all">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">{title}</p>
              <p className="text-3xl font-bold">{value}</p>
              <p className="text-xs text-muted-foreground mt-1">{description}</p>
            </div>
            <div className={`p-3 rounded-full bg-gradient-to-r ${color}`}>
              <Icon className="h-6 w-6 text-white" />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );

  const PendingAbsenceCard = ({ absence }: { absence: any }) => (
    <Card className="hover:shadow-md transition-all">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: absence.absence_type_color }}
              />
              <span className="font-medium">{absence.absence_type_name}</span>
              <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                Pendente
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              <strong>{absence.user_name}</strong> • {absence.start_date} - {absence.end_date}
            </p>
            <p className="text-sm">{absence.reason}</p>
          </div>
          <AbsenceApprovalDialog
            absence={absence}
            trigger={
              <Button size="sm" variant="outline">
                <CheckCircle2 className="h-4 w-4 mr-1" />
                Avaliar
              </Button>
            }
            onApprovalComplete={() => {
              refetchPending();
              refetchAll();
            }}
          />
        </div>
      </CardContent>
    </Card>
  );

  // =====================================================
  // RENDER
  // =====================================================

  return (
    <MainLayout>
      <AdminLayout>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-6"
        >
          {/* Hero Section */}
          <HeroSection
            title="Gestão de Ausências"
            subtitle="Interface administrativa para gerenciar ausências dos colaboradores"
            icon={ClipboardCheck}
            gradient="from-rose-600 via-pink-600 to-orange-600"
            actions={
              <div className="flex items-center gap-3">
                <GenericPermissionGate
                  resourceTypeKey="user_absence"
                  actionKey="register_user_absences"
                  fallbackComponent={null}
                >
                  <AbsenceRegistrationDialog
                    trigger={
                      <Button className="bg-white text-rose-600 hover:bg-rose-50">
                        <UserPlus className="mr-2 h-4 w-4" />
                        Registrar Ausência
                      </Button>
                    }
                    onSuccess={() => {
                      refetchAll();
                      refetchPending();
                    }}
                  />
                </GenericPermissionGate>
                <AdvancedRefreshButton
                  variant="ghost"
                  className="text-white hover:bg-white/10 border border-white/20"
                  onRefresh={async () => {
                    await Promise.all([refetchAll(), refetchPending()]);
                  }}
                  operationName="Ausências"
                  successMessage="Dados de ausências atualizados!"
                  enableSound={true}
                />
              </div>
            }
          />

          {/* Estatísticas Rápidas */}
          <motion.div variants={containerVariants} className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatCard
              title="Pendentes"
              value={stats.pending}
              icon={AlertTriangle}
              color="from-amber-500 to-yellow-500"
              description="Aguardando aprovação"
            />
            <StatCard
              title="Aprovadas"
              value={stats.approved}
              icon={CheckCircle2}
              color="from-green-500 to-emerald-500"
              description="Ausências aprovadas"
            />
            <StatCard
              title="Rejeitadas"
              value={stats.rejected}
              icon={XCircle}
              color="from-red-500 to-rose-500"
              description="Ausências rejeitadas"
            />
            <StatCard
              title="Total"
              value={stats.total}
              icon={Users}
              color="from-blue-500 to-indigo-500"
              description="Todas as ausências"
            />
          </motion.div>

          {/* Tabs de Gestão */}
          <motion.div variants={cardVariants}>
            <Card className="border-0 bg-gradient-to-br from-white to-slate-50 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ClipboardCheck className="h-5 w-5 text-rose-500" />
                  Gerenciamento de Ausências
                </CardTitle>
                <CardDescription>
                  Visualize, aprove e gerencie todas as ausências da empresa
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid grid-cols-2 lg:grid-cols-4 h-auto p-2 bg-background/80 backdrop-blur-sm rounded-xl border shadow-lg">
                    <TabsTrigger 
                      value="pending" 
                      className="flex items-center gap-2 py-3 px-4 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-500 data-[state=active]:to-yellow-500 data-[state=active]:text-white"
                    >
                      <Clock className="h-4 w-4" />
                      <span className="font-medium">Pendentes</span>
                      {stats.pending > 0 && (
                        <Badge variant="secondary" className="bg-amber-100 text-amber-700">
                          {stats.pending}
                        </Badge>
                      )}
                    </TabsTrigger>
                    <TabsTrigger 
                      value="all" 
                      className="flex items-center gap-2 py-3 px-4 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-indigo-500 data-[state=active]:text-white"
                    >
                      <Users className="h-4 w-4" />
                      <span className="font-medium">Todas</span>
                    </TabsTrigger>
                    <TabsTrigger 
                      value="reports" 
                      className="flex items-center gap-2 py-3 px-4 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-500 data-[state=active]:text-white"
                    >
                      <BarChart3 className="h-4 w-4" />
                      <span className="font-medium">Relatórios</span>
                    </TabsTrigger>
                    <TabsTrigger 
                      value="settings" 
                      className="flex items-center gap-2 py-3 px-4 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-500 data-[state=active]:text-white"
                    >
                      <Settings className="h-4 w-4" />
                      <span className="font-medium">Configurações</span>
                    </TabsTrigger>
                  </TabsList>

                  {/* Tab: Ausências Pendentes */}
                  <TabsContent value="pending" className="space-y-6 mt-6">
                    <div className="space-y-4">
                      {loadingPending ? (
                        <div className="space-y-3">
                          {[1, 2, 3].map((i) => (
                            <div key={i} className="h-20 bg-gray-200 rounded-lg animate-pulse" />
                          ))}
                        </div>
                      ) : pendingAbsences.length === 0 ? (
                        <Card className="p-8 text-center">
                          <CheckCircle2 className="h-12 w-12 mx-auto text-green-500 mb-4" />
                          <h3 className="text-lg font-medium mb-2">Nenhuma ausência pendente!</h3>
                          <p className="text-muted-foreground">
                            Todas as ausências foram processadas ou não há solicitações no momento.
                          </p>
                        </Card>
                      ) : (
                        <div className="space-y-3">
                          {pendingAbsences.map((absence) => (
                            <PendingAbsenceCard key={absence.id} absence={absence} />
                          ))}
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  {/* Tab: Todas as Ausências */}
                  <TabsContent value="all" className="mt-6">
                    <AbsencesList
                      userSpecific={false}
                      showAdminControls={true}
                      onAbsenceSelect={(absence) => {
                        // TODO: Implementar visualização detalhada
                        console.log('Visualizar ausência:', absence);
                      }}
                    />
                  </TabsContent>

                  {/* Tab: Relatórios */}
                  <TabsContent value="reports" className="mt-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <TrendingUp className="h-5 w-5 text-blue-500" />
                            Relatórios em Desenvolvimento
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-muted-foreground">
                            Relatórios detalhados de ausências, tendências e análises em breve.
                          </p>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <FileText className="h-5 w-5 text-green-500" />
                            Exportação de Dados
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-muted-foreground">
                            Funcionalidade de exportação para Excel/PDF em desenvolvimento.
                          </p>
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>

                  {/* Tab: Configurações */}
                  <TabsContent value="settings" className="mt-6">
                    <GenericPermissionGate
                      resourceTypeKey="absence_type"
                      actionKey="manage_absence_types"
                      fallbackComponent={
                        <Card className="p-8 text-center">
                          <Settings className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                          <h3 className="text-lg font-medium mb-2">Sem Permissão</h3>
                          <p className="text-muted-foreground">
                            Você não tem permissão para gerenciar tipos de ausência. 
                            <br />
                            <small className="text-xs">Permissão necessária: manage_absence_types</small>
                          </p>
                        </Card>
                      }
                    >
                      <AbsenceTypesManager />
                    </GenericPermissionGate>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </AdminLayout>
    </MainLayout>
  );
} 