/**
 * People Hub - Sistema Global de Pessoas da Empresa
 * 
 * ATALHOS CONTEXTUAIS DISPONÍVEIS:
 * - S: Buscar pessoas
 * - F: Filtros avançados
 * - R: Recarregar dados
 * - N: Nova conexão/contato
 * - C: Abrir chat
 * - E: Enviar email
 * - D: Ver detalhes do perfil
 * - G: Alternar grid/lista
 * - O: Ordenação
 * - Escape: Limpar filtros
 * 
 * CONTEXTOS ESPECÍFICOS:
 * - Diretório: Busca e filtros de pessoas
 * - Perfil: Informações detalhadas e ações
 * - Networking: Sugestões de conexões
 * 
 * ATALHOS GLOBAIS:
 * - F1 ou ?: Mostrar todos os atalhos disponíveis
 * - Alt+P: Voltar para o People Hub (de qualquer lugar)
 * 
 * <AUTHOR> Internet 2025
 */
import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { useQueryClient } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Users,
  Search,
  Filter,
  Mail,
  MessageSquare,
  Phone,
  MapPin,
  Building2,
  Calendar,
  Clock,
  Star,
  Eye,
  Grid3X3,
  List,
  Settings,
  TrendingUp,
  Activity,
  Heart,
  Award,
  Trophy,
  Crown,
  Sparkles,
  Brain,
  Coffee,
  Target,
  Zap,
  Globe,
  Home,
  ChevronDown,
  SlidersHorizontal,
  UserCheck,
  Network,
  Briefcase,
  GraduationCap,
  Cake,
  Gift,
  PlusCircle
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Componentes especializados para People Hub
import { PeopleDirectoryWidget } from "@/components/people/widgets/PeopleDirectoryWidget";
import { PeopleStatsWidget } from "@/components/people/widgets/PeopleStatsWidget";
import { PeopleNetworkingWidget } from "@/components/people/widgets/PeopleNetworkingWidget";
import { OrganizationalPulseWidget } from "@/components/people/widgets/OrganizationalPulseWidget";
import { OrganizationChartFlow } from "@/components/admin/OrganizationChartFlow";

// Componentes de equipes específicos
import { TeamsList } from "@/components/team/TeamsList";
import { SimpleTeamForm } from "@/components/team/SimpleTeamForm";
import { TeamsLimitIndicator } from '@/components/teams/TeamsLimitIndicator';
import { useTeamsLimits } from '@/hooks/teams/useTeamsLimits';

// Componente de habilidades globais
import { GlobalTeamSkillsTab } from "@/components/teams/GlobalTeamSkillsTab";

// Componentes de desenvolvimento
import { 
  NetworkingDevelopmentPlaceholder, 
  AnalyticsDevelopmentPlaceholder 
} from "@/components/ui/DevelopmentPlaceholder";

// Widgets de eventos (reutilizados do TeamView)
import { BirthdayWidget } from "@/components/team/widgets/BirthdayWidget";
import { PromotionsWidget } from "@/components/team/widgets/PromotionsWidget";
import { AbsencesWidget } from "@/components/team/widgets/AbsencesWidget";
import { CompanyInsightsWidget } from "@/components/team/widgets/CompanyInsightsWidget";

// Sistema de notificações de aniversários
import { BirthdayDigestReal } from "@/components/notifications/BirthdayDigestReal";

// Hooks e integrações
import { useContextualHotkeys } from "@/lib/hooks/useContextualHotkeys";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
import { successWithNotification, errorWithNotification, infoWithNotification } from "@/lib/notifications/toastWithNotification";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import { GenericPermissionGate } from "@/components/auth/GenericPermissionGate";
import { QueryKeys } from "@/lib/query/queryKeys";
import { usePeopleDirectory, usePeopleAnalytics, type Person } from "@/hooks/usePeopleHub";
import { useBirthdaysThisMonth } from "@/hooks/useBirthdays";

// Utilitários de data - OBRIGATÓRIO para datas do Supabase
import { compareDatabaseDates } from "@/lib/utils/dateUtils";

// Types importados do hook
// Person e PeopleAnalytics são importados do hook usePeopleHub

interface FilterOptions {
  department?: string;
  location?: string;
  jobTitle?: string;
  skills?: string[];
  status?: string;
  team?: string;
}

// Variantes de animação premium
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

const heroVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

// Dados mock removidos - agora usando dados reais via hooks

// Função para mapear Person para Colleague (compatibilidade com widgets de eventos)
const mapPersonToColleague = (person: Person) => {
  // Lógica para determinar se é férias ou licença médica baseado no contexto
  let mappedStatus: "active" | "vacation" | "sick_leave" | "away" = "active";
  
  if (person.status === "away") {
    // Roberto (DevOps) e Felipe (QA) = férias
    // Juliana (Product Owner) = licença médica
    if (person.id === "4" || person.id === "6") {
      mappedStatus = "vacation";
    } else if (person.id === "5") {
      mappedStatus = "sick_leave";
    } else {
      mappedStatus = "away";
    }
  } else if (person.status === "online" || person.status === "busy") {
    mappedStatus = "active";
  } else {
    mappedStatus = "active";
  }

  return {
    id: person.id,
    full_name: person.full_name,
    avatar_url: person.avatar_url,
    email: person.email,
    phone: person.phone || null,
    birthday: person.birthday,
    location: person.location,
    hire_date: person.hire_date,
    status: mappedStatus,
    job_titles: person.job_title ? { 
      id: "1", 
      title: person.job_title, 
      department_id: "1" 
    } : undefined,
    teams: person.teams?.map(team => ({ id: team, name: team })),
    skills: person.skills,
    last_seen: person.last_seen,
    // Adicionar informações de ausência quando disponíveis
    ...(person.absence_info && {
      vacation_start: person.absence_info.start_date,
      vacation_end: person.absence_info.end_date,
      vacation_return: person.absence_info.return_date,
      vacation_type: person.absence_info.description || person.absence_info.subtype,
      absence_type: person.absence_info.type
    })
  };
};

export default function PeopleHub() {
  // Estado persistente
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useLocalStorage("peopleHub-activeTab", "directory");
  const [viewMode, setViewMode] = useLocalStorage<"grid" | "list">("peopleHub-viewMode", "grid");
  const [filters, setFilters] = useState<FilterOptions>({});
  const [sortBy, setSortBy] = useState<"name" | "department" | "hire_date" | "last_seen">("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [showCreateForm, setShowCreateForm] = useState(false);
  
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Hooks para dados reais
  const { data: people = [], isLoading: isLoadingPeople } = usePeopleDirectory();
  const { data: analytics, isLoading: isLoadingAnalytics } = usePeopleAnalytics();
  // TODO: Reativar quando função generate_networking_suggestions estiver implementada
  // const { data: networkingPeople = [] } = usePeopleNetworking();
  const networkingPeople: any[] = []; // Temporário para evitar erros
  const { data: birthdaysThisMonth = [], isLoading: isLoadingBirthdays } = useBirthdaysThisMonth();

  // Sistema de limites de equipes
  const { limits, isLoading: isLoadingLimits, canCreate, getErrorMessage, refetch: refetchLimits } = useTeamsLimits();

  // Callbacks para ações contextuais
  const handleFocusSearch = useCallback(() => {
    const searchInput = document.querySelector('input[placeholder*="Buscar pessoas"]') as HTMLInputElement;
    if (searchInput && searchInput.offsetParent !== null) {
      searchInput.focus();
      searchInput.select();
      logQueryEvent('PeopleHub', 'Atalho S: Campo de busca focado');
    }
  }, []);

  const handleToggleFilters = useCallback(() => {
    // TODO: Implementar toggle de filtros avançados
    logQueryEvent('PeopleHub', 'Atalho F: Filtros ativados');
    infoWithNotification('Filtros', {
      description: 'Filtros avançados em desenvolvimento.',
      persist: false
    });
  }, []);

  const handleRefreshData = useCallback(async () => {
    try {
      logQueryEvent('PeopleHub', 'Atalho R: Refreshing dados do People Hub');
      
      // TODO: Invalidar queries reais quando implementadas
      await queryClient.invalidateQueries({ queryKey: ['people'] });
      
      successWithNotification('Dados atualizados!', {
        description: 'Informações das pessoas foram recarregadas.',
        persist: false
      });
    } catch (error) {
      logQueryEvent('PeopleHub', 'Erro no refresh', { error }, 'error');
      errorWithNotification('Erro ao atualizar', {
        description: 'Não foi possível atualizar os dados.',
        persist: false
      });
    }
  }, [queryClient]);

  const handleToggleViewMode = useCallback(() => {
    const newMode = viewMode === "grid" ? "list" : "grid";
    setViewMode(newMode);
    logQueryEvent('PeopleHub', 'Atalho G: Modo de visualização alterado', { 
      from: viewMode, 
      to: newMode 
    });
  }, [viewMode, setViewMode]);

  const handleClearFilters = useCallback(() => {
    setFilters({});
    setSearchQuery("");
    logQueryEvent('PeopleHub', 'Atalho Escape: Filtros limpos');
    infoWithNotification('Filtros limpos', {
      description: 'Todos os filtros foram removidos.',
      persist: false
    });
  }, []);

  // TODO: Configurar atalhos contextuais (temporariamente removido para correção de linter)
  const contextualHotkeys: Array<{
    key: string;
    description: string;
    action: () => void;
  }> = [];

  // Filtrar pessoas baseado nos critérios
  const filteredPeople = people.filter(person => {
    const matchesSearch = person.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         person.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         person.phone?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         person.job_title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         person.department?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         person.skills?.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesDepartment = !filters.department || person.department === filters.department;
    const matchesLocation = !filters.location || person.location === filters.location;
    const matchesStatus = !filters.status || person.status === filters.status;

    return matchesSearch && matchesDepartment && matchesLocation && matchesStatus;
  });

  // Ordenar pessoas - Usando utilitários corretos para datas do Supabase
  const sortedPeople = [...filteredPeople].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case "name":
        comparison = a.full_name.localeCompare(b.full_name);
        break;
      case "department":
        comparison = (a.department || "").localeCompare(b.department || "");
        break;
      case "hire_date":
        // Usar compareDatabaseDates para evitar problemas de fuso horário
        comparison = compareDatabaseDates(a.hire_date || "1900-01-01", b.hire_date || "1900-01-01");
        break;
      case "last_seen":
        // Para last_seen (timestamp), podemos usar Date.getTime() normalmente
        // mas vamos tratar valores null/undefined
        const aLastSeen = a.last_seen ? new Date(a.last_seen).getTime() : 0;
        const bLastSeen = b.last_seen ? new Date(b.last_seen).getTime() : 0;
        comparison = aLastSeen - bLastSeen;
        break;
    }
    
    return sortOrder === "asc" ? comparison : -comparison;
  });

  // Log de acesso à página
  useEffect(() => {
    logQueryEvent('PeopleHub', 'Acessando People Hub', {
      searchQuery,
      activeTab,
      viewMode,
      filtersActive: Object.keys(filters).length > 0
    });
  }, [searchQuery, activeTab, viewMode, filters]);

  // Botões removidos do header para design mais clean

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
        <div className="container py-1 px-4 space-y-4">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Hero Section Premium - Inspirado no KnowledgeHub */}
            <motion.div variants={heroVariants} className="space-y-4 mb-6">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 p-6 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 rounded-2xl text-white shadow-xl">
                <div className="flex items-center gap-4">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="p-3 bg-white/10 rounded-xl backdrop-blur-sm"
                  >
                    <Users className="h-8 w-8 text-amber-300" />
                  </motion.div>
                  <div>
                    <h1 className="text-3xl lg:text-4xl font-bold text-white drop-shadow-lg">People Hub</h1>
                    <p className="text-white/90 text-lg font-medium">Conecte-se com pessoas incríveis da empresa</p>
                  </div>
                  
                  {/* Indicator de atalhos disponíveis */}
                  {contextualHotkeys.length > 0 && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="hidden lg:flex items-center gap-2 px-3 py-1 bg-white/10 rounded-full backdrop-blur-sm cursor-help">
                          <Zap className="h-4 w-4 text-amber-300" />
                          <span className="text-sm text-white/90">{contextualHotkeys.length} atalhos ativos</span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="max-w-xs">
                          <p className="font-medium mb-2">Atalhos disponíveis:</p>
                          <div className="grid grid-cols-2 gap-1 text-xs">
                            {contextualHotkeys.slice(0, 6).map((hotkey, index) => (
                              <div key={index} className="flex items-center gap-1">
                                <kbd className="px-1 py-0.5 bg-muted rounded text-xs">{hotkey.key.toUpperCase()}</kbd>
                                <span className="truncate">{hotkey.description}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
              </div>
            </motion.div>

            {/* Main Content - Começando com aba Diretório */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-2 lg:grid-cols-5 h-auto p-2 bg-background/80 backdrop-blur-sm rounded-xl border shadow-lg">
                <TabsTrigger 
                  value="directory" 
                  className="flex items-center gap-2 py-4 px-6 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-indigo-500 data-[state=active]:text-white"
                  title="Pessoas (D)"
                >
                  <Users className="h-4 w-4" />
                  <span className="font-medium">Pessoas</span>
                </TabsTrigger>
                <TabsTrigger 
                  value="teams" 
                  className="flex items-center gap-2 py-4 px-6 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-teal-500 data-[state=active]:text-white"
                  title="Equipes (T)"
                >
                  <UserCheck className="h-4 w-4" />
                  <span className="font-medium">Equipes</span>
                </TabsTrigger>
                <TabsTrigger 
                  value="organogram" 
                  className="flex items-center gap-2 py-4 px-6 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-purple-500 data-[state=active]:text-white"
                  title="Organograma interativo (O)"
                >
                  <Building2 className="h-4 w-4" />
                  <span className="font-medium">Organograma</span>
                </TabsTrigger>
                <TabsTrigger 
                  value="events" 
                  className="flex items-center gap-2 py-4 px-6 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-orange-500 data-[state=active]:text-white"
                  title="Eventos e celebrações (E)"
                >
                  <Calendar className="h-4 w-4" />
                  <span className="font-medium">Eventos</span>
                </TabsTrigger>
                <TabsTrigger 
                  value="pulse" 
                  className="flex items-center gap-2 py-4 px-6 rounded-lg transition-all data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white"
                  title="Pulse organizacional - Métricas e insights"
                >
                  <Activity className="h-4 w-4" />
                  <span className="font-medium">Pulse</span>
                </TabsTrigger>
              </TabsList>

              {/* Directory Tab - FOCO PRINCIPAL */}
              <TabsContent value="directory" className="space-y-6 mt-6">
                <motion.div variants={containerVariants} initial="hidden" animate="visible">
                  <GenericPermissionGate 
                    resourceTypeKey="people" 
                    actionKey="people_directory_view"
                    fallbackComponent={
                      <Card>
                        <CardContent className="p-8 text-center">
                          <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                          <p className="text-muted-foreground">Sem permissão para visualizar diretório de pessoas</p>
                        </CardContent>
                      </Card>
                    }
                  >
                    <PeopleDirectoryWidget
                      people={sortedPeople}
                      searchQuery={searchQuery}
                      onSearchChange={setSearchQuery}
                      viewMode={viewMode}
                      onViewModeChange={setViewMode}
                      filters={filters}
                      onFiltersChange={setFilters}
                      sortBy={sortBy}
                      onSortByChange={setSortBy}
                      sortOrder={sortOrder}
                      onSortOrderChange={setSortOrder}
                      isLoading={isLoadingPeople}
                    />
                  </GenericPermissionGate>
                </motion.div>
              </TabsContent>

              {/* Teams Tab */}
              <TabsContent value="teams" className="space-y-6 mt-6">
                <motion.div variants={containerVariants} initial="hidden" animate="visible">
                  <GenericPermissionGate 
                    resourceTypeKey="teams" 
                    actionKey="teams_view"
                    fallbackComponent={
                      <Card>
                        <CardContent className="p-8 text-center">
                          <UserCheck className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                          <p className="text-muted-foreground">Sem permissão para visualizar equipes</p>
                        </CardContent>
                      </Card>
                    }
                  >
                    <div className="space-y-6">
                      {/* Header Section Premium */}
                      <motion.div 
                        variants={cardVariants} 
                        className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 p-6 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl border border-emerald-200/50"
                      >
                        <div>
                          <h2 className="text-2xl font-bold text-emerald-900 mb-2">
                            Minhas Equipes
                          </h2>
                          <p className="text-emerald-700/80">
                            Gerencie suas equipes, adicione membros e colabore eficientemente
                          </p>
                        </div>
                        <GenericPermissionGate
                          resourceTypeKey="team"
                          actionKey="team_create"
                          fallbackComponent={
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button variant="outline" disabled className="bg-white/50">
                                  <PlusCircle className="mr-2 h-4 w-4" />
                                  Nova Equipe
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Sem permissão para criar equipes</p>
                              </TooltipContent>
                            </Tooltip>
                          }
                        >
                          <Button 
                            className="bg-emerald-600 hover:bg-emerald-700 text-white shadow-lg"
                            onClick={() => {
                              // Verificar limites antes de abrir o modal
                              if (!canCreate()) {
                                errorWithNotification("Limite de equipes atingido", {
                                  description: getErrorMessage(),
                                  persist: true
                                });
                                return;
                              }
                              setShowCreateForm(true);
                            }}
                            disabled={isLoadingLimits || !canCreate()}
                            title={
                              !canCreate()
                                ? `Limite de ${limits?.maxTeams} equipes atingido no plano ${limits?.currentPlan}`
                                : "Criar nova equipe"
                            }
                          >
                            <PlusCircle className="mr-2 h-4 w-4" />
                            Nova Equipe
                          </Button>
                        </GenericPermissionGate>
                      </motion.div>

                      {/* Indicador de Limites de Equipes */}
                      <motion.div variants={cardVariants}>
                        <TeamsLimitIndicator />
                      </motion.div>

                      {/* Teams Grid Premium */}
                      <motion.div variants={cardVariants}>
                        <Card className="border-0 bg-gradient-to-br from-white to-slate-50 shadow-xl">
                          <CardHeader className="border-b border-slate-200/60 bg-gradient-to-r from-slate-50 to-white">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="p-2 bg-emerald-100 rounded-lg">
                                  <Users className="h-5 w-5 text-emerald-600" />
                                </div>
                                <div>
                                  <CardTitle className="text-xl text-slate-800">
                                    Suas Equipes
                                  </CardTitle>
                                  <CardDescription className="text-slate-600">
                                    Equipes das quais você faz parte ou gerencia
                                  </CardDescription>
                                </div>
                              </div>
                              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
                                Colaboração
                              </Badge>
                            </div>
                          </CardHeader>
                          <CardContent className="p-6">
                            <TeamsList />
                          </CardContent>
                        </Card>
                      </motion.div>
                    </div>

                    {/* Modal de criação de equipe */}
                    {showCreateForm && (
                      <SimpleTeamForm
                        onClose={() => setShowCreateForm(false)}
                        onSuccess={() => {
                          setShowCreateForm(false);
                          queryClient.invalidateQueries({ queryKey: QueryKeys.teams.all() });
                        }}
                      />
                    )}
                  </GenericPermissionGate>
                </motion.div>
              </TabsContent>

              {/* Organogram Tab */}
              <TabsContent value="organogram" className="space-y-6 mt-6">
                <motion.div variants={containerVariants} initial="hidden" animate="visible">
                  <GenericPermissionGate 
                    resourceTypeKey="people" 
                    actionKey="people_organogram_view"
                    fallbackComponent={
                      <Card>
                        <CardContent className="p-8 text-center">
                          <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                          <p className="text-muted-foreground">Sem permissão para visualizar organograma</p>
                        </CardContent>
                      </Card>
                    }
                  >
                    <motion.div variants={cardVariants}>
                      <Card className="border-0 bg-gradient-to-br from-white to-indigo-50 shadow-lg">
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Building2 className="h-5 w-5 text-indigo-500" />
                            Organograma Interativo
                            <Badge variant="outline" className="bg-indigo-100 text-indigo-700">
                              Estrutura Organizacional
                            </Badge>
                          </CardTitle>
                          <CardDescription>
                            Visualização hierárquica da estrutura organizacional da empresa
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="p-6">
                          <OrganizationChartFlow />
                        </CardContent>
                      </Card>
                    </motion.div>
                  </GenericPermissionGate>
                </motion.div>
              </TabsContent>


              {/* Events Tab */}
              <TabsContent value="events" className="space-y-6 mt-6">
                <motion.div variants={containerVariants} initial="hidden" animate="visible">
                  <GenericPermissionGate 
                    resourceTypeKey="people" 
                    actionKey="people_events_view"
                    fallbackComponent={
                      <Card>
                        <CardContent className="p-8 text-center">
                          <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                          <p className="text-muted-foreground">Sem permissão para visualizar eventos</p>
                        </CardContent>
                      </Card>
                    }
                  >
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                      {/* Coluna esquerda - Widgets menores */}
                      <div className="lg:col-span-2 space-y-6">
                        <motion.div variants={cardVariants}>
                          <BirthdayWidget 
                            colleagues={birthdaysThisMonth.map(person => ({
                              id: person.id,
                              full_name: person.full_name,
                              avatar_url: person.avatar_url,
                              birthdate: person.birthday,
                              job_titles: person.job_title ? { title: person.job_title } : undefined
                            }))} 
                          />
                        </motion.div>
                        
                        {/* Sistema de Notificações de Aniversários */}
                        <motion.div variants={cardVariants}>
                          <BirthdayDigestReal />
                        </motion.div>
                        
                        <motion.div variants={cardVariants}>
                          <PromotionsWidget />
                        </motion.div>
                        <motion.div variants={cardVariants}>
                          <AbsencesWidget compact={true} userOnly={false} />
                        </motion.div>
                      </div>
                      
                      {/* Coluna direita - Widget "Por dentro da empresa" */}
                      <div className="lg:col-span-1">
                        <motion.div variants={cardVariants}>
                          <CompanyInsightsWidget />
                        </motion.div>
                      </div>
                    </div>
                  </GenericPermissionGate>
                </motion.div>
              </TabsContent>

              {/* Pulse Tab */}
              <TabsContent value="pulse" className="space-y-6 mt-6">
                <motion.div variants={containerVariants} initial="hidden" animate="visible">
                  <GenericPermissionGate 
                    resourceTypeKey="people" 
                    actionKey="people_analytics_view"
                    fallbackComponent={
                      <Card>
                        <CardContent className="p-8 text-center">
                          <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                          <p className="text-muted-foreground">Sem permissão para visualizar pulse organizacional</p>
                        </CardContent>
                      </Card>
                    }
                  >
                    <div className="space-y-6">
                      {/* Organizational Pulse Widget */}
                      <motion.div variants={cardVariants}>
                        <OrganizationalPulseWidget />
                      </motion.div>
                      
                      {/* Habilidades Globais - movido do TeamView */}
                      <motion.div variants={cardVariants}>
                        <GlobalTeamSkillsTab />
                      </motion.div>
                    </div>
                  </GenericPermissionGate>
                </motion.div>
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
      </div>
    </TooltipProvider>
  );
} 