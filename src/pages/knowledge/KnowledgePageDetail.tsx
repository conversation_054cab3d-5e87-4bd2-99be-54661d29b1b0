/**
 * <PERSON><PERSON><PERSON><PERSON> de detalhes de uma página de conhecimento específica
 * <AUTHOR> Internet 2025
 */
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useParams, useNavigate } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  BookOpen,
  ArrowLeft,
  Star,
  Clock,
  Eye,
  MessageSquare,
  Heart,
  Share,
  Edit,
  History,
  Users,
  Code,
  Megaphone,
  Package,
  Settings,
  Loader2,
  Globe,
  Lock,
  Home,
  UserCheck,
  Calendar,
  Tag,
  FileText,
  ChevronRight,
  <PERSON>
} from "lucide-react";
import { useKnowledgePage } from "@/lib/query/hooks/useKnowledgePages";
import { useShareKnowledgePage } from "@/lib/query/hooks/useKnowledgePageShares";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
import { errorWithNotification, successWithNotification } from "@/lib/notifications/toastWithNotification";
import KnowledgeComments from "@/components/knowledge/KnowledgeComments";
import { KnowledgePageMetrics } from "@/components/knowledge/KnowledgePageMetrics";

// Mapeamento de ícones para evitar require dinâmico
const iconMap = {
  BookOpen,
  Code,
  Megaphone,
  Users,
  Package,
  Settings,
  FileText: BookOpen,
  Star,
  Heart,
  Share,
  Calendar,
  Globe,
  Brain
};

// Tipos para o conteúdo estruturado
interface ContentNode {
  type?: string;
  text?: string;
  content?: ContentNode[];
  attrs?: {
    level?: number;
    [key: string]: unknown;
  };
  marks?: Array<{
    type: string;
    [key: string]: unknown;
  }>;
  [key: string]: unknown;
}

interface TextNode {
  type: string;
  text: string;
  marks?: Array<{
    type: string;
    [key: string]: unknown;
  }>;
}

interface ListItem {
  content?: Array<{
    content?: Array<{
      text?: string;
    }>;
  }>;
}

// Variantes de animação premium
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

const heroVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

const floatingVariants = {
  animate: {
    y: [-10, 10, -10],
    transition: {
      duration: 6,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

const iconVariants = {
  hidden: { scale: 0, rotate: -180 },
  visible: {
    scale: 1,
    rotate: 0,
    transition: {
      type: "spring",
      stiffness: 260,
      damping: 20,
      delay: 0.3
    }
  }
};

export default function KnowledgePageDetail() {
  const { pageIdentifier } = useParams();
  const navigate = useNavigate();
  const [isLiked, setIsLiked] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);

  // Buscar dados reais da página (suporta UUID ou slug)
  const { data: page, isLoading, error } = useKnowledgePage(pageIdentifier || '');
  
  // Hook para compartilhamento
  const shareKnowledgePage = useShareKnowledgePage();

  useEffect(() => {
    if (!pageIdentifier) {
      logQueryEvent('KnowledgePageDetail', 'Identificador da página não fornecido', null, 'error');
      navigate('/knowledge');
      return;
    }

    if (error) {
      logQueryEvent('KnowledgePageDetail', 'Erro ao carregar página', error, 'error');
      errorWithNotification('Erro ao carregar página', {
        description: 'Não foi possível carregar os detalhes da página.',
        persist: true,
        notificationType: 'system_error'
      });
    }
  }, [pageIdentifier, error, navigate]);

  useEffect(() => {
    if (page) {
      logQueryEvent('KnowledgePageDetail', 'Página carregada', { id: page.id, title: page.title });
      // Valores padrão para campos que podem não existir ainda
      setIsLiked(page.is_liked_by_user || false);
      setIsFavorited(page.is_favorited_by_user || false);
    }
  }, [page]);

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
        <div className="container py-8 px-4 mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-center min-h-[60vh]"
          >
            <Card className="p-8 text-center max-w-md mx-auto shadow-xl border-0 bg-white/90 backdrop-blur-sm">
              <CardContent className="space-y-6">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  className="mx-auto"
                >
                  <div className="h-16 w-16 rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 flex items-center justify-center">
                    <BookOpen className="h-8 w-8 text-white" />
                  </div>
                </motion.div>
                <div className="space-y-3">
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                    Carregando página
                  </h2>
                  <p className="text-muted-foreground">Aguarde enquanto carregamos o conteúdo para você.</p>
                </div>
                <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
                  <motion.div 
                    className="h-full bg-gradient-to-r from-indigo-500 to-purple-500"
                    animate={{ x: [-100, 100, -100] }}
                    transition={{ repeat: Infinity, duration: 2, ease: "easeInOut" }}
                    style={{ width: "30%" }}
                  />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    );
  }

  // Error state ou página não encontrada
  if (!page || error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
        <div className="container py-8 px-4 mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-center min-h-[60vh]"
          >
            <Card className="p-8 text-center max-w-md mx-auto shadow-xl border-0 bg-white/90 backdrop-blur-sm">
              <CardContent className="space-y-6">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 200, damping: 20 }}
                  className="mx-auto"
                >
                  <div className="h-16 w-16 rounded-full bg-gradient-to-r from-red-500 to-orange-500 flex items-center justify-center">
                    <BookOpen className="h-8 w-8 text-white" />
                  </div>
                </motion.div>
                <div className="space-y-3">
                  <h2 className="text-2xl font-bold text-red-600">Página não encontrada</h2>
                  <p className="text-muted-foreground">
                    {error ? 'Erro ao carregar a página.' : 'A página solicitada não existe ou foi removida.'}
                  </p>
                </div>
                <Button 
                  onClick={() => navigate('/knowledge')}
                  className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white"
                  size="lg"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Voltar ao Knowledge Hub
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    );
  }

  const handleLike = () => {
    setIsLiked(!isLiked);
    successWithNotification(
      isLiked ? 'Like removido' : 'Página curtida!', 
      { 
        description: isLiked ? 'Você removeu sua curtida.' : 'Obrigado pelo feedback positivo!',
        persist: false 
      }
    );
  };

  const handleFavorite = () => {
    setIsFavorited(!isFavorited);
    successWithNotification(
      isFavorited ? 'Removido dos favoritos' : 'Adicionado aos favoritos!', 
      { 
        description: isFavorited ? 'Página removida dos seus favoritos.' : 'Página salva nos seus favoritos.',
        persist: false 
      }
    );
  };

  const handleShare = async () => {
    try {
      const pageUrl = `${window.location.origin}/knowledge/page/${page.slug || page.id}`;
      
      // Registrar compartilhamento no banco de dados
      let shareType: 'copy_link' | 'native_share' = 'copy_link';
      
      if (navigator.share) {
        // Se o dispositivo suporta Web Share API
        await navigator.share({
          title: page.title,
          text: page.excerpt || `Confira esta página: ${page.title}`,
          url: pageUrl
        });
        shareType = 'native_share';
        
        // Registrar compartilhamento
        await shareKnowledgePage.mutateAsync({
          pageId: page.id,
          shareType
        });
        
        successWithNotification('Página compartilhada!', {
          description: 'Link compartilhado com sucesso.',
          persist: false
        });
      } else {
        // Fallback: copiar para área de transferência
        await navigator.clipboard.writeText(pageUrl);
        
        // Registrar compartilhamento
        await shareKnowledgePage.mutateAsync({
          pageId: page.id,
          shareType: 'copy_link'
        });
        
        successWithNotification('Link copiado!', {
          description: 'O link da página foi copiado para a área de transferência.',
          persist: false
        });
      }
    } catch (error) {
      console.error('Erro ao compartilhar:', error);
      // Fallback manual se clipboard também falhar
      try {
        const pageUrl = `${window.location.origin}/knowledge/page/${page.slug || page.id}`;
        await navigator.clipboard.writeText(pageUrl);
        
        // Tentar registrar mesmo com erro
        await shareKnowledgePage.mutateAsync({
          pageId: page.id,
          shareType: 'copy_link'
        });
        
        successWithNotification('Link copiado!', {
          description: 'O link da página foi copiado para a área de transferência.',
          persist: false
        });
      } catch (clipboardError) {
        errorWithNotification('Erro ao compartilhar', {
          description: 'Não foi possível compartilhar ou copiar o link da página.',
          persist: false
        });
      }
    }
  };

  // Função para obter ícone do espaço dinamicamente
  const SpaceIconComponent = iconMap[page.space?.icon as keyof typeof iconMap] || BookOpen;

  // Função para renderizar badge de visibilidade
  const getVisibilityBadge = () => {
    const visibility = page.visibility || 'space';
    
    switch (visibility) {
      case 'company':
        return (
          <Badge variant="secondary" className="flex items-center gap-1 bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 border-blue-200">
            <Globe className="h-3 w-3" />
            Toda empresa
          </Badge>
        );
      case 'restricted':
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-gradient-to-r from-amber-50 to-orange-50 text-amber-700 border-amber-200">
            <Lock className="h-3 w-3" />
            Acesso restrito
          </Badge>
        );
      default:
        return (
          <Badge variant="default" className="flex items-center gap-1 bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200">
            <UserCheck className="h-3 w-3" />
            Membros do espaço
          </Badge>
        );
    }
  };

  // Renderizar conteúdo da página (HTML, JSON estruturado ou texto simples)
  const renderContent = (content: string) => {
    if (!content) {
      return (
        <div className="text-center py-12">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="space-y-4"
          >
            <div className="h-16 w-16 rounded-full bg-gradient-to-r from-gray-200 to-gray-300 flex items-center justify-center mx-auto">
              <FileText className="h-8 w-8 text-gray-500" />
            </div>
            <p className="text-muted-foreground text-lg">Conteúdo não disponível</p>
          </motion.div>
        </div>
      );
    }

    // Verificar se é HTML (conteúdo do TipTap)
    if (content.includes('<p') || content.includes('<h') || content.includes('<img') || content.includes('<ul') || content.includes('<ol')) {
      return (
        <div 
          className="prose prose-slate max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-p:leading-relaxed prose-strong:text-gray-900 prose-em:text-gray-600 prose-img:rounded-lg prose-img:shadow-lg"
          dangerouslySetInnerHTML={{ __html: content }}
        />
      );
    }

    // Se o conteúdo for JSON estruturado (formato antigo), tentar parseá-lo
    try {
      const parsedContent = JSON.parse(content);
      if (parsedContent.content) {
        return parsedContent.content.map((node: ContentNode, index: number) => {
          switch (node.type) {
            case 'heading': {
              const HeadingTag = `h${node.attrs?.level}` as keyof JSX.IntrinsicElements;
              const headingClasses = {
                1: 'text-4xl font-bold mt-8 mb-6 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent',
                2: 'text-3xl font-semibold mt-6 mb-4 text-gray-800',
                3: 'text-2xl font-semibold mt-5 mb-3 text-gray-800',
                4: 'text-xl font-semibold mt-4 mb-2 text-gray-700',
                5: 'text-lg font-semibold mt-3 mb-2 text-gray-700',
                6: 'text-base font-semibold mt-2 mb-1 text-gray-600'
              };
              return (
                <HeadingTag key={index} className={headingClasses[node.attrs?.level as keyof typeof headingClasses]}>
                  {node.content?.[0]?.text || ''}
                </HeadingTag>
              );
            }
            case 'paragraph':
              return (
                <p key={index} className="mb-4 leading-relaxed text-gray-700 text-base">
                  {node.content?.map((contentNode: ContentNode, textIndex: number) => {
                    if (contentNode.type === 'text') {
                      const textNode = contentNode as TextNode;
                      let text: React.ReactNode = textNode.text;
                      if (textNode.marks) {
                        textNode.marks.forEach((mark, markIndex) => {
                          if (mark.type === 'bold') {
                            text = <strong key={`bold-${textIndex}-${markIndex}`} className="font-semibold text-gray-900">{text}</strong>;
                          } else if (mark.type === 'italic') {
                            text = <em key={`italic-${textIndex}-${markIndex}`} className="italic text-gray-600">{text}</em>;
                          }
                        });
                      }
                      return <span key={textIndex}>{text}</span>;
                    }
                    return null;
                  })}
                </p>
              );
            case 'bulletList':
              return (
                <ul key={index} className="list-disc list-inside mb-6 space-y-2 text-gray-700 ml-4">
                  {node.content?.map((listItem: ListItem, listIndex: number) => (
                    <li key={listIndex} className="leading-relaxed">
                      {listItem.content?.[0]?.content?.[0]?.text || ''}
                    </li>
                  ))}
                </ul>
              );
            case 'orderedList':
              return (
                <ol key={index} className="list-decimal list-inside mb-6 space-y-2 text-gray-700 ml-4">
                  {node.content?.map((listItem: ListItem, listIndex: number) => (
                    <li key={listIndex} className="leading-relaxed">
                      {listItem.content?.[0]?.content?.[0]?.text || ''}
                    </li>
                  ))}
                </ol>
              );
            case 'codeBlock':
              return (
                <pre key={index} className="bg-gradient-to-r from-gray-900 to-gray-800 text-gray-100 p-6 rounded-xl mb-6 overflow-x-auto shadow-lg border">
                  <code className="text-sm font-mono">{node.content?.[0]?.text || ''}</code>
                </pre>
              );
            default:
              return null;
          }
        });
      }
    } catch {
      // Se não for JSON válido, renderizar como texto simples
      return (
        <div className="prose prose-slate max-w-none">
          {content.split('\n').map((line, index) => (
            <p key={index} className="mb-4 leading-relaxed text-gray-700 text-base">
              {line}
            </p>
          ))}
        </div>
      );
    }
    
    return null;
  };

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
        <div className="container max-w-7xl mx-auto px-4 py-6">
          <motion.div 
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            {/* Header Elegante - Estilo Knowledge Hub */}
            <motion.div variants={heroVariants}>
                             <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm overflow-hidden">
                <CardHeader className="pb-6">
                  {/* Breadcrumb */}
                  <div className="flex items-center gap-2 text-muted-foreground text-sm mb-6">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => navigate('/knowledge')}
                      className="p-0 h-auto font-normal hover:text-primary"
                    >
                      Knowledge Hub
                    </Button>
                    <ChevronRight className="h-4 w-4" />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => navigate(`/knowledge/space/${page.space?.id}`)}
                      className="p-0 h-auto font-normal hover:text-primary flex items-center gap-1"
                    >
                      <motion.div variants={iconVariants} initial="hidden" animate="visible">
                        <SpaceIconComponent className="h-4 w-4" style={{ color: page.space?.color }} />
                      </motion.div>
                      {page.space?.name}
                    </Button>
                    <ChevronRight className="h-4 w-4" />
                    <span className="font-medium text-foreground">{page.title}</span>
                  </div>

                  {/* Header Principal com destaque sutil da cor do espaço */}
                  <div className="relative p-6 rounded-xl bg-gradient-to-r from-slate-50 via-white to-slate-50 border-l-4 shadow-lg" 
                       style={{ borderLeftColor: page.space?.color || '#6366f1' }}>
                    {/* Elemento decorativo com a cor do espaço */}
                    <div className="absolute top-0 right-0 w-32 h-32 opacity-5 rounded-full -translate-y-16 translate-x-16"
                         style={{ backgroundColor: page.space?.color || '#6366f1' }}></div>
                    
                    <div className="flex items-start justify-between relative z-10">
                      <div className="flex-1 space-y-4">
                        <div className="flex items-start gap-4">
                          <motion.div 
                            variants={floatingVariants}
                            animate="animate"
                            className="p-3 rounded-xl shadow-lg"
                            style={{ 
                              backgroundColor: `${page.space?.color || '#6366f1'}15`,
                              border: `1px solid ${page.space?.color || '#6366f1'}30`
                            }}
                          >
                            <SpaceIconComponent 
                              className="h-8 w-8" 
                              style={{ color: page.space?.color || '#6366f1' }} 
                            />
                          </motion.div>
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-3">
                              <CardTitle className="text-3xl lg:text-4xl font-bold text-gray-900">
                                {page.title}
                              </CardTitle>
                              {page.is_homepage && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  transition={{ delay: 0.5, type: "spring" }}
                                >
                                  <Badge variant="secondary" className="flex items-center gap-1 bg-amber-100 text-amber-800 border-amber-200">
                                    <Home className="h-3 w-3" />
                                    Homepage
                                  </Badge>
                                </motion.div>
                              )}
                            </div>
                            {page.excerpt && (
                              <CardDescription className="text-gray-700 text-lg font-medium leading-relaxed">
                                {page.excerpt}
                              </CardDescription>
                            )}
                            
                            {/* Badges de configuração */}
                            <div className="flex items-center gap-2 mt-4">
                              {getVisibilityBadge()}
                              <Badge variant={page.status === 'published' ? 'default' : 'secondary'} 
                                className={page.status === 'published' 
                                  ? 'bg-green-100 text-green-800 border-green-200' 
                                  : 'bg-gray-100 text-gray-600 border-gray-200'
                                }>
                                {page.status === 'published' ? 'Publicado' : 'Rascunho'}
                              </Badge>
                              {!page.allow_comments && (
                                <Badge variant="outline" className="flex items-center gap-1 bg-red-50 text-red-700 border-red-200">
                                  <MessageSquare className="h-3 w-3" />
                                  Comentários desabilitados
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Ações do Header */}
                      <div className="flex items-center gap-2">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={handleFavorite}
                              className={`hover:bg-gray-100 ${isFavorited ? 'bg-amber-50 text-amber-600' : 'text-gray-600'}`}
                            >
                              <Star className={`h-4 w-4 ${isFavorited ? 'fill-amber-400 text-amber-500' : ''}`} />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            {isFavorited ? 'Remover dos favoritos' : 'Adicionar aos favoritos'}
                          </TooltipContent>
                        </Tooltip>
                        
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="text-gray-600 hover:bg-gray-100"
                              onClick={handleShare}
                              disabled={shareKnowledgePage.isPending}
                            >
                              {shareKnowledgePage.isPending ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Share className="h-4 w-4" />
                              )}
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Compartilhar página</TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="text-gray-600 hover:bg-gray-100"
                              onClick={() => navigate(`/knowledge/page/${page.slug}/edit`)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Editar página</TooltipContent>
                        </Tooltip>
                      </div>
                    </div>

                    {/* Informações do Autor e Métricas */}
                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-6">
                          <div className="flex items-center gap-3">
                            <Avatar className="h-10 w-10 ring-2 ring-gray-200">
                              <AvatarImage src={page.author?.avatar} />
                              <AvatarFallback className="bg-gray-100 text-gray-700">
                                {page.author?.full_name?.[0]}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="text-sm font-medium text-gray-900">{page.author?.full_name}</div>
                              <div className="text-xs text-gray-500 flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                Criado em {new Date(page.created_at).toLocaleDateString('pt-BR')}
                              </div>
                            </div>
                          </div>
                          <Separator orientation="vertical" className="h-10" />
                          
                          {/* Métricas interativas da página */}
                          <div className="flex-1">
                            <KnowledgePageMetrics 
                              pageId={page.id}
                              variant="default"
                              showInteractions={true}
                              className="text-gray-700"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
              </Card>
            </motion.div>

            {/* Grid Principal */}
            <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
              {/* Conteúdo Principal */}
              <motion.div variants={cardVariants} className="xl:col-span-3">
                                 <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
                  <CardContent className="p-8 lg:p-12">
                    {/* Tags */}
                    {page.tags && page.tags.length > 0 && (
                      <motion.div 
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                        className="flex flex-wrap gap-2 mb-8"
                      >
                        {page.tags.map((tag, index) => (
                          <motion.div
                            key={tag}
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.4 + index * 0.1 }}
                          >
                            <Badge variant="outline" className="text-xs bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 border-blue-200 hover:bg-blue-100 transition-colors">
                              <Tag className="h-3 w-3 mr-1" />
                              {tag}
                            </Badge>
                          </motion.div>
                        ))}
                      </motion.div>
                    )}

                    {/* Conteúdo da Página */}
                    <motion.div 
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5 }}
                      className="prose prose-slate max-w-none"
                    >
                      {renderContent(page.content)}
                    </motion.div>

                    {/* Última atualização */}
                    <motion.div 
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.7 }}
                      className="mt-12 pt-6 border-t border-gray-200"
                    >
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <History className="h-4 w-4" />
                        <span>
                          Última atualização: {new Date(page.updated_at).toLocaleString('pt-BR')} por {page.author?.full_name}
                        </span>
                      </div>
                    </motion.div>
                  </CardContent>
                </Card>

                {/* Seção de Comentários */}
                <motion.div variants={cardVariants} className="mt-6">
                  <KnowledgeComments 
                    pageId={page.id}
                    allowComments={page.allow_comments !== false}
                    initialCount={page.comments_count}
                  />
                </motion.div>
              </motion.div>

              {/* Sidebar Premium */}
              <motion.div variants={cardVariants} className="xl:col-span-1 space-y-6">
                                 {/* Informações do Espaço */}
                 <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
                   <CardHeader className="pb-4">
                     <CardTitle className="flex items-center gap-2 text-lg">
                       <SpaceIconComponent className="h-5 w-5" style={{ color: page.space?.color }} />
                       Espaço de Conhecimento
                     </CardTitle>
                   </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-4 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 border">
                      <h4 className="font-semibold text-gray-900 mb-2">{page.space?.name}</h4>
                      <p className="text-sm text-gray-600 leading-relaxed">{page.space?.description}</p>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full bg-gradient-to-r from-indigo-50 to-purple-50 hover:from-indigo-100 hover:to-purple-100 border-indigo-200 text-indigo-700"
                      onClick={() => navigate(`/knowledge/space/${page.space?.id}`)}
                    >
                      <BookOpen className="h-4 w-4 mr-2" />
                      Explorar Espaço
                    </Button>
                  </CardContent>
                </Card>

                                 {/* Configurações da Página */}
                 <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
                   <CardHeader className="pb-4">
                     <CardTitle className="flex items-center gap-2 text-lg">
                       <Settings className="h-5 w-5" />
                       Configurações
                     </CardTitle>
                   </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Visibilidade</span>
                        {getVisibilityBadge()}
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Comentários</span>
                        <Badge variant={page.allow_comments !== false ? "default" : "secondary"}
                          className={page.allow_comments !== false 
                            ? 'bg-green-100 text-green-800 border-green-200' 
                            : 'bg-gray-100 text-gray-600 border-gray-200'
                          }>
                          {page.allow_comments !== false ? "Habilitados" : "Desabilitados"}
                        </Badge>
                      </div>
                      {page.is_homepage && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Página inicial</span>
                          <Badge variant="secondary" className="flex items-center gap-1 bg-amber-100 text-amber-800 border-amber-200">
                            <Home className="h-3 w-3" />
                            Sim
                          </Badge>
                        </div>
                      )}
                      {page.visibility === 'restricted' && page.restricted_users && page.restricted_users.length > 0 && (
                        <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                          <span className="text-sm font-medium text-amber-800">Acesso restrito a:</span>
                          <p className="text-xs text-amber-700 mt-1">
                            {page.restricted_users.length} usuário(s) específico(s)
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                                 
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </TooltipProvider>
  );
} 