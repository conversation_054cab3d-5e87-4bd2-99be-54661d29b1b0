/**
 * Página de criação/edição de templates de conhecimento - Layout moderno e profissional
 * <AUTHOR> Internet 2025
 */
import { useState, useEffect, useCallback, useRef } from "react";
import { motion } from "framer-motion";
import { useNavigate, useSearchParams, useParams } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { PostEditor } from "@/components/editor/PostEditor";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  ArrowLeft,
  Save,
  Eye,
  Send,
  Target,
  FileText,
  Globe,
  Users,
  Type,
  Bold,
  Italic,
  List,
  ListOrdered,
  Quote,
  Code,
  Link,
  Image,
  Table,
  Heading1,
  Heading2,
  Heading3,
  Zap,
  Loader2,
  BookOpen,
  Hash,
  Lock,
  Layout,
  Megaphone,
  Package,
  HelpCircle,
  Settings,
  MessageCircle,
  Shield,
  UserPlus,
  X,
  User,
  Brain,
  Sparkles,
  Clock,
  Tag,
  Star,
  CheckCircle,
  AlertCircle,
  Lightbulb,
  Rocket,
  Heart,
  Edit3,
  Coffee,
  ChevronRight,
  Activity,
  Plus,
  TrendingUp,
  Search,
  Keyboard,
  Timer,
  RotateCcw,
  Check,
  AlertTriangle,
  Wand2,
  Maximize2,
  Copy,
  Palette,
  Layers
} from "lucide-react";
import { useKnowledgeTemplate, useCreateKnowledgeTemplate, useUpdateKnowledgeTemplate } from "@/lib/query/hooks/useKnowledgeTemplates";
import { successWithNotification, errorWithNotification } from "@/lib/notifications/toastWithNotification";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
import type { CreateKnowledgeTemplateData, UpdateKnowledgeTemplateData, KnowledgeTemplateCategory } from "@/types/knowledge.types";

// Variantes de animação premium
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

const heroVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

const floatingVariants = {
  animate: {
    y: [-10, 10, -10],
    transition: {
      duration: 6,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

// Categorias de templates disponíveis
const templateCategories: Array<{
  value: KnowledgeTemplateCategory;
  label: string;
  description: string;
}> = [
  { value: 'meeting', label: 'Reunião', description: 'Atas, retrospectivas, planning' },
  { value: 'project', label: 'Projeto', description: 'Planos, roadmaps, especificações' },
  { value: 'process', label: 'Processo', description: 'SOPs, procedimentos, workflows' },
  { value: 'decision', label: 'Decisão', description: 'ADRs, decisões técnicas' },
  { value: 'runbook', label: 'Runbook', description: 'Procedimentos operacionais' },
  { value: 'guide', label: 'Guia', description: 'Tutoriais, how-tos, manuais' },
  { value: 'other', label: 'Outro', description: 'Templates personalizados' }
];

// Níveis de dificuldade
const difficultyLevels = [
  { value: 'beginner', label: 'Iniciante', color: 'bg-green-100 text-green-700' },
  { value: 'intermediate', label: 'Intermediário', color: 'bg-yellow-100 text-yellow-700' },
  { value: 'advanced', label: 'Avançado', color: 'bg-red-100 text-red-700' }
];

export default function KnowledgeCreateTemplate() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { templateId } = useParams();
  const editingTemplateId = templateId || searchParams.get('edit');

  // Hooks para dados reais
  const { data: existingTemplate, isLoading: isLoadingTemplate } = useKnowledgeTemplate(editingTemplateId || '');
  const createTemplateMutation = useCreateKnowledgeTemplate();
  const updateTemplateMutation = useUpdateKnowledgeTemplate();

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: 'meeting' as KnowledgeTemplateCategory,
    content: '<p></p>', // Estado inicial HTML vazio para o editor
    isGlobal: true,
    difficulty_level: 'intermediate' as const,
    estimated_time: 30,
    tags: [] as string[]
  });

  const [showPreview, setShowPreview] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentTag, setCurrentTag] = useState('');
  
  const titleInputRef = useRef<HTMLInputElement>(null);
  const contentTextareaRef = useRef<HTMLTextAreaElement>(null);

  // Função helper para extrair texto do HTML
  const extractTextFromHTML = (html: string): string => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  // Carregar dados do template existente se estamos editando
  useEffect(() => {
    if (existingTemplate && !isLoadingTemplate && editingTemplateId) {
      logQueryEvent('KnowledgeCreateTemplate', 'Carregando template para edição', { templateId: existingTemplate.id });
      
      setFormData({
        name: existingTemplate.name,
        description: existingTemplate.description || '',
        category: existingTemplate.category,
        content: existingTemplate.content,
        isGlobal: existingTemplate.is_global,
        difficulty_level: (existingTemplate as any).difficulty_level || 'intermediate',
        estimated_time: (existingTemplate as any).estimated_time || 30,
        tags: (existingTemplate as any).tags || []
      });
    }
  }, [existingTemplate, isLoadingTemplate, editingTemplateId]);

  // Função para adicionar tag
  const handleAddTag = useCallback(() => {
    if (currentTag.trim() && !formData.tags.includes(currentTag.trim()) && formData.tags.length < 10) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, currentTag.trim()]
      }));
      setCurrentTag('');
    }
  }, [currentTag, formData.tags]);

  // Função para remover tag
  const handleRemoveTag = useCallback((tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  }, []);

  // Função para salvar/atualizar template
  const handleSave = async (publishNow = false) => {
    if (!formData.name.trim()) {
      errorWithNotification('Nome obrigatório', {
        description: 'Por favor, adicione um nome para o template.',
        persist: false
      });
      titleInputRef.current?.focus();
      return;
    }

    if (!formData.description.trim()) {
      errorWithNotification('Descrição obrigatória', {
        description: 'Por favor, adicione uma descrição para o template.',
        persist: false
      });
      return;
    }

    // Validar conteúdo HTML (extrair texto para verificar se não está vazio)
    const contentText = extractTextFromHTML(formData.content);
    if (!contentText.trim() || formData.content === '<p></p>') {
      errorWithNotification('Conteúdo obrigatório', {
        description: 'Por favor, adicione o conteúdo do template.',
        persist: false
      });
      return;
    }

    try {
      setIsLoading(true);
      logQueryEvent('KnowledgeCreateTemplate', 'Iniciando salvamento de template', {
        templateId: editingTemplateId,
        name: formData.name,
        category: formData.category,
        publishNow
      });

      let result;
      if (editingTemplateId) {
        const updateData: UpdateKnowledgeTemplateData = {
          name: formData.name,
          description: formData.description,
          category: formData.category,
          content: formData.content,
          is_global: formData.isGlobal,
          difficulty_level: formData.difficulty_level,
          estimated_time: formData.estimated_time,
          tags: formData.tags
        };
        result = await updateTemplateMutation.mutateAsync({
          id: editingTemplateId,
          data: updateData
        });
      } else {
        const createData: CreateKnowledgeTemplateData = {
          name: formData.name,
          description: formData.description,
          category: formData.category,
          content: formData.content,
          is_global: formData.isGlobal,
          difficulty_level: formData.difficulty_level,
          estimated_time: formData.estimated_time,
          tags: formData.tags
        };
        result = await createTemplateMutation.mutateAsync(createData);
      }

      const isFirstCreation = !editingTemplateId;
      const reward = isFirstCreation ? {
        xp: 100,     // Criação de template dá mais XP
        stardust: 60  // Criação de template dá mais Stardust
      } : { xp: 0, stardust: 0 };

      successWithNotification(
        `Template ${publishNow ? 'publicado' : 'salvo'} com sucesso!`,
        {
          description: isFirstCreation 
            ? `Criador de Templates! +${reward.xp} XP e +${reward.stardust} Stardust 🌟`
            : 'Template atualizado com sucesso!',
          persist: true,
          notificationType: 'knowledge_template_created',
          metadata: {
            template_id: result.id,
            template_name: result.name,
            category: result.category,
            action: publishNow ? 'publish' : 'save',
            is_first_creation: isFirstCreation,
            reward: reward
          }
        }
      );

      navigate('/knowledge?tab=templates');

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      logQueryEvent('KnowledgeCreateTemplate', 'Erro ao salvar template', { error: errorMessage }, 'error');
      
      errorWithNotification('Erro ao salvar template', {
        description: errorMessage || 'Não foi possível salvar o template. Tente novamente.',
        persist: true
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Atualizar conteúdo padrão quando categoria muda
  const handleCategoryChange = (newCategory: KnowledgeTemplateCategory) => {
    if (!editingTemplateId) {
      setFormData(prev => ({
        ...prev,
        category: newCategory,
        content: prev.content || getTemplateContent(newCategory)
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        category: newCategory
      }));
    }
  };

  const generateTemplateSlug = (name: string) => {
    return name
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  };

  const categoryColors: Record<KnowledgeTemplateCategory, string> = {
    meeting: 'bg-blue-500/10 text-blue-600 border-blue-500/20',
    project: 'bg-green-500/10 text-green-600 border-green-500/20',
    process: 'bg-purple-500/10 text-purple-600 border-purple-500/20',
    decision: 'bg-orange-500/10 text-orange-600 border-orange-500/20',
    runbook: 'bg-red-500/10 text-red-600 border-red-500/20',
    guide: 'bg-indigo-500/10 text-indigo-600 border-indigo-500/20',
    other: 'bg-gray-500/10 text-gray-600 border-gray-500/20'
  };

  const selectedCategory = templateCategories.find(cat => cat.value === formData.category);

  // Loading state para edição
  if (editingTemplateId && isLoadingTemplate) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
        <div className="container py-8 px-4 mx-auto">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            <motion.div variants={cardVariants}>
              <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-xl">
                <CardContent className="p-8">
                  <div className="flex items-center justify-center space-x-3">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <Loader2 className="h-8 w-8 text-green-600" />
                    </motion.div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Carregando template</h3>
                      <p className="text-sm text-gray-600">Preparando editor para edição...</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
        <div className="container py-4 px-4 mx-auto">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
          >
            {/* Header Hero Section */}
            <motion.div variants={heroVariants}>
              <Card className="border-0 overflow-hidden shadow-2xl bg-gradient-to-br from-emerald-600 via-green-600 to-emerald-700 text-white">
                <CardContent className="p-8 relative">
                  {/* Background Pattern */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
                  <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
                  <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
                  
                  <div className="relative z-10">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => navigate('/knowledge?tab=templates')}
                          className="text-white hover:bg-white/20 hover:text-white border-white/20"
                        >
                          <ArrowLeft className="h-4 w-4 mr-2" />
                          Voltar aos Templates
                        </Button>
                        
                        <div className="flex items-center gap-3">
                          <motion.div
                            variants={floatingVariants}
                            animate="animate"
                            className="p-3 bg-white/10 rounded-xl backdrop-blur-sm"
                          >
                            <Target className="h-8 w-8 text-emerald-200" />
                          </motion.div>
                          <div>
                            <h1 className="text-3xl font-bold">
                              {editingTemplateId ? 'Editar Template' : 'Criar Template'}
                            </h1>
                            <p className="text-white/90 text-lg">
                              {editingTemplateId 
                                ? 'Atualize e melhore seu template existente' 
                                : 'Crie templates reutilizáveis para acelerar a criação de conteúdo'
                              }
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-3">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setShowPreview(!showPreview)}
                              className="bg-white/10 hover:bg-white/20 text-white border-white/30"
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              {showPreview ? 'Editor' : 'Preview'}
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{showPreview ? 'Voltar ao editor' : 'Visualizar template'}</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSave(false)}
                              disabled={isLoading}
                              className="bg-white/10 hover:bg-white/20 text-white border-white/30"
                            >
                              {isLoading ? (
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              ) : (
                                <Save className="h-4 w-4 mr-2" />
                              )}
                              Salvar Rascunho
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Salvar template como rascunho</p>
                          </TooltipContent>
                        </Tooltip>

                        <Button
                          onClick={() => handleSave(true)}
                          disabled={isLoading}
                          className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white border-0 shadow-lg"
                        >
                          {isLoading ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <Send className="h-4 w-4 mr-2" />
                          )}
                          {editingTemplateId ? 'Atualizar Template' : 'Publicar Template'}
                        </Button>
                      </div>
                    </div>

                    {/* Breadcrumb */}
                    <div className="flex items-center gap-2 text-sm text-white/80">
                      <BookOpen className="h-4 w-4" />
                      <span>Knowledge Hub</span>
                      <ChevronRight className="h-3 w-3" />
                      <span>Templates</span>
                      <ChevronRight className="h-3 w-3" />
                      <span className="font-medium text-white">
                        {editingTemplateId ? 'Editar Template' : 'Novo Template'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
              {/* Editor Principal */}
              <motion.div className="xl:col-span-2 space-y-6">
                {/* Informações Básicas */}
                <motion.div variants={cardVariants}>
                  <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5 text-green-600" />
                        Informações do Template
                      </CardTitle>
                      <CardDescription>
                        Configure os detalhes básicos do seu template
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Nome */}
                      <div className="space-y-2">
                        <Label htmlFor="name">Nome do Template *</Label>
                        <Input
                          ref={titleInputRef}
                          id="name"
                          placeholder="Ex: Retrospectiva de Sprint, Plano de Projeto..."
                          value={formData.name}
                          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                          className="text-lg font-medium"
                        />
                        {formData.name && (
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Hash className="h-3 w-3" />
                            Slug: {generateTemplateSlug(formData.name)}
                          </div>
                        )}
                      </div>

                      {/* Descrição */}
                      <div className="space-y-2">
                        <Label htmlFor="description">Descrição *</Label>
                        <Textarea
                          id="description"
                          placeholder="Descreva quando e como usar este template..."
                          value={formData.description}
                          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                          rows={3}
                          className="resize-none"
                        />
                      </div>

                      {/* Grid para campos menores */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Categoria */}
                        <div className="space-y-2">
                          <Label htmlFor="category">Categoria *</Label>
                          <Select
                            value={formData.category}
                            onValueChange={handleCategoryChange}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {templateCategories.map((category) => (
                                <SelectItem key={category.value} value={category.value}>
                                  <div>
                                    <div className="font-medium">{category.label}</div>
                                    <div className="text-xs text-muted-foreground">
                                      {category.description}
                                    </div>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Dificuldade */}
                        <div className="space-y-2">
                          <Label htmlFor="difficulty">Nível de Dificuldade</Label>
                          <Select
                            value={formData.difficulty_level}
                            onValueChange={(value: any) => setFormData(prev => ({ 
                              ...prev, 
                              difficulty_level: value 
                            }))}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {difficultyLevels.map((level) => (
                                <SelectItem key={level.value} value={level.value}>
                                  <div className="flex items-center gap-2">
                                    <Badge className={level.color}>
                                      {level.label}
                                    </Badge>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {/* Tempo estimado e Tags */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Tempo Estimado */}
                        <div className="space-y-2">
                          <Label htmlFor="estimated_time">Tempo Estimado (minutos)</Label>
                          <Input
                            id="estimated_time"
                            type="number"
                            min="5"
                            max="300"
                            step="5"
                            value={formData.estimated_time}
                            onChange={(e) => setFormData(prev => ({ 
                              ...prev, 
                              estimated_time: parseInt(e.target.value) || 30 
                            }))}
                            placeholder="30"
                          />
                        </div>

                        {/* Tags */}
                        <div className="space-y-2">
                          <Label htmlFor="tags">Tags</Label>
                          <div className="flex gap-2">
                            <Input
                              id="tags"
                              value={currentTag}
                              onChange={(e) => setCurrentTag(e.target.value)}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  e.preventDefault();
                                  handleAddTag();
                                }
                              }}
                              placeholder="Digite uma tag..."
                              className="flex-1"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={handleAddTag}
                              disabled={!currentTag.trim() || formData.tags.length >= 10}
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                          {formData.tags.length > 0 && (
                            <div className="flex flex-wrap gap-2 mt-2">
                              {formData.tags.map((tag, index) => (
                                <Badge
                                  key={index}
                                  variant="outline"
                                  className="flex items-center gap-1"
                                >
                                  <Tag className="h-3 w-3" />
                                  {tag}
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                                    onClick={() => handleRemoveTag(tag)}
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Editor de Conteúdo */}
                <motion.div variants={cardVariants}>
                  <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="flex items-center gap-2">
                            <Edit3 className="h-5 w-5 text-green-600" />
                            Conteúdo do Template
                          </CardTitle>
                          <CardDescription>
                            {showPreview ? 'Visualização do template' : 'Use Markdown para formatação'}
                          </CardDescription>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setShowPreview(!showPreview)}
                          >
                            {showPreview ? (
                              <>
                                <Edit3 className="h-4 w-4 mr-2" />
                                Editor
                              </>
                            ) : (
                              <>
                                <Eye className="h-4 w-4 mr-2" />
                                Preview
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {!showPreview ? (
                        <>
                          {/* Área de Edição com PostEditor */}
                          <PostEditor
                            content={formData.content}
                            onChange={(content) => setFormData(prev => ({ ...prev, content }))}
                            placeholder="Use o editor rico para criar o conteúdo do seu template. Você pode usar formatação, inserir imagens e gerar conteúdo com IA..."
                            tempPostId={`template-${Date.now()}`}
                          />

                          <div className="flex items-center justify-between mt-4 p-4 bg-muted/20 rounded-lg">
                            <div className="text-sm text-muted-foreground">
                              💡 Use <code className="bg-background px-1 rounded">{"{{variável}}"}</code> para campos que os usuários devem preencher
                            </div>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => {
                                const templateContent = getTemplateContent(formData.category);
                                // Converter markdown para HTML básico para o editor
                                const htmlContent = templateContent.replace(/\n/g, '<br>').replace(/\n\n/g, '</p><p>');
                                setFormData(prev => ({ 
                                  ...prev, 
                                  content: `<p>${htmlContent}</p>`
                                }));
                              }}
                            >
                              <Wand2 className="h-4 w-4 mr-2" />
                              Carregar Exemplo
                            </Button>
                          </div>
                        </>
                      ) : (
                        <div className="border rounded-lg p-6 bg-muted/20 min-h-[500px]">
                          <div className="prose prose-slate max-w-none">
                            {formData.content && formData.content !== '<p></p>' ? (
                              <div dangerouslySetInnerHTML={{ __html: formData.content }} />
                            ) : (
                              <div className="text-muted-foreground italic text-center py-20">
                                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                <p>Adicione conteúdo no editor para ver o preview</p>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              </motion.div>

              {/* Sidebar */}
              <motion.div className="xl:col-span-1 space-y-6">
                {/* Status e Configurações */}
                <motion.div variants={cardVariants}>
                  <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Settings className="h-5 w-5 text-green-600" />
                        Configurações
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Categoria:</span>
                        <Badge className={categoryColors[formData.category]}>
                          {selectedCategory?.label}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Dificuldade:</span>
                        <Badge className={difficultyLevels.find(d => d.value === formData.difficulty_level)?.color}>
                          {difficultyLevels.find(d => d.value === formData.difficulty_level)?.label}
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Tempo:</span>
                        <div className="flex items-center gap-1 text-sm">
                          <Clock className="h-4 w-4" />
                          {formData.estimated_time} min
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Visibilidade:</span>
                        <div className="flex items-center gap-1 text-sm">
                          {formData.isGlobal ? (
                            <>
                              <Globe className="h-4 w-4 text-blue-600" />
                              <span className="text-blue-600">Global</span>
                            </>
                          ) : (
                            <>
                              <Users className="h-4 w-4 text-green-600" />
                              <span className="text-green-600">Personalizado</span>
                            </>
                          )}
                        </div>
                      </div>

                      <Separator />

                      {/* Toggle Visibilidade */}
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="text-sm font-medium">Template Global</Label>
                            <p className="text-xs text-muted-foreground">
                              Disponível para toda empresa
                            </p>
                          </div>
                          <Switch 
                            checked={formData.isGlobal}
                            onCheckedChange={(checked) => setFormData(prev => ({ 
                              ...prev, 
                              isGlobal: checked 
                            }))}
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Dicas */}
                <motion.div variants={cardVariants}>
                  <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Lightbulb className="h-5 w-5 text-yellow-500" />
                        Dicas e Boas Práticas
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4 text-sm">
                      <div className="space-y-2">
                        <div className="font-medium flex items-center gap-2">
                          <Sparkles className="h-4 w-4 text-purple-500" />
                          Variáveis dinâmicas
                        </div>
                        <div className="text-muted-foreground">
                          Use <code className="bg-muted px-1 rounded text-xs">{"{{nome_variavel}}"}</code> para campos que os usuários devem preencher
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <div className="space-y-2">
                        <div className="font-medium flex items-center gap-2">
                          <Target className="h-4 w-4 text-green-500" />
                          Estrutura recomendada
                        </div>
                        <ul className="text-muted-foreground space-y-1 list-disc list-inside">
                          <li>Título claro e descritivo</li>
                          <li>Seções bem organizadas</li>
                          <li>Instruções de preenchimento</li>
                          <li>Campos obrigatórios marcados</li>
                          <li>Exemplos quando necessário</li>
                        </ul>
                      </div>

                      <Separator />

                      <div className="space-y-2">
                        <div className="font-medium flex items-center gap-2">
                          <Rocket className="h-4 w-4 text-blue-500" />
                          Benefícios esperados
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div className="text-center p-2 bg-green-50 rounded">
                            <div className="font-semibold text-green-700">~15min</div>
                            <div className="text-green-600">Economia de tempo</div>
                          </div>
                          <div className="text-center p-2 bg-blue-50 rounded">
                            <div className="font-semibold text-blue-700">85%</div>
                            <div className="text-blue-600">Consistência</div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Progress */}
                <motion.div variants={cardVariants}>
                  <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-lg">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Activity className="h-5 w-5 text-green-600" />
                        Progresso
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span>Completude</span>
                          <span className="font-medium">
                            {Math.round(
                              ((formData.name ? 1 : 0) +
                               (formData.description ? 1 : 0) +
                               (formData.content && formData.content !== '<p></p>' ? 1 : 0)) / 3 * 100
                            )}%
                          </span>
                        </div>
                        <Progress 
                          value={
                            ((formData.name ? 1 : 0) +
                             (formData.description ? 1 : 0) +
                             (formData.content && formData.content !== '<p></p>' ? 1 : 0)) / 3 * 100
                          }
                          className="h-2"
                        />
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          {formData.name ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-amber-500" />
                          )}
                          <span>Nome do template</span>
                        </div>
                        <div className="flex items-center gap-2">
                          {formData.description ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-amber-500" />
                          )}
                          <span>Descrição</span>
                        </div>
                        <div className="flex items-center gap-2">
                          {formData.content && formData.content !== '<p></p>' ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-amber-500" />
                          )}
                          <span>Conteúdo</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </TooltipProvider>
  );
}

// Função para gerar conteúdo padrão por categoria
function getTemplateContent(category: KnowledgeTemplateCategory): string {
  const templates: Record<KnowledgeTemplateCategory, string> = {
    meeting: `# {{Título da Reunião}}

**Data:** {{Data}}
**Participantes:** {{Lista de participantes}}
**Facilitador:** {{Nome do facilitador}}

## Agenda
1. {{Item da agenda 1}}
2. {{Item da agenda 2}}
3. {{Item da agenda 3}}

## Discussões
### {{Tópico 1}}
- {{Ponto discutido}}
- {{Decisão tomada}}

### {{Tópico 2}}
- {{Ponto discutido}}
- {{Decisão tomada}}

## Ações Definidas
| Ação | Responsável | Prazo | Status |
|------|-------------|-------|--------|
| {{Ação 1}} | {{Responsável}} | {{Data}} | Pendente |
| {{Ação 2}} | {{Responsável}} | {{Data}} | Pendente |

## Próximas Reuniões
- **Próxima reunião:** {{Data e horário}}
- **Pauta prévia:** {{Itens para próxima reunião}}`,

    project: `# {{Nome do Projeto}}

## Visão Geral
{{Descrição breve do projeto e seus objetivos principais}}

## Objetivos
- {{Objetivo específico 1}}
- {{Objetivo específico 2}}
- {{Objetivo específico 3}}

## Escopo
### Incluído no Escopo
- {{Item incluído 1}}
- {{Item incluído 2}}

### Fora do Escopo
- {{Item excluído 1}}
- {{Item excluído 2}}

## Cronograma
| Fase | Início | Fim | Responsável |
|------|--------|-----|-------------|
| {{Fase 1}} | {{Data}} | {{Data}} | {{Nome}} |
| {{Fase 2}} | {{Data}} | {{Data}} | {{Nome}} |

## Recursos Necessários
- **Equipe:** {{Descrição da equipe}}
- **Orçamento:** {{Valor estimado}}
- **Ferramentas:** {{Lista de ferramentas}}

## Riscos e Mitigações
| Risco | Probabilidade | Impacto | Mitigação |
|-------|---------------|---------|-----------|
| {{Risco 1}} | {{Alta/Média/Baixa}} | {{Alto/Médio/Baixo}} | {{Estratégia}} |

## Critérios de Sucesso
- {{Critério 1}}
- {{Critério 2}}
- {{Critério 3}}`,

    process: `# {{Nome do Processo}}

## Objetivo
{{Descrição do objetivo do processo}}

## Quando Usar
{{Situações em que este processo deve ser aplicado}}

## Pré-requisitos
- {{Pré-requisito 1}}
- {{Pré-requisito 2}}

## Etapas do Processo

### Etapa 1: {{Nome da Etapa}}
**Responsável:** {{Função/Pessoa}}
**Tempo estimado:** {{Duração}}

**Ações:**
1. {{Ação específica 1}}
2. {{Ação específica 2}}

**Entregáveis:**
- {{Entregável 1}}

### Etapa 2: {{Nome da Etapa}}
**Responsável:** {{Função/Pessoa}}
**Tempo estimado:** {{Duração}}

**Ações:**
1. {{Ação específica 1}}
2. {{Ação específica 2}}

**Entregáveis:**
- {{Entregável 1}}

## Controles de Qualidade
- {{Checkpoint 1}}
- {{Checkpoint 2}}

## Métricas de Sucesso
- {{Métrica 1}}: {{Valor esperado}}
- {{Métrica 2}}: {{Valor esperado}}

## Documentos Relacionados
- [{{Nome do documento}}]({{link}})
- [{{Nome do documento}}]({{link}})`,

    decision: `# {{Título da Decisão}}

**Data da Decisão:** {{Data}}
**Status:** {{Proposta/Aprovada/Rejeitada}}
**Responsável pela Decisão:** {{Nome}}

## Contexto
{{Descreva o contexto que levou à necessidade desta decisão}}

## Problema
{{Descrição clara do problema a ser resolvido}}

## Opções Consideradas
### Opção 1: {{Nome da Opção}}
**Prós:**
- {{Vantagem 1}}
- {{Vantagem 2}}

**Contras:**
- {{Desvantagem 1}}
- {{Desvantagem 2}}

**Custos:** {{Estimativa de custos}}
**Riscos:** {{Principais riscos}}

### Opção 2: {{Nome da Opção}}
**Prós:**
- {{Vantagem 1}}
- {{Vantagem 2}}

**Contras:**
- {{Desvantagem 1}}
- {{Desvantagem 2}}

**Custos:** {{Estimativa de custos}}
**Riscos:** {{Principais riscos}}

## Decisão Tomada
{{Descrição da opção escolhida e justificativa}}

## Impactos
- **Técnicos:** {{Impactos técnicos}}
- **Negócio:** {{Impactos no negócio}}
- **Equipe:** {{Impactos na equipe}}

## Plano de Implementação
1. {{Passo 1}}
2. {{Passo 2}}
3. {{Passo 3}}

## Critérios de Avaliação
{{Como será medido o sucesso desta decisão}}`,

    runbook: `# {{Nome do Runbook}}

## Visão Geral
{{Descrição breve do sistema ou processo coberto por este runbook}}

## Informações do Sistema
- **Ambiente:** {{Produção/Teste/Desenvolvimento}}
- **Responsável:** {{Equipe/Pessoa}}
- **Horário de Suporte:** {{Horário}}
- **Criticidade:** {{Alta/Média/Baixa}}

## Procedimentos Operacionais

### Inicialização do Sistema
1. {{Passo 1}}
2. {{Passo 2}}
3. {{Passo 3}}

### Parada do Sistema
1. {{Passo 1}}
2. {{Passo 2}}
3. {{Passo 3}}

### Backup
**Frequência:** {{Diária/Semanal}}
**Procedimento:**
1. {{Passo 1}}
2. {{Passo 2}}

## Troubleshooting

### Problema: {{Nome do Problema}}
**Sintomas:**
- {{Sintoma 1}}
- {{Sintoma 2}}

**Diagnóstico:**
1. {{Verificação 1}}
2. {{Verificação 2}}

**Solução:**
1. {{Passo da solução 1}}
2. {{Passo da solução 2}}

### Problema: {{Nome do Problema}}
**Sintomas:**
- {{Sintoma 1}}
- {{Sintoma 2}}

**Diagnóstico:**
1. {{Verificação 1}}
2. {{Verificação 2}}

**Solução:**
1. {{Passo da solução 1}}
2. {{Passo da solução 2}}

## Contatos de Emergência
- **Responsável Técnico:** {{Nome e contato}}
- **Gerente:** {{Nome e contato}}
- **Fornecedor:** {{Nome e contato}}

## Logs e Monitoramento
- **Localização dos Logs:** {{Caminho}}
- **Dashboard:** {{Link do dashboard}}
- **Alertas:** {{Configuração de alertas}}`,

    guide: `# {{Título do Guia}}

## Introdução
{{Breve introdução sobre o que este guia ensina}}

## Pré-requisitos
- {{Conhecimento/ferramenta necessária 1}}
- {{Conhecimento/ferramenta necessária 2}}

## Tempo Estimado
{{Tempo necessário para completar este guia}}

## Passo a Passo

### Passo 1: {{Título do Passo}}
{{Explicação detalhada do que fazer}}

1. {{Ação específica 1}}
2. {{Ação específica 2}}

**Dica:** {{Dica útil para este passo}}

**Resultado esperado:** {{O que deve acontecer}}

### Passo 2: {{Título do Passo}}
{{Explicação detalhada do que fazer}}

1. {{Ação específica 1}}
2. {{Ação específica 2}}

**Dica:** {{Dica útil para este passo}}

**Resultado esperado:** {{O que deve acontecer}}

### Passo 3: {{Título do Passo}}
{{Explicação detalhada do que fazer}}

1. {{Ação específica 1}}
2. {{Ação específica 2}}

**Dica:** {{Dica útil para este passo}}

**Resultado esperado:** {{O que deve acontecer}}

## Verificação
{{Como verificar se tudo foi feito corretamente}}

- [ ] {{Checklist item 1}}
- [ ] {{Checklist item 2}}
- [ ] {{Checklist item 3}}

## Solução de Problemas
### {{Problema comum 1}}
**Sintoma:** {{Como identificar o problema}}
**Solução:** {{Como resolver}}

### {{Problema comum 2}}
**Sintoma:** {{Como identificar o problema}}
**Solução:** {{Como resolver}}

## Próximos Passos
- {{Sugestão de próximo passo 1}}
- {{Sugestão de próximo passo 2}}

## Recursos Adicionais
- [{{Nome do recurso}}]({{link}})
- [{{Nome do recurso}}]({{link}})`,

    other: `# {{Título do Template}}

## Seção 1
{{Conteúdo da seção 1}}

## Seção 2
{{Conteúdo da seção 2}}

## Conclusão
{{Conclusão ou próximos passos}}`
  };

  return templates[category] || templates.other;
}