import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { errorWithNotification, successWithNotification } from "@/lib/notifications/toastWithNotification";
import { supabase } from "@/integrations/supabase/client";
import { useQueryClient } from "@tanstack/react-query";
import { Logo } from "@/components/ui/logo";
import { Turnstile } from "@marsidev/react-turnstile";
import { Rocket, Building } from "lucide-react";
import { PasswordInput } from "@/components/ui/PasswordInput";
import { ParticleBackground } from "@/components/ui/ParticleBackground";
import { useCosmosStyles } from "@/styles/cosmos-theme";
import { authManager } from "@/lib/auth/AuthManager";

// Ambiente de desenvolvimento desativa o Turnstile
const IS_DEV_ENV =
  process.env.NODE_ENV === "development" ||
  window.location.hostname === "localhost";
const DISABLE_TURNSTILE = IS_DEV_ENV;

export default function RegisterDirect() {
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();

  // Estados para o formulário
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [turnstileToken, setTurnstileToken] = useState<string | null>(
    DISABLE_TURNSTILE ? "dev-mode-token" : null
  );

  // Aplicar estilos do tema Cosmos
  const { applyStyles } = useCosmosStyles();
  useEffect(applyStyles, [applyStyles]);

  // Pré-preencher email se veio da landing page
  useEffect(() => {
    const state = location.state as { email?: string; plan?: string; trial?: boolean } | null;
    if (state?.email) {
      setEmail(state.email);
    }
  }, [location.state]);

  const handleRegisterDirect = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!DISABLE_TURNSTILE && !turnstileToken) {
      errorWithNotification(
        "Verificação necessária",
        "Por favor, complete a verificação de segurança."
      );
      return;
    }

    setIsLoading(true);

    try {
      // 1. Criar o usuário primeiro
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });

      if (authError) {
        console.error("Erro ao criar usuário:", authError);
        throw authError;
      }

      if (!authData.user) {
        throw new Error("Erro ao criar usuário");
      }

      // 2. Chamar a função SQL para criar a empresa e o perfil do usuário
      const { data: companyData, error: companyError } = await supabase.rpc(
        "register_user_company_direct",
        {
          p_email: email,
          p_password: password,
          p_full_name: fullName,
          p_company_name: companyName,
          p_user_id: authData.user.id,
        }
      );

      if (companyError) {
        console.error("Erro ao criar empresa e perfil:", companyError);
        throw companyError;
      }

      // Verificar se a operação foi bem-sucedida
      if (!companyData.success) {
        console.error("Erro ao criar empresa e perfil:", companyData.error);
        throw new Error(companyData.error || "Erro ao criar empresa e perfil");
      }

      // 5. Armazenar o email para facilitar o login posterior
      sessionStorage.setItem("registeredEmail", email);

      // 6. Armazenar um flag indicando que a configuração da empresa foi concluída com sucesso
      sessionStorage.setItem("companySetupCompleted", "true");

      // 7. Definir company_id no AuthManager para evitar race condition
      authManager.setCompanyId(companyData.company_id);

      // 8. Invalidar cache do perfil para garantir que o AuthManager carregue os dados atualizados
      await queryClient.invalidateQueries({ queryKey: ['current-user'] });
      await queryClient.invalidateQueries({ queryKey: ['profiles'] });

      successWithNotification("Cadastro realizado com sucesso!", {
        description: "Bem-vindo ao Vindula Cosmos! Você será redirecionado para o sistema.",
      });

      // 9. Usar o company_id retornado pela função e redirecionar imediatamente
      navigate("/loading", { 
        state: { 
          destination: "/feed",
          forceCompanyId: companyData.company_id // Passar o company_id conhecido como backup
        } 
      });
    } catch (error: unknown) {
      console.error("Erro no registro direto:", error);
      let errorMessage = "Ocorreu um erro inesperado";

      if (error instanceof Error) {
        errorMessage = error.message;
        // Tratar mensagens de erro específicas
        if (error.message.includes("password")) {
          errorMessage = "A senha deve ter pelo menos 6 caracteres";
        } else if (error.message.includes("email")) {
          errorMessage = "Por favor, forneça um email válido";
        } else if (error.message.includes("já cadastrado")) {
          errorMessage = "Este email já está registrado no sistema";
        }
      }

      errorWithNotification("Erro ao criar conta", {
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center cosmos-bg">
      {/* Componente de partículas interativas */}
      <ParticleBackground
        density={50}
        colors={["#E8A95B", "#C85C2D", "#F3C892", "#B14E1F", "#FFD700"]}
        interactionRadius={180}
        connectionDistance={160}
        speedMultiplier={1}
      />

      <div className="meteor"></div>
      <div className="meteor"></div>
      <div className="meteor"></div>
      <div className="meteor"></div>

      <div className="relative w-full max-w-md z-10 px-4">
        <div className="floating flex flex-col items-center mb-8">
          <div className="mb-4">
            <Logo />
          </div>
          <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#E8A95B] to-[#C85C2D] text-center">
            Cadastro Completo
          </h1>
          <p className="mt-2 text-lg text-gray-400 text-center">
            Crie sua conta e empresa em um único passo
          </p>
        </div>

        <div className="glow-border backdrop-blur-sm">
          <form
            onSubmit={handleRegisterDirect}
            className="bg-[#121520] p-8 rounded-lg space-y-6 shadow-xl"
          >
            <div className="space-y-4">
              {/* Dados do Usuário */}
              <div className="space-y-2">
                <Label
                  htmlFor="full-name"
                  className="text-gray-300 text-sm font-medium"
                >
                  Nome Completo
                </Label>
                <div className="relative">
                  <Input
                    id="full-name"
                    placeholder="Seu Nome"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    required
                    autoFocus
                    className="bg-[#1a1e2a] border-[#2a2e3a] text-white h-11 pl-4 input-fade input-glow"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="email"
                  className="text-gray-300 text-sm font-medium"
                >
                  Email
                </Label>
                <div className="relative">
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="bg-[#1a1e2a] border-[#2a2e3a] text-white h-11 pl-4 input-fade input-glow"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="password"
                  className="text-gray-300 text-sm font-medium"
                >
                  Senha
                </Label>
                <PasswordInput
                  id="password"
                  placeholder="Sua senha (mínimo 6 caracteres)"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  minLength={6}
                  className="bg-[#1a1e2a] border-[#2a2e3a] text-white h-11 pl-4 input-fade input-glow"
                />
              </div>

              {/* Dados da Empresa */}
              <div className="space-y-2 pt-4 border-t border-[#2a2e3a]">
                <Label
                  htmlFor="company-name"
                  className="text-gray-300 text-sm font-medium"
                >
                  Nome da Empresa
                </Label>
                <div className="relative">
                  <Input
                    id="company-name"
                    placeholder="Nome da sua empresa"
                    value={companyName}
                    onChange={(e) => setCompanyName(e.target.value)}
                    required
                    className="bg-[#1a1e2a] border-[#2a2e3a] text-white h-11 pl-10 input-fade input-glow"
                  />
                  <Building
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                    size={18}
                  />
                </div>
                <p className="text-xs text-gray-500">
                  Este será o nome visível para seus colaboradores
                </p>
              </div>
            </div>

            <div className="flex justify-center py-2">
              {DISABLE_TURNSTILE ? (
                <div className="py-3 px-4 text-xs text-gray-400 bg-[#1a1e2a] rounded border border-[#2a2e3a]">
                  [DEV] Verificação de segurança desativada em ambiente de
                  desenvolvimento
                </div>
              ) : (
                <Turnstile
                  siteKey="1x00000000000000000000AA"
                  onSuccess={(token) => setTurnstileToken(token)}
                  onError={() => setTurnstileToken(null)}
                />
              )}
            </div>

            <Button
              type="submit"
              className="w-full h-12 bg-gradient-to-r from-[#E8A95B] to-[#C85C2D] hover:from-[#C85C2D] hover:to-[#E8A95B] text-white font-medium transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-[#E8A95B]/20"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  <span>Processando...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <span>Criar Conta e Empresa</span>
                  <Rocket className="ml-2" size={18} />
                </div>
              )}
            </Button>

            <div className="pt-2 text-center text-sm text-gray-400">
              <p>
                Já tem uma conta?{" "}
                <button
                  type="button"
                  onClick={() => navigate("/auth")}
                  className="text-[#E8A95B] hover:text-[#C85C2D] font-medium transition-colors duration-200"
                >
                  Faça login
                </button>
              </p>
            </div>
          </form>
        </div>

        <div className="mt-6 text-center text-xs text-gray-500">
          <p>
            Ao criar uma conta, você concorda com nossos{" "}
            <a
              href="/terms"
              className="text-[#E8A95B] hover:text-[#C85C2D] transition-colors duration-200"
            >
              Termos de Serviço
            </a>{" "}
            e{" "}
            <a
              href="/privacy"
              className="text-[#E8A95B] hover:text-[#C85C2D] transition-colors duration-200"
            >
              Política de Privacidade
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
