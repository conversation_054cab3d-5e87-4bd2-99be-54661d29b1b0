import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { useCurrentUser } from '@/hooks/use-current-user';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { X, UserPlus, ArrowLeft, Search } from 'lucide-react'; 
import { useToast } from '@/hooks/use-toast';
import { logQueryEvent } from '@/lib/logs/showQueryLogs';

interface Member {
  id: string;
  full_name: string;
  avatar_url: string;
  role: string;
}

interface UserSearchResult {
  id: string;
  full_name: string;
  avatar_url: string;
}

export function ChannelMembersPage() {
  const { channelId } = useParams<{ channelId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const currentUserId = useCurrentUser();

  const [members, setMembers] = useState<Member[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false); 
  const [isLoadingMembers, setIsLoadingMembers] = useState(false); 
  const [channelName, setChannelName] = useState("");
  const [isTeamChannel, setIsTeamChannel] = useState(false);
  const [teamName, setTeamName] = useState("");
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false); 

  const fetchChannelInfo = useCallback(async () => {
    if (!channelId) return;
    try {
      const { data, error } = await supabase
        .from("channels")
        .select(`
          name,
          team_id,
          teams:team_id(name)
        `)
        .eq("id", channelId)
        .single();
        
      if (error) throw error;
      
      if (data) {
        setChannelName(data.name);
        setIsTeamChannel(!!data.team_id);
        setTeamName(data.teams?.name || "");
      }
    } catch (error) {
      logQueryEvent('ChannelMembersPage', 'Erro ao buscar informações do canal', error, 'error');
      toast({ title: 'Erro', description: 'Não foi possível carregar informações do canal.', variant: 'destructive' });
    }
  }, [channelId, toast]);

  const fetchMembers = useCallback(async () => {
    if (!channelId) return;
    setIsLoadingMembers(true);
    try {
      const { data, error } = await supabase
        .from("channel_members")
        .select(`
          user_id,
          role,
          profiles:user_id(
            id,
            full_name,
            avatar_url
          )
        `)
        .eq("channel_id", channelId);

      if (error) {
        logQueryEvent('ChannelMembersPage', 'Error fetching members', error, 'error');
        toast({ title: 'Erro', description: 'Não foi possível carregar os membros do canal.', variant: 'destructive' });
        setMembers([]);
        return;
      }

      const formattedMembers = data.map((member) => ({
        id: member.profiles.id,
        full_name: member.profiles.full_name,
        avatar_url: member.profiles.avatar_url,
        role: member.role,
      }));
      setMembers(formattedMembers);
    } catch (e) {
      logQueryEvent('ChannelMembersPage', 'Exception fetching members', e, 'error');
      toast({ title: 'Erro', description: 'Ocorreu uma exceção ao buscar membros.', variant: 'destructive' });
      setMembers([]);
    } finally {
      setIsLoadingMembers(false);
    }
  }, [channelId, toast]);

  useEffect(() => {
    if (channelId) {
      fetchChannelInfo();
      fetchMembers();
    }
  }, [channelId, fetchChannelInfo, fetchMembers]);

  const searchUsers = useCallback(async (term: string) => {
    if (!term.trim() || !channelId) {
      setSearchResults([]);
      return;
    }
    setIsSearching(true);
    try {
      const { data: usersFound, error: searchError } = await supabase
        .from("profiles")
        .select("id, full_name, avatar_url")
        .ilike("full_name", `%${term}%`)
        .limit(5); 

      if (searchError) throw searchError;

      if (usersFound) {
        const memberIds = members.map(m => m.id);
        const filteredResults = usersFound.filter(u => !memberIds.includes(u.id) && u.id !== currentUserId);
        setSearchResults(filteredResults as UserSearchResult[]);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      logQueryEvent('ChannelMembersPage', 'Erro ao buscar usuários', error, 'error');
      toast({ title: 'Erro na busca', description: 'Não foi possível buscar usuários.', variant: 'destructive' });
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [channelId, members, toast, currentUserId]);

  useEffect(() => {
    const handler = setTimeout(() => {
      if (searchTerm.trim()) {
        searchUsers(searchTerm);
      } else {
        setSearchResults([]);
      }
    }, 300); 

    return () => {
      clearTimeout(handler);
    };
  }, [searchTerm, searchUsers]);


  const executeAddMember = async (userId: string, userName: string) => {
    if (!channelId) return;

    setIsLoading(true);
    try {
      // Verificar se já é membro (segurança adicional, embora searchUsers já filtre)
      const { data: existingMember, error: checkError } = await supabase
        .from("channel_members")
        .select("user_id") // Selecionar apenas um campo para otimizar
        .eq("channel_id", channelId)
        .eq("user_id", userId)
        .maybeSingle(); // Alterado para maybeSingle()

      if (checkError && checkError.code !== 'PGRST116') { // Ignorar erro PGRST116 (0 rows) pois é esperado
        throw checkError; // Lançar outros erros de verificação
      }

      if (existingMember) {
        // Este toast pode ser útil para o usuário, mas a lógica já impede a duplicação
        // toast({ title: "Aviso", description: "Este usuário já é membro do canal.", variant: "default" });
        // setIsLoading(false); // Parar o loading pois não há mais nada a fazer
        // return; // Sair da função
        // Na verdade, searchUsers já deve filtrar, então este caso é mais um fallback.
        // Se chegou aqui, pode ser uma condição de corrida. Deixar adicionar por enquanto,
        // o BD terá um unique constraint que impedirá a duplicação efetiva.
        // Ou, melhor ainda, confiar que searchUsers filtrou e remover essa checagem.
        // Por ora, manter a lógica original de lançar erro se existingMember for encontrado.
        throw new Error("Usuário já é membro do canal");
      }

      const { error: addError } = await supabase
        .from("channel_members")
        .insert({
          channel_id: channelId,
          user_id: userId,
          role: "member",
        });

      if (addError) throw addError;
      
      await supabase.from('chat_messages').insert({
        channel_id: channelId,
        sender_id: currentUserId, 
        content: `${userName} entrou no canal.`,
        message_type: 'system_join', // ✅ CORREÇÃO: Adicionar message_type para filtro v3
        metadata: {
          user_id: userId,
          added_by: currentUserId
        }
      });

      toast({
        title: "Membro adicionado",
        description: `${userName} foi adicionado ao canal com sucesso.`,
      });

      setSearchTerm(""); 
      setSearchResults([]); 
      fetchMembers(); 
    } catch (error: any) {
      logQueryEvent('ChannelMembersPage', 'Erro ao adicionar membro', error, 'error');
      toast({
        title: "Erro ao adicionar membro",
        description: error.message || "Ocorreu um erro ao adicionar o membro.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (!channelId) return;
    setIsLoading(true);
    try {
      const { data: userProfile } = await supabase
        .from("profiles")
        .select("full_name")
        .eq("id", memberId)
        .single();

      const { error } = await supabase
        .from("channel_members")
        .delete()
        .eq("channel_id", channelId)
        .eq("user_id", memberId);

      if (error) throw error;
      
      if (userProfile) {
        const { error: systemMessageError } = await supabase.from('chat_messages').insert({
          channel_id: channelId,
          sender_id: currentUserId, 
          content: `${userProfile.full_name} saiu do canal.`,
          message_type: 'system_leave', // ✅ CORREÇÃO: Adicionar message_type para filtro v3
          metadata: { 
            user_id: memberId,
            removed_by: currentUserId
          }
        });

        if (systemMessageError) {
          logQueryEvent('ChannelMembersPage:handleRemoveMember', 'Erro ao inserir mensagem de sistema (system_leave)', systemMessageError, 'error');
          // Não lançar erro aqui para não impedir a remoção do membro, apenas logar.
        }
      }

      toast({
        title: "Membro removido",
        description: "O usuário foi removido do canal com sucesso.",
      });

      fetchMembers();
    } catch (error: any) {
      logQueryEvent('ChannelMembersPage', 'Erro ao remover membro', error, 'error');
      toast({
        title: "Erro ao remover membro",
        description: error.message || "Ocorreu um erro ao remover o membro.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 md:p-6">
      <Button variant="outline" size="sm" onClick={() => navigate(-1)} className="mb-4">
        <ArrowLeft className="mr-2 h-4 w-4" />
        Voltar
      </Button>
      <header className="mb-6">
        <h1 className="text-2xl font-bold">Gerenciar Membros do Canal</h1>
        {channelName && <p className="text-muted-foreground">{channelName}</p>}
      </header>

      {isTeamChannel && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="text-blue-800 font-medium text-lg mb-2">
            🔗 Canal de Equipe: {teamName}
          </h3>
          <p className="text-blue-700">
            Este é um canal vinculado a uma equipe. Os membros deste canal são gerenciados automaticamente através da equipe correspondente. 
          </p>
          <p className="text-blue-600 text-sm mt-2">
            Para adicionar ou remover membros, vá até a página da equipe "{teamName}" e gerencie os membros por lá.
          </p>
        </div>
      )}

      <div className="space-y-6">
        <div className="bg-card p-4 sm:p-6 rounded-lg border">
          <h2 className="text-lg font-semibold mb-3">Adicionar Novo Membro</h2>
          {isTeamChannel ? (
            <div className="text-center py-8 text-muted-foreground">
              <UserPlus className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p className="text-lg font-medium mb-2">Adição automática via equipe</p>
              <p className="text-sm">
                Novos membros são adicionados automaticamente quando são incluídos na equipe "{teamName}".
              </p>
            </div>
          ) : (
            <div className="relative">
              <Label htmlFor="search-member" className="sr-only">Nome do usuário</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search-member"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Digite o nome do usuário para buscar"
                  className="pl-10"
                  disabled={isLoading} 
                />
              </div>
              {(isSearching || searchResults.length > 0 || (searchTerm.trim() && !isSearching && searchResults.length === 0)) && (
                <div className="absolute z-10 w-full mt-1 bg-card border rounded-md shadow-lg max-h-60 overflow-y-auto">
                  {isSearching && <p className="p-3 text-sm text-muted-foreground">Buscando...</p>}
                  {!isSearching && searchResults.length === 0 && searchTerm.trim() && (
                    <p className="p-3 text-sm text-muted-foreground">Nenhum usuário encontrado.</p>
                  )}
                  {!isSearching && searchResults.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center gap-3 p-3 hover:bg-muted cursor-pointer border-b last:border-b-0"
                      onClick={() => !isLoading && executeAddMember(user.id, user.full_name)}
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.avatar_url} />
                        <AvatarFallback>
                          {user.full_name?.substring(0, 2).toUpperCase() || '??'}
                        </AvatarFallback>
                      </Avatar>
                      <p className="text-sm font-medium">{user.full_name}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        <div className="bg-card p-4 sm:p-6 rounded-lg border">
          <h2 className="text-lg font-semibold mb-3">Membros Atuais</h2>
          {isLoadingMembers ? (
            <p>Carregando membros...</p>
          ) : members.length === 0 ? (
            <p>Nenhum membro encontrado neste canal.</p>
          ) : (
            <ScrollArea className="h-[300px] sm:h-[400px]">
              <div className="space-y-2 pr-4">
                {members.map((member) => (
                  <div
                    key={member.id}
                    className="flex items-center justify-between gap-2 p-3 rounded-lg hover:bg-muted"
                  >
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={member.avatar_url} />
                        <AvatarFallback>
                          {member.full_name?.substring(0, 2).toUpperCase() || '??'}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{member.full_name}</p>
                        <p className="text-sm text-muted-foreground capitalize">
                          {member.role}
                        </p>
                      </div>
                    </div>
                    {isTeamChannel ? (
                      <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                        Auto-gerenciado
                      </span>
                    ) : (
                      member.id !== currentUserId && member.role !== 'owner' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveMember(member.id)}
                          className="text-destructive hover:text-destructive-foreground hover:bg-destructive"
                          disabled={isLoading} 
                        >
                          <X className="mr-1 h-4 w-4" />
                          Remover
                        </Button>
                      )
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </div>
    </div>
  );
}
