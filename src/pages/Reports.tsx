/**
 * Descrição do arquivo.
 * <AUTHOR> Internet 2024
 */
import React, { useState } from 'react';
import { ReportsLayout } from '@/components/reports/ReportsLayout';
import { useParams, Navigate } from 'react-router-dom'; // Mantém Navigate por ora, se necessário
import { MainDashboard } from '@/components/reports/dashboard/MainDashboard'; // Reintroduz MainDashboard
import { EngagementReport } from '@/pages/reports/EngagementReport';
import { ContentReport } from '@/pages/reports/ContentReport';
import { GamificationReport } from '@/components/reports/gamification/GamificationReport';
import { ROIReport } from '@/components/reports/roi/ROIReport';
import { AIReport } from '@/components/reports/ai/AIReport';
import { ESGReport } from '@/components/reports/esg/ESGReport';
import { BenchmarksReport } from '@/components/reports/benchmarks/BenchmarksReport';
import { CultureReport } from '@/components/reports/culture/CultureReport';
import { LearningReport } from '@/components/reports/learning/LearningReport';
import { ReportPeriodSelector, type ReportDateRange } from '@/components/reports/ReportPeriodSelector';
import { subDays } from 'date-fns';
import { useLocalStorage } from '@/hooks/useLocalStorage';

export default function Reports() {
  const { reportType } = useParams();
  
  // Estado compartilhado para período de data com localStorage
  const today = new Date();
  const [dateRange, setDateRange] = useLocalStorage<ReportDateRange>('reports-date-range', {
    from: subDays(today, 7), // Padrão: 7 dias (plano Grátis)
    to: today
  });

  // Handler para mudança do período
  const handleDateRangeChange = (range: ReportDateRange) => {
    setDateRange(range);
  };

  // Criar o seletor de período
  const periodSelector = (
    <ReportPeriodSelector
      value={dateRange}
      onChange={handleDateRangeChange}
      variant="header"
    />
  );

  let content;
  let activeTab = reportType ?? '';
  switch (reportType) {
    case 'engagement':
      content = <EngagementReport dateRange={dateRange} />;
      activeTab = 'engagement';
      break;
    // case 'gamification':
    //   content = <GamificationReport />;
    //   activeTab = 'gamification';
    //   break;
    case 'content':
      content = <ContentReport dateRange={dateRange} />;
      activeTab = 'content';
      break;
    case 'roi':
      content = <ROIReport />;
      activeTab = 'roi';
      break;
    case 'ai-predictive':
      content = <AIReport />;
      activeTab = 'ai';
      break;
    case 'esg':
      content = <ESGReport />;
      activeTab = 'esg';
      break;
    case 'benchmarks':
      content = <BenchmarksReport />;
      activeTab = 'benchmarks';
      break;
    case 'culture':
      content = <CultureReport />;
      activeTab = 'culture';
      break;
    case 'learning':
      content = <LearningReport />;
      activeTab = 'learning';
      break;
    default:
      content = <EngagementReport dateRange={dateRange} />; 
      activeTab = 'engagement';
      break;
  }
  return (
    <ReportsLayout 
      activeTab={activeTab} 
      periodSelector={periodSelector}
      dateRange={dateRange}
    >
      {content}
    </ReportsLayout>
  );
}