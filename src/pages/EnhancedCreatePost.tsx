/**
 * EnhancedCreatePost.tsx
 * Versão aprimorada do componente CreatePost com layout premium de duas colunas
 * <AUTHOR> Internet 2025
 */
import { useState, useEffect, useMemo, useRef } from "react";
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";
import { motion } from "framer-motion";
import { supabase } from "@/integrations/supabase/client";
import { PostEditor } from "@/components/editor/PostEditor";
import { PollCreator, type PollData } from "@/components/poll/PollCreator";
import { HeroSection } from "@/components/common/HeroSection";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  successWithNotification, 
  errorWithNotification 
} from "@/lib/notifications/toastWithNotification";
import {
  ArrowLeft,
  Loader2,
  RocketIcon,
  Send,
  Eye,
  EyeOff,
  Calendar,
  Clock,
  CalendarClock,
  BarChart2,
  Check,
  Edit,
  Globe,
  Sparkles,
  Users,
  Building2,
  UserCheck,
  Zap,
  Clock4,
  Target,
  Star,
  BookOpen,
  Play,
  Pause,
  Mic,
  Upload,
  X,
  Info,
  AlertTriangle,
  Video,
  ImagePlus,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import { format, addHours, isAfter } from "date-fns";
import { ptBR } from "date-fns/locale";
import { cn } from "@/lib/utils";
import {
  AudienceSelector,
  type AudienceData,
} from "@/components/common/AudienceSelector";
import { AudienceDisplay } from "@/components/common/AudienceDisplay";
import { useCreatePost, usePost, useUpdatePost, useCanEditPost } from "@/lib/query/hooks/usePosts";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
import { SmartFeatures } from "@/components/editor/SmartFeatures";
import { PostAudioService } from "@/services/post-audio";
import { VideoPlayer } from "@/components/ui/VideoPlayer";
import { usePostScheduleLimits } from "@/hooks/posts/usePostScheduleLimits";
import { SmartUpgradeButton } from "@/components/ui/PlanUpgradeButton";
import { PhotoGalleryUploader } from "@/components/feed/PhotoGalleryUploader";
import { PostPhotoGallery } from "@/components/feed/PostPhotoGallery";

type PublishMode = "now" | "scheduled";

// Componente para áudio no CreatePost
const AudioSection = ({ 
  onAudioData, 
  audioData 
}: { 
  onAudioData: (data: { url: string; duration: number } | null) => void;
  audioData?: { url: string; duration: number } | null;
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const chunksRef = useRef<Blob[]>([]);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(chunksRef.current, { type: 'audio/webm' });
        const audioUrl = URL.createObjectURL(audioBlob);
        const audio = new Audio(audioUrl);
        
        const handleDurationCapture = async () => {
          console.log('Tentando capturar duração...');
          console.log('audio.duration:', audio.duration);
          console.log('audio.readyState:', audio.readyState);
          
          // Tentar obter duração de várias formas
          let duration = 0;
          
          if (audio.duration && isFinite(audio.duration) && audio.duration > 0) {
            duration = Math.round(audio.duration);
            console.log('Duração obtida dos metadados:', duration);
          } else {
            // Fallback: usar o tempo de gravação como aproximação
            duration = recordingTime > 0 ? recordingTime : 3; // Mínimo 3 segundos se não conseguir capturar
            console.log('Usando duração de gravação como fallback:', duration);
          }
          
          try {
            const uploadResult = await PostAudioService.uploadAudio(audioBlob, `recording-${Date.now()}.webm`);
            console.log('Resultado do upload:', uploadResult);
            const audioUrl = typeof uploadResult === 'string' ? uploadResult : uploadResult.url;
            console.log('URL do áudio extraída:', audioUrl);
            onAudioData({ url: audioUrl, duration });
          } catch (error) {
            console.error('Erro ao fazer upload do áudio:', error);
          }
          
          URL.revokeObjectURL(audioUrl);
        };

        // Tentar capturar duração com múltiplos eventos
        audio.onloadedmetadata = handleDurationCapture;
        audio.oncanplaythrough = () => {
          console.log('Audio pronto para reprodução, duração:', audio.duration);
          if (!audio.duration || !isFinite(audio.duration)) {
            console.log('Duração ainda não disponível no canplaythrough');
          }
        };
        
        // Forçar carregamento
        audio.load();
      };

      mediaRecorder.start();
      setIsRecording(true);
      setRecordingTime(0);

      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

    } catch (error) {
      console.error('Erro ao acessar microfone:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
      
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('audio/')) {
      alert('Por favor, selecione um arquivo de áudio válido.');
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      alert('Arquivo muito grande. Máximo 10MB.');
      return;
    }

    try {
      const audioUrl = URL.createObjectURL(file);
      const audio = new Audio(audioUrl);
      
      audio.onloadedmetadata = async () => {
        const duration = audio.duration && isFinite(audio.duration) ? Math.round(audio.duration) : 0;
        console.log('Duração do arquivo enviado:', duration, 'segundos');
        console.log('Duração original do arquivo audio.duration:', audio.duration);
        
        try {
          const uploadResult = await PostAudioService.uploadAudio(file, file.name);
          console.log('Resultado do upload do arquivo:', uploadResult);
          const audioUrl = typeof uploadResult === 'string' ? uploadResult : uploadResult.url;
          console.log('URL do arquivo extraída:', audioUrl);
          onAudioData({ url: audioUrl, duration });
        } catch (error) {
          console.error('Erro ao fazer upload do áudio:', error);
        }
        
        URL.revokeObjectURL(audioUrl);
      };
    } catch (error) {
      console.error('Erro ao processar arquivo de áudio:', error);
    }
  };

  const togglePlayback = async () => {
    if (!audioData?.url || !audioRef.current) return;

    try {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        await audioRef.current.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error('Erro ao reproduzir áudio:', error);
      setIsPlaying(false);
    }
  };

  const formatTime = (seconds: number) => {
    if (!seconds || isNaN(seconds) || !isFinite(seconds)) {
      return '0:00';
    }
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (audioData) {
    console.log('Renderizando player com audioData:', audioData);
    return (
      <Card className="p-4 border-orange-200 bg-orange-50/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={togglePlayback}
              className="h-8 w-8 p-0 hover:bg-orange-100"
            >
              {isPlaying ? (
                <Pause className="h-4 w-4 text-orange-600" />
              ) : (
                <Play className="h-4 w-4 text-orange-600" />
              )}
            </Button>
            
            <div className="flex flex-col">
              <span className="text-sm font-medium text-orange-700">
                Áudio anexado
              </span>
              <span className="text-xs text-orange-600">
                Duração: {formatTime(audioData.duration)}
              </span>
            </div>
          </div>
          
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => onAudioData(null)}
            className="h-8 w-8 p-0 hover:bg-red-100"
          >
            <X className="h-4 w-4 text-red-600" />
          </Button>
        </div>
        
        <audio
          ref={audioRef}
          src={audioData.url}
          onEnded={() => setIsPlaying(false)}
          onLoadedData={() => {
            console.log('Áudio carregado com sucesso, src:', audioData.url);
            if (audioRef.current && audioRef.current.duration && isFinite(audioRef.current.duration)) {
              const realDuration = Math.round(audioRef.current.duration);
              console.log('Duração real capturada do player:', realDuration);
              if (realDuration !== audioData.duration && realDuration > 0) {
                console.log('Atualizando duração de', audioData.duration, 'para', realDuration);
                onAudioData({ url: audioData.url, duration: realDuration });
              }
            }
          }}
          onDurationChange={() => {
            if (audioRef.current && audioRef.current.duration && isFinite(audioRef.current.duration)) {
              const realDuration = Math.round(audioRef.current.duration);
              console.log('Evento durationChange - Nova duração:', realDuration);
              if (realDuration !== audioData.duration && realDuration > 0) {
                console.log('Atualizando duração via durationChange de', audioData.duration, 'para', realDuration);
                onAudioData({ url: audioData.url, duration: realDuration });
              }
            }
          }}
          onError={(e) => {
            console.error('Erro ao carregar áudio:', e);
            console.error('URL do áudio que falhou:', audioData.url);
          }}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          preload="metadata"
        />
      </Card>
    );
  }

  return (
    <Card className="p-4 border-dashed border-gray-300">
      <div className="flex items-center justify-center gap-4">
        {isRecording ? (
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium text-red-600">
                Gravando: {formatTime(recordingTime)}
              </span>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={stopRecording}
              className="border-red-200 text-red-600 hover:bg-red-50"
            >
              Parar Gravação
            </Button>
          </div>
        ) : (
          <>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={startRecording}
              className="flex items-center gap-2"
            >
              <Mic className="h-4 w-4" />
              Gravar Áudio
            </Button>
            
            <span className="text-sm text-gray-500">ou</span>
            
            <div className="relative">
              <input
                type="file"
                accept="audio/*"
                onChange={handleFileUpload}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Upload className="h-4 w-4" />
                Enviar Arquivo
              </Button>
            </div>
          </>
        )}
      </div>
      
      <p className="text-xs text-gray-500 text-center mt-2">
        Grave um áudio ou envie um arquivo (máx 10MB)
      </p>
    </Card>
  );
};

// Constantes para limites de vídeo
const VIDEO_UPLOAD_MAX_SIZE = 10 * 1024 * 1024; // 10MB
const VIDEO_RECORDING_MAX_DURATION = 120; // 2 minutos em segundos

// Componente para vídeo no CreatePost
const VideoSection = ({ 
  onVideoData, 
  videoData 
}: { 
  onVideoData: (data: { url: string; duration: number; poster?: string } | null) => void;
  videoData?: { url: string; duration: number; poster?: string } | null;
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const previewVideoRef = useRef<HTMLVideoElement | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);
  const recordingTimeRef = useRef<number>(0);

  // Limpar timer ao desmontar
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  // UseEffect para configurar o preview de vídeo quando começar a gravar
  useEffect(() => {
    if (isRecording && streamRef.current && previewVideoRef.current) {
      const videoElement = previewVideoRef.current;
      videoElement.srcObject = streamRef.current;
      videoElement.muted = true;

      videoElement.play().catch((error) => {
        console.error('Erro ao configurar preview no useEffect:', error);
      });
    }
  }, [isRecording]);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: {
          width: { ideal: 480, max: 720 },
          height: { ideal: 640, max: 960 },
          facingMode: "user", // Usar câmera frontal
        },
        audio: true 
      });
      streamRef.current = stream;
      
      // Configurar preview da câmera imediatamente
      if (previewVideoRef.current) {
        previewVideoRef.current.srcObject = stream;
        previewVideoRef.current.muted = true; // Importante para evitar feedback
        previewVideoRef.current.play().catch((error) => {
          console.error('Erro ao iniciar preview:', error);
        });
      }

      // Configurar MediaRecorder com codec compatível
      let options = {};
      if (MediaRecorder.isTypeSupported("video/webm;codecs=vp9")) {
        options = { mimeType: "video/webm;codecs=vp9" };
      } else if (MediaRecorder.isTypeSupported("video/webm;codecs=vp8")) {
        options = { mimeType: "video/webm;codecs=vp8" };
      } else if (MediaRecorder.isTypeSupported("video/webm")) {
        options = { mimeType: "video/webm" };
      }

      const mediaRecorder = new MediaRecorder(stream, options);
      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        // Capturar o tempo de gravação no momento da parada usando a ref
        const finalRecordingTime = recordingTimeRef.current;
        console.log('📹 MediaRecorder parado. Tempo final de gravação (ref):', finalRecordingTime, 'segundos');
        
        // Usar o tipo do MediaRecorder ou fallback para webm
        const mimeType = mediaRecorder.mimeType || "video/webm";
        const videoBlob = new Blob(chunksRef.current, { type: mimeType });
        
        // Parar o preview ao vivo
        if (previewVideoRef.current) {
          previewVideoRef.current.srcObject = null;
        }

        // Parar stream da câmera
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => track.stop());
          streamRef.current = null;
        }

        // Calcular duração do vídeo
        const videoUrl = URL.createObjectURL(videoBlob);
        const video = document.createElement('video');
        
        video.onloadedmetadata = async () => {
          let duration = finalRecordingTime; // Usar tempo de gravação capturado no momento da parada
          console.log('Tempo de gravação registrado no onstop:', finalRecordingTime, 'segundos');
          
          if (video.duration && isFinite(video.duration) && video.duration > 0) {
            duration = Math.round(video.duration);
            console.log('Duração do vídeo obtida dos metadados:', duration, 'segundos');
          } else {
            console.log('Usando tempo de gravação como duração:', duration, 'segundos');
          }
          
          // Garantir que a duração seja pelo menos 1 segundo
          if (duration <= 0) {
            duration = Math.max(1, finalRecordingTime);
            console.log('Duração ajustada para:', duration, 'segundos');
          }
          
          try {
            // Gerar poster de um frame mais avançado do vídeo
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            // Buscar um frame com conteúdo (10% da duração ou pelo menos 0.5s)
            const seekTime = Math.max(0.5, duration * 0.1);
            console.log(`📸 Capturando poster no tempo: ${seekTime}s de ${duration}s`);
            
            video.currentTime = seekTime;
            await new Promise(resolve => {
              video.onseeked = resolve;
            });
            
            // Aguardar um momento para garantir que o frame foi renderizado
            await new Promise(resolve => setTimeout(resolve, 100));
            
            ctx?.drawImage(video, 0, 0);
            const posterBlob = await new Promise<Blob>((resolve) => {
              canvas.toBlob((blob) => resolve(blob!), 'image/jpeg', 0.9);
            });
            
            // Upload do poster
            const posterFile = new File([posterBlob], `video-poster-${Date.now()}.jpg`, { type: 'image/jpeg' });
            const posterUrl = await PostAudioService.uploadAudio(posterFile, posterFile.name);
            
            // Upload do vídeo
            const videoFile = new File([videoBlob], `video-recording-${Date.now()}.webm`, { type: 'video/webm' });
            const uploadedUrl = await PostAudioService.uploadAudio(videoFile, videoFile.name);
            
            onVideoData({ url: uploadedUrl, duration, poster: posterUrl });
          } catch (error) {
            console.error('Erro ao fazer upload do vídeo:', error);
            alert('Erro ao fazer upload do vídeo. Tente novamente.');
          }
          
          URL.revokeObjectURL(videoUrl);
        };
        
        video.onerror = () => {
          console.error('Erro ao carregar metadados do vídeo gravado');
          // Usar tempo de gravação se não conseguir obter duração
          let duration = Math.max(1, finalRecordingTime); // Garantir pelo menos 1 segundo
          console.log('Usando tempo de gravação na condição de erro:', duration, 'segundos');
          
          const videoFile = new File([videoBlob], `video-recording-${Date.now()}.webm`, { type: 'video/webm' });
          PostAudioService.uploadAudio(videoFile, videoFile.name)
            .then(uploadedUrl => {
              onVideoData({ url: uploadedUrl, duration });
              URL.revokeObjectURL(videoUrl);
            })
            .catch(error => {
              console.error('Erro ao fazer upload do vídeo:', error);
              alert('Erro ao fazer upload do vídeo. Tente novamente.');
              URL.revokeObjectURL(videoUrl);
            });
        };
        
        video.src = videoUrl;
      };

      mediaRecorder.start();
      setIsRecording(true);
      setRecordingTime(0);
      recordingTimeRef.current = 0; // Inicializar ref

      // Timer para mostrar tempo de gravação e parar automaticamente
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => {
          const newTime = prev + 1;
          recordingTimeRef.current = newTime; // Manter ref sincronizada
          console.log('⏱️ Tempo de gravação atualizado:', newTime, 'segundos');
          // Parar automaticamente aos 2 minutos
          if (newTime >= VIDEO_RECORDING_MAX_DURATION) {
            stopRecording();
            return VIDEO_RECORDING_MAX_DURATION;
          }
          return newTime;
        });
      }, 1000);

    } catch (error) {
      console.error('Erro ao acessar câmera/microfone:', error);
      alert('Não foi possível acessar a câmera. Verifique as permissões.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      console.log('🛑 Parando gravação. Tempo total do estado:', recordingTime, 'segundos');
      console.log('🛑 Parando gravação. Tempo total da ref:', recordingTimeRef.current, 'segundos');
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      // Parar stream da câmera
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('video/')) {
      alert('Por favor, selecione um arquivo de vídeo válido.');
      return;
    }

    if (file.size > VIDEO_UPLOAD_MAX_SIZE) {
      alert(`Arquivo muito grande. Máximo ${Math.round(VIDEO_UPLOAD_MAX_SIZE / (1024 * 1024))}MB.`);
      return;
    }

    try {
      const videoUrl = URL.createObjectURL(file);
      const video = document.createElement('video');
      
      video.onloadedmetadata = async () => {
        let duration = 0;
        if (video.duration && isFinite(video.duration) && video.duration > 0) {
          duration = Math.round(video.duration);
        }
        console.log('Duração do vídeo enviado:', duration, 'segundos');
        
        try {
          // Gerar poster de um frame mais avançado do vídeo
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;
          
          // Buscar um frame com conteúdo (10% da duração ou pelo menos 0.5s)
          const seekTime = Math.max(0.5, duration * 0.1);
          console.log(`📸 Capturando poster no tempo: ${seekTime}s de ${duration}s`);
          
          video.currentTime = seekTime;
          await new Promise(resolve => {
            video.onseeked = resolve;
          });
          
          // Aguardar um momento para garantir que o frame foi renderizado
          await new Promise(resolve => setTimeout(resolve, 100));
          
          ctx?.drawImage(video, 0, 0);
          const posterBlob = await new Promise<Blob>((resolve) => {
            canvas.toBlob((blob) => resolve(blob!), 'image/jpeg', 0.9);
          });
          
          // Upload do poster
          const posterFile = new File([posterBlob], `video-poster-${Date.now()}.jpg`, { type: 'image/jpeg' });
          const posterUrl = await PostAudioService.uploadAudio(posterFile, posterFile.name);
          
          // Upload do vídeo
          const uploadResult = await PostAudioService.uploadAudio(file, file.name);
          console.log('Resultado do upload do vídeo:', uploadResult);
          const videoUrl = typeof uploadResult === 'string' ? uploadResult : uploadResult.url;
          console.log('URL do vídeo extraída:', videoUrl);
          
          onVideoData({ url: videoUrl, duration, poster: posterUrl });
        } catch (error) {
          console.error('Erro ao fazer upload do vídeo:', error);
          alert('Erro ao fazer upload do vídeo. Tente novamente.');
        }
        
        URL.revokeObjectURL(videoUrl);
      };
      
      video.onerror = () => {
        console.error('Erro ao carregar metadados do vídeo');
        URL.revokeObjectURL(videoUrl);
      };
      
      video.src = videoUrl;
    } catch (error) {
      console.error('Erro ao processar arquivo de vídeo:', error);
      alert('Erro ao processar vídeo. Tente novamente.');
    }
  };

  const togglePlayback = async () => {
    if (!videoData?.url || !videoRef.current) return;

    try {
      if (isPlaying) {
        videoRef.current.pause();
        setIsPlaying(false);
      } else {
        await videoRef.current.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error('Erro ao reproduzir vídeo:', error);
      setIsPlaying(false);
    }
  };

  const formatTime = (seconds: number) => {
    if (!seconds || isNaN(seconds) || !isFinite(seconds)) {
      return '0:00';
    }
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (videoData) {
    console.log('Renderizando player com videoData:', videoData);
    return (
      <Card className="p-4 border-blue-200/60 bg-gradient-to-r from-blue-50/30 to-indigo-50/30 shadow-sm">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <div className="h-1.5 w-1.5 rounded-full bg-blue-500 animate-pulse"></div>
            <span className="text-xs font-medium text-blue-600">
              Vídeo anexado • {formatTime(videoData.duration)}
            </span>
          </div>
          
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => onVideoData(null)}
            className="h-6 w-6 p-0 hover:bg-red-100 transition-all duration-200 hover:scale-105"
          >
            <X className="h-3 w-3 text-red-600" />
          </Button>
        </div>
        
        <div className="rounded-lg overflow-hidden">
          <VideoPlayer 
            src={videoData.url}
            duration={videoData.duration}
            className="w-full"
            aspectRatio="auto"
          />
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-4 border-dashed border-gray-300">
      {isRecording ? (
        <div className="space-y-4">
          {/* Preview da câmera durante gravação */}
          <div className="relative bg-black rounded-lg overflow-hidden">
            <video
              ref={previewVideoRef}
              className="w-full h-48 object-contain"
              style={{ maxHeight: "200px", minHeight: "150px" }}
              autoPlay
              muted
              playsInline
              controls={false}
            />
            {/* Indicador de gravação */}
            <div className="absolute top-2 right-2 flex items-center gap-1 bg-red-500 text-white px-2 py-1 rounded-full text-xs z-10">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              REC
            </div>
            {/* Timer de gravação */}
            <div className="absolute bottom-2 left-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
              {formatTime(recordingTime)} / {formatTime(VIDEO_RECORDING_MAX_DURATION)}
            </div>
          </div>
          
          {/* Controles de gravação */}
          <div className="flex items-center justify-center gap-3">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={stopRecording}
              className="border-red-200 text-red-600 hover:bg-red-50"
            >
              Parar Gravação
            </Button>
          </div>
        </div>
      ) : (
        <>
          <div className="flex items-center justify-center gap-4">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={startRecording}
              className="flex items-center gap-2"
            >
              <Video className="h-4 w-4" />
              Gravar Vídeo
            </Button>
            
            <span className="text-sm text-gray-500">ou</span>
            
            <div className="relative">
              <input
                type="file"
                accept="video/*"
                onChange={handleFileUpload}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Upload className="h-4 w-4" />
                Enviar Arquivo
              </Button>
            </div>
          </div>
          
          <div className="mt-2 space-y-1">
            <p className="text-xs text-gray-500 text-center">
              Grave um vídeo de até {VIDEO_RECORDING_MAX_DURATION / 60} minutos ou envie um arquivo (máx {Math.round(VIDEO_UPLOAD_MAX_SIZE / (1024 * 1024))}MB)
            </p>
            <p className="text-xs text-amber-600 text-center font-medium">
              ⚠️ Ao clicar em "Gravar Vídeo", a gravação iniciará imediatamente
            </p>
          </div>
        </>
      )}
    </Card>
  );
};

// Variantes de animação para layout de duas colunas
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const columnVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

const previewVariants = {
  hidden: { opacity: 0, x: 20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

// Componente para opção de agendamento com controle de limites
const PostScheduleOption = () => {
  const { limits, isLoading, canSchedule, getUpgradeMessage } = usePostScheduleLimits();
  
  const handleScheduleClick = () => {
    if (!canSchedule()) {
      // Mostrar notificação com informações de upgrade
      errorWithNotification("Limite de posts agendados atingido", {
        description: getUpgradeMessage(),
        persist: true
      });
      return;
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <RadioGroupItem 
          value="scheduled" 
          id="publish-scheduled"
          disabled={isLoading || !canSchedule()}
          onClick={handleScheduleClick}
        />
        <Label 
          htmlFor="publish-scheduled" 
          className={cn(
            "cursor-pointer",
            (!canSchedule() && !isLoading) && "text-muted-foreground"
          )}
        >
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Agendar publicação
            {!canSchedule() && !isLoading && (
              <Badge variant="secondary" className="text-xs">
                Limite atingido
              </Badge>
            )}
          </div>
        </Label>
      </div>
      
      {/* Mostrar informações de limite e botão de upgrade quando necessário */}
      {!canSchedule() && !isLoading && limits && (
        <div className="ml-6 p-3 bg-amber-50 border border-amber-200 rounded-lg">
          <div className="flex items-start justify-between gap-3">
            <div className="flex-1">
              <p className="text-sm text-amber-800 font-medium">
                Posts agendados: {limits.currentScheduledPosts}/{limits.maxScheduledPosts === -1 ? '∞' : limits.maxScheduledPosts}
              </p>
              <p className="text-xs text-amber-700 mt-1">
                {getUpgradeMessage()}
              </p>
            </div>
            <SmartUpgradeButton
              currentPlan={limits.currentPlan as 'Grátis' | 'Pro' | 'Max'}
              source="post-schedule-limit"
              size="sm"
              className="shrink-0"
            />
          </div>
        </div>
      )}
      
      {/* Indicador de uso quando há limite mas ainda pode usar */}
      {canSchedule() && !isLoading && limits && !limits.isUnlimited && (
        <div className="ml-6 text-xs text-muted-foreground">
          {limits.remainingSlots === 1 
            ? `Você pode agendar mais 1 post`
            : `Você pode agendar mais ${limits.remainingSlots} posts`
          }
        </div>
      )}
    </div>
  );
};

const EnhancedCreatePost = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  // Verificar se está em modo de edição
  const editPostId = searchParams.get('edit');
  const isEditMode = !!editPostId;
  
  // Verificar se há conteúdo pré-populado
  const generatedData = location.state as {
    generatedContent?: string;
    generatedImage?: string;
    category?: string;
    isGenerated?: boolean;
  } | null;

  const [content, setContent] = useState(generatedData?.generatedContent || "");
  const [publishMode, setPublishMode] = useState<PublishMode>("now");
  const [hasPoll, setHasPoll] = useState(false);
  const [poll, setPoll] = useState<PollData | null>(null);
  const [isPollEditing, setIsPollEditing] = useState(true);
  const [audience, setAudience] = useState<AudienceData>({ type: "all" });
  const [isPreviewMode, setIsPreviewMode] = useState(true);
  const [showGeneratedBadge, setShowGeneratedBadge] = useState(generatedData?.isGenerated || false);

  // ID temporário para associar imagens durante o upload
  const [currentCompanyId, setCurrentCompanyId] = useState<string | null>(null);
  const [tempPostId] = useState<string>(() => {
    const uuid = crypto.randomUUID();
    console.log(`[EnhancedCreatePost] UUID temporário gerado: ${uuid}`);
    return uuid;
  });

  // Definir data inicial como hoje
  const [scheduledDate, setScheduledDate] = useState<Date | undefined>(
    new Date()
  );

  // Definir hora inicial como 30 minutos à frente da hora atual
  const now = new Date();
  now.setMinutes(now.getMinutes() + 30);
  const [scheduledTime, setScheduledTime] = useState(format(now, "HH:mm"));

  const createPostMutation = useCreatePost();
  const updatePostMutation = useUpdatePost();
  
  // Buscar post se estiver em modo de edição
  const { data: existingPost, isLoading: isLoadingPost } = usePost(editPostId || undefined);
  
  // Verificar permissões de edição
  const { data: canEditData, isLoading: isCheckingEditPermissions } = useCanEditPost(editPostId || undefined);

  // Estado para áudio
  const [audioData, setAudioData] = useState<{ url: string; duration: number } | null>(null);
  const [hasAudio, setHasAudio] = useState(false);

  // Estado para vídeo
  const [videoData, setVideoData] = useState<{ url: string; duration: number; poster?: string } | null>(null);
  const [hasVideo, setHasVideo] = useState(false);

  // Estado para galeria de fotos
  const [photosData, setPhotosData] = useState<{ id: string; url: string; file: File; uploading?: boolean }[]>([]);
  const [hasPhotoGallery, setHasPhotoGallery] = useState(false);

  // Estado para controlar a inserção da imagem gerada
  const [generatedImageToInsert, setGeneratedImageToInsert] = useState<string | null>(
    generatedData?.generatedImage || null
  );

  useEffect(() => {
    const fetchCompanyId = async () => {
      try {
        const { data: userData, error: userError } = await supabase.auth.getUser();
        if (userError) throw userError;
        if (!userData.user) {
          logQueryEvent('EnhancedCreatePost', "Usuário não autenticado ao buscar companyId para onImageUpload", {}, "warn");
          throw new Error("User not authenticated");
        }

        const { data: profile, error: profileError } = await supabase
          .from("profiles")
          .select("company_id")
          .eq("id", userData.user.id)
          .single();

        if (profileError) throw profileError;
        if (!profile || !profile.company_id) {
          logQueryEvent('EnhancedCreatePost', "Company ID não encontrado no perfil do usuário", { userId: userData.user.id }, "warn");
          throw new Error("Company ID not found in profile");
        }

        setCurrentCompanyId(profile.company_id);
        logQueryEvent('EnhancedCreatePost', `Company ID fetched para onImageUpload: ${profile.company_id}`);
      } catch (error) {
        logQueryEvent('EnhancedCreatePost', "Erro ao buscar company_id para onImageUpload", { error }, "error");
      }
    };
    fetchCompanyId();
  }, []);

  // Carregar dados do post existente se estiver em modo de edição
  useEffect(() => {
    if (isEditMode && existingPost && !isLoadingPost) {
      setContent(existingPost.content || "");
      
      // Configurar audiência baseada nos dados do post
      if (existingPost.post_audience && existingPost.post_audience.length > 0) {
        const firstAudience = existingPost.post_audience[0];
        const audienceType = firstAudience.target_type as "all" | "department" | "team" | "user";
        
        if (audienceType === "all") {
          setAudience({ type: "all" });
        } else {
          const targets = existingPost.post_audience
            .map((a: any) => a.target_id)
            .filter((id: string) => id !== null);
          
          setAudience({
            type: audienceType,
            targets,
          });
        }
      } else {
        setAudience({ type: "all" });
      }
      
      // Configurar áudio se existir
      if (existingPost.metadata?.audio_url) {
        setAudioData({
          url: existingPost.metadata.audio_url,
          duration: existingPost.metadata.audio_duration || 0,
        });
        setHasAudio(true);
      }
      
      // Configurar enquete se existir
      if (existingPost.has_poll) {
        setHasPoll(true);
        // Nota: Para buscar dados completos da enquete, seria necessário
        // fazer uma query adicional para a tabela polls
      }
    }
  }, [isEditMode, existingPost, isLoadingPost]);

  // Forçar atualização do preview quando há conteúdo gerado pela IA
  useEffect(() => {
    if (generatedData?.generatedContent && generatedData.generatedContent !== content) {
      logQueryEvent('EnhancedCreatePost', 'Sincronizando conteúdo gerado pela IA com preview');
      setContent(generatedData.generatedContent);
    }
  }, [generatedData?.generatedContent, content]);

  // Memoizar o conteúdo processado para o preview
  const previewContent = useMemo(() => {
    if (!content.trim()) {
      return '<p class="text-muted-foreground italic">Preview aparecerá aqui conforme você digita no editor...</p>';
    }
    return content;
  }, [content, generatedData?.isGenerated]);

  // Estatísticas do conteúdo
  const contentStats = useMemo(() => {
    const plainText = content.replace(/<[^>]*>/g, '');
    const wordCount = plainText.trim() ? plainText.trim().split(/\s+/).length : 0;
    const charCount = plainText.length;
    const estimatedReadTime = Math.max(1, Math.ceil(wordCount / 200));

    return {
      words: wordCount,
      characters: charCount,
      readTime: estimatedReadTime
    };
  }, [content]);

  const handleUpdatePost = async () => {
    if (!editPostId || !content.trim()) {
      errorWithNotification("Conteúdo vazio", {
        description: "Por favor, adicione algum conteúdo à sua publicação."
      });
      return;
    }

    try {
      await updatePostMutation.mutateAsync({
        postId: editPostId,
        content: content.trim(),
        editReason: "Editado via interface de criação"
      });

      successWithNotification("Post editado!", {
        description: "Sua publicação foi editada com sucesso."
      });

      navigate("/feed");
    } catch (error) {
      console.error("Erro ao editar publicação:", error);
      errorWithNotification("Erro ao editar post", {
        description: "Não foi possível editar seu post. Tente novamente."
      });
    }
  };

  const handleCreatePost = async () => {
    // Se estiver editando, usar lógica de update
    if (isEditMode && editPostId) {
      return handleUpdatePost();
    }
    // Validações... (manter as mesmas validações do código original)
    if (!content.trim() && !hasPoll && !audioData && !videoData && photosData.length === 0) {
      errorWithNotification("Conteúdo vazio", {
        description: "Por favor, adicione algum conteúdo, um áudio, um vídeo, uma galeria de fotos ou uma enquete à sua publicação."
      });
      return;
    }

    if (hasPoll) {
      if (!poll) {
        errorWithNotification("Enquete incompleta", {
          description: "Por favor, configure sua enquete antes de publicar."
        });
        return;
      }

      if (!poll.question.trim()) {
        errorWithNotification("Pergunta da enquete vazia", {
          description: "Por favor, adicione uma pergunta à sua enquete."
        });
        return;
      }

      if (poll.options.length < 2) {
        errorWithNotification("Opções insuficientes", {
          description: "Sua enquete precisa ter pelo menos 2 opções."
        });
        return;
      }

      if (poll.options.some((option) => !option.text.trim())) {
        errorWithNotification("Opção vazia", {
          description: "Todas as opções da enquete precisam ter texto."
        });
        return;
      }
    }

    if (
      audience.type !== "all" &&
      (!audience.targets || audience.targets.length === 0)
    ) {
      errorWithNotification("Audiência incompleta", {
        description: `Por favor, selecione pelo menos um ${
          audience.type === "department"
            ? "departamento"
            : audience.type === "team"
            ? "equipe"
            : "pessoa"
        }.`
      });
      return;
    }

    if (publishMode === "scheduled" && scheduledDate) {
      const [hours, minutes] = scheduledTime.split(":").map(Number);
      const scheduledDateTime = new Date(scheduledDate);
      scheduledDateTime.setHours(hours, minutes, 0, 0);

      const currentDate = new Date();

      if (!isAfter(scheduledDateTime, currentDate)) {
        errorWithNotification("Data inválida", {
          description: "A data e hora de agendamento devem ser no futuro. Se for para hoje, escolha um horário posterior ao atual."
        });
        return;
      }
    }

    try {
      logQueryEvent('EnhancedCreatePost', 'Conteúdo antes da mutação:', { content });

      const result = await createPostMutation.mutateAsync({
        content,
        publishMode,
        scheduledDate,
        scheduledTime,
        poll: hasPoll ? poll : null,
        audience,
        tempPostId: tempPostId,
        audioData,
        videoData,
        photosData: photosData.length > 0 ? photosData : undefined,
      });

      const postId = result.postId;
      
      logQueryEvent('EnhancedCreatePost', 'ID do post criado:', { postId });
      
      if (result.publishMode === "scheduled") {
        const formattedDate = format(
          new Date(result.scheduledDateTime || ""),
          "d 'de' MMMM 'às' HH:mm",
          { locale: ptBR }
        );

        successWithNotification("Post agendado com sucesso!", {
          description: `Sua publicação será publicada em ${formattedDate}.`,
          persist: true,
          notificationType: "post_published",
          referenceId: postId
        });
      } else {
        // ✅ Toast simples - não persiste no banco (persist: false é o padrão)
        // As notificações do público-alvo são criadas automaticamente pelo trigger
        successWithNotification("Post publicado com sucesso!", {
          description: "Sua publicação já está disponível no feed.",
          // persist: false é o padrão, então não precisa especificar
        });
      }

      navigate("/feed");
    } catch (error) {
      console.error("Erro ao criar publicação:", error);
      errorWithNotification("Erro ao criar post", {
        description: "Não foi possível publicar seu post. Tente novamente."
      });
    }
  };

  const togglePoll = () => {
    if (isEditMode) return; // Não permitir mudanças na enquete durante edição
    
    setHasPoll(!hasPoll);
    if (!hasPoll && !poll) {
      setPoll({
        question: "",
        options: [
          { id: "1", text: "" },
          { id: "2", text: "" },
        ],
        allowMultipleAnswers: false,
        duration: 7,
      });
      setIsPollEditing(true);
    }
  };

  const toggleAudio = () => {
    if (isEditMode) return; // Não permitir mudanças no áudio durante edição
    
    setHasAudio(!hasAudio);
    if (hasAudio) {
      // Se está removendo o áudio, limpar os dados
      setAudioData(null);
    }
  };

  const toggleVideo = () => {
    if (isEditMode) return; // Não permitir mudanças no vídeo durante edição
    
    setHasVideo(!hasVideo);
    if (hasVideo) {
      // Se está removendo o vídeo, limpar os dados
      setVideoData(null);
    }
  };

  const togglePhotoGallery = () => {
    if (isEditMode) return; // Não permitir mudanças na galeria durante edição
    
    setHasPhotoGallery(!hasPhotoGallery);
    if (hasPhotoGallery) {
      // Se está removendo a galeria, limpar os dados
      setPhotosData([]);
    }
  };

  const handlePollChange = (updatedPoll: PollData | null) => {
    setPoll(updatedPoll);
    if (updatedPoll === null) {
      setHasPoll(false);
    } else {
      setIsPollEditing(false);
    }
  };

  const handleEditPoll = () => {
    setIsPollEditing(true);
  };

  const handleAudioDataChange = (data: { url: string; duration: number } | null) => {
    setAudioData(data);
    // Se áudio foi adicionado, garantir que a seção está ativa
    if (data && !hasAudio) {
      setHasAudio(true);
    }
  };

  const handleVideoDataChange = (data: { url: string; duration: number; poster?: string } | null) => {
    setVideoData(data);
    // Se vídeo foi adicionado, garantir que a seção está ativa
    if (data && !hasVideo) {
      setHasVideo(true);
    }
  };

  const isPostReady =
    (content.trim().length > 0 ||
      audioData ||
      videoData ||
      photosData.length > 0 ||
      (hasPoll &&
        poll &&
        poll.question.trim() !== "" &&
        poll.options.length >= 2 &&
        poll.options.every((option) => option.text.trim() !== ""))) &&
    (audience.type === "all" ||
      (audience.targets && audience.targets.length > 0));

  const formatPollDuration = (days: number) => {
    if (days === 1) return "1 dia";
    return `${days} dias`;
  };

  const getAudienceText = () => {
    if (audience.type === "all") {
      return "Todos os funcionários";
    }

    if (!audience.targets || audience.targets.length === 0) {
      return `Nenhum ${
        audience.type === "department"
          ? "departamento"
          : audience.type === "team"
          ? "equipe"
          : "pessoa"
      } selecionado`;
    }

    const count = audience.targets.length;

    if (audience.type === "department") {
      return `${count} ${
        count === 1 ? "departamento" : "departamentos"
      } selecionado${count === 1 ? "" : "s"}`;
    } else if (audience.type === "team") {
      return `${count} ${count === 1 ? "equipe" : "equipes"} selecionada${
        count === 1 ? "" : "s"
      }`;
    } else {
      return `${count} ${count === 1 ? "pessoa" : "pessoas"} selecionada${
        count === 1 ? "" : "s"
      }`;
    }
  };

  const getAudienceIcon = () => {
    switch (audience.type) {
      case "department":
        return Building2;
      case "team":
        return Users;
      case "person":
        return UserCheck;
      default:
        return Globe;
    }
  };

  // Mostrar loading se estiver carregando post para edição ou verificando permissões
  if (isEditMode && (isLoadingPost || isCheckingEditPermissions)) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container max-w-7xl py-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="flex flex-col items-center gap-4">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="text-muted-foreground">
                {isLoadingPost ? "Carregando post para edição..." : "Verificando permissões..."}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Verificar se usuário tem permissão para editar
  if (isEditMode && canEditData && !canEditData.can_edit) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container max-w-7xl py-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="flex flex-col items-center gap-6 text-center">
              <div className="rounded-full bg-red-100 p-6">
                <X className="h-12 w-12 text-red-600" />
              </div>
              <div className="space-y-2">
                <h2 className="text-2xl font-bold text-gray-900">Acesso Negado</h2>
                <p className="text-gray-600 max-w-md">
                  {canEditData.message || "Você não tem permissão para editar esta publicação."}
                </p>
              </div>
              <div className="flex gap-3">
                <Button variant="outline" onClick={() => navigate(-1)}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Voltar
                </Button>
                <Button onClick={() => navigate("/feed")}>
                  <Globe className="h-4 w-4 mr-2" />
                  Ir para Feed
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Verificar se o post existe (caso não encontrado)
  if (isEditMode && !isLoadingPost && !existingPost) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container max-w-7xl py-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="flex flex-col items-center gap-6 text-center">
              <div className="rounded-full bg-yellow-100 p-6">
                <AlertTriangle className="h-12 w-12 text-yellow-600" />
              </div>
              <div className="space-y-2">
                <h2 className="text-2xl font-bold text-gray-900">Post não encontrado</h2>
                <p className="text-gray-600 max-w-md">
                  A publicação que você está tentando editar não foi encontrada ou foi removida.
                </p>
              </div>
              <div className="flex gap-3">
                <Button variant="outline" onClick={() => navigate(-1)}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Voltar
                </Button>
                <Button onClick={() => navigate("/feed")}>
                  <Globe className="h-4 w-4 mr-2" />
                  Ir para Feed
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="flex-1 overflow-auto">
        <div className="container max-w-7xl space-y-6">
        {/* Header Premium com HeroSection */}
        <HeroSection
          title={isEditMode ? "Editar Publicação" : "Criar Nova Publicação"}
          description={isEditMode 
            ? "Edite o conteúdo da sua publicação existente" 
            : "Compartilhe suas ideias, conquistas e descobertas com o cosmos"}
          icon={isEditMode ? Edit : RocketIcon}
          gradientColors="from-indigo-600 via-purple-600 to-pink-600"
          actions={
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => navigate("/post/scheduled")}
                className="bg-white/15 border-white/30 text-white hover:bg-white/25"
              >
                <CalendarClock className="h-4 w-4 mr-2" />
                Posts Agendados
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate(-1)}
                className="bg-white/15 border-white/30 text-white hover:bg-white/25"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Button>
            </div>
          }
        />

        {/* Badge de Conteúdo Gerado */}
        {showGeneratedBadge && generatedData && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-center"
          >
            <Badge 
              variant="outline" 
              className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200 text-purple-700 px-4 py-2 text-sm font-medium"
            >
              <Sparkles className="h-4 w-4 mr-2" />
              Conteúdo gerado: {generatedData.category}
              <Button
                variant="ghost"
                size="sm"
                className="ml-2 h-4 w-4 p-0 text-purple-600 hover:text-purple-800"
                onClick={() => setShowGeneratedBadge(false)}
              >
                ×
              </Button>
            </Badge>
          </motion.div>
        )}

        {/* Alerta do Modo de Edição */}
        {isEditMode && canEditData?.can_edit && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-center"
          >
            <Alert className="max-w-4xl border-orange-200 bg-orange-50">
              <Edit className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <div>
                  <strong>Modo de Edição:</strong> Você está editando uma publicação existente. 
                  {canEditData.plan === 'Grátis' && canEditData.remaining_time_minutes && (
                    <span className="ml-2 text-orange-700">
                      Tempo restante: {Math.floor(canEditData.remaining_time_minutes)}min
                    </span>
                  )}
                </div>
                <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                  {canEditData.plan}
                </Badge>
              </AlertDescription>
            </Alert>
          </motion.div>
        )}

        {/* Layout Premium - Dinâmico baseado no Preview */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          layout
          className={`grid gap-8 transition-all duration-500 ease-in-out ${
            isPreviewMode 
              ? "grid-cols-1 xl:grid-cols-2" 
              : "grid-cols-1 max-w-4xl mx-auto"
          }`}
        >
          {/* Coluna Esquerda - Editor e Configurações */}
          <motion.div variants={columnVariants} className="space-y-6">
            {/* Toolbar de Configurações */}
            <Card className="border-2 border-primary/10 shadow-lg bg-gradient-to-r from-background to-muted/20">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-primary" />
                  Configurações da Publicação
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-3">
                  <Button
                    variant={hasPoll ? "default" : "outline"}
                    size="sm"
                    onClick={togglePoll}
                    disabled={isEditMode}
                    className="flex items-center gap-2"
                    title={isEditMode ? "Não é possível alterar enquetes durante a edição" : ""}
                  >
                    <BarChart2 className="h-4 w-4" />
                    {hasPoll ? "Remover Enquete" : "Adicionar Enquete"}
                  </Button>
                  <Button
                    variant={hasAudio ? "default" : "outline"}
                    size="sm"
                    onClick={toggleAudio}
                    disabled={isEditMode}
                    className="flex items-center gap-2"
                    title={isEditMode ? "Não é possível alterar áudio durante a edição" : ""}
                  >
                    <Mic className="h-4 w-4" />
                    {hasAudio ? "Remover Áudio" : "Adicionar Áudio"}
                  </Button>
                  <Button
                    variant={hasVideo ? "default" : "outline"}
                    size="sm"
                    onClick={toggleVideo}
                    disabled={isEditMode}
                    className="flex items-center gap-2"
                    title={isEditMode ? "Não é possível alterar vídeo durante a edição" : ""}
                  >
                    <Video className="h-4 w-4" />
                    {hasVideo ? "Remover Vídeo" : "Adicionar Vídeo"}
                  </Button>
                  <Button
                    variant={hasPhotoGallery ? "default" : "outline"}
                    size="sm"
                    onClick={togglePhotoGallery}
                    disabled={isEditMode}
                    className="flex items-center gap-2"
                    title={isEditMode ? "Não é possível alterar galeria durante a edição" : ""}
                  >
                    <ImagePlus className="h-4 w-4" />
                    {hasPhotoGallery ? "Remover Galeria" : "Adicionar Galeria"}
                  </Button>
                  <Button
                    variant={isPreviewMode ? "default" : "outline"}
                    size="sm"
                    onClick={() => setIsPreviewMode(!isPreviewMode)}
                    className={`flex items-center gap-2 transition-all duration-300 ${
                      isPreviewMode 
                        ? "bg-green-600 hover:bg-green-700 text-white shadow-lg" 
                        : "border-orange-300 text-orange-600 hover:bg-orange-50"
                    }`}
                  >
                    {isPreviewMode ? (
                      <motion.div
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <Eye className="h-4 w-4" />
                      </motion.div>
                    ) : (
                      <EyeOff className="h-4 w-4" />
                    )}
                                         {isPreviewMode ? "Fechar Preview" : "Abrir Preview"}
                  </Button>
                </div>

                {/* Estatísticas do Conteúdo */}
                <div className="flex flex-wrap gap-4 pt-2 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <BookOpen className="h-4 w-4" />
                    <span>{contentStats.words} palavras</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock4 className="h-4 w-4" />
                    <span>~{contentStats.readTime}min de leitura</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Target className="h-4 w-4" />
                    <span>{contentStats.characters} caracteres</span>
                  </div>
                </div>

                {/* Avisos sobre limitações no modo de edição */}
                {isEditMode && (
                  <Alert className="border-yellow-200 bg-yellow-50 mt-4">
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-1">
                        <p><strong>Limitações durante a edição:</strong></p>
                        {hasAudio && (
                          <p>• <strong>Áudio:</strong> Não pode ser alterado</p>
                        )}
                        {hasPoll && (
                          <p>• <strong>Enquete:</strong> Não pode ser alterada</p>
                        )}
                        {!hasAudio && !hasPoll && (
                          <>
                            <p>• <strong>Áudio:</strong> Não pode ser adicionado</p>
                            <p>• <strong>Enquete:</strong> Não pode ser adicionada</p>
                          </>
                        )}
                        <p className="text-sm text-yellow-700 mt-2">
                          {hasAudio || hasPoll 
                            ? "Para alterar estes elementos, você precisa excluir esta publicação e criar uma nova."
                            : "Para adicionar áudio ou enquete, você precisa excluir esta publicação e criar uma nova."
                          }
                        </p>
                      </div>
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            {/* Editor Principal */}
            <Card className="border-2 border-primary/10 shadow-lg">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Edit className="h-5 w-5 text-primary" />
                  Editor de Conteúdo
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <PostEditor
                  content={content}
                  onChange={setContent}
                  tempPostId={tempPostId}
                  generatedImageToInsert={generatedImageToInsert}
                  onImageInserted={() => setGeneratedImageToInsert(null)}
                  onImageUpload={async (file) => {
                    try {
                      logQueryEvent('EnhancedCreatePost', `Iniciando upload de imagem. Arquivo: ${file.name}, Tamanho: ${file.size} bytes`);

                      const { data: userData, error: userError } =
                        await supabase.auth.getUser();
                      if (userError) throw userError;

                      const { data: profileData, error: profileError } =
                        await supabase
                          .from("profiles")
                          .select("company_id")
                          .eq("id", userData.user.id)
                          .single();
                      if (profileError) throw profileError;

                      if (!profileData?.company_id) {
                        logQueryEvent('EnhancedCreatePost', "Company ID não encontrado no perfil do usuário", { userId: userData.user.id }, "error");
                        throw new Error("Company ID não encontrado no perfil");
                      }

                      const companyId = profileData.company_id;
                      logQueryEvent('EnhancedCreatePost', `Company ID obtido: ${companyId}`);

                      const fileExt = file.name.split(".").pop();
                      const fileName = `${Math.random()
                        .toString(36)
                        .substring(2, 15)}.${fileExt}`;
                      const filePath = `${companyId}/${fileName}`;

                      logQueryEvent('EnhancedCreatePost', `Fazendo upload para: ${filePath}`);

                      const { error: uploadError } = await supabase.storage
                        .from("post-images")
                        .upload(filePath, file);

                      if (uploadError) throw uploadError;

                      const { data } = supabase.storage
                        .from("post-images")
                        .getPublicUrl(filePath);

                      logQueryEvent('EnhancedCreatePost', `Upload concluído. URL: ${data.publicUrl.substring(0, 50)}...`);

                      // Associar a imagem ao post temporário usando o company_id obtido
                      try {
                        const { PostImageService } = await import("@/services/post-images");
                        const imageId = await PostImageService.associateImageWithPost(
                          tempPostId,
                          companyId,
                          data.publicUrl,
                          filePath,
                          file.size
                        );
                        logQueryEvent('EnhancedCreatePost', `Imagem associada ao post temporário: ${tempPostId}, ID da imagem: ${imageId}`);
                      } catch (associationError) {
                        logQueryEvent('EnhancedCreatePost', "Erro ao associar imagem ao post temporário", { error: associationError, tempPostId }, "error");
                        // Não bloquear o upload se a associação falhar - a imagem ainda foi enviada
                      }

                      // Atualizar o state se ainda não foi carregado
                      if (!currentCompanyId) {
                        setCurrentCompanyId(companyId);
                      }

                      return data.publicUrl;
                    } catch (error) {
                      logQueryEvent('EnhancedCreatePost', "Erro no upload da imagem", { error: error.message || error }, "error");
                      
                      // Ainda retornar a URL se conseguiu fazer upload mas falhou na associação
                      if (error.message?.includes("associar")) {
                        // Se já obteve a URL mas falhou só na associação, não é erro crítico
                        return "";
                      }
                      
                      // Para outros erros, mostrar feedback ao usuário
                      errorWithNotification("Erro no upload", {
                        description: "Não foi possível fazer upload da imagem. Tente novamente."
                      });
                      return "";
                    }
                  }}
                />
              </CardContent>
            </Card>

            {/* Seção de Áudio */}
            {hasAudio && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="border-2 border-primary/10 shadow-lg">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Mic className="h-5 w-5 text-primary" />
                      Áudio da Publicação
                      {isEditMode && (
                        <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                          Não editável
                        </Badge>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {isEditMode && audioData ? (
                      <div className="space-y-3">
                        <Alert className="border-yellow-200 bg-yellow-50">
                          <Info className="h-4 w-4" />
                          <AlertDescription>
                            <strong>Áudio não pode ser alterado durante a edição.</strong>
                            <br />Para alterar o áudio, você precisa excluir esta publicação e criar uma nova.
                          </AlertDescription>
                        </Alert>
                        
                        {/* Mostrar player do áudio existente */}
                        <div className="border rounded-lg p-4 bg-gray-50">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                              <Mic className="h-4 w-4 text-orange-600" />
                            </div>
                            <div className="flex-1">
                              <div className="text-sm font-medium text-orange-700">
                                Arquivo de áudio atual
                              </div>
                              <div className="text-xs text-orange-600">
                                Duração: {(() => {
                                  if (!audioData.duration || isNaN(audioData.duration) || !isFinite(audioData.duration)) {
                                    return '0:00';
                                  }
                                  const mins = Math.floor(audioData.duration / 60);
                                  const secs = Math.floor(audioData.duration % 60);
                                  return `${mins}:${secs.toString().padStart(2, '0')}`;
                                })()} 
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <AudioSection 
                        onAudioData={handleAudioDataChange} 
                        audioData={audioData}
                      />
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            )}


            {/* Configuração de Enquete */}
            {hasPoll && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                {isEditMode ? (
                  <Card className="border-primary/20 bg-primary/5">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <BarChart2 className="h-5 w-5" />
                        Enquete da Publicação
                        <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                          Não editável
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <Alert className="border-yellow-200 bg-yellow-50">
                        <Info className="h-4 w-4" />
                        <AlertDescription>
                          <strong>Enquetes não podem ser alteradas durante a edição.</strong>
                          <br />Para alterar a enquete, você precisa excluir esta publicação e criar uma nova.
                        </AlertDescription>
                      </Alert>
                      
                      {/* Mostrar dados da enquete existente se houver */}
                      {existingPost?.has_poll && (
                        <div className="border rounded-lg p-4 bg-gray-50">
                          <div className="flex items-center gap-2 mb-3">
                            <BarChart2 className="h-5 w-5 text-primary" />
                            <span className="font-semibold">Enquete Atual</span>
                          </div>
                          <div className="text-sm text-gray-600">
                            Esta publicação contém uma enquete que será mantida após a edição.
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ) : (
                  <>
                    {isPollEditing ? (
                      <PollCreator onPollChange={handlePollChange} initialPoll={poll} />
                    ) : (
                      <Card className="border-primary/20 bg-primary/5">
                        <CardHeader className="pb-2 flex flex-row items-center justify-between">
                          <CardTitle className="text-lg flex items-center gap-2">
                            <BarChart2 className="h-5 w-5" />
                            Enquete Configurada
                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                              <Check className="mr-1 h-3 w-3" />
                              Pronta
                            </Badge>
                          </CardTitle>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handleEditPoll}
                            className="flex items-center gap-1"
                          >
                            <Edit className="h-3 w-3" />
                            Editar
                          </Button>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          {poll && (
                            <>
                              <div>
                                <h3 className="font-medium mb-1">Pergunta:</h3>
                                <p className="text-lg font-semibold">{poll.question}</p>
                              </div>
                              <div>
                                <h3 className="font-medium mb-2">
                                  Opções ({poll.options.length}):
                                </h3>
                                <ul className="space-y-1 list-disc pl-5">
                                  {poll.options.map((option) => (
                                    <li key={option.id}>{option.text}</li>
                                  ))}
                                </ul>
                              </div>
                              <div className="flex flex-wrap gap-4 pt-2 text-sm text-muted-foreground">
                                <div>
                                  <span className="font-medium">Duração:</span>{" "}
                                  {formatPollDuration(poll.duration)}
                                </div>
                                <div>
                                  <span className="font-medium">
                                    Múltiplas respostas:
                                  </span>{" "}
                                  {poll.allowMultipleAnswers ? "Sim" : "Não"}
                                </div>
                              </div>
                            </>
                          )}
                        </CardContent>
                      </Card>
                    )}
                  </>
                )}
              </motion.div>
            )}

            {/* Seletor/Exibição de Audiência */}
            {isEditMode ? (
              <AudienceDisplay 
                audience={audience}
                className="border-primary/20 bg-primary/5"
              />
            ) : (
              <AudienceSelector
                value={audience}
                onChange={setAudience}
                className="border-primary/20 bg-primary/5"
              />
            )}
          </motion.div>

          {/* Coluna Direita - Preview Dinâmico */}
          {isPreviewMode && (
            <motion.div 
              variants={previewVariants} 
              className="space-y-6 relative"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 50 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
              {/* Seção de Vídeo - Exibida acima do preview quando ativo */}
              {hasVideo && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card className="border-2 border-primary/10 shadow-lg">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Video className="h-5 w-5 text-primary" />
                        Vídeo da Publicação
                        {isEditMode && (
                          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                            Não editável
                          </Badge>
                        )}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {isEditMode && videoData ? (
                        <div className="space-y-3">
                          <Alert className="border-yellow-200 bg-yellow-50">
                            <Info className="h-4 w-4" />
                            <AlertDescription>
                              <strong>Vídeo não pode ser alterado durante a edição.</strong>
                              <br />Para alterar o vídeo, você precisa excluir esta publicação e criar uma nova.
                            </AlertDescription>
                          </Alert>
                          
                          {/* Mostrar player do vídeo existente */}
                          <div className="border rounded-lg p-4 bg-gray-50">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <Video className="h-4 w-4 text-blue-600" />
                              </div>
                              <div className="flex-1">
                                <div className="text-sm font-medium text-blue-700">
                                  Arquivo de vídeo atual
                                </div>
                                <div className="text-xs text-blue-600">
                                  Duração: {(() => {
                                    if (!videoData.duration || isNaN(videoData.duration) || !isFinite(videoData.duration)) {
                                      return '0:00';
                                    }
                                    const mins = Math.floor(videoData.duration / 60);
                                    const secs = Math.floor(videoData.duration % 60);
                                    return `${mins}:${secs.toString().padStart(2, '0')}`;
                                  })()} 
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <VideoSection 
                          onVideoData={handleVideoDataChange} 
                          videoData={videoData}
                        />
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              )}

              {/* Seção de Galeria de Fotos */}
              {hasPhotoGallery && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card className="border-2 border-primary/10 shadow-lg">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <ImagePlus className="h-5 w-5 text-primary" />
                        Galeria de Fotos
                        {isEditMode && (
                          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                            Não editável
                          </Badge>
                        )}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {isEditMode ? (
                        <div className="space-y-3">
                          <Alert className="border-yellow-200 bg-yellow-50">
                            <Info className="h-4 w-4" />
                            <AlertDescription>
                              <strong>Galeria de fotos não pode ser alterada durante a edição.</strong>
                              <br />Para alterar a galeria, você precisa excluir esta publicação e criar uma nova.
                            </AlertDescription>
                          </Alert>
                          
                          {/* Mostrar galeria existente se houver */}
                          {photosData.length > 0 && (
                            <div className="border rounded-lg p-4 bg-gray-50">
                              <div className="flex items-center gap-3 mb-3">
                                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                  <ImagePlus className="h-4 w-4 text-green-600" />
                                </div>
                                <div className="flex-1">
                                  <div className="text-sm font-medium text-green-700">
                                    Galeria de fotos atual
                                  </div>
                                  <div className="text-xs text-green-600">
                                    {photosData.length} foto(s) anexada(s)
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ) : (
                        <PhotoGalleryUploader
                          onPhotosChange={setPhotosData}
                          photos={photosData}
                          tempPostId={tempPostId}
                        />
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              )}

              {/* Preview Card */}
              <div className="sticky top-6 space-y-4">
                <Card className="border-2 border-primary/10 shadow-xl bg-gradient-to-b from-background to-muted/10">
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Eye className="h-5 w-5 text-primary" />
                    Preview em Tempo Real
                    {isPreviewMode && (
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                          Ativo
                        </Badge>
                      </motion.div>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Preview do Conteúdo */}
                  <div className="border rounded-lg p-4 bg-background min-h-[300px]">
                    <div
                      className="tiptap prose prose-slate dark:prose-invert max-w-none focus:outline-none min-h-[200px] px-4 py-4 break-words overflow-wrap-anywhere"
                      dangerouslySetInnerHTML={{ __html: previewContent }}
                    />
                  </div>

                  {/* Preview da Enquete */}
                  {hasPoll && poll && !isPollEditing && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="border rounded-lg p-4 bg-gradient-to-r from-primary/5 to-primary/10"
                    >
                      <div className="flex items-center gap-2 mb-3">
                        <BarChart2 className="h-5 w-5 text-primary" />
                        <span className="font-semibold">Enquete</span>
                      </div>
                      <h3 className="font-medium mb-3">{poll.question}</h3>
                      <div className="space-y-2">
                        {poll.options.map((option, index) => (
                          <div key={option.id} className="flex items-center gap-2 p-2 border rounded bg-background">
                            <div className="w-4 h-4 border border-primary rounded-full" />
                            <span>{option.text}</span>
                          </div>
                        ))}
                      </div>
                      <div className="text-sm text-muted-foreground mt-3">
                        Duração: {formatPollDuration(poll.duration)} • 
                        {poll.allowMultipleAnswers ? " Múltiplas respostas" : " Resposta única"}
                      </div>
                    </motion.div>
                  )}

                  {/* Preview do Vídeo - Mantido para informação na área de preview */}
                  {hasVideo && videoData && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="border rounded-lg p-4 bg-gradient-to-r from-blue-50/30 to-sky-50/30 border-blue-200"
                    >
                      <div className="flex items-center gap-2 mb-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-blue-100"
                          onClick={() => {
                            // Simular click no botão play do componente principal
                            const videoSection = document.querySelector('video');
                            if (videoSection) {
                              if (videoSection.paused) {
                                videoSection.play();
                              } else {
                                videoSection.pause();
                              }
                            }
                          }}
                        >
                          <Play className="h-4 w-4 text-blue-600" />
                        </Button>
                        <span className="font-semibold text-blue-700">Vídeo Anexado</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <Video className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-blue-700">
                            Arquivo de vídeo
                          </div>
                          <div className="text-xs text-blue-600">
                            Duração: {(() => {
                              if (!videoData.duration || isNaN(videoData.duration) || !isFinite(videoData.duration)) {
                                return '0:00';
                              }
                              const mins = Math.floor(videoData.duration / 60);
                              const secs = Math.floor(videoData.duration % 60);
                              return `${mins}:${secs.toString().padStart(2, '0')}`;
                            })()}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {/* Preview do Áudio */}
                  {hasAudio && audioData && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="border rounded-lg p-4 bg-gradient-to-r from-orange-50/30 to-amber-50/30 border-orange-200"
                    >
                      <div className="flex items-center gap-2 mb-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-orange-100"
                          onClick={() => {
                            // Simular click no botão play do componente principal
                            const audioSection = document.querySelector('audio');
                            if (audioSection) {
                              if (audioSection.paused) {
                                audioSection.play();
                              } else {
                                audioSection.pause();
                              }
                            }
                          }}
                        >
                          <Play className="h-4 w-4 text-orange-600" />
                        </Button>
                        <span className="font-semibold text-orange-700">Áudio Anexado</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                          <Mic className="h-4 w-4 text-orange-600" />
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-orange-700">
                            Arquivo de áudio
                          </div>
                          <div className="text-xs text-orange-600">
                            Duração: {(() => {
                              if (!audioData.duration || isNaN(audioData.duration) || !isFinite(audioData.duration)) {
                                return '0:00';
                              }
                              const mins = Math.floor(audioData.duration / 60);
                              const secs = Math.floor(audioData.duration % 60);
                              return `${mins}:${secs.toString().padStart(2, '0')}`;
                            })()}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {/* Preview da Galeria de Fotos */}
                  {hasPhotoGallery && photosData.length > 0 && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="border rounded-lg p-4 bg-gradient-to-r from-purple-50/30 to-pink-50/30 border-purple-200"
                    >
                      <div className="flex items-center gap-2 mb-3">
                        <ImagePlus className="h-5 w-5 text-purple-600" />
                        <span className="font-semibold text-purple-700">Galeria de Fotos</span>
                        <Badge variant="outline" className="text-xs">
                          {photosData.length} foto{photosData.length !== 1 ? 's' : ''}
                        </Badge>
                      </div>
                      <PostPhotoGallery 
                        images={photosData.map((photo, index) => ({
                          id: photo.id,
                          image_url: photo.url,
                          storage_path: '',
                          size: 0,
                          created_at: new Date().toISOString()
                        }))}
                        className="max-h-64"
                      />
                    </motion.div>
                  )}

                  {/* Preview da Audiência */}
                  <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
                    {(() => {
                      const AudienceIcon = getAudienceIcon();
                      return <AudienceIcon className="h-4 w-4 text-muted-foreground" />;
                    })()}
                    <span className="text-sm text-muted-foreground">
                      <span className="font-medium">Audiência:</span> {getAudienceText()}
                    </span>
                  </div>

                  {/* Estatísticas Visuais */}
                  <div className="grid grid-cols-3 gap-4 pt-4 border-t">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">{contentStats.words}</div>
                      <div className="text-xs text-muted-foreground">Palavras</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">{contentStats.readTime}</div>
                      <div className="text-xs text-muted-foreground">Min leitura</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {contentStats.characters > 0 ? "⭐" : "📝"}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {contentStats.characters > 0 ? "Pronto" : "Editando"}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Análises Inteligentes */}
              <SmartFeatures editor={null} content={content} />
            </div>
            </motion.div>
          )}
        </motion.div>

        {/* Painel de Publicação - Full Width */}
        {isPostReady && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Alert className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10 mb-16">
              <AlertDescription className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                    >
                      <Zap className="h-6 w-6 text-primary" />
                    </motion.div>
                    <span className="text-lg font-semibold">
                      {hasPoll && hasAudio && hasVideo
                        ? "Sua publicação com enquete, áudio e vídeo está pronta para decolar!"
                        : hasPoll && hasAudio
                        ? "Sua publicação com enquete e áudio está pronta para decolar!"
                        : hasPoll && hasVideo
                        ? "Sua publicação com enquete e vídeo está pronta para decolar!"
                        : hasAudio && hasVideo
                        ? "Sua publicação com áudio e vídeo está pronta para decolar!"
                        : hasPoll
                        ? "Sua publicação com enquete está pronta para decolar!"
                        : hasAudio
                        ? "Sua publicação com áudio está pronta para decolar!"
                        : hasVideo
                        ? "Sua publicação com vídeo está pronta para decolar!"
                        : "Seu post está pronto para decolar!"}
                    </span>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    <Star className="h-4 w-4 mr-1" />
                    Pronto para publicar
                  </Badge>
                </div>

                <Separator />

                <div className="space-y-4">
                  {!isEditMode && (
                    <RadioGroup
                      value={publishMode}
                      onValueChange={(value) => setPublishMode(value as PublishMode)}
                      className="flex flex-col space-y-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="now" id="publish-now" />
                        <Label htmlFor="publish-now" className="flex items-center gap-2 cursor-pointer">
                          <Send className="h-4 w-4" />
                          Publicar agora
                        </Label>
                      </div>

                      <PostScheduleOption />
                    </RadioGroup>
                  )}

                  {publishMode === "scheduled" && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      className="flex flex-wrap gap-4 p-4 bg-muted/30 rounded-lg"
                    >
                      <div>
                        <Label htmlFor="scheduled-date" className="block mb-2">
                          Data
                        </Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              id="scheduled-date"
                              variant="outline"
                              className={cn(
                                "w-[240px] justify-start text-left font-normal",
                                !scheduledDate && "text-muted-foreground"
                              )}
                            >
                              <Calendar className="mr-2 h-4 w-4" />
                              {scheduledDate ? (
                                format(scheduledDate, "PPP", { locale: ptBR })
                              ) : (
                                <span>Selecione uma data</span>
                              )}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <CalendarComponent
                              mode="single"
                              selected={scheduledDate}
                              onSelect={setScheduledDate}
                              initialFocus
                              disabled={(date) => {
                                const today = new Date();
                                today.setHours(0, 0, 0, 0);
                                return date < today;
                              }}
                            />
                          </PopoverContent>
                        </Popover>
                      </div>

                      <div>
                        <Label htmlFor="scheduled-time" className="block mb-2">
                          Horário
                        </Label>
                        <div className="flex items-center">
                          <Clock className="mr-2 h-4 w-4" />
                          <Input
                            id="scheduled-time"
                            type="time"
                            value={scheduledTime}
                            onChange={(e) => setScheduledTime(e.target.value)}
                            className="w-[120px]"
                          />
                        </div>
                      </div>
                    </motion.div>
                  )}

                  <div className="flex justify-end">
                    <Button
                      onClick={handleCreatePost}
                      disabled={isEditMode ? updatePostMutation.isPending : createPostMutation.isPending}
                      size="lg"
                      className="gap-2 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
                    >
                      {(isEditMode ? updatePostMutation.isPending : createPostMutation.isPending) ? (
                        <>
                          <Loader2 className="h-5 w-5 animate-spin" />
                          {isEditMode ? "Salvando..." : (publishMode === "scheduled" ? "Agendando..." : "Publicando...")}
                        </>
                      ) : (
                        <>
                          {isEditMode ? (
                            <>
                              <Edit className="h-5 w-5" />
                              Salvar Edição
                            </>
                          ) : publishMode === "scheduled" ? (
                            <>
                              <Calendar className="h-5 w-5" />
                              Agendar Publicação
                            </>
                          ) : (
                            <>
                              <Send className="h-5 w-5" />
                              Publicar Agora
                            </>
                          )}
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          </motion.div>
        )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedCreatePost;
