import { useEffect, useState, useCallback, useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { Loader2 } from "lucide-react";
// Importar hooks de queries que precisam ser pré-carregados
import { useStardustBalance } from "@/lib/query/hooks/useStardust";
import { useEnhancedCurrentUser } from "@/lib/query/hooks/useUsers";
import { useActiveUsers } from "@/lib/query/hooks/useActiveUsers";
import { QueryKeys } from "@/lib/query/queryKeys";
// Adicionar import para o hook useUserLevel
import { useUserLevel } from "@/lib/query/hooks/useUserLevel";
// Importar o AuthManager (substituindo authStore)
import { authManager } from '@/lib/auth/AuthManager';
// Adicionar mais imports para outras queries importantes
import { useQueryClient } from "@tanstack/react-query";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
// Importar o contexto de PostIt para pré-carregamento
import { usePostIt } from "@/contexts/PostItContext";
// Importar hooks para pré-carregamento do Feed
import { usePostsFeed } from "@/lib/query/hooks/usePosts";
import { useTrendingPosts } from "@/lib/query/hooks/useTrendingPosts";

import { usePortletSettings } from "@/hooks/usePortletSettings";
import { useWidgetSettings } from "@/lib/query/hooks/useWidgetSettings"; // Corrigido o caminho do import
import { useUserRoles } from "@/lib/query/hooks/useUserRoles";
import { useIsFeatureEnabled } from "@/lib/query/hooks/useFeatureDetails";
import { useUnreadNotificationsCount } from "@/lib/query/hooks/useNotifications";
import { useUpcomingEvents } from "@/lib/query/hooks/useEvents";

import { useFeedStats } from "@/lib/query/hooks/useFeedStats"; // Adicionado import
import { useCommonPermissions } from "@/lib/query/hooks/useCommonPermissions"; // Adicionado para pré-carregamento de permissões
import { useTimelineLimits } from "@/hooks/timeline/useTimelineLimits"; // Para pré-carregamento da timeline
import { usePendingObligations } from "@/hooks/mandatory-reading/use-pending-obligations"; // Para navegação
import { useKnowledgeHubLimits } from "@/hooks/knowledge/useKnowledgeHubLimits"; // Para Knowledge Hub
import { useBirthdaysThisMonth } from "@/hooks/useBirthdays"; // Para dashboards
import { useTimelineNotifications } from "@/lib/query/hooks/useTimelineNotifications"; // Para timeline

// Frases de carregamento para exibir em sequência
const loadingPhrases = [
  "Preparando seu universo digital...",
  "Carregando as estrelas do Cosmos...",
  "Sincronizando seus dados...",
  "Configurando suas permissões...",
  "Ativando seu espaço de trabalho...",
  "Configurando sua experiência...",
  "Quase lá...",
];

/**
 * Componente de carregamento que pré-carrega queries importantes
 * antes de redirecionar o usuário para o destino
 * 
 * OTIMIZAÇÕES 2025:
 * - Pré-carregamento de permissões comuns para performance
 * - Cache otimizado para reduzir queries redundantes
 * - Loading inteligente com progresso detalhado
 * 
 * <AUTHOR> Internet 2025
 */
export default function Loading() {
  const location = useLocation();
  const navigate = useNavigate();
  const [currentPhrase, setCurrentPhrase] = useState(0);
  const destination = location.state?.destination || "/feed";
  const forceCompanyId = location.state?.forceCompanyId; // Company ID conhecido do registro
  const [progress, setProgress] = useState(0);
  const [finalPhase, setFinalPhase] = useState(false);
  
  // 🚀 BYPASS INTELIGENTE - Verificar se pode navegar imediatamente (AuthManager)
  const [authState, setAuthState] = useState(() => authManager.getState());
  
  // Subscription ao AuthManager para atualizações em tempo real
  useEffect(() => {
    const unsubscribe = authManager.subscribe((newState) => {
      setAuthState(newState);
    });
    return unsubscribe;
  }, []);
  
  // ⚡ QUERY CLIENT - DEVE vir ANTES do canBypassLoading
  const queryClient = useQueryClient();
  
  // 🎯 CONDIÇÕES PARA BYPASS IMEDIATO - AuthManager (mais rigorosas)
  const canBypassLoading = 
    !authState.isLoading && // AuthManager não está carregando
    authState.isAuthenticated && // Usuário autenticado
    !!authState.user?.id && // Tem user válido
    (!!authState.company_id || !!forceCompanyId) && // Tem company_id válido OU forceCompanyId
    destination && // Tem destino definido
    // 🔥 VERIFICAÇÃO CRÍTICA: React Query deve ter dados em cache
    queryClient.getQueryData(['current-user']) && // Profile já carregado
    queryClient.getQueryData(['stardust-balance', authState.user?.id]) && // Stardust em cache
    // 🎯 EVITAR BYPASS LOGO APÓS LOGIN - verificar se há dados básicos
    !!profile?.id; // Profile do hook atual também deve existir

  // Estrutura para rastrear o carregamento de cada query - COMPLETA para cache
  const [loadedQueries, setLoadedQueries] = useState<Record<string, boolean>>({
    authManagerReady: false,
    stardust: false,
    user: false,
    userLevel: false,
    feedPosts: false,
    portlets: false,
    userRoles: false,
    featureFlags: false,
    notifications: false,
    upcomingEvents: false,
    trendingPosts: false,
    activeUsers: false,
    widgetSettings: false,
    feedStats: false,
    commonPermissions: false, // Novo: permissões comuns
    timelineLimits: false, // Novo: limites da timeline
    pendingObligations: false, // Novo: obrigações pendentes
    knowledgeHubLimits: false, // Novo: limites do Knowledge Hub
    birthdays: false, // Novo: aniversários
    timelineNotifications: false, // Novo: notificações da timeline
  });
  
  // Contador total de queries para carregar
  const totalQueries = useMemo(() => Object.keys(loadedQueries).length, []);
  
  // Pré-carregar o saldo de Stardust
  const { isLoading: isLoadingStardust } = useStardustBalance();
  
  // Pré-carregar dados do usuário
  const { isLoading: isLoadingUser, profile } = useEnhancedCurrentUser();

  // Pré-carregar nível do usuário usando o hook dedicado
  const { isLoading: isLoadingUserLevel } = useUserLevel();
  
  // Note: Post-its são carregados via contexto após o Loading, não aqui
  
  // Pré-carregar posts do feed com filtros padrão (matching Feed.tsx)
  const defaultFeedFilters = useMemo(() => ({
    sortBy: "newest" as const,
    status: "all" as const,
    audience: "all" as const,
  }), []);
  
  const feedPostsQuery = usePostsFeed(defaultFeedFilters, {
    enabled: loadedQueries.authManagerReady && !!authState.company_id,
    staleTime: Infinity // Manter em cache para reutilização
  });
  const isLoadingFeedPosts = feedPostsQuery.isLoading || feedPostsQuery.isFetchingNextPage;
  
  // Pré-carregar configurações de portlets (usePortletSettings)
  const { isLoading: isLoadingPortlets } = usePortletSettings();
  
  // Pré-carregar permissões de usuário (para o menu)
  const { isLoading: isLoadingUserRoles } = useUserRoles();
  
  // Pré-carregar feature flags (para o menu)
  const { isLoading: isLoadingCustomization } = useIsFeatureEnabled('feature_customization');
  const { isLoading: isLoadingReports } = useIsFeatureEnabled('feature_reports');
  const { isLoading: isLoadingTenantManagement } = useIsFeatureEnabled('feature_tenant_management');
  
  // Pré-carregar contagem de notificações não lidas (para o menu)
  const { isLoading: isLoadingNotifications } = useUnreadNotificationsCount();

  // Queries condicionais - só carregam quando AuthManager está pronto
  const { isLoading: isLoadingUpcomingEvents } = useUpcomingEvents({ 
    limit: 3, 
    scope: 'all', 
    days_ahead: 7, 
    include_personal: true, 
    enabled: loadedQueries.authManagerReady && !!authState.user?.id && !!authState.company_id, 
    staleTime: Infinity 
  });
  const { data: trendingPostsData, isLoading: isLoadingTrendingPosts, isError: isTrendingPostsError, isSuccess: isTrendingPostsSuccess, status: trendingPostsStatus } = useTrendingPosts(3, {
    enabled: loadedQueries.authManagerReady && !!authState.company_id,
    staleTime: Infinity // Manter em cache para reutilização
  });
  const { isLoading: isLoadingActiveUsers } = useActiveUsers({ 
    activeThresholdMinutes: 15, 
    enabled: loadedQueries.authManagerReady && !!authState.company_id 
  });
  const { isLoading: isLoadingWidgetSettings } = useWidgetSettings({
    enabled: loadedQueries.authManagerReady && !!authState.user?.id 
  });
  const { isLoading: isLoadingFeedStats } = useFeedStats({
    enabled: loadedQueries.authManagerReady && !!authState.company_id 
  });
  
  // Pré-carregar permissões comuns - OTIMIZAÇÃO DE PERFORMANCE
  const { isLoading: isLoadingCommonPermissions, loadedCount: permissionsLoadedCount, totalCount: permissionsTotalCount } = useCommonPermissions(
    loadedQueries.authManagerReady && !!authState.user?.id && !!authState.company_id
  );
  
  // Pré-carregar limites da timeline - OTIMIZAÇÃO DE PERFORMANCE
  const { isLoading: isLoadingTimelineLimits, limits: preloadedTimelineLimits, isEnabled: timelineEnabled } = useTimelineLimits();
  
  // Pré-carregar obrigações pendentes - OTIMIZAÇÃO DE PERFORMANCE
  const { isLoading: isLoadingPendingObligations, data: pendingObligationsData } = usePendingObligations();
  
  // Pré-carregar limites do Knowledge Hub - OTIMIZAÇÃO DE PERFORMANCE
  const { isLoading: isLoadingKnowledgeHubLimits, limits: knowledgeHubLimits } = useKnowledgeHubLimits();
  
  // Pré-carregar aniversários - OTIMIZAÇÃO DE PERFORMANCE
  const { isLoading: isLoadingBirthdays, data: birthdaysData } = useBirthdaysThisMonth();
  
  // Pré-carregar notificações da timeline - OTIMIZAÇÃO DE PERFORMANCE
  // Usar filtros vazios para carregar todas as notificações (mesmo que a timeline no TimelineView.tsx)
  const { isLoading: isLoadingTimelineNotifications, data: timelineNotificationsData } = useTimelineNotifications(undefined, {
    enabled: loadedQueries.authManagerReady && !!authState.user?.id,
    staleTime: 2 * 60 * 1000, // 2 minutos de cache para o pré-carregamento
    gcTime: 10 * 60 * 1000 // 10 minutos na memória
  });

  // Função para marcar uma query como carregada
  const markQueryAsLoaded = useCallback((queryName: string) => {
    setLoadedQueries(prev => ({
      ...prev,
      [queryName]: true
    }));
  }, []);

  // 🚀 BYPASS IMEDIATO - useEffect para navegação (DEVE vir ANTES do early return)
  useEffect(() => {
    if (canBypassLoading) {
      logQueryEvent('Loading', '⚡ BYPASS ATIVADO - Navegação imediata para usuário com cache válido', {
        destination,
        userId: authState.user?.id,
        company_id: authState.company_id,
        forceCompanyId: forceCompanyId,
        effectiveCompanyId: authState.company_id || forceCompanyId,
        authManagerState: authState,
        hasCurrentUserCache: !!queryClient.getQueryData(['current-user']),
        hasStardustCache: !!queryClient.getQueryData(['stardust-balance', authState.user?.id]),
        hasProfileFromHook: !!profile?.id
      });
      
      // Navegar imediatamente sem loading
      sessionStorage.setItem('cosmosAppFullyLoaded', 'true');
      logQueryEvent("Loading", "Sinalizador cosmosAppFullyLoaded=true definido no sessionStorage (canBypassLoading).");
      navigate(destination, { replace: true });
      return;
    } else {
      // Log detalhado quando bypass NÃO é ativado
      logQueryEvent('Loading', '🔄 BYPASS NEGADO - Executando loading completo', {
        destination,
        forceCompanyId: forceCompanyId,
        authManagerState: authState,
        hasUserId: !!authState.user?.id,
        hasCompanyId: !!authState.company_id,
        hasForceCompanyId: !!forceCompanyId,
        hasEffectiveCompanyId: !!(authState.company_id || forceCompanyId),
        hasDestination: !!destination,
        hasCurrentUserCache: !!queryClient.getQueryData(['current-user']),
        hasStardustCache: !!queryClient.getQueryData(['stardust-balance', authState.user?.id]),
        hasProfileFromHook: !!profile?.id,
        reasonForLoading: authState.isLoading ? 'AuthManager loading' :
                         !authState.isAuthenticated ? 'Not authenticated' :
                         !authState.user?.id ? 'No user ID' :
                         !(authState.company_id || forceCompanyId) ? 'No company ID (checked both authState and forceCompanyId)' :
                         !destination ? 'No destination' :
                         !queryClient.getQueryData(['current-user']) ? 'No current-user cache' :
                         !queryClient.getQueryData(['stardust-balance', authState.user?.id]) ? 'No stardust cache' :
                         !profile?.id ? 'No profile from hook' : 'Unknown'
      });
    }
  }, [canBypassLoading, destination, navigate, authState.user?.id, authState.company_id, forceCompanyId, queryClient, profile?.id, authState.isLoading, authState.isAuthenticated]);

  // 🎯 EARLY RETURN - Se bypass está ativo, não renderizar loading
  // IMPORTANTE: Deve vir DEPOIS de todos os hooks!
  if (canBypassLoading) {
    return null; // Não renderizar nada, navegação já iniciada
  }

  // Inicializar o AuthManager uma única vez
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        await authManager.initialize();
        logQueryEvent('Loading', 'AuthManager inicializado com sucesso');
      } catch (error) {
        logQueryEvent('Loading', 'Erro ao inicializar AuthManager', error, 'error');
      }
    };

    initializeAuth();
  }, []);

  // Monitorar o estado de carregamento do AuthManager
  useEffect(() => {
    // Se o AuthManager não está mais carregando E está autenticado
    // E ainda não marcamos como pronto, então marcamos.
    if (!authState.isLoading && authState.isAuthenticated && !loadedQueries.authManagerReady) {
      logQueryEvent('LoadingPage', 'Cache check before navigate (FeedSidebar Data):', { 
          trendingPosts: queryClient.getQueryData(QueryKeys.posts.trending(3)), 
                      activeUsers: queryClient.getQueryData(QueryKeys.users.active(15)),
          upcomingEvents: queryClient.getQueryData(QueryKeys.calendar.upcomingEvents({ 
            limit: 3, 
            scope: 'all', 
            days_ahead: 7, 
            include_personal: true 
          }))
        });
      logQueryEvent('Loading', 'AuthManager reportou estar pronto.', { 
        isLoading: authState.isLoading, 
        isAuthenticated: authState.isAuthenticated,
        hasUser: !!authState.user,
        hasCompanyId: !!authState.company_id
      });
      setLoadedQueries(prev => ({ ...prev, authManagerReady: true }));
    }
  }, [authState.isLoading, authState.isAuthenticated, authState.user, authState.company_id, loadedQueries.authManagerReady]);
  
  // Mudar a frase a cada 2 segundos
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentPhrase((prev) => (prev + 1) % loadingPhrases.length);
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  // Atualizar o estado quando o Stardust carregar
  useEffect(() => {
    if (loadedQueries.authManagerReady && profile?.id && !isLoadingStardust && !loadedQueries.stardust) {
      logQueryEvent("Loading", "Saldo de Stardust carregado e marcado");
      markQueryAsLoaded("stardust");
    }
  }, [loadedQueries.authManagerReady, profile?.id, isLoadingStardust, loadedQueries.stardust, markQueryAsLoaded]);
  
  useEffect(() => {
    // isLoadingUser se torna false quando 'profile' está disponível ou houve um erro
    if (!isLoadingUser && !loadedQueries.user) { 
      logQueryEvent("Loading", "Dados do usuário (profile) carregados e marcados", { profileIsAvailable: !!profile });
      markQueryAsLoaded('user');
    }
  }, [isLoadingUser, profile, loadedQueries.user, markQueryAsLoaded]);
  
  useEffect(() => {
    if (loadedQueries.authManagerReady && profile?.id && !isLoadingUserLevel && !loadedQueries.userLevel) {
      logQueryEvent("Loading", "Nível do usuário carregado e marcado");
      markQueryAsLoaded('userLevel');
    }
  }, [loadedQueries.authManagerReady, profile?.id, isLoadingUserLevel, loadedQueries.userLevel, markQueryAsLoaded]);
  
  // Post-its são carregados via contexto após o Loading, não aqui
  
  // Atualizar o estado de carregamento dos posts do feed
  useEffect(() => {
    if (
      loadedQueries.authManagerReady &&
      authState.company_id &&
      !isLoadingFeedPosts &&
      !loadedQueries.feedPosts
    ) {
      const feedQueryKey = QueryKeys.posts.feed(defaultFeedFilters);
      const cacheState = queryClient.getQueryState(feedQueryKey);
      
      logQueryEvent("Loading", "Posts do feed pré-carregados e marcados", {
        pagesCount: feedPostsQuery.data?.pages?.length || 0,
        firstPagePostsCount: feedPostsQuery.data?.pages[0]?.posts?.length || 0,
        queryKey: JSON.stringify(feedQueryKey),
        cacheState: cacheState ? {
          status: cacheState.status,
          dataUpdatedAt: cacheState.dataUpdatedAt,
          hasData: !!cacheState.data
        } : 'no cache state',
        filters: defaultFeedFilters,
        feedQueryStatus: feedPostsQuery.status,
        feedQuerySuccess: feedPostsQuery.isSuccess
      });
      markQueryAsLoaded('feedPosts');
    }
  }, [
    loadedQueries.authManagerReady,
    authState.company_id,
    isLoadingFeedPosts,
    loadedQueries.feedPosts,
    feedPostsQuery.data?.pages,
    feedPostsQuery.status,
    feedPostsQuery.isSuccess,
    defaultFeedFilters,
    queryClient,
    markQueryAsLoaded,
  ]);
  
  // Atualizar o estado de carregamento dos portlets (usePortletSettings)
  useEffect(() => {
    if (loadedQueries.authManagerReady && profile?.id && !isLoadingPortlets && !loadedQueries.portlets) {
      logQueryEvent("Loading", "Configurações de portlets (usePortletSettings) pré-carregadas e marcadas");
      markQueryAsLoaded('portlets');
    }
  }, [loadedQueries.authManagerReady, profile?.id, isLoadingPortlets, loadedQueries.portlets, markQueryAsLoaded]);
  
  // Atualizar o estado de carregamento das permissões de usuário
  useEffect(() => {
    if (loadedQueries.authManagerReady && profile?.id && !isLoadingUserRoles && !loadedQueries.userRoles) {
      logQueryEvent("Loading", "Permissões de usuário (roles) pré-carregadas e marcadas");
      markQueryAsLoaded('userRoles');
    }
  }, [loadedQueries.authManagerReady, profile?.id, isLoadingUserRoles, loadedQueries.userRoles, markQueryAsLoaded]);
  
  // Atualizar o estado de carregamento das feature flags
  useEffect(() => {
    const allFeatureFlagsHooksLoaded = !isLoadingCustomization && !isLoadingReports && !isLoadingTenantManagement;
    if (loadedQueries.authManagerReady && profile?.id && authState.company_id && allFeatureFlagsHooksLoaded && !loadedQueries.featureFlags) {
      logQueryEvent("Loading", "Feature flags pré-carregadas e marcadas");
      markQueryAsLoaded('featureFlags');
    }
  }, [
    loadedQueries.authManagerReady, profile?.id, authState.company_id,
    isLoadingCustomization, isLoadingReports, isLoadingTenantManagement,
    loadedQueries.featureFlags, markQueryAsLoaded
  ]);
  
  // Atualizar o estado de carregamento das notificações
  useEffect(() => {
    if (loadedQueries.authManagerReady && profile?.id && !isLoadingNotifications && !loadedQueries.notifications) {
      logQueryEvent("Loading", "Contagem de notificações pré-carregada e marcada");
      markQueryAsLoaded('notifications');
    }
  }, [loadedQueries.authManagerReady, profile?.id, isLoadingNotifications, loadedQueries.notifications, markQueryAsLoaded]);

  // useEffects para as novas queries adicionadas
  useEffect(() => {
    if (loadedQueries.authManagerReady && profile?.id && authState.company_id && !isLoadingUpcomingEvents && !loadedQueries.upcomingEvents) {
      logQueryEvent("Loading", "Próximos eventos pré-carregados e marcados");
      markQueryAsLoaded('upcomingEvents');
    }
  }, [loadedQueries.authManagerReady, profile?.id, authState.company_id, isLoadingUpcomingEvents, loadedQueries.upcomingEvents, markQueryAsLoaded]);

  useEffect(() => {
    if (loadedQueries.authManagerReady && authState.company_id && !isLoadingTrendingPosts && !loadedQueries.trendingPosts) {
      logQueryEvent("Loading", "Posts em destaque pré-carregados e marcados", {
        status: trendingPostsStatus,
        isSuccess: isTrendingPostsSuccess,
        hasData: !!trendingPostsData,
        dataCount: trendingPostsData?.length || 0
      });
      markQueryAsLoaded('trendingPosts');
    }
  }, [loadedQueries.authManagerReady, authState.company_id, isLoadingTrendingPosts, loadedQueries.trendingPosts, trendingPostsStatus, isTrendingPostsSuccess, trendingPostsData, markQueryAsLoaded]);

  useEffect(() => {
    if (loadedQueries.authManagerReady && profile?.id && authState.company_id && !isLoadingActiveUsers && !loadedQueries.activeUsers) {
      logQueryEvent("Loading", "Usuários ativos pré-carregados e marcados");
      markQueryAsLoaded('activeUsers');
    }
  }, [loadedQueries.authManagerReady, profile?.id, authState.company_id, isLoadingActiveUsers, loadedQueries.activeUsers, markQueryAsLoaded]);

  useEffect(() => {
    if (loadedQueries.authManagerReady && profile?.id && authState.company_id && !isLoadingWidgetSettings && !loadedQueries.widgetSettings) {
      logQueryEvent("Loading", "Configurações de widgets pré-carregadas e marcadas");
      markQueryAsLoaded('widgetSettings');
    }
  }, [loadedQueries.authManagerReady, profile?.id, authState.company_id, isLoadingWidgetSettings, loadedQueries.widgetSettings, markQueryAsLoaded]);

  useEffect(() => {
    if (loadedQueries.authManagerReady && profile?.id && authState.company_id && !isLoadingFeedStats && !loadedQueries.feedStats) {
      logQueryEvent("Loading", "Estatísticas do Feed pré-carregadas e marcadas");
      markQueryAsLoaded('feedStats');
    }
  }, [loadedQueries.authManagerReady, profile?.id, authState.company_id, isLoadingFeedStats, loadedQueries.feedStats, markQueryAsLoaded]);

  // Atualizar estado das permissões comuns
  useEffect(() => {
    if (loadedQueries.authManagerReady && profile?.id && authState.company_id && !isLoadingCommonPermissions && !loadedQueries.commonPermissions) {
      logQueryEvent("Loading", "Permissões comuns pré-carregadas e marcadas", {
        loadedCount: permissionsLoadedCount,
        totalCount: permissionsTotalCount
      });
      markQueryAsLoaded('commonPermissions');
    }
  }, [
    loadedQueries.authManagerReady, 
    profile?.id, 
    authState.company_id, 
    isLoadingCommonPermissions, 
    loadedQueries.commonPermissions, 
    permissionsLoadedCount,
    permissionsTotalCount,
    markQueryAsLoaded
  ]);

  // Atualizar estado dos limites da timeline
  useEffect(() => {
    if (!isLoadingTimelineLimits && !loadedQueries.timelineLimits) {
      logQueryEvent("Loading", "Limites da timeline pré-carregados e marcados", {
        isEnabled: timelineEnabled,
        hasLimits: !!preloadedTimelineLimits,
        currentPlan: preloadedTimelineLimits?.currentPlan
      });
      markQueryAsLoaded('timelineLimits');
    }
  }, [
    isLoadingTimelineLimits,
    loadedQueries.timelineLimits,
    timelineEnabled,
    preloadedTimelineLimits,
    markQueryAsLoaded
  ]);

  // Atualizar estado das obrigações pendentes
  useEffect(() => {
    if (loadedQueries.authManagerReady && authState.user?.id && !isLoadingPendingObligations && !loadedQueries.pendingObligations) {
      logQueryEvent("Loading", "Obrigações pendentes pré-carregadas e marcadas", {
        count: pendingObligationsData?.length || 0
      });
      markQueryAsLoaded('pendingObligations');
    }
  }, [
    loadedQueries.authManagerReady,
    authState.user?.id,
    isLoadingPendingObligations,
    loadedQueries.pendingObligations,
    pendingObligationsData,
    markQueryAsLoaded
  ]);

  // Atualizar estado dos limites do Knowledge Hub
  useEffect(() => {
    if (!isLoadingKnowledgeHubLimits && !loadedQueries.knowledgeHubLimits) {
      logQueryEvent("Loading", "Limites do Knowledge Hub pré-carregados e marcados", {
        hasLimits: !!knowledgeHubLimits
      });
      markQueryAsLoaded('knowledgeHubLimits');
    }
  }, [
    isLoadingKnowledgeHubLimits,
    loadedQueries.knowledgeHubLimits,
    knowledgeHubLimits,
    markQueryAsLoaded
  ]);

  // Atualizar estado dos aniversários
  useEffect(() => {
    if (loadedQueries.authManagerReady && authState.company_id && !isLoadingBirthdays && !loadedQueries.birthdays) {
      logQueryEvent("Loading", "Aniversários pré-carregados e marcados", {
        count: birthdaysData?.length || 0
      });
      markQueryAsLoaded('birthdays');
    }
  }, [
    loadedQueries.authManagerReady,
    authState.company_id,
    isLoadingBirthdays,
    loadedQueries.birthdays,
    birthdaysData,
    markQueryAsLoaded
  ]);

  // Atualizar estado das notificações da timeline
  useEffect(() => {
    if (loadedQueries.authManagerReady && authState.user?.id && !isLoadingTimelineNotifications && !loadedQueries.timelineNotifications) {
      logQueryEvent("Loading", "Notificações da timeline pré-carregadas e marcadas", {
        count: timelineNotificationsData?.length || 0
      });
      markQueryAsLoaded('timelineNotifications');
    }
  }, [
    loadedQueries.authManagerReady,
    authState.user?.id,
    isLoadingTimelineNotifications,
    loadedQueries.timelineNotifications,
    timelineNotificationsData,
    markQueryAsLoaded
  ]);

  // Adicionar logs detalhados para a query useTrendingPosts
  useEffect(() => {
    logQueryEvent('Loading', 'TrendingPosts State', {
      isLoading: isLoadingTrendingPosts,
      isError: isTrendingPostsError,
      isSuccess: isTrendingPostsSuccess,
      status: trendingPostsStatus,
      data: trendingPostsData ? ` TRENDING POSTS ${trendingPostsData.length} posts` : 'no data yet'
    });
  }, [isLoadingTrendingPosts, isTrendingPostsError, isTrendingPostsSuccess, trendingPostsStatus, trendingPostsData]);

  // Verificar se todas as queries foram carregadas e iniciar fase final
  useEffect(() => {
    const loadedCount = Object.values(loadedQueries).filter(loaded => loaded).length;
    
    // Se todas as queries foram carregadas, iniciar a fase final
    if (loadedCount === totalQueries && !finalPhase) {
      console.log("🚀 [Loading] Todas as queries carregadas, iniciando fase final");
      setFinalPhase(true);
    }
  }, [loadedQueries, totalQueries, finalPhase]);
  
  // Timeout de segurança para evitar que o usuário fique preso na tela de carregamento
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!finalPhase) {
        console.log("⚠️ [Loading] Timeout de segurança acionado (2s), forçando fase final...");
        setFinalPhase(true);
        setProgress(80); // Pular para 80% imediatamente
      }
    }, 2000); // Reduzido para 2 segundos
    
    return () => clearTimeout(timer);
  }, [finalPhase]);

  // Calcular progresso
  useEffect(() => {
    // Se ainda estiver carregando queries
    if (!finalPhase) {
      const loadedCount = Object.values(loadedQueries).filter(loaded => loaded).length;
      const queryProgressPercentage = (loadedCount / totalQueries) * 80; // Progresso vai até 80% com queries
      const currentProgress = Math.min(Math.floor(queryProgressPercentage), 80);
      // Atualiza o progresso apenas se ele mudou para evitar logs excessivos
      if (currentProgress !== progress) {
        setProgress(currentProgress);
        console.log(`📊 [Loading] Progresso: ${loadedCount}/${totalQueries} queries carregadas (${currentProgress}%)`);
      }
    } 
    // Fase final - avanço progressivo para 100%
    else {
      const timer = setInterval(() => {
        setProgress(prev => {
          const next = prev + 2;
          if (next >= 100) {
            clearInterval(timer);
            // A navegação será feita por outro useEffect que observa o progresso
            return 100;
          }
          return next;
        });
      }, 50); // Ajuste a velocidade conforme necessário
      
      return () => clearInterval(timer);
    }
  }, [finalPhase, loadedQueries, totalQueries, progress]);

  // Novo useEffect para lidar com a navegação quando o progresso atingir 100%
  useEffect(() => {
    if (progress >= 100) {
      console.log("🏁 [Loading] Progresso completo, redirecionando...");
      sessionStorage.setItem('cosmosAppFullyLoaded', 'true');
      logQueryEvent("Loading", "Sinalizador cosmosAppFullyLoaded=true definido no sessionStorage (bypass)." );
      navigate(destination, { replace: true });
    }
  }, [progress, navigate, destination]);

  // Timeout de segurança final: se ficar mais de 6 segundos carregando, força navegação imediata
  useEffect(() => {
    const safetyTimeout = setTimeout(() => {
      // Verifica se o progresso ainda não atingiu 100% após o timeout
      if (progress < 100) {
        logQueryEvent("Loading", `Timeout FINAL (6s) ATINGIDO. Progresso: ${progress}%. FORÇANDO navegação para ${destination}.`, null, "warning");
        sessionStorage.setItem('cosmosAppFullyLoaded', 'true');
        logQueryEvent("Loading", "Sinalizador cosmosAppFullyLoaded=true definido no sessionStorage.");
        navigate(destination, { replace: true });
      } else {
        logQueryEvent("Loading", `Timeout final (6s) verificado. Progresso já em ${progress}%. Nenhuma ação necessária.`, null, "info");
      }
    }, 6000); // Reduzido de 15s para 6s
    
    // Limpa o timeout se o componente desmontar ou se o progresso completar antes
    return () => clearTimeout(safetyTimeout);
  }, [navigate, destination, progress]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-[#0b0e14] relative overflow-hidden">
      {/* Background com efeito de gradiente */}
      <div className="absolute inset-0 bg-[#0b0e14] opacity-70 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-[#0b0e14] via-[#121520] to-[#0b0e14]"></div>
        <div className="absolute top-0 left-0 right-0 h-40 bg-gradient-radial from-[#E8A95B]/5 to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-radial from-[#C85C2D]/5 to-transparent"></div>
      </div>

      {/* Estrelas e partículas */}
      <div className="absolute inset-0 z-0">
        {Array.from({ length: 50 }).map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-white"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              width: `${Math.random() * 3}px`,
              height: `${Math.random() * 3}px`,
              opacity: Math.random() * 0.7 + 0.3,
              animation: `twinkle ${Math.random() * 5 + 5}s infinite`,
            }}
          ></div>
        ))}
      </div>

      {/* Meteoros ocasionais */}
      <div className="meteor"></div>
      <div className="meteor" style={{ animationDelay: "2s" }}></div>
      <div className="meteor" style={{ animationDelay: "4s" }}></div>

      {/* Conteúdo */}
      <div className="z-10 flex flex-col items-center text-center px-4">
        <div className="mb-8 animate-pulse">
          <Logo />
        </div>

        <div className="flex flex-col items-center space-y-6 mb-8">
          <div className="relative flex items-center justify-center">
            <Loader2 className="h-16 w-16 text-[#E8A95B] animate-spin" />
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-xs font-semibold text-white">{progress}%</span>
            </div>
          </div>

          <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#E8A95B] to-[#C85C2D] animate-pulse">
            {loadingPhrases[currentPhrase]}
          </h2>
        </div>

        {/* Barra de progresso */}
        <div className="w-80 h-2 bg-gray-800 rounded-full overflow-hidden mb-2">
          <div
            className="h-2 bg-gradient-to-r from-[#E8A95B] to-[#C85C2D] rounded-full"
            style={{ width: `${progress}%`, transition: "width 0.2s ease-in-out" }}
          ></div>
        </div>
        <p className="text-sm text-gray-400 animate-pulse">
          Preparando tudo para você
        </p>
      </div>

      {/* Estilo para meteoros */}
      <style>{`
        @keyframes twinkle {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 1; }
        }

        .meteor {
          position: absolute;
          width: 2px;
          height: 50px;
          background: linear-gradient(to bottom, transparent, #E8A95B);
          opacity: 0;
          animation: meteor-fall 6s linear infinite;
        }

        @keyframes meteor-fall {
          0% {
            transform: translateY(-100vh) translateX(0) rotate(45deg);
            opacity: 0;
          }
          10% {
            opacity: 1;
          }
          20% {
            opacity: 1;
          }
          30% {
            opacity: 0;
            transform: translateY(100vh) translateX(100vw) rotate(45deg);
          }
          100% {
            opacity: 0;
            transform: translateY(100vh) translateX(100vw) rotate(45deg);
          }
        }
      `}</style>
    </div>
  );
} 