/**
 * <PERSON><PERSON><PERSON><PERSON> de Feed - Exibe posts e widgets configuráveis com layout elegante e moderno
 * <AUTHOR> Internet 2025
 */
import { useAuthStore } from "@/stores/authStore";
import { usePortletSettings } from "@/hooks/usePortletSettings";
import { usePortletRenderer } from "@/hooks/usePortletRenderer";
import { motion, AnimatePresence } from "framer-motion";
import { EnhancedFeedPostsRefactored as EnhancedFeedPosts } from "@/components/enhanced/feed/EnhancedFeedPostsRefactored";
import { logQueryEvent } from "@/lib/logs/showQueryLogs";
import { memo, useEffect, useMemo, useState, useCallback, useRef } from "react";
import { useFeatureAvailability } from "@/lib/query/hooks/useFeatureFlags";

// Sistema novo de widgets
import { WidgetManager, type Widget } from "@/components/feed/widgets/WidgetManager";

import { Card } from "@/components/ui/card";
import {
  Sparkles,
  BarChart3,
  ChevronUp,
  ChevronDown,
  RefreshCw,
  Filter,
  Calendar,
  Users,
  TrendingUp,
  MessageSquare,
  Heart,
  Star,
  Zap,
  Activity,
  LayoutGrid,
  List,
  Info,
  HelpCircle,
} from "lucide-react";
import { FeedStatsPanel } from "@/components/feed/FeedStatsPanel";
import { FeedSidebar } from "@/components/feed/FeedSidebar";
import { FeedFilters, type FeedFilters as FeedFiltersType } from "@/components/feed/FeedFilters";
import { type PostsFilters } from "@/lib/query/hooks/usePosts";
import { Button } from "@/components/ui/button";
import { HybridButton } from "@/components/ui/HybridButton";
import { HybridHeroSection } from "@/components/common/HybridHeroSection";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import { useQueryClient } from "@tanstack/react-query";
import {
  useWidgetSettings,
  useUpdateWidgetSettings,
  type WidgetSettings,
} from "@/lib/query/hooks/useWidgetSettings";
import { QueryKeys } from "@/lib/query/queryKeys";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  TooltipPortal,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import {
  Alert,
  AlertDescription,
} from "@/components/ui/alert";
import { useScheduledPostsCount } from "@/lib/query/hooks/usePosts";
import { useNavigate } from "react-router-dom";
import {
  successWithNotification,
  errorWithNotification,
} from "@/lib/notifications/toastWithNotification";
import { playSound, SoundEffects } from "@/lib/sound-effects";
import { useUpcomingEvents } from "@/lib/query/hooks/useEvents";
import { useFeedStats } from "@/lib/query/hooks/useFeedStats";
import { useTrendingPosts } from "@/lib/query/hooks/useTrendingPosts";
import { useActiveUsers } from "@/lib/query/hooks/useActiveUsers";
import { usePlatform } from "@/hooks/usePlatform";
import { cn } from "@/lib/utils";
import { PageHybrid, PageHybridVariants, usePageHybrid } from "@/components/layout/PageHybrid";

// Nova Timeline View
import { TimelineView } from "@/components/feed/timeline/TimelineView";

// Variantes de animação centralizadas
import { 
  containerVariants, 
  cardVariants, 
  heroVariants 
} from "@/lib/animations/variants";

// Configurações de recompensas para gamificação (como no Knowledge Hub)
const feedRewards = {
  createPost: { xp: 100, stardust: 50, title: "Criador de Conteúdo" },
  likePost: { xp: 5, stardust: 2, title: "Engajador" },
  commentPost: { xp: 25, stardust: 10, title: "Colaborador" },
  sharePost: { xp: 15, stardust: 8, title: "Influenciador" },
  readPost: { xp: 3, stardust: 1, title: "Leitor Ativo" },
  useFilters: { xp: 10, stardust: 5, title: "Organizador" },
};

function Feed() {
  // Obter dados do usuário diretamente do AuthStore, seguindo o padrão recomendado
  const user = useAuthStore(state => state.user);
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const isLoading = useAuthStore(state => state.isLoading);
  const initialize = useAuthStore(state => state.initialize);

  
  // Hook antigo para compatibilidade (será removido gradualmente)
  const queryClientHook = useQueryClient();

  // Hook para detectar plataforma e helpers híbridos
  const { isNative } = usePlatform();
  const { spacing, iconSize, gap } = usePageHybrid();
  
  // Detectar mobile via media query (Chrome mobile não é nativo)
  const isMobile = window?.innerWidth <= 768;

  // Estado para controlar o carregamento durante a reinicialização
  const [isReinitializing, setIsReinitializing] = useState(false);
  
  
  // Detectar se viemos de um error recovery
  const [isRecoveredSession, setIsRecoveredSession] = useState(false);


  // Estado para controlar a visualização (Timeline como padrão, persistido no localStorage e perfil)
  const [viewMode, setViewMode] = useLocalStorage<'grid' | 'timeline'>('feed-view-mode', 'timeline', {
    syncToProfile: true,
    profileField: 'profile_extensions',
    profileSection: 'ui_preferences'
  });
  
  // Estado para controlar a explicação dos modos de visualização (persistido no localStorage e perfil)
  const [showViewModeExplanation, setShowViewModeExplanation] = useLocalStorage<boolean>('feed-view-mode-explanation-shown', false, {
    syncToProfile: true,
    profileField: 'profile_extensions',
    profileSection: 'ui_preferences'
  });

  // Hook para contar posts agendados
  const { data: scheduledCount = 0 } = useScheduledPostsCount();

  // Verificar se viemos de um error recovery e agir adequadamente
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const isRecovered = urlParams.has('recovered');
    
    if (isRecovered) {
      setIsRecoveredSession(true);
      logQueryEvent("Feed", "Sessão recuperada após error boundary", {
        url: window.location.href,
        timestamp: new Date().toISOString()
      });
      
      // Limpar URL parameter para evitar loop
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('recovered');
      window.history.replaceState({}, document.title, newUrl.toString());
      
      // Refresh seletivo de queries críticas após recovery ao invés de reset completo
      setTimeout(() => {
        // Apenas invalidar queries específicas que podem estar em estado inconsistente
        queryClientHook.invalidateQueries({ queryKey: QueryKeys.posts.feed(), stale: true });
        queryClientHook.invalidateQueries({ queryKey: QueryKeys.timeline.notifications(), stale: true });
        logQueryEvent("Feed", "Refresh seletivo de queries críticas após recovery");
      }, 100);
    }
  }, [queryClientHook]);

  // Log apenas na montagem inicial do componente
  useEffect(() => {
    // Log removido para reduzir verbosidade
  }, [user?.id, isAuthenticated, isLoading, isRecoveredSession]);

  // Não é mais necessário inicializar o authStore aqui, pois isso já é feito no Loading.tsx
  // Se o Feed está sendo renderizado, o authStore já deve estar inicializado
  useEffect(() => {
    if (isLoading) {
      logQueryEvent(
        "Feed",
        "Feed detectou authStore ainda em carregamento, isso não deveria acontecer",
        {
          userId: user?.id,
          isAuthenticated,
        },
        "warn"
      );
    }
  }, [isLoading, user?.id, isAuthenticated]);

  // Hooks para portlets (mantidos para compatibilidade, mas não serão exibidos)
  const { renderPortlet, availablePortlets } = usePortletRenderer();
  const {
    activePortlets,
    rightColumnItems,
    farRightColumnItems,
    togglePortlet,
    updateRightColumnItems,
    updateFarRightColumnItems,
    isLoading: isLoadingPortlets,
    isSaving: isSavingPortlets,
  } = usePortletSettings();

  // ===== VERIFICAÇÃO DE FEATURE FLAG PARA WIDGETS =====
  const { data: widgetFeatureAvailability, isLoading: isLoadingWidgetFeature } =
    useFeatureAvailability("feed_widgets");
  const isWidgetFeatureEnabled = widgetFeatureAvailability?.isFeatureEnabled ?? false;

  // ===== NOVO SISTEMA DE WIDGETS COM PERSISTÊNCIA NO BANCO =====

  // Configurações padrão dos widgets
  const defaultWidgets: Widget[] = [
    {
      id: "feed-posts",
      title: "Posts do Feed",
      category: "social",
      size: "large",
      priority: 0,
      isActive: true,
      columnSpan: 2, // Widget principal ocupa 2 colunas
      heightSpan: 3, // Widget de feed é muito alto (900px)
    },
    {
      id: "trending-posts",
      title: "Posts em Destaque",
      category: "social",
      size: "medium",
      priority: 1,
      isActive: true,
      columnSpan: 1, // Widget secundário ocupa 1 coluna
      heightSpan: 2, // Altura média (600px)
    },
    {
      id: "active-users",
      title: "Usuários Ativos",
      category: "social",
      size: "medium",
      priority: 2,
      isActive: true,
      columnSpan: 1, // Widget secundário ocupa 1 coluna
      heightSpan: 1, // Altura padrão (300px)
    },
    {
      id: "upcoming-events",
      title: "Próximos Eventos",
      category: "productivity",
      size: "medium",
      priority: 3,
      isActive: false,
      columnSpan: 1,
      heightSpan: 1,
    },
    {
      id: "currency-table",
      title: "Cotações",
      category: "finance",
      size: "medium",
      priority: 4,
      isActive: false,
      columnSpan: 1,
      heightSpan: 2, // Tabelas precisam de mais altura
    },
    {
      id: "analytics-model",
      title: "Analytics (Modelo)",
      category: "productivity",
      size: "medium",
      priority: 5,
      isActive: false,
      columnSpan: 2, // Começar com 2 colunas para mostrar funcionalidades
      heightSpan: 2, // Altura média para demonstrar scroll
    },
  ];

  // Hooks para configurações persistidas no banco
  const { data: widgetSettings, isLoading: isLoadingWidgetSettings } = useWidgetSettings();
  const { mutate: updateWidgetSettings } = useUpdateWidgetSettings();

  // ========= MEMOIZAÇÃO DE QUERIES PARA EVITAR QUERY STORM =========

  // Ref para debounce de renderizações
  const renderCountRef = useRef(0);

  // Memoizar parâmetros das queries condicionais para evitar múltiplas chamadas
  const upcomingEventsParams = useMemo(
    () => ({
      limit: 3,
      scope: "all" as const,
      days_ahead: 7,
      include_personal: true,
    }),
    []
  );

  const trendingPostsParams = useMemo(
    () => ({
      limit: 5,
    }),
    []
  );

  const activeUsersParams = useMemo(
    () => ({
      activeThresholdMinutes: 15,
    }),
    []
  );

  // Memoizar condições de enable para evitar re-renders desnecessários
  const queriesEnabled = useMemo(() => {
    const isReady = user?.id && !isLoading && isAuthenticated;
    return {
      isReady,
      upcomingEvents: isReady,
      trendingPosts: isReady,
      activeUsers: isReady,
      widgetSettings: isReady,
      feedStats: isReady,
    };
  }, [user?.id, isLoading, isAuthenticated]);

  // Estado dos widgets derivado das configurações persistidas
  const widgets = useMemo(() => {
    if (!widgetSettings?.widgets || widgetSettings.widgets.length === 0) {
      return defaultWidgets;
    }
    return widgetSettings.widgets;
  }, [widgetSettings?.widgets, defaultWidgets]);

  // Migração one-time do localStorage para o banco (apenas uma vez)
  useEffect(() => {
    if (!isLoadingWidgetSettings && widgetSettings && widgetSettings.widgets.length === 0) {
      // Tentar recuperar dados do localStorage antigo
      try {
        const localStorageWidgets = localStorage.getItem("feed-widgets");
        const localStorageShowWidgets = localStorage.getItem("feed-show-widgets");
        const localStorageShowStats = localStorage.getItem("feed-stats-panel-visible");

        if (localStorageWidgets) {
          const parsedWidgets = JSON.parse(localStorageWidgets);
          if (Array.isArray(parsedWidgets) && parsedWidgets.length > 0) {
            logQueryEvent("Feed", "Migrando dados do localStorage para o banco", {
              widgetCount: parsedWidgets.length,
            });

            updateWidgetSettings({
              widgets: parsedWidgets as Widget[],
              activeWidgets: (parsedWidgets as Widget[]).filter(w => w.isActive).map(w => w.id),
              showWidgets: localStorageShowWidgets ? JSON.parse(localStorageShowWidgets) : false,
              showStatsPanel: localStorageShowStats ? JSON.parse(localStorageShowStats) : false,
            });

            // Limpar localStorage após migração
            localStorage.removeItem("feed-widgets");
            localStorage.removeItem("feed-show-widgets");
            localStorage.removeItem("feed-stats-panel-visible");
          }
        }
      } catch (error) {
        logQueryEvent("Feed", "Erro ao migrar dados do localStorage", { error }, "error");
      }
    }
  }, [isLoadingWidgetSettings, widgetSettings, updateWidgetSettings]);

  // Widgets disponíveis (mapeando dos portlets existentes)
  const availableWidgetsNew = useMemo(
    () =>
      availablePortlets.map(portlet => {
        // Mapear categorias específicas baseado no ID
        let category: "productivity" | "info" | "social" | "finance" | "weather" | "other" =
          "other";

        switch (portlet.id) {
          case "trending-posts":
          case "active-users":
          case "feed-posts":
            category = "social";
            break;
          case "upcoming-events":
          case "calendar":
          case "tasks":
            category = "productivity";
            break;
          case "currency":
          case "currency-table":
          case "stock":
          case "stock-market":
          case "crypto":
            category = "finance";
            break;
          case "weather":
          case "air-quality":
          case "space-weather":
            category = "weather";
            break;
          case "news":
          case "events":
          case "traffic":
            category = "info";
            break;
          default:
            category = "other";
        }

        return {
          id: portlet.id,
          label: portlet.label,
          category,
        };
      }),
    [availablePortlets]
  );

  // Função para normalizar widgets (garantir que tenham todas as propriedades obrigatórias)
  const normalizeWidgets = useCallback((widgets: Widget[]): Widget[] => {
    return widgets.map(widget => ({
      ...widget,
      columnSpan: widget.columnSpan || 1, // Garantir que columnSpan seja sempre um número válido
      heightSpan: widget.heightSpan || 1, // Garantir que heightSpan seja sempre um número válido
    }));
  }, []);

  // Função para atualizar widgets
  const handleUpdateWidgets = useCallback(
    (newWidgets: Widget[]) => {
      const normalizedWidgets = normalizeWidgets(newWidgets);

      // Atualizar no banco através do hook
      updateWidgetSettings({
        widgets: normalizedWidgets,
        activeWidgets: normalizedWidgets.filter(w => w.isActive).map(w => w.id),
      });

      logQueryEvent("Feed", "Widgets atualizados", { count: normalizedWidgets.length });
    },
    [updateWidgetSettings, normalizeWidgets]
  );

  // Efeito para normalizar widgets existentes na inicialização
  useEffect(() => {
    const needsNormalization = widgets.some(
      widget =>
        widget.columnSpan === undefined ||
        widget.columnSpan === null ||
        isNaN(widget.columnSpan as number) ||
        widget.heightSpan === undefined ||
        widget.heightSpan === null ||
        isNaN(widget.heightSpan as number)
    );

    if (needsNormalization && !isLoadingWidgetSettings) {
      logQueryEvent("Feed", "Normalizando widgets existentes sem columnSpan");
      const normalizedWidgets = normalizeWidgets(widgets);
      updateWidgetSettings({
        widgets: normalizedWidgets,
        activeWidgets: normalizedWidgets.filter(w => w.isActive).map(w => w.id),
      });
    }
  }, [widgets, isLoadingWidgetSettings, normalizeWidgets, updateWidgetSettings]);

  // Flags para controlar exibição derivadas das configurações persistidas
  const showWidgets = widgetSettings?.showWidgets ?? false;
  const showStatsPanel = widgetSettings?.showStatsPanel ?? false;

  // Funções para atualizar as flags
  const setShowWidgets = useCallback(
    (show: boolean) => {
      updateWidgetSettings({ showWidgets: show });
    },
    [updateWidgetSettings]
  );

  const setShowStatsPanel = useCallback(
    (show: boolean) => {
      updateWidgetSettings({ showStatsPanel: show });
    },
    [updateWidgetSettings]
  );

  // Estado para controlar filtros
  const [showFilters, setShowFilters] = useState(false);
  const [feedFilters, setFeedFilters] = useState<FeedFiltersType>({
    sortBy: "newest",
    status: "all",
    audience: "all",
  });

  // Converter filtros do componente para o formato do hook
  const postsFilters = useMemo<PostsFilters>(
    () => ({
      search: feedFilters.search,
      sortBy: feedFilters.sortBy,
      status: feedFilters.status,
      audience: feedFilters.audience,
      dateRange: feedFilters.dateRange,
    }),
    [feedFilters]
  );

  // Memoizar os props para EnhancedFeedPosts
  const feedPostsProps = useMemo(
    () => ({
      currentUserId: user?.id || "",
      filters: postsFilters,
    }),
    [user?.id, postsFilters]
  );

  // Estado para controlar o carregamento do botão atualizar
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showRefreshingText, setShowRefreshingText] = useState(false);


  // ========= HOOKS MEMOIZADOS PARA EVITAR QUERY STORM =========

  // Log de renders para debugging
  useEffect(() => {
    renderCountRef.current += 1;
    // Log de re-render removido para reduzir verbosidade
  });

  // Hooks de queries com memoização adequada e staleTime agressivo
  const { isLoading: isLoadingUpcomingEvents } = useUpcomingEvents(upcomingEventsParams, {
    enabled: queriesEnabled.upcomingEvents,
    staleTime: 10 * 60 * 1000, // 10 minutos de cache
    gcTime: 30 * 60 * 1000, // 30 minutos na memória
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  const { isLoading: isLoadingTrendingPosts } = useTrendingPosts(trendingPostsParams.limit);

  const { data: activeUsersData, isLoading: isLoadingActiveUsers, error: activeUsersError } = useActiveUsers({
    ...activeUsersParams,
    enabled: queriesEnabled.activeUsers,
  });

  const { isLoading: isLoadingFeedStats } = useFeedStats();

  // Hooks para funcionalidades
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  // Função para atualizar o feed (compatível com PullToRefresh)
  const handleRefreshFeed = async (): Promise<void> => {
    const startTime = Date.now();

    try {
      setIsRefreshing(true);
      setShowRefreshingText(true);
      logQueryEvent("Feed", "Iniciando atualização manual do feed");

      // Reproduzir feedback sonoro
      playSound(SoundEffects.REFRESH);

      // Configurar tempo mínimo para o texto "Atualizando..." (3 segundos)
      const minimumDisplayTime = new Promise(resolve => setTimeout(resolve, 3000));

      // Configurar timeout para evitar espera infinita no refresh
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Timeout ao atualizar feed")), 10000)
      );

      // Executar o refresh
      const refreshPromise = Promise.race([
        Promise.all([
          queryClient.invalidateQueries({ queryKey: QueryKeys.posts.feed() }),
          queryClient.invalidateQueries({ queryKey: QueryKeys.posts.scheduled() }),
        ]),
        timeoutPromise,
      ]);

      // Aguardar tanto o refresh quanto o tempo mínimo
      await Promise.all([refreshPromise, minimumDisplayTime]);

      successWithNotification("Feed atualizado!", {
        description: "O feed foi atualizado com as publicações mais recentes.",
      });

      logQueryEvent("Feed", "Feed atualizado com sucesso");
    } catch (error) {
      logQueryEvent("Feed", "Erro ao atualizar feed", error, "error");
      errorWithNotification("Erro ao atualizar", {
        description: "Não foi possível atualizar os posts. Tente novamente.",
      });

      // Mesmo em erro, aguardar o tempo mínimo se ainda não passou
      const timeElapsed = Date.now() - startTime;
      if (timeElapsed < 3000) {
        await new Promise(resolve => setTimeout(resolve, 3000 - timeElapsed));
      }
    } finally {
      setIsRefreshing(false);
      setShowRefreshingText(false);
    }
  };

  // Função para navegar para posts agendados
  const handleScheduledPosts = () => {
    logQueryEvent("Feed", "Navegando para posts agendados");
    navigate("/post/scheduled");
  };

  // Função para lidar com mudanças nos filtros
  const handleFilterChange = (newFilters: FeedFiltersType) => {
    setFeedFilters(newFilters);
    logQueryEvent("Feed", "Filtros alterados", newFilters);
  };

  // Função para toggle dos filtros
  const handleToggleFilters = () => {
    setShowFilters(prev => !prev);
    logQueryEvent("Feed", `Filtros ${!showFilters ? "abertos" : "fechados"}`);
  };

  // Função para criar novo post
  const handleCreatePost = () => {
    logQueryEvent("Feed", "Navegando para criar novo post");
    navigate("/post/create");
  };

  // Função para alternar modo de visualização
  const handleToggleViewMode = () => {
    const newMode = viewMode === 'timeline' ? 'grid' : 'timeline';
    setViewMode(newMode);
    logQueryEvent("Feed", `Modo de visualização alterado para ${newMode === 'timeline' ? 'Timeline Completa' : 'Apenas Posts'}`);
  };

  return (
    <PageHybrid
      {...PageHybridVariants.social}
      onRefresh={handleRefreshFeed}
      isRefreshing={isRefreshing}
    >

      {/* Hero Section Híbrida */}
      <HybridHeroSection
        title="Feed"
        description=""
        icon={Sparkles}
        gradientColors="from-orange-500 via-red-500 to-pink-600"
        actions={
          <div
            className={cn(
              "flex items-center",
              (isNative || isMobile) ? "gap-2 w-full justify-end" : "gap-2"
            )}
          >
            {/* Botão de Ajuda - Explica os modos de visualização */}
            <TooltipPortal side="bottom" content={<p>Reexibir explicação dos modos de visualização</p>}>
              <Button
                onClick={() => setShowViewModeExplanation(false)}
                className={cn(
                  "bg-white/20 text-white border-white/30 backdrop-blur-sm hover:bg-white/30",
                  "px-3 py-2"
                )}
                size="sm"
              >
                <HelpCircle className="h-4 w-4" />
              </Button>
            </TooltipPortal>

            {/* Botão de Toggle de Visualização - Apenas ícone com tooltip */}
            <TooltipPortal side="bottom" content={<p>{viewMode === 'grid' ? "Ver Timeline Completa" : "Ver Apenas Posts"}</p>}>
              <Button
                onClick={handleToggleViewMode}
                className={cn(
                  "bg-white/20 text-white border-white/30 backdrop-blur-sm hover:bg-white/30",
                  "px-3 py-2"
                )}
                size="sm"
              >
                {viewMode === 'grid' ? (
                  <List className="h-4 w-4" />
                ) : (
                  <LayoutGrid className="h-4 w-4" />
                )}
              </Button>
            </TooltipPortal>

            {/* Botão Agendados - Apenas ícone com tooltip e badge */}
            <TooltipPortal side="bottom" content={<p>Posts Agendados {scheduledCount > 0 && `(${scheduledCount})`}</p>}>
              <Button
                onClick={handleScheduledPosts}
                className={cn(
                  "bg-white/20 text-white border-white/30 backdrop-blur-sm hover:bg-white/30 relative",
                  "px-3 py-2"
                )}
                size="sm"
              >
                <Calendar className="h-4 w-4" />
                {scheduledCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs font-medium bg-orange-500 hover:bg-orange-600 border-0"
                  >
                    {scheduledCount > 99 ? "99+" : scheduledCount}
                  </Badge>
                )}
              </Button>
            </TooltipPortal>
          </div>
        }
      />

      {/* Explicação sobre os modos de visualização */}
      {!showViewModeExplanation && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className={cn((isNative || isMobile) ? "mb-2" : "mb-4")}
        >
          <Alert className="bg-gradient-to-r from-blue-50 to-indigo-50 border-0 shadow-lg dark:from-blue-950/30 dark:to-indigo-950/30">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                <Info className="h-5 w-5 text-white" />
              </div>
              <div className="flex-1">
                <AlertDescription className="text-gray-700 dark:text-gray-200">
                  <div className="space-y-3">
                    <h4 className="font-semibold text-lg text-gray-900 dark:text-white mb-2">
                      Entenda os Modos de Visualização
                    </h4>
                    
                    <div className="bg-white/70 dark:bg-gray-800/70 rounded-lg p-3 backdrop-blur-sm">
                      <div className="flex items-center gap-2 mb-2">
                        <Activity className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        <span className="font-medium text-blue-900 dark:text-blue-100">Timeline Completa</span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                        Orientada a <strong>eventos</strong> - mais rica e dinâmica. Funciona a partir do seu primeiro login, 
                        capturando todas as suas atividades e interações em tempo real.
                      </p>
                    </div>

                    <div className="bg-white/70 dark:bg-gray-800/70 rounded-lg p-3 backdrop-blur-sm">
                      <div className="flex items-center gap-2 mb-2">
                        <List className="h-4 w-4 text-green-600 dark:text-green-400" />
                        <span className="font-medium text-green-900 dark:text-green-100">Ver Apenas Posts</span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                        Baseada em <strong>histórico temporal</strong> - sempre disponível. Mostra todo o conteúdo 
                        publicado desde o início, incluindo posts anteriores ao seu primeiro acesso.
                      </p>
                    </div>

                    <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400 pt-2 border-t border-gray-200 dark:border-gray-600">
                      <HelpCircle className="h-3 w-3" />
                      <span>Você pode alternar entre os modos usando os botões no cabeçalho</span>
                    </div>
                  </div>
                </AlertDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowViewModeExplanation(true)}
                className="flex-shrink-0 w-8 h-8 p-0 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
              >
                ×
              </Button>
            </div>
          </Alert>
        </motion.div>
      )}

      {/* Timeline View - Renderizada no mesmo nível para evitar layout shifts */}
      {viewMode === 'timeline' && (
        <div className="flex-1 overflow-hidden">
          <TimelineView />
        </div>
      )}

      {/* Painel de Estatísticas Premium - Controlável - Apenas no modo Grid */}
      {viewMode === 'grid' && showStatsPanel && (
        <motion.div
          variants={cardVariants}
          className={cn((isNative || isMobile) ? "mb-1" : "mb-6")} // Muito menos espaço no mobile
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className={(isNative || isMobile) ? "border-0 bg-gradient-to-br from-orange-500/5 to-pink-500/5 backdrop-blur-sm rounded-none mx-0" : "border-0 bg-gradient-to-br from-orange-500/5 to-pink-500/5 backdrop-blur-sm"}>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <BarChart3 className="h-5 w-5 text-orange-500" />
                <h3 className="text-lg font-semibold">Estatísticas do Feed</h3>
              </div>
              <FeedStatsPanel />
            </div>
          </Card>
        </motion.div>
      )}

      {/* Barra de Controles Premium - Apenas no modo Grid */}
      <AnimatePresence>
        {viewMode === 'grid' && (
          <motion.div 
            variants={cardVariants} 
            className={(isNative || isMobile) ? "mb-2" : "mb-6"}
            initial={{ opacity: 0, y: -20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
          >
            <Card className={(isNative || isMobile) ? "p-2 bg-white/70 dark:bg-gray-900/70 backdrop-blur-sm border-0 shadow-lg rounded-none mx-0" : "p-4 bg-white/70 dark:bg-gray-900/70 backdrop-blur-sm border border-white/20 shadow-lg"}>
          <div
            className={cn(
              "flex items-center",
              (isNative || isMobile) ? "gap-1.5 flex-wrap" : "gap-3" // Gap menor e permitir wrap no mobile
            )}
          >
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <HybridButton
                variant="outline"
                size={(isNative || isMobile) ? "sm" : "sm"} // Manter sm mas aplicar override
                onClick={() => setShowStatsPanel(!showStatsPanel)}
                className={cn(
                  "border-orange-200 transition-all duration-300",
                  showStatsPanel
                    ? "bg-orange-100 text-orange-700 border-orange-300"
                    : "hover:bg-orange-50",
                  (isNative || isMobile) && "px-2 py-1 text-xs min-h-[32px]" // Muito menor no mobile
                )}
              >
                <BarChart3 className={cn((isNative || isMobile) ? "h-3 w-3 mr-1" : "h-4 w-4 mr-2")} />
                {(isNative || isMobile) ? "Stats" : "Estatísticas"} {/* Texto mais curto no mobile */}
                {showStatsPanel ? (
                  <ChevronUp className={cn((isNative || isMobile) ? "h-2 w-2 ml-0.5" : "h-3 w-3 ml-1")} />
                ) : (
                  <ChevronDown className={cn((isNative || isMobile) ? "h-2 w-2 ml-0.5" : "h-3 w-3 ml-1")} />
                )}
              </HybridButton>
            </motion.div>

            {isWidgetFeatureEnabled && (
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowWidgets(!showWidgets)}
                  className={cn(
                    "border-purple-200 transition-all duration-300",
                    showWidgets
                      ? "bg-purple-100 text-purple-700 border-purple-300"
                      : "hover:bg-purple-50",
                    (isNative || isMobile) && "px-2 py-1 text-xs min-h-[32px]"
                  )}
                >
                  <LayoutGrid className={cn((isNative || isMobile) ? "h-3 w-3 mr-1" : "h-4 w-4 mr-2")} />
                  {(isNative || isMobile) ? "Widgets" : "Widgets"}
                  {showWidgets ? (
                    <ChevronUp className={cn(isNative ? "h-2 w-2 ml-0.5" : "h-3 w-3 ml-1")} />
                  ) : (
                    <ChevronDown className={cn(isNative ? "h-2 w-2 ml-0.5" : "h-3 w-3 ml-1")} />
                  )}
                </Button>
              </motion.div>
            )}

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline"
                size="sm"
                onClick={handleToggleFilters}
                className={cn(
                  "border-orange-200 transition-all duration-300",
                  showFilters
                    ? "bg-orange-100 text-orange-700 border-orange-300"
                    : "hover:bg-orange-50",
                  isNative && "px-2 py-1 text-xs min-h-[32px]"
                )}
              >
                <Filter className={cn((isNative || isMobile) ? "h-3 w-3 mr-1" : "h-4 w-4 mr-2")} />
                {(isNative || isMobile) ? "Filtros" : "Filtros"}
                {showFilters ? (
                  <ChevronUp className={cn((isNative || isMobile) ? "h-2 w-2 ml-0.5" : "h-3 w-3 ml-1")} />
                ) : (
                  <ChevronDown className={cn((isNative || isMobile) ? "h-2 w-2 ml-0.5" : "h-3 w-3 ml-1")} />
                )}
              </Button>
            </motion.div>

            {/* Botão Sync removido - agora usamos pull-to-refresh no mobile */}
            {!(isNative || isMobile) && (
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <HybridButton
                  variant="default"
                  size="sm"
                  onClick={handleRefreshFeed}
                  disabled={isRefreshing}
                  className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white border-0 shadow-lg disabled:opacity-70"
                >
                  <motion.div
                    animate={isRefreshing ? { rotate: 360 } : { rotate: 0 }}
                    transition={{
                      duration: 2,
                      ease: "linear",
                      repeat: isRefreshing ? Infinity : 0,
                    }}
                    className="mr-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                  </motion.div>
                  {showRefreshingText ? "Atualizando..." : "Atualizar"}
                </HybridButton>
              </motion.div>
            )}
          </div>
        </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Painel de Filtros Premium - Controlável - Apenas no modo Grid */}
      {viewMode === 'grid' && showFilters && (
        <motion.div
          variants={cardVariants}
          className={cn((isNative || isMobile) ? "mb-1" : "mb-6")} // Muito menos espaço no mobile
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className={(isNative || isMobile) ? "border-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 backdrop-blur-sm rounded-none mx-0" : "border-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 backdrop-blur-sm"}>
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Filter className="h-5 w-5 text-blue-500" />
                  <h3 className="text-lg font-semibold">Filtros Avançados</h3>
                </div>
              </div>
              
              <FeedFilters filters={feedFilters} onFilterChange={handleFilterChange} />
            </div>
          </Card>
        </motion.div>
      )}

      {/* Novo Sistema de Widgets Premium - Apenas no modo Grid */}
      {viewMode === 'grid' && isWidgetFeatureEnabled && showWidgets && (
        <motion.div
          variants={cardVariants}
          className={cn((isNative || isMobile) ? "mb-1" : "mb-6")} // Muito menos espaço no mobile
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          <WidgetManager
            widgets={widgets}
            onUpdateWidgets={handleUpdateWidgets}
            availableWidgets={availableWidgetsNew}
            renderWidget={renderPortlet}
            className="mb-6"
          />
        </motion.div>
      )}

      {/* Conteúdo Principal - Grid Mode */}
      {viewMode === 'grid' && (
        /* Grid Principal Tradicional com Layout Responsivo Premium */
        <motion.div
          className={cn(
            "grid grid-cols-12 flex-1",
            (isNative || isMobile) ? "gap-1" : "gap-6" // Gap mínimo no mobile
          )}
          variants={containerVariants}
          key="grid"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 20 }}
          transition={{ duration: 0.5 }}
        >
          {/* Feed Principal */}
          <motion.div className="col-span-12 lg:col-span-8 xl:col-span-9" variants={cardVariants}>
            <EnhancedFeedPosts {...feedPostsProps} />
          </motion.div>

          {/* Sidebar Premium - Sempre visível para demonstrar o design */}
          <motion.div className="hidden lg:block lg:col-span-4 xl:col-span-3" variants={cardVariants}>
            <div className="sticky top-4 space-y-6">
              <FeedSidebar />
            </div>
          </motion.div>
        </motion.div>
      )}
    </PageHybrid>
  );
}

export default memo(Feed);
