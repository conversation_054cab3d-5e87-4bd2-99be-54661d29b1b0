import { useParams, useSearchParams } from "react-router-dom";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useCurrentUser } from "@/hooks/use-current-user";
import { usePostDetails, useTogglePostLike } from "@/lib/query/hooks/usePosts";
import { Skeleton } from "@/components/ui/skeleton";
import { EnhancedPostCard } from "@/components/enhanced/feed/EnhancedPostCard";

const Post = () => {
  const { postId } = useParams();
  const [searchParams] = useSearchParams();
  const commentId = searchParams.get('comment');
  const currentUserId = useCurrentUser();
  
  // Usando os hooks da nova arquitetura centralizada
  const { data: post, isLoading, error } = usePostDetails(postId);
  const toggleLikeMutation = useTogglePostLike();

  const formatDate = (date: string) => {
    return format(new Date(date), "d 'de' MMMM 'às' HH:mm", {
      locale: ptBR,
    });
  };

  // Função para acionar o toggle de like
  const handleToggleLike = (postId: string) => {
    if (!currentUserId) return;
    
    // Verificar se o usuário já deu like
    const isLiked = post?.liked_by.some(like => 
      like.profiles?.id === currentUserId
    );
    
    toggleLikeMutation.mutate({
      postId,
      userId: currentUserId,
      isLiked: !!isLiked
    });
  };

  if (isLoading) {
    return (
      <div className="container mx-auto max-w-3xl py-4">
        <div className="border rounded-lg p-4 space-y-3">
          <div className="flex items-center space-x-2">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-1">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto max-w-3xl py-4">
        <Alert variant="destructive">
          <AlertDescription>
            {error instanceof Error ? error.message : "Erro ao carregar post"}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="container mx-auto max-w-3xl py-4">
        <Alert>
          <AlertDescription>Post não encontrado</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-3xl space-y-4 py-4">
      <EnhancedPostCard
        post={post}
        currentUserId={currentUserId}
        onLike={handleToggleLike}
        formatDate={formatDate}
        highlightCommentId={commentId}
      />
    </div>
  );
};

export default Post;