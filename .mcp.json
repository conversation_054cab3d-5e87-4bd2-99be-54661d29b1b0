{"mcpServers": {"vindula-cosmos-brain": {"command": "/Users/<USER>/projetos/vindulacosmos-e6b4d65c/vindula-cosmos-brain/run_fastmcp.sh"}, "supabase": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-supabase"], "env": {}}, "Playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "upstash-context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "Stripe": {"command": "npx", "args": ["-y", "@stripe/mcp", "--tools=all", "--api-key=sk_test_51HFIT1JUSsQg7OIMoUMsAe5VxqYDqDMGfbiqaWTTEInbN01dVX7bmyjYOgV3hug58vTrE9XhdNTYlONNV7b4OZ8j007phCyBuV"]}, "sequentialthinking": {"command": "docker", "args": ["run", "--rm", "-i", "mcp/sequentialthinking"]}}}