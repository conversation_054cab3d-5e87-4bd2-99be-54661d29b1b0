# Vindula Cosmos - Mapeamento E2E Testing

## 🎯 Objetivo
Criar suite completa de testes E2E com Playwright cobrindo todas as páginas e funcionalidades do Vindula Cosmos.

## 📋 Páginas Mapeadas

### 1. Landing Page (http://localhost:8080)
**Elementos:**
```typescript
// Seletores identificados
const selectors = {
  header: {
    logo: '[data-testid="vindula-logo"]', // ou 'img[alt*="Vindula"]'
    loginButton: 'button:has-text("Fazer Login")'
  },
  hero: {
    testBanner: 'div:has-text("Teste Grátis por Tempo Limitado")',
    emailInput: 'input[placeholder*="email"]',
    startButton: 'button:has-text("Começar")'
  },
  benefits: {
    engagementCard: 'div:has-text("300%")',
    aiCard: 'div:has-text("15 IAs")', 
    setupCard: 'div:has-text("5 min")'
  }
}
```

**Testes a implementar:**
- ✅ Carregamento da página
- ✅ Elementos visíveis
- ✅ Fluxo de cadastro (email → começar)
- ✅ Navegação para login
- ✅ Responsividade

**Actions necessárias:**
1. `page.goto('http://localhost:8080')`
2. `page.fill('[email-input]', '<EMAIL>')`
3. `page.click('[start-button]')`
4. `page.click('[login-button]')`

### 2. Página de Login (/auth) - CACHE LIMPO
**Elementos:**
```typescript
const loginSelectors = {
  header: {
    logo: 'img[alt*="Vindula"]',
    title: 'text=Acesse sua conta',
    subtitle: 'text=Continue sua jornada no universo Cosmos'
  },
  form: {
    emailInput: 'input[placeholder="<EMAIL>"]',
    passwordInput: 'input[type="password"]',
    showPasswordToggle: 'button:has(img)', // Botão do olhinho
    loginButton: 'button:has-text("Entrar")',
    forgotPasswordLink: 'text=Esqueceu sua senha?'
  },
  oauth: {
    googleButton: 'button:has-text("Continuar com Google")',
    microsoftButton: 'button:has-text("Continuar com Microsoft")'
  },
  footer: {
    createAccountLink: 'text=Crie uma conta',
    termsLink: 'text=Termos de Serviço',
    privacyLink: 'text=Política de Privacidade'
  }
}
```

**Ações mapeadas:**
```typescript
// STEP 1: Navegar para login a partir da landing
await page.goto('http://localhost:8080');
await page.click('button:has-text("Fazer Login")');
await expect(page).toHaveURL(/.*auth.*/);

// STEP 2: Preencher formulário de login
await page.fill('input[placeholder="<EMAIL>"]', '<EMAIL>');
await page.fill('input[type="password"]', 'senha123');
await page.click('button:has-text("Entrar")');

// STEP 3: Alternativas OAuth
// await page.click('button:has-text("Continuar com Google")');
// await page.click('button:has-text("Continuar com Microsoft")');

// STEP 4: Fluxos secundários
// await page.click('text=Esqueceu sua senha?');
// await page.click('text=Crie uma conta');
```

**Validações necessárias:**
- ✅ Elementos da página carregados corretamente
- ✅ Login com credenciais válidas → redirecionamento para dashboard
- ✅ Login com credenciais inválidas → mensagem de erro
- ✅ OAuth Google funcional
- ✅ OAuth Microsoft funcional
- ✅ Link "Esqueceu sua senha" funcional
- ✅ Link "Crie uma conta" funcional
- ✅ Validação de campos obrigatórios

---

## 🔄 Fluxos Identificados
1. **Cadastro novo usuário** - Landing → Formulário → Onboarding
2. **Login existente** - Landing → Login → Dashboard
3. **Navegação completa** - Todas as páginas internas

---

### 3. Dashboard/Feed (Pós-Login) ✅
**URL:** `http://localhost:8080/feed`

**Elementos da barra superior mapeados:**
```typescript
const dashboardSelectors = {
  header: {
    logo: 'link:has-text("Vindula Cosmos")',
    toggleSidebar: 'button:has-text("Toggle Sidebar")',
    searchCenter: 'button:has-text("Central de Busca")',
    levelIndicator: 'link:has-text("Nível 3")',
    stardustCounter: 'link:has-text("385")',
    userAvatar: 'getByText("F", { exact: true })'
  },
  userMenu: {
    trigger: 'getByText("F", { exact: true })',
    profile: 'text=Meu Perfil',
    plan: 'text=Plano',
    help: 'text=Central de Ajuda',
    logout: 'text=Sair da conta'
  }
}
```

**Funcionalidade de LOGOUT mapeada:** ✅
```typescript
// FLUXO COMPLETO DE LOGOUT
async fazerLogout() {
  // 1. Clicar no avatar "F" no canto superior direito
  await page.getByText('F', { exact: true }).click();
  
  // 2. Menu contextual abre com opções do perfil
  await expect(page.locator('text=Sair da conta')).toBeVisible();
  
  // 3. Clicar em "Sair da conta"
  await page.click('text=Sair da conta');
  
  // 4. Aguardar redirecionamento para página inicial/login
  await page.waitForTimeout(2000);
}
```

**Menu Sidebar identificado:** ✅
- **Principal:** Feed, Knowledge Hub, Chat
- **Produtividade:** Biblioteca, Obrigações  
- **Colaboração:** People Hub, Eventos
- **Gamificação:** Ranking, Marketplace, Minhas Compras
- **Administração:** Painel de Controle, Relatórios, Portal LGPD

---

## 📝 Próximos passos
- [x] ✅ Mapear funcionalidade de logout - **CONCLUÍDO**
- [x] ✅ Identificar estrutura do menu sidebar - **CONCLUÍDO**
- [ ] Testar fluxo completo login → logout
- [ ] Mapear navegação entre páginas do menu sidebar
- [ ] Documentar todas as páginas internas (Feed, Knowledge Hub, etc.)
- [ ] Criar testes de funcionalidades específicas de cada página