import { test, expect } from '@playwright/test';
import { NavigationSteps } from './steps/navigation-steps';

/**
 * Vindula Cosmos E2E Test Suite - Steps Modulares
 * <AUTHOR> Internet 2025
 */

// Dados de teste
const TEST_DATA = {
  users: {
    valid: {
      email: '<EMAIL>',
      password: 'senha123' // Será atualizado com credenciais reais
    },
    invalid: {
      email: '<EMAIL>',
      password: 'senh<PERSON><PERSON><PERSON>'
    }
  }
};

test.describe('🚀 STEPS INDIVIDUAIS - Landing Page', () => {
  
  test('STEP 1: deve carregar página inicial', async ({ page }) => {
    const nav = new NavigationSteps(page);
    await nav.goToLandingPage();
  });

  test('STEP 2: deve navegar para login', async ({ page }) => {
    const nav = new NavigationSteps(page);
    await nav.goToLandingPage();
    await nav.goToLoginPage();
  });
});

test.describe('🔐 STEPS INDIVIDUAIS - Authentication', () => {
  
  test('STEP 3: deve preencher formulário', async ({ page }) => {
    const nav = new NavigationSteps(page);
    await nav.goToLandingPage();
    await nav.goToLoginPage();
    await nav.fillLoginForm(TEST_DATA.users.valid.email, TEST_DATA.users.valid.password);
  });

  test('STEP 4: deve submeter login', async ({ page }) => {
    const nav = new NavigationSteps(page);
    await nav.goToLandingPage();
    await nav.goToLoginPage();
    await nav.fillLoginForm(TEST_DATA.users.valid.email, TEST_DATA.users.valid.password);
    await nav.submitLogin();
    // Note: Este teste pode falhar se credenciais estiverem incorretas
  });
});

test.describe('🔄 FLUXOS COMPLETOS', () => {
  
  test('FLUXO: Login bem-sucedido', async ({ page }) => {
    const nav = new NavigationSteps(page);
    await nav.completeLoginFlow(TEST_DATA.users.valid.email, TEST_DATA.users.valid.password);
  });

  test('FLUXO: Login com erro', async ({ page }) => {
    const nav = new NavigationSteps(page);
    await nav.completeLoginErrorFlow(TEST_DATA.users.invalid.email, TEST_DATA.users.invalid.password);
  });
});

test.describe('🧪 TESTES ESPECÍFICOS', () => {
  
  test('deve validar elementos da landing page', async ({ page }) => {
    const nav = new NavigationSteps(page);
    await nav.goToLandingPage();
    
    // Verificar cards de benefícios
    await expect(page.locator('text=300%')).toBeVisible();
    await expect(page.locator('text=15 IAs')).toBeVisible();
    await expect(page.locator('text=5 min')).toBeVisible();
  });

  test('deve validar elementos da página de login', async ({ page }) => {
    const nav = new NavigationSteps(page);
    await nav.goToLandingPage();
    await nav.goToLoginPage();
    
    // Verificar elementos específicos
    await expect(page.locator('button:has-text("Continuar com Google")')).toBeVisible();
    await expect(page.locator('button:has-text("Continuar com Microsoft")')).toBeVisible();
    await expect(page.locator('text=Esqueceu sua senha?')).toBeVisible();
    await expect(page.locator('text=Crie uma conta')).toBeVisible();
  });
});