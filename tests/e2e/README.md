# 🧪 Vindula Cosmos E2E Testing

## 📁 Estrutura Modular

```
tests/e2e/
├── steps/                    # Steps modulares reutilizáveis
│   └── navigation-steps.ts   # Steps de navegação e autenticação
├── vindula-e2e.spec.ts      # Suite de testes principal
├── test-mapping.md          # Documentação de mapeamento
└── README.md               # Este arquivo
```

## 🎯 Filosofia dos Steps

Cada **step** é um teste individual que pode ser executado sozinho ou em sequência:

### **Steps Individuais:**
- `STEP 1`: Carregar landing page
- `STEP 2`: Navegar para login  
- `STEP 3`: Preencher formulário
- `STEP 4`: Submeter login
- `STEP 5`: Verificar sucesso
- `STEP 6`: Verificar erro

### **Fluxos Completos:**
- **Login bem-sucedido**: Steps 1→2→3→4→5
- **Login com erro**: Steps 1→2→3→4→6

## 🚀 Como Executar

### Executar todos os testes:
```bash
npx playwright test tests/e2e/
```

### Executar step específico:
```bash
npx playwright test -g "STEP 1"
npx playwright test -g "STEP 2"
```

### Executar fluxo completo:
```bash
npx playwright test -g "FLUXO: Login bem-sucedido"
```

### Executar com interface visual:
```bash
npx playwright test --ui
```

## 📊 Vantagens desta Estrutura

### ✅ **Debugging Preciso**
- Se falhar no Step 3, sabemos que o problema é no preenchimento
- Se falhar no Step 5, sabemos que é no redirecionamento

### ✅ **Reutilização**
- Steps podem ser combinados para diferentes cenários
- Novos fluxos usam steps existentes

### ✅ **Manutenção**
- Mudança na UI = atualizar apenas o step afetado
- Seletores centralizados na classe NavigationSteps

### ✅ **Desenvolvimento Incremental**
- Desenvolver um step por vez
- Testar conforme navega pela aplicação

## 🔧 Próximos Steps a Mapear

1. **Dashboard** - após login bem-sucedido
2. **Menu lateral** - navegação principal
3. **Criação de posts** - funcionalidade core
4. **Gamificação** - medalhas, níveis, XP
5. **Configurações** - perfil, empresa
6. **Biblioteca** - uploads, documentos
7. **Relatórios** - analytics, métricas

## 📝 Atualizando Credenciais

No arquivo `vindula-e2e.spec.ts`, atualizar:
```typescript
const TEST_DATA = {
  users: {
    valid: {
      email: 'EMAIL_REAL_AQUI',
      password: 'SENHA_REAL_AQUI'
    }
  }
};
```

## 🎮 Executando Interativamente

Durante o mapeamento, execute steps individuais para validar:
```bash
# Testar navegação básica
npx playwright test -g "STEP 1" --headed

# Testar preenchimento de formulário  
npx playwright test -g "STEP 3" --headed

# Testar fluxo completo
npx playwright test -g "FLUXO" --headed
```