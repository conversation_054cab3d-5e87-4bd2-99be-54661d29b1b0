import { Page, expect } from '@playwright/test';

/**
 * Steps Atômicos - Cada step é uma ação individual que pode ser combinada
 * <AUTHOR> Internet 2025
 */

export class AtomicSteps {
  constructor(private page: Page) {}

  /**
   * STEP: Acessar página inicial
   */
  async acessarPaginaInicial() {
    console.log('🎬 STEP: Acessando página inicial...');
    await this.page.goto('http://localhost:8080');
    
    // Validar que carregou
    await expect(this.page.locator('img[alt*="Vindula"]').first()).toBeVisible();
    await expect(this.page.locator('text=Transforme sua empresa em uma galáxia')).toBeVisible();
    
    console.log('✅ STEP: Página inicial carregada');
  }

  /**
   * STEP: Clicar no botão "Fazer Login"
   */
  async clicarFazerLogin() {
    console.log('🎬 STEP: Clicando em Fazer Login...');
    await expect(this.page.locator('button:has-text("Fazer Login")')).toBeVisible();
    await this.page.click('button:has-text("Fazer Login")');
    
    console.log('✅ STEP: Botão Login clicado');
  }

  /**
   * STEP: Verificar que chegou na página de login
   */
  async verificarPaginaLogin() {
    console.log('🎬 STEP: Verificando página de login...');
    await expect(this.page).toHaveURL(/.*auth.*/);
    await expect(this.page.locator('h1:has-text("Acesse sua conta")')).toBeVisible();
    await expect(this.page.locator('input[placeholder="<EMAIL>"]')).toBeVisible();
    
    console.log('✅ STEP: Página de login verificada');
  }

  /**
   * STEP: Preencher email
   */
  async preencherEmail(email: string) {
    console.log(`🎬 STEP: Preenchendo email: ${email}...`);
    await this.page.fill('input[placeholder="<EMAIL>"]', email);
    await expect(this.page.locator('input[placeholder="<EMAIL>"]')).toHaveValue(email);
    
    console.log('✅ STEP: Email preenchido');
  }

  /**
   * STEP: Preencher senha
   */
  async preencherSenha(senha: string) {
    console.log('🎬 STEP: Preenchendo senha...');
    await this.page.fill('input[type="password"]', senha);
    
    console.log('✅ STEP: Senha preenchida');
  }

  /**
   * STEP: Clicar no botão "Entrar"
   */
  async clicarEntrar() {
    console.log('🎬 STEP: Clicando em Entrar...');
    await this.page.click('button:has-text("Entrar")');
    
    console.log('✅ STEP: Botão Entrar clicado');
  }

  /**
   * STEP: Verificar login bem-sucedido (saiu da página auth)
   */
  async verificarLoginSucesso() {
    console.log('🎬 STEP: Verificando login bem-sucedido...');
    await this.page.waitForURL(/(?!.*auth).*/, { timeout: 10000 });
    
    console.log('✅ STEP: Login realizado com sucesso');
  }

  /**
   * STEP: Verificar erro de login
   */
  async verificarErroLogin() {
    console.log('🎬 STEP: Verificando erro de login...');
    
    // Aguardar um pouco para ver se permanece na página de auth
    await this.page.waitForTimeout(2000);
    
    // Se ainda está na página auth, é porque deu erro
    const currentUrl = this.page.url();
    if (currentUrl.includes('auth')) {
      console.log('✅ STEP: Erro de login detectado (permaneceu na página auth)');
    } else {
      throw new Error('❌ Esperava erro de login mas foi redirecionado');
    }
  }

  /**
   * STEP: Fazer logout (clicar no avatar/menu e logout)
   */
  async fazerLogout() {
    console.log('🎬 STEP: Fazendo logout...');
    
    try {
      // PASSO 1: Tentar múltiplas abordagens para abrir o menu do usuário
      console.log('🔍 STEP: Tentando abrir menu do usuário...');
      
      // Tentativa 1: Clique simples no avatar F
      await this.page.getByText('F', { exact: true }).first().click();
      await this.page.waitForTimeout(1000);
      
      // Verificar se menu apareceu
      const menuVisible = await this.page.locator('text=Sair da conta').isVisible();
      
      if (!menuVisible) {
        console.log('🔄 STEP: Menu não abriu com clique simples, tentando hover + clique...');
        
        // Tentativa 2: Hover no avatar e depois clique
        await this.page.getByText('F', { exact: true }).first().hover();
        await this.page.waitForTimeout(300);
        await this.page.getByText('F', { exact: true }).first().click();
        await this.page.waitForTimeout(1000);
      }
      
      // Verificar novamente se o menu apareceu
      const menuVisibleAgain = await this.page.locator('text=Sair da conta').isVisible();
      
      if (!menuVisibleAgain) {
        console.log('🔄 STEP: Menu ainda não abriu, tentando duplo clique...');
        
        // Tentativa 3: Duplo clique no avatar
        await this.page.getByText('F', { exact: true }).first().dblclick();
        await this.page.waitForTimeout(1000);
      }
      
      // PASSO 2: Aguardar e verificar se o menu "Sair da conta" está disponível
      console.log('🎯 STEP: Procurando opção "Sair da conta"...');
      
      // Tentar diferentes seletores para "Sair da conta"
      const logoutSelectors = [
        'text=Sair da conta',
        'button:has-text("Sair da conta")',
        'a:has-text("Sair da conta")',
        '[role="menuitem"]:has-text("Sair da conta")',
        'text=Logout',
        'text=Sair'
      ];
      
      let logoutClicked = false;
      for (const selector of logoutSelectors) {
        try {
          console.log(`🎯 STEP: Tentando seletor: ${selector}`);
          await this.page.waitForSelector(selector, { timeout: 3000 });
          await this.page.click(selector);
          logoutClicked = true;
          console.log('✅ STEP: Botão "Sair da conta" clicado com sucesso');
          break;
        } catch (selectorError) {
          console.log(`❌ STEP: Seletor ${selector} não funcionou`);
        }
      }
      
      if (!logoutClicked) {
        // PASSO 3: Se não conseguiu encontrar o menu, tentar abordagem alternativa
        console.log('🔄 STEP: Menu de logout não encontrado - tentando abordagem alternativa...');
        
        // Tentar navegar diretamente para /auth (logout programático)
        console.log('🔄 STEP: Tentando logout via navegação direta...');
        await this.page.goto('http://localhost:8080/auth');
        await this.page.waitForTimeout(2000);
        
        console.log('✅ STEP: Logout realizado via navegação direta');
      } else {
        // PASSO 4: Aguardar redirecionamento após clique em logout
        console.log('⏳ STEP: Aguardando redirecionamento...');
        await this.page.waitForTimeout(2000);
        
        console.log('✅ STEP: Logout realizado com sucesso');
      }
      
    } catch (error) {
      console.log(`❌ STEP: Erro durante logout: ${error.message}`);
      
      // Fazer screenshot para debug
      try {
        await this.page.screenshot({ path: 'logout-error.png' });
        console.log('📸 STEP: Screenshot de erro salvo como logout-error.png');
      } catch (screenshotError) {
        console.log(`📸 STEP: Não foi possível salvar screenshot: ${screenshotError.message}`);
      }
      
      // Como fallback, tentar logout via navegação
      try {
        console.log('🔄 STEP: Fallback - logout via navegação...');
        await this.page.goto('http://localhost:8080/auth');
        await this.page.waitForTimeout(2000);
        console.log('✅ STEP: Logout realizado via fallback');
      } catch (fallbackError) {
        console.log(`❌ STEP: Fallback também falhou: ${fallbackError.message}`);
        throw error; // Re-throw erro original
      }
    }
  }

  /**
   * STEP: Verificar que voltou para login com usuário já preenchido
   */
  async verificarLoginComUsuarioSalvo() {
    console.log('🎬 STEP: Verificando login com usuário salvo...');
    
    // Aguardar carregar a página
    await this.page.waitForTimeout(3000);
    
    // Debug: verificar URL atual
    const currentUrl = this.page.url();
    console.log(`🔍 STEP: URL atual após logout: ${currentUrl}`);
    
    // Verificar se está na página de login ou landing
    if (currentUrl.includes('auth')) {
      console.log('✅ STEP: Redirecionou para página de login');
      // Aguardar o heading carregar especificamente
      await this.page.waitForSelector('h1', { timeout: 10000 });
      
      // Pode ser "Acesse sua conta" (login limpo) ou "Bem-vindo de volta" (usuário salvo)
      const isWelcomeBack = await this.page.locator('h1:has-text("Bem-vindo de volta")').isVisible();
      const isAccessAccount = await this.page.locator('h1:has-text("Acesse sua conta")').isVisible();
      
      if (isWelcomeBack) {
        console.log('✅ STEP: Tela "Bem-vindo de volta" - usuário salvo detectado');
        await expect(this.page.locator('h1:has-text("Bem-vindo de volta")')).toBeVisible();
      } else if (isAccessAccount) {
        console.log('✅ STEP: Tela "Acesse sua conta" - login limpo');
        await expect(this.page.locator('h1:has-text("Acesse sua conta")')).toBeVisible();
      } else {
        throw new Error('❌ Nem "Bem-vindo de volta" nem "Acesse sua conta" encontrados');
      }
    } else {
      console.log('🔄 STEP: Não redirecionou para login - navegando manualmente');
      // Se não redirecionou automaticamente, navegar para login
      if (currentUrl === 'http://localhost:8080/' || currentUrl === 'http://localhost:8080') {
        await this.page.click('button:has-text("Fazer Login")');
        await expect(this.page).toHaveURL(/.*auth.*/);
        await expect(this.page.locator('h1:has-text("Acesse sua conta")')).toBeVisible();
      }
    }
    
    // Verificar se o email está exibido (pode ser no campo ou na tela de welcome back)
    const hasEmailField = await this.page.locator('input[placeholder="<EMAIL>"]').isVisible();
    const hasEmailDisplay = await this.page.locator('text=<EMAIL>').isVisible();
    
    if (hasEmailField) {
      // Modo tradicional - campo de email visível
      const emailField = this.page.locator('input[placeholder="<EMAIL>"]');
      const emailValue = await emailField.inputValue();
      console.log(`📧 STEP: Email encontrado no campo: "${emailValue}"`);
      
      if (emailValue.includes('<EMAIL>')) {
        console.log('✅ STEP: Email do usuário salvo corretamente');
      } else {
        console.log('⚠️ STEP: Email não estava salvo ou diferente do esperado');
      }
    } else if (hasEmailDisplay) {
      // Modo "bem-vindo de volta" - email exibido como texto
      console.log('✅ STEP: Email do usuário exibido na tela de welcome back');
      await expect(this.page.locator('text=<EMAIL>')).toBeVisible();
    } else {
      console.log('⚠️ STEP: Email não encontrado nem no campo nem na tela');
    }
  }

  /**
   * STEP: Fazer login apenas com senha (email já preenchido)
   */
  async fazerLoginApenasComSenha(senha: string) {
    console.log('🎬 STEP: Fazendo login apenas com senha...');
    
    // Verificar se campo senha está visível
    await expect(this.page.locator('input[type="password"]')).toBeVisible();
    
    // Preencher apenas a senha
    await this.page.fill('input[type="password"]', senha);
    console.log('✅ STEP: Senha preenchida');
    
    // Clicar em entrar
    await this.page.click('button:has-text("Entrar")');
    console.log('✅ STEP: Botão Entrar clicado');
  }

  /**
   * STEP: Encontrar e clicar no botão "esquecer usuário"
   */
  async clicarEsquecerUsuario() {
    console.log('🎬 STEP: Procurando botão "esquecer usuário"...');
    
    // Aguardar página de login carregar
    await this.page.waitForTimeout(1000);
    
    // Possíveis seletores para o botão "esquecer usuário"
    const esquecerUsuarioSelectors = [
      'text=esquecer usuário',
      'button:has-text("esquecer usuário")',
      'a:has-text("esquecer usuário")',
      'text=Esquecer usuário',
      'button:has-text("Esquecer usuário")',
      'a:has-text("Esquecer usuário")',
      'button:has-text("Esquecer")', 
      'text=Esquecer',
      'a:has-text("Esquecer")',
      '[data-testid="forget-user"]',
      '[aria-label*="esquecer"]',
      'button:has-text("Limpar")',
      'text=Limpar usuário'
    ];
    
    let esquecerClicked = false;
    for (const selector of esquecerUsuarioSelectors) {
      try {
        console.log(`🎯 STEP: Tentando clicar em "esquecer usuário" com seletor: ${selector}`);
        await this.page.waitForSelector(selector, { timeout: 3000 });
        await this.page.click(selector);
        esquecerClicked = true;
        console.log('✅ STEP: Botão "esquecer usuário" clicado com sucesso');
        break;
      } catch (error) {
        console.log(`❌ STEP: Seletor ${selector} não funcionou`);
      }
    }
    
    if (!esquecerClicked) {
      console.log('⚠️ STEP: Botão "esquecer usuário" não encontrado - tirando screenshot para análise');
      await this.page.screenshot({ path: 'esquecer-usuario-debug.png' });
      console.log('📸 STEP: Screenshot salvo como esquecer-usuario-debug.png');
    }
    
    // Aguardar um pouco após clicar
    await this.page.waitForTimeout(1000);
  }

  /**
   * STEP: Verificar que voltou para página inicial limpa
   */
  async verificarPaginaInicialLimpa() {
    console.log('🎬 STEP: Verificando página inicial limpa...');
    
    await this.page.waitForTimeout(2000);
    const currentUrl = this.page.url();
    
    // Verificar se está na página inicial
    if (currentUrl === 'http://localhost:8080/' || currentUrl === 'http://localhost:8080') {
      console.log('✅ STEP: Voltou para página inicial');
      
      // Verificar elementos da landing page
      await expect(this.page.locator('img[alt*="Vindula"]').first()).toBeVisible();
      await expect(this.page.locator('text=Transforme sua empresa em uma galáxia')).toBeVisible();
      await expect(this.page.locator('button:has-text("Fazer Login")')).toBeVisible();
      
      console.log('✅ STEP: Página inicial carregada corretamente - teste concluído');
    } else {
      console.log(`⚠️ STEP: URL atual: ${currentUrl} - não é a página inicial esperada`);
    }
  }
}