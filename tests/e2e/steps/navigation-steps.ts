import { Page, expect } from '@playwright/test';

/**
 * Steps modulares para navegação Vindula Cosmos
 * Cada step pode ser executado individualmente ou em sequência
 * <AUTHOR> Internet 2025
 */

export class NavigationSteps {
  constructor(private page: Page) {}

  /**
   * STEP 1: Acessar página inicial
   */
  async goToLandingPage() {
    await this.page.goto('http://localhost:8080');
    
    // Validações - usando seletores mais específicos para evitar duplicatas
    await expect(this.page.locator('img[alt*="Vindula"]').first()).toBeVisible();
    await expect(this.page.locator('text=Transforme sua empresa em uma galáxia')).toBeVisible();
    await expect(this.page.locator('button:has-text("Fazer Login")')).toBeVisible();
    await expect(this.page.locator('button:has-text("Começar")').first()).toBeVisible();
    
    console.log('✅ STEP 1: Landing page carregada');
  }

  /**
   * STEP 2: Navegar para página de login
   */
  async goToLoginPage() {
    await this.page.click('button:has-text("Fazer Login")');
    
    // Validações
    await expect(this.page).toHaveURL(/.*auth.*/);
    await expect(this.page.locator('h1:has-text("Acesse sua conta")')).toBeVisible();
    await expect(this.page.locator('input[placeholder="<EMAIL>"]')).toBeVisible();
    await expect(this.page.locator('input[type="password"]')).toBeVisible();
    
    console.log('✅ STEP 2: Página de login carregada');
  }

  /**
   * STEP 3: Preencher formulário de login
   */
  async fillLoginForm(email: string, password: string) {
    await this.page.fill('input[placeholder="<EMAIL>"]', email);
    await this.page.fill('input[type="password"]', password);
    
    // Validações
    await expect(this.page.locator('input[placeholder="<EMAIL>"]')).toHaveValue(email);
    
    console.log('✅ STEP 3: Formulário preenchido');
  }

  /**
   * STEP 4: Submeter login
   */
  async submitLogin() {
    await this.page.click('button:has-text("Entrar")');
    
    console.log('✅ STEP 4: Login submetido');
  }

  /**
   * STEP 5: Verificar login bem-sucedido (vai para dashboard)
   */
  async verifyLoginSuccess() {
    // Aguardar redirecionamento para dashboard ou home interna
    await this.page.waitForURL(/(?!.*auth).*/, { timeout: 10000 });
    
    console.log('✅ STEP 5: Login realizado com sucesso');
  }

  /**
   * STEP 6: Verificar erro de login
   */
  async verifyLoginError() {
    // Procurar por mensagens de erro
    const errorSelectors = [
      'text=Credenciais inválidas',
      'text=Email ou senha incorretos',
      'text=Erro',
      '[role="alert"]'
    ];
    
    let errorFound = false;
    for (const selector of errorSelectors) {
      try {
        await this.page.waitForSelector(selector, { timeout: 3000 });
        errorFound = true;
        break;
      } catch {}
    }
    
    if (errorFound) {
      console.log('✅ STEP 6: Erro de login detectado corretamente');
    } else {
      console.log('⚠️ STEP 6: Nenhuma mensagem de erro encontrada');
    }
  }

  /**
   * FLUXO COMPLETO: Login bem-sucedido
   */
  async completeLoginFlow(email: string, password: string) {
    await this.goToLandingPage();
    await this.goToLoginPage();
    await this.fillLoginForm(email, password);
    await this.submitLogin();
    await this.verifyLoginSuccess();
  }

  /**
   * FLUXO COMPLETO: Teste de login com erro
   */
  async completeLoginErrorFlow(email: string, password: string) {
    await this.goToLandingPage();
    await this.goToLoginPage();
    await this.fillLoginForm(email, password);
    await this.submitLogin();
    await this.verifyLoginError();
  }
}