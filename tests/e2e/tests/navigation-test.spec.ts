import { test, expect } from '@playwright/test';
import { AtomicSteps } from '../steps/atomic-steps';
import { NavigationSteps } from '../steps/navigation-steps';

/**
 * Testes de Navegação Completa - Explorar todas as páginas da aplicação
 * <AUTHOR> Internet 2025
 */

const validCredentials = {
  email: '<EMAIL>',
  password: '222488'
};

test.describe('🧭 NAVEGAÇÃO COMPLETA: Explorar toda a aplicação', () => {
  let atomicSteps: AtomicSteps;
  let navigationSteps: NavigationSteps;

  test.beforeEach(async ({ page }) => {
    atomicSteps = new AtomicSteps(page);
    navigationSteps = new NavigationSteps(page);
    
    // Fazer login antes de cada teste de navegação
    console.log('🔑 SETUP: Fazendo login para explorar aplicação...');
    await navigationSteps.completeLoginFlow(validCredentials.email, validCredentials.password);
    console.log('✅ SETUP: Login concluído - pronto para navegar');
  });

  test('📍 MAPEAMENTO: Explorar seção PRINCIPAL do menu', async ({ page }) => {
    console.log('🎯 INICIANDO: Exploração da seção PRINCIPAL');
    
    // Aguardar dashboard carregar
    await page.waitForTimeout(3000);
    
    // 1. Feed (página atual após login)
    console.log('📄 PÁGINA: Feed (atual)');
    await expect(page.locator('text=Feed')).toBeVisible();
    await page.screenshot({ path: 'navigation-feed.png' });
    console.log('📸 Screenshot: Feed capturado');
    
    // 2. Navegar para Posts
    console.log('📄 PÁGINA: Posts');
    try {
      await page.click('text=Posts');
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'navigation-posts.png' });
      console.log('📸 Screenshot: Posts capturado');
    } catch (error) {
      console.log(`⚠️ Posts não encontrado: ${error.message}`);
    }
    
    // 3. Navegar para Chat
    console.log('📄 PÁGINA: Chat');
    try {
      await page.click('text=Chat');
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'navigation-chat.png' });
      console.log('📸 Screenshot: Chat capturado');
    } catch (error) {
      console.log(`⚠️ Chat não encontrado: ${error.message}`);
    }
    
    // 4. Navegar para Videochamadas
    console.log('📄 PÁGINA: Videochamadas');
    try {
      await page.click('text=Videochamadas');
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'navigation-videochamadas.png' });
      console.log('📸 Screenshot: Videochamadas capturado');
    } catch (error) {
      console.log(`⚠️ Videochamadas não encontrado: ${error.message}`);
    }
    
    console.log('✅ SEÇÃO PRINCIPAL: Exploração concluída');
  });

  test('📊 MAPEAMENTO: Explorar seção PRODUTIVIDADE do menu', async ({ page }) => {
    console.log('🎯 INICIANDO: Exploração da seção PRODUTIVIDADE');
    
    await page.waitForTimeout(3000);
    
    // 1. Biblioteca
    console.log('📄 PÁGINA: Biblioteca');
    try {
      await page.click('text=Biblioteca');
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'navigation-biblioteca.png' });
      console.log('📸 Screenshot: Biblioteca capturado');
    } catch (error) {
      console.log(`⚠️ Biblioteca não encontrado: ${error.message}`);
    }
    
    // 2. Obrigações
    console.log('📄 PÁGINA: Obrigações');
    try {
      await page.click('text=Obrigações');
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'navigation-obrigacoes.png' });
      console.log('📸 Screenshot: Obrigações capturado');
    } catch (error) {
      console.log(`⚠️ Obrigações não encontrado: ${error.message}`);
    }
    
    console.log('✅ SEÇÃO PRODUTIVIDADE: Exploração concluída');
  });

  test('🤝 MAPEAMENTO: Explorar seção COLABORAÇÃO do menu', async ({ page }) => {
    console.log('🎯 INICIANDO: Exploração da seção COLABORAÇÃO');
    
    await page.waitForTimeout(3000);
    
    // 1. People Hub
    console.log('📄 PÁGINA: People Hub');
    try {
      await page.click('text=People Hub');
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'navigation-people-hub.png' });
      console.log('📸 Screenshot: People Hub capturado');
    } catch (error) {
      console.log(`⚠️ People Hub não encontrado: ${error.message}`);
    }
    
    // 2. Eventos
    console.log('📄 PÁGINA: Eventos');
    try {
      await page.click('text=Eventos');
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'navigation-eventos.png' });
      console.log('📸 Screenshot: Eventos capturado');
    } catch (error) {
      console.log(`⚠️ Eventos não encontrado: ${error.message}`);
    }
    
    console.log('✅ SEÇÃO COLABORAÇÃO: Exploração concluída');
  });

  test('🎮 MAPEAMENTO: Explorar seção GAMIFICAÇÃO do menu', async ({ page }) => {
    console.log('🎯 INICIANDO: Exploração da seção GAMIFICAÇÃO');
    
    await page.waitForTimeout(3000);
    
    // Procurar por elementos relacionados a gamificação
    const gamificationSelectors = [
      'text=Níveis',
      'text=Medalhas', 
      'text=Ranking',
      'text=Stardust',
      'text=Achievements',
      'text=Missões',
      'text=Gamificação'
    ];
    
    for (const selector of gamificationSelectors) {
      try {
        console.log(`📄 PÁGINA: Tentando ${selector}`);
        await page.click(selector);
        await page.waitForTimeout(2000);
        const filename = `navigation-${selector.replace('text=', '').toLowerCase()}.png`;
        await page.screenshot({ path: filename });
        console.log(`📸 Screenshot: ${selector} capturado`);
      } catch (error) {
        console.log(`⚠️ ${selector} não encontrado: ${error.message}`);
      }
    }
    
    console.log('✅ SEÇÃO GAMIFICAÇÃO: Exploração concluída');
  });

  test('⚙️ MAPEAMENTO: Explorar seção ADMINISTRAÇÃO do menu', async ({ page }) => {
    console.log('🎯 INICIANDO: Exploração da seção ADMINISTRAÇÃO');
    
    await page.waitForTimeout(3000);
    
    // Procurar por elementos relacionados a administração
    const adminSelectors = [
      'text=Configurações',
      'text=Usuários',
      'text=Empresa',
      'text=Administração',
      'text=Admin',
      'text=Settings'
    ];
    
    for (const selector of adminSelectors) {
      try {
        console.log(`📄 PÁGINA: Tentando ${selector}`);
        await page.click(selector);
        await page.waitForTimeout(2000);
        const filename = `navigation-${selector.replace('text=', '').toLowerCase()}.png`;
        await page.screenshot({ path: filename });
        console.log(`📸 Screenshot: ${selector} capturado`);
      } catch (error) {
        console.log(`⚠️ ${selector} não encontrado: ${error.message}`);
      }
    }
    
    console.log('✅ SEÇÃO ADMINISTRAÇÃO: Exploração concluída');
  });

  test('🔍 EXPLORAÇÃO COMPLETA: Mapear todos os elementos do sidebar', async ({ page }) => {
    console.log('🎯 INICIANDO: Exploração completa do sidebar');
    
    await page.waitForTimeout(3000);
    
    // Capturar screenshot do sidebar completo
    await page.screenshot({ path: 'navigation-sidebar-completo.png' });
    console.log('📸 Screenshot: Sidebar completo capturado');
    
    // Listar todos os elementos clicáveis do sidebar
    console.log('🔍 ANÁLISE: Coletando todos os links do sidebar...');
    
    const allLinks = await page.$$eval('nav a, nav button, [role="navigation"] a, [role="navigation"] button', elements => {
      return elements.map(el => ({
        text: el.textContent?.trim() || '',
        href: el.getAttribute('href') || '',
        tag: el.tagName.toLowerCase(),
        classes: el.className || ''
      })).filter(item => item.text.length > 0);
    });
    
    console.log('📋 ELEMENTOS ENCONTRADOS NO SIDEBAR:');
    allLinks.forEach((link, index) => {
      console.log(`${index + 1}. "${link.text}" (${link.tag}) - ${link.href || 'sem href'}`);
    });
    
    // Tentar clicar em cada elemento encontrado
    for (const [index, link] of allLinks.entries()) {
      if (link.text && link.text.length > 2) { // Ignorar textos muito curtos
        try {
          console.log(`🖱️ CLICANDO: "${link.text}" (${index + 1}/${allLinks.length})`);
          await page.click(`text=${link.text}`);
          await page.waitForTimeout(1500);
          
          const filename = `navigation-auto-${index + 1}-${link.text.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}.png`;
          await page.screenshot({ path: filename });
          console.log(`📸 Screenshot: ${link.text} capturado`);
        } catch (error) {
          console.log(`⚠️ Não foi possível clicar em "${link.text}": ${error.message}`);
        }
      }
    }
    
    console.log('✅ EXPLORAÇÃO COMPLETA: Todos os elementos do sidebar testados');
  });

  test.afterEach(async ({ page }) => {
    // Fazer logout após cada teste
    console.log('🧹 CLEANUP: Fazendo logout...');
    try {
      await atomicSteps.fazerLogout();
      console.log('✅ CLEANUP: Logout concluído');
    } catch (error) {
      console.log(`⚠️ CLEANUP: Erro no logout: ${error.message}`);
    }
  });
});