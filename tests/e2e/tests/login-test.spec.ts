import { test } from '@playwright/test';
import { AtomicSteps } from '../steps/atomic-steps';

/**
 * TESTE COMPLETO: Login com usuário e senha
 * Comb<PERSON> múltiplos steps atômicos para formar um teste completo
 * <AUTHOR> Internet 2025
 */

const TEST_DATA = {
  validUser: {
    email: '<EMAIL>',
    password: '222488'
  },
  invalidUser: {
    email: '<EMAIL>',
    password: 'senh<PERSON><PERSON><PERSON>'
  }
};

test.describe('🔐 TESTE COMPLETO: Login', () => {
  
  test('LOGIN COMPLETO: Entrada → Login → Logout → Re-login → Logout → Esquecer → Início', async ({ page }) => {
    const steps = new AtomicSteps(page);
    
    console.log('🎯 INICIANDO TESTE COMPLETO: Fluxo completo de autenticação');
    
    // === PRIMEIRA SESSÃO DE LOGIN ===
    console.log('🔹 FASE 1: Login inicial');
    await steps.acessarPaginaInicial();                        // Step 1
    await steps.clicarFazerLogin();                           // Step 2  
    await steps.verificarPaginaLogin();                       // Step 3
    await steps.preencherEmail(TEST_DATA.validUser.email);    // Step 4
    await steps.preencherSenha(TEST_DATA.validUser.password); // Step 5
    await steps.clicarEntrar();                               // Step 6
    await steps.verificarLoginSucesso();                      // Step 7
    
    // === PRIMEIRO LOGOUT ===
    console.log('🔹 FASE 2: Primeiro logout');
    await steps.fazerLogout();                                // Step 8
    await steps.verificarLoginComUsuarioSalvo();              // Step 9
    
    // === SEGUNDO LOGIN (APENAS SENHA) ===
    console.log('🔹 FASE 3: Re-login com usuário salvo');
    await steps.fazerLoginApenasComSenha(TEST_DATA.validUser.password); // Step 10
    await steps.verificarLoginSucesso();                      // Step 11
    
    // === SEGUNDO LOGOUT ===
    console.log('🔹 FASE 4: Segundo logout');
    await steps.fazerLogout();                                // Step 12
    await steps.verificarLoginComUsuarioSalvo();              // Step 13
    
    // === ESQUECER USUÁRIO E VOLTAR AO INÍCIO ===
    console.log('🔹 FASE 5: Esquecer usuário e finalizar');
    await steps.clicarEsquecerUsuario();                      // Step 14
    await steps.verificarPaginaInicialLimpa();                // Step 15
    
    console.log('🎉 TESTE COMPLETO CONCLUÍDO: Todos os fluxos de autenticação testados com sucesso!');
  });

  test('LOGIN INVÁLIDO: Entrada → Login → Erro', async ({ page }) => {
    const steps = new AtomicSteps(page);
    
    console.log('🎯 INICIANDO TESTE: Login com credenciais inválidas');
    
    // COMBINAÇÃO DE STEPS PARA TESTE DE LOGIN INVÁLIDO:
    await steps.acessarPaginaInicial();        // Step 1
    await steps.clicarFazerLogin();            // Step 2
    await steps.verificarPaginaLogin();        // Step 3
    await steps.preencherEmail(TEST_DATA.invalidUser.email);     // Step 4
    await steps.preencherSenha(TEST_DATA.invalidUser.password);  // Step 5
    await steps.clicarEntrar();                // Step 6
    await steps.verificarErroLogin();          // Step 7
    
    console.log('🎉 TESTE CONCLUÍDO: Erro de login detectado corretamente!');
  });
});

test.describe('🧪 STEPS INDIVIDUAIS: Para Debug', () => {
  
  test('DEBUG: Apenas navegação até login', async ({ page }) => {
    const steps = new AtomicSteps(page);
    
    await steps.acessarPaginaInicial();
    await steps.clicarFazerLogin();
    await steps.verificarPaginaLogin();
    
    console.log('🔍 DEBUG: Navegação até login OK');
  });

  test('DEBUG: Até preenchimento de formulário', async ({ page }) => {
    const steps = new AtomicSteps(page);
    
    await steps.acessarPaginaInicial();
    await steps.clicarFazerLogin();
    await steps.verificarPaginaLogin();
    await steps.preencherEmail(TEST_DATA.validUser.email);
    await steps.preencherSenha(TEST_DATA.validUser.password);
    
    console.log('🔍 DEBUG: Preenchimento de formulário OK');
  });
});