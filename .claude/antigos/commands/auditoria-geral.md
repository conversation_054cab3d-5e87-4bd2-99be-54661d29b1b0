# Comando: /auditoria-geral

## ⚠️ **PRÉ-REQUISITO OBRIGATÓRIO**
**ANTES DE EXECUTAR A AUDITORIA, SEMPRE EXECUTE:**
```bash
./scripts/dump-current-schema.sh
```
Este comando gera o dump atual do schema do Supabase que será analisado para verificar políticas RLS, isolamento multi-tenant e vulnerabilidades de segurança no banco de dados.

## Descrição
Realiza uma auditoria de segurança e qualidade completa do Vindula Cosmos, identificando falhas de segurança, problemas de performance, vulnerabilidades, anti-patterns e issues críticos. Gera um relatório evolutivo que se aprimora a cada execução, servindo como porta de entrada para todas as avaliações de segurança do sistema.

## Uso
```
/auditoria-geral [--categoria=<categoria>] [--profundidade=<nivel>] [--incluir-sugestoes]
```

## Exemplos
```
/auditoria-geral
/auditoria-geral --categoria=seguranca
/auditoria-geral --categoria=performance --profundidade=alta
/auditoria-geral --incluir-sugestoes
```

## Categorias de Auditoria

### **1. Segurança Multi-tenant** 🔒
- **RLS Policies**: Verificação de todas as políticas Row Level Security
- **Isolamento de Dados**: Validação de isolamento entre empresas
- **Autenticação**: Análise do sistema de auth e tokens JWT
- **Autorização**: Verificação do sistema de permissões e roles
- **Input Validation**: Checagem de validação de entradas
- **SQL Injection**: Busca por vulnerabilidades de injeção
- **XSS Protection**: Verificação de proteção contra XSS
- **CSRF Protection**: Análise de proteção CSRF
- **Frontend Security**: Verificação de validações inseguras no frontend (ex: `useIsVindulaCompany`)
- **Defense in Depth**: Validação de que políticas de permissão do frontend têm proteção equivalente no backend

### **2. Performance e Escalabilidade** ⚡
- **WebSocket Connections**: Análise de subscriptions simultâneas
- **Memory Leaks**: Detecção de vazamentos de memória
- **Query Performance**: Análise de queries lentas
- **Bundle Size**: Verificação do tamanho do bundle
- **Loading Performance**: Análise de tempos de carregamento
- **Database Indexes**: Verificação de índices otimizados
- **Caching Strategy**: Análise da estratégia de cache

### **3. Qualidade de Código** 📋
- **TypeScript Coverage**: Verificação de tipagem
- **Anti-patterns**: Detecção de padrões problemáticos
- **Code Duplication**: Identificação de duplicação
- **Dependency Analysis**: Análise de dependências
- **Error Handling**: Verificação de tratamento de erros
- **Testing Coverage**: Análise de cobertura de testes
- **Documentation**: Verificação de documentação

### **4. Arquitetura e Padrões** 🏗️
- **CLAUDE.md Compliance**: Aderência aos padrões Vindula
- **Component Structure**: Análise da estrutura de componentes
- **State Management**: Verificação do gerenciamento de estado
- **API Design**: Análise do design das APIs
- **Folder Structure**: Verificação da organização de pastas
- **Naming Conventions**: Verificação de convenções de nomenclatura

### **5. Operacional e DevOps** 🔧
- **Environment Variables**: Verificação de variáveis sensíveis
- **Secrets Management**: Análise do gerenciamento de segredos
- **Error Monitoring**: Verificação de monitoramento de erros
- **Logging Strategy**: Análise da estratégia de logs
- **Backup Strategy**: Verificação de estratégia de backup
- **CI/CD Security**: Análise de segurança do pipeline

## Processo de Execução

### **1. Análise de Schema do Supabase**
```bash
# Passo obrigatório: Gerar dump atual do schema para análise de RLS
./scripts/dump-current-schema.sh

# Schema dump criado em: schema_dumps/YYYYMMDD_HHMMSS/
# - complete_schema_with_data.sql (schema completo)
# - public_schema_only.sql (apenas estrutura do schema público)
# - data_only.sql (apenas dados)
```

### **2. Extração de Sistemas Específicos (Opcional)**
```bash
# Para análise detalhada de sistemas específicos
./scripts/extract-specific-system.sh tasks 'tasks,task_assignments,task_assignment_requests'
./scripts/extract-specific-system.sh chat 'chat_channels,chat_messages,chat_participants'
./scripts/extract-specific-system.sh auth 'profiles,user_roles,companies'

# Arquivos extraídos em: supabase/schemas/[sistema]/tables.sql
```

### **3. Análise Incremental**
```javascript
// O comando evolui baseado em execuções anteriores + schema atual
const previousAudit = await loadPreviousAudit();
const currentSchema = await loadSchemaFromDump();
const newFindings = await performNewAnalysis(currentSchema);
const evolutionReport = await compareWithPrevious(previousAudit, newFindings);
```

### **4. Análise de RLS Policies**
```javascript
// Análise automática das políticas RLS do schema dump
const rlsAnalysis = {
  tabelas_sem_rls: [], // Tabelas sem RLS habilitado
  policies_vulneraveis: [], // Políticas com USING(true) ou similares
  tabelas_sem_company_filter: [], // Sem filtro de company_id
  policies_permissivas: [], // Políticas muito amplas
  missing_policies: [] // Tabelas que deveriam ter políticas específicas
};
```

### **5. Categorização Automática**
```javascript
// Problemas são automaticamente categorizados por severidade
const findings = {
  CRÍTICO: [], // Falhas de segurança graves, vulnerabilidades RLS
  ALTO: [],    // Problemas de performance, memory leaks, policies permissivas
  MÉDIO: [],   // Anti-patterns, duplicação de código, missing policies
  BAIXO: [],   // Melhorias de qualidade, documentação
  INFO: []     // Informações gerais, estatísticas de schema
};
```

### **6. Tracking de Progresso**
```javascript
// Acompanha resolução de problemas anteriores
const progressTracking = {
  resolved: [],    // Problemas resolvidos desde última auditoria
  new: [],         // Novos problemas identificados
  persistent: [],  // Problemas que permanecem
  regressed: [],   // Problemas que voltaram a aparecer
  schema_changes: [] // Mudanças no schema desde última auditoria
};
```

## Template do Relatório de Auditoria

```markdown
# Auditoria de Segurança e Qualidade - Vindula Cosmos

**Data:** {timestamp}  
**Versão:** {version}  
**Auditor:** Vindula Internet 2025  
**Execução:** #{execution_number}

## 📊 Dashboard Executivo

### **🚨 Status Crítico**
| Categoria | Críticos | Altos | Médios | Baixos | Status |
|-----------|----------|-------|--------|--------|--------|
| Segurança | {count} | {count} | {count} | {count} | 🔴/🟡/🟢 |
| Performance | {count} | {count} | {count} | {count} | 🔴/🟡/🟢 |
| Qualidade | {count} | {count} | {count} | {count} | 🔴/🟡/🟢 |

### **📈 Evolução desde Última Auditoria**
- ✅ **Resolvidos**: {count} problemas
- 🆕 **Novos**: {count} problemas  
- ⚠️ **Persistentes**: {count} problemas
- 📉 **Regressões**: {count} problemas

### **🎯 Score de Segurança: {score}/100**
- 🔒 **Multi-tenancy**: {score}/25
- ⚡ **Performance**: {score}/25  
- 📋 **Qualidade**: {score}/25
- 🏗️ **Arquitetura**: {score}/25

---

## 🔒 AUDITORIA DE SEGURANÇA

### **FALHAS CRÍTICAS** 🚨

#### **SEC-001: [Título do Problema]**
- **Severidade**: CRÍTICO
- **Categoria**: RLS Policy / Auth / Input Validation / Frontend Security
- **Localização**: `path/to/file.ts:line`
- **Descrição**: [Descrição detalhada da vulnerabilidade]
- **Impacto**: [Impacto potencial]
- **Recomendação**: [Solução específica]
- **Status**: NOVO / PERSISTENTE / RESOLVIDO
- **CVE/OWASP**: [Referência se aplicável]

```sql
-- Exemplo de código problemático
CREATE POLICY "problematic_policy" ON table_name
FOR ALL TO authenticated
USING (true); -- ❌ CRÍTICO: Sem filtro de company_id
```

```typescript
// Exemplo de validação insegura no frontend
const isVindula = company.slug === 'vindula-intranet'; // ❌ CRÍTICO: Manipulável no frontend

// Exemplo de política de permissão apenas no frontend
if (user.role === 'admin') {
  // ❌ CRÍTICO: Política só no frontend, sem proteção no backend
  return <AdminPanel />;
}
```

**Solução Recomendada:**
```sql
-- ✅ Correção aplicar RLS adequado
CREATE POLICY "secure_policy" ON table_name
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles p
    WHERE p.id = auth.uid()
    AND p.company_id = table_name.company_id
  )
);
```

```typescript
// ✅ Correção: Usar validação server-side
const { data: isVindula } = await supabase.rpc('is_vindula_company');

// ✅ Correção: Policy no backend + verificação no frontend
const { data: adminData } = await supabase
  .from('admin_data')
  .select('*'); // Backend RLS protege automaticamente

// Frontend apenas como UX (não como segurança)
if (user.role === 'admin') {
  return <AdminPanel />;
}
```

```sql
-- ✅ Backend: RLS Policy correspondente para admin_data
CREATE POLICY "admin_only_access" ON admin_data
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles p
    JOIN user_roles ur ON p.id = ur.user_id
    WHERE p.id = auth.uid()
    AND ur.role = 'admin'
  )
);
```

---

## ⚡ AUDITORIA DE PERFORMANCE

### **PROBLEMAS CRÍTICOS** 🔥

#### **PERF-001: [Título do Problema]**
- **Severidade**: ALTO
- **Categoria**: Memory Leak / WebSocket / Query Performance
- **Localização**: `path/to/component.tsx`
- **Métrica**: [Métrica específica - ex: 25 WebSocket connections]
- **Benchmark**: [Valor recomendado - ex: máximo 12 connections]
- **Impacto**: [Descrição do impacto na UX]
- **Status**: NOVO / MELHORADO / PIORADO

---

## 📋 AUDITORIA DE QUALIDADE

### **ANTI-PATTERNS IDENTIFICADOS** ⚠️

#### **QUAL-001: [Título do Problema]**
- **Padrão**: [Nome do anti-pattern]
- **Ocorrências**: {count} arquivos afetados
- **Exemplos**: 
  - `src/component1.tsx:45`
  - `src/component2.tsx:67`

---

## 🏗️ AUDITORIA ARQUITETURAL

### **ADERÊNCIA AO CLAUDE.MD** 📋

#### **Conformidade por Categoria**
| Regra | Status | Conformidade | Violações |
|-------|--------|--------------|-----------|
| TanStack Query apenas via hooks | 🔴 | 75% | 12 arquivos |
| Company ID via useAuthStore | 🟡 | 90% | 3 arquivos |
| RLS em todas tabelas | 🟢 | 100% | 0 violações |

---

## 📊 MÉTRICAS DETALHADAS

### **Estatísticas do Codebase**
```json
{
  "arquivos_analisados": 847,
  "linhas_de_codigo": 89420,
  "typescript_coverage": "94%",
  "componentes_react": 234,
  "hooks_customizados": 67,
  "contextos": 12,
  "paginas": 28,
  "migracao_supabase": 156,
  "testes_e2e": 45
}
```

### **WebSocket Connections Analysis**
```json
{
  "total_subscriptions": 23,
  "por_funcionalidade": {
    "chat": 8,
    "notifications": 5,
    "posts": 4,
    "gamification": 3,
    "auth": 3
  },
  "duplicacoes_identificadas": 7,
  "reducao_possivel": "35%"
}
```

### **Security Score Breakdown**
```json
{
  "rls_policies": {
    "total": 45,
    "seguras": 42,
    "vulneraveis": 3,
    "sem_rls": 2,
    "sem_company_filter": 5,
    "score": 84
  },
  "schema_analysis": {
    "tabelas_analisadas": 89,
    "tabelas_com_dados_empresariais": 67,
    "tabelas_protegidas_rls": 62,
    "cobertura_rls": "92.5%",
    "policies_permissivas": 7
  },
  "input_validation": {
    "endpoints": 67,
    "validados": 61,
    "expostos": 6,
    "score": 91
  },
  "defense_in_depth": {
    "frontend_checks": 25,
    "backend_equivalent": 18,
    "gaps_identified": 7,
    "score": 72
  }
}
```

---

## 🎯 PLANO DE AÇÃO PRIORITÁRIO

### **Fase 0: EMERGENCIAL (< 24h)** 🚨
1. **SEC-001**: Corrigir política RLS exposta em `user_data`
2. **SEC-003**: Validar input em endpoint `/api/admin/*`
3. **PERF-001**: Reduzir WebSocket connections de 23 para < 15

### **Fase 1: CRÍTICO (< 1 semana)** 🔥
1. **PERF-002**: Eliminar memory leaks em FloatingChats
2. **SEC-005**: Implementar rate limiting em APIs públicas
3. **QUAL-001**: Refatorar hooks Posts duplicados

### **Fase 2: ALTO (< 2 semanas)** ⚠️
1. **ARCH-001**: Migrar componentes para padrão enhanced
2. **PERF-004**: Otimizar queries N+1 identificadas
3. **QUAL-003**: Aumentar TypeScript coverage para 98%

### **Fase 3: MÉDIO (< 1 mês)** 📋
1. **DOC-001**: Documentar todas as APIs internas
2. **TEST-001**: Aumentar cobertura E2E para 85%
3. **ARCH-002**: Padronizar estrutura de componentes

---

## 📈 TRACKING DE EVOLUÇÃO

### **Comparação com Auditoria Anterior**

#### **Melhorias Identificadas** ✅
- ✅ **SEC-002**: Política RLS de `profiles` corrigida
- ✅ **PERF-003**: Memory leak em `useRealtimeSubscription` resolvido
- ✅ **QUAL-002**: Duplicação em hooks de auth eliminada

#### **Novos Problemas** 🆕
- 🆕 **SEC-006**: Nova vulnerabilidade em upload de arquivos
- 🆕 **PERF-005**: Degradação de performance em componentes enhanced

#### **Problemas Persistentes** ⚠️
- ⚠️ **PERF-001**: WebSocket connections ainda acima do recomendado
- ⚠️ **QUAL-001**: Anti-patterns em componentes legados permanecem

#### **Regressões** 📉
- 📉 **SEC-004**: Validação de input removida acidentalmente

---

## 🔧 FERRAMENTAS E METODOLOGIA

### **Ferramentas Utilizadas**
- **ESLint Security Plugin**: Análise estática de segurança
- **TypeScript Compiler**: Verificação de tipos
- **Bundle Analyzer**: Análise de tamanho do bundle
- **WebSocket Monitor**: Monitoramento de conexões
- **Supabase CLI**: Verificação de políticas RLS e dump de schema
- **Custom Scripts Vindula**: 
  - `dump-current-schema.sh`: Extração de schema atual
  - `extract-specific-system.sh`: Análise por domínios
- **Schema Analysis Tools**: Parsing automático de RLS policies

### **Metodologia**
1. **Schema Dump**: Gerar dump atual do Supabase (`./scripts/dump-current-schema.sh`)
2. **RLS Analysis**: Análise automática de políticas de segurança no schema
3. **Scan Automatizado**: Análise estática do código frontend
4. **Verificação Manual**: Review de componentes críticos
5. **Cross-Reference**: Comparar políticas RLS com validações frontend
6. **Testing Dinâmico**: Testes de penetração básicos
7. **Schema Comparison**: Comparar schema atual com auditoria anterior
8. **Benchmark Comparison**: Comparação com padrões da indústria
9. **Evolution Tracking**: Comparação com auditorias anteriores

---

## 📞 PRÓXIMOS PASSOS

### **Recomendações Imediatas**
1. 🚨 **Resolver todos os problemas CRÍTICOS** antes de qualquer deploy
2. 🔥 **Implementar monitoramento contínuo** para detectar regressões
3. ⚠️ **Estabelecer gates de qualidade** no CI/CD
4. 📋 **Agendar auditorias regulares** (quinzenais/mensais)

### **Melhorias do Processo**
1. **Automatização**: Integrar auditoria no pipeline de CI/CD
2. **Alertas**: Configurar alertas para problemas críticos
3. **Dashboard**: Criar dashboard em tempo real de métricas de segurança
4. **Treinamento**: Capacitar equipe em security best practices

---

## 📋 APÊNDICES

### **A. Checklist de Segurança Multi-tenant (Base Schema Dump)**
- [ ] **Schema Dump Atual**: Executar `./scripts/dump-current-schema.sh` antes da auditoria
- [ ] **RLS Habilitado**: Todas as tabelas com dados empresariais têm RLS ativo
- [ ] **Company Filter**: Policies filtram por company_id adequadamente  
- [ ] **Análise Automática**: Schema dump analisado para patterns vulneráveis
- [ ] **Funções SQL**: Verificar permissões em stored procedures
- [ ] **APIs**: Validar acesso por empresa em endpoints
- [ ] **JWT**: Tokens verificados consistentemente
- [ ] **Defense in Depth**: Toda política frontend tem proteção backend equivalente
- [ ] **Frontend Security**: Nenhuma validação crítica apenas no frontend
- [ ] **Backend First**: Dados sensíveis protegidos por RLS antes da apresentação
- [ ] **Schema Evolution**: Comparar mudanças no schema desde última auditoria

### **B. Performance Benchmarks**
- WebSocket Connections: < 12 simultâneas
- Memory Usage: < 150MB por sessão
- Bundle Size: < 5MB total
- Initial Load: < 3 segundos
- Navigation: < 500ms

### **C. Glossário de Termos**
- **RLS**: Row Level Security
- **XSS**: Cross-Site Scripting
- **CSRF**: Cross-Site Request Forgery
- **JWT**: JSON Web Token
- **CVE**: Common Vulnerabilities and Exposures

---

**Vindula Internet 2025** | Auditoria de Segurança #{execution_number}  
*Próxima auditoria recomendada: {next_audit_date}*
```

## Sistema Evolutivo

### **Tracking de Estado**
```json
// public/docs_v2/auditorias/tracking.json
{
  "last_execution": "2025-01-XX",
  "execution_count": 1,
  "baseline_metrics": {},
  "resolved_issues": [],
  "persistent_issues": [],
  "security_score_history": [],
  "performance_trends": []
}
```

### **Configuração de Auditoria**
```json
// public/docs_v2/auditorias/config.json
{
  "security_rules": {
    "rls_required_tables": ["profiles", "posts", "chat_messages"],
    "forbidden_patterns": ["auth.uid() = ANY", "company_id IS NULL", "company.slug === '"],
    "required_validations": ["email", "file_upload", "user_input"],
    "frontend_security_checks": ["useIsVindulaCompany", "admin_checks", "permission_validations"],
    "defense_in_depth_rules": {
      "frontend_permission_checks": ["user.role", "hasPermission", "isAdmin", "canAccess"],
      "require_backend_equivalent": true,
      "rls_policy_required": true
    }
  },
  "performance_thresholds": {
    "max_websocket_connections": 12,
    "max_memory_usage": "150MB",
    "max_bundle_size": "5MB",
    "min_lighthouse_score": 90
  },
  "quality_standards": {
    "min_typescript_coverage": 95,
    "max_cyclomatic_complexity": 10,
    "max_file_lines": 200,
    "required_jsdoc_coverage": 80
  }
}
```

## Output
Gera arquivo em `/public/docs_v2/auditorias/` com formato:
`auditoria-geral-{timestamp}.md`

Atualiza arquivo de tracking:
`/public/docs_v2/auditorias/tracking.json`

## Casos de Uso

### **Auditoria de Segurança Regular com Schema Analysis**
```bash
# Passo 1: Gerar schema dump atual
./scripts/dump-current-schema.sh

# Passo 2: Executar auditoria focada em segurança
/auditoria-geral --categoria=seguranca
// → Analisa schema dump para políticas RLS vulneráveis
// → Verifica isolamento multi-tenant no banco
// → Cross-reference validações frontend vs backend
// → Identifica tabelas sem RLS adequado
```

### **Análise de Performance Completa**
```
/auditoria-geral --categoria=performance --profundidade=alta
// → Análise profunda de gargalos
// → Memory profiling detalhado  
// → WebSocket connections audit
// → Bundle analysis e tree shaking
// → Benchmark com padrões da indústria
```

### **Auditoria Completa Pre-Release**
```bash
# Workflow completo pre-release
./scripts/dump-current-schema.sh  # Schema atual
/auditoria-geral --incluir-sugestoes
// → Análise completa: segurança + performance + qualidade
// → RLS policies validation no schema
// → Defense in Depth analysis
// → Gera checklist de qualidade completo
// → Compara schema evolution
```

### **Tracking de Evolução com Schema Changes**
```
/auditoria-geral
// → Compara com auditoria anterior
// → Analisa mudanças no schema desde última execução
// → Identifica melhorias e regressões de segurança
// → Track evolução de políticas RLS
// → Atualiza métricas de evolução
```

### **Análise de Sistema Específico**
```bash
# Para auditar sistema específico em detalhes
./scripts/extract-specific-system.sh tasks 'tasks,task_assignments,task_assignment_requests'
/auditoria-geral --categoria=seguranca
// → Foco em tabelas específicas extraídas
// → RLS analysis direcionado para o domínio
// → Validação multi-tenant específica do sistema
```

---

*Este comando é essencial para:*
- 🔒 **Manter segurança** em ambiente multi-tenant com análise de schema
- 🛡️ **Validar RLS policies** automaticamente via schema dumps
- ⚡ **Monitorar performance** continuamente
- 📋 **Garantir qualidade** do código e arquitetura
- 🏗️ **Evoluir arquitetura** de forma controlada
- 📊 **Tracking de melhorias** ao longo do tempo
- 🔍 **Schema evolution tracking** para identificar mudanças de segurança
- 🚨 **Defense in Depth validation** entre frontend e backend