# Comando: /avaliar-funcionalidade

## Descrição
Realiza uma análise técnica completa e abrangente de uma funcionalidade específica do sistema, mapeando toda sua arquitetura, componentes, fluxos de dados, problemas identificados e recomendações detalhadas. Gera um documento de mecanismo detalhado que serve como documentação completa e contexto para futuras modificações.

## Uso
```
/avaliar-funcionalidade <nome-da-funcionalidade>
```

## Exemplos
```
/avaliar-funcionalidade realtime
/avaliar-funcionalidade chat
/avaliar-funcionalidade gamificacao
/avaliar-funcionalidade auth
/avaliar-funcionalidade feed-posts
/avaliar-funcionalidade notifications
```

## Funcionalidade
Este comando irá realizar uma análise em 360° da funcionalidade especificada:

### **1. Mapeamento Global do Ecossistema**
- Identificação de TODOS os arquivos relacionados à funcionalidade
- Contagem de subscriptions/conexões ativas
- Identificação de redundâncias e duplicações
- Análise de performance e overhead
- Mapeamento de dependências entre componentes

### **2. Análise Arquitetural Profunda**
- Estrutura hierárquica de componentes
- Padrões de design utilizados
- Contextos e providers envolvidos
- Fluxo de dados completo
- Integrações com backend/Supabase

### **3. Componentes e Responsabilidades**
- Listagem detalhada de todos os componentes
- Análise de responsabilidades e interfaces
- Identificação de anti-patterns
- Verificação de aderência aos padrões Vindula

### **4. Problemas Críticos Identificados**
- **EMERGENCIAIS**: Problemas que afetam performance/estabilidade
- **CRÍTICOS**: Bugs, conflitos, memory leaks
- **ARQUITETURAIS**: Problemas de design e organização
- **UX/UI**: Inconsistências de experiência do usuário

### **5. Fluxos e Estados**
- Mapeamento completo de estados
- Ciclo de vida dos dados
- Gerenciamento de cache e sincronização
- Tratamento de erros e edge cases

### **6. Análise de Performance**
- Métricas de recursos utilizados
- Gargalos identificados
- Otimizações possíveis
- Comparação com benchmarks

### **7. Segurança e Multi-tenancy**
- Verificação de RLS policies
- Validação de permissões
- Isolamento de dados por empresa
- Vulnerabilidades potenciais

### **8. Recomendações Detalhadas**
- Soluções prioritárias por categoria
- Roadmap de implementação faseado
- Planos de migração e refatoração
- Estimativas de impacto e esforço

## Template do Relatório Gerado

O relatório seguirá uma estrutura similar ao documento de referência:

```markdown
# Mecanismo Detalhado de [FUNCIONALIDADE] - Vindula Cosmos

> **Análise Técnica Completa** do sistema de [funcionalidade], incluindo problemas identificados, arquitetura atual e soluções recomendadas.

## 📋 Índice

1. [Análise Global do Ecossistema](#análise-global-do-ecossistema)
2. [Visão Geral da Arquitetura](#visão-geral-da-arquitetura)
3. [Componentes Principais](#componentes-principais)
4. [Fluxo de Dados Completo](#fluxo-de-dados-completo)
5. [Estados e Controles](#estados-e-controles)
6. [Problemas Identificados](#problemas-identificados)
7. [Análise de Performance](#análise-de-performance)
8. [Recomendações e Soluções](#recomendações-e-soluções)
9. [Roadmap de Implementação](#roadmap-de-implementação)

---

## 🌐 Análise Global do Ecossistema

### **📊 Resumo Executivo**
[Análise do estado atual da funcionalidade]

### **🔢 Componentes por Sistema**
| Sistema | Arquivos | Hooks | Contextos | Status |
|---------|----------|-------|-----------|--------|
| [Sistema A] | X | Y | Z | 🔴/🟡/🟢 |

### **🔴 Redundâncias Críticas Identificadas**
[Listagem de duplicações e conflitos]

### **⚠️ Memory Leaks Potenciais**
[Identificação de vazamentos de memória]

### **📈 Performance Impact**
[Métricas de impacto na performance]

---

## 🏗️ Visão Geral da Arquitetura

### **Estrutura Hierárquica**
```mermaid
[Diagrama da arquitetura]
```

### **Contextos Principais**
[Análise detalhada dos contextos]

---

## 🧩 Componentes Principais

### **1. [ComponenteA]**
```typescript
interface ComponenteAProps {
  // Interface do componente
}
```

**Características:**
- ✅ Pontos positivos
- ⚠️ Pontos de atenção
- ❌ Problemas identificados

---

## ⚠️ Problemas Identificados

### **1. [Categoria de Problema] 🔥**
[Descrição detalhada do problema]

**Impacto:**
- ❌ Impacto A
- ❌ Impacto B

---

## 🔄 Fluxo de Dados Completo

### **1. [Etapa do Fluxo]**
```typescript
// Código exemplo do fluxo
```

---

## 🎮 Estados e Controles

### **Estados do Sistema**
| Estado | Tipo | Responsabilidade |
|--------|------|------------------|

---

## 🔊 Integração com Backend

### **Supabase Integration**
[Análise das integrações]

### **RLS Policies**
[Verificação de segurança]

---

## 💡 Recomendações e Soluções

### **1. [Categoria de Solução] 🔥**
[Soluções detalhadas]

---

## 🎯 Roadmap de Implementação

### **Fase 0: [Nome da Fase] (Prioridade [NÍVEL])** 🚨
[Descrição da fase]

**Impacto esperado:** [Descrição do impacto]

---

## 📊 Métricas e Monitoramento

### **Métricas Baseline (ANTES da otimização)**
```json
{
  "baselineMetrics": {
    // métricas atuais
  }
}
```

### **Métricas Target (DEPOIS da otimização)**
```json
{
  "targetMetrics": {
    // métricas esperadas
  }
}
```

---

## 📝 Considerações Finais

[Resumo executivo das descobertas e recomendações]

---

**Vindula Internet 2025** | Documentação Técnica Detalhada
```

## Processo de Execução

O comando seguirá este processo estruturado:

### **1. Descoberta e Mapeamento**
```javascript
// Mapear todos os arquivos relacionados à funcionalidade
const functionFiles = await discoverFunctionFiles(functionalityName);
const relatedHooks = await findRelatedHooks(functionalityName);
const contexts = await findRelatedContexts(functionalityName);
const components = await findRelatedComponents(functionalityName);
```

### **2. Análise Técnica**
```javascript
// Analisar cada arquivo encontrado
for (const file of functionFiles) {
  const analysis = await analyzeFile(file);
  const dependencies = await findDependencies(file);
  const performance = await analyzePerformance(file);
  const security = await checkSecurity(file);
}
```

### **3. Identificação de Problemas**
```javascript
// Detectar padrões problemáticos
const duplications = await findDuplications(functionFiles);
const memoryLeaks = await detectMemoryLeaks(functionFiles);
const antiPatterns = await findAntiPatterns(functionFiles);
const conflicts = await findConflicts(functionFiles);
```

### **4. Geração do Relatório**
```javascript
// Compilar análise em documento estruturado
const report = await generateDetailedReport({
  functionality: functionalityName,
  architecture: architectureAnalysis,
  problems: identifiedProblems,
  recommendations: generatedSolutions,
  roadmap: implementationPlan
});

// Salvar em docs/parafazer
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
const outputPath = `/docs/parafazer/mecanismo-detalhado-${functionalityName}-${timestamp}.md`;
await writeFile(outputPath, report);
```

## Funcionalidades Especiais

### **Detecção Automática de Padrões**
- 🔍 **Redundâncias**: Identifica automaticamente hooks/componentes duplicados
- 🔍 **Memory Leaks**: Detecta patterns de vazamento de memória
- 🔍 **Anti-patterns**: Reconhece padrões problemáticos do código
- 🔍 **Performance Issues**: Identifica gargalos e problemas de performance

### **Análise de Dependências**
- 📊 **Mapeamento completo** de dependências entre arquivos
- 📊 **Análise de impacto** para mudanças
- 📊 **Detecção de dependências circulares**
- 📊 **Identificação de acoplamento excessivo**

### **Geração de Diagramas**
- 🎨 **Mermaid diagrams** para arquitetura
- 🎨 **Fluxogramas** de dados
- 🎨 **Diagramas de sequência** para processos
- 🎨 **Mapas de dependência** visuais

### **Análise de Conformidade Vindula**
- ✅ **Verificação de padrões** estabelecidos no CLAUDE.md
- ✅ **Aderência ao sistema multi-tenant**
- ✅ **Validação de segurança RLS**
- ✅ **Conformidade com hooks customizados**

## Output
Gera um arquivo detalhado em `/docs/parafazer/` com formato:
`mecanismo-detalhado-{nome-da-funcionalidade}-{timestamp}.md`

## Casos de Uso Típicos

### **Diagnóstico de Performance**
```
/avaliar-funcionalidade realtime
// → Identifica 20+ subscriptions WebSocket simultâneas
// → Mapeia redundâncias críticas
// → Propõe consolidação de subscriptions
```

### **Auditoria de Segurança**
```
/avaliar-funcionalidade auth
// → Verifica todas as políticas RLS
// → Identifica vazamentos de dados entre empresas
// → Propõe correções de segurança
```

### **Refatoração Planejada**
```
/avaliar-funcionalidade chat
// → Mapeia toda a arquitetura de chat
// → Identifica componentes legados vs novos
// → Propõe roadmap de modernização
```

### **Onboarding de Desenvolvedor**
```
/avaliar-funcionalidade gamificacao
// → Documenta todo o sistema de gamificação
// → Explica fluxos e integrações
// → Serve como guia completo para novos devs
```

---

*Este comando é especialmente útil para:*
- 🎯 **Planejamento de refatorações** grandes
- 🎯 **Diagnóstico de problemas** complexos  
- 🎯 **Documentação** de sistemas existentes
- 🎯 **Onboarding** de novos desenvolvedores
- 🎯 **Auditoria técnica** antes de mudanças críticas