# Document Feature Implementation

## Description
Analisa a sessão atual e cria/atualiza documentação completa da funcionalidade implementada, seguindo os padrões do Vindula Cosmos.

**⚠️ OTIMIZAÇÃO DE CRÉDITOS**: Este comando foi otimizado para reduzir o uso de MCP e evitar "credits balance low".

## Usage
```bash
/document-feature [--cache-only] [--minimal] [--skip-mcp]
```

**Nota**: Este comando analisa automaticamente a sessão atual e detecta o tipo e nome da funcionalidade baseado nos arquivos modificados.

### Parâmetros de Otimização
- `--cache-only`: Usa apenas informações em cache, sem consultas MCP
- `--minimal`: Documentação básica sem validações MCP extensivas  
- `--skip-mcp`: Desabilita completamente chamadas MCP (modo emergência)

## Process

### 1. Análise da Sessão Atual
Analise automaticamente:
- Arquivos modificados/criados na sessão
- Migrações SQL adicionadas
- Hooks e componentes implementados
- Funcionalidades alteradas
- Problema resolvido (se for fix)

### 2. Detecção do Tipo de Mudança
Baseado nos arquivos modificados, determine:
- **Feature**: Novos componentes, hooks, páginas principais
- **Improvement**: Melhorias em funcionalidades existentes
- **Fix**: Correções de bugs, problemas de performance

### 3. Detecção de Documentação Existente

Antes de criar novos documentos, pesquisar documentação existente:

#### **🚀 Estratégia de Busca Otimizada (PRIORIDADE: Economia de Créditos)**

**MODO PADRÃO (--cache-only ou --minimal):**
1. **Busca Local Apenas**: Usar `grep` e `find` para localizar docs existentes
2. **Cache First**: Verificar arquivos `.md` existentes em `docs_v2/`
3. **Pattern Matching**: Detectar padrões nos nomes de arquivos modificados

**MODO COMPLETO (apenas se necessário):**
- ⚠️ **USA MCP**: Apenas se `--skip-mcp` NÃO estiver ativo
- 🔥 **ÚLTIMO RECURSO**: Quando busca local não encontrar documentação relacionada

#### **Algoritmo de Detecção Otimizado**
```bash
# SEMPRE executar primeiro (sem MCP):
# 1. Busca rápida por arquivos modificados
find docs_v2/ -name "*.md" -exec grep -l "componente\|hook\|migration" {} \;

# 2. Busca por padrões comuns  
grep -r --include="*.md" "feature.*flag\|sistema.*" docs_v2/

# 3. Busca por nomes similares aos arquivos modificados
# (extrair nomes de src/components/X.tsx e buscar docs mencionando X)
```

**🎯 REGRA DE ECONOMIA:**
- **SEM MCP**: Busca local + análise de padrões (90% dos casos)
- **COM MCP**: Apenas quando busca local falha completamente

#### **Critérios de Atualização vs Nova Criação**
- **ATUALIZAR** se encontrar documentação existente que menciona ≥70% dos arquivos/componentes modificados
- **CONSOLIDAR** se encontrar múltiplos documentos pequenos relacionados
- **CRIAR NOVO** apenas se não houver documentação relacionada OU se for funcionalidade completamente nova

### 4. Criação/Atualização da Documentação

#### Para Atualização de Documentação Existente
**PRIORIDADE MÁXIMA** - Sempre atualizar ao invés de criar novo quando possível:

##### **Processo de Atualização Inteligente**

1. **Identificar Seções Afetadas**:
   - Se modificou hooks → atualizar seção "Componentes Principais" ou "Como Funciona"
   - Se corrigiu bugs → adicionar seção "Correções Implementadas" ou atualizar "Status"
   - Se adicionou features → expandir "Funcionalidades" ou "Arquivos Implementados"

2. **Estratégia de Merge Inteligente**:
   ```markdown
   ## [Data] - Atualização: [Descrição da Mudança]
   
   ### 🔧 Melhorias Implementadas
   [Nova seção com as mudanças específicas da sessão atual]
   
   ### 📁 Arquivos Modificados
   [Adicionar novos arquivos à lista existente, marcando com emoji ✨ para novos]
   
   ### ⚠️ Problemas Corrigidos  
   [Se for fix, adicionar seção específica com o problema resolvido]
   
   ### 📊 Impacto das Mudanças
   [Como as mudanças afetam a funcionalidade existente]
   ```

3. **Atualização de Metadados**:
   ```markdown
   **Data de Última Atualização:** [Data atual]
   **Versão:** [Incrementar versão, ex: 1.0 → 1.1 para improvements, 1.0 → 2.0 para features maiores]
   **Status:** ✅ Atualizado - [Descrição da atualização]
   ```

4. **Preservação de Conteúdo Existente**:
   - **NUNCA** deletar seções existentes
   - **SEMPRE** adicionar/expandir conteúdo
   - **MARCAR** mudanças com data e emoji apropriado
   - **MANTER** histórico de implementação

##### **Template de Atualização**
```markdown
# [Nome da Funcionalidade] - [Mantém título original]

**Data de Criação:** [Data original]
**Data de Última Atualização:** [Data atual] ⚡
**Desenvolvedor:** Vindula Internet 2025  
**Status:** ✅ Atualizado - [Breve descrição da atualização]  
**Versão:** [Incrementar versão]

## 📋 Resumo
[Conteúdo original preservado]

### 🔧 [Data atual] - Melhorias Implementadas
[Nova seção com as mudanças específicas da sessão atual]

## 📦 Arquivos Implementados
[Lista original preservada + novos arquivos marcados com ✨]

### ✨ Arquivos Novos/Modificados ([Data])
- [Lista dos arquivos da sessão atual]

## 🔧 Como Funciona
[Conteúdo original + atualizações relevantes]

### 🆕 [Data] - Funcionalidades Adicionadas
[Se houver novas funcionalidades]

## 🧩 Componentes Principais
[Lista original + novos componentes/hooks]

### 🔄 Componentes Atualizados ([Data])
[Detalhar mudanças em componentes existentes]

## 🔐 Segurança e Permissões
[Preservar original + atualizações de segurança]

## 🐛 Problemas Corrigidos ([Data])
[Se for fix, adicionar seção específica]

## 🧪 Como Testar
[Instruções originais + novos cenários de teste]

## 📊 Métricas e Monitoramento
[Conteúdo original + novas métricas]

## 🔄 Próximos Passos
[Atualizar com base nas novas implementações]

---

### 📚 Histórico de Atualizações
- **v[versão atual]** ([Data]) - [Descrição da atualização atual]
- **v[versão anterior]** ([Data anterior]) - [Descrição implementação original]
```

#### Para Features Novas (apenas se não existir doc relacionada)
Criar em `docs_v2/features/[feature-name].md`:
```markdown
# [Nome da Funcionalidade]

**Data:** [Data atual]  
**Desenvolvedor:** Vindula Internet 2025  
**Status:** ✅ Implementado  
**Versão:** 1.0

## 📋 Resumo
[Descrição baseada na análise dos arquivos]

## 📦 Arquivos Implementados
[Lista automática dos arquivos modificados/criados]

## 🔧 Como Funciona
[Análise técnica baseada nos hooks, componentes e migrações]

## 🧩 Componentes Principais
[Lista de componentes, hooks e serviços com suas responsabilidades]

## 🔐 Segurança e Permissões
[Análise das permissões implementadas e RLS policies]

## 🧪 Como Testar
[Instruções baseadas nos componentes implementados]

## 📊 Métricas e Monitoramento
[Sugestões baseadas na funcionalidade]

## 🔄 Próximos Passos
[Melhorias futuras identificadas]
```

#### Para Improvements
Criar/atualizar em `docs_v2/improvements/[improvement-name].md`:
```markdown
# [Nome da Melhoria]

**Data:** [Data atual]  
**Tipo:** 🔧 Melhoria  
**Status:** ✅ Implementado  
**Autor:** Vindula Internet 2025

## 🎯 Problema Resolvido
[Análise do problema baseada no contexto da sessão]

## ✅ Solução Implementada
[Detalhes da implementação baseada nos arquivos modificados]

## 📁 Arquivos Modificados
[Lista dos arquivos alterados com descrição das mudanças]

## 🔧 Detalhes Técnicos
[Análise técnica das mudanças implementadas]

## 📊 Resultado Final
[Impacto e benefícios da melhoria]
```

#### Para Fixes
Criar/atualizar em `docs_v2/improvements/[fix-name].md`:
```markdown
# Fix: [Nome do Problema]

**Data:** [Data atual]  
**Tipo:** 🐛 Correção  
**Status:** ✅ Resolvido  
**Autor:** Vindula Internet 2025

## 🚨 Problema Identificado
[Descrição do bug/problema baseada no contexto]

## 🔍 Root Cause Analysis
[Análise da causa raiz baseada nas mudanças feitas]

## ✅ Solução Implementada
[Detalhes da correção]

## 📁 Arquivos Corrigidos
[Lista dos arquivos modificados]

## 🧪 Validação
[Como validar que o problema foi resolvido]
```

### 4. Atualização do Changelog
Adicionar entrada no `docs_v2/CHANGELOG.md`:

```markdown
## [YYYY-MM-DD] - [Nome da Funcionalidade/Melhoria/Fix]

### [Emoji] [Categoria]
- **[Nome]**: [Descrição]
  - [Lista de pontos principais baseados na implementação]
```

Usar categorias:
- ✨ **Novas Funcionalidades** (para features)
- 🔧 **Melhorias** (para improvements)
- 🐛 **Correções** (para fixes)
- 📚 **Documentação** (para docs)

### 5. Atualização do Índice
Atualizar `docs_v2/features/index.md` ou `docs_v2/improvements/index.md`:
- Adicionar entrada na seção apropriada
- Incluir status e breve descrição

### 6. Análise de Feature Flags (se aplicável)
Se detectar feature flags:
- Analisar migrations de feature flags
- Documentar limitações por plano
- Incluir informações sobre integração

### 7. Análise de Migrações (se aplicável)
Se detectar migrações SQL:
- Listar tabelas criadas/modificadas
- Documentar funções SQL adicionadas
- Analisar RLS policies implementadas

## Templates e Padrões

### Detecção Automática de Patterns
- **Feature Flags**: Se encontrar `INSERT INTO feature_flags`
- **Hooks**: Se encontrar arquivos em `src/lib/query/hooks/` ou `src/hooks/`
- **Componentes**: Se encontrar arquivos `.tsx` em `src/components/`
- **Páginas**: Se encontrar arquivos em `src/pages/`
- **Migrações**: Se encontrar arquivos `.sql` em `supabase/migrations/`
- **Schemas**: Se encontrar arquivos em `supabase/schemas/`

### Análise de Responsabilidades
- **Hooks**: "Hook para [função baseada no nome e código]"
- **Componentes**: "Componente para [função baseada no JSX e props]"  
- **Serviços**: "Serviço de [função baseada nas funções exportadas]"
- **Páginas**: "Página de [função baseada na rota e componentes]"

### Status e Emojis Padronizados
- ✅ **Implementado** - Funcionalidade completa
- 🔄 **Em Desenvolvimento** - Work in progress
- 🧪 **Experimental** - Em testes
- 📋 **Planejado** - Roadmap
- 🚨 **Crítico** - Requer atenção
- 🔧 **Melhoria** - Enhancement
- 🐛 **Correção** - Bug fix
- 📚 **Documentação** - Docs
- 🔒 **Segurança** - Security related

## Output
- Caminho do arquivo de documentação criado/atualizado
- Lista de arquivos analisados
- Resumo das mudanças detectadas
- Links para documentação relacionada (se houver)

## Examples

```bash
# Documentar funcionalidade automaticamente (único uso suportado)
/document-feature
```

**Funcionalidade**: O comando analisa automaticamente:
- Arquivos modificados/criados na sessão
- Tipo de mudança (feature/improvement/fix)
- Nome da funcionalidade baseado nos componentes/arquivos
- Documentação existente para atualizar ou criar nova

## Smart Features

### Detecção Inteligente
- Analisa nomes de arquivos para inferir funcionalidade
- Detecta padrões de implementação (CRUD, hooks, etc.)
- Identifica relacionamentos entre componentes
- Reconhece migrations e suas tabelas

### Contexto Vindula Cosmos
- Aplica padrões específicos do projeto
- Verifica conformidade com guidelines
- Sugere melhorias baseadas nos padrões
- Inclui verificações de segurança multi-tenant

### Cross-Reference
- Vincula funcionalidades relacionadas
- Atualiza documentação existente se necessário
- Sugere links para documentação relacionada
- Identifica dependências entre componentes