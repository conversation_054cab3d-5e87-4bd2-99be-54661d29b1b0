# 💰 Guia de Otimização de Créditos - Comand<PERSON> Claude

**<AUTHOR> Internet 2025**

## 🚨 Problema: "Credits Balance Low"

Quando os comandos `/commit` → `/document-feature` falham por falta de créditos, use as opções otimizadas.

## ⚡ Soluções Rápidas

### **Para Commits Pequenos/Fixes**
```bash
/commit --skip-docs                    # Zero documentação, zero MCP
/commit --cache-only --title="Fix X"  # Documentação básica, zero MCP
```

### **Para Features/Melhorias**
```bash
/commit --minimal-docs                 # Documentação básica, MCP mínimo
/commit --cache-only                   # Usa apenas cache local
```

### **Para Commits Complexos (emergência)**
```bash
# 1. Commit sem docs
/commit --skip-docs --title="Implement X"

# 2. Documentar depois (quando créditos voltarem)  
/document-feature --cache-only
```

## 🎯 Estratégia de Uso por Tipo

| Tipo de Commit | Comando Recomendado | Economia |
|----------------|-------------------|----------|
| **Bug Fix** | `/commit --skip-docs` | 🟢 Máxima |
| **Small Feature** | `/commit --cache-only` | 🟡 Alta |
| **Major Feature** | `/commit --minimal-docs` | 🟠 Moderada |
| **Complex System** | `/commit` (normal) | 🔴 Nenhuma |

## 🔧 Configuração de Emergência

### **Desabilitar MCPs Pesados Temporariamente**
```bash
# Editar .mcp.json e comentar:
# - "taskmaster-ai" (usa APIs externas)
# - "upstash-context7" (faz muitas consultas)
# - "vindula-cosmos-brain" (processamento pesado)
```

### **Verificar Status de Créditos**
```bash
# Antes de executar comandos pesados
/commit --dry-run    # Visualizar sem executar
```

## 📊 Comparação de Consumo

| Comando | MCP Calls | Context7 | Créditos |
|---------|-----------|----------|----------|
| `/commit` | ~10-15 | ✅ | 🔴 Alto |
| `/commit --minimal-docs` | ~3-5 | ✅ | 🟡 Médio |
| `/commit --cache-only` | ~0-1 | ❌ | 🟢 Baixo |
| `/commit --skip-docs` | 0 | ❌ | 🟢 Zero |

## 🚀 Boas Práticas

### **Durante Desenvolvimento Ativo**
1. Use `--cache-only` para commits frequentes
2. Reserve `/commit` normal para features finalizadas
3. Agrupe pequenas mudanças em um commit maior

### **Final de Sprint/Release**
1. Execute `/commit` normal para documentação completa
2. Use `--minimal-docs` para balance entre docs e créditos
3. Documente manualmente se necessário

### **Modo Emergência (Créditos Baixos)**
1. `--skip-docs` para tudo
2. Documenter depois com `--cache-only`
3. Considere aguardar reset de créditos

## ⚠️ Limitações dos Modos Otimizados

### **--skip-docs**
- ❌ Zero documentação automática
- ❌ CHANGELOG não é atualizado
- ✅ Commit e issue funcionam normalmente

### **--cache-only**
- ⚠️ Documentação básica (sem validações MCP)
- ⚠️ Pode não detectar todos os padrões
- ✅ CHANGELOG é atualizado

### **--minimal-docs**
- ✅ Documentação completa com validações reduzidas
- ✅ CHANGELOG atualizado
- ⚠️ Consumo moderado de créditos

## 🔄 Workflow Otimizado Sugerido

```bash
# Durante desenvolvimento (commits frequentes)
/commit --cache-only

# Feature completa (quando necessário)  
/commit --minimal-docs

# Release/milestone (documentação completa)
/commit  # modo normal
```

---

**💡 Dica**: Sempre prefira `--cache-only` quando possível. A documentação pode ser enriquecida depois, mas o histórico de commits e issues é mais importante.