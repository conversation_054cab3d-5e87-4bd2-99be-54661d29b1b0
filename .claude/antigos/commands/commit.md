# Comando: /commit

## 🚨 REGRAS CRÍTICAS DE EXECUÇÃO

### **WORKFLOW OBRIGATÓRIO COMPLETO**

#### **MODO PADRÃO (sem --close)**
**ORDEM INFLEXÍVEL E OBRIGATÓRIA:**

1. **PRIMEIRO**: Criar issue no GitHub com `gh issue create`
2. **SEGUNDO**: Executar `/document-feature` para documentação automática  
3. **TERCEIRO**: Executar `gh issue comment N --body "..."` com implementação detalhada
4. **QUARTO**: Executar `git commit` com "Closes #N"
5. **QUINTO**: Executar `git push`
6. **RESULTADO**: Issue criada → documentada → explicada → fechada automaticamente

#### **MODO FECHAMENTO (com --close=N)**
**ORDEM MODIFICADA:**

1. **PRIMEIRO**: Executar `/document-feature` para documentação automática  
2. **SEGUNDO**: Executar `gh issue comment N --body "..."` com implementação detalhada
3. **TERCEIRO**: Executar `git commit` com "Closes #N"
4. **QUARTO**: Executar `git push`
5. **RESULTADO**: Issue existente → documentada → explicada → fechada automaticamente

⚠️ **NUNCA INVERTER ESTA ORDEM** ⚠️
⚠️ **NUNCA FAZER COMMIT ANTES DA DOCUMENTAÇÃO E COMENTÁRIO** ⚠️

## Descrição
Comando completo para workflow de desenvolvimento: cria issue, documenta funcionalidade, comita e fecha issue automaticamente. Integra todas as etapas do ciclo de desenvolvimento em um único comando.

## Uso
```
/commit [--title="título da issue"] [--message="mensagem do commit"] [--skip-push] [--dry-run] [--skip-docs] [--feature-name="nome da feature"] [--close=N] [--minimal-docs] [--cache-only]
```

## Exemplos
```
/commit
/commit --title="Implementar sistema de badges automáticos"
/commit --title="Corrigir erro de permissões" --message="fix: resolve RLS policy conflicts"
/commit --feature-name="Sistema de Stardust" --title="Implementar subtração de Stardust"
/commit --skip-push
/commit --dry-run
/commit --close=11
/commit --close=25 --message="fix: resolve issue with user permissions"

# 🆕 NOVOS: Otimização de créditos
/commit --minimal-docs                    # Documentação básica, sem MCP extensivo
/commit --cache-only                      # Usa apenas cache local, zero MCP
/commit --skip-docs                       # Sem documentação (commits pequenos)
/commit --cache-only --title="Fix bug"   # Combinação: cache + título personalizado
```

## Comportamento

### **Modo Padrão vs Modo Fechamento**

#### **Modo Padrão (sem --close)**
Workflow completo: Cria issue → Documenta → Comita → Fecha issue automaticamente

#### **Modo Fechamento (com --close=N)**
Workflow modificado: Documenta → Comita → Fecha issue específica (não cria nova)

### **1. Criação Automática da Issue**
**PRIMEIRO PASSO OBRIGATÓRIO**: O sistema cria automaticamente uma issue no GitHub (exceto quando usar `--close=N`):

#### **Geração Inteligente do Título**
Se `--title` não for fornecido, gera automaticamente baseado nos arquivos modificados:

```bash
# Análise dos arquivos modificados para gerar título:
- Migrações: "Implementar [nome da tabela/função]" 
- Componentes: "Criar/Melhorar [nome do componente]"
- Hooks: "Implementar hook [funcionalidade]"
- Correções: "Corrigir [problema detectado]"
- Features: "Implementar [funcionalidade principal]"
```

#### **Geração do Corpo da Issue**
```bash
gh issue create --title "[título-gerado-ou-fornecido]" --body "$(cat <<'EOF'
## 🎯 Objetivo
[Descrição baseada na análise dos arquivos modificados]

## 📋 Escopo da Implementação
[Lista automática baseada nos arquivos que serão commitados:]
- [Para cada arquivo modificado, descrever o que será implementado]

## ✅ Critérios de Aceitação
[Gerados automaticamente baseados no tipo de mudança:]
- [ ] [Critério 1 baseado na funcionalidade]
- [ ] [Critério 2 baseado nos componentes]
- [ ] [Critério 3 baseado nas migrações]

## 🔧 Arquivos que Serão Modificados
[Lista dos arquivos da sessão atual]

## 📚 Documentação
- [ ] Documentação automática será gerada via `/document-feature`
- [ ] CHANGELOG será atualizado automaticamente
- [ ] Exemplos de uso serão incluídos

---
*Esta issue será implementada e fechada automaticamente pelo comando `/commit`*
EOF
)"
```

### **2. ✨ Documentação Automática via `/document-feature`**
**SEGUNDO PASSO OBRIGATÓRIO**: Executa automaticamente o comando `/document-feature`:

#### **Execução Automática Otimizada**
```bash
# MODO PADRÃO: Documentação completa (pode consumir créditos)
claude document-feature

# MODO OTIMIZADO: Usa parâmetros para economizar créditos
claude document-feature --cache-only     # Se --cache-only no commit
claude document-feature --minimal        # Se --minimal-docs no commit
# Pulado completamente se --skip-docs no commit
```

#### **Integração Completa**
- **Detecção**: Analisa arquivos modificados para determinar tipo (feature/improvement/fix)
- **Busca**: Procura documentação existente para atualizar ao invés de criar nova
- **Criação**: Gera documentação completa seguindo padrões Vindula Cosmos
- **Consolidação**: Atualiza CHANGELOG.md e índices automaticamente
- **Resultado**: Documentação sincronizada com a implementação

### **3. Identificação de Arquivos Modificados**
O comando identifica automaticamente os arquivos que foram manipulados na sessão atual:

```bash
# Verifica status atual
git status --porcelain

# Adiciona apenas os arquivos específicos da sessão + documentação gerada
git add [arquivos-da-sessao] [documentacao-gerada]
```

### **4. Geração de Mensagem de Commit**
- **Automática**: Gera mensagem baseada nos arquivos modificados e padrões do projeto
- **Personalizada**: Usa `--message` quando fornecido
- **Inclui docs**: Automaticamente menciona documentação criada/atualizada
- **Padrão**: Segue convenções de commit do repositório
- **✨ NOVO**: Adiciona automaticamente "Closes #[número]" da issue criada

### **5. Comentário Explicativo na Issue**
**TERCEIRO PASSO OBRIGATÓRIO**: Antes do commit, cria comentário detalhado na issue:

⚠️ **ORDEM OBRIGATÓRIA**: 
1. Documentação já foi criada no passo 2
2. **AGORA**: Criar comentário na issue com `gh issue comment`
3. **DEPOIS**: Fazer commit com "Closes #[número]"
4. **FINAL**: Fazer push

**IMPLEMENTAÇÃO AUTOMÁTICA OBRIGATÓRIA**:
```bash
# ETAPA 3: SEMPRE executar ANTES de qualquer commit
# Analisar arquivos modificados + documentação gerada e criar comentário detalhado
gh issue comment [número] --body "$(cat <<'EOF'
## ✅ Implementação Concluída

**Resumo:** [Análise automática das mudanças - descrever funcionalidade implementada]

**Mudanças realizadas:**
[Listar automaticamente baseado nos arquivos modificados:]
- [Para cada arquivo modificado, explicar a mudança]
- [Para migrações: descrever tabelas/funções criadas]
- [Para componentes: descrever funcionalidade adicionada]

**Arquivos modificados:**
[Listar todos os arquivos que serão commitados, incluindo documentação]

**Documentação criada/atualizada:**
[Listar a documentação gerada pelo `/document-feature`:]
- [docs_v2/features/xxx.md] - [Descrição]
- [docs_v2/CHANGELOG.md] - [Entrada adicionada]

**Como testar:**
[Gerar instruções básicas baseadas no tipo de mudança]

**Links da documentação:**
- [Link para documentação principal gerada]
- [Link para entrada do CHANGELOG]

---
*Esta issue será fechada automaticamente pelo próximo commit.*
EOF
)"

# AGUARDAR confirmação do comentário antes de prosseguir com commit
echo "✅ Comentário adicionado na issue #[número]"
```

### **6. Execução do Commit**

#### **Modo Padrão (sem --close)**
```bash
git commit -m "$(cat <<'EOF'
[mensagem-gerada-ou-personalizada]

Closes #[número-da-issue-criada]

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>
EOF
)"
```

#### **Modo Fechamento (com --close=N)**
```bash
git commit -m "$(cat <<'EOF'
[mensagem-gerada-ou-personalizada]

Closes #[N-fornecido-pelo-usuario]

EOF
)"
```

### **7. Push Automático**
```bash
# Push para branch atual (se não --skip-push)
git push origin $(git branch --show-current)
```

## Parâmetros

### `--title="título"` (opcional)
Define título personalizado para a issue:
```
/commit --title="Implementar sistema de rewards com Stardust"
```

### `--message="mensagem"` (opcional)
Define mensagem personalizada para o commit:
```
/commit --message="feat: implementar sistema de subtração de Stardust"
```

### `--feature-name="nome"` (opcional)
Define nome específico para a funcionalidade (usado pelo `/document-feature`):
```
/commit --feature-name="Sistema de Badges Automáticos"
```

### `--skip-push` (opcional)
Executa apenas o commit, sem fazer push:
```
/commit --skip-push
```

### `--dry-run` (opcional)
Mostra o que seria executado sem fazer alterações:
```
/commit --dry-run
```

### `--skip-docs` (opcional)
Pula a execução automática do `/document-feature`:
```
/commit --skip-docs
```

### `--minimal-docs` (opcional) 🆕
Executa `/document-feature --minimal` (documentação básica, economia de créditos):
```
/commit --minimal-docs
```

### `--cache-only` (opcional) 🆕
Executa `/document-feature --cache-only` (zero MCP, máxima economia):
```
/commit --cache-only
```

### `--close=N` (opcional)
Fecha uma issue específica existente ao invés de criar nova:
```
/commit --close=11
/commit --close=25 --message="fix: resolve user permissions issue"
```

**Comportamento:**
- **NÃO cria** nova issue no GitHub
- Executa documentação automática normalmente
- Adiciona comentário na issue especificada
- Fecha a issue com o commit (usando "Closes #N")
- Mantém todos os outros processos inalterados

## Lógica de Detecção de Arquivos

### **Arquivos Incluídos Automaticamente**
- Arquivos mencionados explicitamente durante a sessão
- Migrações criadas pelo Claude (`supabase/migrations/*.sql`)
- Arquivos de configuração modificados (`CLAUDE.md`, `package.json`, etc.)
- Componentes e hooks criados/modificados
- **✨ NOVO**: Documentação gerada automaticamente pelo `/document-feature` (`docs_v2/**`)
- **✨ NOVO**: Sincronização automática `.cursor/rules/sempre-ligado.mdc` ↔ `CLAUDE.md`

### **Arquivos Excluídos Automaticamente**
- Arquivos em `.gitignore`
- Arquivos de build (`dist/`, `node_modules/`, etc.)
- Arquivos temporários
- Arquivos modificados antes da sessão atual

## Exemplo de Execução Completa

### **Modo Padrão (cria nova issue)**
```bash
# Sessão atual modificou:
# - src/components/gamification/StardustBalance.tsx
# - src/lib/query/hooks/useStardust.ts
# - supabase/migrations/20250708000123_add_subtract_stardust_function.sql

/commit --title="Implementar sistema de subtração de Stardust"
```

### **Modo Fechamento (fecha issue existente)**
```bash
# Sessão atual modificou:
# - src/components/admin/AccessManagerUI.tsx
# - src/lib/permissions/roleValidation.ts

/commit --close=11 --message="fix: resolve access management permissions"
```

**Fluxo Automático Completo (Modo Padrão):**

### **ETAPA 1: Criação da Issue**
```bash
gh issue create --title "Implementar sistema de subtração de Stardust" --body "$(cat <<'EOF'
## 🎯 Objetivo
Implementar funcionalidade para subtrair Stardust do saldo dos usuários, incluindo validações de saldo suficiente e registros de auditoria.

## 📋 Escopo da Implementação
- Criação de função SQL segura para subtração de Stardust
- Componente React para exibir saldo atualizado
- Hook customizado para operações de Stardust
- Validações de segurança multi-tenant

## ✅ Critérios de Aceitação
- [ ] Função SQL `subtract_stardust()` implementada com validações
- [ ] Componente StardustBalance atualizado para refletir subtrações
- [ ] Hook useStardust com operações de subtração
- [ ] RLS policies para segurança multi-tenant
- [ ] Registros de auditoria em stardust_transactions

## 🔧 Arquivos que Serão Modificados
- src/components/gamification/StardustBalance.tsx
- src/lib/query/hooks/useStardust.ts  
- supabase/migrations/20250708000123_add_subtract_stardust_function.sql

## 📚 Documentação
- [ ] Documentação automática será gerada via `/document-feature`
- [ ] CHANGELOG será atualizado automaticamente
- [ ] Exemplos de uso serão incluídos

---
*Esta issue será implementada e fechada automaticamente pelo comando `/commit`*
EOF
)"

# RESULTADO: Issue #15 criada
echo "✅ Issue #15 criada: Implementar sistema de subtração de Stardust"
```

### **ETAPA 2: Documentação Automática**
```bash
# Executa automaticamente o document-feature
claude document-feature

# RESULTADO: 
# - docs_v2/features/sistema-subtracao-stardust.md (criado)
# - docs_v2/CHANGELOG.md (atualizado)
# - docs_v2/features/index.md (atualizado)

echo "✅ Documentação gerada automaticamente"
```

### **ETAPA 3: Comentário na Issue**
```bash
gh issue comment 15 --body "$(cat <<'EOF'
## ✅ Implementação Concluída

**Resumo:** Sistema completo de subtração de Stardust implementado com validações de segurança, componente React atualizado e hook customizado para operações de Stardust.

**Mudanças realizadas:**
- Criada função SQL `subtract_stardust()` com validações de saldo suficiente
- Implementada segurança multi-tenant com RLS policies
- Atualizado componente StardustBalance para exibir subtrações em tempo real
- Criado hook useStardust com operações de subtração e invalidação de cache
- Adicionados registros de auditoria em stardust_transactions

**Arquivos modificados:**
- src/components/gamification/StardustBalance.tsx - Componente atualizado com suporte a subtrações
- src/lib/query/hooks/useStardust.ts - Hook com operações de subtração
- supabase/migrations/20250708000123_add_subtract_stardust_function.sql - Função SQL segura

**Documentação criada/atualizada:**
- docs_v2/features/sistema-subtracao-stardust.md - Documentação completa da funcionalidade
- docs_v2/CHANGELOG.md - Entrada adicionada para nova funcionalidade
- docs_v2/features/index.md - Índice atualizado

**Como testar:**
- Acesse qualquer página com componente StardustBalance
- Teste operações de subtração via hook useStardust
- Verifique validações de saldo insuficiente
- Confirme registros de auditoria em stardust_transactions

**Links da documentação:**
- [Documentação principal](docs_v2/features/sistema-subtracao-stardust.md)
- [CHANGELOG](docs_v2/CHANGELOG.md)

---
*Esta issue será fechada automaticamente pelo próximo commit.*
EOF
)"

echo "✅ Comentário adicionado na issue #15"
```

### **ETAPA 4: Adicionar Arquivos**
```bash
git add src/components/gamification/StardustBalance.tsx \
        src/lib/query/hooks/useStardust.ts \
        supabase/migrations/20250708000123_add_subtract_stardust_function.sql \
        docs_v2/features/sistema-subtracao-stardust.md \
        docs_v2/CHANGELOG.md \
        docs_v2/features/index.md

echo "✅ Arquivos adicionados ao staging"
```

### **ETAPA 5: Commit com Fechamento**
```bash
git commit -m "$(cat <<'EOF'
feat: implement comprehensive Stardust subtraction system

- Add secure subtract_stardust() SQL function with balance validation
- Implement multi-tenant security with RLS policies  
- Update StardustBalance component with real-time subtraction display
- Create useStardust hook with subtraction operations and cache invalidation
- Add audit logging to stardust_transactions table
- Generate comprehensive documentation for new functionality

Closes #15

EOF
)"

echo "✅ Commit realizado"
```

### **ETAPA 6: Push**
```bash
git push origin main

echo "✅ Push realizado - Issue #15 será fechada automaticamente"
```

**Resultado Final:**
```
🎯 Issue #15 criada: "Implementar sistema de subtração de Stardust"
📚 Documentação gerada: docs_v2/features/sistema-subtracao-stardust.md
💬 Comentário detalhado adicionado na issue
✅ Commit realizado com "Closes #15"
🚀 Push feito - Issue fechada automaticamente pelo GitHub
📊 Workflow completo: Criação → Documentação → Implementação → Fechamento
```

---

**Fluxo Automático Completo (Modo Fechamento com --close=11):**

### **ETAPA 1: Documentação Automática**
```bash
# Executa automaticamente o document-feature
claude document-feature

# RESULTADO: 
# - docs_v2/improvements/correcao-permissoes-acesso.md (criado)
# - docs_v2/CHANGELOG.md (atualizado)

echo "✅ Documentação gerada automaticamente"
```

### **ETAPA 2: Comentário na Issue Existente**
```bash
gh issue comment 11 --body "$(cat <<'EOF'
## ✅ Implementação Concluída

**Resumo:** Correção completa do sistema de permissões de acesso com validações aprimoradas, componente AccessManagerUI atualizado e hook de validação de roles.

**Mudanças realizadas:**
- Corrigidas validações de permissões no componente AccessManagerUI
- Implementadas verificações de segurança multi-tenant
- Atualizado sistema de validação de roles
- Adicionadas proteções contra acesso não autorizado

**Arquivos modificados:**
- src/components/admin/AccessManagerUI.tsx - Correções de validação de permissões
- src/lib/permissions/roleValidation.ts - Sistema de validação aprimorado

**Documentação criada/atualizada:**
- docs_v2/improvements/correcao-permissoes-acesso.md - Documentação da correção
- docs_v2/CHANGELOG.md - Entrada adicionada

**Como testar:**
- Acesse a página de gerenciamento de acesso
- Teste validações de permissões por role
- Verifique proteções multi-tenant
- Confirme acesso negado para usuários não autorizados

**Links da documentação:**
- [Documentação da correção](docs_v2/improvements/correcao-permissoes-acesso.md)
- [CHANGELOG](docs_v2/CHANGELOG.md)

---
*Esta issue será fechada automaticamente pelo próximo commit.*
EOF
)"

echo "✅ Comentário adicionado na issue #11"
```

### **ETAPA 3: Adicionar Arquivos**
```bash
git add src/components/admin/AccessManagerUI.tsx \
        src/lib/permissions/roleValidation.ts \
        docs_v2/improvements/correcao-permissoes-acesso.md \
        docs_v2/CHANGELOG.md

echo "✅ Arquivos adicionados ao staging"
```

### **ETAPA 4: Commit com Fechamento**
```bash
git commit -m "$(cat <<'EOF'
fix: resolve access management permissions

- Fix permission validations in AccessManagerUI component
- Implement multi-tenant security checks
- Update role validation system
- Add protection against unauthorized access
- Generate comprehensive documentation for fixes

Closes #11

EOF
)"

echo "✅ Commit realizado"
```

### **ETAPA 5: Push**
```bash
git push origin main

echo "✅ Push realizado - Issue #11 será fechada automaticamente"
```

**Resultado Final:**
```
📚 Documentação gerada: docs_v2/improvements/correcao-permissoes-acesso.md
💬 Comentário detalhado adicionado na issue #11
✅ Commit realizado com "Closes #11"
🚀 Push feito - Issue #11 fechada automaticamente pelo GitHub
📊 Workflow completo: Documentação → Implementação → Fechamento
```

## Casos de Uso

### **Workflow Completo Padrão**
```
/commit
// → Detecta mudanças, cria issue, documenta, comita e fecha automaticamente
```

### **Workflow com Título Específico**
```
/commit --title="Implementar sistema de notificações em tempo real"
// → Usa título personalizado para a issue
```

### **Workflow com Mensagem de Commit Personalizada**
```
/commit --message="feat: sistema completo de gamificação com badges" --title="Sistema de Badges"
// → Usa título personalizado para issue e mensagem personalizada para commit
```

### **Workflow Sem Push (Para Revisão)**
```
/commit --skip-push
// → Cria issue, documenta, comita, mas não faz push
// → Útil para revisar antes de enviar
```

### **Visualizar Sem Executar**
```
/commit --dry-run
// → Mostra todo o workflow que seria executado
// → Exibe issue que seria criada, documentação que seria gerada, commit que seria feito
// → Não faz alterações
```

### **Workflow Sem Documentação Automática**
```
/commit --skip-docs
// → Pula execução do document-feature
// → Útil para commits menores que não precisam de documentação
```

### **Workflow de Fechamento de Issue Existente**
```
/commit --close=11
// → NÃO cria nova issue
// → Documenta automaticamente
// → Comenta na issue #11 existente
// → Fecha issue #11 com commit
```

### **Workflow de Fechamento com Mensagem Personalizada**
```
/commit --close=25 --message="fix: resolve critical security vulnerability"
// → Fecha issue #25 com mensagem personalizada
// → Documenta e comenta automaticamente
```

## Segurança e Validações

### **Verificações Pré-Execução**
- ✅ Verifica se há arquivos modificados na sessão
- ✅ Confirma conectividade com GitHub CLI (`gh auth status`)
- ✅ Valida que não há conflitos de merge pendentes
- ✅ Confirma que a branch atual permite push
- ✅ **NOVO**: Verifica se o `/document-feature` está disponível
- ✅ **NOVO**: Valida estrutura de documentação antes de executar

### **Proteções**
- 🛡️ **Nunca inclui**: arquivos de outros desenvolvedores
- 🛡️ **Nunca comita**: alterações não intencionais
- 🛡️ **Sempre confirma**: lista de arquivos antes do commit
- 🛡️ **Falha seguro**: em caso de erro em qualquer etapa, para o processo
- 🛡️ **NOVO**: Preserva issues existentes (não cria duplicatas)
- 🛡️ **NOVO**: Backup da documentação antes de modificar

### **Tratamento de Erros**
- **Erro na criação da issue**: Para o processo, não executa outras etapas
- **Erro na documentação**: Continua com warning, documenta manualmente
- **Erro no comentário**: Retry automático 1x, depois continua
- **Erro no commit**: Para o processo, mantém issue aberta
- **Erro no push**: Mantém commit local, sugere push manual

## Output

### **Sucesso Completo**
```
🎯 Issue #15 criada: "Implementar sistema de subtração de Stardust"
📚 Documentação gerada via /document-feature:
  - docs_v2/features/sistema-subtracao-stardust.md (criado)
  - docs_v2/CHANGELOG.md (atualizado)
💬 Comentário detalhado adicionado na issue #15
✅ Commit criado: a1b2c3d - "feat: implement comprehensive Stardust subtraction system"
📂 Arquivos incluídos: 3 código + 3 documentação
🚀 Push realizado: origin/main
🔒 Issue #15 será fechada automaticamente pelo GitHub

📋 Resumo do Workflow:
  1. ✅ Issue criada com escopo detalhado
  2. ✅ Documentação gerada automaticamente  
  3. ✅ Implementação explicada na issue
  4. ✅ Commit com fechamento automático
  5. ✅ Push enviado - issue será fechada
```

### **Dry Run**
```
🔍 Workflow que seria executado:

📝 Issue que seria criada:
  Título: "Implementar sistema de subtração de Stardust"
  Corpo: [Preview do corpo da issue gerada]

📚 Documentação que seria gerada:
  - Executaria: claude document-feature
  - Criaria: docs_v2/features/sistema-subtracao-stardust.md
  - Atualizaria: docs_v2/CHANGELOG.md

🧾 Arquivos que seriam incluídos:
  - src/components/gamification/StardustBalance.tsx
  - src/lib/query/hooks/useStardust.ts
  - supabase/migrations/20250708000123_add_subtract_stardust_function.sql
  + documentação gerada

💬 Comentário que seria adicionado:
  [Preview do comentário detalhado]

📝 Commit que seria criado:
  Mensagem: "feat: implement comprehensive Stardust subtraction system"
  [Preview da mensagem completa]

⚠️ Nenhuma alteração foi feita (--dry-run)
```

### **Erro**
```
❌ Erro na criação da issue: [detalhes do erro GitHub]
❌ Erro na documentação: /document-feature falhou - continuando sem docs
❌ Erro no comentário: falha na API do GitHub - tentando novamente...
❌ Erro no commit: conflitos de merge pendentes
❌ Erro no push: branch protegida ou sem permissão
```

---

*Este comando é essencial para: *
- 🎯 **Workflow completo** em um único comando
- 📋 **Rastreabilidade** total do desenvolvimento
- 📚 **Documentação automática** sempre sincronizada
- 🔄 **Integração GitHub** com issues e fechamento automático
- ⚡ **Produtividade máxima** sem etapas manuais
- 🛡️ **Segurança** com validações em cada etapa
- 🧠 **Inteligência** na geração de conteúdo contextual

## 🎯 **Principais Benefícios**

### **📋 Workflow Completo Automatizado**
- ✅ **Zero manual work**: Um comando faz tudo do início ao fim
- ✅ **Rastreabilidade**: Cada implementação tem issue correspondente
- ✅ **Contexto preservado**: Issue explica o que foi feito e por quê
- ✅ **Fechamento elegante**: Issue fechada automaticamente com explicação

### **📚 Documentação Zero-Debt**
- 🔄 **Sempre atualizada**: Documentação gerada automaticamente
- 🎯 **Contextual**: Baseada na implementação real
- 📊 **Completa**: Inclui arquivos, testes, e próximos passos
- 🔗 **Linkada**: Issue conecta código com documentação

### **⚡ Produtividade Máxima**
- 🚀 **Um comando**: Substitui 6-8 comandos manuais
- 🎨 **Padronização**: Sempre segue os mesmos padrões
- 📈 **Escalabilidade**: Funciona para qualquer tamanho de mudança
- 🧠 **Inteligente**: Detecta automaticamente o tipo de mudança

### **🛡️ Segurança e Qualidade**
- ✅ **Validações**: Verifica cada etapa antes de prosseguir
- 🔒 **Proteções**: Não inclui arquivos não intencionais
- 📋 **Auditoria**: Rastro completo de todas as mudanças
- 🎯 **Consistência**: Sempre segue o mesmo padrão de qualidade

### **💰 Otimização de Créditos** 🆕
- ⚡ **Modos Eficientes**: `--cache-only`, `--minimal-docs`, `--skip-docs`
- 🎯 **Economia Inteligente**: Reduz uso de MCP sem perder funcionalidade
- 📊 **Flexibilidade**: Escolha o nível de documentação vs consumo de créditos
- 🚀 **Fallback Robusto**: Funciona mesmo com "credits balance low"