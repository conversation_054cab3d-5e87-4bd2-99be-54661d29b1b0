# Comando: /commitpush

## 🚨 REGRAS CRÍTICAS DE EXECUÇÃO

### **PARA `--close-issue=N`**
**ORDEM OBRIGATÓRIA E INFLEXÍVEL:**

1. **PRIMEIRO**: Executar `gh issue comment N --body "..."` 
2. **AGUARDAR**: Confirmação do comentário
3. **SEGUNDO**: Executar `git commit` com "Closes #N"
4. **TERCEIRO**: Executar `git push`

⚠️ **NUNCA INVERTER ESTA ORDEM** ⚠️
⚠️ **NUNCA FAZER COMMIT ANTES DO COMENTÁRIO** ⚠️

## Descrição
Faz commit e push apenas dos arquivos que foram manipulados na sessão atual do Claude Code, evitando incluir arquivos modificados por outros processos ou desenvolvedores.

## Uso
```
/commitpush [--message="mensagem personalizada"] [--skip-push] [--dry-run] [--skip-docs] [--close-issue=número]
```

## Exemplos
```
/commitpush
/commitpush --message="fix: correções no sistema de Stardust"
/commitpush --skip-push
/commitpush --dry-run
/commitpush --skip-docs
/commitpush --close-issue=7
/commitpush --message="feat: nova funcionalidade" --close-issue=12
```

## Comportamento

### **1. Identificação de Arquivos Modificados**
O comando identifica automaticamente os arquivos que foram manipulados na sessão atual:

```bash
# Verifica status atual
git status --porcelain

# Adiciona apenas os arquivos específicos da sessão
git add [arquivos-da-sessao]
```

### **2. ✨ Análise Inteligente de Documentação**
**NOVO**: Antes do commit, o sistema analisa se as mudanças requerem atualizações na documentação:

#### **Detecção Automática de Impacto**
- **Novos componentes/hooks**: Verifica se precisa documentar em `public/docs_v2/features/`
- **Alterações em APIs**: Analisa mudanças que afetam documentação existente
- **Feature flags**: Detecta features que precisam ser documentadas
- **Migrações**: Verifica se precisa atualizar schemas ou docs técnicas
- **UX/UI changes**: Identifica mudanças que afetam experiência do usuário
- **CLAUDE.md alterado**: Sincroniza automaticamente com `.cursor/rules/sempre-ligado.mdc`

#### **Busca de Documentação Relacionada**
```bash
# Analisa arquivos modificados para detectar:
1. Componentes mencionados em docs existentes
2. Features relacionadas que precisam de atualização
3. Páginas de documentação obsoletas
4. Changelog que precisa ser atualizado
```

#### **Sugestões Automáticas**
- **Criar documentação**: Para novas funcionalidades significativas
- **Atualizar existente**: Para mudanças em funcionalidades documentadas
- **Consolidar docs**: Quando múltiplos arquivos legacy podem ser unidos
- **Atualizar CHANGELOG**: Para todas as mudanças relevantes ao usuário

### **3. Geração/Atualização de Documentação**
**AUTOMÁTICO**: O sistema cria ou atualiza documentação quando necessário:

#### **Criação Automática**
```typescript
// Para novas funcionalidades detecta automaticamente:
- Tipo de funcionalidade (component, feature, improvement)
- Arquivos afetados e sua função
- Integrações com outros sistemas
- Padrões seguidos e exemplos de uso
```

#### **Atualização Inteligente**
```typescript
// Para funcionalidades existentes:
- Atualiza seções específicas afetadas
- Mantém estrutura e formatação existente
- Adiciona novos exemplos quando relevante
- Atualiza status de implementação
```

#### **Documentos Priorizados**
1. **`public/docs_v2/CHANGELOG.md`** - SEMPRE atualizado para mudanças significativas
2. **`public/docs_v2/features/`** - Para novas funcionalidades ou alterações em features
3. **`public/docs_v2/improvements/`** - Para melhorias e otimizações
4. **`CLAUDE.md`** - Para mudanças em padrões ou guidelines do projeto
5. **`.cursor/rules/sempre-ligado.mdc`** - SINCRONIZADO automaticamente com CLAUDE.md (Cursor IDE)

### **4. Geração de Mensagem de Commit**
- **Automática**: Gera mensagem baseada nos arquivos modificados e padrões do projeto
- **Personalizada**: Usa `--message` quando fornecido
- **Inclui docs**: Automaticamente menciona documentação criada/atualizada
- **Padrão**: Segue convenções de commit do repositório
- **✨ NOVO**: Adiciona automaticamente "Closes #[número]" quando `--close-issue` é usado

### **5. Execução do Commit**
```bash
git commit -m "$(cat <<'EOF'
[mensagem-gerada-ou-personalizada]

[Se --close-issue]: Closes #[número]

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>
EOF
)"
```

### **6. Comentário Explicativo na Issue**
**CRÍTICO - EXECUTAR PRIMEIRO** quando `--close-issue` é usado:

⚠️ **ORDEM OBRIGATÓRIA**: 
1. **PRIMEIRO**: Criar comentário na issue com `gh issue comment`
2. **SEGUNDO**: Fazer commit com "Closes #[número]"
3. **TERCEIRO**: Fazer push

**IMPLEMENTAÇÃO AUTOMÁTICA OBRIGATÓRIA**:
```bash
# ETAPA 1: SEMPRE executar ANTES de qualquer commit quando --close-issue é usado
# Analisar arquivos modificados e gerar comentário detalhado
gh issue comment [número] --body "$(cat <<'EOF'
## ✅ Implementação Concluída

**Resumo:** [Análise automática das mudanças - descrever funcionalidade implementada]

**Mudanças realizadas:**
[Listar automaticamente baseado nos arquivos modificados:]
- [Para cada arquivo modificado, explicar a mudança]
- [Para migrações: descrever tabelas/funções criadas]
- [Para componentes: descrever funcionalidade adicionada]

**Arquivos modificados:**
[Listar todos os arquivos que serão commitados]

**Documentação:**
[Se documentação foi criada/atualizada, listar aqui]

**Como testar:**
[Gerar instruções básicas baseadas no tipo de mudança]

---
*Esta issue será fechada automaticamente pelo próximo commit.*
EOF
)"

# ETAPA 2: Aguardar confirmação do comentário antes de prosseguir com commit
echo "✅ Comentário adicionado na issue #[número]"

# ETAPA 3: Só então prosseguir com o commit normal
```

**REGRA CRÍTICA PARA CLAUDE CODE**: 
- Quando `--close-issue=N` é usado, **SEMPRE** executar `gh issue comment N --body` PRIMEIRO
- **NUNCA** fazer commit antes de criar o comentário na issue
- **SEMPRE** aguardar confirmação do comentário antes do commit

### **7. Push Automático**
```bash
# Push para branch atual (se não --skip-push)
git push origin $(git branch --show-current)
```

## Parâmetros

### `--message="mensagem"` (opcional)
Define mensagem personalizada para o commit:
```
/commitpush --message="feat: implementar sistema de subtração de Stardust"
```

### `--skip-push` (opcional)
Executa apenas o commit, sem fazer push:
```
/commitpush --skip-push
```

### `--dry-run` (opcional)
Mostra o que seria executado sem fazer alterações:
```
/commitpush --dry-run
```

### `--skip-docs` (opcional)
Pula a análise e geração automática de documentação:
```
/commitpush --skip-docs
```

### `--close-issue=número` (opcional) **✨ NOVO**
Fecha automaticamente uma issue do GitHub após o commit ser enviado:
```
/commitpush --close-issue=7
/commitpush --message="feat: implementar sistema de badges" --close-issue=12
```

**Comportamento OBRIGATÓRIO:**
- **ETAPA 1 - CRÍTICA**: **SEMPRE** gerar comentário explicativo detalhado usando `gh issue comment` **ANTES** do commit
- **ETAPA 2**: Adicionar `Closes #[número]` à mensagem de commit
- **ETAPA 3**: GitHub fecha a issue automaticamente quando o commit chega na branch principal
- **Resultado**: Issue fica com explicação completa + fechamento automático
- **REGRA**: Funciona com qualquer número de issue válido, mas **SEMPRE** comentário PRIMEIRO

## Lógica de Detecção de Arquivos

### **Arquivos Incluídos Automaticamente**
- Arquivos mencionados explicitamente durante a sessão
- Migrações criadas pelo Claude (`supabase/migrations/*.sql`)
- Arquivos de configuração modificados (`CLAUDE.md`, `package.json`, etc.)
- Componentes e hooks criados/modificados
- **✨ NOVO**: Documentação gerada/atualizada automaticamente (`public/docs_v2/**`)
- **✨ NOVO**: Sincronização automática `.cursor/rules/sempre-ligado.mdc` ↔ `CLAUDE.md`

### **Arquivos Excluídos Automaticamente**
- Arquivos em `.gitignore`
- Arquivos de build (`dist/`, `node_modules/`, etc.)
- Arquivos temporários
- Arquivos modificados antes da sessão atual

## Exemplo de Execução

```bash
# Sessão atual modificou:
# - src/components/layout/sidebar/NavigationMenu.tsx
# - src/lib/query/hooks/useFeatureFlags.ts
# - supabase/migrations/20250730000409_create_missions_feature_flag.sql

/commitpush
```

**Fluxo Automático:**
```bash
# 1. Detecta arquivos modificados
git status --porcelain

# 2. 🧠 ANALISA IMPACTO NA DOCUMENTAÇÃO
# Detecta: Nova feature flag "missions_feature" + mudanças no NavigationMenu
# Busca docs relacionadas: grep "mission\|feature.*flag\|navigation" public/docs_v2/

# 3. 🔍 ENCONTRA DOCUMENTAÇÃO RELACIONADA
# - public/docs_v2/features/sistema-missoes-completo.md (menciona missions_feature)
# - public/docs_v2/features/feature-flags-system.md (sistema de feature flags)

# 4. ✨ CRIA/ATUALIZA DOCUMENTAÇÃO AUTOMATICAMENTE
# Cria: public/docs_v2/improvements/navigation-menu-dev-badges.md
# Atualiza: public/docs_v2/features/feature-flags-system.md (nova badge behavior)
# Atualiza: public/docs_v2/CHANGELOG.md (nova entrada)
# 📋 SINCRONIZA: .cursor/rules/sempre-ligado.mdc com CLAUDE.md (se houver mudanças)

# 5. 📦 ADICIONA TODOS OS ARQUIVOS
git add src/components/layout/sidebar/NavigationMenu.tsx \
        src/lib/query/hooks/useFeatureFlags.ts \
        supabase/migrations/20250730000409_create_missions_feature_flag.sql \
        public/docs_v2/improvements/navigation-menu-dev-badges.md \
        public/docs_v2/features/feature-flags-system.md \
        public/docs_v2/CHANGELOG.md \
        .cursor/rules/sempre-ligado.mdc

# 6. 💬 COMENTÁRIO NA ISSUE (OBRIGATÓRIO SE --close-issue)
# ⚠️ CRÍTICO: Esta etapa SEMPRE vem ANTES do commit quando --close-issue é usado

gh issue comment 7 --body "$(cat <<'EOF'
## ✅ Implementação Concluída

**Resumo:** Sistema de badges automáticos para feature flags em desenvolvimento implementado no NavigationMenu

**Mudanças realizadas:**
- Implementado sistema inteligente de badges que mostra "Dev" para features com development_only: true
- Integração com hook useFeatureAvailability para detectar features em desenvolvimento automaticamente
- Melhorada UX para equipe Vindula identificar facilmente features em validação
- Adicionados tooltips indicando status development-only com atalhos de teclado

**Arquivos modificados:**
- src/components/layout/sidebar/NavigationMenu.tsx
- src/lib/query/hooks/useFeatureFlags.ts
- supabase/migrations/20250730000409_create_missions_feature_flag.sql

**Documentação:**
- Criada documentação abrangente para sistema de badges
- Atualizada documentação de feature flags com exemplos de comportamento de badges
- Atualizado CHANGELOG com nova funcionalidade

**Como testar:**
- Acesse NavigationMenu como usuário Vindula
- Verifique badges "Dev" em features com development_only: true
- Teste tooltips e comportamento de hover nos badges

---
*Esta issue será fechada automaticamente pelo próximo commit.*
EOF
)"

# ⚠️ AGUARDAR confirmação do comentário antes de prosseguir

# 7. 💬 GERA MENSAGEM INTELIGENTE
git commit -m "$(cat <<'EOF'
feat: enhance NavigationMenu with automatic development badges for feature flags

- Add intelligent badge system that shows "Dev" for features with development_only: true
- Integrate with useFeatureAvailability hook to detect development-only features automatically
- Improve UX for Vindula team to easily identify features under validation
- Add tooltip indicating development-only status with keyboard shortcuts
- Create comprehensive documentation for new badge system
- Update feature flags documentation with badge behavior examples

Closes #7

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>
EOF
)"

# 8. 🚀 PUSH PARA BRANCH ATUAL
git push origin main
```

**Análise Automática Realizada:**
```
📊 Impacto detectado:
  - Nova funcionalidade: Badge system para feature flags
  - Componentes afetados: NavigationMenu, useFeatureFlags
  - Documentação criada: navigation-menu-dev-badges.md
  - Documentação atualizada: feature-flags-system.md, CHANGELOG.md
  - 📋 Sincronização: .cursor/rules/sempre-ligado.mdc atualizado
  - Exemplos adicionados: Como configurar development_only badges
```

## Geração Automática de Mensagens

### **Padrões de Mensagem por Tipo**

#### **Correções de Bug**
```
fix: [resumo do problema corrigido]

- Detalhes da correção 1
- Detalhes da correção 2
```

#### **Novas Funcionalidades**
```
feat: [resumo da funcionalidade]

- Implementação 1
- Implementação 2
```

#### **Migrações de Banco**
```
feat: [ou fix:] [descrição da migração]

- Nova tabela/função/política criada
- Correção de schema/RLS/permissões
```

#### **Documentação (Automática)**
```
feat/fix: [funcionalidade] with comprehensive documentation

- [Mudanças no código]
- Create comprehensive documentation for [feature]
- Update [existing-docs] with [new-behavior]
- Add examples and usage instructions
```

### **Detecção Automática de Tipo**
- **Migrações**: Presença de arquivos `.sql` em `supabase/migrations/`
- **Componentes**: Arquivos `.tsx/.ts` em `src/components/`
- **Hooks**: Arquivos em `src/lib/query/hooks/`
- **Configuração**: `CLAUDE.md`, `package.json`, etc.
- **Correções**: Keywords como "fix", "correct", "resolve" no contexto
- **✨ Features**: Novos componentes, hooks, funcionalidades significativas
- **📚 Docs Auto**: Documentação criada/atualizada automaticamente

## Casos de Uso

### **Commit Inteligente com Documentação**
```
/commitpush
// → Analisa impacto na documentação automaticamente
// → Cria/atualiza docs relacionadas conforme necessário
// → Commit automático incluindo documentação
// → Push imediato para branch atual
```

### **Commit com Mensagem Específica**
```
/commitpush --message="feat: sistema completo de gerenciamento de Stardust"
// → Usa mensagem personalizada
// → Mantém formatação padrão
```

### **Apenas Commit (Sem Push)**
```
/commitpush --skip-push
// → Útil para revisar antes do push
// → Permite amend se necessário
```

### **Visualizar Sem Executar**
```
/commitpush --dry-run
// → Mostra arquivos que seriam incluídos
// → Exibe análise de documentação que seria feita
// → Mostra documentação que seria criada/atualizada
// → Exibe mensagem que seria gerada
// → Não faz alterações no git
```

### **Commit Sem Análise de Documentação**
```
/commitpush --skip-docs
// → Pula análise automática de documentação
// → Useful para commits menores ou experimentais
// → Ainda inclui docs modificadas manualmente
```

### **✨ NOVO: Commit com Fechamento de Issue**
```
/commitpush --close-issue=7
// → PASSO 1: Cria comentário explicativo detalhado na issue #7
// → PASSO 2: Adiciona "Closes #7" à mensagem de commit
// → PASSO 3: Issue é fechada automaticamente pelo GitHub
// → RESULTADO: Issue com explicação completa + fechamento elegante
```

### **✨ NOVO: Commit Completo com Issue e Mensagem Custom**
```
/commitpush --message="feat: implementar visualização de senha em formulários" --close-issue=7
// → PASSO 1: Cria comentário explicativo detalhado na issue #7
// → PASSO 2: Usa mensagem personalizada + "Closes #7"
// → PASSO 3: Documenta automaticamente a implementação
// → PASSO 4: Issue #7 fechada quando push for feito
// → RESULTADO: Issue bem documentada + commit personalizado
```

## Segurança e Validações

### **Verificações Pré-Commit**
- ✅ Verifica se há arquivos modificados na sessão
- ✅ Exclui arquivos não relacionados ao trabalho atual
- ✅ Valida que não há conflitos de merge pendentes
- ✅ Confirma que a branch atual permite push
- ✅ **NOVO**: Analisa se documentação está sincronizada com mudanças
- ✅ **NOVO**: Valida estrutura de documentação antes de criar novos arquivos

### **Proteções**
- 🛡️ **Nunca inclui**: arquivos de outros desenvolvedores
- 🛡️ **Nunca comita**: alterações não intencionais
- 🛡️ **Sempre confirma**: lista de arquivos antes do commit
- 🛡️ **Falha seguro**: em caso de dúvida, pergunta ao usuário
- 🛡️ **NOVO**: Não sobrescreve documentação importante sem confirmação
- 🛡️ **NOVO**: Preserva metadados e estrutura de docs existentes

## Output

### **Sucesso**
```
💬 Comentário adicionado na issue #7: Implementação detalhada explicada
✅ Commit criado: a1b2c3d
📂 Arquivos de código incluídos: 3
📚 Documentação criada/atualizada: 2
🚀 Push realizado: origin/main
🔒 Issue #7 será fechada automaticamente pelo GitHub

📋 Documentação gerada:
  - public/docs_v2/improvements/navigation-menu-dev-badges.md (criado)
  - public/docs_v2/CHANGELOG.md (atualizado)
```

### **Dry Run**
```
🔍 Arquivos que seriam incluídos:
  - src/components/layout/sidebar/NavigationMenu.tsx
  - src/lib/query/hooks/useFeatureFlags.ts
  - supabase/migrations/20250730000409_create_missions_feature_flag.sql

🧠 Análise de documentação:
  - ✅ Detectada nova funcionalidade: Badge system para feature flags
  - 📋 Docs relacionadas encontradas: 2 arquivos
  - 📝 Seria criado: navigation-menu-dev-badges.md
  - 🔄 Seria atualizado: feature-flags-system.md, CHANGELOG.md

📚 Documentação que seria gerada:
  - public/docs_v2/improvements/navigation-menu-dev-badges.md (novo)
  - public/docs_v2/features/feature-flags-system.md (atualização)
  - public/docs_v2/CHANGELOG.md (nova entrada)

💬 Comentário que seria adicionado na issue #7:
## ✅ Implementação Concluída
**Resumo:** Sistema de badges automáticos para feature flags...
[Detalhes completos da implementação]

📝 Mensagem que seria gerada:
feat: enhance NavigationMenu with automatic development badges for feature flags
[...]
Closes #7

⚠️ Nenhuma alteração foi feita (--dry-run)
```

### **Erro**
```
❌ Erro: Conflitos de merge pendentes
❌ Erro: Branch protegida, push não permitido
❌ Erro: Nenhum arquivo da sessão atual foi modificado
❌ Erro: Falha na análise de documentação (continua com commit sem docs)
❌ Erro: Documentação conflitante detectada (solicita confirmação)
```

---

*Este comando é essencial para:*
- 🎯 **Commits limpos** apenas com trabalho da sessão atual
- ⚡ **Workflow ágil** sem comandos git manuais
- 📝 **Mensagens consistentes** seguindo padrões do projeto
- 🛡️ **Segurança** evitando commits acidentais
- 🚀 **Automação** do processo commit + push
- 📚 **✨ NOVO**: **Documentação automática** sempre sincronizada com o código
- 🧠 **✨ NOVO**: **Análise inteligente** de impacto nas funcionalidades
- 🔄 **✨ NOVO**: **Atualizações automáticas** de CHANGELOG e documentação técnica

## 🎯 **Principais Benefícios da Nova Funcionalidade**

### **📚 Documentação Sempre Atualizada**
- ✅ **Zero debt**: Documentação nunca fica desatualizada
- ✅ **Automática**: Não depende de lembrar de documentar
- ✅ **Inteligente**: Detecta o que realmente precisa ser documentado
- ✅ **Contextual**: Entende o impacto das mudanças

### **🧠 Análise Inteligente de Impacto**
- 🔍 **Busca relacionada**: Encontra docs que precisam de atualização
- 🎯 **Relevância**: Só documenta o que é significativo
- 📊 **Categorização**: Diferencia features, improvements, fixes
- 🔗 **Conexões**: Identifica integrações e dependências

### **⚡ Produtividade Máxima**
- 🚀 **Um comando faz tudo**: Código + documentação + commit + push
- 🎨 **Padronização**: Documentação segue templates consistentes
- 📈 **Escalabilidade**: Funciona para qualquer tamanho de mudança
- 🔄 **Manutenção**: Docs se mantêm atualizadas automaticamente