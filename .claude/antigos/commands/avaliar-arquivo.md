# Comando: /avaliar-arquivo

## Descrição
Realiza uma avaliação técnica detalhada e abrangente de um arquivo específico, analisando sua arquitetura, implementação, padrões utilizados e qualidade do código. Gera um relatório completo que serve como documentação e contexto para futuras modificações.

## Uso
```
/avaliar-arquivo <caminho-do-arquivo>
```

## Exemplo
```
/avaliar-arquivo src/components/chat/ChatRoom.tsx
```

## Funcionalidade
Este comando irá:

1. **Análise Estrutural**
   - Arquitetura e organização do código
   - Padrões de design utilizados
   - Estrutura de imports e exports
   - Tipagem TypeScript (se aplicável)

2. **Análise de Qualidade**
   - Aderência aos padrões do projeto (CLAUDE.md)
   - Boas práticas de desenvolvimento
   - Performance e otimizações
   - Segurança e vulnerabilidades

3. **Análise Funcional**
   - Responsabilidades e propósito do arquivo
   - Integração com outros componentes
   - Fluxo de dados e estado
   - Tratamento de erros

4. **Pontos Positivos**
   - Implementações bem feitas
   - Padrões seguidos corretamente
   - Código limpo e legível
   - Funcionalidades bem implementadas

5. **Pontos Negativos**
   - Problemas identificados
   - Antipadrões encontrados
   - Vulnerabilidades de segurança
   - Performance issues

6. **Melhorias Sugeridas**
   - Refatorações recomendadas
   - Otimizações possíveis
   - Atualizações de padrões
   - Correções necessárias

7. **Contexto para Futuras Modificações**
   - Dependências e impactos
   - Testes necessários
   - Considerações especiais
   - Documentação adicional

## Output
Gera um arquivo detalhado em `/docs/parafazer/` com formato:
`avaliacao-{nome-do-arquivo}-{timestamp}.md`

## Implementação
```javascript
// Este é o prompt que será executado quando o comando for chamado
const filePath = args[0];
if (!filePath) {
  throw new Error('Caminho do arquivo é obrigatório');
}

// Ler o arquivo
const fileContent = await readFile(filePath);
const fileName = path.basename(filePath);
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

// Análise detalhada do arquivo
const analysis = await analyzeFile(fileContent, filePath);

// Gerar relatório
const report = generateDetailedReport(analysis, fileName, filePath);

// Salvar em docs/parafazer
const outputPath = `/docs/parafazer/avaliacao-${fileName}-${timestamp}.md`;
await writeFile(outputPath, report);

console.log(`Avaliação completa salva em: ${outputPath}`);
```

## Template do Relatório

O relatório gerado seguirá este template estruturado:

```markdown
# Avaliação Técnica: {nome-do-arquivo}

**Arquivo:** `{caminho-completo}`  
**Data da Avaliação:** {data}  
**Autor:** Vindula Internet 2025

## 📋 Resumo Executivo
[Resumo geral do arquivo e sua função no projeto]

## 🏗️ Análise Estrutural
### Arquitetura
- [Padrão arquitetural utilizado]
- [Organização do código]
- [Estrutura de responsabilidades]

### Imports e Dependências
- [Bibliotecas utilizadas]
- [Dependências internas]
- [Possíveis dependências circulares]

### Tipagem TypeScript
- [Qualidade da tipagem]
- [Uso de interfaces/types]
- [Aderência ao strict mode]

## ⚡ Análise de Performance
- [Otimizações implementadas]
- [Possíveis gargalos]
- [Uso de memo/callback quando necessário]

## 🔒 Análise de Segurança
- [Vulnerabilidades identificadas]
- [Validações de entrada]
- [Tratamento de dados sensíveis]

## 🎯 Análise Funcional
### Responsabilidades Principais
- [Função primária do arquivo]
- [Responsabilidades secundárias]

### Integração com Sistema
- [Como se conecta com outros componentes]
- [Dependências de estado]
- [APIs utilizadas]

### Fluxo de Dados
- [Como os dados fluem pelo componente]
- [Gerenciamento de estado]
- [Props e callbacks]

## ✅ Pontos Positivos
1. [Implementação bem feita 1]
2. [Padrão seguido corretamente 2]
3. [Código limpo e legível 3]

## ❌ Pontos Negativos
1. [Problema identificado 1]
2. [Antipadrão encontrado 2]
3. [Issue de performance 3]

## 🔧 Melhorias Sugeridas

### Prioridade Alta
- [ ] [Correção crítica 1]
- [ ] [Vulnerabilidade de segurança]

### Prioridade Média
- [ ] [Otimização de performance]
- [ ] [Refatoração para melhor legibilidade]

### Prioridade Baixa
- [ ] [Melhoria de documentação]
- [ ] [Padronização de nomenclatura]

## 📚 Contexto para Futuras Modificações

### Dependências e Impactos
- [Arquivos que dependem deste]
- [Arquivos dos quais este depende]
- [Possíveis efeitos colaterais de mudanças]

### Testes Necessários
- [Cenários de teste que devem ser considerados]
- [Testes de integração necessários]

### Considerações Especiais
- [Particularidades do componente]
- [Limitações conhecidas]
- [Histórico de problemas]

### Padrões do Projeto (CLAUDE.md)
- [Aderência aos padrões Vindula]
- [Uso correto do Supabase]
- [Seguimento das regras de segurança multi-tenant]

## 📝 Notas Adicionais
[Observações importantes para desenvolvimento futuro]

---
*Este relatório foi gerado automaticamente pelo comando /avaliar-arquivo*
*Para modificações neste arquivo, considere todas as observações acima*
```