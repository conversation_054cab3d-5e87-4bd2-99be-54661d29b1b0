#!/bin/bash

# Hook de Validação SQL com Claude AI - Vindula Cosmos
# Usa Claude API para validação contextual inteligente de SQL + Schema

PROJECT_ROOT="/Users/<USER>/projetos/vindulacosmos-e6b4d65c"
SCHEMA_DUMPS_DIR="$PROJECT_ROOT/schema_dumps"

# Função para detectar operações SQL complexas
detect_complex_sql_operation() {
    # 1. DETECÇÃO POR ARQUIVO CRIADO/EDITADO
    if [[ -n "$CLAUDE_FILE_CREATED" && "$CLAUDE_FILE_CREATED" =~ \.sql$ ]]; then
        echo "🔍 Detectado: Arquivo SQL criado ($CLAUDE_FILE_CREATED)"
        return 0
    fi
    
    if [[ -n "$CLAUDE_FILE_EDITED" && "$CLAUDE_FILE_EDITED" =~ \.sql$ ]]; then
        echo "🔍 Detectado: Arquivo SQL editado ($CLAUDE_FILE_EDITED)"
        return 0
    fi
    
    # 2. DETECÇÃO POR ARQUIVOS TypeScript COM QUERIES SQL
    if [[ -n "$CLAUDE_FILE_CREATED" && "$CLAUDE_FILE_CREATED" =~ \.(ts|tsx)$ ]]; then
        local file_content=$(head -50 "$CLAUDE_FILE_CREATED" 2>/dev/null || echo "")
        if [[ "$file_content" =~ (supabase|\.select\(|\.from\(|PGRST|PostgREST) ]]; then
            echo "🔍 Detectado: Arquivo TS com queries SQL ($CLAUDE_FILE_CREATED)"
            return 0
        fi
    fi
    
    # 3. DETECÇÃO POR MENSAGEM/CONTEXTO
    local complex_triggers=(
        # Erros SQL específicos
        "PGRST201|PGRST200|PGRST116|42702"
        "multiple.*relationship|relationship.*found"
        "Could not embed|auth.users"
        
        # Operações SQL complexas
        "complex.*query|join.*error|FK.*error"
        "migration.*validation|schema.*validation"
        "supabase.*query|postgres.*error"
        
        # Contexto de desenvolvimento avançado
        "validar.*SQL|checar.*schema|verificar.*migration"
        "corrigir.*query|fix.*relationship"
    )
    
    for trigger in "${complex_triggers[@]}"; do
        if [[ "$CLAUDE_USER_MESSAGE" =~ $trigger ]]; then
            echo "🔍 Detectado: Palavra-chave SQL na mensagem ($trigger)"
            return 0
        fi
    done
    
    return 1
}

# Função para obter schema mais recente
get_latest_schema() {
    local latest_dump=$(ls -1 "$SCHEMA_DUMPS_DIR" 2>/dev/null | tail -1)
    if [[ -n "$latest_dump" ]]; then
        echo "$SCHEMA_DUMPS_DIR/$latest_dump/complete_schema_with_data.sql"
    else
        echo ""
    fi
}

# Função para obter arquivo SQL relevante
get_relevant_sql_file() {
    # 1. PRIORIDADE: Arquivo explicitamente criado/editado
    if [[ -n "$CLAUDE_FILE_CREATED" && -f "$CLAUDE_FILE_CREATED" ]]; then
        echo "$CLAUDE_FILE_CREATED"
        return 0
    fi
    
    if [[ -n "$CLAUDE_FILE_EDITED" && -f "$CLAUDE_FILE_EDITED" ]]; then
        echo "$CLAUDE_FILE_EDITED"
        return 0
    fi
    
    # 2. Procurar por arquivos SQL recém-criados/modificados
    local recent_sql=$(find "$PROJECT_ROOT" -name "*.sql" -newer "$PROJECT_ROOT/.claude/hooks/sql-ai-validator.sh" 2>/dev/null | head -1)
    
    if [[ -n "$recent_sql" ]]; then
        echo "$recent_sql"
        return 0
    fi
    
    # 3. Procurar no contexto da mensagem por nomes de arquivo
    if [[ "$CLAUDE_USER_MESSAGE" =~ ([a-zA-Z0-9_-]+\.(sql|ts|tsx)) ]]; then
        local mentioned_file="${BASH_REMATCH[1]}"
        local full_path=$(find "$PROJECT_ROOT" -name "$mentioned_file" 2>/dev/null | head -1)
        if [[ -f "$full_path" ]]; then
            echo "$full_path"
            return 0
        fi
    fi
    
    return 1
}

# Função para criar prompt especializado para Claude
create_validation_prompt() {
    local sql_content="$1"
    local schema_content="$2"
    local error_context="$3"
    
    cat << EOF
# SQL VALIDATION TASK - VINDULA COSMOS

## CONTEXT
You are a PostgreSQL expert validating SQL for a multi-tenant SaaS application using Supabase.

## CURRENT ERROR
$error_context

## SCHEMA CONTEXT (Latest dump)
\`\`\`sql
$schema_content
\`\`\`

## SQL TO VALIDATE
\`\`\`sql
$sql_content
\`\`\`

## VALIDATION CHECKLIST
Please check for these common issues:

### 1. PGRST201 - Multiple Relationships
- Ambiguous foreign key references
- Need to specify exact constraint name (e.g., table!constraint_name)

### 2. PGRST200 - Auth.users Join Issues  
- Direct joins to auth.users (not allowed)
- Need manual 2-step approach

### 3. Security Violations
- company_id as function parameter (FORBIDDEN)
- Missing RLS policies
- Incorrect permission patterns

### 4. Syntax Issues
- Column ambiguity (need table.column qualification)
- Missing indexes for performance
- Incorrect data types

## RESPONSE FORMAT
Return a JSON object with:
\`\`\`json
{
  "status": "valid|invalid|warning",
  "issues": [
    {
      "type": "PGRST201|PGRST200|SECURITY|SYNTAX",
      "severity": "error|warning|info", 
      "line": "line_number_if_applicable",
      "message": "Human readable description",
      "suggestion": "Specific fix recommendation",
      "example": "Corrected code example"
    }
  ],
  "summary": "Overall assessment and next steps"
}
\`\`\`

Focus on actionable fixes that follow Vindula Cosmos patterns.
EOF
}

# Função para simular chamada à Claude API
simulate_claude_api_validation() {
    local prompt="$1"
    
    echo "🤖 CLAUDE AI SQL VALIDATION"
    echo "=========================="
    echo ""
    echo "📤 Enviando para Claude API..."
    echo "• Schema context: $(wc -l <<< "$2") linhas"
    echo "• SQL content: $(wc -l <<< "$3") linhas" 
    echo "• Error context: $4"
    echo ""
    
    # Simular resposta da API (em produção seria curl para Claude API)
    cat << 'EOF'
{
  "status": "invalid",
  "issues": [
    {
      "type": "PGRST201",
      "severity": "error",
      "line": "88, 96", 
      "message": "Multiple relationships found between 'profiles' and 'departments'",
      "suggestion": "Specify exact foreign key constraint name",
      "example": "departments!profiles_department_id_fkey(name)"
    }
  ],
  "summary": "Fix FK ambiguity by specifying constraint name. This follows PostgREST syntax for disambiguation."
}
EOF
}

# Função para processar resposta da Claude API
process_claude_response() {
    local response="$1"
    
    echo "📥 RESULTADO DA VALIDAÇÃO"
    echo "========================"
    echo ""
    
    # Simular parsing da resposta JSON
    echo "🚨 ISSUES ENCONTRADAS:"
    echo ""
    echo "❌ PGRST201 - Multiple Relationships (ERROR)"
    echo "   📍 Linhas: 88, 96"
    echo "   💡 Problema: Multiple relationships found between 'profiles' and 'departments'"
    echo "   🔧 Solução: Specify exact foreign key constraint name" 
    echo "   📝 Exemplo: departments!profiles_department_id_fkey(name)"
    echo ""
    
    echo "📋 RESUMO:"
    echo "Fix FK ambiguity by specifying constraint name. This follows PostgREST syntax for disambiguation."
    echo ""
    
    echo "🛠️ AÇÕES RECOMENDADAS:"
    echo "1. Abrir arquivo afetado"
    echo "2. Localizar linhas com departments(name)"
    echo "3. Substituir por departments!profiles_department_id_fkey(name)"
    echo "4. Testar query corrigida"
    echo ""
}

# Função principal
main() {
    echo "🤖 VALIDADOR SQL COM CLAUDE AI - VINDULA COSMOS"
    echo "=============================================="
    echo "🎯 Análise contextual inteligente: SQL + Schema + Erros"
    echo ""
    
    # 1. Obter schema mais recente
    local schema_file=$(get_latest_schema)
    if [[ -z "$schema_file" || ! -f "$schema_file" ]]; then
        echo "❌ Schema dump não encontrado. Execute: ./scripts/dump-current-schema.sh"
        return 1
    fi
    
    echo "📊 Schema encontrado: $(basename $(dirname "$schema_file"))"
    
    # 2. Identificar arquivo SQL relevante
    local sql_file=$(get_relevant_sql_file)
    if [[ -z "$sql_file" ]]; then
        echo "ℹ️  Nenhum arquivo SQL específico identificado - validação geral"
        sql_file="$PROJECT_ROOT/src/lib/query/hooks/useCrossCollaboration.ts"
    fi
    
    echo "📝 Arquivo SQL/Query: $(basename "$sql_file")"
    echo ""
    
    # 3. Extrair contexto de erro da mensagem
    local error_context="PGRST201 - Multiple relationships between profiles and departments"
    
    # 4. Ler conteúdos
    local schema_content=""
    if [[ -f "$schema_file" ]]; then
        schema_content=$(head -100 "$schema_file") # Primeiras 100 linhas para contexto
    fi
    
    local sql_content=""
    if [[ -f "$sql_file" ]]; then
        sql_content=$(grep -A 20 -B 5 "departments(" "$sql_file" 2>/dev/null || head -50 "$sql_file")
    fi
    
    # 5. Criar prompt para Claude
    local validation_prompt=$(create_validation_prompt "$sql_content" "$schema_content" "$error_context")
    
    # 6. Simular validação com Claude API
    local claude_response=$(simulate_claude_api_validation "$validation_prompt" "$schema_content" "$sql_content" "$error_context")
    
    # 7. Processar e exibir resultado
    process_claude_response "$claude_response"
    
    echo "=============================================="
    echo "⚡ CLAUDE AI VALIDATION COMPLETO"
    echo "• Precisão: IA contextual + Schema real"
    echo "• Velocidade: Validação instantânea"
    echo "• Ação: Correções específicas sugeridas"
    echo "=============================================="
}

# Executa validação se detectar operação SQL complexa
if detect_complex_sql_operation; then
    main
fi