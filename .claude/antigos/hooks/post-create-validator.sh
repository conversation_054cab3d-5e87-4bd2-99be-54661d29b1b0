#!/bin/bash

# Hook Pós-Criação - Vindula Cosmos
# Roda APÓS arquivo ser criado/editado para validação e formatação automática
# Executa: validação + prettier + double-check de violações

PROJECT_ROOT="/Users/<USER>/projetos/vindulacosmos-e6b4d65c"

# Função para detectar arquivos criados/editados
detect_file_creation() {
    # Verifica se foi criado/editado um arquivo relevante
    if [[ -n "$CLAUDE_FILE_CREATED" ]]; then
        echo "📁 Arquivo criado: $CLAUDE_FILE_CREATED"
        return 0
    fi
    
    if [[ -n "$CLAUDE_FILE_EDITED" ]]; then
        echo "📝 Arquivo editado: $CLAUDE_FILE_EDITED"  
        return 0
    fi
    
    return 1
}

# Função para obter o arquivo a ser validado
get_target_file() {
    if [[ -n "$CLAUDE_FILE_CREATED" ]]; then
        echo "$CLAUDE_FILE_CREATED"
    elif [[ -n "$CLAUDE_FILE_EDITED" ]]; then
        echo "$CLAUDE_FILE_EDITED"
    fi
}

# Função para validar arquivo React/TypeScript
validate_react_file() {
    local file_path="$1"
    local filename=$(basename "$file_path")
    
    echo "⚛️  VALIDAÇÃO PÓS-CRIAÇÃO: $filename"
    echo "=================================="
    echo ""
    
    # Verificar se arquivo existe
    if [[ ! -f "$file_path" ]]; then
        echo "❌ Arquivo não encontrado: $file_path"
        return 1
    fi
    
    # Verificar extensão
    if [[ ! "$file_path" =~ \.(tsx|ts)$ ]]; then
        echo "ℹ️  Arquivo não é React/TypeScript - pulando validação"
        return 0
    fi
    
    local violations=0
    
    echo "🔍 VERIFICANDO VIOLAÇÕES..."
    echo ""
    
    # 1. Verificar tamanho do arquivo
    local line_count=$(wc -l < "$file_path")
    if [[ $line_count -gt 200 ]]; then
        echo "❌ VIOLAÇÃO: Arquivo muito grande ($line_count linhas > 200)"
        echo "   📋 AÇÃO: Refatorar em componentes menores"
        violations=$((violations + 1))
    else
        echo "✅ Tamanho: $line_count linhas (OK)"
    fi
    
    # 2. Verificar useQuery/useMutation inline
    if grep -q "useQuery\|useMutation" "$file_path" && ! grep -q "import.*useQuery\|import.*useMutation" "$file_path"; then
        echo "❌ VIOLAÇÃO: useQuery/useMutation inline detectado"
        echo "   📋 AÇÃO: Criar hook personalizado em /src/lib/query/hooks/"
        violations=$((violations + 1))
    else
        echo "✅ TanStack Query: Sem violações inline"
    fi
    
    # 3. Verificar company_id incorreto
    if grep -q "user\.company_id" "$file_path"; then
        echo "❌ VIOLAÇÃO: user.company_id detectado"
        echo "   📋 AÇÃO: Usar useAuthStore((state) => state.company_id)"
        violations=$((violations + 1))
    else
        echo "✅ Company ID: Padrão correto"
    fi
    
    # 4. Verificar imports incorretos
    if grep -q "import.*toast.*from.*react-hot-toast" "$file_path"; then
        echo "❌ VIOLAÇÃO: Import toast incorreto"
        echo "   📋 AÇÃO: Usar toastWithNotification"
        violations=$((violations + 1))
    else
        echo "✅ Imports: Padrão correto"
    fi
    
    # 5. Verificar <img> tags para avatars
    if grep -q "<img.*src.*avatar\|<img.*rounded-full\|<img.*profile" "$file_path"; then
        echo "❌ VIOLAÇÃO: <img> tag detectada para avatar/perfil"
        echo "   📋 AÇÃO: Usar <EnhancedOptimizedAvatar> em vez de <img>"
        violations=$((violations + 1))
    else
        echo "✅ Avatars: Usando componente adequado"
    fi
    
    # 6. Verificar JSDoc
    if ! grep -q "<AUTHOR> Internet 2025" "$file_path"; then
        echo "⚠️  AVISO: JSDoc <AUTHOR>
        echo "   📋 SUGESTÃO: Adicionar /** <AUTHOR> Internet 2025 */"
    else
        echo "✅ JSDoc: Presente"
    fi
    
    echo ""
    if [[ $violations -gt 0 ]]; then
        echo "🚨 TOTAL DE VIOLAÇÕES: $violations"
        echo "📋 REVISAR E CORRIGIR ANTES DE CONTINUAR"
        return 1
    else
        echo "✅ ARQUIVO VÁLIDO: Sem violações detectadas"
        return 0
    fi
}

# Função para validar arquivo SQL
validate_sql_file() {
    local file_path="$1"
    local filename=$(basename "$file_path")
    
    echo "🗄️  VALIDAÇÃO SQL PÓS-CRIAÇÃO: $filename"
    echo "====================================="
    echo ""
    
    # Verificar se arquivo existe
    if [[ ! -f "$file_path" ]]; then
        echo "❌ Arquivo não encontrado: $file_path"
        return 1
    fi
    
    # Verificar extensão
    if [[ ! "$file_path" =~ \.sql$ ]]; then
        echo "ℹ️  Arquivo não é SQL - pulando validação"
        return 0
    fi
    
    local violations=0
    
    echo "🔍 VERIFICANDO VIOLAÇÕES DE SEGURANÇA..."
    echo ""
    
    # 1. Verificar company_id como parâmetro
    if grep -q "company_id.*UUID" "$file_path" && grep -q "CREATE.*FUNCTION" "$file_path"; then
        echo "❌ VIOLAÇÃO CRÍTICA: company_id como parâmetro detectado"
        echo "   📋 AÇÃO: Usar auth.uid() + profiles para obter company_id"
        violations=$((violations + 1))
    else
        echo "✅ Segurança: Sem company_id como parâmetro"
    fi
    
    # 2. Verificar funções Stardust proibidas
    if grep -q "add_user_stardust" "$file_path"; then
        echo "❌ VIOLAÇÃO: Função add_user_stardust proibida"
        echo "   📋 AÇÃO: Usar add_stardust() oficial"
        violations=$((violations + 1))
    else
        echo "✅ Stardust: Funções corretas"
    fi
    
    # 3. Verificar RLS em CREATE TABLE
    if grep -q "CREATE TABLE" "$file_path" && ! grep -q "CREATE POLICY\|ENABLE ROW LEVEL SECURITY" "$file_path"; then
        echo "⚠️  AVISO: CREATE TABLE sem RLS detectado"
        echo "   📋 SUGESTÃO: Adicionar políticas RLS"
    else
        echo "✅ RLS: Políticas presentes"
    fi
    
    echo ""
    if [[ $violations -gt 0 ]]; then
        echo "🚨 TOTAL DE VIOLAÇÕES: $violations"
        echo "📋 REVISAR E CORRIGIR ANTES DE CONTINUAR"
        return 1
    else
        echo "✅ ARQUIVO SQL VÁLIDO: Sem violações detectadas"
        return 0
    fi
}

# Função para executar Prettier
run_prettier() {
    local file_path="$1"
    
    # Verificar se é arquivo que pode ser formatado
    if [[ "$file_path" =~ \.(ts|tsx|js|jsx|json|css|scss|md)$ ]]; then
        echo "🎨 EXECUTANDO PRETTIER..."
        
        cd "$PROJECT_ROOT"
        if command -v bun >/dev/null 2>&1; then
            if bun run prettier --write "$file_path" 2>/dev/null; then
                echo "✅ Prettier: Arquivo formatado com sucesso"
            else
                echo "⚠️  Prettier: Erro na formatação (arquivo pode ter sintaxe inválida)"
            fi
        else
            echo "⚠️  Prettier: bun não encontrado"
        fi
    else
        echo "ℹ️  Prettier: Arquivo não suportado para formatação"
    fi
}

# Função para relatório final
final_report() {
    local file_path="$1"
    local filename=$(basename "$file_path")
    
    echo ""
    echo "📊 RELATÓRIO FINAL - $filename"
    echo "=============================="
    
    # Estatísticas do arquivo
    if [[ -f "$file_path" ]]; then
        local lines=$(wc -l < "$file_path")
        local size=$(ls -lh "$file_path" | awk '{print $5}')
        
        echo "📏 Linhas: $lines"
        echo "📦 Tamanho: $size"
        
        # Tipo de arquivo
        if [[ "$file_path" =~ \.tsx$ ]]; then
            echo "🎯 Tipo: React Component (TSX)"
        elif [[ "$file_path" =~ \.ts$ ]]; then
            echo "🎯 Tipo: TypeScript"
        elif [[ "$file_path" =~ \.sql$ ]]; then
            echo "🎯 Tipo: SQL Migration/Function"
        fi
    fi
    
    echo ""
    echo "✅ PROCESSO CONCLUÍDO"
    echo "===================="
}

# Função principal
main() {
    local target_file=$(get_target_file)
    
    if [[ -z "$target_file" ]]; then
        echo "ℹ️  Nenhum arquivo para validar"
        return 0
    fi
    
    echo "🚀 HOOK PÓS-CRIAÇÃO - VINDULA COSMOS"
    echo "===================================="
    echo "🎯 Arquivo: $target_file"
    echo ""
    
    # Validação baseada na extensão
    if [[ "$target_file" =~ \.(tsx|ts)$ ]]; then
        validate_react_file "$target_file"
        local react_result=$?
        
        echo ""
        run_prettier "$target_file"
        echo ""
        
        final_report "$target_file"
        
        return $react_result
        
    elif [[ "$target_file" =~ \.sql$ ]]; then
        validate_sql_file "$target_file"
        local sql_result=$?
        
        echo ""
        final_report "$target_file"
        
        return $sql_result
        
    else
        echo "ℹ️  Arquivo não requer validação específica"
        run_prettier "$target_file"
        echo ""
        final_report "$target_file"
        
        return 0
    fi
}

# Executa se detectar criação/edição de arquivo
if detect_file_creation; then
    main
fi