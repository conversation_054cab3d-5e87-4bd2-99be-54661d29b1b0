#!/bin/bash

# Hook de Validação React/TypeScript - Vindula Cosmos
PROJECT_ROOT="/Users/<USER>/projetos/vindulacosmos-e6b4d65c"

# Função para ler dados do hook via stdin
read_hook_data() {
    if [[ -t 0 ]]; then
        echo "{}"
    else
        cat
    fi
}

# Função para validar arquivos React/TypeScript
validate_react_file() {
    local hook_data="$1"
    
    if command -v jq >/dev/null 2>&1 && [[ "$hook_data" != "{}" ]]; then
        local file_path=$(echo "$hook_data" | jq -r '.tool_input.file_path // ""')
        local content=$(echo "$hook_data" | jq -r '.tool_input.content // ""')
        
        if [[ "$file_path" =~ \.(tsx?|jsx?)$ ]] && [[ -n "$content" ]]; then
            echo "⚛️  Validando React: $(basename "$file_path")"
            
            # Verificar violações críticas
            if echo "$content" | grep -q "useQuery\|useMutation" && ! echo "$file_path" | grep -q "/hooks/"; then
                echo "❌ useQuery/useMutation inline - criar hook em /src/lib/query/hooks/"
            fi
            
            if echo "$content" | grep -q "company_id.*user\." && ! echo "$content" | grep -q "useAuthStore"; then
                echo "❌ company_id incorreto - usar useAuthStore((state) => state.company_id)"
            fi
            
            local line_count=$(echo "$content" | wc -l)
            if [[ $line_count -gt 200 ]]; then
                echo "⚠️  Arquivo grande ($line_count linhas) - considere refatorar"
            fi
            
            if echo "$content" | grep -q "toast.*from.*react-hot-toast"; then
                echo "❌ Import incorreto - usar toastWithNotification de @/lib/notifications"
            fi
            
            if echo "$content" | grep -q "<img.*avatar" && ! echo "$content" | grep -q "EnhancedOptimizedAvatar"; then
                echo "❌ Usar EnhancedOptimizedAvatar em vez de <img> para avatars"
            fi
        fi
    fi
}

# Função principal
main() {
    local hook_data=$(read_hook_data)
    validate_react_file "$hook_data"
}

main