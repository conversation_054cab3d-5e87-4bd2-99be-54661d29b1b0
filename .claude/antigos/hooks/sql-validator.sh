#!/bin/bash

# Hook Mestre de Validação SQL - Vindula Cosmos
# Consolida TODAS as validações SQL: migrations, segurança, schema, RLS
# Substitui hooks separados por validação abrangente

PROJECT_ROOT="/Users/<USER>/projetos/vindulacosmos-e6b4d65c"
MIGRATIONS_DIR="$PROJECT_ROOT/supabase/migrations"
SCHEMA_DUMPS_DIR="$PROJECT_ROOT/schema_dumps"
DUMP_SCRIPT="$PROJECT_ROOT/scripts/dump-current-schema.sh"

# Função para detectar operações SQL
detect_sql_operation() {
    local sql_triggers=(
        # Criação/edição de arquivos SQL
        "CREATE TABLE|ALTER TABLE|CREATE FUNCTION|CREATE POLICY"
        "migra|migration|\.sql"
        
        # Busca por estruturas existentes
        "buscar.*function|como.*implementad|verificar.*structure"
        "existe.*function|definição.*table|procurar.*schema"
        
        # Funcionalidades que requerem SQL
        "implementar.*sistema|criar.*feature|nova.*funcionalidade"
        "permiss[õã]o|permission|RLS|policy"
        "gamifica[çc][ãa]o|stardust|notifica"
        
        # Contexto de desenvolvimento SQL
        "function|procedure|trigger|schema|database|supabase"
    )
    
    for trigger in "${sql_triggers[@]}"; do
        if [[ "$CLAUDE_USER_MESSAGE" =~ $trigger ]]; then
            return 0
        fi
    done
    
    if [[ "$CLAUDE_INTENT" =~ (sql|migration|database|schema|function|security) ]]; then
        return 0
    fi
    
    return 1
}

# Função para garantir schema dump atualizado
ensure_current_schema() {
    local current_date=$(date +%Y%m%d)
    local today_dump_found=false
    
    echo "📊 VERIFICANDO SCHEMA ATUAL..."
    
    for dump_dir in $(ls -1 "$SCHEMA_DUMPS_DIR" 2>/dev/null | sort -r); do
        local dump_date=$(echo "$dump_dir" | cut -d'_' -f1)
        if [[ "$dump_date" == "$current_date" ]]; then
            today_dump_found=true
            echo "✅ Schema do dia atual disponível: $dump_dir"
            break
        fi
    done
    
    if [[ "$today_dump_found" == false ]]; then
        echo "⚠️  Schema do dia atual não encontrado - use o mais recente ou execute: $DUMP_SCRIPT"
    fi
}

# Função para validação de migration
validate_migration() {
    echo "🔍 VALIDAÇÃO DE MIGRATION"
    echo "========================"
    
    if [[ ! -d "$MIGRATIONS_DIR" ]]; then
        echo "❌ Diretório de migrations não encontrado"
        return 1
    fi
    
    # Mostrar últimas migrations
    echo "📁 Últimas 3 migrations:"
    ls -la "$MIGRATIONS_DIR"/*.sql 2>/dev/null | tail -3 | awk '{print "   ", $9, "(" $6, $7, $8 ")"}'
    
    # Calcular próximo timestamp
    local last_migration=$(ls -1 "$MIGRATIONS_DIR"/*.sql 2>/dev/null | tail -1)
    if [[ -n "$last_migration" ]]; then
        local last_timestamp=$(basename "$last_migration" | cut -d'_' -f1)
        local next_num=$((${last_timestamp//[^0-9]/} + 1))
        local next_timestamp=$(printf "%014d" $next_num)
        echo "⏰ Último: $last_timestamp"
        echo "✅ Próximo timestamp: $next_timestamp"
        echo "📝 Formato: ${next_timestamp}_nome_da_migracao.sql"
    else
        echo "📝 Primeira migration - usar timestamp atual: $(date +%Y%m%d%H%M%S)"
    fi
    echo ""
}

# Função para auditoria de segurança
security_audit() {
    echo "🚨 AUDITORIA DE SEGURANÇA MULTI-TENANT"
    echo "======================================"
    echo ""
    
    echo "❌ VIOLAÇÕES CRÍTICAS A EVITAR:"
    echo ""
    echo "1. **COMPANY_ID COMO PARÂMETRO:**"
    echo "   ❌ CREATE FUNCTION get_data(company_id UUID)"
    echo "   ✅ Usar: auth.uid() + profiles para obter company_id"
    echo ""
    echo "2. **FUNÇÕES STARDUST PROIBIDAS:**"
    echo "   ❌ add_user_stardust() (atualiza tabela errada)"
    echo "   ✅ Usar: add_stardust(), subtract_stardust()"
    echo ""
    echo "3. **ROLES INCORRETAS:**"
    echo "   ❌ app_roles, access_control_entries diretamente"
    echo "   ✅ Usar: user_roles com padrão estabelecido"
    echo ""
    echo "4. **RLS AUSENTE:**"
    echo "   ❌ Permissões para 'anon' em dados sensíveis"
    echo "   ✅ RLS obrigatório em todas tabelas empresariais"
    echo ""
    
    echo "🛡️ FUNÇÕES HELPER OBRIGATÓRIAS:"
    echo "• check_same_company(company_id) - Verifica mesma empresa"
    echo "• check_admin_role() - Role admin/company_owner"  
    echo "• check_same_company_admin(company_id) - Empresa + admin"
    echo "• check_user_permission(resource_type, action_key) - Permissões"
    echo "• log_security_event(event_type, details) - Log eventos"
    echo ""
}

# Função para templates padrão
show_templates() {
    echo "📋 TEMPLATES PADRONIZADOS"
    echo "========================="
    echo ""
    echo "🔒 **RLS POLICY PADRÃO:**"
    echo "-- SELECT: Apenas mesma empresa"
    echo "CREATE POLICY \"table_select\" ON table_name"
    echo "FOR SELECT USING (public.check_same_company(company_id));"
    echo ""
    echo "-- INSERT/UPDATE/DELETE: Apenas admins da mesma empresa"
    echo "CREATE POLICY \"table_admin_only\" ON table_name" 
    echo "FOR INSERT/UPDATE/DELETE"
    echo "USING (public.check_same_company_admin(company_id));"
    echo ""
    echo "-- Com permissões específicas"
    echo "CREATE POLICY \"table_permission_based\" ON table_name"
    echo "FOR SELECT USING ("
    echo "  public.check_same_company(company_id)"
    echo "  AND public.check_user_permission('resource_type', 'action_key')"
    echo ");"
    echo ""
    
    echo "⚙️ **FUNÇÃO SEGURA PADRÃO:**"
    echo "CREATE OR REPLACE FUNCTION get_data_v1()"
    echo "RETURNS TABLE(...) AS \$\$"
    echo "DECLARE"
    echo "    current_company_id UUID;"
    echo "BEGIN"
    echo "    -- Obter company_id via auth.uid() + profiles"
    echo "    SELECT company_id INTO current_company_id"
    echo "    FROM profiles WHERE id = auth.uid();"
    echo "    "
    echo "    -- Verificar permissões"
    echo "    IF NOT public.check_same_company(current_company_id) THEN"
    echo "        RAISE EXCEPTION 'Acesso negado';"
    echo "    END IF;"
    echo "    "
    echo "    -- Lógica da função aqui..."
    echo "END;"
    echo "\$\$ LANGUAGE plpgsql SECURITY DEFINER;"
    echo ""
}

# Função para extrair estrutura de tabelas do schema
extract_table_structure() {
    local latest_dump=$(ls -1 "$SCHEMA_DUMPS_DIR" 2>/dev/null | tail -1)
    if [[ -z "$latest_dump" ]]; then
        echo "❌ Schema dump não encontrado" >&2
        return 1
    fi
    
    local schema_file="$SCHEMA_DUMPS_DIR/$latest_dump/public_schema_only.sql"
    if [[ ! -f "$schema_file" ]]; then
        echo "❌ Arquivo de schema não encontrado: $schema_file" >&2
        return 1
    fi
    
    
    # Criar arquivo temporário com estrutura
    local temp_structure="/tmp/vindula_table_structure.txt"
    > "$temp_structure"
    
    # Processar cada tabela
    grep -n "CREATE TABLE.*\"public\"" "$schema_file" | while read -r line; do
        local line_num=$(echo "$line" | cut -d: -f1)
        local table_name=$(echo "$line" | sed 's/.*"public"\."\([^"]*\)".*/\1/')
        
        echo "TABLE:$table_name" >> "$temp_structure"
        
        # Extrair colunas (próximas linhas até encontrar );)
        sed -n "${line_num},/^);/p" "$schema_file" | grep -E '^\s*"[^"]*"' | while read -r col_line; do
            local col_name=$(echo "$col_line" | sed 's/^\s*"\([^"]*\)".*/\1/')
            echo "  COLUMN:$col_name" >> "$temp_structure"
        done
    done
    
    # Retornar apenas o caminho do arquivo
    echo "$temp_structure"
}

# Função para validar campos SQL
validate_sql_fields() {
    local sql_content="$1"
    local structure_file="$2"
    
    
    # Extrair referências de tabela.coluna do SQL
    local table_column_refs=$(echo "$sql_content" | grep -oE '[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*' | sort -u)
    
    local validation_errors=0
    
    while IFS= read -r ref; do
        if [[ -n "$ref" ]]; then
            local table_alias=$(echo "$ref" | cut -d. -f1)
            local column_name=$(echo "$ref" | cut -d. -f2)
            
            
            # Buscar tabela real a partir do alias (padrão comum: p1 = profiles, etc)
            local real_table=""
            case "$table_alias" in
                "p1"|"p"|"profiles") real_table="profiles" ;;
                "u1"|"u"|"users") real_table="auth.users" ;;
                "d1"|"d"|"departments") real_table="departments" ;;
                "c1"|"c"|"companies") real_table="companies" ;;
                *) 
                    # Tentar encontrar tabela com nome similar
                    real_table=$(grep "TABLE:$table_alias" "$structure_file" | head -1 | cut -d: -f2)
                    if [[ -z "$real_table" ]]; then
                        echo "⚠️  Tabela não identificada para alias: $table_alias"
                        continue
                    fi
                    ;;
            esac
            
            # Verificar se coluna existe na tabela
            if [[ -n "$real_table" ]]; then
                local table_section=$(grep -A 100 "TABLE:$real_table" "$structure_file" | grep -B 100 "TABLE:" | head -n -1)
                if ! echo "$table_section" | grep -q "COLUMN:$column_name"; then
                    echo "❌ $ref - Coluna '$column_name' não existe em '$real_table'"
                    
                    # Sugerir coluna similar
                    local similar_col=$(echo "$table_section" | grep "COLUMN:" | cut -d: -f2 | grep -i "$column_name" | head -1)
                    if [[ -n "$similar_col" ]]; then
                        echo "   💡 Use: $table_alias.$similar_col"
                    fi
                    
                    validation_errors=$((validation_errors + 1))
                fi
            fi
        fi
    done <<< "$table_column_refs"
    
    if [[ $validation_errors -gt 0 ]]; then
        return 1
    else
        return 0
    fi
}

# Função para comandos úteis
show_useful_commands() {
    echo "🔍 COMANDOS DE VERIFICAÇÃO"
    echo "=========================="
    echo ""
    
    # Schema atual
    local latest_dump=$(ls -1 "$SCHEMA_DUMPS_DIR" 2>/dev/null | tail -1)
    if [[ -n "$latest_dump" ]]; then
        local dump_path="$SCHEMA_DUMPS_DIR/$latest_dump"
        echo "📊 **BUSCAR NO SCHEMA ATUAL:**"
        echo "• Function: grep -n 'FUNCTION nome_function' $dump_path/complete_schema_with_data.sql"
        echo "• Table: grep -n 'CREATE TABLE nome_table' $dump_path/public_schema_only.sql"
        echo "• Policy: grep -n 'CREATE POLICY' $dump_path/public_schema_only.sql"
        echo ""
    fi
    
    echo "🔍 **AUDITORIA DE SEGURANÇA:**"
    echo "• Company_id como parâmetro:"
    echo "  grep -rn 'function.*company_id.*(' supabase/migrations/"
    echo ""
    echo "• Funções Stardust proibidas:"
    echo "  grep -rn 'add_user_stardust' ."
    echo ""
    echo "• RLS ausente em tabelas:"
    echo "  grep -A 10 'CREATE TABLE' supabase/migrations/ | grep -L 'POLICY'"
    echo ""
}

# Função para ler dados do hook via stdin
read_hook_data() {
    # Ler JSON de entrada via stdin se disponível
    if [[ -t 0 ]]; then
        # Não há dados no stdin (execução manual)
        echo "{}"
    else
        # Ler dados JSON do stdin
        cat
    fi
}

# Função para detectar e validar SQLs pós-criação
detect_and_validate_sql() {
    local hook_data="$1"
    
    # Extrair informações do JSON de entrada
    local tool_name=""
    local file_path=""
    local content=""
    
    if command -v jq >/dev/null 2>&1 && [[ "$hook_data" != "{}" ]]; then
        tool_name=$(echo "$hook_data" | jq -r '.tool_name // ""')
        file_path=$(echo "$hook_data" | jq -r '.tool_input.file_path // ""')
        content=$(echo "$hook_data" | jq -r '.tool_input.content // ""')
    fi
    
    local validation_needed=false
    
    # 1. Validar arquivo SQL específico do hook
    if [[ "$file_path" =~ \.sql$ ]] && [[ -n "$content" ]]; then
        echo "🔍 Validando SQL: $(basename "$file_path")"
        validation_needed=true
        
        local structure_file=$(extract_table_structure)
        
        if [[ -f "$structure_file" ]]; then
            if ! validate_sql_fields "$content" "$structure_file"; then
                echo "🔧 Revise os campos antes de aplicar a migration"
            fi
        fi
    fi
    
}

# Função principal
main() {
    # Ler dados do hook via stdin
    local hook_data=$(read_hook_data)
    
    # Garantir schema atualizado (silencioso)
    ensure_current_schema > /dev/null 2>&1
    
    # Validação automática de campos SQL
    detect_and_validate_sql "$hook_data"
    
    # Mostrar apenas se migration detectada
    if [[ "$hook_data" =~ migration ]] || [[ "$hook_data" =~ \.sql ]]; then
        echo ""
        echo "⚠️  LEMBRETE: Jamais aplicar migrations automaticamente"
    fi
}

# Hook executa sempre - validação inteligente dentro do main()
main