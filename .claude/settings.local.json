{"permissions": {"allow": ["Bash(grep -l:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(npm run typecheck:*)", "Bash(bun run typecheck:*)", "Bash(claude --version)", "Bash(git add:*)", "Bash(rg:*)", "Bash(npm run lint)", "Bash(ls:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(head:*)", "<PERSON><PERSON>(tail:*)", "Bash(wc:*)", "Bash(sort:*)", "Bash(xargs:*)", "WebFetch(domain:docs.anthropic.com)", "mcp__task-master-ai__get_tasks", "mcp__task-master-ai__set_task_status", "mcp__supabase__list_tables", "mcp__supabase__list_projects", "mcp__supabase__list_projects", "Bash(./scripts/extract-specific-system.sh:*)", "Bash(gh issue:*)", "Bash(cp:*)", "Bash(git checkout:*)", "WebFetch(domain:github.com)", "WebFetch(domain:capacitorjs.com)", "WebFetch(domain:forum.ionicframework.com)", "<PERSON><PERSON>(sed:*)", "WebFetch(domain:documentation.onesignal.com)", "WebFetch(domain:hackerrangers.com)", "WebFetch(domain:coda.io)", "Bash(./scripts/dump-current-schema.sh:*)", "mcp__vindula-cosmos-especialista__vindula_validator", "WebFetch(domain:supabase.com)", "Bash(node:*)", "mcp__vindula-cosmos-especialista__vindula_advisor", "Bash(find * | xargs grep:*)", "Bash(find supabase/migrations:*)", "Bash(find /Users/<USER>/projetos/vindulacosmos-e6b4d65c/vindula-cosmos-especialista/src/knowledge/patterns:*)", "Bash(find /Users/<USER>/projetos/vindulacosmos-e6b4d65c -name *.sql | xargs grep:*)", "mcp__vindula-cosmos-especialista__vindula_assistant", "Bash(/document-feature)", "Bash(claude document-feature:*)", "WebFetch(domain:www.gupy.io)", "WebFetch(domain:www.feedz.com.br)", "Bash(.claude/commands/document-feature.sh:*)", "mcp__vindula-cosmos-mcp__vindula_analytics", "mcp__vindula-cosmos-mcp__vindula_recipe", "mcp__vindula-cosmos-mcp__vindula_recipe", "<PERSON><PERSON>(chmod:*)", "Bash(gh pr list:*)", "Bash(gh repo view:*)", "mcp__supabase__search_docs", "mcp__vindula-cosmos-mcp__vindula_status", "Bash(gh auth:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__vindula-cosmos-brain__vindula_recipe", "<PERSON><PERSON>(jq:*)", "<PERSON><PERSON>(bun run test:*)", "<PERSON><PERSON>(bunx playwright test:*)", "mcp__Playwright__browser_snapshot", "mcp__Playwright__browser_click", "mcp__Playwright__browser_type", "mcp__Playwright__browser_wait_for", "mcp__Playwright__browser_navigate", "mcp__Playwright__browser_take_screenshot", "<PERSON><PERSON>(bun run playwright test:*)", "WebFetch(domain:docs.stripe.com)", "mcp__sequentialthinking__sequentialthinking", "WebFetch(domain:spinify.com)", "WebFetch(domain:console.groq.com)", "WebFetch(domain:console.groq.com)", "WebFetch(domain:groq.com)", "WebFetch(domain:screencorp.com.br)", "WebFetch(domain:onsign.tv)", "WebFetch(domain:www.onsign.com)", "WebFetch(domain:www.sistematvi.com.br)", "WebFetch(domain:padlet.com)", "mcp__serena__initial_instructions", "mcp__serena__search_for_pattern", "mcp__serena__find_file", "mcp__serena__get_symbols_overview", "mcp__serena__find_symbol", "mcp__serena__list_memories", "mcp__serena__find_symbol", "mcp__serena__list_memories", "mcp__serena__list_dir", "mcp__serena__think_about_task_adherence", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__summarize_changes", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__write_memory", "mcp__serena__think_about_collected_information", "mcp__mcp-sequentialthinking-tools__sequentialthinking_tools", "mcp__upstash-context7__get-library-docs", "WebFetch(domain:blog.cloudflare.com)", "WebFetch(domain:tanstack.com)", "mcp__upstash-context7__resolve-library-id", "mcp__serena__get_current_config", "Bash(./scripts/smart-schema-dump.sh:*)", "mcp__vindula-cosmos-brain__vindula_health", "mcp__vindula-cosmos-brain__vindula_query_logs", "<PERSON><PERSON>(ollama list:*)", "<PERSON><PERSON>(ollama run:*)", "mcp__Playwright__browser_press_key", "mcp__vindula-cosmos-brain__vindula_analytics", "mcp__Playwright__browser_console_messages", "mcp__Playwright__browser_evaluate", "mcp__vindula-cosmos-brain__vindula_feedback", "mcp__serena__read_memory", "Bash(./scripts/update-brain-schema.sh:*)", "WebFetch(domain:www.vindula.com.br)"], "deny": ["Bash(bun run supabase db*)"]}, "enableAllProjectMcpServers": true}