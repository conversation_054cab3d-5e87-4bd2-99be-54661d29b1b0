# Comando: /fechar

## Descrição
Comando simples para fechar uma issue do GitHub fazendo commit, push e fechamento automático usando o commit-specialist.

## Uso
```
/fechar <NUMERO_DA_ISSUE>
```

## Exemplo
```
/fechar 298
```

## O que faz
Automaticamente executa o commit-specialist com o prompt:
"Faça o comentário, commit, push e feche a issue #<NUMERO> do GitHub"

## Implementação
O comando chama diretamente o commit-specialist que já tem toda a inteligência para:
- Analisar as alterações
- Criar commit semântico  
- Fazer push
- Comentar na issue
- Fechar a issue

É apenas um atalho para não precisar digitar o prompt completo toda vez.