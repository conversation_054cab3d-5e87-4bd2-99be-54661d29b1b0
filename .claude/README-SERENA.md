# 🤖 <PERSON>ra - Claude Code com Serena

## Uso

Para iniciar Claude Code com Serena integrado:

```bash
claudera
```

## Como foi configurado

1. **Script:** `.claude/scripts/claudera.sh` - Inicia Claude com `--append-system-prompt` do Serena
2. **Alias:** `claudera` adicionado ao `~/.zshrc`

## Pré-requisitos

Instale o `uvx`:
```bash
pip install uvx
```

## Como funciona

- Usa `--append-system-prompt` para carregar instruções do Serena automaticamente
- Baseado na recomendação oficial do Serena
- Experimental, mas recomendado pelos desenvolvedores do Serena

## Alternativa Manual

Se preferir inicializar manualmente em cada sessão:
```bash
/serena
```

## Expandindo

O script `claudera.sh` pode ser expandido para incluir outros system prompts ou configurações específicas do projeto.