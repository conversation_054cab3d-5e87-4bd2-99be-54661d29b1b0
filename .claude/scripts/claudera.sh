#!/bin/bash

# Script para iniciar Claude Code com instruções do Serena automaticamente carregadas
# Baseado na recomendação oficial do Serena: https://github.com/oraios/serena

echo "🚀 Iniciando Claude Code com Serena integrado..."

# Verifica se uvx está disponível
if ! command -v uvx &> /dev/null; then
    echo "❌ uvx não encontrado. Instale com: pip install uvx"
    exit 1
fi

# Inicia Claude Code com system prompt do Serena
claude --append-system-prompt "$(uvx --from git+https://github.com/oraios/serena serena print-system-prompt)" "$@"