# CRUSH.md - Vindula Cosmos Engineering Guidelines

## Commands
- Build: `bun build`
- Dev: `bun dev`
- Lint: `bun run lint`
- Type check: `bun run type-check`
- Test all: `bun test`
- Single test: `bun test --grep "test name"` (Playwright)
- Component test: `bun run test:ui`

## Code Style
- **Toolchain**: Use Bun exclusively (no npm/yarn)
- **TypeScript**: Strict mode; JSDoc on all files (`<AUTHOR> Internet 2025`)
- **Imports**: Absolute paths (e.g., `@/components`)
- **Naming**: PascalCase for components; camelCase for hooks
- **Error Handling**: Use `logQueryEvent` (NO `console.log`)
- **Supabase**: Always use RLS; get `company_id` via `useAuthStore`
- **Components**: Follow Cursor rules:
  - HeroSection for page headers
  - GenericPermissionGate for access control
  - RefreshButton standard component
- **STARDUST**: Format with `<Sparkles>` icon (never points/emojis)

## Testing
- Single-test workflow: `bun test --grep "[describe block]"`
- Always verify TanStack Query test behavior