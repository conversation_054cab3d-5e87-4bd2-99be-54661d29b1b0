/**
 * Edge Function para envio de emails de convite de usuários com templates premium.
 * Suporta dois tipos de email: convite tradicional e credenciais temporárias.
 * Templates com design Cosmos otimizados para máxima compatibilidade.
 * <AUTHOR> Internet 2025
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-sentry-trace, baggage',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Request headers:', req.headers)
    const requestData = await req.json()
    console.log('Received request data:', requestData)

    const { email, full_name, company_id, invited_by, sign_in_token, temp_password } = requestData

    // Verificar campos obrigatórios com base no tipo de email
    if (!email || !full_name || !company_id) {
      console.error('Missing required fields:', { email, full_name, company_id })
      return new Response(
        JSON.stringify({ 
          error: 'Missing required fields',
          details: { email, full_name, company_id }
        }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Determinar o tipo de email e conteúdo com base nos parâmetros
    let subject = '';
    let htmlContent = '';

    // Obter a URL base correta para redirecionamentos
    const origin = req.headers.get('origin') || req.headers.get('referer')?.split('/').slice(0, 3).join('/') || 'https://app.vindula.net';

    // Determinar tipo de email e aplicar template correspondente

    // URL base para o logo (usando URL pública acessível)
    const logoUrl = 'https://cosmos.vindula.com.br/vindula-cosmos.png';

    if (temp_password) {
      // Email com credenciais temporárias
      subject = 'Suas credenciais de acesso - Vindula Cosmos';
      htmlContent = `
        <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html xmlns="http://www.w3.org/1999/xhtml">
        <head>
          <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>Suas Credenciais - Vindula Cosmos</title>
          <style type="text/css">
            /* Estilos para garantir compatibilidade móvel */
            .email-title { color: #ffffff !important; }
            .email-title-fallback { color: #ffffff; }
            h1 { color: #ffffff !important; }
            
            /* Media queries para dispositivos móveis */
            @media only screen and (max-width: 600px) {
              .email-title { color: #ffffff !important; }
              .email-title-fallback { color: #ffffff !important; }
              h1 { color: #ffffff !important; }
            }
            
            /* Fallback para clientes que não suportam CSS */
            [data-ogsc] .email-title { color: #ffffff !important; }
            [data-ogsc] h1 { color: #ffffff !important; }
          </style>
        </head>
        <body style="margin: 0; padding: 0; font-family: Arial, Helvetica, sans-serif; background-color: #000000; color: #f8fafc;">
          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
            <tr>
              <td>
                <table align="center" border="0" cellpadding="0" cellspacing="0" width="600" style="border-collapse: collapse; background-color: #0a0f1a; border-radius: 8px; margin-top: 40px; margin-bottom: 40px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);">
                  <!-- HEADER -->
                  <tr>
                    <td align="center" bgcolor="#050914" style="padding: 30px 20px; background-image: linear-gradient(135deg, #0a0f1a, #050914); border-top-left-radius: 8px; border-top-right-radius: 8px;">
                      <table border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                          <td align="center" style="padding-bottom: 20px;">
                            <img src="${logoUrl}" alt="Vindula Cosmos Logo" width="120" style="display: block; border: 0;" />
                          </td>
                        </tr>
                        <tr>
                          <td align="center">
                            <h1 class="email-title email-title-fallback" style="color: #ffffff !important; font-size: 28px; font-weight: 600; margin: 0; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">Bem-vindo ao Cosmos!</h1>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  
                  <!-- CONTEÚDO -->
                  <tr>
                    <td bgcolor="#0a0f1a" style="padding: 35px;">
                      <table border="0" cellpadding="0" cellspacing="0" width="100%">
                        <!-- Saudação -->
                        <tr>
                          <td style="padding-bottom: 20px;">
                            <p style="color: #f8fafc; font-size: 20px; font-weight: 600; margin: 0;">Olá, ${full_name}!</p>
                          </td>
                        </tr>
                        
                        <!-- Mensagem -->
                        <tr>
                          <td style="padding-bottom: 30px;">
                            <p style="color: #cbd5e1; margin: 0 0 15px 0;">Sua conta no <strong style="color: #f8fafc;">Vindula Cosmos</strong> foi criada com sucesso!</p>
                            <p style="color: #cbd5e1; margin: 0;">Use as credenciais abaixo para fazer seu primeiro acesso:</p>
                          </td>
                        </tr>
                        
                        <!-- Credenciais -->
                        <tr>
                          <td style="padding: 25px 0;">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #111827; border-radius: 8px; border-left: 4px solid #f97316;">
                              <tr>
                                <td style="padding: 20px;">
                                  <p style="color: #f8fafc; font-size: 16px; margin: 0 0 10px 0;"><strong>Email:</strong></p>
                                  <p style="color: #cbd5e1; font-family: monospace; font-size: 16px; margin: 0 0 15px 0;">${email}</p>
                                  <p style="color: #f8fafc; font-size: 16px; margin: 0 0 10px 0;"><strong>Senha temporária:</strong></p>
                                  <p style="color: #cbd5e1; font-family: monospace; font-size: 18px; font-weight: bold; margin: 0;">${temp_password}</p>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                        
                        <!-- Botão de ação -->
                        <tr>
                          <td align="center" style="padding: 35px 0;">
                            <table border="0" cellpadding="0" cellspacing="0">
                              <tr>
                                <td align="center" bgcolor="#f97316" style="border-radius: 8px;">
                                  <a href="${origin}/auth" target="_blank" style="display: inline-block; padding: 14px 32px; font-size: 16px; color: #ffffff; text-decoration: none; font-weight: 600; letter-spacing: 0.5px; border-radius: 8px; background-color: #f97316;">Acessar Vindula Cosmos</a>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                        
                        <!-- Caixa de informação -->
                        <tr>
                          <td style="padding: 0 0 30px 0;">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #111827; border-radius: 8px; border-left: 4px solid #f59e0b;">
                              <tr>
                                <td style="padding: 18px;">
                                  <p style="color: #cbd5e1; font-size: 14px; margin: 0;"><strong style="color: #f8fafc;">Importante:</strong> Por motivos de segurança, você será solicitado a alterar sua senha no primeiro acesso.</p>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  
                  <!-- FOOTER -->
                  <tr>
                    <td bgcolor="#050914" style="padding: 25px; text-align: center; border-top: 1px solid rgba(255, 255, 255, 0.1); border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;">
                      <p style="color: #94a3b8; font-size: 13px; margin: 5px 0;">Este é um email automático, por favor não responda.</p>
                      <p style="color: #94a3b8; font-size: 13px; margin: 5px 0;">&copy; 2025 Vindula - Todos os direitos reservados.</p>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </body>
        </html>
      `;
    } else if (sign_in_token && invited_by) {
      // Email de convite tradicional
      const signUpUrl = `${origin}/auth?token=${sign_in_token}`;
      subject = 'Convite para o Vindula Cosmos';
      htmlContent = `
        <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html xmlns="http://www.w3.org/1999/xhtml">
        <head>
          <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>Convite - Vindula Cosmos</title>
          <style type="text/css">
            /* Estilos para garantir compatibilidade móvel */
            .email-title { color: #ffffff !important; }
            .email-title-fallback { color: #ffffff; }
            h1 { color: #ffffff !important; }
            
            /* Media queries para dispositivos móveis */
            @media only screen and (max-width: 600px) {
              .email-title { color: #ffffff !important; }
              .email-title-fallback { color: #ffffff !important; }
              h1 { color: #ffffff !important; }
            }
            
            /* Fallback para clientes que não suportam CSS */
            [data-ogsc] .email-title { color: #ffffff !important; }
            [data-ogsc] h1 { color: #ffffff !important; }
          </style>
        </head>
        <body style="margin: 0; padding: 0; font-family: Arial, Helvetica, sans-serif; background-color: #000000; color: #f8fafc;">
          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
            <tr>
              <td>
                <table align="center" border="0" cellpadding="0" cellspacing="0" width="600" style="border-collapse: collapse; background-color: #0a0f1a; border-radius: 8px; margin-top: 40px; margin-bottom: 40px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);">
                  <!-- HEADER -->
                  <tr>
                    <td align="center" bgcolor="#050914" style="padding: 30px 20px; background-image: linear-gradient(135deg, #0a0f1a, #050914); border-top-left-radius: 8px; border-top-right-radius: 8px;">
                      <table border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                          <td align="center" style="padding-bottom: 20px;">
                            <img src="${logoUrl}" alt="Vindula Cosmos Logo" width="120" style="display: block; border: 0;" />
                          </td>
                        </tr>
                        <tr>
                          <td align="center">
                            <h1 class="email-title email-title-fallback" style="color: #ffffff !important; font-size: 28px; font-weight: 600; margin: 0; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">Você foi convidado!</h1>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  
                  <!-- CONTEÚDO -->
                  <tr>
                    <td bgcolor="#0a0f1a" style="padding: 35px;">
                      <table border="0" cellpadding="0" cellspacing="0" width="100%">
                        <!-- Saudação -->
                        <tr>
                          <td style="padding-bottom: 20px;">
                            <p style="color: #f8fafc; font-size: 20px; font-weight: 600; margin: 0;">Olá, ${full_name}!</p>
                          </td>
                        </tr>
                        
                        <!-- Mensagem -->
                        <tr>
                          <td style="padding-bottom: 30px;">
                            <p style="color: #cbd5e1; margin: 0 0 15px 0;">Você foi convidado para se juntar ao <strong style="color: #f8fafc;">Vindula Cosmos</strong>, a plataforma de colaboração corporativa que vai transformar a forma como sua equipe trabalha e interage.</p>
                            <p style="color: #cbd5e1; margin: 0;">Aceite o convite e descubra um universo de possibilidades para sua empresa!</p>
                          </td>
                        </tr>
                        
                        <!-- Botão de ação -->
                        <tr>
                          <td align="center" style="padding: 35px 0;">
                            <table border="0" cellpadding="0" cellspacing="0">
                              <tr>
                                <td align="center" bgcolor="#f97316" style="border-radius: 8px;">
                                  <a href="${signUpUrl}" target="_blank" style="display: inline-block; padding: 14px 32px; font-size: 16px; color: #ffffff; text-decoration: none; font-weight: 600; letter-spacing: 0.5px; border-radius: 8px; background-color: #f97316;">Criar Minha Conta</a>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                        
                        <!-- Recursos em destaque -->
                        <tr>
                          <td style="padding: 20px 0;">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: rgba(30, 41, 59, 0.5); border-radius: 8px; border-left: 4px solid #3b82f6;">
                              <tr>
                                <td style="padding: 20px;">
                                  <p style="color: #f8fafc; font-size: 16px; font-weight: 600; margin: 0 0 15px 0;">🚀 O que você encontrará no Cosmos:</p>
                                  <ul style="color: #cbd5e1; font-size: 14px; margin: 0; padding-left: 20px;">
                                    <li style="margin-bottom: 8px;">Feed interativo com posts e reações</li>
                                    <li style="margin-bottom: 8px;">Sistema de gamificação com pontos e medalhas</li>
                                    <li style="margin-bottom: 8px;">Chat em tempo real e mensagens diretas</li>
                                    <li style="margin-bottom: 8px;">Centro de conhecimento e documentação</li>
                                    <li style="margin-bottom: 8px;">IA integrada para aumentar produtividade</li>
                                  </ul>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                        
                        <!-- Caixa de informação -->
                        <tr>
                          <td style="padding: 0 0 30px 0;">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #111827; border-radius: 8px; border-left: 4px solid #f59e0b;">
                              <tr>
                                <td style="padding: 18px;">
                                  <p style="color: #cbd5e1; font-size: 14px; margin: 0;"><strong style="color: #f8fafc;">Importante:</strong> Este convite expira em 7 dias. Se você não esperava receber este convite, pode ignorar este email.</p>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  
                  <!-- FOOTER -->
                  <tr>
                    <td bgcolor="#050914" style="padding: 25px; text-align: center; border-top: 1px solid rgba(255, 255, 255, 0.1); border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;">
                      <p style="color: #94a3b8; font-size: 13px; margin: 5px 0;">Este é um email automático, por favor não responda.</p>
                      <p style="color: #94a3b8; font-size: 13px; margin: 5px 0;">&copy; 2025 Vindula - Todos os direitos reservados.</p>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </body>
        </html>
      `;
    } else {
      return new Response(
        JSON.stringify({ 
          error: 'Invalid parameters for email type',
          details: 'Either sign_in_token and invited_by OR temp_password must be provided'
        }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Sending email to:', email)
    console.log('Email type:', temp_password ? 'credentials' : 'invite')

    // Usar variável de ambiente para a API key
    const mailersendApiKey = Deno.env.get('MAILERSEND_API_KEY');
    if (!mailersendApiKey) {
      throw new Error('MAILERSEND_API_KEY environment variable not set');
    }

    const mailersendResponse = await fetch('https://api.mailersend.com/v1/email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${mailersendApiKey}`,
      },
      body: JSON.stringify({
        "from": {
          "email": "<EMAIL>",
          "name": "Vindula"
        },
        "to": [
          {
            "email": email,
            "name": full_name
          }
        ],
        "subject": subject,
        "html": htmlContent
      }),
    })

    console.log('MailerSend response status:', mailersendResponse.status)
    const responseText = await mailersendResponse.text()
    console.log('MailerSend response body:', responseText)

    if (!mailersendResponse.ok) {
      throw new Error(`MailerSend API error: ${responseText}`)
    }

    return new Response(
      JSON.stringify({ message: 'Email sent successfully' }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      },
    )
  } catch (error: any) {
    console.error("Error in send-invite-email function:", error)
    return new Response(
      JSON.stringify({ 
        error: error.message,
        details: error.toString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})