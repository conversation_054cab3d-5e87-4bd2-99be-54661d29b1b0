/**
 * Edge Function para gerar resumos automáticos de conversas
 * <AUTHOR> Internet 2025
 */
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { OpenAI } from "https://deno.land/x/openai@v4.24.0/mod.ts"
import { initSentry, withSentry, timeOperation } from "../_shared/sentry.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0'

// Inicializar Sentry
initSentry()

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-sentry-trace, baggage',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface Message {
  content: string;
  sender_name: string;
  created_at: string;
  is_current_user: boolean;
}

interface SummaryRequest {
  chat_id?: string;
  channel_id?: string;
  current_user_name: string;
  context?: string;
  summary_type?: 'detailed';
  time_period: string;
  period_label?: string;
}

serve(withSentry("conversation-summary", async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const { chat_id, channel_id, current_user_name, context, summary_type = 'detailed', time_period, period_label }: SummaryRequest = await req.json()

    if (!Deno.env.get('GROQ_API_KEY')) {
      throw new Error('GROQ_API_KEY is not configured')
    }

    if (!chat_id && !channel_id) {
      throw new Error('Either chat_id or channel_id is required')
    }

    if (!time_period) {
      throw new Error('time_period is required')
    }

    // Criar cliente Supabase
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Mapear períodos para horas
    const periodHoursMap: Record<string, number> = {
      '24h': 24,
      '3d': 72,
      '7d': 168,
      '30d': 720
    }

    const hours = periodHoursMap[time_period]
    if (!hours) {
      throw new Error(`Invalid time_period: ${time_period}`)
    }

    // Calcular data de corte
    const cutoffDate = new Date()
    cutoffDate.setHours(cutoffDate.getHours() - hours)

    console.log('Fetching messages for summary:', {
      chat_id,
      channel_id,
      time_period,
      period_label,
      cutoffDate: cutoffDate.toISOString(),
      current_user_name
    })

    // Buscar mensagens do período
    const columnName = chat_id ? "chat_id" : "channel_id"
    const columnValue = chat_id || channel_id

    const { data: messagesData, error: messagesError } = await supabase
      .from("chat_messages")
      .select(`
        content,
        created_at,
        sender:profiles!chat_messages_sender_id_fkey(
          full_name
        )
      `)
      .eq(columnName, columnValue)
      .gte('created_at', cutoffDate.toISOString())
      .is('deleted_at', null)
      .not('content', 'is', null)
      .neq('content', '')
      .order("created_at", { ascending: true })
      .limit(1000) // Limite seguro para evitar timeout

    if (messagesError) {
      console.error('Erro ao buscar mensagens:', messagesError)
      throw new Error(`Database error: ${messagesError.message}`)
    }

    const messages = messagesData || []
    
    console.log('Messages fetched:', {
      totalMessages: messages.length,
      periodHours: hours,
      firstMessage: messages[0]?.created_at,
      lastMessage: messages[messages.length - 1]?.created_at
    })

    // Tratar casos com poucas mensagens de forma amigável
    if (messages.length === 0) {
      return new Response(
        JSON.stringify({ 
          summary: `## 📭 Sem Atividade Recente

### ${period_label}
Não foram encontradas mensagens neste chat durante **${period_label}**.

### 💡 Sugestões
- Tente um período maior (como "Último mês")
- Verifique se há atividade em outros chats
- Este período pode ter sido mais silencioso

### 📊 Estatísticas
- **Mensagens encontradas:** 0
- **Período analisado:** ${period_label}`,
          summary_type,
          message_count: 0,
          time_period,
          period_label,
          usage: {
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0,
            prompt_time: 0,
            completion_time: 0,
            total_time: 0
          }
        }),
        { 
          headers: { 
            ...corsHeaders, 
            'Content-Type': 'application/json',
            'Cache-Control': 'no-store, no-cache, must-revalidate'
          } 
        }
      )
    }

    if (messages.length === 1) {
      const singleMessage = messages[0]
      return new Response(
        JSON.stringify({ 
          summary: `## 📝 Atividade Mínima

### ${period_label}
Apenas **1 mensagem** foi encontrada durante **${period_label}**.

### 💬 Mensagem Encontrada
**${singleMessage.sender?.full_name || 'Usuário'}** (${new Date(singleMessage.created_at).toLocaleString('pt-BR')}):
> ${singleMessage.content.length > 200 ? singleMessage.content.substring(0, 200) + '...' : singleMessage.content}

### 💡 Sugestões
- Tente um período maior para obter mais contexto
- Uma única mensagem não permite análise de conversa
- Considere usar "Último mês" para análise completa

### 📊 Estatísticas
- **Mensagens encontradas:** 1
- **Período analisado:** ${period_label}`,
          summary_type,
          message_count: 1,
          time_period,
          period_label,
          usage: {
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0,
            prompt_time: 0,
            completion_time: 0,
            total_time: 0
          }
        }),
        { 
          headers: { 
            ...corsHeaders, 
            'Content-Type': 'application/json',
            'Cache-Control': 'no-store, no-cache, must-revalidate'
          } 
        }
      )
    }

    // Preparar conversa para resumo
    const conversationText = messages.map(msg => 
      `${msg.sender?.full_name || 'Usuário'}: ${msg.content}`
    ).join('\n')

    const openai = new OpenAI({
      baseURL: "https://api.groq.com/openai/v1",
      apiKey: Deno.env.get('GROQ_API_KEY')
    })

    // Criar contexto temporal
    const timeContext = period_label ? `
📅 PERÍODO ANALISADO: ${period_label}
📊 MENSAGENS ENCONTRADAS: ${messages.length} mensagens` : ''

    const systemMessage = `Você é especialista em resumir conversas corporativas brasileiras.

TAREFA: Crie um resumo DETALHADO da conversa em formato markdown bem estruturado.${timeContext}

FOQUE EM:
📋 PONTOS PRINCIPAIS: Decisões, tarefas, problemas e próximos passos
👥 PARTICIPANTES: Quem participou mais e principais contribuições  
⏰ CRONOLOGIA: Sequência dos assuntos e mudanças de tópico
🎯 CONCLUSÕES: O que foi decidido e o que ficou pendente

FORMATO OBRIGATÓRIO (use exatamente esta estrutura markdown):

## 📋 Resumo da Conversa

### 👥 Participantes
- **Nome da Pessoa**: principais contribuições e papel na conversa
- **Outra Pessoa**: o que discutiu e decidiu

### 🎯 Pontos Principais  
- **Tópico importante**: descrição do que foi discutido
- **Outro tópico**: detalhes relevantes mencionados
- **Problema identificado**: como foi abordado

### ✅ Decisões e Ações
- **Decisão tomada**: quem ficou responsável e prazo (se mencionado)
- **Ação definida**: detalhes de implementação
- **Tarefa atribuída**: pessoa responsável

### ⏳ Pendências
- **Item pendente**: o que precisa ser resolvido ainda
- **Questão em aberto**: próximos passos necessários

**IMPORTANTE**: 
- Use markdown corretamente (##, ###, -, **)
- Preserve emojis na estrutura
- Seja conciso mas informativo
- Destaque nomes de pessoas em **negrito**
- Use listas com - (hífen)

CONVERSA:
${conversationText}`

    const completion = await timeOperation("groq-conversation-summary", async () => {
      return await openai.chat.completions.create({
        model: "llama-3.1-8b-instant",
        messages: [
          {
            role: "system",
            content: systemMessage
          },
          {
            role: "user",
            content: "Gere resumo detalhado em markdown:"
          }
        ],
        temperature: 0.3,
        max_tokens: 1000,
        top_p: 0.9,
        stream: false
      })
    })
    
    const summary = completion.choices[0]?.message?.content
    const usage = completion.usage

    if (!summary) {
      throw new Error('No summary generated from AI')
    }

    console.log('Conversation summary generated successfully')
    console.log('Summary length:', summary.length)
    console.log('Token usage:', {
      prompt_tokens: usage?.prompt_tokens,
      completion_tokens: usage?.completion_tokens,
      total_tokens: usage?.total_tokens
    })

    return new Response(
      JSON.stringify({ 
        summary: summary.trim(),
        summary_type,
        message_count: messages.length,
        time_period,
        period_label,
        usage: {
          prompt_tokens: usage?.prompt_tokens || 0,
          completion_tokens: usage?.completion_tokens || 0,
          total_tokens: usage?.total_tokens || 0,
          prompt_time: usage?.prompt_time || 0,
          completion_time: usage?.completion_time || 0,
          total_time: usage?.total_time || 0
        }
      }),
      { 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json',
          'Cache-Control': 'no-store, no-cache, must-revalidate'
        } 
      }
    )
  } catch (error) {
    console.error('Error in conversation-summary:', error)
    
    return new Response(
      JSON.stringify({ 
        error: error.message,
        details: error.toString(),
        timestamp: new Date().toISOString(),
        summary: null
      }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json',
          'Cache-Control': 'no-store'
        }
      }
    )
  }
}))