/**
 * Edge Function: Generate Knowledge Page
 * Gera páginas de conhecimento personalizadas usando IA
 * <AUTHOR> Internet 2025
 */
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { OpenAI } from "https://deno.land/x/openai@v4.24.0/mod.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-sentry-trace, baggage',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface GenerateKnowledgePageRequest {
  prompt: string;
  category: string;
  spaceId: string;
  formData: Record<string, string>;
}

interface GeneratedPage {
  title: string;
  excerpt: string;
  content: string;
  tags: string[];
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('🚀 Iniciando geração de página de conhecimento...');
    
    // Verificar autenticação
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      console.error('❌ Token de autorização não fornecido');
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Inicializar cliente Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Verificar usuário autenticado
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      console.error('❌ Erro de autenticação:', authError);
      return new Response(
        JSON.stringify({ error: 'Invalid or expired token' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Obter company_id do usuário
    const { data: profile } = await supabase
      .from('profiles')
      .select('company_id')
      .eq('id', user.id)
      .single()

    if (!profile?.company_id) {
      console.error('❌ Company ID não encontrado para o usuário');
      return new Response(
        JSON.stringify({ error: 'User company not found' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('✅ Usuário autenticado:', user.id, 'Company:', profile.company_id);

    // Parse do corpo da requisição
    const { prompt, category, spaceId, formData }: GenerateKnowledgePageRequest = await req.json()

    console.log('📝 Dados recebidos:', { category, spaceId, prompt: prompt.substring(0, 100) + '...' });

    // Validar entrada
    if (!prompt || !category || !spaceId) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: prompt, category, spaceId' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Verificar se o espaço existe e pertence à empresa
    const { data: space, error: spaceError } = await supabase
      .from('knowledge_spaces')
      .select('id, name, company_id')
      .eq('id', spaceId)
      .eq('company_id', profile.company_id)
      .single()

    if (spaceError || !space) {
      console.error('❌ Espaço não encontrado ou sem permissão:', spaceError);
      return new Response(
        JSON.stringify({ error: 'Knowledge space not found or access denied' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('✅ Espaço validado:', space.name);

    // Configurar Groq
    const groqApiKey = Deno.env.get('GROQ_API_KEY')
    if (!groqApiKey) {
      console.error('❌ GROQ_API_KEY não configurada');
      return new Response(
        JSON.stringify({ error: 'Groq API key not configured' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const openai = new OpenAI({
      baseURL: "https://api.groq.com/openai/v1",
      apiKey: groqApiKey
    })

    // Prompt do sistema otimizado para documentos prontos
    const systemPrompt = `Você é um especialista em documentação corporativa e criação de documentos operacionais. 

Sua tarefa é criar um DOCUMENTO PRONTO PARA USO, não um guia ou tutorial. O documento deve ser funcional e utilizável imediatamente.

TIPO DE DOCUMENTO: ${category}

INSTRUÇÕES CRÍTICAS:
1. Crie um DOCUMENTO FUNCIONAL, não um guia de como fazer
2. Use placeholders no formato {{CAMPO_EDITAVEL}} para campos que devem ser preenchidos
3. O documento deve estar pronto para ser usado/impresso/assinado
4. Inclua todas as seções necessárias para o tipo de documento
5. Use formatação HTML profissional
6. Mantenha linguagem formal e juridicamente adequada quando aplicável
7. Inclua campos para assinaturas, datas, e aprovações quando necessário

FORMATAÇÃO HTML REQUERIDA:
- Use tags HTML semânticas: <h1>, <h2>, <h3>, <p>, <ul>, <ol>, <li>, <table>, <tr>, <td>, <th>
- Para campos editáveis use: <span class="bg-yellow-100 px-2 py-1 border-b-2 border-yellow-400">{{NOME_DO_CAMPO}}</span>
- Para assinaturas use: <div class="border-b-2 border-gray-400 w-64 mt-8 mb-2"></div>
- Para tabelas use: <table class="table-auto border-collapse border border-gray-300 w-full">
- Para alertas legais use: <div class="bg-red-50 border-l-4 border-red-400 p-4 my-4">
- Para seções importantes use: <div class="bg-gray-50 p-4 rounded border">

EXEMPLOS DE PLACEHOLDERS:
- {{NOME_FUNCIONARIO}} - Nome do funcionário
- {{DATA_ATUAL}} - Data de hoje
- {{CARGO}} - Cargo do funcionário
- {{MOTIVO_ADVERTENCIA}} - Descrição do motivo
- {{NOME_SUPERVISOR}} - Nome do supervisor
- {{NUMERO_PROTOCOLO}} - Número do protocolo

RESPONDA APENAS COM UM JSON no seguinte formato:
{
  "title": "Título do documento (máximo 100 caracteres)",
  "excerpt": "Resumo do propósito do documento (máximo 300 caracteres)",
  "content": "Documento completo em HTML com placeholders (mínimo 500 palavras)",
  "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"]
}

Categoria: ${category}
Espaço: ${space.name}`

    console.log('🤖 Enviando requisição para Groq (llama-3.1-8b-instant)...');

    // Fazer requisição para Groq
    const openaiResponse = await openai.chat.completions.create({
      model: 'llama-3.1-8b-instant',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 3000,
      response_format: { type: "json_object" }
    })

    console.log('✅ Resposta recebida da Groq');

    // Parse da resposta
    let generatedPage: GeneratedPage
    try {
      generatedPage = JSON.parse(openaiResponse.choices[0].message.content || '{}')
      console.log('📄 Página gerada:', {
        title: generatedPage.title,
        excerptLength: generatedPage.excerpt?.length || 0,
        contentLength: generatedPage.content?.length || 0,
        tagsCount: generatedPage.tags?.length || 0
      });
    } catch (parseError) {
      console.error('❌ Erro ao fazer parse da resposta Groq:', parseError);
      throw new Error('Failed to parse Groq response')
    }

    // Validar resposta
    if (!generatedPage.title || !generatedPage.content) {
      console.error('❌ Resposta da Groq incompleta');
      throw new Error('Incomplete response from Groq')
    }

    // Limitar e validar dados
    generatedPage.title = generatedPage.title.substring(0, 100)
    generatedPage.excerpt = (generatedPage.excerpt || '').substring(0, 300)
    generatedPage.tags = (generatedPage.tags || []).slice(0, 10).map(tag => 
      tag.toLowerCase().trim().substring(0, 30)
    ).filter(tag => tag.length > 0)

    // Adicionar tags baseadas na categoria e dados do formulário
    const categoryTags = [category.toLowerCase().replace(/\s+/g, '-')]
    const formTags = Object.values(formData)
      .filter(value => value && value.length > 0)
      .map(value => value.toLowerCase().replace(/[^a-z0-9\s]/g, '').trim())
      .filter(tag => tag.length > 2 && tag.length <= 20)
      .slice(0, 3)

    generatedPage.tags = [...new Set([...generatedPage.tags, ...categoryTags, ...formTags])].slice(0, 8)

    console.log('✅ Página processada e validada');
    console.log('🏷️ Tags finais:', generatedPage.tags);

    // Retornar resultado
    return new Response(
      JSON.stringify(generatedPage),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('❌ Erro na geração da página:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate knowledge page',
        details: errorMessage 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
}) 