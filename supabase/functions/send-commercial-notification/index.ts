import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-sentry-trace, baggage',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Request headers:', req.headers)
    const requestData = await req.json()
    console.log('Received request data:', requestData)

    const { lead_id } = requestData

    if (!lead_id) {
      console.error('Missing lead_id')
      return new Response(
        JSON.stringify({ 
          error: 'Missing required field: lead_id'
        }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Processing lead:', lead_id)

    // Inicializar cliente Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Buscar dados do lead comercial
    const { data: leadData, error: leadError } = await supabase
      .from('commercial_leads')
      .select(`
        *,
        profiles!commercial_leads_user_id_fkey(full_name, email),
        companies!inner(name)
      `)
      .eq('id', lead_id)
      .single()

    if (leadError || !leadData) {
      console.error('Error fetching lead data:', leadError)
      return new Response(
        JSON.stringify({ 
          error: 'Lead not found',
          details: leadError?.message
        }),
        { 
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Lead data fetched:', leadData)

    // Buscar nomes dos planos separadamente para evitar problemas de relacionamento
    let currentPlanName = 'Plano atual'
    let requestedPlanName = 'Plano solicitado'

    // Buscar plano atual se existir
    if (leadData.current_plan_id) {
      const { data: currentPlanData } = await supabase
        .from('subscription_plans')
        .select('name')
        .eq('id', leadData.current_plan_id)
        .single()
      
      if (currentPlanData) {
        currentPlanName = currentPlanData.name
      }
    }

    // Extrair plano solicitado do campo selected_plan (JSONB)
    if (leadData.selected_plan && leadData.selected_plan.name) {
      requestedPlanName = leadData.selected_plan.name
    }

    // Preparar dados para o email
    const companyName = leadData.companies?.name || 'Empresa sem nome'
    const userName = leadData.profiles?.full_name || leadData.contact_info?.name || 'Usuário sem nome'
    const userEmail = leadData.profiles?.email || leadData.contact_info?.email
    const currentPlan = currentPlanName
    const requestedPlan = requestedPlanName
    const upgradeContext = leadData.upgrade_context || {}
    const contactPreferences = leadData.contact_preferences || {}
    const leadSource = leadData.source_feature || leadData.lead_source || 'Sistema'

    console.log('Email data extracted:', {
      companyName,
      userName, 
      userEmail,
      currentPlan,
      requestedPlan,
      leadSource,
      selectedPlanRaw: leadData.selected_plan,
      currentPlanId: leadData.current_plan_id,
      hasUpgradeContext: !!upgradeContext,
      hasContactPreferences: !!contactPreferences
    })

    // Verificar se temos email válido do usuário
    if (!userEmail) {
      console.error('User email not found in lead data:', leadData.profiles)
      return new Response(
        JSON.stringify({ 
          error: 'User email not found in lead data',
          details: 'Cannot send confirmation email without user email'
        }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // URL do sistema comercial
    const commercialUrl = `https://cosmos.vindula.com.br/admin/commercial-leads`
    const logoUrl = 'https://cosmos.vindula.com.br/vindula-cosmos.png'
    
    const subject = `🚀 Novo Lead Comercial - ${companyName}`
    const htmlContent = `
      <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
      <html xmlns="http://www.w3.org/1999/xhtml">
      <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Novo Lead Comercial - Vindula Cosmos</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Arial, Helvetica, sans-serif; background-color: #000000; color: #f8fafc;">
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
          <tr>
            <td>
              <table align="center" border="0" cellpadding="0" cellspacing="0" width="600" style="border-collapse: collapse; background-color: #0a0f1a; border-radius: 8px; margin-top: 40px; margin-bottom: 40px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);">
                <!-- HEADER -->
                <tr>
                  <td align="center" bgcolor="#050914" style="padding: 30px 20px; background-image: linear-gradient(135deg, #0a0f1a, #050914); border-top-left-radius: 8px; border-top-right-radius: 8px;">
                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                      <tr>
                        <td align="center" style="padding-bottom: 20px;">
                          <img src="${logoUrl}" alt="Vindula Cosmos Logo" width="120" style="display: block; border: 0;" />
                        </td>
                      </tr>
                      <tr>
                        <td align="center">
                          <h1 style="color: #ffffff; font-size: 28px; font-weight: 600; margin: 0; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">🚀 Novo Lead Comercial</h1>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                
                <!-- CONTEÚDO -->
                <tr>
                  <td bgcolor="#0a0f1a" style="padding: 35px;">
                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                      <!-- Botão de ação -->
                      <tr>
                        <td align="center" style="padding: 0 0 35px 0;">
                          <table border="0" cellpadding="0" cellspacing="0">
                            <tr>
                              <td align="center" bgcolor="#059669" style="border-radius: 8px;">
                                <a href="${commercialUrl}" target="_blank" style="display: inline-block; padding: 14px 32px; font-size: 16px; color: #ffffff; text-decoration: none; font-weight: 600; letter-spacing: 0.5px; border-radius: 8px; background-color: #059669;">🔗 Acessar Lead no Sistema</a>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      
                      <!-- Informações da Empresa -->
                      <tr>
                        <td style="padding-bottom: 25px;">
                          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #111827; border-radius: 8px; border-left: 4px solid #3b82f6;">
                            <tr>
                              <td style="padding: 18px;">
                                <h3 style="color: #ffffff; font-size: 18px; margin: 0 0 12px 0;">📋 Informações da Empresa</h3>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;"><strong>Empresa:</strong> ${companyName}</p>
                                <p style="color: #cbd5e1; margin: 0;"><strong>Lead ID:</strong> ${lead_id}</p>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      
                      <!-- Dados do Solicitante -->
                      <tr>
                        <td style="padding-bottom: 25px;">
                          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #111827; border-radius: 8px; border-left: 4px solid #10b981;">
                            <tr>
                              <td style="padding: 18px;">
                                <h3 style="color: #ffffff; font-size: 18px; margin: 0 0 12px 0;">👤 Dados do Solicitante</h3>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;"><strong>Nome:</strong> ${userName}</p>
                                <p style="color: #cbd5e1; margin: 0;"><strong>Email:</strong> ${userEmail}</p>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      
                      <!-- Detalhes do Upgrade -->
                      <tr>
                        <td style="padding-bottom: 25px;">
                          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #111827; border-radius: 8px; border-left: 4px solid #f59e0b;">
                            <tr>
                              <td style="padding: 18px;">
                                <h3 style="color: #ffffff; font-size: 18px; margin: 0 0 12px 0;">📊 Detalhes do Upgrade</h3>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;"><strong>Transição:</strong> ${currentPlan} → ${requestedPlan}</p>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;"><strong>Origem:</strong> ${leadSource}</p>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;"><strong>Tipo:</strong> ${upgradeContext.flow_type || upgradeContext.upgrade_type || 'Upgrade Padrão'}</p>
                                ${upgradeContext.total_monthly_value ? `<p style="color: #10b981; margin: 0; font-size: 16px; font-weight: 600;"><strong>Valor Mensal:</strong> R$ ${upgradeContext.total_monthly_value}</p>` : ''}
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      
                      <!-- Preferências de Contato -->
                      <tr>
                        <td style="padding-bottom: 25px;">
                          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #111827; border-radius: 8px; border-left: 4px solid #8b5cf6;">
                            <tr>
                              <td style="padding: 18px;">
                                <h3 style="color: #ffffff; font-size: 18px; margin: 0 0 12px 0;">📞 Preferências de Contato</h3>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;"><strong>Método Preferido:</strong> ${contactPreferences.preferredContactMethod || 'Email'}</p>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;"><strong>Urgência:</strong> ${contactPreferences.urgency || 'Normal'}</p>
                                ${contactPreferences.phone ? `<p style="color: #cbd5e1; margin: 0;"><strong>Telefone:</strong> ${contactPreferences.phone}</p>` : ''}
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      
                      <!-- Link direto -->
                      <tr>
                        <td>
                          <p style="color: #94a3b8; font-size: 14px; margin: 0; font-style: italic;">Acesse: <a href="${commercialUrl}" style="color: #3b82f6;">Sistema de Leads Comerciais</a></p>
                          <p style="color: #64748b; font-size: 12px; margin: 5px 0 0 0;">Procure pelo Lead ID: <strong>${lead_id}</strong></p>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                
                <!-- FOOTER -->
                <tr>
                  <td bgcolor="#050914" style="padding: 25px; text-align: center; border-top: 1px solid rgba(255, 255, 255, 0.1); border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;">
                    <p style="color: #94a3b8; font-size: 13px; margin: 5px 0;">Este lead foi gerado automaticamente pelo sistema Vindula Cosmos.</p>
                    <p style="color: #94a3b8; font-size: 13px; margin: 5px 0;">&copy; 2025 Vindula - Todos os direitos reservados.</p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
      </html>
    `

    console.log('Sending email with subject:', subject)

    // Verificar API key do MailerSend
    const mailersendApiKey = Deno.env.get('MAILERSEND_API_KEY')
    if (!mailersendApiKey) {
      throw new Error('MAILERSEND_API_KEY environment variable not set')
    }

    // Enviar email via MailerSend
    const mailersendResponse = await fetch('https://api.mailersend.com/v1/email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${mailersendApiKey}`,
      },
      body: JSON.stringify({
        "from": {
          "email": "<EMAIL>",
          "name": "Vindula Cosmos"
        },
        "to": [
          {
            "email": "<EMAIL>",
            "name": "Comercial Vindula"
          }
        ],
        "subject": subject,
        "html": htmlContent
      }),
    })

    console.log('MailerSend response status:', mailersendResponse.status)
    const responseText = await mailersendResponse.text()
    console.log('MailerSend response body:', responseText)

    if (!mailersendResponse.ok) {
      throw new Error(`MailerSend API error comercial: ${responseText}`)
    }

    console.log('Commercial email sent successfully')

    // 🎯 NOVO: Enviar email de confirmação para o usuário
    const userSubject = `✅ Solicitação de Upgrade Recebida - ${requestedPlan}`
    const userHtmlContent = `
      <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
      <html xmlns="http://www.w3.org/1999/xhtml">
      <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Solicitação Recebida - Vindula Cosmos</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Arial, Helvetica, sans-serif; background-color: #000000; color: #f8fafc;">
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
          <tr>
            <td>
              <table align="center" border="0" cellpadding="0" cellspacing="0" width="600" style="border-collapse: collapse; background-color: #0a0f1a; border-radius: 8px; margin-top: 40px; margin-bottom: 40px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);">
                <!-- HEADER -->
                <tr>
                  <td align="center" bgcolor="#050914" style="padding: 30px 20px; background-image: linear-gradient(135deg, #0a0f1a, #050914); border-top-left-radius: 8px; border-top-right-radius: 8px;">
                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                      <tr>
                        <td align="center" style="padding-bottom: 20px;">
                          <img src="${logoUrl}" alt="Vindula Cosmos Logo" width="120" style="display: block; border: 0;" />
                        </td>
                      </tr>
                      <tr>
                        <td align="center">
                          <h1 style="color: #ffffff; font-size: 28px; font-weight: 600; margin: 0; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">✅ Solicitação Recebida!</h1>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                
                <!-- CONTEÚDO -->
                <tr>
                  <td bgcolor="#0a0f1a" style="padding: 35px;">
                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                      <!-- Saudação -->
                      <tr>
                        <td style="padding-bottom: 25px;">
                          <p style="color: #f8fafc; font-size: 20px; font-weight: 600; margin: 0;">Olá, ${userName}!</p>
                          <p style="color: #cbd5e1; margin: 15px 0 0 0;">Recebemos sua solicitação de upgrade e nossa equipe comercial já foi notificada!</p>
                        </td>
                      </tr>
                      
                      <!-- Resumo da Solicitação -->
                      <tr>
                        <td style="padding-bottom: 25px;">
                          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #111827; border-radius: 8px; border-left: 4px solid #10b981;">
                            <tr>
                              <td style="padding: 18px;">
                                <h3 style="color: #ffffff; font-size: 18px; margin: 0 0 12px 0;">📋 Resumo da Solicitação</h3>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;"><strong>Empresa:</strong> ${companyName}</p>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;"><strong>Transição:</strong> ${currentPlan} → ${requestedPlan}</p>
                                ${upgradeContext.total_monthly_value ? `<p style="color: #10b981; margin: 0 0 8px 0; font-weight: 600;"><strong>Valor Mensal:</strong> R$ ${upgradeContext.total_monthly_value}</p>` : ''}
                                <p style="color: #94a3b8; margin: 0; font-size: 14px;"><strong>ID da Solicitação:</strong> ${lead_id}</p>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      
                      <!-- Próximos Passos -->
                      <tr>
                        <td style="padding-bottom: 25px;">
                          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #111827; border-radius: 8px; border-left: 4px solid #3b82f6;">
                            <tr>
                              <td style="padding: 18px;">
                                <h3 style="color: #ffffff; font-size: 18px; margin: 0 0 12px 0;">⏰ Próximos Passos</h3>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;">• Nossa equipe comercial entrará em contato em até <strong style="color: #f8fafc;">24 horas</strong></p>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;">• Você receberá acesso imediato ao plano solicitado por <strong style="color: #f8fafc;">7 dias de cortesia</strong></p>
                                <p style="color: #cbd5e1; margin: 0;">• Durante esse período, podemos formalizar os detalhes comerciais</p>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      
                      <!-- Preferências de Contato -->
                      <tr>
                        <td style="padding-bottom: 25px;">
                          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #111827; border-radius: 8px; border-left: 4px solid #8b5cf6;">
                            <tr>
                              <td style="padding: 18px;">
                                <h3 style="color: #ffffff; font-size: 18px; margin: 0 0 12px 0;">💬 Suas Preferências de Contato</h3>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;"><strong>Método Preferido:</strong> ${contactPreferences.preferredContactMethod || 'Email'}</p>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;"><strong>Urgência:</strong> ${contactPreferences.urgency || 'Normal'}</p>
                                ${contactPreferences.phone ? `<p style="color: #cbd5e1; margin: 0;"><strong>Telefone:</strong> ${contactPreferences.phone}</p>` : ''}
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      
                      <!-- Suporte -->
                      <tr>
                        <td style="padding-bottom: 25px;">
                          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #111827; border-radius: 8px; border-left: 4px solid #f59e0b;">
                            <tr>
                              <td style="padding: 18px;">
                                <h3 style="color: #ffffff; font-size: 18px; margin: 0 0 12px 0;">📞 Precisa de Ajuda?</h3>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;">Se você tiver dúvidas ou precisar de ajuda imediata:</p>
                                <p style="color: #cbd5e1; margin: 0 0 8px 0;"><strong>Email:</strong> <EMAIL></p>
                                <p style="color: #cbd5e1; margin: 0;"><strong>WhatsApp:</strong> (11) 99999-9999</p>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      
                      <!-- Agradecimento -->
                      <tr>
                        <td align="center">
                          <p style="color: #10b981; font-size: 18px; font-weight: 600; margin: 0;">Obrigado por escolher a Vindula! 🚀</p>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                
                <!-- FOOTER -->
                <tr>
                  <td bgcolor="#050914" style="padding: 25px; text-align: center; border-top: 1px solid rgba(255, 255, 255, 0.1); border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;">
                    <p style="color: #94a3b8; font-size: 13px; margin: 5px 0;">Esta é uma confirmação automática. Nossa equipe comercial entrará em contato em breve.</p>
                    <p style="color: #94a3b8; font-size: 13px; margin: 5px 0;">&copy; 2025 Vindula - Todos os direitos reservados.</p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
      </html>
    `

    console.log('Sending confirmation email to user:', userEmail)

    // Enviar email de confirmação para o usuário
    const userMailersendResponse = await fetch('https://api.mailersend.com/v1/email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${mailersendApiKey}`,
      },
      body: JSON.stringify({
        "from": {
          "email": "<EMAIL>",
          "name": "Vindula Cosmos"
        },
        "to": [
          {
            "email": userEmail,
            "name": userName
          }
        ],
        "subject": userSubject,
        "html": userHtmlContent
      }),
    })

    console.log('User MailerSend response status:', userMailersendResponse.status)
    const userResponseText = await userMailersendResponse.text()
    console.log('User MailerSend response body:', userResponseText)

    if (!userMailersendResponse.ok) {
      console.error(`MailerSend API error user: ${userResponseText}`)
      // Não falhar se email do usuário não foi enviado
    }

    return new Response(
      JSON.stringify({ 
        message: 'Emails sent successfully',
        lead_id: lead_id,
        commercial_email: {
          recipient: '<EMAIL>',
          subject: subject,
          status: 'sent'
        },
        user_email: {
          recipient: userEmail,
          subject: userSubject,
          status: userMailersendResponse.ok ? 'sent' : 'failed'
        }
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      },
    )

  } catch (error: any) {
    console.error("Error in send-commercial-notification function:", error)
    return new Response(
      JSON.stringify({ 
        error: error.message,
        details: error.toString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})