/**
 * Edge Function para gerar espaços de conhecimento usando IA
 * Cria espaços personalizados baseados no perfil da empresa
 * <AUTHOR> Internet 2025
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import { OpenAI } from "https://deno.land/x/openai@v4.24.0/mod.ts"
import { corsHeaders } from '../_shared/cors.ts'

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

interface GenerateSpacesRequest {
  companyType: string
  industry: string
  teamSize: number
  knowledgeAreas: string[]
  customRequirements?: string
  spacesCount?: number
}

interface GeneratedSpace {
  name: string
  key: string
  description: string
  icon: string
  color: string
  is_private: boolean
}

// Mapeamento de ícones disponíveis
const availableIcons = [
  'BookOpen', 'Code', 'Megaphone', 'Users', 'Package', 'Settings', 
  'FileText', 'Globe', 'Calendar', 'Star', 'Heart', 'Activity', 
  'Target', 'Brain', 'Lightbulb', 'Rocket', 'Shield', 'TrendingUp',
  'Award', 'Bookmark', 'Database', 'Monitor', 'Clipboard', 'Map'
]

// Cores disponíveis para os espaços
const availableColors = [
  '#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444', 
  '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
]

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    // Verificar se a API key do Groq está configurada
    if (!Deno.env.get('GROQ_API_KEY')) {
      throw new Error('GROQ_API_KEY não está configurada')
    }

    // Configurar Groq
    const openai = new OpenAI({
      baseURL: "https://api.groq.com/openai/v1",
      apiKey: Deno.env.get('GROQ_API_KEY')
    })

    // Parse request body
    const body = await req.json() as GenerateSpacesRequest
    const { companyType, industry, teamSize, knowledgeAreas, customRequirements, spacesCount = 5 } = body

    // Validar entrada
    if (!companyType || !industry || !teamSize || !knowledgeAreas?.length) {
      return new Response(
        JSON.stringify({ 
          error: 'Campos obrigatórios: companyType, industry, teamSize, knowledgeAreas',
          received: { companyType, industry, teamSize, knowledgeAreas: knowledgeAreas?.length }
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    if (spacesCount > 15 || spacesCount < 1) {
      return new Response(
        JSON.stringify({ 
          error: 'Número de espaços deve ser entre 1 e 15',
          received: spacesCount
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Obter informações da empresa do usuário autenticado
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Token de autorização necessário' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Buscar company_id do usuário
    const { data: userData, error: userError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''))
    if (userError || !userData.user) {
      return new Response(
        JSON.stringify({ error: 'Usuário não autenticado' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('company_id')
      .eq('id', userData.user.id)
      .single()

    if (profileError || !profile) {
      return new Response(
        JSON.stringify({ error: 'Perfil do usuário não encontrado' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verificar se já existem espaços para não duplicar conceitos
    const { data: existingSpaces, error: spacesError } = await supabase
      .from('knowledge_spaces')
      .select('name, key, description')
      .eq('company_id', profile.company_id)

    if (spacesError) {
      console.warn('Erro ao buscar espaços existentes:', spacesError.message)
    }

    const existingSpaceNames = existingSpaces?.map(space => space.name) || []
    const existingSpaceKeys = existingSpaces?.map(space => space.key) || []

    console.log(`Gerando ${spacesCount} espaços para empresa tipo: ${companyType}, setor: ${industry}`)

    // Mapear áreas de conhecimento para descrições mais claras
    const knowledgeAreaDescriptions = {
      'technical': 'Documentação Técnica',
      'policies': 'Políticas e Procedimentos',
      'training': 'Treinamentos e Capacitação',
      'communication': 'Comunicação Interna',
      'products': 'Produtos e Serviços',
      'projects': 'Gestão de Projetos',
      'innovation': 'Inovação e Pesquisa',
      'culture': 'Cultura Organizacional',
      'processes': 'Processos e Qualidade',
      'strategy': 'Estratégia e Planejamento',
      'collaboration': 'Colaboração e Equipes',
      'external': 'Conhecimento Externo'
    }

    const selectedAreas = knowledgeAreas.map(area => knowledgeAreaDescriptions[area] || area).join(', ')

    // System message otimizado para geração de espaços de conhecimento
    const systemMessage = `Você é um especialista em gestão do conhecimento e arquitetura da informação corporativa.

Sua missão é criar espaços de conhecimento estratégicos que:
- Sejam ÚNICOS e não duplicados com os espaços existentes
- Tenham nomes CLAROS e ESPECÍFICOS para o contexto da empresa
- Tenham chaves (keys) válidas: apenas letras minúsculas, números e hífens
- Sejam organizados de forma lógica e hierárquica
- Facilitem a colaboração e o compartilhamento de conhecimento
- Estejam alinhados com as necessidades do negócio brasileiro

REGRAS OBRIGATÓRIAS:
1. Gere EXATAMENTE ${spacesCount} espaços únicos
2. Cada espaço deve ter nome atrativo e específico
3. Keys devem ser únicas: formato kebab-case (ex: "gestao-projetos")
4. Descrições devem ser práticas e claras (50-150 caracteres)
5. icon: escolha um dos disponíveis: ${availableIcons.join(', ')}
6. color: use cores hexadecimais dos disponíveis: ${availableColors.join(', ')}
7. is_private: false para espaços colaborativos, true para áreas sensíveis
8. NÃO DUPLIQUE conceitos dos espaços existentes

FOQUE EM:
- Organização lógica por departamento/função
- Espaços que promovam colaboração
- Áreas específicas do setor da empresa
- Estrutura que facilite busca e navegação
- Equilíbrio entre espaços públicos e privados

Responda APENAS com um JSON válido no formato:
{
  "spaces": [
    {
      "name": "Nome do Espaço",
      "key": "chave-unica-kebab-case",
      "description": "Descrição clara e específica do propósito do espaço",
      "icon": "BookOpen",
      "color": "#3B82F6",
      "is_private": false
    }
  ]
}`

    // User message personalizado com contexto da empresa
    const userMessage = `
Crie ${spacesCount} espaços de conhecimento para esta empresa:

CONTEXTO DA EMPRESA:
- Tipo: ${companyType}
- Setor: ${industry}
- Tamanho da equipe: ${teamSize} pessoas
- Áreas de conhecimento priorizadas: ${selectedAreas}
- Requisitos específicos: ${customRequirements || 'Nenhum requisito específico'}

ESPAÇOS EXISTENTES (NÃO DUPLICAR):
${existingSpaceNames.length > 0 ? existingSpaceNames.join(', ') : 'Nenhum espaço existente'}

CHAVES JÁ UTILIZADAS (NÃO REPETIR):
${existingSpaceKeys.length > 0 ? existingSpaceKeys.join(', ') : 'Nenhuma chave utilizada'}

IMPORTANTE: 
- Criar espaços únicos que complementem os existentes
- Focar nas áreas de conhecimento selecionadas
- Adequar ao tamanho e perfil da empresa
- Considerar o setor de atuação para criar espaços relevantes
- Criar uma estrutura organizacional lógica
- Balancear espaços públicos (colaborativos) e privados (sensíveis)
`

    console.log('Gerando espaços com Groq (llama-3.1-8b-instant)...')
    
    // Gerar espaços com Groq
    const completion = await openai.chat.completions.create({
      model: "llama-3.1-8b-instant",
      messages: [
        { role: "system", content: systemMessage },
        { role: "user", content: userMessage }
      ],
      temperature: 0.7,
      max_tokens: 2000,
      response_format: { type: "json_object" }
    })

    const responseContent = completion.choices[0]?.message?.content
    if (!responseContent) {
      throw new Error('Resposta vazia da Groq')
    }

    console.log('Resposta da Groq:', responseContent)

    // Parse da resposta
    let generatedData: { spaces: GeneratedSpace[] }
    try {
      generatedData = JSON.parse(responseContent)
    } catch (parseError) {
      console.error('Erro ao fazer parse da resposta:', parseError)
      throw new Error('Formato de resposta inválido da IA')
    }

    if (!generatedData.spaces || !Array.isArray(generatedData.spaces)) {
      throw new Error('Formato de espaços inválido na resposta da IA')
    }

    // Validar e ajustar os dados gerados
    const spaces = generatedData.spaces
      .slice(0, spacesCount) // Garantir o número correto
      .map((space, index) => {
        // Garantir que a key seja válida (kebab-case)
        const validKey = (space.key || `espaco-${index + 1}`)
          .toLowerCase()
          .replace(/[^a-z0-9\-]/g, '-')
          .replace(/--+/g, '-')
          .replace(/^-|-$/g, '')

        // Verificar se a key já existe e adicionar sufixo se necessário
        let finalKey = validKey
        let suffix = 1
        while (existingSpaceKeys.includes(finalKey)) {
          finalKey = `${validKey}-${suffix}`
          suffix++
        }

        return {
          company_id: profile.company_id,
          name: space.name || `Espaço ${index + 1}`,
          key: finalKey,
          description: space.description || 'Espaço gerado automaticamente',
          icon: availableIcons.includes(space.icon) ? space.icon : 'BookOpen',
          color: availableColors.includes(space.color) ? space.color : availableColors[index % availableColors.length],
          is_private: Boolean(space.is_private),
          created_by: userData.user.id
        }
      })

    console.log(`Processados ${spaces.length} espaços para inserção`)

    if (spaces.length === 0) {
      throw new Error('Nenhum espaço válido foi gerado')
    }

    // Inserir espaços no banco
    const { data: insertedSpaces, error: insertError } = await supabase
      .from('knowledge_spaces')
      .insert(spaces)
      .select('id, name, key, description, icon, color, is_private')

    if (insertError) {
      console.error('Erro ao inserir espaços:', insertError)
      throw new Error(`Erro ao salvar espaços: ${insertError.message}`)
    }

    console.log('Espaços inseridos com sucesso:', insertedSpaces?.length)

    // Resposta de sucesso
    return new Response(
      JSON.stringify({
        success: true,
        message: `${spaces.length} espaços de conhecimento criados com sucesso!`,
        spaces_created: spaces.length,
        total_spaces: (existingSpaces?.length || 0) + spaces.length,
        created_spaces: insertedSpaces?.map(space => ({
          id: space.id,
          name: space.name,
          key: space.key,
          description: space.description,
          icon: space.icon,
          color: space.color,
          is_private: space.is_private
        })) || [],
        ai_model: 'llama-3.1-8b-instant',
        generated_at: new Date().toISOString()
      }),
      { 
        status: 200,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json',
          'Cache-Control': 'no-store'
        }
      }
    )

  } catch (error: unknown) {
    console.error('Erro na função:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    const errorDetails = error instanceof Error ? error.toString() : String(error)
    
    return new Response(
      JSON.stringify({ 
        error: errorMessage,
        details: errorDetails,
        timestamp: new Date().toISOString()
      }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json',
          'Cache-Control': 'no-store'
        }
      }
    )
  }
}) 