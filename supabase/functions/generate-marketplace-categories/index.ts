/**
 * Edge Function para gerar categorias estratégicas do marketplace usando IA
 * Gera categorias personalizadas baseadas no tipo de empresa e setor
 * <AUTHOR> Internet 2025
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import { OpenAI } from "https://deno.land/x/openai@v4.24.0/mod.ts"
import { corsHeaders } from '../_shared/cors.ts'

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

interface GenerateCategoriesRequest {
  companyType: string
  sector: string
  employeeCount?: number
  customRequirements?: string
  quantity: number // Número de categorias a gerar (máximo 8)
}

interface GeneratedCategory {
  name: string
  description: string
  icon: string
  gradient: string
  bg_gradient: string
  value_proposition: string
  order_position: number
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    // Verificar se a API key do Groq está configurada
    if (!Deno.env.get('GROQ_API_KEY')) {
      throw new Error('GROQ_API_KEY não está configurada')
    }

    // Configurar Groq
    const openai = new OpenAI({
      baseURL: "https://api.groq.com/openai/v1",
      apiKey: Deno.env.get('GROQ_API_KEY')
    })

    // Parse request body
    const body = await req.json() as GenerateCategoriesRequest
    const { companyType, sector, employeeCount, customRequirements, quantity } = body

    // Validar entrada
    if (!companyType || !sector || !quantity) {
      return new Response(
        JSON.stringify({ 
          error: 'Campos obrigatórios: companyType, sector, quantity',
          received: { companyType, sector, quantity }
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    if (quantity > 8 || quantity < 1) {
      return new Response(
        JSON.stringify({ 
          error: 'Quantidade deve ser entre 1 e 8 categorias',
          received: quantity
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Obter informações da empresa do usuário autenticado
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Token de autorização necessário' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Buscar company_id do usuário
    const { data: userData, error: userError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''))
    if (userError || !userData.user) {
      return new Response(
        JSON.stringify({ error: 'Usuário não autenticado' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('company_id')
      .eq('id', userData.user.id)
      .single()

    if (profileError || !profile) {
      return new Response(
        JSON.stringify({ error: 'Perfil do usuário não encontrado' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // System message otimizado para marketplace estratégico brasileiro
    const systemMessage = `Você é um especialista em benefícios corporativos e marketplace estratégico no Brasil.

Sua missão é criar categorias estratégicas de benefícios para marketplace corporativo que:
- Aumentem o engajamento dos colaboradores
- Ofereçam alto ROI em satisfação e retenção
- Sejam adequadas para a cultura corporativa brasileira
- Gerem valor percebido pelos funcionários

REGRAS OBRIGATÓRIAS:
1. Gere EXATAMENTE ${quantity} categorias
2. Cada categoria deve ter um nome único e atrativo
3. Descrições devem ser profissionais mas envolventes
4. Ícones devem ser escolhidos da lista: Gift, Crown, Coffee, Plane, BookOpen, Laptop, Car, Home, Heart, Trophy, Star, Zap, Sparkles
5. Gradientes devem ser escolhidos da lista: from-blue-500 to-purple-600, from-purple-500 to-pink-600, from-green-500 to-emerald-600, from-orange-500 to-red-600, from-yellow-400 to-orange-500, from-cyan-400 to-blue-500, from-pink-400 to-rose-500, from-indigo-500 to-purple-500
6. bg_gradient deve seguir o padrão: from-[cor]-500/20 via-[cor2]-500/10 to-[cor3]-500/20
7. value_proposition deve ser uma frase curta e impactante sobre o ROI
8. order_position deve ser sequencial começando em 0

FOQUE EM:
- Bem-estar e qualidade de vida
- Desenvolvimento profissional e pessoal
- Benefícios tangíveis e experiências
- Flexibilidade e work-life balance
- Reconhecimento e recompensas
- Conveniências do dia a dia

Responda APENAS com um JSON válido no formato:
{
  "categories": [
    {
      "name": "Nome da Categoria",
      "description": "Descrição atrativa da categoria",
      "icon": "Gift",
      "gradient": "from-blue-500 to-purple-600",
      "bg_gradient": "from-blue-500/20 via-purple-500/10 to-indigo-500/20",
      "value_proposition": "Alto ROI em satisfação",
      "order_position": 0
    }
  ]
}`

    // User message personalizado
    const userMessage = `
Gere ${quantity} categorias estratégicas para marketplace corporativo:

CONTEXTO DA EMPRESA:
- Tipo: ${companyType}
- Setor: ${sector}
- Número de funcionários: ${employeeCount || 'Não informado'}
- Requisitos específicos: ${customRequirements || 'Nenhum requisito específico'}

Foque em categorias que façam sentido para este perfil de empresa e que gerem alto engajamento dos colaboradores brasileiros.
`

    console.log('Gerando categorias com Groq (llama-3.1-8b-instant)...')
    
    // Gerar categorias com Groq
    const completion = await openai.chat.completions.create({
      model: "llama-3.1-8b-instant",
      messages: [
        { role: "system", content: systemMessage },
        { role: "user", content: userMessage }
      ],
      temperature: 0.7,
      max_tokens: 2000,
      response_format: { type: "json_object" }
    })

    const responseContent = completion.choices[0]?.message?.content
    if (!responseContent) {
      throw new Error('Resposta vazia da Groq')
    }

    console.log('Resposta da Groq:', responseContent)

    // Parse da resposta
    let generatedData: { categories: GeneratedCategory[] }
    try {
      generatedData = JSON.parse(responseContent)
    } catch (parseError) {
      console.error('Erro ao fazer parse da resposta:', parseError)
      throw new Error('Formato de resposta inválido da IA')
    }

    if (!generatedData.categories || !Array.isArray(generatedData.categories)) {
      throw new Error('Formato de categorias inválido na resposta da IA')
    }

    // Validar e ajustar os dados gerados
    const categories = generatedData.categories.slice(0, quantity).map((cat, index) => ({
      name: cat.name || `Categoria ${index + 1}`,
      description: cat.description || 'Descrição gerada automaticamente',
      icon: cat.icon || 'Gift',
      gradient: cat.gradient || 'from-blue-500 to-purple-600',
      bg_gradient: cat.bg_gradient || 'from-blue-500/20 via-purple-500/10 to-indigo-500/20',
      value_proposition: cat.value_proposition || 'Alto valor para colaboradores',
      order_position: index,
      company_id: profile.company_id,
      active: true
    }))

    console.log('Categorias processadas:', categories)

    // Inserir categorias no banco
    const { data: insertedCategories, error: insertError } = await supabase
      .from('strategic_categories')
      .insert(categories)
      .select('*')

    if (insertError) {
      console.error('Erro ao inserir categorias:', insertError)
      throw new Error(`Erro ao salvar categorias: ${insertError.message}`)
    }

    console.log('Categorias inseridas com sucesso:', insertedCategories)

    // Resposta de sucesso
    return new Response(
      JSON.stringify({
        success: true,
        message: `${categories.length} categorias criadas com sucesso!`,
        categories: insertedCategories,
        ai_model: 'llama-3.1-8b-instant',
        generated_at: new Date().toISOString()
      }),
      { 
        status: 200,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json',
          'Cache-Control': 'no-store'
        }
      }
    )

  } catch (error: unknown) {
    console.error('Erro na função:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    const errorDetails = error instanceof Error ? error.toString() : String(error)
    
    return new Response(
      JSON.stringify({ 
        error: errorMessage,
        details: errorDetails,
        timestamp: new Date().toISOString()
      }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json',
          'Cache-Control': 'no-store'
        }
      }
    )
  }
})