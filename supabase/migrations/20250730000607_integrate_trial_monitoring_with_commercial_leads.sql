-- ==================================================
-- INTEGRAR TRIAL MONITORING COM COMMERCIAL LEADS
-- Modificar functions para usar commercial_leads como fonte única de dados
-- <AUTHOR> Internet 2025
-- ==================================================

-- Criar função get_trial_statistics_v2 para usar commercial_leads
CREATE OR REPLACE FUNCTION public.get_trial_statistics_v2()
RETURNS TABLE(
    active_trials INTEGER,
    expiring_24h INTEGER,
    expiring_48h INTEGER,
    expired_pending INTEGER,
    total_trial_companies INTEGER,
    average_trial_duration_days NUMERIC,
    conversion_rate_last_30_days NUMERIC
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Verificar se é usuário Vindula
    IF NOT public.is_vindula_company() THEN
        RAISE EXCEPTION 'Access denied: Vindula company only';
    END IF;

    RETURN QUERY
    WITH trial_stats AS (
        SELECT 
            cl.id,
            cl.company_id,
            cl.courtesy_period_start,
            cl.courtesy_period_end,
            cl.created_at,
            cl.status,
            c.name as company_name,
            CASE 
                WHEN cl.courtesy_period_end IS NOT NULL AND cl.courtesy_period_end > now() 
                THEN 'active_trial'
                WHEN cl.courtesy_period_end IS NOT NULL AND cl.courtesy_period_end <= now()
                THEN 'expired_trial'
                ELSE 'no_trial'
            END as trial_status,
            EXTRACT(day FROM (cl.courtesy_period_end - cl.courtesy_period_start)) as trial_duration
        FROM commercial_leads cl
        JOIN companies c ON cl.company_id = c.id
        WHERE cl.courtesy_period_end IS NOT NULL
        AND cl.courtesy_granted = true
    ),
    conversion_stats AS (
        -- Calcular conversões baseado em billing history
        SELECT 
            COUNT(DISTINCT CASE 
                WHEN sbh.created_at >= (now() - interval '30 days')
                AND sbh.total_amount > 0
                AND sbh.billing_status = 'completed'
                THEN sbh.company_id
            END) as conversions_last_30_days,
            COUNT(DISTINCT CASE 
                WHEN cl.created_at >= (now() - interval '30 days')
                AND cl.courtesy_period_end IS NOT NULL
                THEN cl.company_id
            END) as trials_last_30_days
        FROM commercial_leads cl
        LEFT JOIN subscription_billing_history sbh ON cl.company_id = sbh.company_id
        WHERE cl.courtesy_period_end IS NOT NULL
    ),
    aggregated_stats AS (
        -- Agregar estatísticas de trials
        SELECT 
            COUNT(*) FILTER (WHERE trial_status = 'active_trial') as active_count,
            COUNT(*) FILTER (
                WHERE trial_status = 'active_trial' 
                AND courtesy_period_end <= (now() + interval '24 hours')
            ) as expiring_24h_count,
            COUNT(*) FILTER (
                WHERE trial_status = 'active_trial' 
                AND courtesy_period_end <= (now() + interval '48 hours')
            ) as expiring_48h_count,
            COUNT(*) FILTER (WHERE trial_status = 'expired_trial' AND status != 'converted') as expired_pending_count,
            COUNT(DISTINCT company_id) as total_companies_count,
            AVG(trial_duration) as avg_duration
        FROM trial_stats
    )
    SELECT 
        ass.active_count::INTEGER,
        ass.expiring_24h_count::INTEGER,
        ass.expiring_48h_count::INTEGER,
        ass.expired_pending_count::INTEGER,
        ass.total_companies_count::INTEGER,
        ass.avg_duration::NUMERIC,
        CASE 
            WHEN cs.trials_last_30_days > 0 
            THEN ROUND((cs.conversions_last_30_days::NUMERIC / cs.trials_last_30_days::NUMERIC) * 100, 2)
            ELSE 0
        END::NUMERIC
    FROM aggregated_stats ass
    CROSS JOIN conversion_stats cs;
END;
$$;

-- Criar função get_expiring_trials_v2 para usar commercial_leads
CREATE OR REPLACE FUNCTION public.get_expiring_trials_v2(p_days_ahead INTEGER DEFAULT 2)
RETURNS TABLE(
    company_id UUID,
    company_name TEXT,
    owner_name TEXT,
    owner_email TEXT,
    subscription_id UUID,
    current_plan_name TEXT,
    courtesy_until timestamptz,
    days_remaining INTEGER,
    trial_duration_days INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Verificar se é usuário Vindula
    IF NOT public.is_vindula_company() THEN
        RAISE EXCEPTION 'Access denied: Vindula company only';
    END IF;

    RETURN QUERY
    SELECT 
        cl.company_id,
        c.name as company_name,
        (cl.contact_info->>'name')::TEXT as owner_name,
        (cl.contact_info->>'email')::TEXT as owner_email,
        cl.subscription_id,
        (cl.selected_plan->>'name')::TEXT as current_plan_name,
        cl.courtesy_period_end as courtesy_until,
        CEIL(EXTRACT(epoch FROM (cl.courtesy_period_end - now())) / 86400)::INTEGER as days_remaining,
        CEIL(EXTRACT(epoch FROM (cl.courtesy_period_end - cl.courtesy_period_start)) / 86400)::INTEGER as trial_duration_days
    FROM commercial_leads cl
    JOIN companies c ON cl.company_id = c.id
    WHERE cl.courtesy_period_end IS NOT NULL
    AND cl.courtesy_granted = true
    AND cl.courtesy_period_end > now()
    AND cl.courtesy_period_end <= (now() + interval '1 day' * p_days_ahead)
    ORDER BY cl.courtesy_period_end ASC;
END;
$$;

-- Criar função get_trial_dashboard_v2 para usar commercial_leads
CREATE OR REPLACE FUNCTION public.get_trial_dashboard_v2()
RETURNS TABLE(dashboard_data JSONB)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    stats_data JSONB;
    alerts_data JSONB;
    recent_activity JSONB;
BEGIN
    -- Verificar se é usuário Vindula
    IF NOT public.is_vindula_company() THEN
        RAISE EXCEPTION 'Access denied: Vindula company only';
    END IF;

    -- Buscar estatísticas de trials
    SELECT row_to_json(t) INTO stats_data
    FROM public.get_trial_statistics_v2() t;

    -- Buscar alertas recentes (últimas 24h) - relacionados a trials
    SELECT jsonb_agg(alert_data ORDER BY alert_data->>'created_at' DESC) INTO alerts_data
    FROM (
        SELECT jsonb_build_object(
            'company_name', c.name,
            'notification_type', 'trial_expiring',
            'status', 'pending',
            'created_at', cl.updated_at,
            'recipient_email', cl.contact_info->>'email'
        ) as alert_data
        FROM commercial_leads cl
        JOIN companies c ON cl.company_id = c.id
        WHERE cl.courtesy_period_end IS NOT NULL
        AND cl.courtesy_granted = true
        AND cl.courtesy_period_end <= (now() + interval '24 hours')
        AND cl.courtesy_period_end > now()
        ORDER BY cl.courtesy_period_end ASC
        LIMIT 50
    ) alerts;

    -- Buscar atividade recente de trials (últimos 7 dias)
    SELECT jsonb_agg(activity_data ORDER BY activity_data->>'created_at' DESC) INTO recent_activity
    FROM (
        SELECT jsonb_build_object(
            'company_name', c.name,
            'activation_type', 
            CASE 
                WHEN cl.status = 'converted' THEN 'trial_conversion'
                WHEN cl.courtesy_period_end < now() THEN 'trial_expiration'
                ELSE 'trial_activation'
            END,
            'created_at', cl.updated_at,
            'previous_plan', 'Free'
        ) as activity_data
        FROM commercial_leads cl
        JOIN companies c ON cl.company_id = c.id
        WHERE cl.courtesy_period_end IS NOT NULL
        AND cl.courtesy_granted = true
        AND cl.updated_at > (now() - interval '7 days')
        ORDER BY cl.updated_at DESC
        LIMIT 50
    ) activities;

    -- Montar dashboard
    dashboard_data := jsonb_build_object(
        'statistics', COALESCE(stats_data, '{}'::jsonb),
        'recent_alerts', COALESCE(alerts_data, '[]'::jsonb),
        'recent_activity', COALESCE(recent_activity, '[]'::jsonb),
        'last_updated', now(),
        'system_status', jsonb_build_object(
            'cron_enabled', true,
            'alerts_enabled', true,
            'auto_expiration_enabled', true
        )
    );

    RETURN NEXT;
    RETURN;
END;
$$;

-- Comentários das funções v2
COMMENT ON FUNCTION public.get_trial_statistics_v2() IS 
'V2: Integrada com commercial_leads - fonte única de dados de trial';

COMMENT ON FUNCTION public.get_expiring_trials_v2(INTEGER) IS 
'V2: Integrada com commercial_leads - mostra trials expirando baseado em courtesy_period_end';

COMMENT ON FUNCTION public.get_trial_dashboard_v2() IS 
'V2: Integrada com commercial_leads - dashboard unificado de trials comerciais';