/**
 * Melhora função de contagem de mensagens não lidas
 * - Exclui mensagens de sistema (join/leave) dos contadores
 * - Melhora performance com verificação prévia de membership
 * - Adiciona logs estruturados para debugging
 * <AUTHOR> Internet 2025
 */

-- Função melhorada que exclui mensagens de sistema
CREATE OR REPLACE FUNCTION public.get_unread_messages_count_v3(p_channel_id uuid)
RETURNS integer
LANGUAGE plpgsql 
SECURITY DEFINER
AS $$
DECLARE
  v_user_id uuid;
  v_company_id uuid;
  v_count integer;
BEGIN
  -- Get current user and company
  SELECT id, company_id INTO v_user_id, v_company_id
  FROM public.profiles 
  WHERE id = auth.uid();
  
  IF v_user_id IS NULL THEN
    RETURN 0;
  END IF;

  -- Verify user is channel member (early return for performance)
  IF NOT EXISTS (
    SELECT 1 FROM public.channel_members chm
    WHERE chm.channel_id = p_channel_id 
    AND chm.user_id = v_user_id
    AND chm.archived = false  -- Usuários que arquivaram o canal não veem contadores
  ) THEN
    RETURN 0;
  END IF;

  -- Count messages not read by user, EXCLUDING system messages
  SELECT COUNT(*)::integer INTO v_count
  FROM public.chat_messages cm
  WHERE cm.channel_id = p_channel_id
  AND cm.sender_id != v_user_id  -- Don't count own messages
  AND (
    cm.message_type IS NULL 
    OR cm.message_type NOT IN ('system_join', 'system_leave', 'system_archive', 'system_unarchive')
  )  -- EXCLUSÃO: Mensagens de sistema não contam como não lidas
  AND NOT EXISTS (
    SELECT 1 FROM public.message_read_receipts mrr
    WHERE mrr.message_id = cm.id
    AND mrr.user_id = v_user_id
  );

  RETURN COALESCE(v_count, 0);
END;
$$;

-- Permissões
GRANT EXECUTE ON FUNCTION public.get_unread_messages_count_v3(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_unread_messages_count_v3(uuid) TO service_role;

-- Comentário para documentação
COMMENT ON FUNCTION public.get_unread_messages_count_v3(uuid) IS 
'Gets count of unread messages in a channel for current user (v3: excludes system messages and checks archived status)';

-- Helper function para mapear messageId para channelId (usado pelo frontend)
CREATE OR REPLACE FUNCTION public.get_channel_id_from_message(p_message_id uuid)
RETURNS uuid
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT channel_id 
  FROM public.chat_messages 
  WHERE id = p_message_id
  LIMIT 1;
$$;

GRANT EXECUTE ON FUNCTION public.get_channel_id_from_message(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_channel_id_from_message(uuid) TO service_role;

COMMENT ON FUNCTION public.get_channel_id_from_message(uuid) IS 
'Helper function to get channel_id from message_id for cache invalidation';