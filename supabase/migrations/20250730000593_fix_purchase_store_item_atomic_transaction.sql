/**
 * CORREÇÃO CRÍTICA: purchase_store_item com transação atômica
 * 
 * PROBLEMA IDENTIFICADO (ISSUE #296):
 * - Stardust é debitado ANTES da inserção no inventário
 * - Se inserção falha, Stardust não é revertido
 * - Usuário perde Stardust mas não recebe o item
 * 
 * SOLUÇÃO:
 * - Transação atômica completa
 * - Rollback automático em caso de erro
 * - Verificações de integridade aprimoradas
 * 
 * <AUTHOR> Internet 2025
 */

CREATE OR REPLACE FUNCTION "public"."purchase_store_item"("p_user_id" "uuid", "p_item_id" "uuid") 
RETURNS "jsonb"
LANGUAGE "plpgsql" 
SECURITY DEFINER
AS $$
DECLARE
    v_item_name text;
    v_item_resource_id uuid;
    v_item_type text;
    v_user_level integer;
    v_item_level integer;
    v_user_company_id uuid;
    v_current_user_id uuid;
    v_user_balance integer;
    v_item_price integer;
    v_stardust_result jsonb;
    v_result jsonb;
    v_new_balance integer;
BEGIN
    -- 🔒 INÍCIO DA TRANSAÇÃO ATÔMICA
    -- Tudo dentro de um único bloco transacional
    
    -- Obter user_id da sessão autenticada
    v_current_user_id := auth.uid();
    
    -- CRÍTICO: Verificar se o user_id solicitado é o mesmo da sessão autenticada
    IF v_current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'Usuário não autenticado'
        );
    END IF;
    
    IF p_user_id != v_current_user_id THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'Acesso negado: não é possível comprar para outro usuário'
        );
    END IF;
    
    -- Obter company_id do usuário autenticado
    SELECT company_id INTO v_user_company_id 
    FROM public.profiles 
    WHERE id = v_current_user_id;
    
    IF v_user_company_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'Usuário não encontrado'
        );
    END IF;
    
    -- 🔍 VERIFICAÇÕES PRÉ-COMPRA (ANTES DO DÉBITO)
    
    -- Obter informações do item
    SELECT name, level_required, resource_id, item_type, price
    INTO v_item_name, v_item_level, v_item_resource_id, v_item_type, v_item_price
    FROM public.store_items
    WHERE id = p_item_id AND active = true;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'Item não encontrado ou inativo'
        );
    END IF;
    
    -- ✅ VERIFICAÇÃO CRÍTICA: Checar se usuário já possui o item ANTES do débito
    IF EXISTS (
        SELECT 1 FROM public.user_visual_assets
        WHERE user_id = v_current_user_id AND asset_id = v_item_resource_id
    ) THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'Você já possui este item'
        );
    END IF;
    
    -- Verificar se o asset existe
    IF NOT EXISTS (
        SELECT 1 FROM public.visual_assets 
        WHERE id = v_item_resource_id
    ) THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'Asset do item não encontrado no sistema'
        );
    END IF;
    
    -- Obter saldo atual usando função confiável
    v_user_balance := public.get_stardust_balance_v2();
    
    -- Verificar saldo suficiente
    IF v_user_balance < v_item_price THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', format('Stardust insuficiente. Necessário: %s, Disponível: %s', v_item_price, v_user_balance)
        );
    END IF;
    
    -- Verificar nível do usuário
    SELECT current_level
    INTO v_user_level
    FROM public.user_levels
    WHERE user_id = v_current_user_id AND company_id = v_user_company_id;
    
    -- Se não encontrou nível, assumir nível 1
    IF NOT FOUND THEN
        v_user_level := 1;
    END IF;
    
    IF v_user_level < v_item_level THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', format('Nível insuficiente. Necessário: %s, Atual: %s', v_item_level, v_user_level)
        );
    END IF;
    
    -- 💸 TRANSAÇÃO ATÔMICA: DÉBITO + INSERÇÃO NO INVENTÁRIO
    BEGIN
        -- PASSO 1: Debitar Stardust usando função segura
        SELECT subtract_stardust_with_validation(
            v_current_user_id,
            'store_item',
            p_item_id,
            1, -- quantidade
            jsonb_build_object(
                'item_name', v_item_name,
                'item_type', v_item_type,
                'resource_id', v_item_resource_id,
                'action', 'purchase_from_store',
                'atomic_transaction', true
            )
        ) INTO v_stardust_result;
        
        -- Verificar se a subtração foi bem-sucedida
        IF NOT (v_stardust_result->>'success')::boolean THEN
            -- Se falhou, retornar erro SEM precisar de rollback manual
            RETURN jsonb_build_object(
                'success', false,
                'message', v_stardust_result->>'error_message',
                'error_code', v_stardust_result->>'error_code',
                'debug_info', v_stardust_result
            );
        END IF;
        
        -- PASSO 2: Adicionar item ao inventário (user_visual_assets)
        -- Se isso falhar, o débito de Stardust será revertido automaticamente
        INSERT INTO public.user_visual_assets (
            user_id,
            asset_id,
            equipped,
            unlocked_at,
            metadata
        ) VALUES (
            v_current_user_id,
            v_item_resource_id,
            false,
            now(),
            jsonb_build_object(
                'purchased_from_store', true,
                'store_item_id', p_item_id,
                'purchase_price', v_item_price,
                'purchase_date', now(),
                'stardust_transaction', v_stardust_result,
                'atomic_transaction', true
            )
        );
        
        -- PASSO 3: Calcular novo saldo
        v_new_balance := (v_stardust_result->>'new_balance')::integer;
        
        -- ✅ SUCESSO: Ambas operações foram bem-sucedidas
        v_result := jsonb_build_object(
            'success', true,
            'message', format('Item %s adquirido com sucesso!', v_item_name),
            'item_id', p_item_id,
            'item_name', v_item_name,
            'price', v_item_price,
            'new_balance', v_new_balance,
            'resource_id', v_item_resource_id,
            'stardust_transaction', v_stardust_result,
            'transaction_type', 'atomic_purchase'
        );
        
    EXCEPTION
        WHEN unique_violation THEN
            -- Usuário já possui o item (race condition)
            RETURN jsonb_build_object(
                'success', false,
                'message', 'Você já possui este item',
                'error_type', 'duplicate_ownership'
            );
        WHEN foreign_key_violation THEN
            -- Asset não existe ou problema de referência
            RETURN jsonb_build_object(
                'success', false,
                'message', 'Erro de integridade: asset inválido',
                'error_type', 'invalid_asset'
            );
        WHEN OTHERS THEN
            -- Qualquer outro erro - rollback automático
            RAISE NOTICE 'Erro na transação atômica de compra: %', SQLERRM;
            RETURN jsonb_build_object(
                'success', false,
                'message', 'Erro interno na transação. Stardust não foi debitado.',
                'error_detail', SQLERRM,
                'error_type', 'transaction_failed',
                'transaction_rolled_back', true
            );
    END;
    
    RETURN v_result;
END;
$$;

-- Comentário da função corrigida
COMMENT ON FUNCTION public.purchase_store_item(uuid, uuid) IS 
'🔒 VERSÃO ATÔMICA: Compra de item com transação completa. Garante que débito de Stardust e adição ao inventário sejam atômicos. Corrige ISSUE #296.';

-- Manter permissões existentes
GRANT ALL ON FUNCTION "public"."purchase_store_item"("p_user_id" "uuid", "p_item_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."purchase_store_item"("p_user_id" "uuid", "p_item_id" "uuid") TO "service_role";

-- 📊 Adicionar índices para performance em user_visual_assets se não existirem
CREATE INDEX IF NOT EXISTS idx_user_visual_assets_user_asset 
ON public.user_visual_assets (user_id, asset_id);

CREATE INDEX IF NOT EXISTS idx_user_visual_assets_unlocked_at 
ON public.user_visual_assets (unlocked_at DESC);

-- 🧪 Função de teste para verificar integridade do sistema
CREATE OR REPLACE FUNCTION test_purchase_integrity(p_user_id uuid, p_item_id uuid)
RETURNS jsonb
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_initial_balance integer;
    v_final_balance integer;
    v_item_price integer;
    v_has_item_before boolean;
    v_has_item_after boolean;
    v_purchase_result jsonb;
BEGIN
    -- Estado inicial
    v_initial_balance := public.get_stardust_balance_v2();
    
    SELECT price INTO v_item_price FROM store_items WHERE id = p_item_id;
    
    SELECT EXISTS(
        SELECT 1 FROM user_visual_assets uva
        JOIN store_items si ON si.resource_id = uva.asset_id
        WHERE uva.user_id = p_user_id AND si.id = p_item_id
    ) INTO v_has_item_before;
    
    -- Tentar compra
    SELECT purchase_store_item(p_user_id, p_item_id) INTO v_purchase_result;
    
    -- Estado final
    v_final_balance := public.get_stardust_balance_v2();
    
    SELECT EXISTS(
        SELECT 1 FROM user_visual_assets uva
        JOIN store_items si ON si.resource_id = uva.asset_id
        WHERE uva.user_id = p_user_id AND si.id = p_item_id
    ) INTO v_has_item_after;
    
    RETURN jsonb_build_object(
        'purchase_result', v_purchase_result,
        'integrity_check', jsonb_build_object(
            'initial_balance', v_initial_balance,
            'final_balance', v_final_balance,
            'expected_final_balance', v_initial_balance - CASE WHEN (v_purchase_result->>'success')::boolean THEN v_item_price ELSE 0 END,
            'balance_correct', v_final_balance = (v_initial_balance - CASE WHEN (v_purchase_result->>'success')::boolean THEN v_item_price ELSE 0 END),
            'had_item_before', v_has_item_before,
            'has_item_after', v_has_item_after,
            'item_correctly_added', (NOT v_has_item_before AND v_has_item_after) OR v_has_item_before,
            'transaction_atomic', true
        )
    );
END;
$$;

GRANT EXECUTE ON FUNCTION test_purchase_integrity(uuid, uuid) TO authenticated;