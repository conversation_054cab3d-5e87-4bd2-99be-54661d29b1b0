/**
 * Estender tabela post_shares para suportar compartilhamentos de páginas de conhecimento
 * <AUTHOR> Internet 2025
 */

-- Adicionar coluna content_type para diferenciar posts de páginas de conhecimento
ALTER TABLE public.post_shares 
ADD COLUMN IF NOT EXISTS content_type TEXT DEFAULT 'post' NOT NULL 
CHECK (content_type IN ('post', 'knowledge_page'));

-- Adicionar coluna content_id para referenciar o conteúdo compartilhado
ALTER TABLE public.post_shares 
ADD COLUMN IF NOT EXISTS content_id UUID;

-- Preencher content_id com post_id para registros existentes
UPDATE public.post_shares 
SET content_id = post_id 
WHERE content_id IS NULL;

-- Tornar content_id obrigatório
ALTER TABLE public.post_shares 
ALTER COLUMN content_id SET NOT NULL;

-- Criar índices para otimização
CREATE INDEX IF NOT EXISTS idx_post_shares_content_type ON public.post_shares(content_type);
CREATE INDEX IF NOT EXISTS idx_post_shares_content_id ON public.post_shares(content_id);
CREATE INDEX IF NOT EXISTS idx_post_shares_content_type_id ON public.post_shares(content_type, content_id);

-- Atualizar constraint única para incluir content_type
-- Primeiro, remover constraint antiga se existir
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'post_shares_post_id_user_id_share_type_key' 
        AND table_name = 'post_shares'
    ) THEN
        ALTER TABLE public.post_shares DROP CONSTRAINT post_shares_post_id_user_id_share_type_key;
    END IF;
END $$;

-- Adicionar nova constraint única considerando content_type e content_id
ALTER TABLE public.post_shares 
ADD CONSTRAINT post_shares_content_unique 
UNIQUE (content_type, content_id, user_id, share_type);

-- Comentário na tabela atualizada
COMMENT ON TABLE public.post_shares IS 'Tabela para rastrear compartilhamentos de posts e páginas de conhecimento com RLS e multi-tenancy';
COMMENT ON COLUMN public.post_shares.content_type IS 'Tipo do conteúdo compartilhado: post ou knowledge_page';
COMMENT ON COLUMN public.post_shares.content_id IS 'ID do conteúdo compartilhado (post_id ou knowledge_page_id)';