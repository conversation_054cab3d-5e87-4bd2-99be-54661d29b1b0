-- <PERSON><PERSON>r consolidate_or_create_lead_v3: corrigir request_types para usar valores válidos da constraint
-- Migration: 20250730000604_create_consolidate_lead_v3.sql
-- <AUTHOR> Internet 2025

CREATE OR REPLACE FUNCTION "public"."consolidate_or_create_lead_v3"(
    "p_user_id" "uuid", 
    "p_requested_plan_id" "uuid", 
    "p_requested_addons" "jsonb" DEFAULT '[]'::"jsonb", 
    "p_contact_preferences" "jsonb" DEFAULT '{}'::"jsonb", 
    "p_upgrade_context" "jsonb" DEFAULT '{}'::"jsonb", 
    "p_source_context" "text" DEFAULT 'addon_flow'::"text"
) 
RETURNS TABLE(
    "success" boolean, 
    "lead_id" "uuid", 
    "action_taken" "text", 
    "consolidation_details" "jsonb", 
    "error_code" "text", 
    "error_message" "text"
)
LANGUAGE "plpgsql" SECURITY DEFINER
AS $$
DECLARE
    v_company_id UUID;
    v_existing_leads UUID[];
    v_consolidation_group_id UUID;
    v_new_lead_id UUID;
    v_action_taken TEXT;
    v_consolidation_details JSONB := '{}'::jsonb;
    v_recent_lead_window INTERVAL := INTERVAL '7 days';
    -- ✅ Variáveis para cálculo de valor
    v_selected_plan_data JSONB := '{}'::jsonb;
    v_estimated_monthly_value DECIMAL(10,2) := 0;
    v_plan_price DECIMAL(10,2) := 0;
    v_addons_price DECIMAL(10,2) := 0;
    v_current_subscription RECORD;
    v_original_plan_id UUID;
    -- ✅ Variável para source_feature dinâmico
    v_source_feature TEXT;
    -- ✅ Variáveis para análise simples
    v_request_type TEXT := 'full_upgrade';
    v_priority_score INTEGER := 3;
    v_urgency_notes TEXT := 'Standard upgrade request';
    v_current_addons JSONB := '[]'::jsonb;
BEGIN
    -- Obter company_id
    SELECT profiles.company_id INTO v_company_id
    FROM public.profiles WHERE profiles.id = p_user_id;

    IF v_company_id IS NULL THEN
        success := false;
        error_code := 'USER_NOT_FOUND';
        error_message := 'Usuário não encontrado ou sem empresa associada';
        RETURN NEXT;
        RETURN;
    END IF;

    -- ✅ CORREÇÃO: Busca da subscription atual SEM referência a previous_plan_id
    SELECT s.*, sp.id as plan_id, sp.name as plan_name, sp.price as plan_price
    INTO v_current_subscription
    FROM public.subscriptions s
    LEFT JOIN public.subscription_plans sp ON s.plan_id = sp.id
    WHERE s.company_id = v_company_id 
    AND s.status = 'active'
    ORDER BY s.created_at DESC
    LIMIT 1;

    -- ✅ CORREÇÃO: Lógica simplificada para determinar plano original
    IF v_current_subscription IS NOT NULL THEN
        -- Se o plano atual for pago (Pro/Premium), assumir que era Grátis antes
        IF v_current_subscription.plan_price > 0 THEN
            SELECT id INTO v_original_plan_id 
            FROM public.subscription_plans 
            WHERE (name ILIKE '%grátis%' OR name ILIKE '%free%' OR price = 0)
            ORDER BY price ASC, created_at ASC
            LIMIT 1;
        ELSE
            -- Se é gratuito, manter como plano original
            v_original_plan_id := v_current_subscription.plan_id;
        END IF;
    ELSE
        -- Se não há subscription, assumir que era plano gratuito
        SELECT id INTO v_original_plan_id 
        FROM public.subscription_plans 
        WHERE (name ILIKE '%grátis%' OR name ILIKE '%free%' OR price = 0)
        ORDER BY price ASC, created_at ASC
        LIMIT 1;
    END IF;

    -- ✅ Determinar source_feature baseado no contexto
    IF p_requested_plan_id IS NOT NULL AND (p_requested_addons = '[]'::jsonb OR jsonb_array_length(p_requested_addons) = 0) THEN
        -- Upgrade de plano puro (sem addons)
        v_source_feature := 'plan_flow_system';
    ELSIF p_requested_plan_id IS NULL AND jsonb_array_length(p_requested_addons) > 0 THEN
        -- Somente addons (sem mudança de plano)
        v_source_feature := 'addon_flow_system';
    ELSIF p_requested_plan_id IS NOT NULL AND jsonb_array_length(p_requested_addons) > 0 THEN
        -- Plano + addons
        v_source_feature := 'hybrid_flow_system';
    ELSE
        -- Fallback para contexto original
        v_source_feature := 'addon_flow_system';
    END IF;

    -- ✅ Buscar dados completos do plano solicitado
    IF p_requested_plan_id IS NOT NULL THEN
        -- Extrair preço do plano primeiro
        SELECT price INTO v_plan_price
        FROM public.subscription_plans
        WHERE id = p_requested_plan_id;
        
        -- Construir dados do plano manualmente
        SELECT 
            ('{"id":"' || id || '","name":"' || name || '","price":' || price || '}')::jsonb
        INTO v_selected_plan_data
        FROM public.subscription_plans
        WHERE id = p_requested_plan_id;
    END IF;

    -- ✅ Simplificar cálculo dos addons (sem usar jsonb_array_elements)
    IF p_requested_addons IS NOT NULL AND jsonb_array_length(p_requested_addons) > 0 THEN
        -- Assumir preço base dos addons para cálculo simples
        v_addons_price := jsonb_array_length(p_requested_addons) * 50.00; -- valor base por addon
    END IF;

    -- ✅ Calcular valor mensal total estimado
    v_estimated_monthly_value := v_plan_price + v_addons_price;

    -- ✅ Definir valores de análise diretamente (sem função externa)
    v_current_addons := COALESCE(p_requested_addons, '[]'::jsonb);
    
    -- ✅ CORREÇÃO: Determinar tipo de request baseado nos parâmetros (usando valores válidos da constraint)
    IF p_requested_plan_id IS NOT NULL AND jsonb_array_length(p_requested_addons) = 0 THEN
        v_request_type := 'plan_change';
        v_priority_score := 4;
    ELSIF p_requested_plan_id IS NULL AND jsonb_array_length(p_requested_addons) > 0 THEN
        v_request_type := 'addon_request';
        v_priority_score := 2;
    ELSIF p_requested_plan_id IS NOT NULL AND jsonb_array_length(p_requested_addons) > 0 THEN
        v_request_type := 'hybrid_upgrade';
        v_priority_score := 5;
    ELSE
        v_request_type := 'full_upgrade';
        v_priority_score := 1;
    END IF;

    -- Buscar leads recentes do mesmo usuário (últimos 7 dias)
    SELECT array_agg(cl.id) INTO v_existing_leads
    FROM public.commercial_leads cl
    WHERE cl.user_id = p_user_id
    AND cl.company_id = v_company_id
    AND cl.status IN ('pending', 'contacted', 'negotiating')
    AND cl.created_at > (now() - v_recent_lead_window);

    -- Decidir ação baseada em leads existentes
    IF v_existing_leads IS NOT NULL AND array_length(v_existing_leads, 1) > 0 THEN
        -- Gerar ID de grupo de consolidação
        v_consolidation_group_id := gen_random_uuid();
        
        -- Marcar leads existentes como consolidados
        UPDATE public.commercial_leads 
        SET 
            consolidation_group_id = v_consolidation_group_id,
            is_consolidated = true,
            updated_at = now()
        WHERE id = ANY(v_existing_leads);

        v_action_taken := 'consolidated';
        v_consolidation_details := jsonb_build_object(
            'previous_leads_count', array_length(v_existing_leads, 1),
            'previous_leads_ids', v_existing_leads,
            'consolidation_window_days', 7,
            'consolidation_reason', 'Multiple requests within 7 days'
        );
    ELSE
        v_action_taken := 'created';
        v_consolidation_details := jsonb_build_object(
            'new_lead', true,
            'no_recent_leads', true
        );
    END IF;

    -- ✅ Criar novo lead com dados corrigidos
    INSERT INTO public.commercial_leads (
        company_id,
        user_id,
        selected_plan,
        selected_addons,
        contact_info,
        payment_preferences,
        source_context,
        source_feature,
        request_type,
        current_plan_id,
        current_addons,
        request_priority,
        commercial_urgency_notes,
        previous_request_id,
        consolidation_group_id,
        is_consolidated,
        decision_timeline_days,
        estimated_monthly_value,
        status,
        activation_status,
        created_at,
        updated_at
    ) VALUES (
        v_company_id,
        p_user_id,
        CASE WHEN p_requested_plan_id IS NOT NULL THEN
            v_selected_plan_data
        ELSE
            '{}'::jsonb
        END,
        p_requested_addons,
        p_contact_preferences,
        jsonb_build_object('method', 'automatic', 'urgency', v_priority_score),
        p_source_context,
        v_source_feature,
        v_request_type,
        v_original_plan_id,
        v_current_addons,
        v_priority_score,
        v_urgency_notes,
        CASE WHEN v_existing_leads IS NOT NULL AND array_length(v_existing_leads, 1) > 0 THEN
            v_existing_leads[1]
        ELSE
            NULL
        END,
        v_consolidation_group_id,
        v_action_taken = 'consolidated',
        CASE WHEN v_priority_score <= 3 THEN 3 ELSE 7 END,
        v_estimated_monthly_value,
        'pending',
        'not_activated',
        now(),
        now()
    ) RETURNING id INTO v_new_lead_id;

    -- Construir resposta de sucesso
    success := true;
    lead_id := v_new_lead_id;
    action_taken := v_action_taken;
    consolidation_details := jsonb_build_object(
        'consolidation_details', v_consolidation_details,
        'original_plan_id', v_original_plan_id,
        'current_plan_id', v_current_subscription.plan_id,
        'source_feature', v_source_feature,
        'estimated_value', v_estimated_monthly_value,
        'request_analysis', jsonb_build_object(
            'request_type', v_request_type,
            'priority_score', v_priority_score,
            'urgency_notes', v_urgency_notes,
            'current_addons', v_current_addons
        )
    );
    error_code := null;
    error_message := null;

    RETURN NEXT;
    RETURN;

EXCEPTION
    WHEN OTHERS THEN
        success := false;
        error_code := 'INTERNAL_ERROR';
        error_message := 'Erro interno: ' || SQLERRM;
        RETURN NEXT;
        RETURN;
END;
$$;

-- Comentários da versão v3
COMMENT ON FUNCTION "public"."consolidate_or_create_lead_v3" IS 'v3 - CORRIGIDO: request_types compatíveis com constraint (plan_change, addon_request, hybrid_upgrade, full_upgrade)';