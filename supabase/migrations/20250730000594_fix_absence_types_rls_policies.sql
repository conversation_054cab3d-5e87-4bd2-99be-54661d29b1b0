/**
 * Migration: <PERSON><PERSON><PERSON><PERSON> Políticas RLS para Tipos de Ausência
 * 
 * PROBLEMA:
 * - A política RLS para absence_types não estava permitindo INSERT
 * - Missing WITH CHECK clause para operações INSERT/UPDATE
 * 
 * SOLUÇÃO:
 * - <PERSON><PERSON><PERSON><PERSON> pol<PERSON>s com WITH CHECK appropriado
 * - Separar políticas por operação para melhor controle
 * 
 * <AUTHOR> Internet 2025
 */

-- =====================================================
-- REMOVER POLÍTICAS EXISTENTES
-- =====================================================

DROP POLICY IF EXISTS "Users can view absence types from their company" ON public.absence_types;
DROP POLICY IF EXISTS "HR can manage absence types" ON public.absence_types;

-- =====================================================
-- RECRIAR POLÍTICAS COM WITH CHECK CORRETO
-- =====================================================

-- Política para SELECT: Usuários podem ver tipos da sua empresa
CREATE POLICY "absence_types_select"
    ON public.absence_types FOR SELECT
    USING (
        company_id IN (
            SELECT p.company_id 
            FROM public.profiles p 
            WHERE p.id = auth.uid()
        )
    );

-- Política para INSERT: Apenas com permissão específica
CREATE POLICY "absence_types_insert"
    ON public.absence_types FOR INSERT
    WITH CHECK (
        company_id IN (
            SELECT p.company_id 
            FROM public.profiles p 
            WHERE p.id = auth.uid()
        )
        AND EXISTS (
            SELECT 1 FROM public.check_permission_v2(
                auth.uid(),
                'absence_type',
                'manage_absence_types',
                NULL
            ) WHERE check_permission_v2 = true
        )
    );

-- Política para UPDATE: Apenas com permissão específica
CREATE POLICY "absence_types_update"
    ON public.absence_types FOR UPDATE
    USING (
        company_id IN (
            SELECT p.company_id 
            FROM public.profiles p 
            WHERE p.id = auth.uid()
        )
        AND EXISTS (
            SELECT 1 FROM public.check_permission_v2(
                auth.uid(),
                'absence_type',
                'manage_absence_types',
                NULL
            ) WHERE check_permission_v2 = true
        )
    )
    WITH CHECK (
        company_id IN (
            SELECT p.company_id 
            FROM public.profiles p 
            WHERE p.id = auth.uid()
        )
        AND EXISTS (
            SELECT 1 FROM public.check_permission_v2(
                auth.uid(),
                'absence_type',
                'manage_absence_types',
                NULL
            ) WHERE check_permission_v2 = true
        )
    );

-- Política para DELETE: Apenas com permissão específica
CREATE POLICY "absence_types_delete"
    ON public.absence_types FOR DELETE
    USING (
        company_id IN (
            SELECT p.company_id 
            FROM public.profiles p 
            WHERE p.id = auth.uid()
        )
        AND EXISTS (
            SELECT 1 FROM public.check_permission_v2(
                auth.uid(),
                'absence_type',
                'manage_absence_types',
                NULL
            ) WHERE check_permission_v2 = true
        )
    );

-- =====================================================
-- VERIFICAÇÃO DE PERMISSÕES
-- =====================================================

-- Verificar se as permissões necessárias existem
DO $$
BEGIN
    -- Verificar se o resource_type existe
    IF NOT EXISTS (
        SELECT 1 FROM public.resource_types 
        WHERE key = 'absence_type'
    ) THEN
        RAISE NOTICE '⚠️  Resource type absence_type não encontrado! Executando inserção...';
        
        INSERT INTO public.resource_types (key, name) VALUES
        ('absence_type', 'Tipos de Ausência')
        ON CONFLICT (key) DO NOTHING;
        
        RAISE NOTICE '✅ Resource type absence_type criado!';
    END IF;

    -- Verificar se a action existe
    IF NOT EXISTS (
        SELECT 1 FROM public.permission_actions 
        WHERE key = 'manage_absence_types'
    ) THEN
        RAISE NOTICE '⚠️  Permission action manage_absence_types não encontrada! Executando inserção...';
        
        INSERT INTO public.permission_actions (key, name, resource_type_key) VALUES
        ('manage_absence_types', 'Gerenciar Tipos de Ausência', 'absence_type')
        ON CONFLICT (key) DO NOTHING;
        
        RAISE NOTICE '✅ Permission action manage_absence_types criada!';
    END IF;

    -- Verificar permissões para admin
    IF NOT EXISTS (
        SELECT 1 FROM public.default_role_permissions 
        WHERE role_key = 'admin' 
        AND resource_type_key = 'absence_type' 
        AND action_key = 'manage_absence_types'
    ) THEN
        RAISE NOTICE '⚠️  Permissão admin para manage_absence_types não encontrada! Executando inserção...';
        
        INSERT INTO public.default_role_permissions (role_key, resource_type_key, action_key, is_granted) VALUES
        ('admin', 'absence_type', 'manage_absence_types', true)
        ON CONFLICT (role_key, resource_type_key, action_key) DO UPDATE SET is_granted = EXCLUDED.is_granted;
        
        RAISE NOTICE '✅ Permissão admin para manage_absence_types criada!';
    END IF;

    RAISE NOTICE '🎉 Políticas RLS para absence_types corrigidas com sucesso!';
END;
$$;