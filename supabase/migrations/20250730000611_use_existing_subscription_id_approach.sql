-- ==================================================
-- FIX: USAR SUBSCRIPTION_ID EXISTENTE DA EMPRESA
-- Modificar para usar subscription_id diretamente da empresa
-- <AUTHOR> Internet 2025
-- ==================================================

-- Recriar a função usando abordagem mais direta
CREATE OR REPLACE FUNCTION public.activate_plan_subscription_with_billing_v1(
    p_lead_id UUID,
    p_activation_notes TEXT DEFAULT NULL
)
RETURNS TABLE(
    success BOOLEAN,
    lead_id UUID,
    subscription_id UUID,
    billing_id UUID,
    courtesy_end_date timestamptz,
    error_code TEXT,
    error_message TEXT,
    activation_details JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    current_company_id UUID;
    lead_record RECORD;
    plan_activation_result RECORD;
    billing_entry_id UUID;
    plan_price NUMERIC;
    plan_name TEXT;
    existing_subscription_id UUID;
BEGIN
    -- Obter company_id do usuário autenticado
    SELECT profiles.company_id INTO current_company_id
    FROM public.profiles WHERE profiles.id = auth.uid();

    IF current_company_id IS NULL THEN
        success := false;
        error_code := 'USER_NOT_FOUND';
        error_message := 'Usuário não encontrado ou sem empresa associada';
        activation_details := '{}'::jsonb;
        RETURN NEXT;
        RETURN;
    END IF;

    -- Verificar se é usuário da Vindula (apenas Vindula pode ativar planos comerciais)
    IF NOT public.is_vindula_company() THEN
        success := false;
        error_code := 'PERMISSION_DENIED';
        error_message := 'Apenas usuários Vindula podem ativar planos comerciais';
        activation_details := '{}'::jsonb;
        RETURN NEXT;
        RETURN;
    END IF;

    -- Buscar dados do lead E subscription existente
    SELECT 
        cl.*,
        c.name as company_name,
        s.id as existing_subscription_id
    INTO lead_record
    FROM commercial_leads cl
    JOIN companies c ON cl.company_id = c.id
    LEFT JOIN subscriptions s ON s.company_id = cl.company_id AND s.status = 'active'
    WHERE cl.id = p_lead_id;

    IF NOT FOUND THEN
        success := false;
        error_code := 'LEAD_NOT_FOUND';
        error_message := 'Lead comercial não encontrado';
        activation_details := '{}'::jsonb;
        RETURN NEXT;
        RETURN;
    END IF;

    -- Verificar se tem subscription ativa
    IF lead_record.existing_subscription_id IS NULL THEN
        success := false;
        error_code := 'NO_SUBSCRIPTION_FOUND';
        error_message := 'Nenhuma subscription ativa encontrada para a empresa';
        activation_details := '{}'::jsonb;
        RETURN NEXT;
        RETURN;
    END IF;

    -- Verificar se é uma mudança de plano (não add-on)
    IF lead_record.request_type != 'plan_change' THEN
        success := false;
        error_code := 'INVALID_REQUEST_TYPE';
        error_message := 'Esta função é apenas para mudanças de plano';
        activation_details := '{}'::jsonb;
        RETURN NEXT;
        RETURN;
    END IF;

    -- Extrair informações do plano
    plan_price := (lead_record.selected_plan->>'price')::NUMERIC;
    plan_name := lead_record.selected_plan->>'name';
    existing_subscription_id := lead_record.existing_subscription_id;

    IF plan_price IS NULL THEN
        success := false;
        error_code := 'PLAN_NOT_FOUND';
        error_message := 'Informações do plano não encontradas';
        activation_details := '{}'::jsonb;
        RETURN NEXT;
        RETURN;
    END IF;

    BEGIN
        -- 1. Ativar o plano usando função existente
        SELECT * INTO plan_activation_result
        FROM public.activate_plan_with_courtesy_v1(
            p_lead_id,                                         -- p_lead_id
            lead_record.user_id,                               -- p_user_id
            lead_record.selected_plan,                         -- p_selected_plan (JSON completo)
            '[]'::jsonb                                        -- p_selected_addons (vazio para mudança de plano)
        );

        IF NOT plan_activation_result.success THEN
            success := false;
            error_code := plan_activation_result.error_code;
            error_message := 'Erro ao ativar plano: ' || plan_activation_result.error_message;
            activation_details := '{}'::jsonb;
            RETURN NEXT;
            RETURN;
        END IF;

        -- 2. Gerar entrada de cobrança no sistema de billing - usar subscription existente
        INSERT INTO subscription_billing_history (
            company_id,
            subscription_id,
            plan_id,
            plan_name,
            plan_monthly_price,
            plan_features,
            billing_period_start,
            billing_period_end,
            billing_status,
            total_plan_amount,
            total_addons_amount,
            total_amount,
            payment_method
        ) VALUES (
            lead_record.company_id,
            existing_subscription_id,                          -- USAR subscription existente
            (lead_record.selected_plan->>'id')::UUID,
            plan_name,
            plan_price,
            COALESCE(lead_record.selected_plan->'features', '{}'::jsonb),
            now(), -- billing_period_start
            now() + interval '1 month', -- billing_period_end (mensal)
            'pending', -- Status pendente - será marcado como pago manualmente
            plan_price, -- total_plan_amount
            0, -- total_addons_amount (sem add-ons por enquanto)
            plan_price, -- total_amount = plan_price
            'commercial_activation' -- payment_method
        ) RETURNING id INTO billing_entry_id;

        -- 3. Atualizar status do lead para 'converted'
        UPDATE commercial_leads 
        SET 
            status = 'converted',
            activation_status = 'activated',
            updated_at = now(),
            approval_status = 'approved',
            approved_by = auth.uid(),
            approved_at = now(),
            approval_notes = COALESCE(p_activation_notes, 'Plano ativado com cobrança gerada'),
            activation_details = jsonb_build_object(
                'billing_id', billing_entry_id,
                'activation_date', now(),
                'activated_by', auth.uid(),
                'activation_record_id', plan_activation_result.activation_id
            )
        WHERE id = p_lead_id;

        -- Retorno de sucesso - usar subscription_id existente
        success := true;
        lead_id := p_lead_id;
        subscription_id := existing_subscription_id;           -- USAR subscription existente
        billing_id := billing_entry_id;
        courtesy_end_date := plan_activation_result.courtesy_end_date;
        error_code := NULL;
        error_message := NULL;
        activation_details := jsonb_build_object(
            'plan_name', plan_name,
            'plan_price', plan_price,
            'billing_id', billing_entry_id,
            'company_name', lead_record.company_name,
            'activation_type', 'commercial_conversion',
            'courtesy_end_date', plan_activation_result.courtesy_end_date,
            'previous_plan_id', plan_activation_result.previous_plan_id,
            'activation_record_id', plan_activation_result.activation_id,
            'used_existing_subscription', true
        );
        RETURN NEXT;
        RETURN;

    EXCEPTION
        WHEN OTHERS THEN
            -- Capturar erros de transação
            success := false;
            error_code := 'TRANSACTION_ERROR';
            error_message := 'Erro durante ativação: ' || SQLERRM;
            activation_details := jsonb_build_object(
                'sql_error_code', SQLSTATE,
                'sql_error_message', SQLERRM,
                'lead_id', p_lead_id
            );
            RETURN NEXT;
            RETURN;
    END;
END;
$$;

-- Comentários da função
COMMENT ON FUNCTION public.activate_plan_subscription_with_billing_v1(UUID, TEXT) IS 
'V1: Ativa plano comercial e gera cobrança automática no billing - USA subscription existente';

-- Permissões
GRANT EXECUTE ON FUNCTION public.activate_plan_subscription_with_billing_v1(UUID, TEXT) TO authenticated;