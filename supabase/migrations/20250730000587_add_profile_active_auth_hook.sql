-- Migration: Implementar Auth Hook para bloquear login de usuários inativos
-- Author: Vindula Internet 2025
-- Description: Custom Access Token Hook que verifica se profile está ativo ANTES do login ser concluído

-- ===================================================================
-- 🚨 CUSTOM ACCESS TOKEN HOOK - VALIDAÇÃO DE PROFILE ATIVO
-- ===================================================================
-- Este hook roda ANTES de um token ser emitido e pode BLOQUEAR o login
-- se o profile do usuário estiver inativo (active = false)

CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_active boolean;
    profile_record RECORD;
    claims jsonb;
    target_user_id uuid;
BEGIN
    -- Extrair user_id do evento
    target_user_id := (event->>'user_id')::uuid;
    
    -- Extrair claims do evento
    claims := event->'claims';
    
    -- Buscar profile do usuário
    SELECT active INTO profile_record
    FROM public.profiles 
    WHERE id = target_user_id;
    
    -- 🚨 VALIDAÇÃO CRÍTICA DE SEGURANÇA
    -- Se profile não existe, BLOQUEAR login
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Profile not found for user' 
        USING 
            ERRCODE = 'P0001',
            DETAIL = 'User profile must be created before authentication',
            HINT = 'Contact administrator to set up your profile';
    END IF;
    
    -- Se profile está inativo, BLOQUEAR login
    IF profile_record.active = false THEN
        RAISE EXCEPTION 'Account deactivated' 
        USING 
            ERRCODE = 'P0002',
            DETAIL = 'Your account has been deactivated by an administrator',
            HINT = 'Contact support to reactivate your account';
    END IF;
    
    -- ✅ Profile válido e ativo - permitir login
    -- Adicionar claim personalizada indicando que passou pela validação
    claims := jsonb_set(
        claims,
        '{profile_validated}',
        'true',
        true
    );
    
    -- Adicionar timestamp da validação para auditoria
    claims := jsonb_set(
        claims,
        '{validation_timestamp}',
        to_jsonb(extract(epoch from now())),
        true
    );
    
    -- Retornar evento com claims atualizadas
    RETURN jsonb_set(event, '{claims}', claims);
    
EXCEPTION 
    WHEN OTHERS THEN
        -- Log do erro para debugging (será visível nos logs do Supabase)
        RAISE LOG 'Custom Access Token Hook Error for user %: % (%)', 
            target_user_id, 
            SQLERRM, 
            SQLSTATE;
        
        -- Re-lançar o erro para bloquear o login
        RAISE;
END;
$$;

-- Comentário para documentação
COMMENT ON FUNCTION public.custom_access_token_hook(jsonb) IS 
'Hook de autenticação que valida se o profile do usuário está ativo antes de emitir o token. Bloqueia login de usuários inativos.';

-- ===================================================================
-- 🛡️ SEGURANÇA: Conceder permissões apenas para o Supabase Auth
-- ===================================================================
-- A função deve ser executável pelo sistema de auth do Supabase
-- mas não diretamente pelos usuários da aplicação

-- Revogar permissões públicas (por segurança)
REVOKE ALL ON FUNCTION public.custom_access_token_hook(jsonb) FROM PUBLIC;
REVOKE ALL ON FUNCTION public.custom_access_token_hook(jsonb) FROM authenticated;
REVOKE ALL ON FUNCTION public.custom_access_token_hook(jsonb) FROM anon;

-- Conceder apenas para supabase_auth_admin (usado pelo sistema de auth)
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(jsonb) TO supabase_auth_admin;