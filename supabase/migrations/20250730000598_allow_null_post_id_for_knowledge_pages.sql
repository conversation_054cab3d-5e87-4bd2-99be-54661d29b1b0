/**
 * Permitir post_id NULL para knowledge_pages na tabela post_shares
 * <AUTHOR> Internet 2025
 */

-- <PERSON>erar coluna post_id para permitir NULL
ALTER TABLE public.post_shares 
ALTER COLUMN post_id DROP NOT NULL;

-- Adicionar constraint para garantir que ou post_id ou content_id seja preenchido
ALTER TABLE public.post_shares 
ADD CONSTRAINT post_shares_content_reference_check 
CHECK (
  (content_type = 'post' AND post_id IS NOT NULL AND content_id = post_id) OR
  (content_type = 'knowledge_page' AND post_id IS NULL AND content_id IS NOT NULL)
);

-- Coment<PERSON><PERSON> explicativo
COMMENT ON CONSTRAINT post_shares_content_reference_check ON public.post_shares IS 
'Garante que posts usem post_id e knowledge_pages usem apenas content_id';

-- Coment<PERSON>rio atualizado na coluna post_id
COMMENT ON COLUMN public.post_shares.post_id IS 'ID do post (apenas para content_type = post, NULL para knowledge_page)';