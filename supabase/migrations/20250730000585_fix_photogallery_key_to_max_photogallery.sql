-- Corrigir chave de photogallery_max para max_photogallery na feature flag "conteudos"
-- Para seguir o padrão max_[tipo] usado pela função get_content_type_limit
-- <AUTHOR> Internet 2025

-- Atualizar a feature flag "conteudos" para usar max_photogallery
UPDATE public.feature_flags 
SET access_levels = jsonb_set(
  jsonb_set(
    jsonb_set(
      -- Primeiro remover as chaves antigas photogallery_max
      jsonb_set(
        jsonb_set(
          access_levels #- '{Grátis,limits,photogallery_max}',
          '{Pro,limits}',
          (access_levels->'Pro'->'limits') #- '{photogallery_max}'
        ),
        '{Max,limits}',
        (access_levels->'Max'->'limits') #- '{photogallery_max}'
      ),
      -- Adicionar as novas chaves max_photogallery
      '{Grátis,limits,max_photogallery}',
      '15'::jsonb
    ),
    '{Pro,limits,max_photogallery}',
    '75'::jsonb
  ),
  '{Max,limits,max_photogallery}',
  '-1'::jsonb
),
updated_at = NOW()
WHERE key = 'conteudos';