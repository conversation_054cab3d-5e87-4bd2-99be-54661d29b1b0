-- Migration: Corrigir marketplace functions para usar subscriptions corretamente
-- <AUTHOR> Internet 2025

-- Drop e recriar todas as funções marketplace usando o relacionamento correto
DROP FUNCTION IF EXISTS validate_marketplace_category_creation();
DROP FUNCTION IF EXISTS validate_marketplace_category_creation(UUID);
DROP FUNCTION IF EXISTS validate_marketplace_item_creation(UUID);
DROP FUNCTION IF EXISTS validate_marketplace_item_creation(UUID, UUID);
DROP FUNCTION IF EXISTS validate_marketplace_offer_creation();
DROP FUNCTION IF EXISTS validate_marketplace_offer_creation(UUID);

-- Função para validar criação de categorias marketplace
CREATE OR REPLACE FUNCTION validate_marketplace_category_creation()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_company_id UUID;
  feature_data JSONB;
  subscription_plan TEXT;
  max_categories INTEGER;
  current_categories INTEGER;
  can_create BOOLEAN;
  remaining_slots INTEGER;
  is_unlimited BOOLEAN;
BEGIN
  -- Obter company_id através de auth.uid() + profiles
  SELECT profiles.company_id INTO v_company_id
  FROM profiles WHERE profiles.id = auth.uid();
  
  IF v_company_id IS NULL THEN
    RAISE EXCEPTION 'Usuário não encontrado ou sem empresa associada';
  END IF;

  -- Buscar plano da empresa através do relacionamento correto
  SELECT sp.name INTO subscription_plan
  FROM subscriptions s
  JOIN subscription_plans sp ON sp.id = s.plan_id
  WHERE s.company_id = v_company_id 
    AND s.status = 'active'
  ORDER BY s.created_at DESC
  LIMIT 1;
    
  IF subscription_plan IS NULL THEN
    subscription_plan := 'Grátis';
  END IF;
  
  -- Buscar dados da feature flag
  SELECT access_levels -> subscription_plan INTO feature_data
  FROM feature_flags 
  WHERE key = 'marketplace';
  
  -- Extrair limitações
  max_categories := COALESCE((feature_data -> 'limits' ->> 'max_categories')::INTEGER, 6);
  is_unlimited := max_categories = -1;
  
  -- Contar categorias atuais
  SELECT COUNT(*) INTO current_categories
  FROM strategic_categories
  WHERE company_id = v_company_id;
  
  -- Calcular se pode criar
  can_create := is_unlimited OR current_categories < max_categories;
  
  -- Calcular slots restantes
  IF is_unlimited THEN
    remaining_slots := NULL;
  ELSE
    remaining_slots := GREATEST(0, max_categories - current_categories);
  END IF;
  
  RETURN jsonb_build_object(
    'can_create', can_create,
    'current_count', current_categories,
    'limit', max_categories,
    'remaining_slots', remaining_slots,
    'is_unlimited', is_unlimited,
    'subscription_plan', subscription_plan
  );
END;
$$;

-- Função para validar criação de itens marketplace
CREATE OR REPLACE FUNCTION validate_marketplace_item_creation(p_category_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_company_id UUID;
  feature_data JSONB;
  subscription_plan TEXT;
  max_items_per_category INTEGER;
  current_items INTEGER;
  can_create BOOLEAN;
  remaining_slots INTEGER;
  is_unlimited BOOLEAN;
BEGIN
  -- Obter company_id através de auth.uid() + profiles
  SELECT profiles.company_id INTO v_company_id
  FROM profiles WHERE profiles.id = auth.uid();
  
  IF v_company_id IS NULL THEN
    RAISE EXCEPTION 'Usuário não encontrado ou sem empresa associada';
  END IF;

  -- Verificar se a categoria pertence à empresa do usuário
  IF NOT EXISTS (
    SELECT 1 FROM strategic_categories 
    WHERE id = p_category_id AND company_id = v_company_id
  ) THEN
    RAISE EXCEPTION 'Categoria não encontrada ou não pertence à empresa';
  END IF;

  -- Buscar plano da empresa através do relacionamento correto
  SELECT sp.name INTO subscription_plan
  FROM subscriptions s
  JOIN subscription_plans sp ON sp.id = s.plan_id
  WHERE s.company_id = v_company_id 
    AND s.status = 'active'
  ORDER BY s.created_at DESC
  LIMIT 1;
    
  IF subscription_plan IS NULL THEN
    subscription_plan := 'Grátis';
  END IF;
  
  -- Buscar dados da feature flag
  SELECT access_levels -> subscription_plan INTO feature_data
  FROM feature_flags 
  WHERE key = 'marketplace';
  
  -- Extrair limitações
  max_items_per_category := COALESCE((feature_data -> 'limits' ->> 'max_items_per_category')::INTEGER, 5);
  is_unlimited := max_items_per_category = -1;
  
  -- Contar itens atuais na categoria
  SELECT COUNT(*) INTO current_items
  FROM strategic_items si
  JOIN strategic_categories sc ON sc.id = si.category_id
  WHERE sc.company_id = v_company_id 
    AND si.category_id = p_category_id;
  
  -- Calcular se pode criar
  can_create := is_unlimited OR current_items < max_items_per_category;
  
  -- Calcular slots restantes
  IF is_unlimited THEN
    remaining_slots := NULL;
  ELSE
    remaining_slots := GREATEST(0, max_items_per_category - current_items);
  END IF;
  
  RETURN jsonb_build_object(
    'can_create', can_create,
    'current_count', current_items,
    'limit', max_items_per_category,
    'remaining_slots', remaining_slots,
    'is_unlimited', is_unlimited,
    'subscription_plan', subscription_plan
  );
END;
$$;

-- Função para validar criação de ofertas especiais marketplace
CREATE OR REPLACE FUNCTION validate_marketplace_offer_creation()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_company_id UUID;
  feature_data JSONB;
  subscription_plan TEXT;
  max_special_offers INTEGER;
  current_offers INTEGER;
  can_create BOOLEAN;
  remaining_slots INTEGER;
  is_unlimited BOOLEAN;
BEGIN
  -- Obter company_id através de auth.uid() + profiles
  SELECT profiles.company_id INTO v_company_id
  FROM profiles WHERE profiles.id = auth.uid();
  
  IF v_company_id IS NULL THEN
    RAISE EXCEPTION 'Usuário não encontrado ou sem empresa associada';
  END IF;

  -- Buscar plano da empresa através do relacionamento correto
  SELECT sp.name INTO subscription_plan
  FROM subscriptions s
  JOIN subscription_plans sp ON sp.id = s.plan_id
  WHERE s.company_id = v_company_id 
    AND s.status = 'active'
  ORDER BY s.created_at DESC
  LIMIT 1;
    
  IF subscription_plan IS NULL THEN
    subscription_plan := 'Grátis';
  END IF;
  
  -- Buscar dados da feature flag
  SELECT access_levels -> subscription_plan INTO feature_data
  FROM feature_flags 
  WHERE key = 'marketplace';
  
  -- Extrair limitações
  max_special_offers := COALESCE((feature_data -> 'limits' ->> 'max_special_offers')::INTEGER, 1);
  is_unlimited := max_special_offers = -1;
  
  -- Contar ofertas especiais atuais
  SELECT COUNT(*) INTO current_offers
  FROM special_offers
  WHERE company_id = v_company_id;
  
  -- Calcular se pode criar
  can_create := is_unlimited OR current_offers < max_special_offers;
  
  -- Calcular slots restantes
  IF is_unlimited THEN
    remaining_slots := NULL;
  ELSE
    remaining_slots := GREATEST(0, max_special_offers - current_offers);
  END IF;
  
  RETURN jsonb_build_object(
    'can_create', can_create,
    'current_count', current_offers,
    'limit', max_special_offers,
    'remaining_slots', remaining_slots,
    'is_unlimited', is_unlimited,
    'subscription_plan', subscription_plan
  );
END;
$$;

-- Conceder permissões
GRANT EXECUTE ON FUNCTION validate_marketplace_category_creation() TO authenticated;
GRANT EXECUTE ON FUNCTION validate_marketplace_item_creation(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_marketplace_offer_creation() TO authenticated;

-- Comentários
COMMENT ON FUNCTION validate_marketplace_category_creation() IS 'Valida se empresa pode criar nova categoria marketplace baseado no plano';
COMMENT ON FUNCTION validate_marketplace_item_creation(UUID) IS 'Valida se empresa pode criar novo item marketplace em categoria específica baseado no plano';
COMMENT ON FUNCTION validate_marketplace_offer_creation() IS 'Valida se empresa pode criar nova oferta especial marketplace baseado no plano';