-- Migration: Recriar funções do marketplace com aliases explícitos para resolver ambiguidade
-- <AUTHOR> Internet 2025

-- Dropar funções existentes
DROP FUNCTION IF EXISTS get_strategic_categories(uuid);
DROP FUNCTION IF EXISTS get_all_strategic_categories(uuid);

-- Recriar função get_strategic_categories com aliases explícitos
CREATE OR REPLACE FUNCTION get_strategic_categories(p_company_id uuid DEFAULT NULL)
RETURNS TABLE(
    id uuid,
    name text,
    description text,
    icon text,
    gradient text,
    bg_gradient text,
    value_proposition text,
    order_position integer,
    items_count bigint,
    total_revenue bigint
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_company_id uuid;
BEGIN
    -- Se não foi passado company_id, usar do usuário atual
    IF p_company_id IS NULL THEN
        SELECT profiles.company_id INTO v_company_id
        FROM profiles WHERE profiles.id = auth.uid();
        
        IF v_company_id IS NULL THEN
            RAISE EXCEPTION 'Usuário não encontrado ou sem empresa associada';
        END IF;
    ELSE
        v_company_id := p_company_id;
    END IF;

    RETURN QUERY
    SELECT 
        categories.id,
        categories.name,
        categories.description,
        categories.icon,
        categories.gradient,
        categories.bg_gradient,
        categories.value_proposition,
        categories.order_position,
        COUNT(items.id)::bigint as items_count,
        COALESCE(SUM(purchases.total_cost), 0)::bigint as total_revenue
    FROM strategic_categories categories
    LEFT JOIN strategic_items items ON categories.id = items.category_id AND items.active = true
    LEFT JOIN strategic_purchases purchases ON items.id = purchases.item_id AND purchases.status = 'completed'
    WHERE categories.company_id = v_company_id 
      AND categories.active = true
    GROUP BY categories.id, categories.name, categories.description, categories.icon, 
             categories.gradient, categories.bg_gradient, categories.value_proposition, 
             categories.order_position
    ORDER BY categories.order_position ASC NULLS LAST, categories.created_at ASC;
END;
$$;

-- Recriar função get_all_strategic_categories com aliases explícitos
CREATE OR REPLACE FUNCTION get_all_strategic_categories(p_company_id uuid DEFAULT NULL)
RETURNS TABLE(
    id uuid,
    name text,
    description text,
    icon text,
    gradient text,
    bg_gradient text,
    value_proposition text,
    active boolean,
    order_position integer,
    items_count bigint,
    total_revenue bigint,
    created_at timestamptz,
    updated_at timestamptz
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_company_id uuid;
BEGIN
    -- Se não foi passado company_id, usar do usuário atual
    IF p_company_id IS NULL THEN
        SELECT profiles.company_id INTO v_company_id
        FROM profiles WHERE profiles.id = auth.uid();
        
        IF v_company_id IS NULL THEN
            RAISE EXCEPTION 'Usuário não encontrado ou sem empresa associada';
        END IF;
    ELSE
        v_company_id := p_company_id;
    END IF;

    RETURN QUERY
    SELECT 
        categories.id,
        categories.name,
        categories.description,
        categories.icon,
        categories.gradient,
        categories.bg_gradient,
        categories.value_proposition,
        categories.active,
        categories.order_position,
        COUNT(items.id)::bigint as items_count,
        COALESCE(SUM(purchases.total_cost), 0)::bigint as total_revenue,
        categories.created_at,
        categories.updated_at
    FROM strategic_categories categories
    LEFT JOIN strategic_items items ON categories.id = items.category_id AND items.active = true
    LEFT JOIN strategic_purchases purchases ON items.id = purchases.item_id AND purchases.status = 'completed'
    WHERE categories.company_id = v_company_id 
    -- Não filtrar por active aqui - mostrar todas para admin
    GROUP BY categories.id, categories.name, categories.description, categories.icon, 
             categories.gradient, categories.bg_gradient, categories.value_proposition, 
             categories.active, categories.order_position, categories.created_at, 
             categories.updated_at
    ORDER BY categories.order_position ASC NULLS LAST, categories.created_at ASC;
END;
$$;

-- Conceder permissões
GRANT EXECUTE ON FUNCTION get_strategic_categories(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_strategic_categories(uuid) TO authenticated;