-- Adicionar photogallery_max à feature flag "conteudos"
-- Limites: Gr<PERSON><PERSON>=15, Pro=75, Max=Ilimitado
-- <AUTHOR> Internet 2025

-- Atualizar a feature flag "conteudos" para incluir photogallery_max
UPDATE public.feature_flags 
SET access_levels = jsonb_set(
  jsonb_set(
    jsonb_set(
      access_levels,
      '{Grátis,limits,photogallery_max}',
      '15'::jsonb
    ),
    '{Pro,limits,photogallery_max}',
    '75'::jsonb
  ),
  '{Max,limits,photogallery_max}',
  '-1'::jsonb
),
updated_at = NOW()
WHERE key = 'conteudos';

-- Atualizar descrição da feature flag para incluir galeria de fotos
UPDATE public.feature_flags 
SET description = 'Sistema flexível para controlar limites de criação de conteúdos organizacionais (unidades, departamentos, cargos, localidades, equipes) e galeria de fotos baseado no plano de assinatura',
    updated_at = NOW()
WHERE key = 'conteudos';

-- Verificação
DO $$
DECLARE
    plan_name TEXT;
    limits_info JSONB;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Feature Flag "conteudos" atualizada para incluir photogallery_max!';
    RAISE NOTICE '';
    RAISE NOTICE '📊 Limites de galeria de fotos por plano:';
    
    FOR plan_name, limits_info IN 
        SELECT jsonb_each.key, jsonb_each.value->'limits' 
        FROM public.feature_flags, 
             jsonb_each(access_levels) 
        WHERE feature_flags.key = 'conteudos'
    LOOP
        RAISE NOTICE '  %:', plan_name;
        RAISE NOTICE '    • Fotos por post: %', 
            CASE WHEN (limits_info->>'photogallery_max')::integer = -1 THEN 'Ilimitado' 
                 ELSE (limits_info->>'photogallery_max') 
            END;
    END LOOP;
    RAISE NOTICE '';
END;
$$;