/**
 * Atualizar políticas RLS da tabela post_shares para suportar páginas de conhecimento
 * <AUTHOR> Internet 2025
 */

-- Remover políticas RLS antigas
DROP POLICY IF EXISTS "Usuários podem criar compartilhamentos em posts da sua empresa" ON public.post_shares;
DROP POLICY IF EXISTS "Usuários podem ver compartilhamentos de posts da sua empresa" ON public.post_shares;
DROP POLICY IF EXISTS "Usuários podem deletar seus próprios compartilhamentos" ON public.post_shares;

-- Política para SELECT: Ver compartilhamentos da mesma empresa
CREATE POLICY "post_shares_select" ON public.post_shares
FOR SELECT USING (public.check_same_company(company_id));

-- Política para INSERT: Criar compartilhamentos para conteúdo da mesma empresa
CREATE POLICY "post_shares_insert" ON public.post_shares
FOR INSERT WITH CHECK (
  public.check_same_company(company_id) AND
  (
    -- Para posts: verificar se o post existe e pertence à mesma empresa
    (content_type = 'post' AND EXISTS (
      SELECT 1 FROM posts p, profiles pr 
      WHERE p.id = content_id 
      AND pr.id = auth.uid() 
      AND p.company_id = pr.company_id
    ))
    OR
    -- Para páginas de conhecimento: verificar se a página existe e pertence à mesma empresa
    -- (knowledge_pages -> knowledge_spaces -> company_id)
    (content_type = 'knowledge_page' AND EXISTS (
      SELECT 1 FROM knowledge_pages kp, knowledge_spaces ks, profiles pr 
      WHERE kp.id = content_id 
      AND kp.space_id = ks.id
      AND pr.id = auth.uid() 
      AND ks.company_id = pr.company_id
    ))
  )
);

-- Política para UPDATE: Atualizar apenas próprios compartilhamentos da mesma empresa
CREATE POLICY "post_shares_update" ON public.post_shares
FOR UPDATE USING (
  public.check_same_company(company_id) AND 
  user_id = auth.uid()
);

-- Política para DELETE: Deletar apenas próprios compartilhamentos
CREATE POLICY "post_shares_delete" ON public.post_shares
FOR DELETE USING (
  public.check_same_company(company_id) AND 
  user_id = auth.uid()
);

-- Comentário atualizado na tabela
COMMENT ON TABLE public.post_shares IS 'Tabela para rastrear compartilhamentos de posts e páginas de conhecimento com RLS multi-tenant';