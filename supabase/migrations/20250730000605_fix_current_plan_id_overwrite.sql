-- Criar função activate_plan_with_courtesy_v2 que NÃO sobrescreve current_plan_id do lead
-- O current_plan_id deve permanecer com o plano original para mostrar corretamente "<PERSON><PERSON><PERSON><PERSON> → Pro"
-- Migration: 20250730000605_create_activate_plan_with_courtesy_v2.sql
-- <AUTHOR> Internet 2025

CREATE OR REPLACE FUNCTION public.activate_plan_with_courtesy_v2(
    p_lead_id UUID,
    p_user_id UUID,
    p_selected_plan JSONB,
    p_selected_addons JSONB DEFAULT '[]'::jsonb
)
RETURNS TABLE(
    success BOOLEAN,
    activation_id UUID,
    courtesy_end_date TIMESTAMPTZ,
    previous_plan_id TEXT,
    error_code TEXT,
    error_message TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_company_id UUID;
    v_current_subscription_id UUID;
    v_previous_plan_id TEXT;
    v_new_plan_id TEXT;
    v_courtesy_start TIMESTAMPTZ;
    v_courtesy_end TIMESTAMPTZ;
    v_activation_id UUID;
BEGIN
    -- Obter company_id do usuário logado (padrão obrigatório do projeto)
    SELECT profiles.company_id INTO v_company_id
    FROM public.profiles 
    WHERE profiles.id = auth.uid();

    IF v_company_id IS NULL THEN
        RETURN QUERY SELECT false, NULL::UUID, NULL::TIMESTAMPTZ, NULL::TEXT, 'USER_NOT_FOUND', 'Usuário não encontrado ou sem empresa associada';
        RETURN;
    END IF;

    -- Gerar ID único para esta ativação
    v_activation_id := gen_random_uuid();
    
    -- Definir período de cortesia (7 dias)
    v_courtesy_start := now();
    v_courtesy_end := v_courtesy_start + INTERVAL '7 days';
    
    -- Extrair ID do novo plano
    v_new_plan_id := (p_selected_plan->>'id')::TEXT;
    
    IF v_new_plan_id IS NULL THEN
        RETURN QUERY SELECT false, NULL::UUID, NULL::TIMESTAMPTZ, NULL::TEXT, 'INVALID_PLAN', 'Plan ID not found in selected_plan JSON';
        RETURN;
    END IF;
    
    -- Buscar subscription atual
    SELECT id, price_id INTO v_current_subscription_id, v_previous_plan_id
    FROM public.subscriptions 
    WHERE company_id = v_company_id 
    AND status = 'active'
    LIMIT 1;
    
    -- Se não há subscription ativa, criar uma nova
    IF v_current_subscription_id IS NULL THEN
        INSERT INTO public.subscriptions (
            id,
            company_id,
            price_id,
            plan_id,
            user_limit,
            status,
            current_period_start,
            current_period_end,
            created_at,
            updated_at,
            metadata
        ) VALUES (
            gen_random_uuid(),
            v_company_id,
            v_new_plan_id,
            v_new_plan_id::UUID,
            (SELECT user_limit FROM subscription_plans WHERE id = v_new_plan_id::UUID),
            'active',
            v_courtesy_start,
            v_courtesy_end + INTERVAL '1 month',
            now(),
            now(),
            jsonb_build_object(
                'courtesy_period', true,
                'courtesy_start', v_courtesy_start,
                'courtesy_end', v_courtesy_end,
                'activation_id', v_activation_id,
                'lead_id', p_lead_id,
                'previous_plan', 'price_free_plan'
            )
        ) RETURNING id INTO v_current_subscription_id;
        
        v_previous_plan_id := 'price_free_plan';
    ELSE
        -- Atualizar subscription existente
        UPDATE public.subscriptions 
        SET 
            price_id = v_new_plan_id,
            plan_id = v_new_plan_id::UUID,
            user_limit = (SELECT user_limit FROM subscription_plans WHERE id = v_new_plan_id::UUID),
            current_period_start = v_courtesy_start,
            current_period_end = v_courtesy_end + INTERVAL '1 month',
            updated_at = now(),
            metadata = COALESCE(metadata, '{}'::jsonb) || jsonb_build_object(
                'courtesy_period', true,
                'courtesy_start', v_courtesy_start,
                'courtesy_end', v_courtesy_end,
                'activation_id', v_activation_id,
                'lead_id', p_lead_id,
                'previous_plan', v_previous_plan_id
            )
        WHERE id = v_current_subscription_id;
    END IF;
    
    -- ✅ CORREÇÃO: Atualizar o lead comercial SEM sobrescrever current_plan_id
    UPDATE public.commercial_leads 
    SET 
        -- ❌ REMOVIDO: current_plan_id = v_new_plan_id::UUID,  -- Não sobrescrever o plano original!
        courtesy_period_start = v_courtesy_start,
        courtesy_period_end = v_courtesy_end,
        plan_activated_at = v_courtesy_start,
        activation_status = 'activated',
        activated_at = v_courtesy_start,
        subscription_id = v_current_subscription_id,
        courtesy_granted = true,
        courtesy_reason = 'automatic_upgrade_request',
        updated_at = now()
    WHERE id = p_lead_id;
    
    -- Retornar sucesso
    RETURN QUERY SELECT 
        true,
        v_activation_id,
        v_courtesy_end,
        v_previous_plan_id,
        NULL::TEXT,
        NULL::TEXT;
    
EXCEPTION WHEN OTHERS THEN
    -- Em caso de erro, retornar detalhes
    RETURN QUERY SELECT 
        false,
        NULL::UUID,
        NULL::TIMESTAMPTZ,
        NULL::TEXT,
        SQLSTATE,
        SQLERRM;
END;
$$;

-- Comentário da versão v2
COMMENT ON FUNCTION public.activate_plan_with_courtesy_v2 IS 'v2 - CORRIGIDO: Não sobrescreve current_plan_id para preservar plano original no lead comercial';