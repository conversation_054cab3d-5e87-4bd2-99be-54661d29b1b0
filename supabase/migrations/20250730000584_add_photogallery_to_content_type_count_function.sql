-- Adici<PERSON>r suporte para 'photogallery' na função get_content_type_count
-- <AUTHOR> Internet 2025

-- Atualizar função para incluir photogallery
CREATE OR REPLACE FUNCTION public.get_content_type_count(tenant_id UUID, content_type TEXT)
RETURNS INTEGER AS $$
DECLARE
    content_count INTEGER := 0;
    query TEXT;
BEGIN
    -- Construir query dinamicamente baseado no tipo de conteúdo
    CASE content_type
        WHEN 'units' THEN 
            query := 'SELECT COUNT(*) FROM public.units WHERE company_id = $1';
        WHEN 'departments' THEN 
            query := 'SELECT COUNT(*) FROM public.departments WHERE company_id = $1';
        WHEN 'job_titles' THEN 
            query := 'SELECT COUNT(*) FROM public.job_titles WHERE company_id = $1';
        WHEN 'locations' THEN 
            query := 'SELECT COUNT(*) FROM public.locations WHERE company_id = $1';
        WHEN 'teams' THEN 
            query := 'SELECT COUNT(*) FROM public.teams WHERE company_id = $1';
        WHEN 'photogallery' THEN 
            -- Para photogallery, vamos retornar sempre 0 já que o limite é por post, não global
            -- A validação real será feita no frontend por número de fotos por post
            RETURN 0;
        ELSE
            RETURN 0;
    END CASE;
    
    -- Executar query com tratamento de erro
    BEGIN
        EXECUTE query INTO content_count USING tenant_id;
    EXCEPTION
        WHEN OTHERS THEN
            -- Se a tabela não existir ou houver erro, retornar 0
            RETURN 0;
    END;
    
    RETURN COALESCE(content_count, 0);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comentário atualizado
COMMENT ON FUNCTION public.get_content_type_count IS 'Função genérica para obter contagem atual de qualquer tipo de conteúdo (incluindo photogallery)';

-- Verificação
DO $$
BEGIN
    RAISE NOTICE '✅ Função get_content_type_count atualizada para incluir photogallery!';
    RAISE NOTICE '🔧 Adicionado suporte para content_type = ''photogallery''';
    RAISE NOTICE '📋 Função agora suporta: units, departments, job_titles, locations, teams, photogallery';
    RAISE NOTICE '💡 Photogallery retorna sempre 0 - validação é feita por número de fotos por post';
END $$;