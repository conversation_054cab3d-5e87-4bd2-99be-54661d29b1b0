-- Migration: <PERSON><PERSON><PERSON><PERSON> para permitir signup de novos usuários
-- Author: Vindula Internet 2025
-- Description: Modifica custom_access_token_hook para NÃO bloquear signup quando profile ainda não existe

-- ===================================================================
-- 🔧 CORREÇÃO DO CUSTOM ACCESS TOKEN HOOK
-- ===================================================================
-- PROBLEMA: Hook estava bloqueando signup porque exigia profile existente
-- SOLUÇÃO: Permitir signup quando profile não existe (usuário novo)

CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_active boolean;
    profile_record RECORD;
    claims jsonb;
    target_user_id uuid;
BEGIN
    -- Extrair user_id do evento
    target_user_id := (event->>'user_id')::uuid;
    
    -- Extrair claims do evento
    claims := event->'claims';
    
    -- Buscar profile do usuário
    SELECT active INTO profile_record
    FROM public.profiles 
    WHERE id = target_user_id;
    
    -- 🔧 CORREÇÃO: Se profile não existe, permitir (novo signup)
    -- Durante signup, o profile ainda não foi criado
    IF NOT FOUND THEN
        -- ✅ Permitir signup - adicionar claim indicando novo usuário
        claims := jsonb_set(
            claims,
            '{new_user_signup}',
            'true',
            true
        );
        
        -- Retornar evento permitindo o signup
        RETURN jsonb_set(event, '{claims}', claims);
    END IF;
    
    -- 🚨 Se profile existe e está inativo, BLOQUEAR login
    IF profile_record.active = false THEN
        RAISE EXCEPTION 'Account deactivated' 
        USING 
            ERRCODE = 'P0002',
            DETAIL = 'Your account has been deactivated by an administrator',
            HINT = 'Contact support to reactivate your account';
    END IF;
    
    -- ✅ Profile válido e ativo - permitir login
    -- Adicionar claim personalizada indicando que passou pela validação
    claims := jsonb_set(
        claims,
        '{profile_validated}',
        'true',
        true
    );
    
    -- Adicionar timestamp da validação para auditoria
    claims := jsonb_set(
        claims,
        '{validation_timestamp}',
        to_jsonb(extract(epoch from now())),
        true
    );
    
    -- Retornar evento com claims atualizadas
    RETURN jsonb_set(event, '{claims}', claims);
    
EXCEPTION 
    WHEN OTHERS THEN
        -- Log do erro para debugging (será visível nos logs do Supabase)
        RAISE LOG 'Custom Access Token Hook Error for user %: % (%)', 
            target_user_id, 
            SQLERRM, 
            SQLSTATE;
        
        -- Re-lançar o erro para bloquear o login
        RAISE;
END;
$$;

-- Comentário atualizado
COMMENT ON FUNCTION public.custom_access_token_hook(jsonb) IS 
'Hook de autenticação que valida se o profile do usuário está ativo antes de emitir o token. Permite signup de novos usuários e bloqueia login apenas de usuários inativos existentes.';