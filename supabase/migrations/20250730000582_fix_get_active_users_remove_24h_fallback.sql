/**
 * Fix get_active_users function - Remove fallback de 24 horas
 * 
 * PROBLEMA: Usuários inativos (Juliana, Talita) aparecem na lista de "usuários ativos"
 * com status "offline" (bolinha cinza) devido ao fallback de 24 horas.
 * 
 * SOLUÇÃO: Remover completamente o fallback de 24 horas e usar apenas INNER JOIN
 * para mostrar apenas usuários com sessão ativa nas últimas 2 horas.
 * 
 * BEFORE: Lista "usuários ativos" incluía quem fez login nas últimas 24h
 * AFTER: Lista "usuários ativos" mostra apenas quem tem atividade nas últimas 2h
 * 
 * <AUTHOR> Internet 2025
 */

CREATE OR REPLACE FUNCTION get_active_users(
    active_threshold_minutes INTEGER DEFAULT 15
)
RETURNS TABLE (
    id UUID,
    full_name TEXT,
    avatar_url TEXT,
    status TEXT,
    last_activity_minutes INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_company_id UUID;
BEGIN
    -- Obter company_id do usuário atual para multi-tenancy
    SELECT p.company_id INTO current_company_id
    FROM public.profiles p
    WHERE p.id = auth.uid();
    
    IF current_company_id IS NULL THEN
        RAISE EXCEPTION 'Usuário não está associado a nenhuma empresa';
    END IF;
    
    RETURN QUERY
    WITH recent_sessions AS (
        SELECT DISTINCT ON (us.user_id)
            us.user_id,
            us.last_activity_at,
            us.ended_at,
            EXTRACT(EPOCH FROM (NOW() - us.last_activity_at)) / 60 AS minutes_since_activity
        FROM public.user_sessions us
        WHERE 
            us.company_id = current_company_id
            AND us.last_activity_at >= NOW() - INTERVAL '2 hours' -- Apenas atividade nas últimas 2 horas
        ORDER BY us.user_id, us.last_activity_at DESC
    )
    SELECT 
        p.id,
        COALESCE(p.full_name, 'Usuário') AS full_name,
        p.avatar_url,
        CASE 
            WHEN rs.ended_at IS NULL AND rs.minutes_since_activity <= 1 THEN 'online'
            WHEN rs.ended_at IS NULL AND rs.minutes_since_activity <= active_threshold_minutes THEN 'away'
            ELSE 'offline'
        END AS status,
        COALESCE(FLOOR(rs.minutes_since_activity)::INTEGER, 999) AS last_activity_minutes
    FROM public.profiles p
    INNER JOIN recent_sessions rs ON rs.user_id = p.id  -- ✅ INNER JOIN: apenas usuários com sessão recente
    WHERE 
        p.company_id = current_company_id
        AND p.active = true
        -- ✅ REMOVIDO: fallback de 24 horas que causava usuários inativos na lista
    ORDER BY 
        CASE 
            WHEN rs.ended_at IS NULL AND rs.minutes_since_activity <= 1 THEN 1
            WHEN rs.ended_at IS NULL AND rs.minutes_since_activity <= active_threshold_minutes THEN 2
            ELSE 3
        END,
        rs.last_activity_at DESC NULLS LAST,
        p.full_name
    LIMIT 20; -- Limitar a 20 usuários mais ativos
END;
$$;

-- Comentários atualizados
COMMENT ON FUNCTION get_active_users(INTEGER) IS 'Retorna APENAS usuários realmente ativos da empresa (com sessão nas últimas 2h). Status: online (<= 1min), away (<= threshold), offline (> threshold mas < 2h).';

-- Grants (mantidos)
GRANT EXECUTE ON FUNCTION get_active_users(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_active_users(INTEGER) TO service_role;