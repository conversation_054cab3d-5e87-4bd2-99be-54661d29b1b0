-- Migration: Corri<PERSON><PERSON> violação crítica de segurança - remover company_id das funções marketplace
-- REGRA CRÍTICA: JAMAIS passar company_id como parâmetro - SEMPRE usar auth.uid() + profiles
-- <AUTHOR> Internet 2025

-- Dropar funções que violam a regra de segurança
DROP FUNCTION IF EXISTS get_strategic_categories(uuid);
DROP FUNCTION IF EXISTS get_all_strategic_categories(uuid);
DROP FUNCTION IF EXISTS validate_marketplace_category_creation(uuid);
DROP FUNCTION IF EXISTS validate_marketplace_item_creation(uuid, uuid);
DROP FUNCTION IF EXISTS validate_marketplace_offer_creation(uuid);

-- Recriar get_strategic_categories SEM parâmetro company_id
CREATE OR REPLACE FUNCTION get_strategic_categories()
RETURNS TABLE(
    id uuid,
    name text,
    description text,
    icon text,
    gradient text,
    bg_gradient text,
    value_proposition text,
    order_position integer,
    items_count bigint,
    total_revenue bigint
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_company_id uuid;
BEGIN
    -- SEMPRE usar auth.uid() + profiles para obter company_id
    SELECT profiles.company_id INTO v_company_id
    FROM profiles WHERE profiles.id = auth.uid();
    
    IF v_company_id IS NULL THEN
        RAISE EXCEPTION 'Usuário não encontrado ou sem empresa associada';
    END IF;

    RETURN QUERY
    SELECT 
        categories.id,
        categories.name,
        categories.description,
        categories.icon,
        categories.gradient,
        categories.bg_gradient,
        categories.value_proposition,
        categories.order_position,
        COUNT(items.id)::bigint as items_count,
        COALESCE(SUM(purchases.total_cost), 0)::bigint as total_revenue
    FROM strategic_categories categories
    LEFT JOIN strategic_items items ON categories.id = items.category_id AND items.active = true
    LEFT JOIN strategic_purchases purchases ON items.id = purchases.item_id AND purchases.status = 'completed'
    WHERE categories.company_id = v_company_id 
      AND categories.active = true
    GROUP BY categories.id, categories.name, categories.description, categories.icon, 
             categories.gradient, categories.bg_gradient, categories.value_proposition, 
             categories.order_position
    ORDER BY categories.order_position ASC NULLS LAST, categories.created_at ASC;
END;
$$;

-- Recriar get_all_strategic_categories SEM parâmetro company_id
CREATE OR REPLACE FUNCTION get_all_strategic_categories()
RETURNS TABLE(
    id uuid,
    name text,
    description text,
    icon text,
    gradient text,
    bg_gradient text,
    value_proposition text,
    active boolean,
    order_position integer,
    items_count bigint,
    total_revenue bigint,
    created_at timestamptz,
    updated_at timestamptz
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_company_id uuid;
BEGIN
    -- SEMPRE usar auth.uid() + profiles para obter company_id
    SELECT profiles.company_id INTO v_company_id
    FROM profiles WHERE profiles.id = auth.uid();
    
    IF v_company_id IS NULL THEN
        RAISE EXCEPTION 'Usuário não encontrado ou sem empresa associada';
    END IF;

    RETURN QUERY
    SELECT 
        categories.id,
        categories.name,
        categories.description,
        categories.icon,
        categories.gradient,
        categories.bg_gradient,
        categories.value_proposition,
        categories.active,
        categories.order_position,
        COUNT(items.id)::bigint as items_count,
        COALESCE(SUM(purchases.total_cost), 0)::bigint as total_revenue,
        categories.created_at,
        categories.updated_at
    FROM strategic_categories categories
    LEFT JOIN strategic_items items ON categories.id = items.category_id AND items.active = true
    LEFT JOIN strategic_purchases purchases ON items.id = purchases.item_id AND purchases.status = 'completed'
    WHERE categories.company_id = v_company_id 
    -- Não filtrar por active aqui - mostrar todas para admin
    GROUP BY categories.id, categories.name, categories.description, categories.icon, 
             categories.gradient, categories.bg_gradient, categories.value_proposition, 
             categories.active, categories.order_position, categories.created_at, 
             categories.updated_at
    ORDER BY categories.order_position ASC NULLS LAST, categories.created_at ASC;
END;
$$;

-- Recriar validate_marketplace_category_creation SEM parâmetro company_id
CREATE OR REPLACE FUNCTION validate_marketplace_category_creation()
RETURNS TABLE(
    can_create boolean,
    current_count integer,
    max_allowed integer,
    plan_name text
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_company_id uuid;
    v_plan_name text;
    v_current_count integer;
    v_max_allowed integer;
BEGIN
    -- SEMPRE usar auth.uid() + profiles para obter company_id
    SELECT profiles.company_id INTO v_company_id
    FROM profiles WHERE profiles.id = auth.uid();
    
    IF v_company_id IS NULL THEN
        RAISE EXCEPTION 'Usuário não encontrado ou sem empresa associada';
    END IF;

    -- Buscar informações do plano
    SELECT subscription_plan INTO v_plan_name
    FROM companies 
    WHERE id = v_company_id;

    -- Contar categorias atuais
    SELECT COUNT(*)::integer INTO v_current_count
    FROM strategic_categories
    WHERE company_id = v_company_id AND active = true;

    -- Definir limites por plano
    CASE v_plan_name
        WHEN 'gratis' THEN v_max_allowed := 6;
        WHEN 'pro' THEN v_max_allowed := 12;
        WHEN 'max' THEN v_max_allowed := -1; -- Ilimitado
        ELSE v_max_allowed := 6; -- Default para grátis
    END CASE;

    RETURN QUERY SELECT 
        (v_max_allowed = -1 OR v_current_count < v_max_allowed) as can_create,
        v_current_count,
        v_max_allowed,
        v_plan_name;
END;
$$;

-- Recriar validate_marketplace_item_creation SEM parâmetro company_id, apenas category_id
CREATE OR REPLACE FUNCTION validate_marketplace_item_creation(p_category_id uuid)
RETURNS TABLE(
    can_create boolean,
    current_count integer,
    max_allowed integer,
    plan_name text
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_company_id uuid;
    v_plan_name text;
    v_current_count integer;
    v_max_allowed integer;
BEGIN
    -- SEMPRE usar auth.uid() + profiles para obter company_id
    SELECT profiles.company_id INTO v_company_id
    FROM profiles WHERE profiles.id = auth.uid();
    
    IF v_company_id IS NULL THEN
        RAISE EXCEPTION 'Usuário não encontrado ou sem empresa associada';
    END IF;

    -- Verificar se a categoria pertence à empresa do usuário
    IF NOT EXISTS (
        SELECT 1 FROM strategic_categories 
        WHERE id = p_category_id AND company_id = v_company_id
    ) THEN
        RAISE EXCEPTION 'Categoria não encontrada ou não pertence à empresa';
    END IF;

    -- Buscar informações do plano
    SELECT subscription_plan INTO v_plan_name
    FROM companies 
    WHERE id = v_company_id;

    -- Contar itens atuais na categoria
    SELECT COUNT(*)::integer INTO v_current_count
    FROM strategic_items
    WHERE category_id = p_category_id AND active = true;

    -- Definir limites por plano
    CASE v_plan_name
        WHEN 'gratis' THEN v_max_allowed := 5;
        WHEN 'pro' THEN v_max_allowed := 10;
        WHEN 'max' THEN v_max_allowed := -1; -- Ilimitado
        ELSE v_max_allowed := 5; -- Default para grátis
    END CASE;

    RETURN QUERY SELECT 
        (v_max_allowed = -1 OR v_current_count < v_max_allowed) as can_create,
        v_current_count,
        v_max_allowed,
        v_plan_name;
END;
$$;

-- Recriar validate_marketplace_offer_creation SEM parâmetro company_id
CREATE OR REPLACE FUNCTION validate_marketplace_offer_creation()
RETURNS TABLE(
    can_create boolean,
    current_count integer,
    max_allowed integer,
    plan_name text
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_company_id uuid;
    v_plan_name text;
    v_current_count integer;
    v_max_allowed integer;
BEGIN
    -- SEMPRE usar auth.uid() + profiles para obter company_id
    SELECT profiles.company_id INTO v_company_id
    FROM profiles WHERE profiles.id = auth.uid();
    
    IF v_company_id IS NULL THEN
        RAISE EXCEPTION 'Usuário não encontrado ou sem empresa associada';
    END IF;

    -- Buscar informações do plano
    SELECT subscription_plan INTO v_plan_name
    FROM companies 
    WHERE id = v_company_id;

    -- Contar ofertas ativas atuais
    SELECT COUNT(*)::integer INTO v_current_count
    FROM special_offers
    WHERE company_id = v_company_id 
      AND active = true 
      AND NOW() BETWEEN start_date AND end_date;

    -- Definir limites por plano
    CASE v_plan_name
        WHEN 'gratis' THEN v_max_allowed := 1;
        WHEN 'pro' THEN v_max_allowed := 2;
        WHEN 'max' THEN v_max_allowed := -1; -- Ilimitado
        ELSE v_max_allowed := 1; -- Default para grátis
    END CASE;

    RETURN QUERY SELECT 
        (v_max_allowed = -1 OR v_current_count < v_max_allowed) as can_create,
        v_current_count,
        v_max_allowed,
        v_plan_name;
END;
$$;

-- Conceder permissões para as funções corrigidas
GRANT EXECUTE ON FUNCTION get_strategic_categories() TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_strategic_categories() TO authenticated;
GRANT EXECUTE ON FUNCTION validate_marketplace_category_creation() TO authenticated;
GRANT EXECUTE ON FUNCTION validate_marketplace_item_creation(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_marketplace_offer_creation() TO authenticated;