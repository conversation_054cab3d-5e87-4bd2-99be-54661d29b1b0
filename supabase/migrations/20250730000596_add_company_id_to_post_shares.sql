/**
 * Adicionar coluna company_id na tabela post_shares para suporte ao RLS multi-tenant
 * <AUTHOR> Internet 2025
 */

-- Adicionar coluna company_id (OBRIGATÓRIO para RLS)
ALTER TABLE public.post_shares 
ADD COLUMN IF NOT EXISTS company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE;

-- Preencher company_id com dados dos usuários existentes
UPDATE public.post_shares 
SET company_id = profiles.company_id
FROM public.profiles
WHERE post_shares.user_id = profiles.id 
AND post_shares.company_id IS NULL;

-- Tornar company_id obrigatório
ALTER TABLE public.post_shares 
ALTER COLUMN company_id SET NOT NULL;

-- <PERSON><PERSON><PERSON> índice para otimização
CREATE INDEX IF NOT EXISTS idx_post_shares_company_id ON public.post_shares(company_id);

-- Comentário na coluna
COMMENT ON COLUMN public.post_shares.company_id IS 'ID da empresa para controle de multi-tenancy e RLS';