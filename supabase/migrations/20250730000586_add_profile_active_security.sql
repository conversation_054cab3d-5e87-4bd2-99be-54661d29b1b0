-- Migration: Adicionar validação de perfil ativo para segurança
-- Author: Vindula Internet 2025
-- Description: Criar função para verificar se perfil está ativo e prevenir acesso de usuários inativos

-- Function para verificar se o profile do usuário atual está ativo
-- Esta function deve ser chamada em qualquer RPC que precisa garantir que apenas usuários ativos acessem o sistema
CREATE OR REPLACE FUNCTION public.check_profile_active()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_active boolean;
BEGIN
    -- Verificar se usuário está autenticado
    IF auth.uid() IS NULL THEN
        RETURN false;
    END IF;
    
    -- Buscar status ativo do profile
    SELECT active INTO user_active
    FROM public.profiles
    WHERE id = auth.uid();
    
    -- Se profile não existe ou está inativo, retornar false
    IF user_active IS NULL OR user_active = false THEN
        RETURN false;
    END IF;
    
    RETURN true;
END;
$$;

-- Comentário para documentar o uso
COMMENT ON FUNCTION public.check_profile_active() IS 'Verifica se o perfil do usuário autenticado está ativo. Retorna false se usuário não autenticado ou perfil inativo.';