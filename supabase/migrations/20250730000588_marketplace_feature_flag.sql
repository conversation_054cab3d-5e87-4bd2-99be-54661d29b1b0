-- Migration: Criar feature flag marketplace
-- <AUTHOR> Internet 2025

-- Inserir feature flag para marketplace com limitações por plano
INSERT INTO public.feature_flags (
  key, 
  name, 
  description, 
  default_enabled, 
  access_levels, 
  development_only
) VALUES (
  'marketplace',
  'Sistema de Marketplace',
  'Controla limitações de categorias, itens e ofertas especiais do marketplace por plano de assinatura',
  true,
  '{
    "Grátis": {
      "enabled": true,
      "limits": {
        "max_categories": 6,
        "max_items_per_category": 5,
        "max_special_offers": 1
      }
    },
    "Pro": {
      "enabled": true,
      "limits": {
        "max_categories": 12,
        "max_items_per_category": 10,
        "max_special_offers": 2
      }
    },
    "Max": {
      "enabled": true,
      "limits": {
        "max_categories": -1,
        "max_items_per_category": -1,
        "max_special_offers": -1
      }
    }
  }'::jsonb,
  false
) ON CONFLICT (key) DO UPDATE SET
  access_levels = EXCLUDED.access_levels,
  updated_at = now();

-- Adicionar validation functions para marketplace
CREATE OR REPLACE FUNCTION validate_marketplace_category_creation(tenant_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  feature_data JSONB;
  subscription_plan TEXT;
  max_categories INTEGER;
  current_categories INTEGER;
  can_create BOOLEAN;
  remaining_slots INTEGER;
  is_unlimited BOOLEAN;
BEGIN
  -- Buscar plano da empresa
  SELECT sp.name INTO subscription_plan
  FROM subscriptions s
  JOIN subscription_plans sp ON sp.id = s.plan_id
  WHERE s.company_id = tenant_id 
    AND s.status = 'active';
    
  IF subscription_plan IS NULL THEN
    subscription_plan := 'Grátis';
  END IF;
  
  -- Buscar dados da feature flag
  SELECT access_levels -> subscription_plan INTO feature_data
  FROM feature_flags 
  WHERE key = 'marketplace';
  
  -- Extrair limitações
  max_categories := COALESCE((feature_data -> 'limits' ->> 'max_categories')::INTEGER, 6);
  is_unlimited := max_categories = -1;
  
  -- Contar categorias atuais
  SELECT COUNT(*) INTO current_categories
  FROM strategic_categories
  WHERE company_id = tenant_id;
  
  -- Calcular se pode criar
  can_create := is_unlimited OR current_categories < max_categories;
  
  -- Calcular slots restantes
  IF is_unlimited THEN
    remaining_slots := NULL;
  ELSE
    remaining_slots := GREATEST(0, max_categories - current_categories);
  END IF;
  
  RETURN jsonb_build_object(
    'can_create', can_create,
    'current_count', current_categories,
    'limit', max_categories,
    'remaining_slots', remaining_slots,
    'is_unlimited', is_unlimited,
    'subscription_plan', subscription_plan
  );
END;
$$;

CREATE OR REPLACE FUNCTION validate_marketplace_item_creation(tenant_id UUID, category_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  feature_data JSONB;
  subscription_plan TEXT;
  max_items_per_category INTEGER;
  current_items INTEGER;
  can_create BOOLEAN;
  remaining_slots INTEGER;
  is_unlimited BOOLEAN;
BEGIN
  -- Buscar plano da empresa
  SELECT sp.name INTO subscription_plan
  FROM subscriptions s
  JOIN subscription_plans sp ON sp.id = s.plan_id
  WHERE s.company_id = tenant_id 
    AND s.status = 'active';
    
  IF subscription_plan IS NULL THEN
    subscription_plan := 'Grátis';
  END IF;
  
  -- Buscar dados da feature flag
  SELECT access_levels -> subscription_plan INTO feature_data
  FROM feature_flags 
  WHERE key = 'marketplace';
  
  -- Extrair limitações
  max_items_per_category := COALESCE((feature_data -> 'limits' ->> 'max_items_per_category')::INTEGER, 5);
  is_unlimited := max_items_per_category = -1;
  
  -- Contar itens atuais na categoria
  SELECT COUNT(*) INTO current_items
  FROM strategic_items si
  JOIN strategic_categories sc ON sc.id = si.category_id
  WHERE sc.company_id = tenant_id 
    AND si.category_id = category_id;
  
  -- Calcular se pode criar
  can_create := is_unlimited OR current_items < max_items_per_category;
  
  -- Calcular slots restantes
  IF is_unlimited THEN
    remaining_slots := NULL;
  ELSE
    remaining_slots := GREATEST(0, max_items_per_category - current_items);
  END IF;
  
  RETURN jsonb_build_object(
    'can_create', can_create,
    'current_count', current_items,
    'limit', max_items_per_category,
    'remaining_slots', remaining_slots,
    'is_unlimited', is_unlimited,
    'subscription_plan', subscription_plan
  );
END;
$$;

CREATE OR REPLACE FUNCTION validate_marketplace_offer_creation(tenant_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  feature_data JSONB;
  subscription_plan TEXT;
  max_special_offers INTEGER;
  current_offers INTEGER;
  can_create BOOLEAN;
  remaining_slots INTEGER;
  is_unlimited BOOLEAN;
BEGIN
  -- Buscar plano da empresa
  SELECT sp.name INTO subscription_plan
  FROM subscriptions s
  JOIN subscription_plans sp ON sp.id = s.plan_id
  WHERE s.company_id = tenant_id 
    AND s.status = 'active';
    
  IF subscription_plan IS NULL THEN
    subscription_plan := 'Grátis';
  END IF;
  
  -- Buscar dados da feature flag
  SELECT access_levels -> subscription_plan INTO feature_data
  FROM feature_flags 
  WHERE key = 'marketplace';
  
  -- Extrair limitações
  max_special_offers := COALESCE((feature_data -> 'limits' ->> 'max_special_offers')::INTEGER, 1);
  is_unlimited := max_special_offers = -1;
  
  -- Contar ofertas especiais atuais
  SELECT COUNT(*) INTO current_offers
  FROM special_offers
  WHERE company_id = tenant_id;
  
  -- Calcular se pode criar
  can_create := is_unlimited OR current_offers < max_special_offers;
  
  -- Calcular slots restantes
  IF is_unlimited THEN
    remaining_slots := NULL;
  ELSE
    remaining_slots := GREATEST(0, max_special_offers - current_offers);
  END IF;
  
  RETURN jsonb_build_object(
    'can_create', can_create,
    'current_count', current_offers,
    'limit', max_special_offers,
    'remaining_slots', remaining_slots,
    'is_unlimited', is_unlimited,
    'subscription_plan', subscription_plan
  );
END;
$$;

-- Conceder permissões
GRANT EXECUTE ON FUNCTION validate_marketplace_category_creation(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_marketplace_item_creation(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_marketplace_offer_creation(UUID) TO authenticated;