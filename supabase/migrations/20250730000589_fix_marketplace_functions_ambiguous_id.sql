-- Migration: Corrigir ambiguidade de referências "id" nas funções do marketplace
-- <AUTHOR> Internet 2025

-- Corrigir função get_strategic_categories - ambiguidade na linha 48 do COUNT(si.id)
CREATE OR REPLACE FUNCTION get_strategic_categories(p_company_id uuid DEFAULT NULL)
RETURNS TABLE(
    id uuid,
    name text,
    description text,
    icon text,
    gradient text,
    bg_gradient text,
    value_proposition text,
    order_position integer,
    items_count bigint,
    total_revenue bigint
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_company_id uuid;
BEGIN
    -- Se não foi passado company_id, usar do usuário atual
    IF p_company_id IS NULL THEN
        SELECT company_id INTO v_company_id
        FROM profiles WHERE id = auth.uid();
        
        IF v_company_id IS NULL THEN
            RAISE EXCEPTION 'Usuário não encontrado ou sem empresa associada';
        END IF;
    ELSE
        v_company_id := p_company_id;
    END IF;

    RETURN QUERY
    SELECT 
        sc.id,
        sc.name,
        sc.description,
        sc.icon,
        sc.gradient,
        sc.bg_gradient,
        sc.value_proposition,
        sc.order_position,
        COUNT(si.id) as items_count,
        COALESCE(SUM(sp.total_cost), 0) as total_revenue
    FROM strategic_categories sc
    LEFT JOIN strategic_items si ON sc.id = si.category_id AND si.active = true
    LEFT JOIN strategic_purchases sp ON si.id = sp.item_id AND sp.status = 'completed'
    WHERE sc.company_id = v_company_id 
      AND sc.active = true
    GROUP BY sc.id, sc.name, sc.description, sc.icon, sc.gradient, sc.bg_gradient, sc.value_proposition, sc.order_position
    ORDER BY sc.order_position ASC, sc.created_at ASC;
END;
$$;

-- Corrigir função get_all_strategic_categories - ambiguidade na linha 46 do COUNT(si.id)
CREATE OR REPLACE FUNCTION get_all_strategic_categories(p_company_id uuid DEFAULT NULL)
RETURNS TABLE(
    id uuid,
    name text,
    description text,
    icon text,
    gradient text,
    bg_gradient text,
    value_proposition text,
    active boolean,
    order_position integer,
    items_count bigint,
    total_revenue bigint,
    created_at timestamptz,
    updated_at timestamptz
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_company_id uuid;
BEGIN
    -- Se não foi passado company_id, usar do usuário atual
    IF p_company_id IS NULL THEN
        SELECT company_id INTO v_company_id
        FROM profiles WHERE id = auth.uid();
        
        IF v_company_id IS NULL THEN
            RAISE EXCEPTION 'Usuário não encontrado ou sem empresa associada';
        END IF;
    ELSE
        v_company_id := p_company_id;
    END IF;

    RETURN QUERY
    SELECT 
        sc.id,
        sc.name,
        sc.description,
        sc.icon,
        sc.gradient,
        sc.bg_gradient,
        sc.value_proposition,
        sc.active,
        sc.order_position,
        COUNT(si.id) as items_count,
        COALESCE(SUM(sp.total_cost), 0) as total_revenue,
        sc.created_at,
        sc.updated_at
    FROM strategic_categories sc
    LEFT JOIN strategic_items si ON sc.id = si.category_id AND si.active = true
    LEFT JOIN strategic_purchases sp ON si.id = sp.item_id AND sp.status = 'completed'
    WHERE sc.company_id = v_company_id 
    -- Não filtrar por active aqui - mostrar todas para admin
    GROUP BY sc.id, sc.name, sc.description, sc.icon, sc.gradient, sc.bg_gradient, sc.value_proposition, sc.active, sc.order_position, sc.created_at, sc.updated_at
    ORDER BY sc.order_position ASC, sc.created_at ASC;
END;
$$;

-- Conceder permissões
GRANT EXECUTE ON FUNCTION get_strategic_categories(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_strategic_categories(uuid) TO authenticated;