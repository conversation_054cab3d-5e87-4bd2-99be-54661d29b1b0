# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.bun

# Claude settings - allow hooks configuration
!.claude/settings.local.json
supabase/.temp/gotrue-version
supabase/.temp/pooler-url
supabase/.temp/postgres-version
supabase/.temp/project-ref
supabase/.temp/rest-version
supabase/.temp/storage-version
.venv/*

# Environment files
.env
.env.*
!.env.example
!.env.template
!.env.development
!.env.staging
!.env.production
# But always ignore local overrides
.env.local

# Cloudflare (para não commitar por acidente na raiz)
wrangler.toml
_redirects
.wrangler/

# Nunca ignorar 
!src/lib/logs/

# Added by <PERSON> Task Master
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.vscode
# OS specific
# Task files
tasks.json
.taskmaster/
# Sentry Config File
.env.sentry-build-plugin
schema_dumps/
.serena/cache/typescript/*.pkl
.crush/
