#!/usr/bin/env python3
"""
Vindula Cosmos Brain - MCP Server usando FastMCP
Sistema de receitas integrado com FastMCP API

<AUTHOR> Internet 2025
"""

import sys
import asyncio
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# Adicionar diretório raiz ao PYTHONPATH
sys.path.insert(0, str(Path(__file__).parent))

from mcp.server.fastmcp import FastMCP
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from dataclasses import dataclass

# Imports do nosso sistema de receitas
from app.recipes.registry import RecipeRegistry
from app.recipes.engine import RecipeEngine
from app.recipes.selector import RecipeSelector
from app.core.queue import task_queue
from app.core.query_logger import query_logger
from app.core.task_feedback import task_feedback_manager

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stderr)]
)

logger = logging.getLogger(__name__)

@dataclass
class VindulaBrainContext:
    """Contexto da aplicação para gerenciar recursos"""
    recipe_registry: RecipeRegistry
    recipe_engine: RecipeEngine
    task_queue: Any
    initialized: bool = False

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[VindulaBrainContext]:
    """Gerencia ciclo de vida da aplicação"""
    logger.info("🚀 Inicializando Vindula Cosmos Brain...")
    
    # Inicializar sistema de receitas
    try:
        # Inicializar registry com auto-descoberta
        RecipeRegistry.initialize()
        
        # Inicializar e iniciar task queue
        await task_queue.start()
        
        # Criar contexto
        context = VindulaBrainContext(
            recipe_registry=RecipeRegistry,
            recipe_engine=RecipeEngine,
            task_queue=task_queue,
            initialized=True
        )
        
        # Log das receitas descobertas
        recipes = RecipeRegistry.list_recipes()
        logger.info(f"✅ Sistema inicializado: {len(recipes)} receitas registradas")
        
        if recipes:
            for recipe_name in recipes:
                metadata = RecipeRegistry.get_metadata(recipe_name)
                async_flag = " (async)" if metadata and metadata.is_async else ""
                logger.info(f"  • {recipe_name}{async_flag}")
        
        yield context
        
    except Exception as e:
        logger.error(f"❌ Erro na inicialização: {str(e)}")
        raise
    finally:
        # Cleanup
        try:
            await task_queue.stop()
            logger.info("🧹 Cleanup concluído")
        except:
            pass

# Criar instância FastMCP
mcp = FastMCP("vindula-cosmos-brain", lifespan=app_lifespan)

@mcp.tool()
def vindula_health() -> str:
    """
    🏥 Health check completo do sistema MCP e receitas
    
    Verifica status de todos os componentes do Vindula Cosmos Brain
    """
    try:
        logger.info("vindula_health chamada via FastMCP")
        
        # Verificar componentes principais
        components_status = []
        
        # 1. Recipe Registry
        registry_stats = RecipeRegistry.stats()
        if registry_stats["initialized"] and registry_stats["total_recipes"] > 0:
            components_status.append("✅ **Recipe Registry**: OK")
        else:
            components_status.append("❌ **Recipe Registry**: Falhou")
        
        # 2. Task Queue
        if hasattr(task_queue, 'running') and task_queue.running:
            components_status.append("✅ **Task Queue**: OK")
        else:
            components_status.append("❌ **Task Queue**: Falhou")
        
        # 3. MCP Server
        components_status.append("✅ **MCP Server**: OK (FastMCP)")
        
        # 4. Dependencies check
        try:
            import pglast
            import sqlparse
            components_status.append("✅ **SQL Parsers**: OK (pglast + sqlparse)")
        except ImportError as e:
            components_status.append(f"❌ **SQL Parsers**: Falhou ({str(e)})")
        
        # Status geral
        failed_components = [status for status in components_status if status.startswith("❌")]
        overall_status = "🟢 **HEALTHY**" if not failed_components else "🔴 **UNHEALTHY**"
        
        # Montar resposta
        result = f"""{overall_status}

🔧 **Componentes**
{chr(10).join(components_status)}

📊 **Estatísticas**
• Receitas registradas: {registry_stats['total_recipes']}
• Receitas síncronas: {registry_stats['sync_recipes']}
• Receitas assíncronas: {registry_stats['async_recipes']}

🎯 **FastMCP**
• API: Versão 1.11.0
• Protocolo: stdio otimizado
• Status: ✅ Operacional

🧪 **Teste**
• MCP conectado: ✅
• Sistema pronto: {'✅' if not failed_components else '❌'}"""
        
        return result
        
    except Exception as e:
        error_msg = f"Erro ao verificar health: {str(e)}"
        logger.error(error_msg)
        return f"""🔴 **UNHEALTHY**

❌ **Erro Crítico**
{error_msg}

🔧 **Ações Recomendadas**
1. Verificar logs do sistema
2. Reiniciar MCP Server
3. Verificar dependências"""

@mcp.tool()
async def vindula_recipe(query: str, content: str = "") -> str:
    """
    🧠 Sistema inteligente de recipes Vindula Cosmos
    
    Executa receitas baseado em query de linguagem natural para análise SQL,
    validações, schemas, etc.
    
    Args:
        query: Pergunta ou comando (ex: 'validar esta function', 'próximo timestamp')
        content: SQL, código ou conteúdo para análise (opcional)
    """
    try:
        logger.info(f"vindula_recipe chamada: query='{query[:50]}...'")
        if content and content.strip():
            logger.info(f"Content recebido: {len(content)} caracteres")
            logger.info(f"Content preview: '{content[:100]}...'")
        else:
            logger.info("Nenhum content recebido ou content vazio")
        
        # 1. Seletor determina qual receita usar
        async def run_recipe():
            recipe_name = await RecipeSelector.select_recipe(query, content)
            
            if not recipe_name:
                return "❌ **Receita não encontrada1111**\n\nNão foi possível determinar qual receita usar para esta consulta."
            
            logger.info(f"Receita selecionada: {recipe_name}")
            
            # 2. Preparar dados de entrada
            input_data = {"query": query}
            if content and content.strip():
                input_data["content"] = content
                input_data["sql_code"] = content  # Para compatibilidade
                logger.info(f"Passando sql_code com {len(content)} caracteres para receita")
            
            # 3. Verificar se receita é assíncrona
            if RecipeRegistry.is_async(recipe_name):
                # Execução assíncrona
                task_id = await RecipeEngine.execute_async(recipe_name, input_data)
                return f"🔄 **Task Iniciada**\n\nTask ID: `{task_id}`\n\nUse `vindula_status('{task_id}')` para verificar o progresso."
            else:
                # Execução síncrona
                result = await RecipeEngine.execute(recipe_name, input_data)
                return format_recipe_result(result, recipe_name)
        
        # Executar diretamente (FastMCP suporta async tools)
        return await run_recipe()
        
    except Exception as e:
        error_msg = f"Erro ao executar vindula_recipe: {str(e)}"
        logger.error(error_msg)
        return f"❌ **Erro**\n\n{error_msg}"

@mcp.tool()
async def vindula_status(task_id: str) -> str:
    """
    📊 Verificar status de task assíncrona
    
    Args:
        task_id: ID da task a ser verificada
    """
    try:
        logger.info(f"vindula_status chamada: task_id='{task_id}'")
        
        async def get_status():
            task_result = await RecipeEngine.get_task_result(task_id)
            
            if not task_result:
                return f"❌ **Task não encontrada**\n\nTask ID: `{task_id}`"
            
            status = task_result["status"]
            
            if status == "pending":
                return f"⏳ **Task Pendente**\n\nTask ID: `{task_id}`\nStatus: Aguardando execução"
            elif status == "processing":
                return f"🔄 **Task em Processamento**\n\nTask ID: `{task_id}`\nStatus: Executando..."
            elif status == "completed":
                result_data = task_result["result"]
                recipe_name = result_data.get("metadata", {}).get("recipe_name", "unknown")
                
                # Criar objeto mockado para formatação
                from app.recipes.base import RecipeOutput
                recipe_output = RecipeOutput(
                    success=result_data["success"],
                    data=result_data["data"],
                    metadata=result_data["metadata"],
                    error=result_data.get("error"),
                    duration_ms=result_data.get("duration_ms")
                )
                
                return format_recipe_result(recipe_output, recipe_name)
            elif status == "failed":
                error = task_result.get("error", "Erro desconhecido")
                return f"❌ **Task Falhou**\n\nTask ID: `{task_id}`\nErro: {error}"
            else:
                return f"❓ **Status Desconhecido**\n\nTask ID: `{task_id}`\nStatus: {status}"
        
        # Executar diretamente (FastMCP suporta async tools)
        return await get_status()
            
    except Exception as e:
        error_msg = f"Erro ao verificar status: {str(e)}"
        logger.error(error_msg)
        return f"❌ **Erro**\n\n{error_msg}"

@mcp.tool()
async def vindula_analytics() -> str:
    """
    📈 Métricas e estatísticas do sistema de receitas
    """
    try:
        logger.info("vindula_analytics chamada")
        
        async def get_analytics():
            health = await RecipeEngine.health_check()
            
            registry = health["registry"]
            queue = health["queue"]
            
            return f"""📊 **Analytics - Vindula Recipe System**

🧠 **Registry**
• Total de receitas: {registry['total_recipes']}
• Receitas síncronas: {registry['sync_recipes']}
• Receitas assíncronas: {registry['async_recipes']}
• Status: {'✅ Inicializado' if registry['initialized'] else '❌ Não inicializado'}

⚡ **Queue System**
• Workers ativos: {queue['workers']}
• Tasks pendentes: {queue['pending']}
• Tasks processando: {queue['processing']}
• Tasks completadas: {queue['completed']}
• Tasks com falha: {queue['failed']}
• Status: {'✅ Rodando' if queue['running'] else '❌ Parado'}

💾 **Cache**
• Entradas no cache: {health['cache_entries']}

🚀 **FastMCP**
• Versão: 1.11.0
• Protocolo: stdio otimizado
• Performance: ✅ Excelente"""
        
        # Executar diretamente (FastMCP suporta async tools)
        return await get_analytics()
            
    except Exception as e:
        error_msg = f"Erro ao obter analytics: {str(e)}"
        logger.error(error_msg)
        return f"❌ **Erro**\n\n{error_msg}"

def format_recipe_result(result, recipe_name: str) -> str:
    """Formatar resultado de receita para exibição"""
    if result.success:
        duration = result.duration_ms or 0
        data = result.data
        
        # Se tem mensagem formatada, usar ela diretamente
        if "message" in data and data["message"]:
            return data["message"]
        
        # Fallback para formatação antiga
        formatted = f"✅ **{recipe_name}** ({duration}ms)\n\n"
        
        if "validation_passed" in data:
            status = "✅ Válido" if data["validation_passed"] else "❌ Inválido"
            formatted += f"**Status**: {status}\n\n"
            
            if "issues_found" in data and data["issues_found"]:
                formatted += f"**Issues encontradas**: {len(data['issues_found'])}\n"
                for issue in data["issues_found"][:5]:
                    formatted += f"• {issue}\n"
        
        elif "tables" in data:
            formatted += f"**Tabelas encontradas**: {len(data.get('tables', []))}\n"
            formatted += f"**Colunas analisadas**: {len(data.get('columns', []))}\n"
            formatted += f"**Operações detectadas**: {len(data.get('operations', []))}\n\n"
            
            if data.get('tables'):
                formatted += "**Tabelas**:\n"
                for table in data['tables'][:10]:
                    formatted += f"• {table}\n"
        
        return formatted
    else:
        return f"❌ **{recipe_name}** - Falhou\n\n**Erro**: {result.error}"

@mcp.tool()
def vindula_query_logs() -> str:
    """
    📋 Relatório de queries sem resposta e estatísticas
    
    Mostra queries que não conseguiram ser processadas pelo sistema
    """
    try:
        logger.info("vindula_query_logs chamada")
        
        # Obter estatísticas do dia
        stats = query_logger.get_stats_today()
        
        if stats.get("error"):
            return f"❌ **Erro ao obter logs**\n\n{stats['error']}"
        
        if stats["total_queries"] == 0:
            return "✅ **Excelente!**\n\n🎉 Nenhuma query sem resposta hoje.\nTodas as consultas foram processadas com sucesso!"
        
        # Obter queries recentes
        recent_queries = query_logger.get_recent_queries(10)
        
        # Montar relatório
        result = f"📋 **Relatório de Queries Sem Resposta**\n\n"
        result += f"📊 **Estatísticas Hoje**\n"
        result += f"• Total de queries sem resposta: {stats['total_queries']}\n"
        result += f"• Arquivo de log: `{stats.get('file_path', 'N/A')}`\n\n"
        
        if stats["error_types"]:
            result += f"🔍 **Tipos de Erro**\n"
            for error_type, count in sorted(stats["error_types"].items(), key=lambda x: x[1], reverse=True):
                result += f"• **{error_type}**: {count} ocorrências\n"
            result += "\n"
        
        if recent_queries:
            result += f"📝 **Últimas {len(recent_queries)} Queries Sem Resposta**\n"
            for i, entry in enumerate(recent_queries[-10:], 1):
                time = entry.get("timestamp", "")[-8:-3] if entry.get("timestamp") else "N/A"
                query = entry.get("query", "")[:50]
                error_type = entry.get("error_type", "unknown")
                result += f"{i}. **{time}** - `{error_type}` - \"{query}...\"\n"
            result += "\n"
        
        result += f"💡 **Análise**\n"
        if stats["total_queries"] > 10:
            result += "• ⚠️ Alto número de queries sem resposta - considere melhorar prompts do LLM\n"
        elif stats["total_queries"] > 5:
            result += "• 📈 Número moderado - monitorar padrões\n"
        else:
            result += "• ✅ Número baixo - sistema funcionando bem\n"
        
        result += f"• 🤖 LLM usado: Phi3:mini via Ollama\n"
        result += f"• 📅 Data: {stats.get('file_path', '').split('_')[-1].replace('.jsonl', '') if 'file_path' in stats else 'hoje'}"
        
        return result
        
    except Exception as e:
        error_msg = f"Erro ao obter logs de queries: {str(e)}"
        logger.error(error_msg)
        return f"❌ **Erro**\n\n{error_msg}"

@mcp.tool()
async def vindula_feedback(task_id: str, rating: int, comment: str = "") -> str:
    """
    💬 Avaliar qualidade de uma resposta MCP anterior
    
    Permite avaliar a qualidade de respostas usando o Task ID fornecido
    
    Args:
        task_id: ID da task a ser avaliada (ex: 'task_152030_a1b2c3d4')
        rating: Nota de 1-5 (1=Péssimo, 2=Ruim, 3=Regular, 4=Bom, 5=Excelente)
        comment: Comentário opcional sobre a resposta
    """
    try:
        logger.info(f"vindula_feedback chamada: task_id='{task_id}', rating={rating}")
        
        # Validar rating
        if not isinstance(rating, int) or not (1 <= rating <= 5):
            return f"❌ **Rating Inválido**\n\nO rating deve ser um número inteiro entre 1 e 5.\nRecebido: {rating}"
        
        # Verificar se task existe
        task_details = await task_feedback_manager.get_task_details(task_id)
        if not task_details:
            return f"❌ **Task ID Não Encontrada**\n\nTask ID: `{task_id}`\n\nVerifique se o ID está correto ou se a task foi executada recentemente."
        
        # Salvar feedback
        feedback_saved = await task_feedback_manager.save_feedback(
            task_id=task_id,
            rating=rating,
            comment=comment,
            user_agent="mcp_fastapi"
        )
        
        if not feedback_saved:
            return f"❌ **Erro ao Salvar Feedback**\n\nNão foi possível salvar o feedback para a task `{task_id}`."
        
        # Montar resposta com contexto da task
        rating_emoji = {1: "😞", 2: "😕", 3: "😐", 4: "😊", 5: "🤩"}
        rating_text = {1: "Péssimo", 2: "Ruim", 3: "Regular", 4: "Bom", 5: "Excelente"}
        
        response = f"✅ **Feedback Registrado**\n\n"
        response += f"📝 **Task ID**: `{task_id}`\n"
        response += f"⭐ **Avaliação**: {rating}/5 - {rating_text[rating]} {rating_emoji[rating]}\n"
        
        if comment:
            response += f"💬 **Comentário**: \"{comment}\"\n"
        
        response += f"\n🔍 **Contexto da Task**:\n"
        response += f"• **Query Original**: {task_details['query'][:100]}...\n"
        response += f"• **Receita Usada**: {task_details['recipe_used'] or 'N/A'}\n"
        response += f"• **Timestamp**: {task_details['timestamp'][:19]}\n"
        
        response += f"\n📊 **Obrigado pelo feedback!** Isso nos ajuda a melhorar o sistema."
        
        return response
        
    except Exception as e:
        error_msg = f"Erro ao processar feedback: {str(e)}"
        logger.error(error_msg)
        return f"❌ **Erro**\n\n{error_msg}"

if __name__ == "__main__":
    logger.info("🚀 Iniciando Vindula Cosmos Brain FastMCP Server...")
    try:
        mcp.run()
    except KeyboardInterrupt:
        logger.info("⏹️  MCP Server interrompido pelo usuário")
    except Exception as e:
        logger.error(f"💥 Erro fatal: {str(e)}")
        sys.exit(1)