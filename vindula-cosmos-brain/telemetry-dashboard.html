<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Vindula Cosmos Brain - Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .status-bar {
            background: rgba(255,255,255,0.95);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            margin-right: 20px;
            font-weight: 500;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-healthy { background: #10b981; }
        .status-warning { background: #f59e0b; }
        .status-error { background: #ef4444; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .card-title .icon {
            font-size: 1.5rem;
            margin-right: 10px;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            color: #6b7280;
            font-weight: 500;
        }

        .metric-value {
            font-weight: 700;
            font-size: 1.1rem;
            color: #1f2937;
        }

        .logs-container {
            background: rgba(255,255,255,0.95);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin-top: 20px;
        }

        .log-entry {
            padding: 12px;
            margin: 8px 0;
            border-left: 4px solid #e5e7eb;
            background: #f9fafb;
            border-radius: 6px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.9rem;
        }

        .log-entry.error { border-left-color: #ef4444; background: #fef2f2; }
        .log-entry.warning { border-left-color: #f59e0b; background: #fffbeb; }
        .log-entry.info { border-left-color: #3b82f6; background: #eff6ff; }

        .refresh-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 20px;
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }

        .loading {
            display: inline-flex;
            align-items: center;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f4f6;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .time-badge {
            background: #e5e7eb;
            color: #374151;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .error-type-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-left: 8px;
        }

        .error-type-llm { background: #fef2f2; color: #dc2626; }
        .error-type-recipe { background: #fff7ed; color: #ea580c; }
        .error-type-invalid { background: #f3f4f6; color: #374151; }

        .no-data {
            text-align: center;
            color: #6b7280;
            font-style: italic;
            padding: 40px;
        }

        .chart-container {
            height: 200px;
            position: relative;
            background: #f9fafb;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
        }

        .rating-stars {
            display: flex;
            gap: 4px;
        }

        .star {
            cursor: pointer;
            font-size: 1rem;
            opacity: 0.3;
            transition: all 0.2s ease;
            user-select: none;
        }

        .star:hover,
        .star.active {
            opacity: 1;
            transform: scale(1.1);
        }

        .star:hover ~ .star {
            opacity: 0.3;
        }

        .rating-stars:hover .star {
            opacity: 0.3;
        }

        .rating-stars:hover .star:hover,
        .rating-stars:hover .star:hover ~ .star {
            opacity: 1;
        }

        .tab-button {
            padding: 10px 20px;
            border: none;
            background: #f3f4f6;
            color: #6b7280;
            cursor: pointer;
            border-radius: 6px 6px 0 0;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .tab-button.active {
            background: white;
            color: #1f2937;
            border-bottom: 2px solid #667eea;
        }

        .tab-button:hover:not(.active) {
            background: #e5e7eb;
            color: #374151;
        }

        .query-entry {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            background: #f9fafb;
            border-left: 4px solid #e5e7eb;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.9rem;
        }

        .query-entry.success { 
            border-left-color: #10b981; 
            background: #f0fdf4; 
        }

        .query-entry.failure { 
            border-left-color: #ef4444; 
            background: #fef2f2; 
        }

        .query-meta {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
            font-size: 0.8rem;
        }

        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-failure {
            background: #fee2e2;
            color: #991b1b;
        }

        .llm-rating {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-top: 8px;
            padding: 8px;
            background: rgba(255,255,255,0.5);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 Vindula Cosmos Brain</h1>
            <p>Sistema de Monitoramento e Análise - MCP + LLM Dashboard</p>
        </div>

        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot status-healthy" id="mcp-status"></div>
                <span>MCP Server</span>
            </div>
            <div class="status-indicator">
                <div class="status-dot status-healthy" id="llm-status"></div>
                <span>LLM (Phi3)</span>
            </div>
            <div class="status-indicator">
                <div class="status-dot status-healthy" id="recipes-status"></div>
                <span>Recipes</span>
            </div>
            <div class="status-indicator">
                <div class="status-dot status-healthy" id="queue-status"></div>
                <span>Task Queue</span>
            </div>
        </div>

        <!-- Filtros de Período -->
        <div class="status-bar">
            <div style="display: flex; align-items: center; gap: 20px; flex-wrap: wrap;">
                <div style="font-weight: 600; color: #1f2937;">📅 Filtro por Período:</div>
                <select id="period-filter" onchange="onPeriodChange()" style="padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; background: white;">
                    <option value="today">Hoje</option>
                    <option value="yesterday">Ontem</option>
                    <option value="last-7-days">Últimos 7 dias</option>
                    <option value="last-30-days">Últimos 30 dias</option>
                    <option value="custom">Período personalizado</option>
                </select>
                <div id="custom-dates" style="display: none; gap: 10px;">
                    <input type="date" id="start-date" style="padding: 8px; border: 1px solid #d1d5db; border-radius: 4px;">
                    <span>até</span>
                    <input type="date" id="end-date" style="padding: 8px; border: 1px solid #d1d5db; border-radius: 4px;">
                    <button onclick="applyCustomPeriod()" style="padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;">Aplicar</button>
                </div>
            </div>
        </div>

        <button class="refresh-btn" onclick="refreshData()" id="refresh-btn">
            🔄 Atualizar Dados
        </button>

        <div class="grid">
            <div class="card">
                <div class="card-title">
                    <span class="icon">🧠</span>
                    Sistema
                </div>
                <div class="metric">
                    <span class="metric-label">Receitas Registradas</span>
                    <span class="metric-value" id="total-recipes">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Receitas Síncronas</span>
                    <span class="metric-value" id="sync-recipes">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Receitas Assíncronas</span>
                    <span class="metric-value" id="async-recipes">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Status Registry</span>
                    <span class="metric-value" id="registry-status">-</span>
                </div>
            </div>

            <div class="card">
                <div class="card-title">
                    <span class="icon">⚡</span>
                    Queue System
                </div>
                <div class="metric">
                    <span class="metric-label">Workers Ativos</span>
                    <span class="metric-value" id="active-workers">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Tasks Pendentes</span>
                    <span class="metric-value" id="pending-tasks">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Tasks Processando</span>
                    <span class="metric-value" id="processing-tasks">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Tasks Completadas</span>
                    <span class="metric-value" id="completed-tasks">-</span>
                </div>
            </div>

            <div class="card">
                <div class="card-title">
                    <span class="icon">📊</span>
                    <span id="queries-title">Todas as Queries (Hoje)</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Total de Queries</span>
                    <span class="metric-value" id="total-queries">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Queries com Sucesso</span>
                    <span class="metric-value" id="successful-queries" style="color: #10b981;">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Queries com Falha</span>
                    <span class="metric-value" id="failed-queries" style="color: #ef4444;">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Taxa de Sucesso</span>
                    <span class="metric-value" id="success-rate">-</span>
                </div>
            </div>

            <div class="card">
                <div class="card-title">
                    <span class="icon">🤖</span>
                    Auto-avaliação LLM
                </div>
                <div class="metric">
                    <span class="metric-label">Nota Média</span>
                    <span class="metric-value" id="llm-avg-rating">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Avaliações 5⭐</span>
                    <span class="metric-value" id="rating-5-count">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Avaliações 4⭐</span>
                    <span class="metric-value" id="rating-4-count">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Receita Mais Usada</span>
                    <span class="metric-value" id="most-used-recipe">-</span>
                </div>
            </div>

            <div class="card">
                <div class="card-title">
                    <span class="icon">💾</span>
                    Cache & Performance
                </div>
                <div class="metric">
                    <span class="metric-label">Entradas no Cache</span>
                    <span class="metric-value" id="cache-entries">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Última Atualização</span>
                    <span class="metric-value" id="last-update">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Status FastMCP</span>
                    <span class="metric-value">✅ 1.11.0</span>
                </div>
            </div>
        </div>

        <!-- Abas para separar queries com sucesso e falhas -->
        <div class="logs-container">
            <div class="card-title">
                <span class="icon">📝</span>
                Todas as Queries Recentes
            </div>
            
            <div style="margin-bottom: 20px;">
                <div style="display: flex; gap: 10px; border-bottom: 2px solid #e5e7eb;">
                    <button id="tab-all" class="tab-button active" onclick="switchTab('all')">
                        📊 Todas (20)
                    </button>
                    <button id="tab-success" class="tab-button" onclick="switchTab('success')">
                        ✅ Sucesso (10)
                    </button>
                    <button id="tab-failures" class="tab-button" onclick="switchTab('failures')">
                        ❌ Falhas (2)
                    </button>
                </div>
            </div>
            
            <div id="recent-all-queries">
                <div class="no-data">Carregando todas as queries...</div>
            </div>
            
            <div id="recent-successful-queries" style="display: none;">
                <div class="no-data">Carregando queries com sucesso...</div>
            </div>
            
            <div id="recent-failed-queries" style="display: none;">
                <div class="no-data">Carregando queries com falha...</div>
            </div>
        </div>
    </div>

    <script>
        let lastUpdate = null;
        let currentPeriod = 'today';
        let customStartDate = null;
        let customEndDate = null;

        // Buscar dados reais da API do servidor
        async function fetchSystemData() {
            try {
                console.log('📊 Buscando dados da API...');
                
                const periodParams = getPeriodParams();
                const apiUrl = periodParams ? `/api/telemetry?${periodParams}` : '/api/telemetry';
                
                const response = await fetch(apiUrl);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const apiData = await response.json();
                console.log('✅ Dados recebidos da API:', apiData);
                
                // Converter formato da API para formato esperado pelo dashboard
                const systemData = {
                    recipes: {
                        total: apiData.total_recipes || 0,
                        sync: apiData.sync_recipes || 0,
                        async: apiData.async_recipes || 0,
                        status: apiData.registry_initialized ? "✅ OK" : "❌ Erro"
                    },
                    queue: {
                        workers: apiData.active_workers || 0,
                        pending: apiData.pending_tasks || 0,
                        processing: apiData.processing_tasks || 0,
                        completed: apiData.completed_tasks || 0,
                        failed: apiData.failed_tasks || 0,
                        running: apiData.queue_running || false
                    },
                    queries: {
                        total: apiData.queries?.total || 0,
                        successful: apiData.queries?.successful || 0,
                        failed: apiData.queries?.failed || 0,
                        success_rate: apiData.queries?.success_rate || '0.0',
                        types: apiData.failed_queries_by_type || {}
                    },
                    cache: {
                        entries: apiData.cache_entries || 0
                    },
                    llm_ratings: apiData.llm_ratings || { avg: null, distribution: {} },
                    recipes_used: apiData.recipes_used || {},
                    recent_queries: apiData.recent_queries || [],
                    recent_successful_queries: apiData.recent_successful_queries || [],
                    recent_failed_queries: apiData.recent_failed_queries || [],
                    recentLogs: apiData.recent_failed_queries || [],
                    system_healthy: apiData.system_healthy,
                    last_update: apiData.last_update
                };

                return systemData;
                
            } catch (error) {
                console.error('❌ Erro ao buscar dados da API:', error);
                
                // Fallback para dados básicos em caso de erro
                return {
                    recipes: { total: 0, sync: 0, async: 0, status: "❌ API Error" },
                    queue: { workers: 0, pending: 0, processing: 0, completed: 0, failed: 0, running: false },
                    queries: { total: 0, types: {} },
                    cache: { entries: 0 },
                    recentLogs: [],
                    system_healthy: false,
                    api_error: error.message
                };
            }
        }

        function updateUI(data) {
            if (!data) {
                console.error('Dados não disponíveis');
                return;
            }

            // Atualizar título do período
            updatePeriodTitle();

            // Atualizar métricas do sistema
            document.getElementById('total-recipes').textContent = data.recipes.total;
            document.getElementById('sync-recipes').textContent = data.recipes.sync;
            document.getElementById('async-recipes').textContent = data.recipes.async;
            document.getElementById('registry-status').textContent = data.recipes.status;

            // Atualizar métricas da queue
            document.getElementById('active-workers').textContent = data.queue.workers;
            document.getElementById('pending-tasks').textContent = data.queue.pending;
            document.getElementById('processing-tasks').textContent = data.queue.processing;
            document.getElementById('completed-tasks').textContent = data.queue.completed;

            // Atualizar métricas das queries
            document.getElementById('total-queries').textContent = data.queries.total || 0;
            document.getElementById('successful-queries').textContent = data.queries.successful || 0;
            document.getElementById('failed-queries').textContent = data.queries.failed || 0;
            document.getElementById('success-rate').textContent = data.queries.success_rate ? `${data.queries.success_rate}%` : '0%';
            
            // Atualizar auto-avaliação LLM
            document.getElementById('llm-avg-rating').textContent = data.llm_ratings?.avg ? `${data.llm_ratings.avg}/5` : '-';
            document.getElementById('rating-5-count').textContent = data.llm_ratings?.distribution?.['5'] || 0;
            document.getElementById('rating-4-count').textContent = data.llm_ratings?.distribution?.['4'] || 0;
            
            // Receita mais usada
            const mostUsedRecipe = data.recipes_used ? Object.keys(data.recipes_used).reduce((a, b) => 
                data.recipes_used[a] > data.recipes_used[b] ? a : b, Object.keys(data.recipes_used)[0]) : null;
            document.getElementById('most-used-recipe').textContent = mostUsedRecipe ? 
                mostUsedRecipe.replace('_', ' ') : '-';

            // Atualizar cache
            document.getElementById('cache-entries').textContent = data.cache.entries;
            document.getElementById('last-update').textContent = new Date().toLocaleTimeString('pt-BR');

            // Atualizar status indicators
            updateStatusIndicators(data);

            // Atualizar logs recentes
            updateAllQueries(data.recent_queries || [], data.recent_successful_queries || [], data.recent_failed_queries || []);

            lastUpdate = new Date();
        }

        function updateStatusIndicators(data) {
            // MCP Status - sempre OK se chegou dados
            document.getElementById('mcp-status').className = 'status-dot status-healthy';

            // LLM Status - warning se muitos LLM failures
            const llmFailures = data.queries.types.LLM_FAILURE || 0;
            const llmStatus = llmFailures > 5 ? 'status-warning' : 'status-healthy';
            document.getElementById('llm-status').className = `status-dot ${llmStatus}`;

            // Recipes Status - warning se não inicializado
            const recipesStatus = data.recipes.status.includes('✅') ? 'status-healthy' : 'status-error';
            document.getElementById('recipes-status').className = `status-dot ${recipesStatus}`;

            // Queue Status
            const queueStatus = data.queue.running ? 'status-healthy' : 'status-error';
            document.getElementById('queue-status').className = `status-dot ${queueStatus}`;
        }

        let currentTab = 'all';
        let allQueriesData = { all: [], success: [], failures: [] };

        function switchTab(tabName) {
            // Atualizar botões das abas
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`tab-${tabName}`).classList.add('active');
            
            // Mostrar/esconder conteúdo
            document.getElementById('recent-all-queries').style.display = tabName === 'all' ? 'block' : 'none';
            document.getElementById('recent-successful-queries').style.display = tabName === 'success' ? 'block' : 'none';
            document.getElementById('recent-failed-queries').style.display = tabName === 'failures' ? 'block' : 'none';
            
            currentTab = tabName;
        }

        function updateAllQueries(allQueries, successQueries, failedQueries) {
            allQueriesData = { all: allQueries, success: successQueries, failures: failedQueries };
            
            // Atualizar contadores nas abas
            document.getElementById('tab-all').innerHTML = `📊 Todas (${allQueries.length})`;
            document.getElementById('tab-success').innerHTML = `✅ Sucesso (${successQueries.length})`;
            document.getElementById('tab-failures').innerHTML = `❌ Falhas (${failedQueries.length})`;
            
            // Renderizar conteúdo das abas
            updateTabContent('recent-all-queries', allQueries, true);
            updateTabContent('recent-successful-queries', successQueries, false);
            updateTabContent('recent-failed-queries', failedQueries, false);
        }

        function updateTabContent(containerId, queries, showMixed = true) {
            const container = document.getElementById(containerId);
            
            if (!queries || queries.length === 0) {
                container.innerHTML = '<div class="no-data">✅ Nenhuma query encontrada!</div>';
                return;
            }

            const queriesHtml = queries.map(query => {
                const time = new Date(query.timestamp).toLocaleTimeString('pt-BR');
                // Se não tem status, mas tem selected_recipe, é sucesso
                // Se tem status, usar o status
                const isSuccess = query.status ? (query.status === 'success') : !!query.selected_recipe;
                const statusClass = isSuccess ? 'success' : 'failure';
                const statusBadge = isSuccess ? 'status-success' : 'status-failure';
                const statusText = isSuccess ? 'SUCESSO' : (query.error_type || 'FALHA');
                
                return `
                    <div class="query-entry ${statusClass}">
                        <div class="query-meta">
                            <span class="time-badge">${time}</span>
                            <span class="status-badge ${statusBadge}">${statusText}</span>
                            ${query.query_id ? `<span style="color: #6b7280;">ID: ${query.query_id}</span>` : ''}
                            ${query.execution_time_ms ? `<span style="color: #6b7280;">${Math.round(query.execution_time_ms)}ms</span>` : ''}
                        </div>
                        
                        <div style="margin-bottom: 8px;">
                            <strong>Query:</strong> "${query.query}"
                        </div>
                        
                        ${isSuccess ? `
                            <div style="margin-bottom: 6px;">
                                <strong>Receita:</strong> ${query.selected_recipe || 'N/A'}
                            </div>
                            <div style="color: #059669; font-size: 0.85rem; margin-bottom: 8px;">
                                <strong>Resposta:</strong> ${(query.recipe_response || 'N/A').substring(0, 200)}${query.recipe_response && query.recipe_response.length > 200 ? '...' : ''}
                            </div>
                        ` : `
                            <div style="color: #dc2626; font-size: 0.85rem; margin-bottom: 8px;">
                                <strong>Erro:</strong> ${query.llm_response || 'N/A'}
                            </div>
                        `}
                        
                        ${query.llm_self_rating ? `
                            <div class="llm-rating">
                                <span style="font-size: 0.8rem; color: #6b7280;">🤖 Auto-avaliação LLM:</span>
                                <span style="font-weight: bold;">${query.llm_self_rating}/5 ⭐</span>
                                ${query.llm_self_feedback ? `<span style="font-style: italic; color: #6b7280;">- ${query.llm_self_feedback}</span>` : ''}
                            </div>
                        ` : ''}
                    </div>
                `;
            }).join('');

            container.innerHTML = queriesHtml;
        }

        function getErrorTypeClass(errorType) {
            switch (errorType) {
                case 'LLM_FAILURE': return 'error-type-llm';
                case 'RECIPE_NOT_FOUND': return 'error-type-recipe';
                case 'INVALID_RECIPE': return 'error-type-invalid';
                default: return 'error-type-invalid';
            }
        }

        function getLogLevel(errorType) {
            switch (errorType) {
                case 'LLM_FAILURE': return 'error';
                case 'RECIPE_NOT_FOUND': return 'warning';
                case 'INVALID_RECIPE': return 'info';
                default: return 'info';
            }
        }

        // Função para atualizar título do período
        function updatePeriodTitle() {
            const titleElement = document.getElementById('queries-title');
            
            switch (currentPeriod) {
                case 'today':
                    titleElement.textContent = 'Todas as Queries (Hoje)';
                    break;
                case 'yesterday':
                    titleElement.textContent = 'Todas as Queries (Ontem)';
                    break;
                case 'last-7-days':
                    titleElement.textContent = 'Todas as Queries (Últimos 7 dias)';
                    break;
                case 'last-30-days':
                    titleElement.textContent = 'Todas as Queries (Últimos 30 dias)';
                    break;
                case 'custom':
                    if (customStartDate && customEndDate) {
                        const start = new Date(customStartDate).toLocaleDateString('pt-BR');
                        const end = new Date(customEndDate).toLocaleDateString('pt-BR');
                        titleElement.textContent = `Todas as Queries (${start} - ${end})`;
                    } else {
                        titleElement.textContent = 'Todas as Queries (Período Personalizado)';
                    }
                    break;
                default:
                    titleElement.textContent = 'Todas as Queries';
            }
        }

        // Funções para filtro de período
        function onPeriodChange() {
            const select = document.getElementById('period-filter');
            const customDates = document.getElementById('custom-dates');
            
            currentPeriod = select.value;
            
            if (currentPeriod === 'custom') {
                customDates.style.display = 'flex';
                // Definir datas padrão
                const today = new Date();
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                
                document.getElementById('end-date').value = today.toISOString().split('T')[0];
                document.getElementById('start-date').value = yesterday.toISOString().split('T')[0];
            } else {
                customDates.style.display = 'none';
                refreshData(); // Auto-refresh quando muda período pré-definido
            }
        }

        function applyCustomPeriod() {
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            
            if (!startDate || !endDate) {
                alert('Por favor, selecione as datas de início e fim.');
                return;
            }
            
            if (new Date(startDate) > new Date(endDate)) {
                alert('A data de início deve ser anterior à data de fim.');
                return;
            }
            
            customStartDate = startDate;
            customEndDate = endDate;
            refreshData();
        }

        function getPeriodParams() {
            const params = new URLSearchParams();
            
            switch (currentPeriod) {
                case 'today':
                    params.append('period', 'today');
                    break;
                case 'yesterday':
                    params.append('period', 'yesterday');
                    break;
                case 'last-7-days':
                    params.append('period', 'last-7-days');
                    break;
                case 'last-30-days':
                    params.append('period', 'last-30-days');
                    break;
                case 'custom':
                    if (customStartDate && customEndDate) {
                        params.append('start_date', customStartDate);
                        params.append('end_date', customEndDate);
                    }
                    break;
            }
            
            return params.toString();
        }

        // Funções para avaliação de respostas
        function generateQueryId(log) {
            // Gerar ID único baseado no timestamp e query
            return btoa(log.timestamp + log.query).replace(/[^a-zA-Z0-9]/g, '').substr(0, 16);
        }

        async function rateResponse(queryId, rating) {
            try {
                // Highlight das estrelas selecionadas
                const stars = document.querySelectorAll(`[data-query-id="${queryId}"] .star`);
                stars.forEach((star, index) => {
                    if (index < rating) {
                        star.classList.add('active');
                    } else {
                        star.classList.remove('active');
                    }
                });

                // Simular chamada para API de feedback (substituir por chamada real quando implementado)
                const response = await fetch('/api/feedback', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query_id: queryId,
                        rating: rating,
                        comment: '' // Pode ser expandido para incluir comentários
                    })
                });

                const statusElement = document.getElementById(`rating-status-${queryId}`);
                if (response.ok) {
                    statusElement.textContent = '✅ Avaliado!';
                    setTimeout(() => {
                        statusElement.textContent = '';
                    }, 3000);
                } else {
                    statusElement.textContent = '❌ Erro';
                    statusElement.style.color = '#ef4444';
                }

            } catch (error) {
                console.error('Erro ao avaliar resposta:', error);
                const statusElement = document.getElementById(`rating-status-${queryId}`);
                statusElement.textContent = '❌ Erro de conexão';
                statusElement.style.color = '#ef4444';
            }
        }

        async function refreshData() {
            const btn = document.getElementById('refresh-btn');
            const originalContent = btn.innerHTML;
            
            // Mostrar loading
            btn.innerHTML = '<div class="spinner"></div>Atualizando...';
            btn.disabled = true;

            try {
                const data = await fetchSystemData();
                updateUI(data);
            } catch (error) {
                console.error('Erro na atualização:', error);
            } finally {
                // Restaurar botão
                btn.innerHTML = originalContent;
                btn.disabled = false;
            }
        }

        // Auto-refresh a cada 30 segundos
        setInterval(refreshData, 30000);

        // Carregamento inicial
        document.addEventListener('DOMContentLoaded', () => {
            refreshData();
        });

        // Indicador de conexão em tempo real
        window.addEventListener('online', () => {
            document.querySelector('.header p').textContent = 'Sistema de Monitoramento e Análise - MCP + LLM Dashboard';
        });

        window.addEventListener('offline', () => {
            document.querySelector('.header p').textContent = 'Sistema de Monitoramento e Análise - OFFLINE';
        });
    </script>
</body>
</html>