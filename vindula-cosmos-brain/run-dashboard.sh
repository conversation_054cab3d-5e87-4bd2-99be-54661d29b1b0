#!/bin/bash

# 🌐 VINDULA COSMOS BRAIN - DASHBOARD STARTUP SCRIPT
# 
# Script para inicializar o dashboard de telemetria do MCP System
# 
# <AUTHOR> Internet 2025

echo "🧠 Vindula Cosmos Brain - Dashboard Startup"
echo "========================================"
echo ""

# Verificar se estamos no diretório correto
if [ ! -f "serve-dashboard.cjs" ]; then
    echo "❌ Erro: Script deve ser executado no diretório vindula-cosmos-brain/"
    echo "   Caminho atual: $(pwd)"
    echo "   Execute: cd vindula-cosmos-brain && ./run-dashboard.sh"
    exit 1
fi

# Verificar se Node.js está instalado
if ! command -v node &> /dev/null; then
    echo "❌ Erro: Node.js não encontrado"
    echo "   Instale Node.js: https://nodejs.org/"
    exit 1
fi

# Verificar versão do Node.js
NODE_VERSION=$(node --version)
echo "✅ Node.js detectado: $NODE_VERSION"

# Criar diretório de logs se não existir
if [ ! -d "logs" ]; then
    echo "📁 Criando diretório de logs..."
    mkdir -p logs
fi

# Verificar arquivos necessários
echo "🔍 Verificando arquivos..."
if [ ! -f "telemetry-dashboard.html" ]; then
    echo "❌ Erro: telemetry-dashboard.html não encontrado"
    exit 1
fi

if [ ! -f "serve-dashboard.cjs" ]; then
    echo "❌ Erro: serve-dashboard.cjs não encontrado"
    exit 1
fi

echo "✅ Arquivos necessários encontrados"
echo ""

# Exibir informações do sistema
echo "📊 Informações do Sistema:"
echo "   • Diretório: $(pwd)"
echo "   • Logs: $(pwd)/logs"
echo "   • Porta: 3000"
echo ""

# Verificar se a porta está disponível
if command -v lsof &> /dev/null; then
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null; then
        echo "⚠️  Aviso: Porta 3000 já está em uso"
        echo "   Execute: lsof -ti:3000 | xargs kill -9"
        echo "   Ou altere a porta no serve-dashboard.cjs"
        echo ""
    fi
fi

echo "🚀 Iniciando Dashboard Server..."
echo "   💻 Acesse: http://localhost:3000/dashboard"
echo "   🔧 API: http://localhost:3000/api/telemetry"
echo "   ⏹️  Para parar: Ctrl+C"
echo ""
echo "📝 Log do servidor:"
echo "----------------------------------------"

# Tornar o arquivo executável e iniciar
chmod +x serve-dashboard.cjs
node serve-dashboard.cjs