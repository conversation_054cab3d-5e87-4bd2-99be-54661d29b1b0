"""
MCP Server - Servidor MCP integrado ao Vindula Cosmos Brain
Implementa protocol MCP com integração ao sistema de receitas

<AUTHOR> Internet 2025
"""

import asyncio
import logging
from typing import Any, Sequence

from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent

from .tools import MCPTools
from ..recipes.registry import RecipeRegistry
from ..recipes.engine import RecipeEngine
from ..core.queue import task_queue

logger = logging.getLogger(__name__)

# Criar instância do servidor MCP
app = Server("vindula-cosmos-brain")


@app.list_tools()
async def list_tools() -> list[Tool]:
    """
    Lista todas as tools disponíveis no MCP
    
    Returns:
        Lista de tools MCP
    """
    try:
        logger.info("MCP: Listando tools disponíveis")
        
        # Obter definições das tools
        tool_definitions = MCPTools.get_tool_definitions()
        
        # Converter para objetos Tool do MCP
        tools = []
        for tool_def in tool_definitions:
            tool = Tool(
                name=tool_def["name"],
                description=tool_def["description"],
                inputSchema=tool_def["inputSchema"]
            )
            tools.append(tool)
        
        logger.info(f"MCP: {len(tools)} tools disponíveis")
        return tools
        
    except Exception as e:
        logger.error(f"Erro ao listar tools MCP: {str(e)}")
        return []


@app.call_tool()
async def call_tool(name: str, arguments: dict) -> Sequence[TextContent]:
    """
    Executa uma tool MCP
    
    Args:
        name: Nome da tool
        arguments: Argumentos da tool
        
    Returns:
        Lista de conteúdos de texto com resultado
    """
    try:
        logger.info(f"MCP: Executando tool '{name}' com argumentos: {arguments}")
        
        result_text = ""
        
        if name == "vindula_recipe":
            # Tool principal - execução de receitas
            query = arguments.get("query", "")
            content = arguments.get("content")
            
            if not query:
                result_text = "❌ **Erro**: Parâmetro 'query' é obrigatório"
            else:
                result_text = await MCPTools.vindula_recipe(query, content)
        
        elif name == "vindula_status":
            # Tool de status de tasks
            task_id = arguments.get("task_id", "")
            
            if not task_id:
                result_text = "❌ **Erro**: Parâmetro 'task_id' é obrigatório"
            else:
                result_text = await MCPTools.vindula_status(task_id)
        
        elif name == "vindula_analytics":
            # Tool de analytics
            result_text = await MCPTools.vindula_analytics()
        
        elif name == "vindula_health":
            # Tool de health check
            result_text = await MCPTools.vindula_health()
        
        elif name == "vindula_feedback":
            # Tool de feedback
            task_id = arguments.get("task_id", "")
            rating = arguments.get("rating")
            comment = arguments.get("comment", "")
            
            if not task_id:
                result_text = "❌ **Erro**: Parâmetro 'task_id' é obrigatório"
            elif rating is None:
                result_text = "❌ **Erro**: Parâmetro 'rating' é obrigatório"
            else:
                result_text = await MCPTools.vindula_feedback(task_id, rating, comment)
        
        else:
            result_text = f"❌ **Tool não encontrada**: '{name}'"
            logger.error(f"Tool MCP não reconhecida: {name}")
        
        logger.info(f"MCP: Tool '{name}' executada com sucesso")
        
        return [TextContent(type="text", text=result_text)]
        
    except Exception as e:
        error_msg = f"❌ **Erro interno**: {str(e)}"
        logger.error(f"Erro ao executar tool MCP '{name}': {str(e)}")
        return [TextContent(type="text", text=error_msg)]


class MCPServerManager:
    """
    Gerenciador do servidor MCP integrado
    """
    
    def __init__(self):
        self.server_task = None
        self.running = False
    
    async def start(self):
        """
        Inicia o servidor MCP
        """
        if self.running:
            logger.warning("MCP Server já está rodando")
            return
        
        try:
            logger.info("Iniciando MCP Server...")
            
            # Inicializar sistema de receitas
            await self._initialize_recipe_system()
            
            # Iniciar servidor MCP via stdio com app global
            self.running = True
            logger.info("MCP Server iniciado com sucesso")
            
            # Usar stdio_server sem async context manager
            await app.run()
        
        except Exception as e:
            logger.error(f"Erro ao iniciar MCP Server: {str(e)}")
            raise
        finally:
            self.running = False
            logger.info("MCP Server finalizado")
    
    async def stop(self):
        """
        Para o servidor MCP
        """
        if not self.running:
            return
        
        logger.info("Parando MCP Server...")
        
        if self.server_task:
            self.server_task.cancel()
            try:
                await self.server_task
            except asyncio.CancelledError:
                pass
        
        # Parar sistema de queue
        await task_queue.stop()
        
        self.running = False
        logger.info("MCP Server parado")
    
    async def _initialize_recipe_system(self):
        """
        Inicializa o sistema de receitas
        """
        try:
            logger.info("Inicializando sistema de receitas...")
            
            # Inicializar registry com auto-descoberta
            RecipeRegistry.initialize()
            
            # Inicializar e iniciar task queue
            await task_queue.start()
            
            # Log das receitas descobertas
            recipes = RecipeRegistry.list_recipes()
            logger.info(f"Sistema de receitas inicializado: {len(recipes)} receitas")
            
            if recipes:
                for recipe_name in recipes:
                    metadata = RecipeRegistry.get_metadata(recipe_name)
                    async_flag = " (async)" if metadata and metadata.is_async else ""
                    logger.info(f"  • {recipe_name}{async_flag}")
            
        except Exception as e:
            logger.error(f"Erro ao inicializar sistema de receitas: {str(e)}")
            raise


# Instância global do gerenciador
mcp_manager = MCPServerManager()


async def start_mcp_server():
    """
    Função helper para iniciar o servidor MCP
    """
    try:
        logger.info("Iniciando MCP Server...")
        
        # Inicializar registry com auto-descoberta
        RecipeRegistry.initialize()
        
        # Inicializar e iniciar task queue
        await task_queue.start()
        
        # Log das receitas descobertas
        recipes = RecipeRegistry.list_recipes()
        logger.info(f"Sistema de receitas inicializado: {len(recipes)} receitas")
        
        if recipes:
            for recipe_name in recipes:
                metadata = RecipeRegistry.get_metadata(recipe_name)
                async_flag = " (async)" if metadata and metadata.is_async else ""
                logger.info(f"  • {recipe_name}{async_flag}")
        
        logger.info("MCP Server iniciado com sucesso")
        
        # Executar servidor MCP via stdio
        from mcp.server.stdio import stdio_server
        async with stdio_server() as (read_stream, write_stream):
            # Executar servidor com initialization_options vazio
            await app.run(read_stream, write_stream, {})
            
    except Exception as e:
        logger.error(f"Erro ao iniciar MCP Server: {str(e)}")
        raise
    finally:
        try:
            await task_queue.stop()
        except:
            pass
        logger.info("MCP Server finalizado")


async def stop_mcp_server():
    """
    Função helper para parar o servidor MCP
    """
    await mcp_manager.stop()


if __name__ == "__main__":
    """
    Execução standalone do MCP Server
    """
    import sys
    
    # Configurar logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stderr)  # MCP usa stderr para logs
        ]
    )
    
    # Rodar servidor
    try:
        asyncio.run(start_mcp_server())
    except KeyboardInterrupt:
        logger.info("MCP Server interrompido pelo usuário")
    except Exception as e:
        logger.error(f"Erro fatal no MCP Server: {str(e)}")
        sys.exit(1)