"""
MCP Tools - vindula_recipe e vindula_status
Ferramentas MCP para execução de receitas

<AUTHOR> Internet 2025
"""

from typing import Any, Dict, List
import logging
import time

from ..recipes.engine import RecipeEngine
from ..recipes.registry import RecipeRegistry
from ..recipes.selector import RecipeSelector

logger = logging.getLogger(__name__)


class MCPTools:
    """
    Classe que implementa as tools MCP para o sistema de receitas
    """
    
    @staticmethod
    async def vindula_recipe(query: str, content: str = None) -> str:
        """
        Tool principal: executa receitas baseado em query de linguagem natural
        
        Args:
            query: Pergunta ou comando em linguagem natural
            content: Conteúdo opcional (SQL, código, etc.) para análise
            
        Returns:
            Resultado formatado para o LLM
        """
        try:
            logger.info(f"vindula_recipe chamada: query='{query[:100]}...'")
            
            # 1. Seletor Phi-3 determina qual receita usar
            recipe_name = await RecipeSelector.select_recipe(query, content)
            
            if not recipe_name:
                return "❌ **Receita não encontrada**\n\nNão foi possível determinar qual receita usar para esta consulta."
            
            logger.info(f"Receita selecionada: {recipe_name}")
            
            # 2. Preparar dados de entrada
            input_data = {
                "query": query
            }
            
            if content:
                input_data["content"] = content
                input_data["sql_code"] = content  # Para compatibilidade com receitas SQL
            
            # 3. Verificar se receita é assíncrona
            if RecipeRegistry.is_async(recipe_name):
                # Execução assíncrona
                task_id = await RecipeEngine.execute_async(recipe_name, input_data)
                return f"🔄 **Task Iniciada**\n\nTask ID: `{task_id}`\n\nUse `vindula_status('{task_id}')` para verificar o progresso."
            
            else:
                # Execução síncrona (engine.py já gera task_id automaticamente)
                result = await RecipeEngine.execute(recipe_name, input_data, save_for_feedback=True)
                
                return MCPTools._format_recipe_result(result, recipe_name)
        
        except Exception as e:
            error_msg = f"Erro ao executar vindula_recipe: {str(e)}"
            logger.error(error_msg)
            return f"❌ **Erro**\n\n{error_msg}"
    
    @staticmethod
    async def vindula_status(task_id: str) -> str:
        """
        Tool de status: verifica status de task assíncrona
        
        Args:
            task_id: ID da task a ser verificada
            
        Returns:
            Status formatado da task
        """
        try:
            logger.info(f"vindula_status chamada: task_id='{task_id}'")
            
            # Obter resultado da task
            task_result = await RecipeEngine.get_task_result(task_id)
            
            if not task_result:
                return f"❌ **Task não encontrada**\n\nTask ID: `{task_id}`"
            
            status = task_result["status"]
            
            if status == "pending":
                return f"⏳ **Task Pendente**\n\nTask ID: `{task_id}`\nStatus: Aguardando execução"
            
            elif status == "processing":
                return f"🔄 **Task em Processamento**\n\nTask ID: `{task_id}`\nStatus: Executando..."
            
            elif status == "completed":
                # Task completada - formatar resultado
                result_data = task_result["result"]
                recipe_name = result_data.get("metadata", {}).get("recipe_name", "unknown")
                
                # Criar objeto RecipeOutput mockado para usar formatação existente
                from ..recipes.base import RecipeOutput
                recipe_output = RecipeOutput(
                    success=result_data["success"],
                    data=result_data["data"],
                    metadata=result_data["metadata"],
                    error=result_data.get("error"),
                    duration_ms=result_data.get("duration_ms")
                )
                
                return MCPTools._format_recipe_result(recipe_output, recipe_name)
            
            elif status == "failed":
                error = task_result.get("error", "Erro desconhecido")
                return f"❌ **Task Falhou**\n\nTask ID: `{task_id}`\nErro: {error}"
            
            elif status == "expired":
                return f"⏰ **Task Expirada**\n\nTask ID: `{task_id}`\nStatus: Resultado expirou"
            
            else:
                return f"❓ **Status Desconhecido**\n\nTask ID: `{task_id}`\nStatus: {status}"
        
        except Exception as e:
            error_msg = f"Erro ao verificar status: {str(e)}"
            logger.error(error_msg)
            return f"❌ **Erro**\n\n{error_msg}"
    
    @staticmethod
    async def vindula_analytics() -> str:
        """
        Tool de analytics: retorna métricas do sistema de receitas
        
        Returns:
            Métricas formatadas do sistema
        """
        try:
            logger.info("vindula_analytics chamada")
            
            # Obter health check do engine
            health = await RecipeEngine.health_check()
            
            # Formatar métricas
            registry = health["registry"]
            queue = health["queue"]
            
            result = f"""📊 **Analytics - Vindula Recipe System**

🧠 **Registry**
• Total de receitas: {registry['total_recipes']}
• Receitas síncronas: {registry['sync_recipes']}
• Receitas assíncronas: {registry['async_recipes']}
• Status: {'✅ Inicializado' if registry['initialized'] else '❌ Não inicializado'}

⚡ **Queue System**
• Queue size: {queue['queue_size']}
• Workers ativos: {queue['workers']}
• Tasks pendentes: {queue['pending']}
• Tasks processando: {queue['processing']}
• Tasks completadas: {queue['completed']}
• Tasks com falha: {queue['failed']}
• Status: {'✅ Rodando' if queue['running'] else '❌ Parado'}

💾 **Cache**
• Entradas no cache: {health['cache_entries']}

⏰ **Timestamp**
• {health['timestamp']}"""
            
            return result
        
        except Exception as e:
            error_msg = f"Erro ao obter analytics: {str(e)}"
            logger.error(error_msg)
            return f"❌ **Erro**\n\n{error_msg}"
    
    @staticmethod
    def _format_recipe_result(result, recipe_name: str) -> str:
        """
        Formata resultado de receita para exibição no MCP
        
        Args:
            result: RecipeOutput da execução
            recipe_name: Nome da receita executada
            
        Returns:
            String formatada para o LLM
        """
        if result.success:
            # Sucesso - formatar dados de saída
            duration = result.duration_ms or 0
            
            # Header
            formatted = f"✅ **{recipe_name}** ({duration}ms)\n\n"
            
            # Dados principais
            data = result.data
            
            # Formatação específica por tipo de dados
            if "validation_passed" in data:
                # Resultado de validação
                status = "✅ Válido" if data["validation_passed"] else "❌ Inválido"
                formatted += f"**Status**: {status}\n\n"
                
                if "issues_found" in data and data["issues_found"]:
                    formatted += f"**Issues encontradas**: {len(data['issues_found'])}\n"
                    for issue in data["issues_found"][:5]:  # Máximo 5 issues
                        formatted += f"• {issue}\n"
                    
                    if len(data["issues_found"]) > 5:
                        formatted += f"• ... e mais {len(data['issues_found']) - 5} issues\n"
            
            elif "tables" in data:
                # Resultado de análise SQL
                formatted += f"**Tabelas encontradas**: {len(data.get('tables', []))}\n"
                formatted += f"**Colunas analisadas**: {len(data.get('columns', []))}\n"
                formatted += f"**Operações detectadas**: {len(data.get('operations', []))}\n\n"
                
                if data.get('tables'):
                    formatted += "**Tabelas**:\n"
                    for table in data['tables'][:10]:  # Máximo 10 tabelas
                        formatted += f"• {table}\n"
            
            elif "final_result" in data:
                # Resultado de workflow
                workflow_data = data["final_result"]
                steps = data.get("workflow_results", [])
                
                formatted += f"**Workflow completado**: {len(steps)} passos\n\n"
                
                for step in steps:
                    status_icon = "✅" if step["success"] else "❌"
                    formatted += f"{status_icon} **{step['recipe_name']}** ({step['duration_ms']}ms)\n"
            
            elif "message" in data and len(data) <= 5:
                # Resultado com mensagem simples (ex: timestamp_calculator)
                formatted += data["message"]
            
            else:
                # Dados genéricos
                for key, value in data.items():
                    if isinstance(value, (str, int, float, bool)):
                        formatted += f"**{key}**: {value}\n"
                    elif isinstance(value, list):
                        formatted += f"**{key}**: {len(value)} items\n"
                    elif isinstance(value, dict):
                        formatted += f"**{key}**: {len(value)} propriedades\n"
            
            # Adicionar Task ID para feedback (sempre verificar)
            if result.metadata and "task_id" in result.metadata:
                task_id = result.metadata["task_id"]
                formatted += f"\n\n📋 **Task ID**: `{task_id}` (para feedback)\n"
                formatted += f"💬 **Como avaliar**: Use `vindula_feedback('{task_id}', rating, 'comentário')`"
            
            return formatted
        
        else:
            # Erro - formatar mensagem de erro
            return f"❌ **{recipe_name}** - Falhou\n\n**Erro**: {result.error}"
    
    @staticmethod
    async def vindula_health() -> str:
        """
        Tool de health check: verifica se o MCP e sistema de receitas estão funcionando
        
        Returns:
            Status detalhado do sistema
        """
        try:
            logger.info("vindula_health chamada")
            
            # Obter health check do engine
            health = await RecipeEngine.health_check()
            
            # Verificar componentes principais
            components_status = []
            
            # 1. Recipe Registry
            registry_stats = health["registry"]
            if registry_stats["initialized"] and registry_stats["total_recipes"] > 0:
                components_status.append("✅ **Recipe Registry**: OK")
            else:
                components_status.append("❌ **Recipe Registry**: Falhou")
            
            # 2. Task Queue
            queue_stats = health["queue"]
            if queue_stats["running"] and queue_stats["workers"] > 0:
                components_status.append("✅ **Task Queue**: OK")
            else:
                components_status.append("❌ **Task Queue**: Falhou")
            
            # 3. MCP Server
            components_status.append("✅ **MCP Server**: OK (respondendo)")
            
            # 4. Dependencies check
            try:
                import pglast
                import sqlparse
                components_status.append("✅ **SQL Parsers**: OK (pglast + sqlparse)")
            except ImportError as e:
                components_status.append(f"❌ **SQL Parsers**: Falhou ({str(e)})")
            
            # Status geral
            failed_components = [status for status in components_status if status.startswith("❌")]
            overall_status = "🟢 **HEALTHY**" if not failed_components else "🔴 **UNHEALTHY**"
            
            # Montar resposta
            result = f"""{overall_status}

🔧 **Componentes**
{chr(10).join(components_status)}

📊 **Estatísticas Detalhadas**

**Registry**
• Receitas registradas: {registry_stats['total_recipes']}
• Receitas síncronas: {registry_stats['sync_recipes']}
• Receitas assíncronas: {registry_stats['async_recipes']}
• Inicializado: {'Sim' if registry_stats['initialized'] else 'Não'}

**Task Queue**
• Status: {'🟢 Rodando' if queue_stats['running'] else '🔴 Parado'}
• Workers: {queue_stats['workers']}
• Queue size: {queue_stats['queue_size']}
• Tasks pendentes: {queue_stats['pending']}
• Tasks processando: {queue_stats['processing']}
• Tasks completadas: {queue_stats['completed']}
• Tasks com falha: {queue_stats['failed']}

💾 **Cache**
• Entradas: {health['cache_entries']}

⏰ **Timestamp**
• {health['timestamp']}

🧪 **Teste Rápido**
• MCP respondendo: ✅
• Tools carregadas: ✅ 
• Sistema pronto para uso: {'✅' if not failed_components else '❌'}"""
            
            return result
        
        except Exception as e:
            error_msg = f"Erro ao verificar health: {str(e)}"
            logger.error(error_msg)
            return f"""🔴 **UNHEALTHY**

❌ **Erro Crítico**
{error_msg}

🔧 **Ações Recomendadas**
1. Verificar logs do sistema
2. Reiniciar MCP Server
3. Verificar dependências (pglast, sqlparse)
4. Contatar suporte se problema persistir"""

    @staticmethod
    async def vindula_feedback(task_id: str, rating: int, comment: str = "") -> str:
        """
        Tool de feedback: permite avaliar qualidade de uma resposta MCP anterior
        
        Args:
            task_id: ID da task a ser avaliada
            rating: Nota de 1-5 (1=Péssimo, 2=Ruim, 3=Regular, 4=Bom, 5=Excelente)
            comment: Comentário opcional sobre a resposta
            
        Returns:
            Confirmação do feedback registrado
        """
        try:
            logger.info(f"vindula_feedback chamada: task_id='{task_id}', rating={rating}")
            
            # Validar rating
            if not (1 <= rating <= 5):
                return "❌ **Rating inválido**\n\nO rating deve ser entre 1 e 5."
            
            # Salvar feedback (aqui você pode implementar persistência se necessário)
            # Por enquanto, apenas log para telemetria
            feedback_data = {
                "task_id": task_id,
                "rating": rating,
                "comment": comment,
                "timestamp": int(time.time() * 1000)
            }
            
            # Log estruturado para telemetria
            logger.info(f"FEEDBACK_RECEIVED: {feedback_data}")
            
            # Determinar emoji baseado no rating
            rating_emojis = {
                1: "😞", 2: "😐", 3: "🙂", 4: "😊", 5: "🤩"
            }
            
            rating_text = {
                1: "Péssimo", 2: "Ruim", 3: "Regular", 4: "Bom", 5: "Excelente"
            }
            
            emoji = rating_emojis.get(rating, "❓")
            text = rating_text.get(rating, "Desconhecido")
            
            result = f"""✅ **Feedback Registrado** {emoji}

📋 **Task ID**: `{task_id}`
⭐ **Rating**: {rating}/5 ({text})"""
            
            if comment:
                result += f"\n💬 **Comentário**: {comment}"
            
            result += f"\n\n🙏 **Obrigado pelo feedback!** Isso nos ajuda a melhorar o sistema."
            
            return result
        
        except Exception as e:
            error_msg = f"Erro ao registrar feedback: {str(e)}"
            logger.error(error_msg)
            return f"❌ **Erro**\n\n{error_msg}"
    
    @staticmethod
    def get_tool_definitions() -> List[Dict[str, Any]]:
        """
        Retorna definições das tools MCP
        
        Returns:
            Lista com definições das tools
        """
        return [
            {
                "name": "vindula_health",
                "description": "🏥 Health check completo do sistema MCP e receitas",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "vindula_recipe",
                "description": "🧠 Sistema inteligente de recipes Vindula Cosmos - análise SQL, validações, schemas, etc.",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "Pergunta ou comando (ex: 'validar esta function', 'próximo timestamp')"
                        },
                        "content": {
                            "type": "string",
                            "description": "SQL, código ou conteúdo para análise (opcional)"
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "vindula_status",
                "description": "📊 Verificar status de task assíncrona",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "task_id": {
                            "type": "string",
                            "description": "ID da task a ser verificada"
                        }
                    },
                    "required": ["task_id"]
                }
            },
            {
                "name": "vindula_analytics",
                "description": "📈 Métricas e estatísticas do sistema de receitas",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "vindula_feedback",
                "description": "💬 Avaliar qualidade de uma resposta MCP anterior",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "task_id": {
                            "type": "string",
                            "description": "ID da task a ser avaliada (ex: 'task_152030_a1b2c3d4')"
                        },
                        "rating": {
                            "type": "integer",
                            "description": "Nota de 1-5 (1=Péssimo, 2=Ruim, 3=Regular, 4=Bom, 5=Excelente)",
                            "minimum": 1,
                            "maximum": 5
                        },
                        "comment": {
                            "type": "string",
                            "description": "Comentário opcional sobre a resposta",
                            "default": ""
                        }
                    },
                    "required": ["task_id", "rating"]
                }
            }
        ]