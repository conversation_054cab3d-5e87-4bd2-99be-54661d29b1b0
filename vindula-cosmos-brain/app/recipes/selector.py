"""
RecipeSelector - Seletor inteligente de receitas baseado em query
Por enquanto usa regras simples, futuramente integrará Phi-3/Llama3

<AUTHOR> Internet 2025
"""

import re
import json
from typing import Optional, Dict, List
import logging

import ollama
from .registry import RecipeRegistry
from ..core.query_logger import query_logger

logger = logging.getLogger(__name__)


class RecipeSelector:
    """
    Seletor inteligente que determina qual receita usar baseado na query
    
    Funcionalidades:
    - Análise de palavras-chave na query
    - Detecção de contexto (SQL, validação, etc.)
    - Sistema de scoring para escolher melhor receita
    - Preparado para integração com LLM (Phi-3/Llama3)
    """
    
    # SIMPLIFICAÇÃO: Removidas as estratégias de keywords e patterns
    # Agora apenas o LLM (Phi3) decide qual receita usar
    
    @classmethod
    async def select_recipe(cls, query: str, content: str = None) -> Optional[str]:
        """
        Seleciona a receita mais adequada usando APENAS LLM (Phi3)
        
        Args:
            query: Query em linguagem natural
            content: Conteúdo opcional (SQL, código, etc.)
            
        Returns:
            Nome da receita selecionada ou None se não encontrada
        """
        try:
            logger.info(f"🤖 Selecionando receita via LLM para query: '{query[:100]}...'")
            
            # ÚNICA ESTRATÉGIA: LLM decide tudo
            recipe_from_llm = await cls._select_by_llm(query, content)
            if recipe_from_llm:
                logger.info(f"✅ LLM selecionou receita: {recipe_from_llm}")
                return recipe_from_llm
            
            # Se LLM não conseguir decidir, logar e retornar None
            logger.warning(f"❌ LLM não conseguiu selecionar receita para query: '{query}'")
            query_logger.log_recipe_not_found(
                query=query,
                content=content or "",
                llm_response="LLM retornou None ou receita inválida",
                user_agent="mcp_fastapi"
            )
            return None
            
        except Exception as e:
            logger.error(f"❌ Erro ao selecionar receita via LLM: {str(e)}")
            query_logger.log_llm_failure(
                query=query,
                content=content or "",
                llm_error=str(e),
                user_agent="mcp_fastapi"
            )
            return None
    
    # Métodos de fallback removidos - apenas LLM decide
    
    @classmethod
    async def _select_by_llm(cls, query: str, content: str = None) -> Optional[str]:
        """
        Seleciona receita usando LLM local (Ollama) - VERSÃO LEGACY
        Esta função mantém compatibilidade com código existente
        
        Args:
            query: Query em linguagem natural
            content: Conteúdo opcional
            
        Returns:
            Nome da receita ou None
        """
        try:
            # Usar nova versão estruturada
            structured_response = await cls.analyze_and_structure_by_llm(query, content)
            
            if structured_response and structured_response.get("selected_recipe"):
                return structured_response["selected_recipe"]
            
            return None
                
        except Exception as e:
            logger.error(f"Erro ao consultar LLM: {str(e)}")
            return None
    
    @classmethod
    async def analyze_and_structure_by_llm(cls, query: str, content: str = None) -> Optional[Dict]:
        """
        Analisa query e estrutura resposta usando LLM local (Ollama)
        
        Args:
            query: Query em linguagem natural
            content: Conteúdo opcional
            
        Returns:
            Dict estruturado com receita selecionada e elementos identificados
        """
        try:
            # Obter receitas disponíveis
            available_recipes = RecipeRegistry.get_all_info()
            
            if not available_recipes:
                return None
            
            # Preparar informações das receitas para o prompt
            recipes_info = []
            for recipe_name, info in available_recipes.items():
                description = info.get("description", "Sem descrição")
                tags = info.get("tags", [])
                tags_str = f" [tags: {', '.join(tags)}]" if tags else ""
                recipes_info.append(f"- {recipe_name}: {description}{tags_str}")
            
            # Construir prompt otimizado para seleção simples
            prompt = f"""Você é um especialista que deve escolher a receita mais apropriada para a solicitação do usuário.

RECEITAS DISPONÍVEIS:
{chr(10).join(recipes_info)}

SOLICITAÇÃO:
Query: "{query}"
{f'Conteúdo: {content[:300]}...' if content else 'Sem conteúdo'}

REGRAS:
1. Analise a solicitação e escolha UMA receita
2. Se for pedido de ajuda/listagem → help_recipes
3. Se for validação de SQL → validar_estrutura_sql  
4. Se for análise de estrutura → estruturar_dados_sql
5. Se for verificação de schema → schema_inspector
6. Se for policies/RLS → policy_inspector
7. Se for timestamp → timestamp_calculator
8. Se não souber → NONE

FORMATO OBRIGATÓRIO - APENAS o nome da receita ou NONE:
[nome_da_receita]

Exemplos:
- "listar receitas" → help_recipes
- "validar esta function" → validar_estrutura_sql
- "verificar policies" → policy_inspector
- "próximo timestamp" → timestamp_calculator

SUA RESPOSTA (apenas o nome):"""

            # Chamar Ollama
            logger.info(f"🤖 Chamando Phi3 para seleção - Query: '{query[:50]}...'")
            
            response = ollama.generate(
                model='phi3:mini',
                prompt=prompt,
                options={
                    'temperature': 0.1,  # Baixa criatividade
                    'top_p': 0.9,
                    'num_predict': 50    # Resposta curta - só o nome
                }
            )
            
            raw_response = response['response'].strip()
            logger.info(f"🤖 Phi3 resposta: {raw_response}")
            
            # Parse simples - apenas extrair nome da receita
            selected_recipe = cls._extract_recipe_name(raw_response, available_recipes)
            
            if selected_recipe and selected_recipe != "NONE":
                logger.info(f"✅ Phi3 selecionou: {selected_recipe}")
                return {"selected_recipe": selected_recipe}
            else:
                logger.warning(f"❌ Phi3 não conseguiu selecionar receita válida")
                # Log detalhado da resposta do LLM
                query_logger.log_recipe_not_found(
                    query=query,
                    content=content or "",
                    llm_response=raw_response,
                    user_agent="mcp_fastapi"
                )
                return None
                
        except Exception as e:
            logger.error(f"Erro na análise estruturada do LLM: {str(e)}")
            return None
    
    @classmethod
    def _extract_recipe_name(cls, raw_response: str, available_recipes: Dict) -> Optional[str]:
        """
        Extrai nome da receita da resposta simples do LLM
        
        Args:
            raw_response: Resposta bruta do LLM
            available_recipes: Receitas disponíveis
            
        Returns:
            Nome da receita ou None
        """
        try:
            response_clean = raw_response.strip().lower()
            
            # Remover formatação comum
            response_clean = response_clean.replace("[", "").replace("]", "")
            response_clean = response_clean.replace("```", "").replace("`", "")
            response_clean = response_clean.split("\n")[0]  # Primeira linha apenas
            
            # Verificar se é NONE explícito
            if "none" in response_clean or "nenhuma" in response_clean:
                return None
            
            # Procurar nome exato da receita
            for recipe_name in available_recipes.keys():
                if recipe_name in response_clean:
                    return recipe_name
            
            # Procurar match parcial mais específico
            for recipe_name in available_recipes.keys():
                recipe_words = recipe_name.split("_")
                if all(word in response_clean for word in recipe_words):
                    return recipe_name
            
            return None
            
        except Exception as e:
            logger.error(f"Erro ao extrair nome da receita: {str(e)}")
            return None
    
    @classmethod
    async def select_recipe_with_analysis(cls, query: str, content: str = None) -> Optional[Dict]:
        """
        Versão pública que retorna análise estruturada completa
        
        Args:
            query: Query em linguagem natural
            content: Conteúdo opcional (SQL, código, etc.)
            
        Returns:
            Dict com análise estruturada ou None se não encontrada
        """
        return await cls.analyze_and_structure_by_llm(query, content)
    
    # Métodos de análise removidos - simplicidade primeiro
    
    # Sistema simplificado - apenas LLM + log de queries sem resposta