"""
RecipeEngine - Motor de execução de receitas e workflows

<AUTHOR> Internet 2025
"""

import asyncio
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
import logging

from .base import BaseRecipe, RecipeContext, RecipeOutput
from .registry import RecipeRegistry
from ..core.queue import AsyncTaskQueue, task_queue
from ..core.task_feedback import task_feedback_manager

logger = logging.getLogger(__name__)


class WorkflowStep:
    """
    Representa um passo de um workflow
    """
    
    def __init__(self, recipe_name: str, input_data: Dict[str, Any] = None, 
                 merge_strategy: str = "replace"):
        self.recipe_name = recipe_name
        self.input_data = input_data or {}
        self.merge_strategy = merge_strategy  # "replace", "merge", "append"
    
    def merge_data(self, current_data: Dict[str, Any], step_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge dados do resultado com dados atuais do workflow
        
        Args:
            current_data: Dados atuais do workflow
            step_result: Resultado da execução desta etapa
            
        Returns:
            Dados merged para próxima etapa
        """
        if self.merge_strategy == "replace":
            return step_result
        elif self.merge_strategy == "merge":
            merged = current_data.copy()
            merged.update(step_result)
            return merged
        elif self.merge_strategy == "append":
            if "results" not in current_data:
                current_data["results"] = []
            current_data["results"].append(step_result)
            return current_data
        else:
            return step_result


class RecipeEngine:
    """
    Motor principal para execução de receitas e workflows
    
    Funcionalidades:
    - Execução de receitas individuais
    - Execução de workflows compostos
    - Execução assíncrona para receitas longas
    - Cache de resultados
    - Error handling robusto
    """
    
    _cache: Dict[str, Any] = {}
    
    @classmethod
    async def execute(cls, recipe_name: str, input_data: Dict[str, Any], 
                     context: Optional[RecipeContext] = None,
                     save_for_feedback: bool = True) -> RecipeOutput:
        """
        Executa uma receita específica
        
        Args:
            recipe_name: Nome da receita a ser executada
            input_data: Dados de entrada para a receita
            context: Contexto do workflow (opcional)
            save_for_feedback: Se deve salvar para permitir feedback posterior
            
        Returns:
            RecipeOutput com resultado da execução
        """
        # Gerar Task ID único para feedback
        task_id = task_feedback_manager.generate_task_id() if save_for_feedback else None
        logger.info(f"🔍 Debug: save_for_feedback={save_for_feedback}, task_id={task_id}")
        start_time = datetime.utcnow()
        
        try:
            # Verificar se receita existe
            if not RecipeRegistry.is_registered(recipe_name):
                error_msg = f"Receita '{recipe_name}' não encontrada"
                logger.error(error_msg)
                return RecipeOutput(
                    success=False,
                    data={},
                    metadata={"recipe_name": recipe_name},
                    error=error_msg
                )
            
            # Obter instância da receita
            recipe_instance = RecipeRegistry.get_instance(recipe_name)
            if not recipe_instance:
                error_msg = f"Não foi possível criar instância da receita '{recipe_name}'"
                logger.error(error_msg)
                return RecipeOutput(
                    success=False,
                    data={},
                    metadata={"recipe_name": recipe_name},
                    error=error_msg
                )
            
            # Executar receita
            result = await recipe_instance.execute(input_data, context)
            
            # Calcular tempo total (incluindo overhead)
            total_duration = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            # Log do resultado
            if result.success:
                logger.info(f"Receita '{recipe_name}' executada com sucesso em {result.duration_ms}ms")
                
                # Salvar task response para feedback (apenas em caso de sucesso)
                logger.info(f"🔍 Debug: Resultado sucesso, save_for_feedback={save_for_feedback}, task_id={task_id}")
                if save_for_feedback and task_id:
                    logger.info(f"🔍 Debug: Salvando task response...")
                    try:
                        query_text = input_data.get('query', f"Execução da receita {recipe_name}")
                        response_text = str(result.data)[:2000]  # Limitar tamanho
                        
                        await task_feedback_manager.save_task_response(
                            task_id=task_id,
                            query=query_text,
                            response=response_text,
                            recipe_used=recipe_name,
                            execution_time_ms=total_duration,
                            user_agent="mcp-engine"
                        )
                        
                        # Adicionar task_id aos metadata do resultado
                        if not result.metadata:
                            result.metadata = {}
                        result.metadata['task_id'] = task_id
                        logger.info(f"🔍 Debug: task_id adicionado ao metadata: {task_id}")
                        logger.info(f"🔍 Debug: metadata completo: {result.metadata}")
                        
                        logger.info(f"📝 Task response salva: {task_id}")
                        
                    except Exception as e:
                        logger.warning(f"⚠️ Erro ao salvar task response: {str(e)}")
                        
            else:
                logger.error(f"Receita '{recipe_name}' falhou: {result.error}")
            
            return result
            
        except Exception as e:
            error_msg = f"Erro inesperado ao executar receita '{recipe_name}': {str(e)}"
            logger.error(error_msg)
            return RecipeOutput(
                success=False,
                data={},
                metadata={"recipe_name": recipe_name},
                error=error_msg
            )
    
    @classmethod
    async def execute_workflow(cls, steps: List[WorkflowStep], 
                              initial_data: Dict[str, Any]) -> RecipeOutput:
        """
        Executa um workflow completo com múltiplas receitas
        
        Args:
            steps: Lista de passos do workflow
            initial_data: Dados iniciais do workflow
            
        Returns:
            RecipeOutput com resultado final do workflow
        """
        start_time = datetime.utcnow()
        context = RecipeContext()
        current_data = initial_data.copy()
        workflow_results = []
        
        try:
            logger.info(f"Iniciando workflow com {len(steps)} passos")
            
            for i, step in enumerate(steps):
                logger.info(f"Executando passo {i+1}/{len(steps)}: {step.recipe_name}")
                
                # Preparar dados de entrada para este passo
                step_input = step.input_data.copy()
                step_input.update(current_data)
                
                # Executar receita
                step_result = await cls.execute(step.recipe_name, step_input, context)
                
                # Adicionar resultado ao contexto
                context.add_step_result(step.recipe_name, step_result.data)
                workflow_results.append({
                    "step": i + 1,
                    "recipe_name": step.recipe_name,
                    "success": step_result.success,
                    "duration_ms": step_result.duration_ms,
                    "error": step_result.error
                })
                
                # Se passo falhou, interromper workflow
                if not step_result.success:
                    error_msg = f"Workflow interrompido no passo {i+1} ({step.recipe_name}): {step_result.error}"
                    logger.error(error_msg)
                    
                    duration = (datetime.utcnow() - start_time).total_seconds() * 1000
                    return RecipeOutput(
                        success=False,
                        data={"workflow_results": workflow_results},
                        metadata={
                            "workflow_steps": len(steps),
                            "completed_steps": i + 1,
                            "failed_step": step.recipe_name
                        },
                        error=error_msg,
                        duration_ms=int(duration)
                    )
                
                # Merge dados para próximo passo
                current_data = step.merge_data(current_data, step_result.data)
            
            # Workflow completado com sucesso
            duration = (datetime.utcnow() - start_time).total_seconds() * 1000
            logger.info(f"Workflow completado com sucesso em {duration:.0f}ms")
            
            return RecipeOutput(
                success=True,
                data={
                    "final_result": current_data,
                    "workflow_results": workflow_results,
                    "context_data": context.get_all_results()
                },
                metadata={
                    "workflow_steps": len(steps),
                    "completed_steps": len(steps),
                    "total_duration_ms": int(duration)
                },
                duration_ms=int(duration)
            )
            
        except Exception as e:
            duration = (datetime.utcnow() - start_time).total_seconds() * 1000
            error_msg = f"Erro inesperado no workflow: {str(e)}"
            logger.error(error_msg)
            
            return RecipeOutput(
                success=False,
                data={"workflow_results": workflow_results},
                metadata={
                    "workflow_steps": len(steps),
                    "completed_steps": len(workflow_results)
                },
                error=error_msg,
                duration_ms=int(duration)
            )
    
    @classmethod
    async def execute_async(cls, recipe_name: str, input_data: Dict[str, Any]) -> str:
        """
        Executa uma receita assíncronamente usando task queue
        
        Args:
            recipe_name: Nome da receita a ser executada
            input_data: Dados de entrada para a receita
            
        Returns:
            Task ID para acompanhar execução
        """
        # Verificar se receita existe e é assíncrona
        if not RecipeRegistry.is_registered(recipe_name):
            raise ValueError(f"Receita '{recipe_name}' não encontrada")
        
        # Preparar dados da task
        task_data = {
            "recipe_name": recipe_name,
            "input_data": input_data,
            "context": None
        }
        
        # Adicionar à queue
        task_id = await task_queue.add_task("execute_recipe", task_data, priority=1)
        
        logger.info(f"Receita '{recipe_name}' adicionada à queue assíncrona: {task_id}")
        return task_id
    
    @classmethod
    async def get_task_result(cls, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Obtém resultado de uma task assíncrona
        
        Args:
            task_id: ID da task
            
        Returns:
            Resultado da task ou None se não encontrada
        """
        task_result = await task_queue.get_result(task_id)
        if task_result:
            return {
                "task_id": task_id,
                "status": task_result.status.value,
                "result": task_result.result,
                "error": task_result.error,
                "created_at": task_result.created_at.isoformat(),
                "completed_at": task_result.completed_at.isoformat() if task_result.completed_at else None
            }
        return None
    
    @classmethod
    def cache_result(cls, key: str, result: Any, ttl: int = 300) -> None:
        """
        Cache um resultado por TTL especificado
        
        Args:
            key: Chave do cache
            result: Resultado a ser cacheado
            ttl: Time to live em segundos
        """
        expiry = datetime.utcnow().timestamp() + ttl
        cls._cache[key] = {
            "result": result,
            "expiry": expiry
        }
    
    @classmethod
    def get_cached_result(cls, key: str) -> Optional[Any]:
        """
        Obtém resultado do cache se ainda válido
        
        Args:
            key: Chave do cache
            
        Returns:
            Resultado cacheado ou None se expirado/não encontrado
        """
        if key in cls._cache:
            cached = cls._cache[key]
            if datetime.utcnow().timestamp() < cached["expiry"]:
                return cached["result"]
            else:
                # Remove entrada expirada
                del cls._cache[key]
        return None
    
    @classmethod
    def clear_cache(cls) -> None:
        """Limpa todo o cache"""
        cls._cache.clear()
        logger.info("Cache do RecipeEngine limpo")
    
    @classmethod
    async def health_check(cls) -> Dict[str, Any]:
        """
        Verifica saúde do engine
        
        Returns:
            Dict com informações de saúde
        """
        registry_stats = RecipeRegistry.stats()
        queue_stats = await task_queue.get_queue_stats()
        
        return {
            "engine_status": "healthy",
            "registry": registry_stats,
            "queue": queue_stats,
            "cache_entries": len(cls._cache),
            "timestamp": datetime.utcnow().isoformat()
        }


# Handler para execução de receitas na queue
async def execute_recipe_handler(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handler para executar receitas na task queue
    
    Args:
        data: Dados da task contendo recipe_name, input_data e context
        
    Returns:
        Resultado da execução da receita
    """
    recipe_name = data["recipe_name"]
    input_data = data["input_data"]
    context_data = data.get("context")
    
    # Recriar contexto se fornecido
    context = None
    if context_data:
        context = RecipeContext()
        context.data = context_data.get("data", {})
        context.step_results = context_data.get("step_results", {})
        context.metadata = context_data.get("metadata", {})
    
    # Executar receita
    result = await RecipeEngine.execute(recipe_name, input_data, context)
    
    # Retornar resultado serializado
    return {
        "success": result.success,
        "data": result.data,
        "metadata": result.metadata,
        "error": result.error,
        "duration_ms": result.duration_ms
    }


# Registrar handler na queue
task_queue.register_handler("execute_recipe", execute_recipe_handler)