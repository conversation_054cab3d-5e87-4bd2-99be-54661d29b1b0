"""
Help Recipes - Receita para consultas de ajuda e documentação
Trata queries como "listar receitas", "ajuda", "o que você pode fazer", etc.

<AUTHOR> Internet 2025
"""

from typing import Dict, Any
import logging

from ..base import BaseRecipe, RecipeMetadata, RecipeContext
from ..registry import RecipeRegistry

logger = logging.getLogger(__name__)


class HelpRecipesRecipe(BaseRecipe):
    """
    Receita para consultas de ajuda e documentação
    
    Funcionalidades:
    - Listar todas as receitas disponíveis
    - Mostrar informações sobre receitas específicas
    - Fornecer ajuda e documentação
    - Responder queries gerais do sistema
    """
    
    def metadata(self) -> RecipeMetadata:
        return RecipeMetadata(
            name="help_recipes",
            description="Fornece ajuda e lista receitas disponíveis no sistema",
            version="1.0.0",
            tags=["help", "documentation", "recipes", "list", "ajuda"],
            is_async=False,
            expected_inputs=["query"],
            outputs=["formatted_help", "recipes_list"]
        )
    
    async def process(self, input_data: Dict[str, Any], context: RecipeContext) -> Dict[str, Any]:
        """
        Processa a receita de ajuda
        
        Args:
            input_data: Dados de entrada
            context: Contexto da receita
            
        Returns:
            Dict com resultado da execução
        """
        query = input_data.get("query", "").lower().strip()
        context.logger.info(f"Processando ajuda para query: '{query}'")
        
        # Determinar tipo de ajuda solicitada
        if any(keyword in query for keyword in ["listar", "list", "receitas", "recipes", "todas", "all"]):
            help_content = self._generate_recipes_list()
        elif any(keyword in query for keyword in ["o que", "what", "pode fazer", "can do", "capaz"]):
            help_content = self._generate_capabilities_overview()
        elif any(keyword in query for keyword in ["como", "how", "usar", "use"]):
            help_content = self._generate_usage_guide()
        else:
            help_content = self._generate_general_help()
        
        return {
            "message": help_content,
            "help_type": "general",
            "query_processed": query
        }
    
    def _generate_recipes_list(self) -> str:
        """
        Gera lista formatada de todas as receitas disponíveis
        
        Returns:
            String formatada com lista de receitas
        """
        all_recipes = RecipeRegistry.get_all_info()
        
        if not all_recipes:
            return "❌ **Nenhuma receita encontrada**\n\nO sistema não possui receitas registradas."
        
        content = f"📋 **Receitas Disponíveis ({len(all_recipes)})**\n\n"
        
        # Organizar por categoria baseada nas tags
        categories = {}
        
        for recipe_name, recipe_info in all_recipes.items():
            metadata = RecipeRegistry.get_metadata(recipe_name)
            tags = metadata.tags if metadata else ["geral"]
            
            # Usar primeira tag como categoria
            category = tags[0] if tags else "geral"
            
            if category not in categories:
                categories[category] = []
            
            # Formato: nome - descrição [async]
            async_marker = " 🔄" if metadata and metadata.is_async else ""
            description = recipe_info.get("description", "Sem descrição")
            
            categories[category].append(f"• **{recipe_name}**{async_marker}\n  {description}")
        
        # Gerar output por categoria
        for category, recipes in categories.items():
            content += f"## {category.title()}\n"
            content += "\n".join(recipes)
            content += "\n\n"
        
        content += "💡 **Como usar:**\n"
        content += "• Use `vindula_recipe('sua pergunta aqui')` para executar uma receita\n"
        content += "• O sistema selecionará automaticamente a receita mais adequada\n"
        content += "• Receitas com 🔄 são assíncronas - use `vindula_status(task_id)` para verificar progresso"
        
        return content
    
    def _generate_capabilities_overview(self) -> str:
        """
        Gera visão geral das capacidades do sistema
        
        Returns:
            String formatada com capacidades
        """
        all_recipes = RecipeRegistry.get_all_info()
        capabilities_count = len(all_recipes)
        
        content = f"🧠 **Vindula Cosmos Brain - Capacidades**\n\n"
        content += f"Sou um sistema inteligente com **{capabilities_count} receitas especializadas** para:\n\n"
        
        # Agrupar capacidades por tipo
        capabilities = {
            "🔍 **Análise SQL**": [
                "Estruturar e parsear código SQL",
                "Validar estrutura de funções e procedures",
                "Extrair tabelas, colunas e operações",
                "Analisar queries complexas"
            ],
            "🛡️ **Segurança & Validação**": [
                "Verificar políticas RLS (Row Level Security)",
                "Validar company_id em queries multi-tenant",
                "Inspecionar schemas de banco de dados",
                "Detectar vulnerabilidades em SQL"
            ],
            "⚙️ **Utilitários**": [
                "Calcular timestamps para migrações",
                "Gerar documentação automatizada",
                "Análise de performance",
                "Sistema de ajuda e documentação"
            ],
            "🤖 **IA Integrada**": [
                "Seleção inteligente de receitas via LLM",
                "Análise de linguagem natural",
                "Processamento assíncrono de tasks",
                "Sistema de queue para tarefas pesadas"
            ]
        }
        
        for category, items in capabilities.items():
            content += f"{category}\n"
            for item in items:
                content += f"• {item}\n"
            content += "\n"
        
        content += "💬 **Exemplos de uso:**\n"
        content += "• `vindula_recipe('validar esta function', sql_code)`\n"
        content += "• `vindula_recipe('próximo timestamp')`\n"
        content += "• `vindula_recipe('listar policies da tabela users')`\n"
        content += "• `vindula_recipe('verificar company_id neste SQL', query)`"
        
        return content
    
    def _generate_usage_guide(self) -> str:
        """
        Gera guia de uso do sistema
        
        Returns:
            String formatada com guia de uso
        """
        content = "📖 **Como usar o Vindula Cosmos Brain**\n\n"
        
        content += "## 🚀 Comandos Principais\n\n"
        content += "**`vindula_recipe(query, content='')`**\n"
        content += "• Execute receitas com linguagem natural\n"
        content += "• `query`: Sua pergunta ou comando\n"
        content += "• `content`: SQL ou código opcional para análise\n\n"
        
        content += "**`vindula_health()`**\n"
        content += "• Verifica status de todos os componentes\n"
        content += "• Mostra estatísticas do sistema\n\n"
        
        content += "**`vindula_status(task_id)`**\n"
        content += "• Verifica progresso de tasks assíncronas\n"
        content += "• Use o task_id retornado por operações async\n\n"
        
        content += "**`vindula_analytics()`**\n"
        content += "• Métricas detalhadas do sistema\n"
        content += "• Performance e estatísticas de uso\n\n"
        
        content += "## 💡 Dicas de Uso\n\n"
        content += "• **Seja específico**: \"validar esta function\" funciona melhor que \"validar\"\n"
        content += "• **Use linguagem natural**: O sistema entende português e inglês\n"
        content += "• **Forneça contexto**: Inclua o código SQL quando relevante\n"
        content += "• **Consulte a ajuda**: Use \"listar receitas\" para ver opções\n\n"
        
        content += "## 🔄 Receitas Assíncronas\n\n"
        content += "Algumas operações pesadas retornam um `task_id`:\n"
        content += "1. Execute: `result = vindula_recipe('sua query')`\n"
        content += "2. Se retornar task_id, verifique: `vindula_status(task_id)`\n"
        content += "3. Status possíveis: pending, processing, completed, failed\n\n"
        
        content += "## 🆘 Suporte\n\n"
        content += "• Use `vindula_recipe('ajuda')` para esta ajuda\n"
        content += "• Use `vindula_recipe('listar receitas')` para ver opções\n"
        content += "• Use `vindula_health()` se algo não funcionar"
        
        return content
    
    def _generate_general_help(self) -> str:
        """
        Gera ajuda geral do sistema
        
        Returns:
            String formatada com ajuda geral
        """
        content = "🤖 **Vindula Cosmos Brain - Assistente IA**\n\n"
        content += "Olá! Sou um sistema inteligente especializado em análise SQL e operações de banco de dados.\n\n"
        
        content += "🎯 **Como posso ajudar:**\n"
        content += "• Validar estruturas SQL e funções\n"
        content += "• Analisar segurança multi-tenant\n"
        content += "• Inspecionar schemas e policies\n"
        content += "• Calcular timestamps para migrações\n"
        content += "• E muito mais!\n\n"
        
        content += "💬 **Comandos úteis:**\n"
        content += "• `vindula_recipe('listar receitas')` - Ver todas as opções\n"
        content += "• `vindula_recipe('o que você pode fazer')` - Capacidades\n"
        content += "• `vindula_recipe('como usar')` - Guia detalhado\n"
        content += "• `vindula_health()` - Status do sistema\n\n"
        
        content += "🚀 **Exemplo rápido:**\n"
        content += "```\n"
        content += "vindula_recipe('validar esta function', '''\n"
        content += "CREATE FUNCTION exemplo()\n"
        content += "RETURNS TEXT AS $$\n"
        content += "BEGIN\n"
        content += "  RETURN 'Hello World';\n"
        content += "END;\n"
        content += "$$ LANGUAGE plpgsql;\n"
        content += "''')\n"
        content += "```\n\n"
        
        content += "Experimente fazer uma pergunta!"
        
        return content