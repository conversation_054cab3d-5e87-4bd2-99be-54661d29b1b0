"""
Query Logger - Sistema de log para queries sem resposta
Salva todas as consultas que não conseguiram ser processadas

<AUTHOR> Internet 2025
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class QueryLogEntry:
    """Entrada de log para query sem resposta"""
    timestamp: str
    query: str
    content: str
    llm_response: str
    selected_recipe: Optional[str]
    error_type: str
    user_agent: str
    session_id: Optional[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte para dicionário"""
        return asdict(self)

class QueryLogger:
    """
    Sistema de log para queries que não conseguiram ser processadas
    
    Funcionalidades:
    - Log estruturado de queries sem resposta
    - Rotação automática de arquivos por data
    - Formato JSON para análise posterior
    - Métricas de queries não atendidas
    """
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(__file__).parent.parent.parent / log_dir
        self.log_dir.mkdir(exist_ok=True)
        
        # Arquivo de log atual
        today = datetime.now().strftime("%Y-%m-%d")
        self.current_log_file = self.log_dir / f"queries_sem_resposta_{today}.jsonl"
        
        logger.info(f"QueryLogger inicializado: {self.current_log_file}")
    
    def log_query_sem_resposta(
        self,
        query: str,
        content: str = "",
        llm_response: str = "",
        selected_recipe: Optional[str] = None,
        error_type: str = "NO_RECIPE_SELECTED",
        user_agent: str = "unknown",
        session_id: Optional[str] = None
    ) -> None:
        """
        Registra uma query que não conseguiu ser processada
        
        Args:
            query: Query original do usuário
            content: Conteúdo adicional fornecido
            llm_response: Resposta bruta do LLM
            selected_recipe: Receita selecionada (se houver)
            error_type: Tipo do erro
            user_agent: User agent ou fonte da query
            session_id: ID da sessão (se disponível)
        """
        try:
            entry = QueryLogEntry(
                timestamp=datetime.now().isoformat(),
                query=query,
                content=content[:500] if content else "",  # Limitar tamanho
                llm_response=llm_response[:500] if llm_response else "",
                selected_recipe=selected_recipe,
                error_type=error_type,
                user_agent=user_agent,
                session_id=session_id
            )
            
            # Escrever no arquivo JSONL (uma linha por entrada)
            with open(self.current_log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(entry.to_dict(), ensure_ascii=False) + "\n")
            
            logger.warning(f"📝 Query sem resposta logada: '{query[:50]}...' -> {error_type}")
            
        except Exception as e:
            logger.error(f"Erro ao logar query sem resposta: {str(e)}")
    
    def log_llm_failure(
        self,
        query: str,
        content: str = "",
        llm_error: str = "",
        user_agent: str = "unknown"
    ) -> None:
        """
        Registra falha específica do LLM
        """
        self.log_query_sem_resposta(
            query=query,
            content=content,
            llm_response=llm_error,
            selected_recipe=None,
            error_type="LLM_FAILURE",
            user_agent=user_agent
        )
    
    def log_recipe_not_found(
        self,
        query: str,
        content: str = "",
        llm_response: str = "",
        user_agent: str = "unknown"
    ) -> None:
        """
        Registra quando LLM não consegue selecionar receita válida
        """
        self.log_query_sem_resposta(
            query=query,
            content=content,
            llm_response=llm_response,
            selected_recipe=None,
            error_type="RECIPE_NOT_FOUND",
            user_agent=user_agent
        )
    
    def log_invalid_recipe(
        self,
        query: str,
        content: str = "",
        llm_response: str = "",
        invalid_recipe: str = "",
        user_agent: str = "unknown"
    ) -> None:
        """
        Registra quando LLM seleciona receita inválida
        """
        self.log_query_sem_resposta(
            query=query,
            content=content,
            llm_response=llm_response,
            selected_recipe=invalid_recipe,
            error_type="INVALID_RECIPE",
            user_agent=user_agent
        )
    
    def get_stats_today(self) -> Dict[str, Any]:
        """
        Obtém estatísticas do dia atual
        
        Returns:
            Dict com estatísticas
        """
        try:
            if not self.current_log_file.exists():
                return {"total_queries": 0, "error_types": {}}
            
            stats = {
                "total_queries": 0,
                "error_types": {},
                "most_common_queries": {},
                "file_path": str(self.current_log_file)
            }
            
            with open(self.current_log_file, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        stats["total_queries"] += 1
                        
                        # Contar tipos de erro
                        error_type = entry.get("error_type", "unknown")
                        stats["error_types"][error_type] = stats["error_types"].get(error_type, 0) + 1
                        
                        # Contar queries mais comuns
                        query = entry.get("query", "")[:50]  # Primeiros 50 chars
                        stats["most_common_queries"][query] = stats["most_common_queries"].get(query, 0) + 1
                        
                    except json.JSONDecodeError:
                        continue
            
            return stats
            
        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {str(e)}")
            return {"error": str(e)}
    
    def get_recent_queries(self, limit: int = 10) -> list:
        """
        Obtém queries recentes sem resposta
        
        Args:
            limit: Número máximo de entries para retornar
            
        Returns:
            Lista de entries recentes
        """
        try:
            if not self.current_log_file.exists():
                return []
            
            entries = []
            with open(self.current_log_file, "r", encoding="utf-8") as f:
                lines = f.readlines()
                
                # Pegar as últimas N linhas
                for line in lines[-limit:]:
                    try:
                        entry = json.loads(line.strip())
                        entries.append(entry)
                    except json.JSONDecodeError:
                        continue
            
            return entries
            
        except Exception as e:
            logger.error(f"Erro ao obter queries recentes: {str(e)}")
            return []
    
    def export_daily_report(self, date: str = None) -> str:
        """
        Exporta relatório diário formatado
        
        Args:
            date: Data no formato YYYY-MM-DD (padrão: hoje)
            
        Returns:
            Relatório formatado em texto
        """
        try:
            if not date:
                date = datetime.now().strftime("%Y-%m-%d")
            
            log_file = self.log_dir / f"queries_sem_resposta_{date}.jsonl"
            
            if not log_file.exists():
                return f"📅 **Relatório {date}**\n\n❌ Nenhum log encontrado para esta data."
            
            stats = {"total": 0, "by_type": {}, "queries": []}
            
            with open(log_file, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        stats["total"] += 1
                        
                        error_type = entry.get("error_type", "unknown")
                        stats["by_type"][error_type] = stats["by_type"].get(error_type, 0) + 1
                        
                        stats["queries"].append({
                            "time": entry.get("timestamp", "")[-8:-3],  # HH:MM
                            "query": entry.get("query", "")[:60],
                            "error": error_type
                        })
                        
                    except json.JSONDecodeError:
                        continue
            
            # Formatar relatório
            report = f"📅 **Relatório de Queries Sem Resposta - {date}**\n\n"
            report += f"📊 **Resumo**\n"
            report += f"• Total de queries: {stats['total']}\n"
            report += f"• Arquivo: {log_file.name}\n\n"
            
            if stats["by_type"]:
                report += f"🔍 **Por Tipo de Erro**\n"
                for error_type, count in sorted(stats["by_type"].items(), key=lambda x: x[1], reverse=True):
                    report += f"• {error_type}: {count}\n"
                report += "\n"
            
            if stats["queries"]:
                report += f"📝 **Últimas 10 Queries**\n"
                for query_info in stats["queries"][-10:]:
                    report += f"• {query_info['time']} - {query_info['error']} - \"{query_info['query']}\"\n"
            
            return report
            
        except Exception as e:
            logger.error(f"Erro ao exportar relatório: {str(e)}")
            return f"❌ Erro ao gerar relatório: {str(e)}"

# Instância global do logger
query_logger = QueryLogger()