"""
Response Evaluator - Sistema de avaliação de qualidade das respostas MCP
Classifica respostas de 1-5 usando LLM para feedback e melhoria contínua

<AUTHOR> Internet 2025
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

import ollama

logger = logging.getLogger(__name__)

@dataclass
class ResponseEvaluation:
    """Avaliação de uma resposta MCP"""
    timestamp: str
    query: str
    mcp_response: str
    recipe_used: Optional[str]
    execution_time_ms: float
    
    # Métricas de avaliação
    quality_score: int  # 1-5
    quality_reason: str
    usefulness: int  # 1-5
    accuracy: int  # 1-5
    clarity: int  # 1-5
    completeness: int  # 1-5
    
    # Feedback adicional
    positive_aspects: list
    improvement_areas: list
    overall_feedback: str

class ResponseEvaluator:
    """
    Sistema de avaliação automática de respostas MCP
    
    Funcionalidades:
    - Avaliação 1-5 da qualidade da resposta
    - Feedback detalhado sobre aspectos específicos
    - Log estruturado para análise posterior
    - Métricas para melhoria contínua
    """
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(__file__).parent.parent.parent / log_dir
        self.log_dir.mkdir(exist_ok=True)
        
        # Arquivo para avaliações
        today = datetime.now().strftime("%Y-%m-%d")
        self.evaluation_log = self.log_dir / f"response_evaluations_{today}.jsonl"
        
        logger.info(f"ResponseEvaluator inicializado: {self.evaluation_log}")
    
    async def evaluate_response(
        self,
        query: str,
        mcp_response: str,
        recipe_used: Optional[str] = None,
        execution_time_ms: float = 0.0
    ) -> ResponseEvaluation:
        """
        Avalia a qualidade de uma resposta MCP usando LLM
        
        Args:
            query: Query original do usuário
            mcp_response: Resposta fornecida pelo MCP
            recipe_used: Receita que gerou a resposta
            execution_time_ms: Tempo de execução
            
        Returns:
            ResponseEvaluation com métricas detalhadas
        """
        try:
            logger.info(f"📊 Avaliando resposta para: '{query[:50]}...'")
            
            # Prompt especializado para avaliação
            evaluation_prompt = self._create_evaluation_prompt(
                query, mcp_response, recipe_used, execution_time_ms
            )
            
            # Chamar LLM para avaliação
            llm_evaluation = await self._call_llm_evaluator(evaluation_prompt)
            
            if not llm_evaluation:
                # Fallback para avaliação básica
                llm_evaluation = self._basic_evaluation(query, mcp_response, execution_time_ms)
            
            # Criar objeto de avaliação
            evaluation = ResponseEvaluation(
                timestamp=datetime.now().isoformat(),
                query=query,
                mcp_response=mcp_response[:1000],  # Limitar tamanho
                recipe_used=recipe_used,
                execution_time_ms=execution_time_ms,
                quality_score=llm_evaluation.get("quality_score", 3),
                quality_reason=llm_evaluation.get("quality_reason", "Avaliação automática"),
                usefulness=llm_evaluation.get("usefulness", 3),
                accuracy=llm_evaluation.get("accuracy", 3),
                clarity=llm_evaluation.get("clarity", 3),
                completeness=llm_evaluation.get("completeness", 3),
                positive_aspects=llm_evaluation.get("positive_aspects", []),
                improvement_areas=llm_evaluation.get("improvement_areas", []),
                overall_feedback=llm_evaluation.get("overall_feedback", "")
            )
            
            # Salvar avaliação
            await self._save_evaluation(evaluation)
            
            logger.info(f"✅ Resposta avaliada: {evaluation.quality_score}/5 - {evaluation.quality_reason}")
            return evaluation
            
        except Exception as e:
            logger.error(f"❌ Erro na avaliação de resposta: {str(e)}")
            # Retornar avaliação neutra em caso de erro
            return ResponseEvaluation(
                timestamp=datetime.now().isoformat(),
                query=query,
                mcp_response=mcp_response[:1000],
                recipe_used=recipe_used,
                execution_time_ms=execution_time_ms,
                quality_score=3,
                quality_reason="Erro na avaliação automática",
                usefulness=3,
                accuracy=3,
                clarity=3,
                completeness=3,
                positive_aspects=[],
                improvement_areas=["Sistema de avaliação indisponível"],
                overall_feedback="Avaliação não pôde ser completada devido a erro técnico"
            )
    
    def _create_evaluation_prompt(
        self,
        query: str,
        mcp_response: str,
        recipe_used: Optional[str],
        execution_time_ms: float
    ) -> str:
        """Cria prompt especializado para avaliação"""
        
        return f"""Você é um avaliador especialista de respostas do sistema Vindula Cosmos Brain MCP.

CONTEXTO:
- Sistema MCP com receitas especializadas (SQL, validação, schemas, timestamps, etc.)
- Foco em respostas técnicas precisas e úteis para desenvolvedores
- Tempo ideal de resposta: < 2000ms

QUERY DO USUÁRIO:
"{query}"

RESPOSTA DO MCP:
"{mcp_response}"

RECEITA USADA: {recipe_used or "N/A"}
TEMPO DE EXECUÇÃO: {execution_time_ms}ms

INSTRUÇÕES DE AVALIAÇÃO:
Avalie a resposta em escala de 1-5 (1=Péssimo, 2=Ruim, 3=Regular, 4=Bom, 5=Excelente):

1. QUALIDADE GERAL (1-5): Avaliação global da resposta
2. UTILIDADE (1-5): Quão útil é para resolver o problema do usuário
3. PRECISÃO (1-5): Correção técnica das informações
4. CLAREZA (1-5): Facilidade de entendimento e formatação
5. COMPLETUDE (1-5): Se responde completamente à pergunta

CRITÉRIOS ESPECÍFICOS:
- Resposta com ❌ = máximo 2 pontos
- Resposta com ✅ e informações úteis = mínimo 4 pontos  
- Tempo > 3000ms = -1 ponto na qualidade geral
- Formatação clara com emojis/markdown = +1 ponto na clareza

FORMATO DE RESPOSTA (JSON válido):
{{
    "quality_score": 4,
    "quality_reason": "Resposta clara e útil com boa formatação",
    "usefulness": 4,
    "accuracy": 5,
    "clarity": 4,
    "completeness": 4,
    "positive_aspects": ["Formatação clara", "Informações precisas"],
    "improvement_areas": ["Poderia ser mais detalhada"],
    "overall_feedback": "Boa resposta que resolve o problema do usuário"
}}

RESPONDA APENAS COM O JSON:"""
    
    async def _call_llm_evaluator(self, prompt: str) -> Optional[Dict]:
        """Chama LLM para fazer a avaliação"""
        try:
            response = ollama.generate(
                model='phi3:mini',
                prompt=prompt,
                options={
                    'temperature': 0.2,  # Baixa para avaliações consistentes
                    'top_p': 0.9,
                    'num_predict': 250   # Resposta estruturada
                }
            )
            
            raw_response = response['response'].strip()
            logger.debug(f"🤖 LLM evaluation response: {raw_response}")
            
            # Extrair JSON da resposta
            if '{' in raw_response and '}' in raw_response:
                json_start = raw_response.find('{')
                json_end = raw_response.rfind('}') + 1
                json_str = raw_response[json_start:json_end]
                
                parsed = json.loads(json_str)
                
                # Validar e corrigir scores se necessário
                parsed = self._validate_scores(parsed)
                
                return parsed
            
            return None
            
        except Exception as e:
            logger.error(f"Erro na chamada LLM evaluator: {str(e)}")
            return None
    
    def _validate_scores(self, evaluation: Dict) -> Dict:
        """Valida e corrige scores para estar na faixa 1-5"""
        score_fields = ["quality_score", "usefulness", "accuracy", "clarity", "completeness"]
        
        for field in score_fields:
            if field in evaluation:
                score = evaluation[field]
                # Garantir que está entre 1 e 5
                evaluation[field] = max(1, min(5, int(score)))
        
        # Garantir que campos obrigatórios existem
        if "quality_reason" not in evaluation:
            evaluation["quality_reason"] = "Avaliação automática"
        
        if "positive_aspects" not in evaluation:
            evaluation["positive_aspects"] = []
        
        if "improvement_areas" not in evaluation:
            evaluation["improvement_areas"] = []
        
        if "overall_feedback" not in evaluation:
            evaluation["overall_feedback"] = "Avaliação completada"
        
        return evaluation
    
    def _basic_evaluation(
        self,
        query: str,
        mcp_response: str,
        execution_time_ms: float
    ) -> Dict:
        """Avaliação básica quando LLM falha"""
        
        # Análise simples baseada em regras
        quality_score = 3
        positive_aspects = []
        improvement_areas = []
        
        # Verificar indicadores na resposta
        if "❌" in mcp_response or "erro" in mcp_response.lower():
            quality_score = 2
            improvement_areas.append("Resposta indica erro")
        elif "✅" in mcp_response:
            quality_score = 4
            positive_aspects.append("Resposta indica sucesso")
        
        # Verificar tamanho da resposta
        if len(mcp_response.strip()) < 20:
            quality_score = max(1, quality_score - 1)
            improvement_areas.append("Resposta muito curta")
        elif len(mcp_response) > 100:
            positive_aspects.append("Resposta detalhada")
        
        # Verificar tempo de execução
        if execution_time_ms > 3000:
            quality_score = max(1, quality_score - 1)
            improvement_areas.append("Tempo de execução elevado")
        elif execution_time_ms < 1000:
            positive_aspects.append("Resposta rápida")
        
        if not positive_aspects:
            positive_aspects.append("Resposta fornecida")
        
        if not improvement_areas:
            improvement_areas.append("Sistema de avaliação LLM indisponível")
        
        return {
            "quality_score": quality_score,
            "quality_reason": "Avaliação básica automática",
            "usefulness": quality_score,
            "accuracy": quality_score,
            "clarity": quality_score,
            "completeness": quality_score,
            "positive_aspects": positive_aspects,
            "improvement_areas": improvement_areas,
            "overall_feedback": f"Avaliação automática básica: {quality_score}/5"
        }
    
    async def _save_evaluation(self, evaluation: ResponseEvaluation) -> None:
        """Salva avaliação no arquivo de log"""
        try:
            with open(self.evaluation_log, "a", encoding="utf-8") as f:
                f.write(json.dumps(asdict(evaluation), ensure_ascii=False) + "\n")
        except Exception as e:
            logger.error(f"Erro ao salvar avaliação: {str(e)}")
    
    def get_quality_stats(self, days: int = 1) -> Dict[str, Any]:
        """
        Obtém estatísticas de qualidade do período
        
        Args:
            days: Número de dias para analisar
            
        Returns:
            Dict com estatísticas de qualidade
        """
        try:
            if not self.evaluation_log.exists():
                return {
                    "total_evaluations": 0,
                    "avg_quality": 0.0,
                    "quality_distribution": {},
                    "avg_metrics": {}
                }
            
            evaluations = []
            with open(self.evaluation_log, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        evaluation = json.loads(line.strip())
                        evaluations.append(evaluation)
                    except json.JSONDecodeError:
                        continue
            
            if not evaluations:
                return {
                    "total_evaluations": 0,
                    "avg_quality": 0.0,
                    "quality_distribution": {},
                    "avg_metrics": {}
                }
            
            # Calcular estatísticas
            total = len(evaluations)
            avg_quality = sum(e["quality_score"] for e in evaluations) / total
            
            # Distribuição de qualidade
            quality_dist = {i: 0 for i in range(1, 6)}
            for e in evaluations:
                score = e["quality_score"]
                if 1 <= score <= 5:
                    quality_dist[score] += 1
            
            # Métricas médias
            avg_metrics = {
                "usefulness": sum(e.get("usefulness", 3) for e in evaluations) / total,
                "accuracy": sum(e.get("accuracy", 3) for e in evaluations) / total,
                "clarity": sum(e.get("clarity", 3) for e in evaluations) / total,
                "completeness": sum(e.get("completeness", 3) for e in evaluations) / total
            }
            
            return {
                "total_evaluations": total,
                "avg_quality": round(avg_quality, 2),
                "quality_distribution": quality_dist,
                "avg_metrics": {k: round(v, 2) for k, v in avg_metrics.items()},
                "period": f"hoje ({days} dia(s))"
            }
            
        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {str(e)}")
            return {"error": str(e)}

# Instância global do avaliador
response_evaluator = ResponseEvaluator()