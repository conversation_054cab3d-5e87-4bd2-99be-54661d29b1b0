"""
Task Feedback System - Sistema de feedback com ID de tarefas
Permite avaliação posterior das respostas MCP usando IDs únicos

<AUTHOR> Internet 2025
"""

import json
import uuid
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class TaskResponse:
    """Resposta de uma tarefa com ID único"""
    task_id: str
    timestamp: str
    query: str
    response: str
    recipe_used: Optional[str]
    execution_time_ms: float
    user_agent: str = "unknown"

@dataclass
class TaskFeedback:
    """Feedback do usuário sobre uma tarefa"""
    task_id: str
    timestamp: str
    rating: int  # 1-5
    comment: str
    user_agent: str = "unknown"
    
    # Opcional: categorização do feedback
    feedback_category: Optional[str] = None  # "accuracy", "completeness", "usefulness", etc.

class TaskFeedbackManager:
    """
    Gerenciador de feedback de tarefas
    
    Funcionalidades:
    - Gerar IDs únicos para cada resposta
    - Armazenar respostas com contexto
    - Receber feedback posterior por ID
    - Análise de padrões de feedback
    """
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(__file__).parent.parent.parent / log_dir
        self.log_dir.mkdir(exist_ok=True)
        
        # Arquivos de log
        today = datetime.now().strftime("%Y-%m-%d")
        self.responses_log = self.log_dir / f"task_responses_{today}.jsonl"
        self.feedback_log = self.log_dir / f"task_feedback_{today}.jsonl"
        
        logger.info(f"TaskFeedbackManager inicializado: {self.log_dir}")
    
    def generate_task_id(self) -> str:
        """Gera um ID único para a tarefa"""
        # Usar timestamp + UUID curto para IDs legíveis
        timestamp = datetime.now().strftime("%H%M%S")
        short_uuid = str(uuid.uuid4())[:8]
        return f"task_{timestamp}_{short_uuid}"
    
    async def save_task_response(
        self,
        task_id: str,
        query: str,
        response: str,
        recipe_used: Optional[str] = None,
        execution_time_ms: float = 0.0,
        user_agent: str = "unknown"
    ) -> None:
        """
        Salva uma resposta de tarefa para posterior feedback
        
        Args:
            task_id: ID único da tarefa
            query: Query original do usuário
            response: Resposta fornecida pelo MCP
            recipe_used: Receita utilizada
            execution_time_ms: Tempo de execução
            user_agent: Identificação do cliente
        """
        try:
            task_response = TaskResponse(
                task_id=task_id,
                timestamp=datetime.now().isoformat(),
                query=query,
                response=response[:2000],  # Limitar tamanho
                recipe_used=recipe_used,
                execution_time_ms=execution_time_ms,
                user_agent=user_agent
            )
            
            # Salvar no log
            with open(self.responses_log, "a", encoding="utf-8") as f:
                f.write(json.dumps(asdict(task_response), ensure_ascii=False) + "\n")
            
            logger.info(f"📝 Task response salva: {task_id}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao salvar task response: {str(e)}")
    
    async def save_feedback(
        self,
        task_id: str,
        rating: int,
        comment: str = "",
        user_agent: str = "unknown",
        feedback_category: Optional[str] = None
    ) -> bool:
        """
        Salva feedback do usuário sobre uma tarefa
        
        Args:
            task_id: ID da tarefa a ser avaliada
            rating: Nota de 1-5
            comment: Comentário opcional
            user_agent: Identificação do cliente
            feedback_category: Categoria do feedback
            
        Returns:
            True se o feedback foi salvo com sucesso
        """
        try:
            # Validar rating
            if not (1 <= rating <= 5):
                logger.warning(f"⚠️ Rating inválido: {rating}. Deve ser entre 1-5")
                return False
            
            # Verificar se a task existe
            task_exists = await self._task_exists(task_id)
            if not task_exists:
                logger.warning(f"⚠️ Task ID não encontrada: {task_id}")
                # Ainda assim salvar o feedback, pode ser útil
            
            feedback = TaskFeedback(
                task_id=task_id,
                timestamp=datetime.now().isoformat(),
                rating=rating,
                comment=comment[:500],  # Limitar tamanho
                user_agent=user_agent,
                feedback_category=feedback_category
            )
            
            # Salvar no log
            with open(self.feedback_log, "a", encoding="utf-8") as f:
                f.write(json.dumps(asdict(feedback), ensure_ascii=False) + "\n")
            
            logger.info(f"📊 Feedback salvo: {task_id} - {rating}/5 - '{comment[:50]}...'")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao salvar feedback: {str(e)}")
            return False
    
    async def _task_exists(self, task_id: str) -> bool:
        """Verifica se uma task ID existe nos logs"""
        try:
            if not self.responses_log.exists():
                return False
            
            with open(self.responses_log, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        task_response = json.loads(line.strip())
                        if task_response.get("task_id") == task_id:
                            return True
                    except json.JSONDecodeError:
                        continue
            
            return False
            
        except Exception as e:
            logger.error(f"Erro ao verificar task existence: {str(e)}")
            return False
    
    async def get_task_details(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Obtém detalhes de uma task por ID
        
        Args:
            task_id: ID da task
            
        Returns:
            Dict com detalhes da task ou None se não encontrada
        """
        try:
            if not self.responses_log.exists():
                return None
            
            with open(self.responses_log, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        task_response = json.loads(line.strip())
                        if task_response.get("task_id") == task_id:
                            return task_response
                    except json.JSONDecodeError:
                        continue
            
            return None
            
        except Exception as e:
            logger.error(f"Erro ao obter detalhes da task: {str(e)}")
            return None
    
    def get_feedback_stats(self, days: int = 7) -> Dict[str, Any]:
        """
        Obtém estatísticas de feedback do período
        
        Args:
            days: Número de dias para analisar
            
        Returns:
            Dict com estatísticas
        """
        try:
            if not self.feedback_log.exists():
                return {
                    "total_feedback": 0,
                    "avg_rating": 0.0,
                    "rating_distribution": {},
                    "common_issues": []
                }
            
            feedbacks = []
            with open(self.feedback_log, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        feedback = json.loads(line.strip())
                        feedbacks.append(feedback)
                    except json.JSONDecodeError:
                        continue
            
            if not feedbacks:
                return {
                    "total_feedback": 0,
                    "avg_rating": 0.0,
                    "rating_distribution": {},
                    "common_issues": []
                }
            
            # Calcular estatísticas
            total = len(feedbacks)
            avg_rating = sum(f["rating"] for f in feedbacks) / total
            
            # Distribuição de ratings
            rating_dist = {i: 0 for i in range(1, 6)}
            for feedback in feedbacks:
                rating = feedback["rating"]
                if 1 <= rating <= 5:
                    rating_dist[rating] += 1
            
            # Issues comuns (comentários de ratings baixos)
            low_ratings = [f for f in feedbacks if f["rating"] <= 2 and f["comment"]]
            common_issues = [f["comment"][:100] for f in low_ratings[:5]]
            
            return {
                "total_feedback": total,
                "avg_rating": round(avg_rating, 2),
                "rating_distribution": rating_dist,
                "common_issues": common_issues,
                "period": f"últimos {days} dias"
            }
            
        except Exception as e:
            logger.error(f"Erro ao obter estatísticas de feedback: {str(e)}")
            return {"error": str(e)}
    
    def get_recent_tasks(self, limit: int = 10) -> list:
        """
        Obtém tasks recentes com seus feedbacks (se existirem)
        
        Args:
            limit: Número máximo de tasks
            
        Returns:
            Lista de tasks com feedback associado
        """
        try:
            # Carregar responses
            responses = []
            if self.responses_log.exists():
                with open(self.responses_log, "r", encoding="utf-8") as f:
                    for line in f:
                        try:
                            response = json.loads(line.strip())
                            responses.append(response)
                        except json.JSONDecodeError:
                            continue
            
            # Carregar feedbacks
            feedbacks = {}
            if self.feedback_log.exists():
                with open(self.feedback_log, "r", encoding="utf-8") as f:
                    for line in f:
                        try:
                            feedback = json.loads(line.strip())
                            feedbacks[feedback["task_id"]] = feedback
                        except json.JSONDecodeError:
                            continue
            
            # Combinar responses com feedbacks
            recent_tasks = []
            for response in sorted(responses, key=lambda x: x["timestamp"], reverse=True)[:limit]:
                task_id = response["task_id"]
                task_info = {
                    "task_id": task_id,
                    "timestamp": response["timestamp"],
                    "query": response["query"][:100],
                    "recipe_used": response["recipe_used"],
                    "execution_time_ms": response["execution_time_ms"],
                    "has_feedback": task_id in feedbacks
                }
                
                if task_id in feedbacks:
                    feedback = feedbacks[task_id]
                    task_info.update({
                        "rating": feedback["rating"],
                        "feedback_comment": feedback["comment"][:100],
                        "feedback_timestamp": feedback["timestamp"]
                    })
                
                recent_tasks.append(task_info)
            
            return recent_tasks
            
        except Exception as e:
            logger.error(f"Erro ao obter tasks recentes: {str(e)}")
            return []

# Instância global do gerenciador
task_feedback_manager = TaskFeedbackManager()