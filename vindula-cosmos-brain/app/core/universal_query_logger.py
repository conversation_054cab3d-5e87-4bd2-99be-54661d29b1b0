"""
Universal Query Logger - Sistema completo de log para TODAS as queries
Registra todas as consultas (com sucesso e falhas) + auto-avaliação LLM

<AUTHOR> Internet 2025
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
import uuid

logger = logging.getLogger(__name__)

@dataclass
class UniversalQueryEntry:
    """Entrada de log universal para qualquer query"""
    query_id: str
    timestamp: str
    query: str
    content: str
    llm_response: str
    selected_recipe: Optional[str]
    recipe_response: str
    status: str  # 'success', 'failure', 'partial'
    error_type: Optional[str]
    execution_time_ms: float
    user_agent: str
    session_id: Optional[str]
    
    # Auto-avaliação do LLM
    llm_self_rating: Optional[int] = None  # 1-5
    llm_self_feedback: Optional[str] = None
    confidence_score: Optional[float] = None  # 0.0-1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte para dicionário"""
        return asdict(self)

class UniversalQueryLogger:
    """
    Sistema universal de log para TODAS as queries
    
    Funcionalidades:
    - Log de queries com sucesso E falhas
    - Auto-avaliação do LLM sobre suas próprias respostas
    - Métricas completas de performance
    - Dashboard unificado de análise
    """
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(__file__).parent.parent.parent / log_dir
        self.log_dir.mkdir(exist_ok=True)
        
        # Arquivo de log universal
        today = datetime.now().strftime("%Y-%m-%d")
        self.universal_log_file = self.log_dir / f"todas_queries_{today}.jsonl"
        
        logger.info(f"UniversalQueryLogger inicializado: {self.universal_log_file}")
    
    def log_query(
        self,
        query: str,
        content: str = "",
        llm_response: str = "",
        selected_recipe: Optional[str] = None,
        recipe_response: str = "",
        status: str = "success",
        error_type: Optional[str] = None,
        execution_time_ms: float = 0.0,
        user_agent: str = "unknown",
        session_id: Optional[str] = None,
        # Auto-avaliação
        llm_self_rating: Optional[int] = None,
        llm_self_feedback: Optional[str] = None,
        confidence_score: Optional[float] = None
    ) -> str:
        """
        Registra qualquer query (sucesso ou falha)
        
        Returns:
            query_id: ID único da query para referência
        """
        try:
            query_id = str(uuid.uuid4())[:8]  # ID curto
            
            entry = UniversalQueryEntry(
                query_id=query_id,
                timestamp=datetime.now().isoformat(),
                query=query,
                content=content[:1000] if content else "",
                llm_response=llm_response[:1000] if llm_response else "",
                selected_recipe=selected_recipe,
                recipe_response=recipe_response[:2000] if recipe_response else "",
                status=status,
                error_type=error_type,
                execution_time_ms=execution_time_ms,
                user_agent=user_agent,
                session_id=session_id,
                llm_self_rating=llm_self_rating,
                llm_self_feedback=llm_self_feedback,
                confidence_score=confidence_score
            )
            
            # Escrever no arquivo JSONL
            with open(self.universal_log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(entry.to_dict(), ensure_ascii=False) + "\n")
            
            status_icon = "✅" if status == "success" else "❌" if status == "failure" else "⚠️"
            logger.info(f"{status_icon} Query {query_id} logada: '{query[:50]}...' -> {status}")
            
            return query_id
            
        except Exception as e:
            logger.error(f"Erro ao logar query universal: {str(e)}")
            return ""
    
    def log_successful_query(
        self,
        query: str,
        content: str = "",
        llm_response: str = "",
        selected_recipe: str = "",
        recipe_response: str = "",
        execution_time_ms: float = 0.0,
        user_agent: str = "unknown",
        confidence_score: Optional[float] = None
    ) -> str:
        """Registra query processada com sucesso"""
        return self.log_query(
            query=query,
            content=content,
            llm_response=llm_response,
            selected_recipe=selected_recipe,
            recipe_response=recipe_response,
            status="success",
            execution_time_ms=execution_time_ms,
            user_agent=user_agent,
            confidence_score=confidence_score
        )
    
    def log_failed_query(
        self,
        query: str,
        content: str = "",
        llm_response: str = "",
        error_type: str = "UNKNOWN_ERROR",
        execution_time_ms: float = 0.0,
        user_agent: str = "unknown"
    ) -> str:
        """Registra query que falhou"""
        return self.log_query(
            query=query,
            content=content,
            llm_response=llm_response,
            status="failure",
            error_type=error_type,
            execution_time_ms=execution_time_ms,
            user_agent=user_agent
        )
    
    def add_llm_self_evaluation(
        self,
        query_id: str,
        self_rating: int,
        self_feedback: str = "",
        confidence_score: Optional[float] = None
    ) -> bool:
        """
        Adiciona auto-avaliação do LLM para uma query existente
        
        Args:
            query_id: ID da query
            self_rating: Nota que o LLM dá para sua própria resposta (1-5)
            self_feedback: Comentário do LLM sobre sua resposta
            confidence_score: Confiança na resposta (0.0-1.0)
        """
        try:
            # Ler arquivo e atualizar entrada específica
            if not self.universal_log_file.exists():
                return False
            
            lines = []
            updated = False
            
            with open(self.universal_log_file, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        if entry.get("query_id") == query_id:
                            entry["llm_self_rating"] = self_rating
                            entry["llm_self_feedback"] = self_feedback
                            if confidence_score is not None:
                                entry["confidence_score"] = confidence_score
                            updated = True
                        lines.append(json.dumps(entry, ensure_ascii=False))
                    except json.JSONDecodeError:
                        lines.append(line.strip())
            
            if updated:
                # Reescrever arquivo
                with open(self.universal_log_file, "w", encoding="utf-8") as f:
                    for line in lines:
                        f.write(line + "\n")
                
                logger.info(f"🤖 Auto-avaliação LLM adicionada para query {query_id}: {self_rating}/5")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Erro ao adicionar auto-avaliação: {str(e)}")
            return False
    
    def get_all_queries(
        self,
        limit: int = 50,
        status_filter: Optional[str] = None,
        date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Obtém todas as queries (sucesso + falhas)
        
        Args:
            limit: Número máximo de queries
            status_filter: Filtrar por status ('success', 'failure', 'partial')
            date: Data específica (YYYY-MM-DD)
        """
        try:
            if date:
                log_file = self.log_dir / f"todas_queries_{date}.jsonl"
            else:
                log_file = self.universal_log_file
            
            if not log_file.exists():
                return []
            
            queries = []
            with open(log_file, "r", encoding="utf-8") as f:
                lines = f.readlines()
                
                # Processar em ordem reversa (mais recentes primeiro)
                for line in reversed(lines):
                    try:
                        entry = json.loads(line.strip())
                        
                        # Filtrar por status se especificado
                        if status_filter and entry.get("status") != status_filter:
                            continue
                        
                        queries.append(entry)
                        
                        if len(queries) >= limit:
                            break
                            
                    except json.JSONDecodeError:
                        continue
            
            return queries
            
        except Exception as e:
            logger.error(f"Erro ao obter queries: {str(e)}")
            return []
    
    def get_stats_comprehensive(self, date: Optional[str] = None) -> Dict[str, Any]:
        """
        Obtém estatísticas completas de todas as queries
        """
        try:
            if date:
                log_file = self.log_dir / f"todas_queries_{date}.jsonl"
            else:
                log_file = self.universal_log_file
            
            if not log_file.exists():
                return {
                    "total_queries": 0,
                    "successful_queries": 0,
                    "failed_queries": 0,
                    "success_rate": 0.0,
                    "error_types": {},
                    "avg_execution_time": 0.0,
                    "llm_self_ratings": {"avg": 0.0, "distribution": {}},
                    "recipes_used": {}
                }
            
            stats = {
                "total_queries": 0,
                "successful_queries": 0,
                "failed_queries": 0,
                "success_rate": 0.0,
                "error_types": {},
                "avg_execution_time": 0.0,
                "total_execution_time": 0.0,
                "llm_self_ratings": {"total": 0, "sum": 0, "avg": 0.0, "distribution": {}},
                "recipes_used": {},
                "confidence_scores": []
            }
            
            with open(log_file, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line.strip())
                        stats["total_queries"] += 1
                        
                        # Status
                        if entry.get("status") == "success":
                            stats["successful_queries"] += 1
                        else:
                            stats["failed_queries"] += 1
                        
                        # Tipos de erro
                        if entry.get("error_type"):
                            error_type = entry["error_type"]
                            stats["error_types"][error_type] = stats["error_types"].get(error_type, 0) + 1
                        
                        # Tempo de execução
                        exec_time = entry.get("execution_time_ms", 0)
                        stats["total_execution_time"] += exec_time
                        
                        # Auto-avaliação LLM
                        if entry.get("llm_self_rating"):
                            rating = entry["llm_self_rating"]
                            stats["llm_self_ratings"]["total"] += 1
                            stats["llm_self_ratings"]["sum"] += rating
                            stats["llm_self_ratings"]["distribution"][str(rating)] = \
                                stats["llm_self_ratings"]["distribution"].get(str(rating), 0) + 1
                        
                        # Receitas usadas
                        if entry.get("selected_recipe"):
                            recipe = entry["selected_recipe"]
                            stats["recipes_used"][recipe] = stats["recipes_used"].get(recipe, 0) + 1
                        
                        # Scores de confiança
                        if entry.get("confidence_score") is not None:
                            stats["confidence_scores"].append(entry["confidence_score"])
                            
                    except json.JSONDecodeError:
                        continue
            
            # Calcular médias
            if stats["total_queries"] > 0:
                stats["success_rate"] = stats["successful_queries"] / stats["total_queries"]
                stats["avg_execution_time"] = stats["total_execution_time"] / stats["total_queries"]
            
            if stats["llm_self_ratings"]["total"] > 0:
                stats["llm_self_ratings"]["avg"] = stats["llm_self_ratings"]["sum"] / stats["llm_self_ratings"]["total"]
            
            return stats
            
        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {str(e)}")
            return {"error": str(e)}

# Instância global do logger universal
universal_query_logger = UniversalQueryLogger()