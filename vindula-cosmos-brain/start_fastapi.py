#!/usr/bin/env python3
"""
Vindula Cosmos Brain - FastAPI Server para teste via navegador
Sistema de receitas com interface web para testing

<AUTHOR> Internet 2025
"""

import sys
import asyncio
import logging
import json
from pathlib import Path
from typing import Dict, Any, Optional

# Adicionar diretório raiz ao PYTHONPATH
sys.path.insert(0, str(Path(__file__).parent))

from fastapi import FastAPI, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel
import uvicorn

# Imports do nosso sistema de receitas
from app.recipes.registry import RecipeRegistry
from app.recipes.engine import RecipeEngine
from app.recipes.selector import RecipeSelector
from app.core.queue import task_queue

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stderr)]
)

logger = logging.getLogger(__name__)

# Models para API
class RecipeRequest(BaseModel):
    query: str
    content: str = ""

class RecipeResponse(BaseModel):
    success: bool
    data: Dict[str, Any]
    message: str = ""
    recipe_used: str = ""
    duration_ms: int = 0
    task_id: str = ""

# FastAPI app
app = FastAPI(
    title="Vindula Cosmos Brain API",
    description="Sistema de receitas para análise SQL e validação",
    version="2.0.0"
)

@app.on_event("startup")
async def startup_event():
    """Inicializar sistema de receitas"""
    logger.info("🚀 Inicializando Vindula Cosmos Brain FastAPI...")
    
    try:
        # Inicializar registry com auto-descoberta
        RecipeRegistry.initialize()
        
        # Inicializar e iniciar task queue
        await task_queue.start()
        
        # Log das receitas descobertas
        recipes = RecipeRegistry.list_recipes()
        logger.info(f"✅ Sistema inicializado: {len(recipes)} receitas registradas")
        
        if recipes:
            for recipe_name in recipes:
                metadata = RecipeRegistry.get_metadata(recipe_name)
                async_flag = " (async)" if metadata and metadata.is_async else ""
                logger.info(f"  • {recipe_name}{async_flag}")
                
    except Exception as e:
        logger.error(f"❌ Erro na inicialização: {str(e)}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup"""
    try:
        await task_queue.stop()
        logger.info("🧹 Cleanup concluído")
    except:
        pass

@app.get("/", response_class=HTMLResponse)
async def home():
    """Interface web para testar recipes"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Vindula Cosmos Brain - Recipe Tester</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; border-bottom: 3px solid #007acc; padding-bottom: 10px; }
            .form-group { margin-bottom: 20px; }
            label { display: block; font-weight: bold; margin-bottom: 5px; color: #555; }
            input, textarea, select { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
            textarea { height: 200px; font-family: 'Courier New', monospace; }
            button { background: #007acc; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
            button:hover { background: #005a99; }
            .result { margin-top: 20px; padding: 20px; border-radius: 4px; white-space: pre-wrap; font-family: 'Courier New', monospace; }
            .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
            .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
            .examples { background: #e7f3ff; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
            .examples h3 { margin-top: 0; color: #007acc; }
            .example { margin: 10px 0; cursor: pointer; color: #0056b3; text-decoration: underline; }
            .loading { display: none; color: #007acc; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🧠 Vindula Cosmos Brain - Recipe Tester</h1>
            
            <div class="examples">
                <h3>📋 Exemplos Rápidos:</h3>
                <div class="example" onclick="loadExample('structure', 'CREATE FUNCTION test_function(p_user_id uuid, p_name text) RETURNS void AS $$ BEGIN INSERT INTO users (id, name) VALUES (p_user_id, p_name); END; $$ LANGUAGE plpgsql;')">
                    🔍 Estruturar Function SQL
                </div>
                <div class="example" onclick="loadExample('schema', '')">
                    📋 Verificar Schema da tabela users
                </div>
                <div class="example" onclick="loadExample('validation', 'CREATE OR REPLACE FUNCTION add_stardust_v2(p_user_id uuid, p_action_type text, p_metadata jsonb DEFAULT null) RETURNS void AS $$ BEGIN INSERT INTO stardust_transactions (user_id, action_type, metadata) VALUES (p_user_id, p_action_type, p_metadata); END; $$ LANGUAGE plpgsql;')">
                    ✅ Validar Function Stardust
                </div>
            </div>
            
            <form onsubmit="testRecipe(event)">
                <div class="form-group">
                    <label for="query">Query (pergunta em linguagem natural):</label>
                    <input type="text" id="query" name="query" placeholder="Ex: estruturar este SQL, validar esta function, verificar tabela users" required>
                </div>
                
                <div class="form-group">
                    <label for="content">Conteúdo SQL (opcional):</label>
                    <textarea id="content" name="content" placeholder="Cole aqui o código SQL para análise..."></textarea>
                </div>
                
                <button type="submit">🚀 Executar Recipe</button>
                <div class="loading" id="loading">⏳ Processando...</div>
            </form>
            
            <div id="result"></div>
        </div>
        
        <script>
            function loadExample(type, content) {
                const queries = {
                    'structure': 'estruturar este SQL',
                    'schema': 'estrutura da tabela users',
                    'validation': 'validar esta function SQL'
                };
                
                document.getElementById('query').value = queries[type];
                document.getElementById('content').value = content;
            }
            
            async function testRecipe(event) {
                event.preventDefault();
                
                const query = document.getElementById('query').value;
                const content = document.getElementById('content').value;
                const resultDiv = document.getElementById('result');
                const loadingDiv = document.getElementById('loading');
                
                // Show loading
                loadingDiv.style.display = 'block';
                resultDiv.innerHTML = '';
                
                try {
                    const response = await fetch('/recipe', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            query: query,
                            content: content
                        })
                    });
                    
                    const data = await response.json();
                    
                    // Hide loading
                    loadingDiv.style.display = 'none';
                    
                    if (data.success) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `✅ Sucesso (${data.duration_ms}ms)\\n\\nReceita: ${data.recipe_used}\\n\\n${data.message}`;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = `❌ Erro\\n\\n${data.message}`;
                    }
                    
                } catch (error) {
                    loadingDiv.style.display = 'none';
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Erro de conexão\\n\\n${error.message}`;
                }
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.post("/recipe", response_model=RecipeResponse)
async def execute_recipe(request: RecipeRequest):
    """Executar recipe via API"""
    try:
        logger.info(f"Recipe API chamada: query='{request.query[:50]}...'")
        if request.content:
            logger.info(f"Content recebido: {len(request.content)} caracteres")
        
        # 1. Seletor determina qual receita usar
        recipe_name = await RecipeSelector.select_recipe(request.query, request.content)
        
        if not recipe_name:
            raise HTTPException(
                status_code=400, 
                detail="Nenhuma receita encontrada para esta consulta"
            )
        
        logger.info(f"Receita selecionada: {recipe_name}")
        
        # 2. Preparar dados de entrada
        input_data = {"query": request.query}
        if request.content and request.content.strip():
            input_data["content"] = request.content
            input_data["sql_code"] = request.content  # Para compatibilidade
        
        # 3. Executar receita
        result = await RecipeEngine.execute(recipe_name, input_data)
        
        if result.success:
            message = ""
            if "message" in result.data and result.data["message"]:
                message = result.data["message"]
            else:
                message = f"Receita {recipe_name} executada com sucesso"
            
            # Para estruturar_dados_sql, mostrar JSON estruturado (dados úteis)
            if recipe_name == "estruturar_dados_sql":
                formatted_json = json.dumps(result.data, indent=2, ensure_ascii=False)
                message += f"\n\n**JSON ESTRUTURADO:**\n```json\n{formatted_json}\n```"
            
            # Para validar_estrutura_sql, mostrar apenas resumo estruturado útil
            elif recipe_name == "validar_estrutura_sql":
                validation_summary = result.data.get("validation_summary", {})
                table_issues = [t for t in result.data.get("table_validations", []) if not t.get("exists", False)]
                column_issues = [c for c in result.data.get("column_validations", []) if not c.get("exists", False)]
                
                if table_issues or column_issues:
                    message += f"\n\n**RESUMO ESTRUTURADO:**\n```json\n"
                    summary_data = {
                        "status": validation_summary.get("overall_status"),
                        "problemas_tabelas": [{"tabela": t["table"], "sugestao": t.get("suggested_tables", ["N/A"])[0] if t.get("suggested_tables") else "N/A"} for t in table_issues[:3]],
                        "problemas_colunas": [{"tabela_coluna": f"{c['table']}.{c['column']}", "contexto": c["context"], "sugestao": c.get("suggested_columns", ["N/A"])[0] if c.get("suggested_columns") else "N/A"} for c in column_issues[:3]]
                    }
                    message += json.dumps(summary_data, indent=2, ensure_ascii=False)
                    message += "\n```"
            
            return RecipeResponse(
                success=True,
                data=result.data,
                message=message,
                recipe_used=recipe_name,
                duration_ms=result.duration_ms or 0,
                task_id=result.metadata.get("task_id", "") if result.metadata else ""
            )
        else:
            return RecipeResponse(
                success=False,
                data={},
                message=result.error or "Erro desconhecido",
                recipe_used=recipe_name,
                duration_ms=result.duration_ms or 0,
                task_id=result.metadata.get("task_id", "") if result.metadata else ""
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao executar recipe: {str(e)}")
        return RecipeResponse(
            success=False,
            data={},
            message=f"Erro interno: {str(e)}",
            recipe_used="",
            duration_ms=0,
            task_id=""
        )

@app.get("/health")
async def health_check():
    """Health check da API"""
    try:
        # Verificar componentes principais
        registry_stats = RecipeRegistry.stats()
        
        return JSONResponse(content={
            "status": "healthy",
            "recipes": registry_stats["total_recipes"],
            "registry_initialized": registry_stats["initialized"],
            "queue_running": hasattr(task_queue, 'running') and task_queue.running
        })
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy", 
                "error": str(e)
            }
        )

@app.get("/recipes")
async def list_recipes():
    """Listar todas as receitas disponíveis"""
    try:
        recipes = RecipeRegistry.get_all_info()
        return JSONResponse(content={
            "total": len(recipes),
            "recipes": recipes
        })
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

if __name__ == "__main__":
    logger.info("🚀 Iniciando Vindula Cosmos Brain FastAPI Server...")
    uvicorn.run(
        "start_fastapi:app",
        host="0.0.0.0", 
        port=8000,
        reload=True,
        log_level="info"
    )