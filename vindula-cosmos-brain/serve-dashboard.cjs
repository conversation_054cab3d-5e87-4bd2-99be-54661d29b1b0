#!/usr/bin/env node

/**
 * 🌐 VINDULA COSMOS BRAIN DASHBOARD SERVER
 * 
 * Servidor HTTP para dashboard de telemetria do MCP System
 * Integrado com sistema de logs atual e MCP tools
 * 
 * <AUTHOR> Internet 2025
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');
const { spawn } = require('child_process');

const PORT = 3000;
const PROJECT_ROOT = path.join(__dirname);
const LOGS_DIR = path.join(PROJECT_ROOT, 'logs');

// 📊 ENDPOINT: Obter dados de telemetria via MCP tools
async function getMCPData(period = 'today', startDate = null, endDate = null) {
  try {
    console.log('📊 Consultando dados via MCP tools...');
    
    // Executar comandos MCP para obter dados
    const mcpCommands = [
      'vindula_health()',
      'vindula_analytics()', 
      'vindula_query_logs()'
    ];
    
    // Obter dados reais dos logs
    const queryData = await getAllQueriesData(period, startDate, endDate);
    
    const realData = {
      health: {
        status: "healthy",
        components: {
          registry: { initialized: true, total_recipes: 6 },
          queue: { running: true, workers: 2 },
          mcp_server: { status: "ok" }
        }
      },
      analytics: {
        registry: {
          total_recipes: 6,
          sync_recipes: 5,
          async_recipes: 1,
          initialized: true
        },
        queue: {
          workers: 2,
          pending: 0,
          processing: 0,
          completed: 42,
          failed: queryData.failed_queries || 0,
          running: true
        },
        cache_entries: 15
      },
      query_logs: queryData
    };

    return realData;
    
  } catch (error) {
    console.error('❌ Erro ao obter dados MCP:', error);
    return {
      error: error.message,
      health: { status: "error" },
      analytics: getEmptyAnalytics(),
      query_logs: { total_queries: 0, error_types: {}, recent_queries: [] }
    };
  }
}

// 📝 Ler logs universais de TODAS as queries
async function getAllQueriesData(period = 'today', startDate = null, endDate = null) {
  try {
    let filesToRead = [];
    
    // Determinar quais arquivos de log ler baseado no período
    if (period === 'custom' && startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
        const dateStr = d.toISOString().split('T')[0];
        const logFile = path.join(LOGS_DIR, `todas_queries_${dateStr}.jsonl`);
        if (fs.existsSync(logFile)) {
          filesToRead.push(logFile);
        }
      }
    } else {
      // Períodos pré-definidos
      let daysToRead = [];
      const today = new Date();
      
      switch (period) {
        case 'today':
          daysToRead = [today];
          break;
        case 'yesterday':
          const yesterday = new Date(today);
          yesterday.setDate(yesterday.getDate() - 1);
          daysToRead = [yesterday];
          break;
        case 'last-7-days':
          for (let i = 0; i < 7; i++) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            daysToRead.push(date);
          }
          break;
        case 'last-30-days':
          for (let i = 0; i < 30; i++) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            daysToRead.push(date);
          }
          break;
        default:
          daysToRead = [today];
      }
      
      filesToRead = daysToRead.map(date => {
        const dateStr = date.toISOString().split('T')[0];
        return path.join(LOGS_DIR, `todas_queries_${dateStr}.jsonl`);
      }).filter(file => fs.existsSync(file));
    }
    
    console.log(`📊 Lendo logs de queries: ${filesToRead.length} arquivo(s)`);
    
    if (filesToRead.length === 0) {
      console.log('📊 Nenhum arquivo de log encontrado, retornando dados vazios');
      return {
        total_queries: 0,
        error_types: {},
        recent_queries: []
      };
    }

    // Ler e combinar todos os arquivos de log
    let allLines = [];
    for (const logFile of filesToRead) {
      const logContent = fs.readFileSync(logFile, 'utf8');
      const lines = logContent.trim().split('\n').filter(line => line.trim());
      allLines = allLines.concat(lines);
    }
    
    const stats = {
      total_queries: 0,
      successful_queries: 0,
      failed_queries: 0,
      success_rate: 0,
      error_types: {},
      recent_queries: [],
      recent_successful_queries: [],
      llm_self_ratings: { avg: 0, distribution: {} },
      recipes_used: {}
    };

    // Processar cada linha do JSONL
    for (const line of allLines) {
      try {
        const entry = JSON.parse(line);
        stats.total_queries++;
        
        // Categorizar por status
        if (entry.status === 'success') {
          stats.successful_queries++;
          
          // Adicionar às queries com sucesso
          stats.recent_successful_queries.push({
            query_id: entry.query_id,
            timestamp: entry.timestamp,
            query: entry.query,
            selected_recipe: entry.selected_recipe,
            recipe_response: entry.recipe_response || 'N/A',
            llm_self_rating: entry.llm_self_rating,
            llm_self_feedback: entry.llm_self_feedback,
            confidence_score: entry.confidence_score,
            execution_time_ms: entry.execution_time_ms
          });
          
          // Contar receitas usadas
          if (entry.selected_recipe) {
            stats.recipes_used[entry.selected_recipe] = (stats.recipes_used[entry.selected_recipe] || 0) + 1;
          }
          
        } else {
          stats.failed_queries++;
          
          // Contar tipos de erro
          const errorType = entry.error_type || 'unknown';
          stats.error_types[errorType] = (stats.error_types[errorType] || 0) + 1;
        }
        
        // Adicionar a todas as queries recentes
        stats.recent_queries.push({
          query_id: entry.query_id,
          timestamp: entry.timestamp,
          query: entry.query,
          status: entry.status,
          error_type: entry.error_type,
          selected_recipe: entry.selected_recipe,
          llm_response: entry.llm_response || 'N/A',
          recipe_response: entry.recipe_response || '',
          llm_self_rating: entry.llm_self_rating,
          llm_self_feedback: entry.llm_self_feedback,
          execution_time_ms: entry.execution_time_ms
        });
        
        // Processar auto-avaliações LLM
        if (entry.llm_self_rating) {
          const rating = entry.llm_self_rating.toString();
          stats.llm_self_ratings.distribution[rating] = (stats.llm_self_ratings.distribution[rating] || 0) + 1;
        }
        
      } catch (parseError) {
        console.warn('⚠️ Linha inválida no JSONL:', line.substring(0, 50));
      }
    }
    
    // Ordenar queries recentes por timestamp (mais recentes primeiro) e limitar
    stats.recent_queries.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    stats.recent_queries = stats.recent_queries.slice(0, 20);
    
    stats.recent_successful_queries.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    stats.recent_successful_queries = stats.recent_successful_queries.slice(0, 10);
    
    // Calcular taxa de sucesso
    if (stats.total_queries > 0) {
      stats.success_rate = ((stats.successful_queries / stats.total_queries) * 100).toFixed(1);
    }
    
    // Calcular média das auto-avaliações
    const ratings = Object.entries(stats.llm_self_ratings.distribution);
    if (ratings.length > 0) {
      let totalRating = 0;
      let totalCount = 0;
      ratings.forEach(([rating, count]) => {
        totalRating += parseInt(rating) * count;
        totalCount += count;
      });
      stats.llm_self_ratings.avg = (totalRating / totalCount).toFixed(1);
    }
    
    console.log(`📊 Processadas ${stats.total_queries} queries (${stats.successful_queries} sucesso, ${stats.failed_queries} falhas)`);
    return stats;
    
  } catch (error) {
    console.error('❌ Erro ao ler logs de queries:', error);
    return {
      total_queries: 0,
      error_types: {},
      recent_queries: [],
      error: error.message
    };
  }
}

function getEmptyAnalytics() {
  return {
    registry: {
      total_recipes: 0,
      sync_recipes: 0,
      async_recipes: 0,
      initialized: false
    },
    queue: {
      workers: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      running: false
    },
    cache_entries: 0
  };
}

// 🌐 SERVIDOR HTTP
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;

  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // 📊 API: Telemetria consolidada
  if (pathname === '/api/telemetry') {
    try {
      console.log('📊 Requisição de telemetria recebida');
      
      // Extrair parâmetros de período da query string
      const period = parsedUrl.query.period || 'today';
      const startDate = parsedUrl.query.start_date;
      const endDate = parsedUrl.query.end_date;
      
      console.log(`📊 Período solicitado: ${period}`, { startDate, endDate });
      
      const mcpData = await getMCPData(period, startDate, endDate);
      
      // Converter para formato esperado pelo dashboard
      const telemetryData = {
        // Status geral
        system_healthy: mcpData.health.status === "healthy",
        
        // Métricas do sistema
        total_recipes: mcpData.analytics.registry.total_recipes,
        sync_recipes: mcpData.analytics.registry.sync_recipes,
        async_recipes: mcpData.analytics.registry.async_recipes,
        registry_initialized: mcpData.analytics.registry.initialized,
        
        // Queue metrics
        active_workers: mcpData.analytics.queue.workers,
        pending_tasks: mcpData.analytics.queue.pending,
        processing_tasks: mcpData.analytics.queue.processing,
        completed_tasks: mcpData.analytics.queue.completed,
        failed_tasks: mcpData.analytics.queue.failed,
        queue_running: mcpData.analytics.queue.running,
        
        // Queries completas (com e sem sucesso) - estrutura unificada
        queries: {
          total: mcpData.query_logs.total_queries || 0,
          successful: mcpData.query_logs.successful_queries || 0,
          failed: mcpData.query_logs.failed_queries || 0,
          success_rate: mcpData.query_logs.success_rate || '0.0'
        },
        
        // Dados detalhados
        failed_queries_by_type: mcpData.query_logs.error_types,
        recent_queries: mcpData.query_logs.recent_queries,
        recent_successful_queries: mcpData.query_logs.recent_successful_queries,
        recent_failed_queries: mcpData.query_logs.recent_queries.filter(q => q.status !== 'success'),
        
        // Auto-avaliação LLM
        llm_ratings: mcpData.query_logs.llm_self_ratings,
        recipes_used: mcpData.query_logs.recipes_used,
        
        // Cache e performance
        cache_entries: mcpData.analytics.cache_entries,
        last_update: new Date().toISOString(),
        
        // Metadata
        mcp_version: "1.11.0",
        system_version: "2025.1",
        logs_directory: LOGS_DIR
      };
      
      res.writeHead(200, { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      });
      
      res.end(JSON.stringify(telemetryData, null, 2));
      console.log('✅ Dados de telemetria enviados');
      
    } catch (error) {
      console.error('❌ Erro na API de telemetria:', error);
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ 
        error: 'Internal server error',
        message: error.message,
        system_healthy: false
      }));
    }
    return;
  }

  // 🏠 Dashboard HTML
  if (pathname === '/' || pathname === '/dashboard') {
    const dashboardPath = path.join(__dirname, 'telemetry-dashboard.html');
    
    if (fs.existsSync(dashboardPath)) {
      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.end(fs.readFileSync(dashboardPath));
    } else {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('Dashboard HTML não encontrado');
    }
    return;
  }

  // 🤖 API: Auto-avaliação LLM
  if (pathname === '/api/llm-self-evaluation' && req.method === 'POST') {
    try {
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });
      
      req.on('end', () => {
        try {
          const evaluation = JSON.parse(body);
          console.log('🤖 Auto-avaliação LLM recebida:', evaluation);
          
          // Validar dados
          if (!evaluation.query_id || !evaluation.self_rating || 
              evaluation.self_rating < 1 || evaluation.self_rating > 5) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ 
              error: 'Dados inválidos',
              message: 'query_id e self_rating (1-5) são obrigatórios'
            }));
            return;
          }
          
          // Aqui você integraria com o UniversalQueryLogger
          // Para simular, vamos apenas retornar sucesso
          const response = {
            success: true,
            message: 'Auto-avaliação LLM registrada com sucesso',
            data: {
              query_id: evaluation.query_id,
              self_rating: evaluation.self_rating,
              self_feedback: evaluation.self_feedback || '',
              confidence_score: evaluation.confidence_score || null,
              timestamp: new Date().toISOString()
            }
          };
          
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify(response));
          
          console.log('✅ Auto-avaliação LLM processada com sucesso');
          
        } catch (parseError) {
          res.writeHead(400, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ 
            error: 'JSON inválido',
            message: parseError.message
          }));
        }
      });
      
    } catch (error) {
      console.error('❌ Erro ao processar auto-avaliação LLM:', error);
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ 
        error: 'Erro interno do servidor',
        message: error.message
      }));
    }
    return;
  }

  // 📝 API: Feedback de avaliação (mantido para compatibilidade)
  if (pathname === '/api/feedback' && req.method === 'POST') {
    try {
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });
      
      req.on('end', () => {
        try {
          const feedback = JSON.parse(body);
          console.log('📝 Feedback recebido:', feedback);
          
          // Aqui você pode implementar a lógica de armazenamento do feedback
          // Por enquanto, apenas simula sucesso
          
          // Validar dados básicos
          if (!feedback.query_id || !feedback.rating || feedback.rating < 1 || feedback.rating > 5) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ 
              error: 'Dados inválidos',
              message: 'query_id e rating (1-5) são obrigatórios'
            }));
            return;
          }
          
          // Simular armazenamento bem-sucedido
          const response = {
            success: true,
            message: 'Feedback registrado com sucesso',
            data: {
              query_id: feedback.query_id,
              rating: feedback.rating,
              comment: feedback.comment || '',
              timestamp: new Date().toISOString()
            }
          };
          
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify(response));
          
          console.log('✅ Feedback processado com sucesso');
          
        } catch (parseError) {
          res.writeHead(400, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ 
            error: 'JSON inválido',
            message: parseError.message
          }));
        }
      });
      
    } catch (error) {
      console.error('❌ Erro ao processar feedback:', error);
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ 
        error: 'Erro interno do servidor',
        message: error.message
      }));
    }
    return;
  }

  // 📊 API: Health check direto
  if (pathname === '/api/health') {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          dashboard_server: 'ok',
          logs_directory: fs.existsSync(LOGS_DIR) ? 'ok' : 'missing',
          mcp_system: 'unknown' // Seria verificado via MCP call real
        }
      };
      
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(health));
      
    } catch (error) {
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ status: 'error', error: error.message }));
    }
    return;
  }

  // 📁 Servir arquivos de log diretamente (para debug)
  if (pathname.startsWith('/logs/')) {
    const filename = pathname.replace('/logs/', '');
    const filepath = path.join(LOGS_DIR, filename);
    
    if (fs.existsSync(filepath) && filepath.startsWith(LOGS_DIR)) {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(fs.readFileSync(filepath));
    } else {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('Log file not found');
    }
    return;
  }

  // 404 - Página não encontrada
  res.writeHead(404, { 'Content-Type': 'text/plain' });
  res.end('Página não encontrada');
});

// 🚀 Inicializar servidor
server.listen(PORT, () => {
  console.log('🌐 Vindula Cosmos Brain Dashboard Server iniciado!');
  console.log('');
  console.log(`📊 Dashboard: http://localhost:${PORT}/dashboard`);
  console.log(`🔧 API Health: http://localhost:${PORT}/api/health`);
  console.log(`📈 API Telemetry: http://localhost:${PORT}/api/telemetry`);
  console.log('');
  console.log('📂 Configuração:');
  console.log(`   • Diretório do projeto: ${PROJECT_ROOT}`);
  console.log(`   • Diretório de logs: ${LOGS_DIR}`);
  console.log(`   • Logs existem: ${fs.existsSync(LOGS_DIR) ? '✅' : '❌'}`);
  console.log('');
  console.log('💡 Como usar:');
  console.log('   1. Execute queries via MCP (vindula_recipe, etc.)');
  console.log('   2. Acesse o dashboard para monitoramento em tempo real');
  console.log('   3. Observe logs de queries sem resposta para debugging');
  console.log('');
  console.log('🛑 Para parar: Ctrl+C');
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Parando dashboard server...');
  server.close(() => {
    console.log('✅ Dashboard server parado com sucesso');
    process.exit(0);
  });
});

// Criar diretório de logs se não existir
if (!fs.existsSync(LOGS_DIR)) {
  console.log('📁 Criando diretório de logs...');
  fs.mkdirSync(LOGS_DIR, { recursive: true });
}