# CLAUDE.md

**Vindula Cosmos**: Plataforma colaboração corporativa multi-tenant - React 18 + TypeScript + Vite + Supabase (PostgreSQL + Auth + Storage + Realtime) - Arquitetura multi-tenant com RLS + TanStack Query + Zustand

## Regra do 10º Homem - OBRIGATÓRIO

- **CONTEXTO**: Baseado no filme "Guerra Mundial Z" - quando 9 pessoas concordam, a 10ª DEVE discordar
- **APLICAÇÃO**: Para qualquer análise, planejamento ou tomada de decisão de desenvolvimento
- **PROCESSO OBRIGATÓRIO**:
  1. **Apresentar consenso**: Mostrar a abordagem/solução que todos concordariam
  2. **Assumir papel do 10º homem**: OBRIGATORIAMENTE questionar e buscar alternativas
  3. **Identificar riscos**: Cenários onde a abordagem consensual falharia
  4. **Propor alternativas**: Soluções completamente diferentes ou contrárias
  5. **Avaliar trade-offs**: Comparar prós/contras de ambas abordagens
- **GATILHOS**: Sempre aplicar quando solicitado "análise", "planejamento", "estratégia", "abordagem"
- **FORMATO**: Apresentar sempre duas perspectivas: "Consenso vs 10º Homem"

## 🚨 REGRAS CRÍTICAS (Sistema quebra se ignorar)

### MCP Vindula - Recipe System V2 - USO OBRIGATÓRIO
- **REGRA FUNDAMENTAL**: SEMPRE usar MCP Vindula Recipe System antes de implementar ou validar qualquer funcionalidade
- **FERRAMENTA ÚNICA**: `mcp__vindula-cosmos-mcp__vindula_recipe` - Seleção automática do recipe adequado

#### **ANTI-ALUCINAÇÃO - VERIFICAÇÃO DE DADOS REAIS**
- **REGRA CRÍTICA**: NUNCA inventar tabelas, campos ou functions que não existem
- **PROCESSO OBRIGATÓRIO ANTES DE QUALQUER IMPLEMENTAÇÃO**:
  1. **VERIFICAR SCHEMA**: `vindula_recipe("estrutura da tabela X")` → verificar campos existentes
  2. **VERIFICAR TRIGGERS**: `vindula_recipe("triggers para tabela X")` → verificar triggers existentes  
  3. **VERIFICAR FUNCTIONS**: `vindula_recipe("functions relacionadas a X")` → verificar stored procedures
  4. **VERIFICAR PERMISSÕES**: `vindula_recipe("validar resource_type X action_key Y")` → verificar permissões

#### **RECIPES DISPONÍVEIS**:
- 🔍 **Schema Inspector**: Estrutura de tabelas (campos, tipos, constraints)
- 🔥 **Trigger Inspector**: Triggers para tabelas (BEFORE/AFTER, INSERT/UPDATE/DELETE)
- ⚙️ **Function Inspector**: Functions SQL (stored procedures, trigger functions)
- 🎯 **Permission Validator**: Valida resource_types e action_keys contra dados reais
- 📊 **Timestamp Calculator**: Calcula próximo timestamp sequencial para migrations
- 🔒 **Company ID Checker**: Detecta violações de segurança multi-tenant

#### **EXEMPLOS DE USO**:
- ✅ **SCHEMA**: `vindula_recipe("estrutura da tabela posts")` → campos, tipos, constraints
- ✅ **TRIGGERS**: `vindula_recipe("triggers para tabela posts")` → triggers INSERT/UPDATE/DELETE
- ✅ **FUNCTIONS**: `vindula_recipe("functions de stardust")` → stored procedures relacionadas
- ✅ **MIGRATION**: `vindula_recipe("criar migration de permissões")` → timestamp + padrões
- ✅ **VALIDAÇÃO**: `vindula_recipe("validar knowledge_hub knowledge_space_edit")` → verificação automática

- **DETECÇÃO DE VIOLAÇÃO**: Se implementar sem consultar MCP = violação crítica
- **PERFORMANCE**: Dados estruturados processados em ~5 segundos
- **PRECISÃO**: 100% dos dados reais do banco (171 triggers, 623 functions, 178 tabelas)

### Segurança Multi-tenant - CRÍTICO
- **JAMAIS** passar `company_id` como parâmetro em funções SQL
- **SEMPRE** usar `auth.uid()` + `profiles` para obter company_id
- **NUNCA** dar permissões a `anon` para dados sensíveis
- **RLS**: Aplicar em todas tabelas com dados empresariais

### Funções Helper de Segurança - OBRIGATÓRIO
- `public.check_same_company(company_id)` - Verifica se usuário pertence à mesma empresa
- `public.check_admin_role()` - Verifica se usuário tem role admin/company_owner
- `public.check_user_permission(resource_type, action_key)` - Sistema genérico de permissões

### Supabase - Migrações
- **MCP V2**: `vindula_recipe("criar migration")` calcula timestamps automaticamente
- **PROIBIDO**: NUNCA executar `bun run supabase db push` ou aplicar migrações automaticamente
- **APLICAÇÃO**: Usuário aplica migrações manualmente após criação e validação MCP

### Sistema de Stardust - FUNÇÕES OFICIAIS
- **ADICIONAR**: `add_stardust(p_user_id, p_action_type, p_metadata)`
- **SUBTRAIR**: `subtract_stardust(p_user_id, p_action_type, p_amount, p_metadata)`
- **COMPRAS**: `subtract_stardust_with_validation(p_user_id, p_item_type, p_item_id, p_quantity, p_metadata)`
- **PROIBIDO**: Usar funções não oficiais (detectado automaticamente pelo MCP V2)

### Permissões - Sistema Genérico - OBRIGATÓRIO
- **OBRIGATÓRIO**: `useGenericPermissionCheck` + `GenericPermissionGate`
- **PROIBIDO**: Queries manuais de roles, lógica hardcoded
- **RLS Pattern**: `check_permission_v2(auth.uid(), 'resource_type', 'action', NULL)`

### Tratamento de Datas - OBRIGATÓRIO
- **JAMAIS** usar `new Date(dateString)` para datas do Supabase tipo DATE
- **SEMPRE** usar `formatDatabaseDate()` de `@/lib/utils/dateUtils`
- **PROBLEMA**: Datas do banco sofrem offset de fuso horário (UTC-3 = dia anterior)

### Animações - Sistema Centralizado - OBRIGATÓRIO
- **SEMPRE** usar `import { cardVariants, containerVariants } from '@/lib/animations/variants'`
- **PROIBIDO**: Criar variantes locais duplicadas (cardVariants, containerVariants, heroVariants)
- **CUSTOMIZAÇÃO**: Quando necessário, pode ignorar e criar variante específica

## ⚡ REGRAS IMPORTANTES (Uso diário obrigatório)

### Essentials
- Responder em português brasileiro
- Usar exclusivamente `bun` (não npm/yarn)
- **MCP V2**: `vindula_recipe("validar SQL")` sempre verificar antes de implementar
- Reutilizar hooks existentes em `/src/lib/query/hooks/`
- JSDoc em todos arquivos: `<AUTHOR> Internet 2025`

### Commits - OBRIGATÓRIO
- **USAR SEMPRE**: `commit-specialist` para qualquer commit
- **WORKFLOW AUTOMÁTICO**: Issue → Docs → Comentário → Commit → Push
- **NUNCA MANUAL**: Não fazer commits sem usar o agente especializado
- **RASTREABILIDADE**: Todo commit deve ter issue associada e documentação

### Funções SQL - Versionamento
- **MCP V2**: `vindula_recipe("functions relacionadas a X")` sempre verificar functions existentes primeiro
- **Novas funções**: Começar com `_v1`
- **Alterações**: Criar `_v2` (nunca modificar versão existente)
- **ANTI-DUPLICAÇÃO**: Sempre verificar se função similar já existe antes de criar

### Comandos de Desenvolvimento
```bash
bun run dev        # Desenvolvimento (porta 8080)
bun run build      # Build de produção
bun run typecheck  # Verificação TypeScript
bun run test       # Testes E2E Playwright
```

## 🤖 SISTEMA DE AGENTES ESPECIALIZADOS - USO OBRIGATÓRIO

### **AGENTES DISPONÍVEIS - Uso Automático e Proativo**

#### **🔧 `commit-specialist` - Workflow Completo de Commits**
- **USO AUTOMÁTICO**: Para qualquer commit, implementação ou feature concluída
- **INTEGRAÇÃO**: Chama automaticamente `vindula-docs-specialist` durante o processo
- **WORKFLOW**: Issue → Documentação → Comentário → Commit → Push
- **COMANDO**: `/commit [--title=""] [--close=N] [--minimal-docs] [--cache-only]`
- **BENEFÍCIO**: Zero trabalho manual, rastreabilidade total, documentação sempre sincronizada

#### **📚 `vindula-docs-specialist` - Documentação Centralizada**
- **USO PROATIVO**: Chamado automaticamente pelo `commit-specialist`
- **REGRA DE OURO**: Sempre atualizar documentação existente, nunca duplicar
- **PROCESSO**: Busca docs existentes → Atualiza/Cria → Referencia relacionados
- **CENTRALIZAÇÃO**: Um tópico = Um documento principal
- **RESULTADO**: Documentação sempre atualizada e centralizada

#### **🔍 `code-reviewer` - Análise de Qualidade e Segurança**
- **USO PROATIVO**: Após implementações significativas ou mudanças críticas
- **FOCO**: Multi-tenant security, RLS policies, performance patterns
- **DETECTA**: Violações de `company_id`, permissões incorretas, anti-patterns
- **VALIDAÇÕES**: Helper functions, system genérico de permissões, date handling
- **SAÍDA**: Relatório com issues críticos, sugestões e código corrigido

#### **🐛 `debugger` - Resolução Sistemática de Problemas**
- **USO PROATIVO**: Quando encontrar erros, falhas de teste ou comportamento inesperado
- **EXPERTISE**: Runtime errors, performance issues, multi-tenant bugs
- **TOOLS**: Supabase debugging, WebSocket issues, RLS troubleshooting
- **MÉTODO**: Reprodução → Root cause → Solução sistemática
- **RESULTADO**: Diagnóstico completo com solução implementada

#### **📊 `data-scientist` - Análise de Dados e Performance**
- **USO PROATIVO**: Para análises de dados, otimização de queries, métricas de negócio
- **EXPERTISE**: SQL complexo, performance optimization, business intelligence
- **ANÁLISES**: User engagement, feature adoption, gamification effectiveness
- **MULTI-TENANT**: Queries seguros com company isolation
- **RELATÓRIOS**: KPIs, trends, recommendations acionáveis

### **COMANDOS HERDADOS - Agora Executados via Agentes**

#### **`/commit` → `commit-specialist`**
```bash
/commit                                    # Workflow completo automático
/commit --title="Feature Implementation"   # Com título personalizado
/commit --close=15                        # Fecha issue específica
/commit --minimal-docs                    # Documentação básica (economia)
/commit --cache-only                      # Zero MCP (máxima economia)
```

#### **`/auditoria-geral` → `code-reviewer` + `data-scientist`**
- **EXECUÇÃO**: Chamada automática dos dois agentes em paralelo
- **code-reviewer**: Análise de segurança, qualidade, padrões
- **data-scientist**: Performance queries, métricas, data integrity
- **RESULTADO**: Relatório unificado com prioridades e ações

#### **`/avaliar-arquivo <path>` → `code-reviewer`**
- **ANÁLISE**: Arquivo específico com foco em arquitetura e padrões
- **DETECTA**: Violações, problemas de performance, melhorias
- **CONTEXTO**: Entende padrões Vindula Cosmos automaticamente

#### **`/document-feature` → `vindula-docs-specialist`**
- **DOCUMENTAÇÃO**: Automática e centralizada
- **BUSCA**: Documentação existente antes de criar nova
- **ATUALIZA**: CHANGELOG.md e referências cruzadas
- **QUALIDADE**: Padrões Vindula com headers obrigatórios

### **REGRAS DE USO DOS AGENTES**

#### **Quando Usar Cada Agente**
- **🔧 commit-specialist**: TODO commit, implementação concluída, feature finalizada
- **📚 vindula-docs-specialist**: Chamado automaticamente pelo commit-specialist
- **🔍 code-reviewer**: Após mudanças críticas, antes de releases, code review
- **🐛 debugger**: Erros, falhas de teste, comportamento inesperado
- **📊 data-scientist**: Análises de dados, otimização de queries, métricas de negócio

#### **Integração entre Agentes**
- **commit-specialist** → **vindula-docs-specialist** (automático)
- **code-reviewer** + **data-scientist** para auditorias completas
- **debugger** pode chamar **code-reviewer** para análise de root cause
- **data-scientist** pode ser chamado por **debugger** para queries lentas

#### **Pré-requisitos Importantes**
- **Schema atualizado**: `./scripts/dump-current-schema.sh` antes de auditorias
- **MCP funcionando**: Verificar se `mcp__vindula-cosmos-mcp__vindula_recipe` está ativo
- **Agentes disponíveis**: Verificar se todos os 5 agentes estão configurados
- **Context7**: Para documentação externa sempre que necessário

# Utilitarios SQL Functions
check_same_company(company_id)
- Uso: Verificar se usuário pertence à mesma empresa
- Segurança: SECURITY DEFINER + STABLE
check_same_company_admin(company_id)
- Uso: Verificar se usuário é admin da mesma empresa
- Segurança: SECURITY DEFINER + verifica roles admin/company_owner


### Documentação - OBRIGATÓRIO (Via Agentes)
- **AUTOMÁTICA**: `commit-specialist` chama `vindula-docs-specialist` automaticamente
- **CENTRALIZADA**: Sempre atualizar docs existentes, nunca duplicar
- **ORGANIZAÇÃO**: `docs_v2/features/` ou `docs_v2/improvements/`
- **QUALIDADE**: Headers padrão, referencias cruzadas, CHANGELOG atualizado
- **PROCESSO**: Busca existente → Atualiza/Cria → Referencia → Centraliza

### WebSocket Consolidation - DOCUMENTAÇÃO OFICIAL
@docs_v2/features/unified-realtime-provider-sistema-centralizado.md