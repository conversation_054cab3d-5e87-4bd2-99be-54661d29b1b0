Id,Rule Name,Product,Severity,Status,Dependency,Version,Reachability,Transitivity,Cve,Epss,Assistant Component,Repository Name,Repository Url,Line Of Code Url,Semgrep Platform Link,Created At,Last Opened At,Branch,Triaged At,Triage Comment,Triage Reason,Rule Description
224564537,Infinite Loop in nanoid,Supply Chain,Medium,Open,nanoid,3.3.7,No Reachability Analysis,Transitive,CVE-2024-55565,0.02% (Low),,vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c/blob/c2195acf0c7cf303c34179b05fc4cb20186bb212/package-lock.json#L6196,https://semgrep.dev/orgs/vindula/supply-chain/findings/224564537,2025-07-27 03:33:09.058840,2025-07-27 03:33:09.044196,refs/heads/main,,,,Affected versions of nanoid are vulnerable to Loop with Unreachable Exit Condition ('Infinite Loop').
224564536,Missing Origin Validation in WebSockets in vite,Supply Chain,Medium,Open,vite,5.4.10,No Reachability Analysis,Direct,CVE-2025-24010,0.01% (Low),,vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c/blob/c2195acf0c7cf303c34179b05fc4cb20186bb212/package-lock.json#L7974,https://semgrep.dev/orgs/vindula/supply-chain/findings/224564536,2025-07-27 03:33:09.058825,2025-07-27 03:33:09.044186,refs/heads/main,,,,Affected versions of vite are vulnerable to Missing Origin Validation in WebSockets / Origin Validation Error / Reliance on Reverse DNS Resolution for a Security-Critical Action.
224564535,Path Traversal in @supabase/auth-js,Supply Chain,Low,Open,@supabase/auth-js,2.67.3,No Reachability Analysis,Transitive,CVE-2025-48370,0.03% (Low),,vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c/blob/c2195acf0c7cf303c34179b05fc4cb20186bb212/package-lock.json#L2596,https://semgrep.dev/orgs/vindula/supply-chain/findings/224564535,2025-07-27 03:33:09.058809,2025-07-27 03:33:09.044176,refs/heads/main,,,,Affected versions of @supabase/auth-js are vulnerable to Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal').
224564534,Origin Validation Error in esbuild,Supply Chain,Medium,Open,esbuild,0.21.5,No Reachability Analysis,Transitive,GHSA-67mh-4wv8-2f99,0.0% (Low),,vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c/blob/c2195acf0c7cf303c34179b05fc4cb20186bb212/package-lock.json#L5053,https://semgrep.dev/orgs/vindula/supply-chain/findings/224564534,2025-07-27 03:33:09.058793,2025-07-27 03:33:09.044166,refs/heads/main,,,,Affected versions of esbuild are vulnerable to Origin Validation Error.
224564533,Exposure of Sensitive Information to an Unauthorized Actor in vite,Supply Chain,Medium,Open,vite,5.4.10,No Reachability Analysis,Direct,CVE-2025-31125,8.56% (Low),,vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c/blob/c2195acf0c7cf303c34179b05fc4cb20186bb212/package-lock.json#L7974,https://semgrep.dev/orgs/vindula/supply-chain/findings/224564533,2025-07-27 03:33:09.058777,2025-07-27 03:33:09.044156,refs/heads/main,,,,Affected versions of vite are vulnerable to Exposure of Sensitive Information to an Unauthorized Actor / Improper Access Control.
224564532,Exposure of Sensitive Information to an Unauthorized Actor in vite,Supply Chain,Medium,Open,vite,5.4.10,No Reachability Analysis,Direct,CVE-2025-30208,75.83% (High),,vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c/blob/c2195acf0c7cf303c34179b05fc4cb20186bb212/package-lock.json#L7974,https://semgrep.dev/orgs/vindula/supply-chain/findings/224564532,2025-07-27 03:33:09.058762,2025-07-27 03:33:09.044146,refs/heads/main,,,,Affected versions of vite are vulnerable to Exposure of Sensitive Information to an Unauthorized Actor / Improper Access Control.
224564531,Inefficient Regular Expression Complexity in @babel/helpers,Supply Chain,Medium,Open,@babel/runtime,7.25.9,No Reachability Analysis,Transitive,CVE-2025-27789,0.04% (Low),,vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c/blob/c2195acf0c7cf303c34179b05fc4cb20186bb212/package-lock.json#L143,https://semgrep.dev/orgs/vindula/supply-chain/findings/224564531,2025-07-27 03:33:09.058746,2025-07-27 03:33:09.044135,refs/heads/main,,,,"Affected versions of @babel/helpers, @babel/runtime, @babel/runtime-corejs2, and @babel/runtime- corejs3 are vulnerable to Inefficient Regular Expression Complexity."
224564530,Exposure of Sensitive Information to an Unauthorized Actor in vite,Supply Chain,Medium,Open,vite,5.4.10,No Reachability Analysis,Direct,CVE-2025-31486,0.01% (Low),,vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c/blob/c2195acf0c7cf303c34179b05fc4cb20186bb212/package-lock.json#L7974,https://semgrep.dev/orgs/vindula/supply-chain/findings/224564530,2025-07-27 03:33:09.058730,2025-07-27 03:33:09.044124,refs/heads/main,,,,Affected versions of vite are vulnerable to Exposure of Sensitive Information to an Unauthorized Actor / Improper Access Control.
224564529,Exposure of Sensitive Information to an Unauthorized Actor in vite,Supply Chain,Medium,Open,vite,5.4.10,No Reachability Analysis,Direct,CVE-2025-32395,0.02% (Low),,vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c/blob/c2195acf0c7cf303c34179b05fc4cb20186bb212/package-lock.json#L7974,https://semgrep.dev/orgs/vindula/supply-chain/findings/224564529,2025-07-27 03:33:09.058714,2025-07-27 03:33:09.044112,refs/heads/main,,,,Affected versions of vite are vulnerable to Exposure of Sensitive Information to an Unauthorized Actor.
224564528,Path Traversal in vite,Supply Chain,Medium,Open,vite,5.4.10,No Reachability Analysis,Direct,CVE-2025-46565,0.08% (Low),,vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c/blob/c2195acf0c7cf303c34179b05fc4cb20186bb212/package-lock.json#L7974,https://semgrep.dev/orgs/vindula/supply-chain/findings/224564528,2025-07-27 03:33:09.058698,2025-07-27 03:33:09.044101,refs/heads/main,,,,Affected versions of vite are vulnerable to Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal').
224564527,Uncontrolled Resource Consumption in brace-expansion,Supply Chain,Low,Open,brace-expansion,2.0.1,No Reachability Analysis,Transitive,CVE-2025-5889,0.02% (Low),,vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c/blob/c2195acf0c7cf303c34179b05fc4cb20186bb212/package-lock.json#L3717,https://semgrep.dev/orgs/vindula/supply-chain/findings/224564527,2025-07-27 03:33:09.058682,2025-07-27 03:33:09.044090,refs/heads/main,,,,Affected versions of brace-expansion are vulnerable to Uncontrolled Resource Consumption.
224564526,Uncontrolled Resource Consumption in brace-expansion,Supply Chain,Low,Open,brace-expansion,2.0.1,No Reachability Analysis,Transitive,CVE-2025-5889,0.02% (Low),,vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c/blob/c2195acf0c7cf303c34179b05fc4cb20186bb212/package-lock.json#L5616,https://semgrep.dev/orgs/vindula/supply-chain/findings/224564526,2025-07-27 03:33:09.058665,2025-07-27 03:33:09.044073,refs/heads/main,,,,Affected versions of brace-expansion are vulnerable to Uncontrolled Resource Consumption.
224564525,Uncontrolled Resource Consumption in brace-expansion,Supply Chain,Low,Open,brace-expansion,1.1.11,No Reachability Analysis,Transitive,CVE-2025-5889,0.02% (Low),,vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c,https://github.com/vindulaintranet/vindulacosmos-e6b4d65c/blob/c2195acf0c7cf303c34179b05fc4cb20186bb212/package-lock.json#L4041,https://semgrep.dev/orgs/vindula/supply-chain/findings/224564525,2025-07-27 03:33:09.058625,2025-07-27 03:33:09.044016,refs/heads/main,,,,Affected versions of brace-expansion are vulnerable to Uncontrolled Resource Consumption.
