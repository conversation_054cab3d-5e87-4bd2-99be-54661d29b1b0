# Vindula Cosmos - Agent Instructions

## Commands
- **Dev**: `bun dev` (port 8080)
- **Build**: `bun run build` / `bun run typecheck` / `bun run lint` / `bun run format`
- **Tests**: `bunx playwright test` (E2E) / `bun test` (unit) / `bun run test:auth` (single auth test)
- **Task Master**: `task-master list` / `task-master next` (see .windsurfrules for full commands)

## Architecture
- **Stack**: React 18 + TypeScript + Vite + Supabase (PostgreSQL + Auth + Storage + Realtime)
- **Multi-tenant** corporate social platform with RLS security
- **Key Libraries**: TanStack Query + Zustand + shadcn/ui (Radix) + Framer Motion + TipTap
- **Subprojects**: `/vindula-cosmos-mobile` (Capacitor), `/vindula-cosmos-mcp` (MCP server)
- **Main Dirs**: `src/components/` (feature-based), `src/lib/query/hooks/`, `src/services/`, `src/stores/`

## Code Style (Portuguese + Multi-tenant Security)
- **Portuguese**: Comments, docs, JSDoc `<AUTHOR> Internet 2025`
- **Package Manager**: `bun` only (never npm/yarn)
- **Imports**: `@/` alias, centralized QueryKeys in `/src/lib/query/queryKeys.ts`
- **Queries**: Always in separate hooks (never inline), custom hooks only
- **Security**: NEVER pass `company_id` params - use `auth.uid()` + `profiles` table, RLS mandatory
- **Limits**: 200 lines max per file, date handling via `formatDatabaseDate()` from `@/lib/utils/dateUtils`
- **Feature Flags**: Use `useFeatureCheck('key', 'Name')` hook, `<FeatureNotAvailable />` component

## Database & Security
- **Supabase**: NO direct migrations (create `.sql` files only), multi-tenant with company isolation
- **Helper Functions**: `check_same_company()`, `check_admin_role()`, `check_user_permission()`
- **Logs**: Use `logQueryEvent()` from `@/lib/logs/showQueryLogs` (never console.log)
