#!/bin/bash

# 🧠 UPDATE BRAIN SCHEMA - Script Unificado
# Executa smart-schema-dump + process-schema-dump em sequência
# Atualiza completamente o schema para o Vindula Cosmos Brain
# 
# <AUTHOR> Internet 2025

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Diretório do script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo -e "${CYAN}🧠 VINDULA COSMOS BRAIN - ATUALIZAÇÃO DE SCHEMA${NC}"
echo -e "${CYAN}=================================================${NC}"
echo ""
echo -e "${BLUE}📋 Este script executa:${NC}"
echo -e "${BLUE}  1. Smart Schema Dump (com cache inteligente)${NC}"
echo -e "${BLUE}  2. Process Schema Dump (estruturação JSON)${NC}"
echo -e "${BLUE}  3. Atualização do Brain com schema estruturado${NC}"
echo ""

# Verificar se estamos no diretório correto
if [ ! -f "$SCRIPT_DIR/smart-schema-dump.sh" ] || [ ! -f "$SCRIPT_DIR/process-schema-dump.sh" ]; then
    echo -e "${RED}❌ Erro: Scripts necessários não encontrados${NC}"
    echo -e "${RED}   Verifique se está executando de /scripts/${NC}"
    exit 1
fi

# Verificar se bun está disponível (necessário para o processor)
if ! command -v bun &> /dev/null; then
    echo -e "${YELLOW}⚠️  Bun não encontrado, tentando usar node...${NC}"
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Erro: Node.js ou Bun necessário para processar schema${NC}"
        exit 1
    fi
fi

echo -e "${YELLOW}🚀 Iniciando atualização do schema...${NC}"
echo ""

# ETAPA 1: Smart Schema Dump
echo -e "${GREEN}📥 ETAPA 1/3: Executando Smart Schema Dump...${NC}"
echo -e "${BLUE}----------------------------------------${NC}"

if ! "$SCRIPT_DIR/smart-schema-dump.sh" auto; then
    echo -e "${RED}❌ Erro na Etapa 1: Smart Schema Dump falhou${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}✅ Etapa 1 concluída: Schema dump realizado${NC}"
echo ""

# ETAPA 2: Process Schema Dump  
echo -e "${GREEN}🔧 ETAPA 2/3: Executando Process Schema Dump...${NC}"
echo -e "${BLUE}----------------------------------------${NC}"

if ! "$SCRIPT_DIR/process-schema-dump.sh"; then
    echo -e "${RED}❌ Erro na Etapa 2: Process Schema Dump falhou${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}✅ Etapa 2 concluída: Schema estruturado gerado${NC}"
echo ""

# ETAPA 3: Copiar para o Brain (se estrutura foi criada)
echo -e "${GREEN}📋 ETAPA 3/3: Atualizando Vindula Cosmos Brain...${NC}"
echo -e "${BLUE}----------------------------------------${NC}"

# Encontrar dump mais recente com estrutura (compatível com macOS)
SCHEMA_DUMPS_DIR="$SCRIPT_DIR/../schema_dumps"
LATEST_STRUCTURED=$(find "$SCHEMA_DUMPS_DIR" -name "structured_schema.json" -type f -exec stat -f "%m %N" {} \; 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-)

if [ -z "$LATEST_STRUCTURED" ]; then
    echo -e "${YELLOW}⚠️  Nenhum structured_schema.json encontrado, mas dumps básicos estão OK${NC}"
    echo -e "${YELLOW}   Brain pode usar dumps SQL diretamente${NC}"
else
    # Copiar para o diretório do Brain
    BRAIN_DATA_DIR="$SCRIPT_DIR/../vindula-cosmos-brain/app/data"
    
    if [ -d "$BRAIN_DATA_DIR" ]; then
        echo -e "${BLUE}📂 Copiando structured_schema.json para o Brain...${NC}"
        cp "$LATEST_STRUCTURED" "$BRAIN_DATA_DIR/structured_schema.json"
        
        # Verificar se cópia foi bem-sucedida
        if [ -f "$BRAIN_DATA_DIR/structured_schema.json" ]; then
            BRAIN_SIZE=$(du -h "$BRAIN_DATA_DIR/structured_schema.json" | cut -f1)
            echo -e "${GREEN}✅ Schema estruturado copiado para o Brain (${BRAIN_SIZE})${NC}"
        else
            echo -e "${YELLOW}⚠️  Falha ao copiar para o Brain, mas dados estão disponíveis em:${NC}"
            echo -e "${YELLOW}   $LATEST_STRUCTURED${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  Diretório do Brain não encontrado, schema estruturado disponível em:${NC}"
        echo -e "${YELLOW}   $LATEST_STRUCTURED${NC}"
    fi
fi

echo ""
echo -e "${GREEN}✅ Etapa 3 concluída: Brain atualizado${NC}"
echo ""

# RESUMO FINAL
echo -e "${CYAN}🎉 ATUALIZAÇÃO CONCLUÍDA COM SUCESSO!${NC}"
echo -e "${CYAN}====================================${NC}"
echo ""
echo -e "${GREEN}📊 Resumo do que foi atualizado:${NC}"

# Mostrar informações dos dumps gerados
LATEST_DUMP_DIR=$(find "$SCHEMA_DUMPS_DIR" -name "20*" -type d | sort -r | head -1)

if [ -n "$LATEST_DUMP_DIR" ]; then
    echo -e "${BLUE}📁 Diretório de dumps: $(basename "$LATEST_DUMP_DIR")${NC}"
    
    # Mostrar arquivos gerados
    if [ -f "$LATEST_DUMP_DIR/complete_schema_with_data.sql" ]; then
        DUMP_SIZE=$(du -h "$LATEST_DUMP_DIR/complete_schema_with_data.sql" | cut -f1)
        echo -e "${GREEN}  • Schema completo: ${DUMP_SIZE}${NC}"
    fi
    
    if [ -d "$LATEST_DUMP_DIR/structured" ]; then
        STRUCTURED_COUNT=$(ls "$LATEST_DUMP_DIR/structured"/*.sql 2>/dev/null | wc -l || echo "0")
        echo -e "${GREEN}  • Arquivos estruturados: ${STRUCTURED_COUNT} SQL files${NC}"
        
        if [ -f "$LATEST_DUMP_DIR/structured/structured_schema.json" ]; then
            JSON_SIZE=$(du -h "$LATEST_DUMP_DIR/structured/structured_schema.json" | cut -f1)
            echo -e "${GREEN}  • Schema JSON: ${JSON_SIZE}${NC}"
        fi
    fi
fi

echo ""
echo -e "${YELLOW}🔧 Para usar o Brain atualizado:${NC}"
echo -e "${YELLOW}  cd vindula-cosmos-brain/${NC}"
echo -e "${YELLOW}  ./run_fastmcp.sh${NC}"
echo ""
echo -e "${YELLOW}🧪 Para testar recipes SQL:${NC}"
echo -e "${YELLOW}  vindula_recipe(\"estruture esta função\", \"CREATE FUNCTION test()...\")${NC}"
echo -e "${YELLOW}  vindula_recipe(\"verificar schema da tabela users\")${NC}"
echo ""
echo -e "${BLUE}📈 Status: Schema do Brain totalmente atualizado e pronto para uso!${NC}"