#!/bin/bash

# Smart Schema Dump - Sistema de Cache Inteligente
# Evita downloads desnecessários do schema, usando cache por data
# <AUTHOR> Internet 2025

set -e

CACHE_DIR="schema_dumps/cache"
TODAY=$(date +%Y-%m-%d)
TODAY_CACHE="$CACHE_DIR/$TODAY"
CACHE_INFO="$TODAY_CACHE/.cache_info"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Função para verificar se cache é válido
check_cache_validity() {
    if [ -d "$TODAY_CACHE" ] && [ -f "$CACHE_INFO" ]; then
        local cache_timestamp=$(grep "timestamp=" "$CACHE_INFO" | cut -d'=' -f2)
        local current_timestamp=$(date +%s)
        local age=$((current_timestamp - cache_timestamp))
        
        # Cache válido por 24h (86400 segundos)
        if [ $age -lt 86400 ]; then
            echo "valid"
            return 0
        else
            log_warning "Cache existe mas está expirado (>24h)"
        fi
    fi
    echo "invalid"
    return 1
}

# Função para verificar integridade dos arquivos de cache
check_cache_integrity() {
    local required_files=("complete_schema_with_data.sql" "public_schema_only.sql" "data_only.sql")
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$TODAY_CACHE/$file" ]; then
            log_error "Arquivo de cache ausente ou corrompido: $file"
            return 1
        fi
        
        # Verificar se arquivo não está vazio
        if [ ! -s "$TODAY_CACHE/$file" ]; then
            log_error "Arquivo de cache vazio: $file"
            return 1
        fi
    done
    
    return 0
}

# Função para criar novo dump com cache
create_cached_dump() {
    log_info "Criando novo schema dump em cache..."
    
    # Criar diretório de cache se não existe
    mkdir -p "$TODAY_CACHE"
    
    # Verificar se script original existe
    if [ ! -f "$SCRIPT_DIR/dump-current-schema.sh" ]; then
        log_error "Script dump-current-schema.sh não encontrado em $SCRIPT_DIR"
        exit 1
    fi
    
    # Executar dump original
    log_info "Executando dump original do schema..."
    cd "$SCRIPT_DIR/.." # Voltar para root do projeto
    ./scripts/dump-current-schema.sh
    
    # Encontrar o dump mais recente (compatível com macOS)
    LATEST_DUMP=$(find schema_dumps -name "complete_schema_with_data.sql" -type f -exec stat -f "%m %N" {} \; | sort -n | tail -1 | cut -d' ' -f2-)
    
    if [ -z "$LATEST_DUMP" ]; then
        log_error "Nenhum dump encontrado após execução do script"
        exit 1
    fi
    
    LATEST_DIR=$(dirname "$LATEST_DUMP")
    log_info "Copiando dump de: $LATEST_DIR"
    
    # Copiar arquivos para cache
    cp "$LATEST_DIR"/*.sql "$TODAY_CACHE/" 2>/dev/null || {
        log_error "Erro ao copiar arquivos de dump para cache"
        exit 1
    }
    
    # Obter metadados do banco (se DATABASE_URL estiver disponível)
    local schema_version="unknown"
    local tables_count="unknown"
    local policies_count="unknown"
    
    if [ ! -z "$DATABASE_URL" ]; then
        schema_version=$(psql "$DATABASE_URL" -t -c "SELECT version FROM schema_migrations ORDER BY version DESC LIMIT 1;" 2>/dev/null | xargs || echo "unknown")
        tables_count=$(psql "$DATABASE_URL" -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema='public';" 2>/dev/null | xargs || echo "unknown")
        policies_count=$(psql "$DATABASE_URL" -t -c "SELECT count(*) FROM pg_policies;" 2>/dev/null | xargs || echo "unknown")
    fi
    
    # Criar metadados do cache
    cat > "$CACHE_INFO" << EOF
timestamp=$(date +%s)
date=$TODAY
time=$(date +%H:%M:%S)
schema_version=$schema_version
tables_count=$tables_count
policies_count=$policies_count
original_dump_path=$LATEST_DIR
files_cached=$(ls "$TODAY_CACHE"/*.sql | wc -l)
total_size=$(du -sh "$TODAY_CACHE" | cut -f1)
EOF
    
    log_success "Schema dump criado e armazenado em cache: $TODAY_CACHE"
    log_info "Cache contém $(ls "$TODAY_CACHE"/*.sql | wc -l) arquivos SQL ($(du -sh "$TODAY_CACHE" | cut -f1))"
}

# Função para usar cache existente
use_cached_dump() {
    log_info "Verificando cache existente de hoje: $TODAY_CACHE"
    
    # Verificar integridade dos arquivos
    if ! check_cache_integrity; then
        log_warning "Cache corrompido, recriando..."
        create_cached_dump
        return
    fi
    
    log_success "Usando schema dump em cache de hoje"
    
    # Mostrar informações do cache
    if [ -f "$CACHE_INFO" ]; then
        local cache_time=$(grep "time=" "$CACHE_INFO" | cut -d'=' -f2)
        local files_count=$(grep "files_cached=" "$CACHE_INFO" | cut -d'=' -f2)
        local cache_size=$(grep "total_size=" "$CACHE_INFO" | cut -d'=' -f2)
        
        log_info "Cache criado às $cache_time com $files_count arquivos ($cache_size)"
    fi
}

# Função para mostrar status do cache
show_cache_status() {
    echo "📊 Status do Cache de Schema Dump"
    echo "================================="
    
    if [ -f "$CACHE_INFO" ]; then
        echo "📁 Localização: $TODAY_CACHE"
        echo "📅 Data: $TODAY"
        echo ""
        echo "📋 Detalhes do Cache:"
        while IFS='=' read -r key value; do
            case $key in
                "timestamp") 
                    local readable_time=$(date -d "@$value" "+%d/%m/%Y %H:%M:%S" 2>/dev/null || date -r "$value" "+%d/%m/%Y %H:%M:%S" 2>/dev/null || echo "unknown")
                    echo "  ⏰ Criado em: $readable_time"
                    ;;
                "time") echo "  🕐 Horário: $value" ;;
                "schema_version") echo "  🔢 Versão Schema: $value" ;;
                "tables_count") echo "  📊 Tabelas: $value" ;;
                "policies_count") echo "  🔒 RLS Policies: $value" ;;
                "files_cached") echo "  📁 Arquivos: $value" ;;
                "total_size") echo "  💾 Tamanho: $value" ;;
            esac
        done < "$CACHE_INFO"
        
        echo ""
        echo "📁 Arquivos em Cache:"
        ls -lh "$TODAY_CACHE"/*.sql 2>/dev/null || echo "  Nenhum arquivo SQL encontrado"
        
    else
        echo "❌ Nenhum cache encontrado para hoje ($TODAY)"
        echo ""
        echo "💡 Para criar cache:"
        echo "  $0 auto"
        echo "  $0 force"
    fi
}

# Função para limpar cache antigo
clean_old_cache() {
    log_info "Limpando cache de schema antigo (>7 dias)..."
    
    if [ ! -d "$CACHE_DIR" ]; then
        log_warning "Diretório de cache não existe: $CACHE_DIR"
        return
    fi
    
    local removed_count=0
    
    # Encontrar e remover diretórios com mais de 7 dias
    while IFS= read -r -d '' dir; do
        log_info "Removendo cache antigo: $(basename "$dir")"
        rm -rf "$dir"
        ((removed_count++))
    done < <(find "$CACHE_DIR" -type d -name "20*-*-*" -mtime +7 -print0 2>/dev/null)
    
    if [ $removed_count -eq 0 ]; then
        log_info "Nenhum cache antigo encontrado para remover"
    else
        log_success "Cache antigo removido: $removed_count diretórios"
    fi
    
    # Mostrar cache restante
    echo ""
    echo "📁 Cache Restante:"
    ls -la "$CACHE_DIR"/ 2>/dev/null | grep "^d.*20" || echo "  Nenhum cache restante"
}

# Função para mostrar ajuda
show_help() {
    echo "Smart Schema Dump - Sistema de Cache Inteligente"
    echo "================================================"
    echo ""
    echo "Uso: $0 [comando]"
    echo ""
    echo "Comandos:"
    echo "  auto     (padrão) Usa cache se existe e válido, senão cria novo"
    echo "  force    Força criação de novo dump, sobrescreve cache"
    echo "  status   Mostra informações do cache atual"
    echo "  clean    Remove cache antigo (>7 dias)"
    echo "  help     Mostra esta ajuda"
    echo ""
    echo "Exemplos:"
    echo "  $0                    # Usa cache automático"
    echo "  $0 auto               # Mesmo que acima"
    echo "  $0 force              # Força novo dump"
    echo "  $0 status             # Mostra status do cache"
    echo ""
    echo "Variáveis de ambiente:"
    echo "  DATABASE_URL          URL de conexão com o banco (opcional)"
    echo ""
    echo "Cache é armazenado em: $CACHE_DIR/YYYY-MM-DD/"
    echo "Cache expira em: 24 horas"
}

# Função principal
main() {
    local command="${1:-auto}"
    
    case "$command" in
        "auto"|"")
            if [ "$(check_cache_validity)" = "valid" ]; then
                use_cached_dump
            else
                create_cached_dump
            fi
            ;;
        "force"|"--force"|"--novo-dump"|"--refresh-schema")
            create_cached_dump
            ;;
        "status"|"--status"|"info")
            show_cache_status
            ;;
        "clean"|"--clean"|"purge")
            clean_old_cache
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            log_error "Comando desconhecido: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
    
    # Exportar variável para uso do agente security-auditor
    export CURRENT_SCHEMA_PATH="$TODAY_CACHE"
    echo ""
    log_success "Schema dump disponível em: $TODAY_CACHE"
}

# Executar função principal
main "$@"