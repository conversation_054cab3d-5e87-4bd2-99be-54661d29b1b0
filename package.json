{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "NODE_OPTIONS='--max-old-space-size=8192' vite build --mode production", "build:dev": "vite build --mode development", "build:staging": "vite build --mode staging", "build:prod": "vite build --mode production", "build:cf": "NODE_OPTIONS='--max-old-space-size=4096' vite build --mode production", "typecheck": "tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "preview": "vite preview", "preview:staging": "vite preview --mode staging", "test": "bunx playwright test --reporter=line", "test:ui": "bunx playwright test --ui", "test:debug": "bunx playwright test --debug", "test:report": "bunx playwright show-report", "test:auth": "bunx playwright test tests/playwright/auth-modular.spec.ts --reporter=line", "test:auth-ui": "bunx playwright test tests/playwright/auth-modular.spec.ts --ui", "test:auth-report": "bunx playwright test tests/playwright/auth-modular.spec.ts --reporter=html", "test:posts": "bunx playwright test tests/playwright/posts-tests.spec.ts --reporter=line", "test:posts-ui": "bunx playwright test tests/playwright/posts-tests.spec.ts --ui", "test:posts-report": "bunx playwright test tests/playwright/posts-tests.spec.ts --reporter=html", "generate-test-user": "bun tests/playwright/generate-test-user.ts", "test:unit": "vitest", "test:unit:ui": "vitest --ui", "test:unit:run": "vitest run", "test:unit:coverage": "vitest run --coverage", "test:unit:watch": "vitest --watch", "test:bun": "bun test", "test:bun:watch": "bun test --watch", "test:bun:coverage": "bun test --coverage", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd", "deploy:cache-bust": "./scripts/deploy-with-cache-bust.sh", "cache:clear": "rm -rf dist/ node_modules/.vite/ .vite/ && bun install"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@babel/runtime": "^7.28.2", "@capacitor-community/safe-area": "^7.0.0-alpha.1", "@capacitor/android": "^7.4.0", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/filesystem": "^7.1.1", "@capacitor/ios": "^7.2.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.9.0", "@lukemorales/query-key-factory": "^1.3.4", "@marsidev/react-turnstile": "^1.1.0", "@mui/icons-material": "^6.4.6", "@mui/material": "^6.4.6", "@onesignal/node-onesignal": "^5.0.0-alpha-02", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/scroll-mode": "^3.12.0", "@react-pdf/renderer": "^4.3.0", "@sentry/react": "^9.30.0", "@sentry/vite-plugin": "^3.5.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.52.1", "@tanstack/query-persist-client-core": "^5.70.0", "@tanstack/query-sync-storage-persister": "^5.70.0", "@tanstack/react-query": "^5.56.2", "@tiptap/extension-image": "^2.11.3", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-mention": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/pm": "^2.2.4", "@tiptap/react": "^2.2.4", "@tiptap/starter-kit": "^2.2.4", "@tiptap/suggestion": "^2.2.4", "@types/canvas-confetti": "^1.9.0", "@types/lodash.debounce": "^4.0.9", "@types/react-helmet": "^6.1.11", "@types/ua-parser-js": "^0.7.39", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@xyflow/react": "^12.4.2", "boxen": "^8.0.1", "browser-image-compression": "^2.0.2", "caniuse-lite": "^1.0.30001707", "canvas": "^3.1.0", "canvas-confetti": "^1.9.3", "capacitor-plugin-safe-area": "^4.0.0", "capacitor-voice-recorder": "^7.0.6", "chalk": "^4.1.2", "class-variance-authority": "^0.7.1", "cli-table3": "^0.6.5", "clsx": "^2.1.1", "cmdk": "^1.0.0", "commander": "^11.1.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "diff": "^8.0.2", "dotenv": "^16.3.1", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "emoji-mart": "^5.6.0", "esbuild": "^0.25.8", "express": "^4.21.2", "fastmcp": "^1.20.5", "figlet": "^1.8.0", "form-data": "^4.0.4", "framer-motion": "^12.0.6", "fuse.js": "^7.0.0", "glob": "^11.0.3", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "idb": "^8.0.3", "immer": "^10.1.1", "input-otp": "^1.2.4", "inquirer": "^12.5.0", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lodash.debounce": "^4.0.8", "lru-cache": "^10.2.0", "lucide-react": "^0.462.0", "minimatch": "^10.0.3", "nanoid": "^5.1.5", "next-themes": "^0.3.0", "node-sql-parser": "^5.3.10", "openai": "^4.89.0", "ora": "^8.2.0", "pdfjs-dist": "3.4.120", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.53.0", "react-hotkeys-hook": "^4.6.1", "react-image-crop": "^11.0.5", "react-markdown": "^10.1.0", "react-pdf": "7.7.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-textarea-autosize": "^8.5.9", "recharts": "^2.15.2", "remark-gfm": "^4.0.1", "sharp": "^0.33.5", "sonner": "^1.5.0", "stripe": "^18.2.1", "supabase": "^2.9.6", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tippy.js": "^6.3.7", "ua-parser-js": "^2.0.3", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.32.0", "@playwright/test": "^1.50.1", "@tailwindcss/typography": "^0.5.15", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/diff": "^8.0.0", "@types/dompurify": "^3.2.0", "@types/html2canvas": "^1.0.0", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/ui": "^3.2.3", "autoprefixer": "^10.4.20", "bun-types": "latest", "dompurify": "^3.2.4", "eslint": "^9.32.0", "eslint-formatter-compact": "^8.40.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jsdom": "^26.1.0", "lovable-tagger": "^1.0.19", "postcss": "^8.4.47", "prettier": "^3.5.3", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.38.0", "vite": "^5.4.19", "vitest": "^3.2.3"}, "module": "index.ts"}